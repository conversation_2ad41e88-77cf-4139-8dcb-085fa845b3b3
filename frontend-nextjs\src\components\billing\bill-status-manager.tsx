'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  IconEdit, 
  IconCheck, 
  IconX, 
  IconAlertTriangle,
  IconMail,
  IconFileText,
  IconCreditCard
} from '@tabler/icons-react';
import { Bill } from '@/types/clinic';
import { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { useRole, PermissionGate } from '@/lib/role-context';
import { toast } from 'sonner';

interface BillStatusManagerProps {
  bill: Bill;
  onStatusUpdate?: (updatedBill: Bill) => void;
  trigger?: React.ReactNode;
}

// Define valid status transitions
const statusTransitions: Record<string, string[]> = {
  draft: ['sent', 'cancelled'],
  sent: ['confirmed', 'cancelled'],
  confirmed: ['paid', 'cancelled'],
  paid: [], // Final status - no transitions allowed
  cancelled: [], // Final status - no transitions allowed
};

// Status display configuration
const statusConfig = {
  draft: {
    label: '草稿',
    color: 'bg-gray-100 text-gray-800',
    icon: IconFileText,
    description: '账单正在编辑中',
  },
  sent: {
    label: '已发送',
    color: 'bg-blue-100 text-blue-800',
    icon: IconMail,
    description: '账单已发送给患者',
  },
  confirmed: {
    label: '已确认',
    color: 'bg-yellow-100 text-yellow-800',
    icon: IconCheck,
    description: '患者已确认账单',
  },
  paid: {
    label: '已支付',
    color: 'bg-green-100 text-green-800',
    icon: IconCreditCard,
    description: '账单已完全支付',
  },
  cancelled: {
    label: '已取消',
    color: 'bg-red-100 text-red-800',
    icon: IconX,
    description: '账单已取消',
  },
};

export function BillStatusManager({ bill, onStatusUpdate, trigger }: BillStatusManagerProps) {
  const { hasPermission } = useRole();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(bill.status);
  const [notes, setNotes] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const currentStatusConfig = statusConfig[bill.status];
  const availableTransitions = statusTransitions[bill.status] || [];
  const canUpdateStatus = hasPermission('canEditBills') && availableTransitions.length > 0;

  const handleStatusUpdate = async () => {
    if (selectedStatus === bill.status) {
      toast.warning('请选择不同的状态');
      return;
    }

    try {
      setIsUpdating(true);

      // Validate transition
      if (!availableTransitions.includes(selectedStatus)) {
        toast.error('无效的状态转换');
        return;
      }

      // Special validation for paid status
      if (selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0) {
        toast.error('账单还有未支付金额，无法标记为已支付');
        return;
      }

      const updateData: Partial<Bill> = {
        status: selectedStatus as Bill['status'],
      };

      // Add notes if provided
      if (notes.trim()) {
        updateData.notes = bill.notes 
          ? `${bill.notes}\n\n[状态更新] ${notes.trim()}`
          : `[状态更新] ${notes.trim()}`;
      }

      const updatedBill = await billsAPI.updateBill(bill.id, updateData);

      toast.success(`账单状态已更新为: ${statusConfig[selectedStatus].label}`);
      
      if (onStatusUpdate) {
        onStatusUpdate(updatedBill);
      }

      setIsOpen(false);
      setNotes('');
      
    } catch (error) {
      console.error('Failed to update bill status:', error);
      const errorMessage = error instanceof BillingAPIError 
        ? error.message 
        : '状态更新失败，请稍后重试';
      toast.error(errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusWarning = (status: string) => {
    switch (status) {
      case 'sent':
        return '发送账单后，患者将收到账单通知';
      case 'confirmed':
        return '确认账单表示患者已同意账单内容';
      case 'paid':
        return '标记为已支付前，请确保所有款项已收到';
      case 'cancelled':
        return '取消账单后将无法恢复，请谨慎操作';
      default:
        return null;
    }
  };

  if (!canUpdateStatus) {
    return (
      <Badge className={currentStatusConfig.color}>
        <currentStatusConfig.icon className="h-3 w-3 mr-1" />
        {currentStatusConfig.label}
      </Badge>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <IconEdit className="h-4 w-4 mr-2" />
            更新状态
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>更新账单状态</DialogTitle>
          <DialogDescription>
            账单编号: {bill.billNumber}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Status */}
          <div>
            <Label className="text-sm font-medium">当前状态</Label>
            <div className="mt-1">
              <Badge className={currentStatusConfig.color}>
                <currentStatusConfig.icon className="h-3 w-3 mr-1" />
                {currentStatusConfig.label}
              </Badge>
              <p className="text-xs text-muted-foreground mt-1">
                {currentStatusConfig.description}
              </p>
            </div>
          </div>

          {/* New Status Selection */}
          <div>
            <Label htmlFor="status-select" className="text-sm font-medium">
              新状态
            </Label>
            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as typeof selectedStatus)}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="选择新状态" />
              </SelectTrigger>
              <SelectContent>
                {availableTransitions.map((status) => {
                  const config = statusConfig[status as keyof typeof statusConfig];
                  const Icon = config.icon;
                  return (
                    <SelectItem key={status} value={status}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <span>{config.label}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Status Warning */}
          {selectedStatus !== bill.status && getStatusWarning(selectedStatus) && (
            <Alert>
              <IconAlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {getStatusWarning(selectedStatus)}
              </AlertDescription>
            </Alert>
          )}

          {/* Special validation for paid status */}
          {selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0 && (
            <Alert variant="destructive">
              <IconAlertTriangle className="h-4 w-4" />
              <AlertDescription>
                账单还有 {billingUtils.formatCurrency(bill.remainingAmount || 0)} 未支付，
                无法标记为已支付状态。
              </AlertDescription>
            </Alert>
          )}

          {/* Notes */}
          <div>
            <Label htmlFor="notes" className="text-sm font-medium">
              备注 (可选)
            </Label>
            <Textarea
              id="notes"
              placeholder="添加状态更新的备注..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="mt-1 resize-none"
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Button
              onClick={handleStatusUpdate}
              disabled={
                isUpdating || 
                selectedStatus === bill.status ||
                (selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0)
              }
              className="flex-1"
            >
              {isUpdating ? '更新中...' : '确认更新'}
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setIsOpen(false);
                setSelectedStatus(bill.status);
                setNotes('');
              }}
              disabled={isUpdating}
            >
              取消
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export status configuration for use in other components
export { statusConfig, statusTransitions };
