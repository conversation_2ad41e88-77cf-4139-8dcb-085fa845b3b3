{"version": 3, "file": "../app/api/patients/[id]/interactions/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,iDCAA,sDCAA,6FCAA,wCCAA,2VCIO,IAAMA,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAChI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCK,CADoCL,CAAAA,CAC9B,GAAIM,GAAIL,CAAAA,EAAQI,GAAG,EAAXJ,EAGNM,QAASF,CAAAA,EAAIG,CAAJH,WAAgB,CAACI,GAAG,CAAC,OAAY,SAClDC,EAAOH,QAASF,CAAAA,EAAIG,CAAJH,WAAgB,CAACI,GAAG,CAAC,MAAW,QAChDE,EAAkBN,EAAIG,CAAJH,UAAAA,CAAgB,CAACI,GAAG,CAAC,mBACvCG,EAASP,EAAIG,CAAJH,CAAAA,UAAgB,CAACI,GAAG,CAAC,UAC9BI,EAAWR,EAAIG,CAAJH,GAAAA,QAAgB,CAACI,GAAG,CAAC,YAGlCK,EAAmB,CACrBC,OAAS,CADY,CACVC,MAAAA,CAAQd,EAAOe,EAAAA,CAC5B,EAEIN,IACFG,EAAYH,SADO,MACQ,CAAG,CAAEK,MAAQL,CAAAA,CAAgB,GAGtDC,IACFE,EAAYF,MAAM,CAAG,CAAEI,CAAvBF,KAA+BF,CAAAA,CAAO,GAGpCC,IACFC,EAAYD,EADA,MACQ,CAApBC,CAAyBE,MAAQH,CAAAA,CAAS,GAIxCb,IAAAA,IAAwB,KAAnBkB,IAAI,CACXJ,EAAYK,GAAG,CAAG,CAChBL,EACA,CACEM,CAHJN,CAGQ,EACF,CACEO,GAJNP,QAImB,EACXE,MAAAA,CAAQhB,EAAKsB,aACf,CACF,EACA,CACEX,eAAiB,EACfY,EAAI,EAAC,oBAAqB,uBAAwB,kBAAkB,CAExE,EACD,EAEJ,CACsB,YAAc,GAA5BvB,EAAKkB,EAALlB,EAAS,GAClBc,EAAYK,GAAG,CAAG,CAChBL,EACA,CACEH,CAHJG,OACEA,OAEmB,EACfS,EAAI,EAAC,aAAc,QAAS,kBAAkB,CAElD,EACD,EAGH,IAAMC,EAAO,MAAMrB,EAAcsB,WAAAA,WAAsB,CAAC,OACtDC,KAAAA,EACAhB,EACAiB,EADAjB,GACOI,CAAAA,EACPc,IAAM,KADCd,QAET,GAEA,MAAOe,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBL,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBM,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,sCAAwCA,CAAAA,GAC/CE,CAAAA,CAD+CF,CAAAA,EAC/CE,EAAAA,CAAoB,wCAC7B,CACF,CAAG,EClEG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAIH,EAAoB,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,iCAAiC,SACrD,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CACF,CAAC,CAIC,IAAC,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,6CACA,2CACA,iBACA,qDACA,CAAK,CACL,iIACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patients/[id]/interactions/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\n\nexport const GET = withAuthentication(async (user: Authenticated<PERSON>ser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const url = new URL(request.url);\n    \n    // Extract query parameters\n    const limit = parseInt(url.searchParams.get('limit') || '20');\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const interactionType = url.searchParams.get('interactionType');\n    const status = url.searchParams.get('status');\n    const priority = url.searchParams.get('priority');\n    \n    // Build where clause for this specific patient\n    let whereClause: any = {\n      patient: { equals: params.id },\n    };\n    \n    if (interactionType) {\n      whereClause.interactionType = { equals: interactionType };\n    }\n    \n    if (status) {\n      whereClause.status = { equals: status };\n    }\n    \n    if (priority) {\n      whereClause.priority = { equals: priority };\n    }\n    \n    // Apply role-based filtering\n    if (user.role === 'doctor') {\n      whereClause.and = [\n        whereClause,\n        {\n          or: [\n            {\n              staffMember: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              interactionType: {\n                in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],\n              },\n            },\n          ],\n        },\n      ];\n    } else if (user.role === 'front-desk') {\n      whereClause.and = [\n        whereClause,\n        {\n          interactionType: {\n            in: ['phone-call', 'email', 'billing-inquiry'],\n          },\n        },\n      ];\n    }\n    \n    const data = await payloadClient.getPatientInteractions({\n      limit,\n      page,\n      where: whereClause,\n      sort: '-timestamp', // Sort by timestamp descending (newest first)\n    });\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error fetching patient interactions:', error);\n    return createErrorResponse('Failed to fetch patient interactions');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patients/[id]/interactions',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patients\\\\[id]\\\\interactions\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patients/[id]/interactions/route\",\n        pathname: \"/api/patients/[id]/interactions\",\n        filename: \"route\",\n        bundlePath: \"app/api/patients/[id]/interactions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patients\\\\[id]\\\\interactions\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "params", "payloadClient", "createPayloadClient", "url", "URL", "parseInt", "searchParams", "get", "page", "interactionType", "status", "priority", "<PERSON><PERSON><PERSON><PERSON>", "patient", "equals", "id", "role", "and", "or", "staffMember", "payloadUserId", "in", "data", "getPatientInteractions", "limit", "where", "sort", "createSuccessResponse", "error", "console", "createErrorResponse", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}