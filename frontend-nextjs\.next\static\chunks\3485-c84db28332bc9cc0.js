try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="********-15d2-452f-842c-5e2d8e386d2e",e._sentryDebugIdIdentifier="sentry-dbid-********-15d2-452f-842c-5e2d8e386d2e")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3485],{750:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","building-bank","IconBuildingBank",[["path",{d:"M3 21l18 0",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M5 6l7 -3l7 3",key:"svg-2"}],["path",{d:"M4 10l0 11",key:"svg-3"}],["path",{d:"M20 10l0 11",key:"svg-4"}],["path",{d:"M8 14l0 3",key:"svg-5"}],["path",{d:"M12 14l0 3",key:"svg-6"}],["path",{d:"M16 14l0 3",key:"svg-7"}]])},7964:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]])},12987:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","brand-alipay","IconBrandAlipay",[["path",{d:"M19 3h-14a2 2 0 0 0 -2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2v-14a2 2 0 0 0 -2 -2z",key:"svg-0"}],["path",{d:"M7 7h10",key:"svg-1"}],["path",{d:"M12 3v7",key:"svg-2"}],["path",{d:"M21 17.314c-2.971 -1.923 -15 -8.779 -15 -1.864c0 1.716 1.52 2.55 2.985 2.55c3.512 0 6.814 -5.425 6.814 -8h-6.604",key:"svg-3"}]])},16661:(e,a,t)=>{t.d(a,{UC:()=>S,ZL:()=>K,bL:()=>U,l9:()=>q});var r=t(99004),n=t(84732),o=t(39552),d=t(38774),l=t(17430),s=t(6280),v=t(40201),i=t(29548),h=t(97677),p=t(55173),u=t(22474),c=t(51452),g=t(50516),y=t(18608),k=t(10144),f=t(92350),M=t(52880),A="Popover",[b,m]=(0,d.A)(A,[h.Bk]),C=(0,h.Bk)(),[I,x]=b(A),w=e=>{let{__scopePopover:a,children:t,open:n,defaultOpen:o,onOpenChange:d,modal:l=!1}=e,s=C(a),v=r.useRef(null),[p,u]=r.useState(!1),[c=!1,g]=(0,y.i)({prop:n,defaultProp:o,onChange:d});return(0,M.jsx)(h.bL,{...s,children:(0,M.jsx)(I,{scope:a,contentId:(0,i.B)(),triggerRef:v,open:c,onOpenChange:g,onOpenToggle:r.useCallback(()=>g(e=>!e),[g]),hasCustomAnchor:p,onCustomAnchorAdd:r.useCallback(()=>u(!0),[]),onCustomAnchorRemove:r.useCallback(()=>u(!1),[]),modal:l,children:t})})};w.displayName=A;var j="PopoverAnchor";r.forwardRef((e,a)=>{let{__scopePopover:t,...n}=e,o=x(j,t),d=C(t),{onCustomAnchorAdd:l,onCustomAnchorRemove:s}=o;return r.useEffect(()=>(l(),()=>s()),[l,s]),(0,M.jsx)(h.Mz,{...d,...n,ref:a})}).displayName=j;var R="PopoverTrigger",P=r.forwardRef((e,a)=>{let{__scopePopover:t,...r}=e,d=x(R,t),l=C(t),s=(0,o.s)(a,d.triggerRef),v=(0,M.jsx)(c.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":d.open,"aria-controls":d.contentId,"data-state":L(d.open),...r,ref:s,onClick:(0,n.m)(e.onClick,d.onOpenToggle)});return d.hasCustomAnchor?v:(0,M.jsx)(h.Mz,{asChild:!0,...l,children:v})});P.displayName=R;var D="PopoverPortal",[z,F]=b(D,{forceMount:void 0}),E=e=>{let{__scopePopover:a,forceMount:t,children:r,container:n}=e,o=x(D,a);return(0,M.jsx)(z,{scope:a,forceMount:t,children:(0,M.jsx)(u.C,{present:t||o.open,children:(0,M.jsx)(p.Z,{asChild:!0,container:n,children:r})})})};E.displayName=D;var _="PopoverContent",N=r.forwardRef((e,a)=>{let t=F(_,e.__scopePopover),{forceMount:r=t.forceMount,...n}=e,o=x(_,e.__scopePopover);return(0,M.jsx)(u.C,{present:r||o.open,children:o.modal?(0,M.jsx)(O,{...n,ref:a}):(0,M.jsx)(T,{...n,ref:a})})});N.displayName=_;var O=r.forwardRef((e,a)=>{let t=x(_,e.__scopePopover),d=r.useRef(null),l=(0,o.s)(a,d),s=r.useRef(!1);return r.useEffect(()=>{let e=d.current;if(e)return(0,k.Eq)(e)},[]),(0,M.jsx)(f.A,{as:g.DX,allowPinchZoom:!0,children:(0,M.jsx)(B,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{var a;e.preventDefault(),s.current||null==(a=t.triggerRef.current)||a.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let a=e.detail.originalEvent,t=0===a.button&&!0===a.ctrlKey;s.current=2===a.button||t},{checkForDefaultPrevented:!1}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),T=r.forwardRef((e,a)=>{let t=x(_,e.__scopePopover),n=r.useRef(!1),o=r.useRef(!1);return(0,M.jsx)(B,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var r,d;null==(r=e.onCloseAutoFocus)||r.call(e,a),a.defaultPrevented||(n.current||null==(d=t.triggerRef.current)||d.focus(),a.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:a=>{var r,d;null==(r=e.onInteractOutside)||r.call(e,a),a.defaultPrevented||(n.current=!0,"pointerdown"===a.detail.originalEvent.type&&(o.current=!0));let l=a.target;(null==(d=t.triggerRef.current)?void 0:d.contains(l))&&a.preventDefault(),"focusin"===a.detail.originalEvent.type&&o.current&&a.preventDefault()}})}),B=r.forwardRef((e,a)=>{let{__scopePopover:t,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:o,disableOutsidePointerEvents:d,onEscapeKeyDown:i,onPointerDownOutside:p,onFocusOutside:u,onInteractOutside:c,...g}=e,y=x(_,t),k=C(t);return(0,s.Oh)(),(0,M.jsx)(v.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:o,children:(0,M.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:d,onInteractOutside:c,onEscapeKeyDown:i,onPointerDownOutside:p,onFocusOutside:u,onDismiss:()=>y.onOpenChange(!1),children:(0,M.jsx)(h.UC,{"data-state":L(y.open),role:"dialog",id:y.contentId,...k,...g,ref:a,style:{...g.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),G="PopoverClose";function L(e){return e?"open":"closed"}r.forwardRef((e,a)=>{let{__scopePopover:t,...r}=e,o=x(G,t);return(0,M.jsx)(c.sG.button,{type:"button",...r,ref:a,onClick:(0,n.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=G,r.forwardRef((e,a)=>{let{__scopePopover:t,...r}=e,n=C(t);return(0,M.jsx)(h.i3,{...n,...r,ref:a})}).displayName="PopoverArrow";var U=w,q=P,K=E,S=N},18183:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","refresh","IconRefresh",[["path",{d:"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4",key:"svg-0"}],["path",{d:"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4",key:"svg-1"}]])},21747:(e,a,t)=>{t.d(a,{B8:()=>P,UC:()=>z,bL:()=>R,l9:()=>D});var r=t(99004),n=t(84732),o=t(38774),d=t(59949),l=t(22474),s=t(51452),v=t(51825),i=t(18608),h=t(29548),p=t(52880),u="Tabs",[c,g]=(0,o.A)(u,[d.RG]),y=(0,d.RG)(),[k,f]=c(u),M=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,onValueChange:n,defaultValue:o,orientation:d="horizontal",dir:l,activationMode:u="automatic",...c}=e,g=(0,v.jH)(l),[y,f]=(0,i.i)({prop:r,onChange:n,defaultProp:o});return(0,p.jsx)(k,{scope:t,baseId:(0,h.B)(),value:y,onValueChange:f,orientation:d,dir:g,activationMode:u,children:(0,p.jsx)(s.sG.div,{dir:g,"data-orientation":d,...c,ref:a})})});M.displayName=u;var A="TabsList",b=r.forwardRef((e,a)=>{let{__scopeTabs:t,loop:r=!0,...n}=e,o=f(A,t),l=y(t);return(0,p.jsx)(d.bL,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:r,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":o.orientation,...n,ref:a})})});b.displayName=A;var m="TabsTrigger",C=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,disabled:o=!1,...l}=e,v=f(m,t),i=y(t),h=w(v.baseId,r),u=j(v.baseId,r),c=r===v.value;return(0,p.jsx)(d.q7,{asChild:!0,...i,focusable:!o,active:c,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":c,"aria-controls":u,"data-state":c?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:h,...l,ref:a,onMouseDown:(0,n.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():v.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&v.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==v.activationMode;c||o||!e||v.onValueChange(r)})})})});C.displayName=m;var I="TabsContent",x=r.forwardRef((e,a)=>{let{__scopeTabs:t,value:n,forceMount:o,children:d,...v}=e,i=f(I,t),h=w(i.baseId,n),u=j(i.baseId,n),c=n===i.value,g=r.useRef(c);return r.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(l.C,{present:o||c,children:t=>{let{present:r}=t;return(0,p.jsx)(s.sG.div,{"data-state":c?"active":"inactive","data-orientation":i.orientation,role:"tabpanel","aria-labelledby":h,hidden:!r,id:u,tabIndex:0,...v,ref:a,style:{...e.style,animationDuration:g.current?"0s":void 0},children:r&&d})}})});function w(e,a){return"".concat(e,"-trigger-").concat(a)}function j(e,a){return"".concat(e,"-content-").concat(a)}x.displayName=I;var R=M,P=b,D=C,z=x},26151:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","chart-bar","IconChartBar",[["path",{d:"M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-0"}],["path",{d:"M15 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-1"}],["path",{d:"M9 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z",key:"svg-2"}],["path",{d:"M4 20h14",key:"svg-3"}]])},26368:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]])},27709:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","cash","IconCash",[["path",{d:"M7 15h-3a1 1 0 0 1 -1 -1v-8a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v3",key:"svg-0"}],["path",{d:"M7 9m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z",key:"svg-1"}],["path",{d:"M12 14a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-2"}]])},29118:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","filter","IconFilter",[["path",{d:"M4 4h16v2.172a2 2 0 0 1 -.586 1.414l-4.414 4.414v7l-6 2v-8.5l-4.48 -4.928a2 2 0 0 1 -.52 -1.345v-2.227z",key:"svg-0"}]])},40773:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]])},42486:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","currency-yuan","IconCurrencyYuan",[["path",{d:"M12 19v-7l-5 -7",key:"svg-0"}],["path",{d:"M17 5l-5 7",key:"svg-1"}],["path",{d:"M8 13h8",key:"svg-2"}]])},46976:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","alert-triangle","IconAlertTriangle",[["path",{d:"M12 9v4",key:"svg-0"}],["path",{d:"M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},47889:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","stethoscope","IconStethoscope",[["path",{d:"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1",key:"svg-0"}],["path",{d:"M8 15a6 6 0 1 0 12 0v-3",key:"svg-1"}],["path",{d:"M11 3v2",key:"svg-2"}],["path",{d:"M6 3v2",key:"svg-3"}],["path",{d:"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-4"}]])},49314:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","receipt","IconReceipt",[["path",{d:"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2m4 -14h6m-6 4h6m-2 4h2",key:"svg-0"}]])},57165:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","user","IconUser",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}]])},59196:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","device-mobile","IconDeviceMobile",[["path",{d:"M6 5a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2v-14z",key:"svg-0"}],["path",{d:"M11 4h2",key:"svg-1"}],["path",{d:"M12 17v.01",key:"svg-2"}]])},60382:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]])},60664:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},61654:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","printer","IconPrinter",[["path",{d:"M17 17h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-14a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2",key:"svg-0"}],["path",{d:"M17 9v-4a2 2 0 0 0 -2 -2h-6a2 2 0 0 0 -2 2v4",key:"svg-1"}],["path",{d:"M7 13m0 2a2 2 0 0 1 2 -2h6a2 2 0 0 1 2 2v4a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2z",key:"svg-2"}]])},66444:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},68046:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","x","IconX",[["path",{d:"M18 6l-12 12",key:"svg-0"}],["path",{d:"M6 6l12 12",key:"svg-1"}]])},68065:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","mail","IconMail",[["path",{d:"M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z",key:"svg-0"}],["path",{d:"M3 7l9 6l9 -6",key:"svg-1"}]])},71861:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","eye","IconEye",[["path",{d:"M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6",key:"svg-1"}]])},72486:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","calendar","IconCalendar",[["path",{d:"M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z",key:"svg-0"}],["path",{d:"M16 3v4",key:"svg-1"}],["path",{d:"M8 3v4",key:"svg-2"}],["path",{d:"M4 11h16",key:"svg-3"}],["path",{d:"M11 15h1",key:"svg-4"}],["path",{d:"M12 15v3",key:"svg-5"}]])},73387:(e,a,t)=>{t.d(a,{b:()=>v});var r=t(99004),n=t(51452),o=t(52880),d="horizontal",l=["horizontal","vertical"],s=r.forwardRef((e,a)=>{var t;let{decorative:r,orientation:s=d,...v}=e,i=(t=s,l.includes(t))?s:d;return(0,o.jsx)(n.sG.div,{"data-orientation":i,...r?{role:"none"}:{"aria-orientation":"vertical"===i?i:void 0,role:"separator"},...v,ref:a})});s.displayName="Separator";var v=s},82168:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","download","IconDownload",[["path",{d:"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M7 11l5 5l5 -5",key:"svg-1"}],["path",{d:"M12 4l0 12",key:"svg-2"}]])},92708:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},92775:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","file-text","IconFileText",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}],["path",{d:"M9 9l1 0",key:"svg-2"}],["path",{d:"M9 13l6 0",key:"svg-3"}],["path",{d:"M9 17l6 0",key:"svg-4"}]])},93900:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])},95108:(e,a,t)=>{t.d(a,{A:()=>r});var r=(0,t(49202).A)("outline","calendar-event","IconCalendarEvent",[["path",{d:"M4 5m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z",key:"svg-0"}],["path",{d:"M16 3l0 4",key:"svg-1"}],["path",{d:"M8 3l0 4",key:"svg-2"}],["path",{d:"M4 11l16 0",key:"svg-3"}],["path",{d:"M8 15h2v2h-2z",key:"svg-4"}]])}}]);