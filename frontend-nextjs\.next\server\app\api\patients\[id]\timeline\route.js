try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="44fd9b8a-0770-4807-92bc-6415122fb05a",e._sentryDebugIdIdentifier="sentry-dbid-44fd9b8a-0770-4807-92bc-6415122fb05a")}catch(e){}"use strict";(()=>{var e={};e.id=5228,e.ids=[5228],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94467:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>P,serverHooks:()=>k,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>b});var i={};r.r(i),r.d(i,{DELETE:()=>f,GET:()=>m,HEAD:()=>w,OPTIONS:()=>T,PATCH:()=>h,POST:()=>g,PUT:()=>q});var s=r(86047),o=r(85544),a=r(36135),n=r(63033),p=r(53547),d=r(54360),u=r(19761);let l=(0,p.ZA)(async(e,t,{params:r})=>{try{let i=(0,d.o)(e),s=new URL(t.url),o=parseInt(s.searchParams.get("limit")||"50"),a=parseInt(s.searchParams.get("page")||"1"),n=s.searchParams.get("type"),u=[];if(!n||"interaction"===n){let t={patient:{equals:r.id}};"doctor"===e.role?t.and=[t,{or:[{staffMember:{equals:e.payloadUserId}},{interactionType:{in:["consultation-note","treatment-discussion","in-person-visit"]}}]}]:"front-desk"===e.role&&(t.and=[t,{interactionType:{in:["phone-call","email","billing-inquiry"]}}]),(await i.getPatientInteractions({limit:100,page:1,where:t,sort:"-timestamp"})).docs.forEach(e=>{u.push({id:e.id,type:"interaction",timestamp:e.timestamp,title:e.title,status:e.status,priority:e.priority,staffMember:e.staffMember,data:e})})}if(!n||"task"===n){let t={patient:{equals:r.id}};"doctor"===e.role?t.and=[t,{or:[{assignedTo:{equals:e.payloadUserId}},{createdBy:{equals:e.payloadUserId}},{taskType:{in:["treatment-reminder","medical-record-update","consultation-follow-up"]}}]}]:"front-desk"===e.role&&(t.and=[t,{or:[{assignedTo:{equals:e.payloadUserId}},{taskType:{in:["follow-up-call","appointment-scheduling","billing-follow-up"]}}]}]),(await i.getPatientTasks({limit:100,page:1,where:t,sort:"-dueDate"})).docs.forEach(e=>{u.push({id:e.id,type:"task",timestamp:e.dueDate,title:e.title,status:e.status,priority:e.priority,assignedTo:e.assignedTo,createdBy:e.createdBy,data:e})})}u.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime());let l=(a-1)*o,c=l+o,x={docs:u.slice(l,c),totalDocs:u.length,limit:o,page:a,totalPages:Math.ceil(u.length/o),hasNextPage:c<u.length,hasPrevPage:a>1,nextPage:c<u.length?a+1:null,prevPage:a>1?a-1:null};return(0,p.$y)(x)}catch(e){return console.error("Error fetching patient timeline:",e),(0,p.WX)("Failed to fetch patient timeline")}}),c={...n},x="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;function y(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,r,i)=>{let s;try{let e=x?.getStore();s=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/patients/[id]/timeline",headers:s}).apply(r,i)}})}let m=y(l,"GET"),g=y(void 0,"POST"),q=y(void 0,"PUT"),h=y(void 0,"PATCH"),f=y(void 0,"DELETE"),w=y(void 0,"HEAD"),T=y(void 0,"OPTIONS"),P=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/patients/[id]/timeline/route",pathname:"/api/patients/[id]/timeline",filename:"route",bundlePath:"app/api/patients/[id]/timeline/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\timeline\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:v,workUnitAsyncStorage:b,serverHooks:k}=P;function E(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:b})}},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[55,3738,1950,5886,9615,125],()=>r(94467));module.exports=i})();
//# sourceMappingURL=route.js.map