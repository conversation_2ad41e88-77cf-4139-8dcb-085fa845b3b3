// Comprehensive input validation and sanitization for billing system
// Prevents SQL injection, XSS, and other security vulnerabilities

import { BillingErrorHandler } from './billing-error-handler';

// Validation schemas and rules
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'email' | 'phone' | 'date' | 'currency' | 'id';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  customValidator?: (value: any) => boolean;
  sanitizer?: (value: any) => any;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

// Common validation patterns
const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  currency: /^\d+(\.\d{1,2})?$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  chineseName: /^[\u4e00-\u9fa5]{2,10}$/,
  billNumber: /^BILL-\d{8}-\d{6}$/,
  receiptNumber: /^REC-\d{8}-[A-Z0-9]{6}$/,
  depositNumber: /^DEP-\d{8}-\d{6}$/,
  transactionId: /^TXN-[A-Z0-9]{9,20}$/,
};

// Billing-specific validation schemas
export const BILLING_SCHEMAS = {
  createBill: {
    patient: { required: true, type: 'id' as const },
    billType: { required: true, type: 'string' as const, pattern: /^(consultation|treatment|deposit|other)$/ },
    subtotal: { required: true, type: 'currency' as const, min: 0, max: 50000 },
    discountAmount: { type: 'currency' as const, min: 0, max: 10000 },
    taxAmount: { type: 'currency' as const, min: 0, max: 5000 },
    totalAmount: { required: true, type: 'currency' as const, min: 0, max: 50000 },
    dueDate: { type: 'date' as const },
    notes: { type: 'string' as const, maxLength: 1000 },
  },

  processPayment: {
    billId: { required: true, type: 'id' as const },
    amount: { required: true, type: 'currency' as const, min: 0.01, max: 50000 },
    paymentMethod: { required: true, type: 'string' as const, pattern: /^(cash|card|wechat|alipay|bank-transfer|installment)$/ },
    transactionId: { type: 'string' as const, pattern: VALIDATION_PATTERNS.transactionId },
    notes: { type: 'string' as const, maxLength: 500 },
  },

  createDeposit: {
    patient: { required: true, type: 'id' as const },
    amount: { required: true, type: 'currency' as const, min: 1, max: 20000 },
    purpose: { required: true, type: 'string' as const, maxLength: 200 },
    expiryDate: { type: 'date' as const },
    notes: { type: 'string' as const, maxLength: 500 },
  },

  refundPayment: {
    paymentId: { required: true, type: 'id' as const },
    amount: { required: true, type: 'currency' as const, min: 0.01, max: 50000 },
    reason: { required: true, type: 'string' as const, maxLength: 500 },
    refundMethod: { type: 'string' as const, pattern: /^(original|cash|bank-transfer)$/ },
  },

  searchBills: {
    patientId: { type: 'id' as const },
    status: { type: 'string' as const, pattern: /^(draft|pending|paid|overdue|cancelled)$/ },
    startDate: { type: 'date' as const },
    endDate: { type: 'date' as const },
    minAmount: { type: 'currency' as const, min: 0 },
    maxAmount: { type: 'currency' as const, min: 0, max: 100000 },
    limit: { type: 'number' as const, min: 1, max: 1000 },
    page: { type: 'number' as const, min: 1 },
  },
};

export class BillingValidator {
  /**
   * Validate data against a schema
   */
  static validate(data: any, schema: ValidationSchema): { isValid: boolean; errors: string[]; sanitizedData: any } {
    const errors: string[] = [];
    const sanitizedData: any = {};

    for (const [field, rule] of Object.entries(schema)) {
      const value = data[field];

      try {
        // Check required fields
        if (rule.required && (value === undefined || value === null || value === '')) {
          errors.push(`${field} 是必填字段`);
          continue;
        }

        // Skip validation for optional empty fields
        if (!rule.required && (value === undefined || value === null || value === '')) {
          continue;
        }

        // Type validation and sanitization
        const sanitizedValue = this.validateAndSanitizeField(field, value, rule);
        sanitizedData[field] = sanitizedValue;

      } catch (error) {
        errors.push(error instanceof Error ? error.message : `${field} 验证失败`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData,
    };
  }

  /**
   * Validate and sanitize a single field
   */
  private static validateAndSanitizeField(field: string, value: any, rule: ValidationRule): any {
    let sanitizedValue = value;

    // Apply custom sanitizer first
    if (rule.sanitizer) {
      sanitizedValue = rule.sanitizer(sanitizedValue);
    }

    // Type validation
    switch (rule.type) {
      case 'string':
        sanitizedValue = this.validateString(field, sanitizedValue, rule);
        break;
      case 'number':
        sanitizedValue = this.validateNumber(field, sanitizedValue, rule);
        break;
      case 'currency':
        sanitizedValue = this.validateCurrency(field, sanitizedValue, rule);
        break;
      case 'email':
        sanitizedValue = this.validateEmail(field, sanitizedValue);
        break;
      case 'phone':
        sanitizedValue = this.validatePhone(field, sanitizedValue);
        break;
      case 'date':
        sanitizedValue = this.validateDate(field, sanitizedValue);
        break;
      case 'id':
        sanitizedValue = this.validateId(field, sanitizedValue);
        break;
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(String(sanitizedValue))) {
      throw new Error(`${field} 格式不正确`);
    }

    // Custom validation
    if (rule.customValidator && !rule.customValidator(sanitizedValue)) {
      throw new Error(`${field} 自定义验证失败`);
    }

    return sanitizedValue;
  }

  /**
   * Validate string fields
   */
  private static validateString(field: string, value: any, rule: ValidationRule): string {
    if (typeof value !== 'string') {
      throw new Error(`${field} 必须是字符串`);
    }

    // Sanitize HTML and potential XSS
    let sanitized = this.sanitizeHtml(value);

    // Length validation
    if (rule.minLength && sanitized.length < rule.minLength) {
      throw new Error(`${field} 长度不能少于 ${rule.minLength} 个字符`);
    }

    if (rule.maxLength && sanitized.length > rule.maxLength) {
      throw new Error(`${field} 长度不能超过 ${rule.maxLength} 个字符`);
    }

    return sanitized;
  }

  /**
   * Validate number fields
   */
  private static validateNumber(field: string, value: any, rule: ValidationRule): number {
    const num = Number(value);
    
    if (isNaN(num)) {
      throw new Error(`${field} 必须是有效数字`);
    }

    if (rule.min !== undefined && num < rule.min) {
      throw new Error(`${field} 不能小于 ${rule.min}`);
    }

    if (rule.max !== undefined && num > rule.max) {
      throw new Error(`${field} 不能大于 ${rule.max}`);
    }

    return num;
  }

  /**
   * Validate currency fields
   */
  private static validateCurrency(field: string, value: any, rule: ValidationRule): number {
    const num = this.validateNumber(field, value, rule);

    // Ensure currency has at most 2 decimal places
    const rounded = Math.round(num * 100) / 100;
    
    if (Math.abs(num - rounded) > 0.001) {
      throw new Error(`${field} 最多只能有两位小数`);
    }

    return rounded;
  }

  /**
   * Validate email fields
   */
  private static validateEmail(field: string, value: any): string {
    if (typeof value !== 'string') {
      throw new Error(`${field} 必须是字符串`);
    }

    const sanitized = value.trim().toLowerCase();
    
    if (!VALIDATION_PATTERNS.email.test(sanitized)) {
      throw new Error(`${field} 邮箱格式不正确`);
    }

    return sanitized;
  }

  /**
   * Validate phone fields
   */
  private static validatePhone(field: string, value: any): string {
    if (typeof value !== 'string') {
      throw new Error(`${field} 必须是字符串`);
    }

    const sanitized = value.replace(/\s+/g, '');
    
    if (!VALIDATION_PATTERNS.phone.test(sanitized)) {
      throw new Error(`${field} 电话号码格式不正确`);
    }

    return sanitized;
  }

  /**
   * Validate date fields
   */
  private static validateDate(field: string, value: any): string {
    const date = new Date(value);
    
    if (isNaN(date.getTime())) {
      throw new Error(`${field} 日期格式不正确`);
    }

    return date.toISOString();
  }

  /**
   * Validate ID fields
   */
  private static validateId(field: string, value: any): string {
    if (typeof value !== 'string') {
      throw new Error(`${field} ID必须是字符串`);
    }

    const sanitized = value.trim();
    
    if (sanitized.length === 0) {
      throw new Error(`${field} ID不能为空`);
    }

    // Basic ID format validation (alphanumeric, hyphens, underscores)
    if (!/^[a-zA-Z0-9_-]+$/.test(sanitized)) {
      throw new Error(`${field} ID格式不正确`);
    }

    return sanitized;
  }

  /**
   * Sanitize HTML content to prevent XSS
   */
  private static sanitizeHtml(input: string): string {
    return input
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .trim();
  }

  /**
   * Validate bill creation data
   */
  static validateBillData(data: any): any {
    const result = this.validate(data, BILLING_SCHEMAS.createBill);
    
    if (!result.isValid) {
      const error = new Error('账单数据验证失败');
      (error as any).code = 'INVALID_PAYMENT_AMOUNT';
      (error as any).details = result.errors;
      throw error;
    }

    // Additional business logic validation
    const { subtotal, discountAmount = 0, taxAmount = 0, totalAmount } = result.sanitizedData;
    const calculatedTotal = subtotal - discountAmount + taxAmount;
    
    if (Math.abs(totalAmount - calculatedTotal) > 0.01) {
      const error = new Error('账单总额计算错误');
      (error as any).code = 'INVALID_PAYMENT_AMOUNT';
      (error as any).details = {
        expected: calculatedTotal,
        received: totalAmount
      };
      throw error;
    }

    return result.sanitizedData;
  }

  /**
   * Validate payment data
   */
  static validatePaymentData(data: any): any {
    const result = this.validate(data, BILLING_SCHEMAS.processPayment);
    
    if (!result.isValid) {
      const error = new Error('支付数据验证失败');
      (error as any).code = 'PAYMENT_METHOD_ERROR';
      (error as any).details = result.errors;
      throw error;
    }

    return result.sanitizedData;
  }

  /**
   * Validate deposit data
   */
  static validateDepositData(data: any): any {
    const result = this.validate(data, BILLING_SCHEMAS.createDeposit);
    
    if (!result.isValid) {
      throw BillingErrorHandler.createError(
        'INVALID_PAYMENT_AMOUNT',
        { errors: result.errors }
      );
    }

    return result.sanitizedData;
  }

  /**
   * Validate search parameters
   */
  static validateSearchParams(data: any): any {
    const result = this.validate(data, BILLING_SCHEMAS.searchBills);
    
    if (!result.isValid) {
      throw BillingErrorHandler.createError(
        'VALIDATION_ERROR',
        { errors: result.errors }
      );
    }

    // Validate date range if both dates are provided
    const { startDate, endDate } = result.sanitizedData;
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start > end) {
        throw BillingErrorHandler.createError(
          'VALIDATION_ERROR',
          { message: '开始日期不能晚于结束日期' }
        );
      }
    }

    return result.sanitizedData;
  }
}
