try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="47ae408a-9d27-4f76-bcf7-001e193c3ecd",e._sentryDebugIdIdentifier="sentry-dbid-47ae408a-9d27-4f76-bcf7-001e193c3ecd")}catch(e){}(()=>{var e={};e.id=4131,e.ids=[4131],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14435:(e,r,t)=>{"use strict";t.r(r),t.d(r,{Toaster:()=>o,toast:()=>a,useSonner:()=>n});var s=t(91611);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0\\node_modules\\sonner\\dist\\index.mjs","Toaster"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0\\node_modules\\sonner\\dist\\index.mjs","toast"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0\\node_modules\\sonner\\dist\\index.mjs","useSonner")},16599:(e,r,t)=>{Promise.resolve().then(t.bind(t,14435))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21327:(e,r,t)=>{Promise.resolve().then(t.bind(t,85001))},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36075:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>Y,routeModule:()=>k,serverHooks:()=>V,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>F});var s={};t.r(s),t.d(s,{DELETE:()=>U,GET:()=>D,HEAD:()=>L,OPTIONS:()=>j,PATCH:()=>M,POST:()=>P,PUT:()=>q});var o=t(86047),a=t(85544),n=t(36135),i=t(63033),d=t(35886),c=t(79615),u=t(87920),l=t(14435);let p={AUTH_REQUIRED:{message:"请先登录以继续操作",severity:"error"},AUTH_SERVICE_ERROR:{message:"认证服务暂时不可用，请稍后重试",severity:"error"},CLERK_USER_FETCH_ERROR:{message:"获取用户信息失败，请重新登录",severity:"error"},USER_EMAIL_MISSING:{message:"用户邮箱信息缺失，请联系管理员",severity:"error"},VALIDATION_ERROR:{message:"输入数据验证失败，请检查表单内容",severity:"error"},INVALID_JSON:{message:"数据格式错误，请刷新页面重试",severity:"error"},INVALID_LIMIT:{message:"分页参数错误",severity:"warning"},INVALID_PAGE:{message:"页码参数错误",severity:"warning"},SUBTOTAL_MISMATCH:{message:"账单金额计算错误，请重新检查",severity:"error"},INVALID_PAYMENT_AMOUNT:{message:"支付金额无效",severity:"error"},BILL_NOT_FOUND:{message:"账单不存在或已被删除",severity:"error"},PAYMENT_METHOD_ERROR:{message:"支付方式验证失败",severity:"error"},INSUFFICIENT_PERMISSIONS:{message:"权限不足，无法执行此操作",severity:"error"},BACKEND_ERROR:{message:"后端服务错误",severity:"error"},BACKEND_SERVICE_ERROR:{message:"后端服务暂时不可用，请稍后重试",severity:"error"},DATABASE_ERROR:{message:"数据库操作失败，请稍后重试",severity:"error"},NETWORK_ERROR:{message:"网络连接错误，请检查网络连接",severity:"error"},RATE_LIMIT_EXCEEDED:{message:"操作过于频繁，请稍后再试",severity:"error"},SUSPICIOUS_ACTIVITY:{message:"检测到可疑活动，操作已被阻止",severity:"error"},SESSION_EXPIRED:{message:"会话已过期，请重新登录",severity:"error"},INVALID_AUTHENTICATION:{message:"身份验证失败",severity:"error"},UNAUTHORIZED_ACCESS:{message:"未授权访问，请先登录",severity:"error"},PAYMENT_GATEWAY_ERROR:{message:"支付网关错误，请稍后重试",severity:"error"},CARD_DECLINED:{message:"银行卡被拒绝，请检查卡片状态",severity:"error"},CARD_EXPIRED:{message:"银行卡已过期，请使用其他卡片",severity:"error"},INVALID_CARD_NUMBER:{message:"银行卡号无效",severity:"error"},TRANSACTION_TIMEOUT:{message:"交易超时，请重新尝试",severity:"error"},DUPLICATE_TRANSACTION:{message:"重复交易，请勿重复提交",severity:"error"},INSUFFICIENT_FUNDS:{message:"余额不足，无法完成支付",severity:"error"},PAYMENT_ALREADY_PROCESSED:{message:"支付已处理，请勿重复操作",severity:"error"},DEPOSIT_EXPIRED:{message:"押金已过期",severity:"error"},DEPOSIT_INSUFFICIENT_BALANCE:{message:"押金余额不足",severity:"error"},REFUND_AMOUNT_EXCEEDS_PAYMENT:{message:"退款金额超过支付金额",severity:"error"},DEPOSIT_NOT_FOUND:{message:"未找到指定押金记录",severity:"error"},CONFIGURATION_ERROR:{message:"系统配置错误，请联系管理员",severity:"error"},EXTERNAL_SERVICE_ERROR:{message:"外部服务错误，请稍后重试",severity:"error"},INTERNAL_ERROR:{message:"系统内部错误，请稍后重试",severity:"error"},UNKNOWN_ERROR:{message:"发生未知错误，请联系技术支持",severity:"error"}};class m{constructor(){this.errorLog=[]}static getInstance(){return m.instance||(m.instance=new m),m.instance}handleAPIError(e,r={}){let t,{showToast:s=!0,logError:o=!0,context:a="API",fallbackMessage:n="操作失败，请稍后重试"}=r;if(e?.code&&p[e.code]){let r=p[e.code];t={code:e.code,message:e.message||r.message,userMessage:e.message||r.message,severity:r.severity,details:e.details,timestamp:new Date,context:a}}else t={code:"UNKNOWN_ERROR",message:e?.message||"Unknown error occurred",userMessage:n,severity:"error",details:e,timestamp:new Date,context:a};return o&&this.logError(t),s&&this.showErrorToast(t),t}handleNetworkError(e,r={}){let t={code:"NETWORK_ERROR",message:e?.message||"Network request failed",userMessage:"网络连接错误，请检查网络连接后重试",severity:"error",details:e,timestamp:new Date,context:r.context||"Network"};return!1!==r.logError&&this.logError(t),!1!==r.showToast&&this.showErrorToast(t),t}handleValidationError(e,r={}){let t=e[0],s={code:"VALIDATION_ERROR",message:"Validation failed",userMessage:t?.message||"表单验证失败，请检查输入内容",severity:"error",details:e,timestamp:new Date,context:r.context||"Validation"};return!1!==r.logError&&this.logError(s),!1!==r.showToast&&this.showErrorToast(s),s}showSuccess(e,r){l.toast.success(e,{description:r,duration:3e3})}showWarning(e,r){l.toast.warning(e,{description:r,duration:4e3})}showInfo(e,r){l.toast.info(e,{description:r,duration:3e3})}logError(e){console.error(`[${e.context}] ${e.code}: ${e.message}`,{userMessage:e.userMessage,details:e.details,timestamp:e.timestamp}),this.errorLog.push(e),this.errorLog.length>100&&this.errorLog.shift()}showErrorToast(e){let r={duration:"error"===e.severity?5e3:4e3};switch(e.severity){case"error":l.toast.error(e.userMessage,r);break;case"warning":l.toast.warning(e.userMessage,r);break;case"info":l.toast.info(e.userMessage,r)}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}let E=m.getInstance(),R=(e,r)=>E.handleAPIError(e,r),_=(e,r)=>E.handleNetworkError(e,r),y=async(e,r=3,t=1e3,s)=>{let o;for(let a=1;a<=r;a++)try{return await e()}catch(n){if(o=n,a===r)throw R(n,{context:s||"Retry",fallbackMessage:`操作失败，已重试${r}次`}),n;let e=t*Math.pow(2,a-1);await new Promise(r=>setTimeout(r,e))}throw o};class g extends Error{constructor(e,r,t,s){super(e),this.status=r,this.code=t,this.details=s,this.name="BillingAPIError"}}let h={isValidPaymentAmount:(e,r)=>{let t=r.remainingAmount||0;return e>t?{valid:!1,reason:`支付金额不能超过待付金额 $${t.toFixed(2)}`}:e<=0?{valid:!1,reason:"支付金额必须大于0"}:{valid:!0}}};var I=t(13446),A=t(19761);let T="http://localhost:8002";class N extends Error{constructor(e,r=500,t,s){super(e),this.status=r,this.code=t,this.details=s,this.name="APIError"}}async function v(e){try{let r=await fetch(`https://api.clerk.com/v1/users/${e}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}});if(!r.ok)throw new N("Failed to fetch user information",401,"CLERK_USER_FETCH_ERROR");return await r.json()}catch(e){throw console.error("Error fetching Clerk user:",e),new N("Authentication service unavailable",503,"AUTH_SERVICE_ERROR")}}async function f(e,r,t,s){try{let o=await fetch(e,{...r,headers:{"Content-Type":"application/json","x-clerk-user-id":t,"x-user-email":s,...r.headers}}),a=await o.json();if(!o.ok)throw new N(a.error||`Backend request failed: ${o.status}`,o.status,a.code||"BACKEND_ERROR",a);return a}catch(e){if(e instanceof N)throw e;throw console.error("Backend request error:",e),new N("Backend service unavailable",503,"BACKEND_SERVICE_ERROR")}}async function x(e){try{let{userId:r}=await (0,c.j)();if(!r)return d.NextResponse.json({error:"Authentication required"},{status:401});let t=await fetch(`https://api.clerk.com/v1/users/${r}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}}).then(e=>e.json()),s=new URL(e.url),o=`${T}/api/payments${s.search}`,a=await fetch(o,{method:"GET",headers:{"Content-Type":"application/json","x-clerk-user-id":r,"x-user-email":t.email_addresses[0]?.email_address||""}}),n=await a.json();if(!a.ok)return d.NextResponse.json(n,{status:a.status});return d.NextResponse.json(n)}catch(e){return console.error("Error proxying payments request:",e),d.NextResponse.json({error:"Internal server error"},{status:500})}}async function O(e){try{let r,{userId:t}=await (0,c.j)();if(!t)return I._O.logFinancialOperation("anonymous","anonymous","CREATE_PAYMENT_UNAUTHORIZED","payments",{endpoint:"/api/payments"},!1,"Authentication required",e),d.NextResponse.json({error:"Authentication required",code:"AUTH_REQUIRED",message:"请先登录以处理支付"},{status:401});let s=I.xC.checkRateLimit(t,!0);if(!s.allowed)return I._O.logFinancialOperation(t,"unknown","CREATE_PAYMENT_RATE_LIMITED","payments",{endpoint:"/api/payments",resetTime:s.resetTime},!1,"Payment rate limit exceeded",e),d.NextResponse.json({error:"Payment rate limit exceeded",code:"PAYMENT_RATE_LIMIT_EXCEEDED",message:"支付请求过于频繁，请稍后重试",resetTime:s.resetTime},{status:429});let o=await v(t),a=o.email_addresses[0]?.email_address||"";if(!a)return d.NextResponse.json({error:"User email not found",code:"USER_EMAIL_MISSING",message:"用户邮箱信息缺失，请联系管理员"},{status:400});try{r=await e.json()}catch(r){return I._O.logFinancialOperation(t,a,"CREATE_PAYMENT_JSON_PARSE_FAILED","payments",{error:r},!1,"Failed to parse JSON request body",e),d.NextResponse.json({error:"Invalid JSON in request body",code:"INVALID_JSON",message:"请求数据格式错误"},{status:400})}try{r.amount=I.SR.sanitizeAmount(r.amount),r.paymentMethod=I.SR.sanitizePaymentMethod(r.paymentMethod),r.transactionId&&(r.transactionId=I.SR.sanitizeText(r.transactionId,100)),r.notes&&(r.notes=I.SR.sanitizeText(r.notes,500))}catch(r){return I._O.logFinancialOperation(t,a,"CREATE_PAYMENT_SANITIZATION_FAILED","payments",{error:r},!1,r instanceof Error?r.message:"Payment sanitization failed",e),d.NextResponse.json({error:"Input sanitization failed",code:"SANITIZATION_ERROR",message:"支付数据格式不正确"},{status:400})}let n=u.aU.safeParse(r);if(!n.success){let r=(0,u.Yw)(n.error);return I._O.logFinancialOperation(t,a,"CREATE_PAYMENT_VALIDATION_FAILED","payments",{validationErrors:r},!1,"Payment validation failed",e),d.NextResponse.json({error:"Validation failed",code:"VALIDATION_ERROR",message:"支付数据验证失败",details:r},{status:400})}let i=n.data;if(r.billId)try{let e=`${T}/api/bills/${r.billId}`,s=await f(e,{method:"GET"},t,a),o=h.isValidPaymentAmount(i.amount,s);if(!o.valid)return d.NextResponse.json({error:"Invalid payment amount",code:"INVALID_PAYMENT_AMOUNT",message:o.reason},{status:400})}catch(e){if(e instanceof N&&404===e.status)return d.NextResponse.json({error:"Bill not found",code:"BILL_NOT_FOUND",message:"指定的账单不存在"},{status:404});throw e}let l=function(e){var r;let{paymentMethod:t,transactionId:s,amount:o}=e;return["card","wechat","alipay","transfer"].includes(t)&&(!s||0===s.trim().length)?{valid:!1,reason:`${({cash:"现金",card:"银行卡",wechat:"微信支付",alipay:"支付宝",transfer:"银行转账",installment:"分期付款"})[r=t]||r}需要提供交易ID`}:"cash"===t&&o>5e4?{valid:!1,reason:"现金支付单笔金额不能超过50,000元"}:"wechat"===t&&o>2e5?{valid:!1,reason:"微信支付单笔金额不能超过200,000元"}:"alipay"===t&&o>2e5?{valid:!1,reason:"支付宝单笔金额不能超过200,000元"}:{valid:!0}}(i);if(!l.valid)return d.NextResponse.json({error:"Payment method validation failed",code:"PAYMENT_METHOD_ERROR",message:l.reason},{status:400});let p=`${T}/api/payments`,m=await f(p,{method:"POST",body:JSON.stringify(i)},t,a);return I._O.logFinancialOperation(t,a,"CREATE_PAYMENT","payments",{paymentId:m.id,billId:r.billId,amount:i.amount,paymentMethod:i.paymentMethod,transactionId:i.transactionId,patientId:r.patientId},!0,void 0,e),d.NextResponse.json(m,{status:201})}catch(e){if(e instanceof N)return d.NextResponse.json({error:e.message,code:e.code,message:e.message,details:e.details},{status:e.status});return console.error("Unexpected error in POST /api/payments:",e),d.NextResponse.json({error:"Internal server error",code:"INTERNAL_ERROR",message:"处理支付时发生服务器错误，请稍后重试"},{status:500})}}let w={...i},S="workUnitAsyncStorage"in w?w.workUnitAsyncStorage:"requestAsyncStorage"in w?w.requestAsyncStorage:void 0;function C(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=S?.getStore();o=e?.headers}catch(e){}return A.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/payments",headers:o}).apply(t,s)}})}let D=C(x,"GET"),P=C(O,"POST"),q=C(void 0,"PUT"),M=C(void 0,"PATCH"),U=C(void 0,"DELETE"),L=C(void 0,"HEAD"),j=C(void 0,"OPTIONS"),k=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\payments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:F,serverHooks:V}=k;function Y(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:F})}},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615,6451,1227,6166],()=>t(36075));module.exports=s})();
//# sourceMappingURL=route.js.map