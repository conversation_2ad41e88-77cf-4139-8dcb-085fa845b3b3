'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  IconReceipt, 
  IconCalendar, 
  IconUser,
  IconStethoscope,
  IconCurrencyYuan,
  IconCheck,
  IconAlertTriangle,
  IconRefresh
} from '@tabler/icons-react';
import { Appointment, Bill } from '@/types/clinic';
import { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { useRole, PermissionGate } from '@/lib/role-context';
import { billingNotifications } from '@/lib/billing-notifications';
import { toast } from 'sonner';

interface AppointmentToBillProps {
  onBillGenerated?: (bill: Bill) => void;
  className?: string;
}

interface AppointmentWithBillStatus extends Appointment {
  hasBill?: boolean;
  billId?: string;
  billNumber?: string;
}

export function AppointmentToBill({ onBillGenerated, className }: AppointmentToBillProps) {
  const { hasPermission } = useRole();
  const [appointments, setAppointments] = useState<AppointmentWithBillStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState<string | null>(null);
  const [selectedBillType, setSelectedBillType] = useState<string>('treatment');

  const fetchCompletedAppointments = async () => {
    try {
      setLoading(true);
      
      // Fetch completed appointments
      const appointmentsResponse = await fetch('/api/appointments?status=completed&limit=50');
      const appointmentsData = await appointmentsResponse.json();
      
      if (!appointmentsResponse.ok) {
        throw new Error(appointmentsData.error || 'Failed to fetch appointments');
      }

      // Fetch existing bills to check which appointments already have bills
      const billsResponse = await billsAPI.fetchBills({ limit: 100 });
      const existingBills = billsResponse.docs;

      // Map appointments with bill status
      const appointmentsWithBillStatus: AppointmentWithBillStatus[] = appointmentsData.docs.map((appointment: Appointment) => {
        const existingBill = existingBills.find(bill => 
          typeof bill.appointment === 'object' && bill.appointment?.id === appointment.id
        );
        
        return {
          ...appointment,
          hasBill: !!existingBill,
          billId: existingBill?.id,
          billNumber: existingBill?.billNumber,
        };
      });

      setAppointments(appointmentsWithBillStatus);
    } catch (error) {
      console.error('Failed to fetch appointments:', error);
      billingNotifications.system.dataRefreshError();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCompletedAppointments();
  }, []);

  // Check permissions after all hooks
  if (!hasPermission('canCreateBills')) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconAlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">权限不足</h3>
          <p className="text-muted-foreground">
            您没有权限从预约生成账单
          </p>
        </div>
      </div>
    );
  }

  const handleGenerateBill = async (appointment: AppointmentWithBillStatus) => {
    try {
      setGenerating(appointment.id);

      const response = await billsAPI.generateFromAppointment(appointment.id, selectedBillType);
      
      billingNotifications.bill.generateFromAppointment(
        response,
        new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')
      );

      // Update the appointment status locally
      setAppointments(prev => 
        prev.map(apt => 
          apt.id === appointment.id 
            ? { ...apt, hasBill: true, billId: response.id, billNumber: response.billNumber }
            : apt
        )
      );

      if (onBillGenerated) {
        onBillGenerated(response);
      }

    } catch (error) {
      console.error('Failed to generate bill:', error);
      const errorMessage = error instanceof BillingAPIError 
        ? error.message 
        : undefined;
      billingNotifications.bill.createError(errorMessage);
    } finally {
      setGenerating(null);
    }
  };

  const getAppointmentStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'scheduled':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getAppointmentStatusLabel = (status: string) => {
    const labels = {
      scheduled: '已预约',
      confirmed: '已确认',
      completed: '已完成',
      cancelled: '已取消',
      'no-show': '未到诊',
    };
    return labels[status as keyof typeof labels] || status;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载已完成预约中...</p>
        </div>
      </div>
    );
  }

  const unbilledAppointments = appointments.filter(apt => !apt.hasBill);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <IconReceipt className="size-6" />
            预约生成账单
          </h2>
          <p className="text-muted-foreground">
            从已完成的预约自动生成账单
          </p>
        </div>
        <Button variant="outline" onClick={fetchCompletedAppointments} disabled={loading}>
          <IconRefresh className="h-4 w-4 mr-2" />
          刷新
        </Button>
      </div>

      {/* Bill Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">账单类型设置</CardTitle>
          <CardDescription>
            选择生成账单的默认类型
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedBillType} onValueChange={setSelectedBillType}>
            <SelectTrigger className="w-64">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="treatment">治疗账单</SelectItem>
              <SelectItem value="consultation">咨询账单</SelectItem>
              <SelectItem value="additional">补充账单</SelectItem>
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {appointments.length}
              </div>
              <div className="text-sm text-muted-foreground">已完成预约</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {appointments.filter(apt => apt.hasBill).length}
              </div>
              <div className="text-sm text-muted-foreground">已生成账单</div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {unbilledAppointments.length}
              </div>
              <div className="text-sm text-muted-foreground">待生成账单</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Appointments List */}
      {unbilledAppointments.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <IconCheck className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">全部预约已生成账单</h3>
              <p className="text-muted-foreground">
                所有已完成的预约都已生成对应的账单
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">待生成账单的预约</h3>
          
          <div className="grid gap-4">
            {unbilledAppointments.map((appointment) => (
              <Card key={appointment.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-3 flex-1">
                      {/* Appointment Header */}
                      <div className="flex items-center gap-3">
                        <Badge className={getAppointmentStatusColor(appointment.status)}>
                          {getAppointmentStatusLabel(appointment.status)}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          预约ID: {appointment.id}
                        </span>
                      </div>

                      {/* Appointment Details */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <IconCalendar className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <IconUser className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {typeof appointment.patient === 'object' 
                              ? appointment.patient.fullName 
                              : '未知患者'}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <IconStethoscope className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {typeof appointment.treatment === 'object' 
                              ? appointment.treatment.name 
                              : '未知治疗'}
                          </span>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <IconCurrencyYuan className="h-4 w-4 text-muted-foreground" />
                          <span>
                            {billingUtils.formatCurrency(appointment.price || 0)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Generate Bill Button */}
                    <div className="ml-4">
                      <Button
                        onClick={() => handleGenerateBill(appointment)}
                        disabled={generating === appointment.id}
                        className="min-w-24"
                      >
                        {generating === appointment.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            生成中...
                          </>
                        ) : (
                          <>
                            <IconReceipt className="h-4 w-4 mr-2" />
                            生成账单
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Info Alert */}
      <Alert>
        <IconAlertTriangle className="h-4 w-4" />
        <AlertDescription>
          生成的账单将包含预约的治疗项目、价格和患者信息。您可以在账单列表中进一步编辑账单详情。
        </AlertDescription>
      </Alert>
    </div>
  );
}
