import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';
import { PatientInteraction } from '@/types/clinic';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const data = await payloadClient.getPatientInteraction(params.id) as PatientInteraction;

    // Check if user has permission to view this interaction
    if (user.role === 'doctor') {
      const allowedTypes = ['consultation-note', 'treatment-discussion', 'in-person-visit'];
      const staffMemberId = typeof data.staffMember === 'object' ? data.staffMember.id : data.staffMember;
      if (staffMemberId !== user.payloadUserId && !allowedTypes.includes(data.interactionType)) {
        return createErrorResponse('Permission denied', 403);
      }
    } else if (user.role === 'front-desk') {
      const allowedTypes = ['phone-call', 'email', 'billing-inquiry'];
      if (!allowedTypes.includes(data.interactionType)) {
        return createErrorResponse('Permission denied', 403);
      }
    }
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient interaction:', error);
    return createErrorResponse('Failed to fetch patient interaction');
  }
});

export const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const updateData = await request.json();
    
    // Get the existing interaction to check permissions
    const existingInteraction = await payloadClient.getPatientInteraction(params.id) as PatientInteraction;

    // Check if user can update this interaction
    const staffMemberId = typeof existingInteraction.staffMember === 'object' ? existingInteraction.staffMember.id : existingInteraction.staffMember;
    if (user.role !== 'admin' && staffMemberId !== user.payloadUserId) {
      return createErrorResponse('You can only update your own interactions', 403);
    }
    
    // Prevent changing the staff member
    delete updateData.staffMember;
    
    const data = await payloadClient.updatePatientInteraction(params.id, updateData);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error updating patient interaction:', error);
    return createErrorResponse('Failed to update patient interaction');
  }
});

export const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    // Only admin can delete interactions
    if (user.role !== 'admin') {
      return createErrorResponse('Only administrators can delete interactions', 403);
    }
    
    const payloadClient = createPayloadClient(user);
    await payloadClient.deletePatientInteraction(params.id);
    
    return createSuccessResponse({ message: 'Patient interaction deleted successfully' });
  } catch (error) {
    console.error('Error deleting patient interaction:', error);
    return createErrorResponse('Failed to delete patient interaction');
  }
});
