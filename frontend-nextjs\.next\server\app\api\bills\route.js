try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4c5916b2-4b56-4dd6-a2dc-29f81aeb8128",e._sentryDebugIdIdentifier="sentry-dbid-4c5916b2-4b56-4dd6-a2dc-29f81aeb8128")}catch(e){}(()=>{var e={};e.id=2392,e.ids=[2392],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7760:()=>{},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{"use strict";e.exports=require("node:os")},49616:()=>{},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59876:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>S,serverHooks:()=>L,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{DELETE:()=>y,GET:()=>A,HEAD:()=>N,OPTIONS:()=>O,PATCH:()=>q,POST:()=>T,PUT:()=>b});var i=r(86047),o=r(85544),n=r(36135),a=r(63033),u=r(35886),c=r(79615),d=r(87920),l=r(13446),p=r(19761);let m="http://localhost:8002";class x extends Error{constructor(e,t=500,r,s){super(e),this.status=t,this.code=r,this.details=s,this.name="APIError"}}async function E(e){try{let t=await fetch(`https://api.clerk.com/v1/users/${e}`,{headers:{Authorization:`Bearer ${process.env.CLERK_SECRET_KEY}`}});if(!t.ok)throw new x("Failed to fetch user information",401,"CLERK_USER_FETCH_ERROR");return await t.json()}catch(e){throw console.error("Error fetching Clerk user:",e),new x("Authentication service unavailable",503,"AUTH_SERVICE_ERROR")}}async function R(e,t,r,s){try{let i=await fetch(e,{...t,headers:{"Content-Type":"application/json","x-clerk-user-id":r,"x-user-email":s,...t.headers}}),o=await i.json();if(!i.ok)throw new x(o.error||`Backend request failed: ${i.status}`,i.status,o.code||"BACKEND_ERROR",o);return o}catch(e){if(e instanceof x)throw e;throw console.error("Backend request error:",e),new x("Backend service unavailable",503,"BACKEND_SERVICE_ERROR")}}async function h(e){try{let{userId:t}=await (0,c.j)();if(!t)return l._O.logFinancialOperation("anonymous","anonymous","GET_BILLS_UNAUTHORIZED","bills",{endpoint:"/api/bills"},!1,"Authentication required",e),u.NextResponse.json({error:"Authentication required",code:"AUTH_REQUIRED",message:"请先登录以访问账单数据"},{status:401});let r=l.xC.checkRateLimit(t);if(!r.allowed)return l._O.logFinancialOperation(t,"unknown","GET_BILLS_RATE_LIMITED","bills",{endpoint:"/api/bills",resetTime:r.resetTime},!1,"Rate limit exceeded",e),u.NextResponse.json({error:"Rate limit exceeded",code:"RATE_LIMIT_EXCEEDED",message:"请求过于频繁，请稍后重试",resetTime:r.resetTime},{status:429});let s=new URL(e.url),i=Object.fromEntries(s.searchParams.entries());if(i.limit&&(isNaN(Number(i.limit))||Number(i.limit)>100))return u.NextResponse.json({error:"Invalid limit parameter",code:"INVALID_LIMIT",message:"分页限制必须是1-100之间的数字"},{status:400});if(i.page&&(isNaN(Number(i.page))||1>Number(i.page)))return u.NextResponse.json({error:"Invalid page parameter",code:"INVALID_PAGE",message:"页码必须是大于0的数字"},{status:400});let o=await E(t),n=o.email_addresses[0]?.email_address||"";if(!n)return u.NextResponse.json({error:"User email not found",code:"USER_EMAIL_MISSING",message:"用户邮箱信息缺失，请联系管理员"},{status:400});let a=`${m}/api/bills${s.search}`,d=await R(a,{method:"GET"},t,n);return l._O.logFinancialOperation(t,n,"GET_BILLS","bills",{endpoint:"/api/bills",queryParams:Object.fromEntries(s.searchParams.entries()),resultCount:d.docs?.length||0},!0,void 0,e),u.NextResponse.json(d)}catch(e){if(e instanceof x)return u.NextResponse.json({error:e.message,code:e.code,message:e.message},{status:e.status});return console.error("Unexpected error in GET /api/bills:",e),u.NextResponse.json({error:"Internal server error",code:"INTERNAL_ERROR",message:"服务器内部错误，请稍后重试"},{status:500})}}async function _(e){try{let t,{userId:r}=await (0,c.j)();if(!r)return u.NextResponse.json({error:"Authentication required",code:"AUTH_REQUIRED",message:"请先登录以创建账单"},{status:401});let s=await E(r),i=s.email_addresses[0]?.email_address||"";if(!i)return u.NextResponse.json({error:"User email not found",code:"USER_EMAIL_MISSING",message:"用户邮箱信息缺失，请联系管理员"},{status:400});try{t=await e.json()}catch(t){return l._O.logFinancialOperation(r,i,"CREATE_BILL_JSON_PARSE_FAILED","bills",{error:t},!1,"Failed to parse JSON request body",e),u.NextResponse.json({error:"Invalid JSON in request body",code:"INVALID_JSON",message:"请求数据格式错误"},{status:400})}try{t.description&&(t.description=l.SR.sanitizeText(t.description,500)),t.notes&&(t.notes=l.SR.sanitizeText(t.notes,1e3)),t.items&&(t.items=t.items.map(e=>({...e,itemName:l.SR.sanitizeText(e.itemName,200),quantity:l.SR.sanitizeAmount(e.quantity),unitPrice:l.SR.sanitizeAmount(e.unitPrice)})))}catch(t){return l._O.logFinancialOperation(r,i,"CREATE_BILL_SANITIZATION_FAILED","bills",{error:t},!1,t instanceof Error?t.message:"Sanitization failed",e),u.NextResponse.json({error:"Input sanitization failed",code:"SANITIZATION_ERROR",message:"输入数据格式不正确"},{status:400})}let o=d.nt.safeParse(t);if(!o.success){let t=(0,d.Yw)(o.error);return l._O.logFinancialOperation(r,i,"CREATE_BILL_VALIDATION_FAILED","bills",{validationErrors:t},!1,"Validation failed",e),u.NextResponse.json({error:"Validation failed",code:"VALIDATION_ERROR",message:"账单数据验证失败",details:t},{status:400})}let n=o.data,a=n.items.reduce((e,t)=>{let r=t.quantity*t.unitPrice,s=r*((t.discountRate||0)/100);return e+(r-s)},0),p=Math.round(100*a)/100,x=n.discountAmount||0,h=n.taxAmount||0,_=Math.round((p+h-x)*100)/100,f=`${m}/api/bills`,I={...n,subtotal:p,totalAmount:_},g=await R(f,{method:"POST",body:JSON.stringify(I)},r,i);return l._O.logFinancialOperation(r,i,"CREATE_BILL","bills",{billId:g.id,patientId:n.patient,billType:n.billType,subtotal:p,totalAmount:_,itemCount:n.items?.length||0},!0,void 0,e),u.NextResponse.json(g,{status:201})}catch(e){if(e instanceof x)return u.NextResponse.json({error:e.message,code:e.code,message:e.message,details:e.details},{status:e.status});return console.error("Unexpected error in POST /api/bills:",e),u.NextResponse.json({error:"Internal server error",code:"INTERNAL_ERROR",message:"创建账单时发生服务器错误，请稍后重试"},{status:500})}}let f={...a},I="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function g(e,t){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,r,s)=>{let i;try{let e=I?.getStore();i=e?.headers}catch(e){}return p.wrapRouteHandlerWithSentry(e,{method:t,parameterizedRoute:"/api/bills",headers:i}).apply(r,s)}})}let A=g(h,"GET"),T=g(_,"POST"),b=g(void 0,"PUT"),q=g(void 0,"PATCH"),y=g(void 0,"DELETE"),N=g(void 0,"HEAD"),O=g(void 0,"OPTIONS"),S=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/bills/route",pathname:"/api/bills",filename:"route",bundlePath:"app/api/bills/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:v,serverHooks:L}=S;function j(){return(0,n.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:v})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[55,3738,1950,5886,9615,1227,6166],()=>r(59876));module.exports=s})();
//# sourceMappingURL=route.js.map