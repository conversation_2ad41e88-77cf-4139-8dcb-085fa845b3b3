try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},l=(new e.Error).stack;l&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[l]="cace072c-4892-427b-8d58-18a75a83b945",e._sentryDebugIdIdentifier="sentry-dbid-cace072c-4892-427b-8d58-18a75a83b945")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3329],{19160:(e,l,n)=>{n.d(l,{a:()=>a});var s=n(11487);let a=(0,s.createServerReference)("7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261",s.callServer,void 0,s.findSourceMapURL,"createOrReadKeylessAction")},93329:(e,l,n)=>{n.r(l),n.d(l,{KeylessCreatorOrReader:()=>i});var s=n(95181),a=n(99004),t=n(19160);let i=e=>{var l;let{children:n}=e,i=(null==(l=(0,s.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[r,d]=a.useActionState(t.a,null);return((0,a.useEffect)(()=>{i||a.startTransition(()=>{d()})},[i]),a.isValidElement(n))?a.cloneElement(n,{key:null==r?void 0:r.publishableKey,publishableKey:null==r?void 0:r.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==r?void 0:r.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==r?void 0:r.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):n}}}]);