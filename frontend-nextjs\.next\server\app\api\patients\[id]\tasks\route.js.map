{"version": 3, "file": "../app/api/patients/[id]/tasks/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,yWCIO,IAAMA,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAChI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCK,CADoCL,CAAAA,CAC9B,GAAIM,GAAIL,CAAAA,EAAQI,GAAG,EAAXJ,EAGNM,QAASF,CAAAA,EAAIG,CAAJH,WAAgB,CAACI,GAAG,CAAC,OAAY,SAClDC,EAAOH,QAASF,CAAAA,EAAIG,CAAJH,WAAgB,CAACI,GAAG,CAAC,MAAW,QAChDE,EAAWN,EAAIG,CAAJH,GAAAA,QAAgB,CAACI,GAAG,CAAC,YAChCG,EAASP,EAAIG,CAAJH,CAAAA,UAAgB,CAACI,GAAG,CAAC,UAC9BI,EAAWR,EAAIG,CAAJH,GAAAA,QAAgB,CAACI,GAAG,CAAC,YAChCK,EAAaT,EAAIG,CAAJH,KAAAA,MAAgB,CAACI,GAAG,CAAC,cAGpCM,EAAmB,CACrBC,OAAS,CADY,CACVC,MAAAA,CAAQf,EAAOgB,EAAAA,CAC5B,EAEIP,IACFI,EAAYJ,EADA,MACQ,CAAG,CAAEM,MAAQN,CAAAA,CAAS,GAGxCC,IACFG,EADU,MACQ,CAAG,CAAEE,CAAvBF,KAA+BH,CAAAA,CAAO,GAGpCC,GACFE,GAAYF,EADA,MACZE,CAAuB,CAAEE,MAAQJ,CAAAA,CAAS,GAGxCC,IACFC,EAAYD,IADE,KACdC,CAAsB,CAAG,CAAEE,MAAQH,CAAAA,CAAW,GAI9B,QAAU,GAAxBd,EAAKmB,EAALnB,EAAS,CACXe,EAAYK,GAAG,CAAG,CAChBL,EACA,CACEM,CAHJN,CAGQ,EACF,CACED,GAJNC,OAIkB,EACVE,MAAAA,CAAQjB,EAAKsB,aAAAA,CAEjB,EACA,CACEC,SAAW,EACTN,MAAAA,CAAQjB,EAAKsB,aAAAA,CAEjB,EACA,CACEX,QAAU,EACRa,EAAI,EAAC,qBAAsB,wBAAyB,yBAAyB,CAEjF,EACD,EAEJ,CACsB,YAAc,GAA5BxB,EAAKmB,EAALnB,EAAS,GAClBe,EAAYK,GAAG,CAAG,CAChBL,EACA,CACEM,CAHJN,CAGQ,EACF,CACED,GAJNC,OAIkB,EACVE,MAAAA,CAAQjB,EAAKsB,aAAAA,CAEjB,EACA,CACEX,QAAU,EACRa,EAAI,EAAC,iBAAkB,yBAA0B,oBAAoB,CAEzE,EACD,EAEJ,EAGH,IAAMC,EAAO,MAAMtB,EAAcuB,WAAAA,IAAe,CAAC,OAC/CC,KAAAA,EACAjB,EACAkB,EADAlB,GACOK,CAAAA,EACPc,IAAM,KADCd,MAET,GAEA,MAAOe,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBL,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBM,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,+BAAiCA,CAAAA,GACxCE,CAAAA,CADwCF,CAAAA,EACxCE,EAAAA,CAAoB,iCAC7B,CACF,CAAG,ECrFG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,0BAA0B,CAC9C,OAAO,EACf,CAAO,CAAC,CAAC,KAAK,CAAC,EAAS,EACxB,CAAK,CACF,CAAC,CAIC,IAAC,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAYC,OAA6B,EAAH,IAAS,EAEtD,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,sCACA,oCACA,iBACA,8CACA,CAAK,CACL,0HACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,sDCAA,6FCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patients/[id]/tasks/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\n\nexport const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const url = new URL(request.url);\n    \n    // Extract query parameters\n    const limit = parseInt(url.searchParams.get('limit') || '20');\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const taskType = url.searchParams.get('taskType');\n    const status = url.searchParams.get('status');\n    const priority = url.searchParams.get('priority');\n    const assignedTo = url.searchParams.get('assignedTo');\n    \n    // Build where clause for this specific patient\n    let whereClause: any = {\n      patient: { equals: params.id },\n    };\n    \n    if (taskType) {\n      whereClause.taskType = { equals: taskType };\n    }\n    \n    if (status) {\n      whereClause.status = { equals: status };\n    }\n    \n    if (priority) {\n      whereClause.priority = { equals: priority };\n    }\n    \n    if (assignedTo) {\n      whereClause.assignedTo = { equals: assignedTo };\n    }\n    \n    // Apply role-based filtering\n    if (user.role === 'doctor') {\n      whereClause.and = [\n        whereClause,\n        {\n          or: [\n            {\n              assignedTo: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              createdBy: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              taskType: {\n                in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],\n              },\n            },\n          ],\n        },\n      ];\n    } else if (user.role === 'front-desk') {\n      whereClause.and = [\n        whereClause,\n        {\n          or: [\n            {\n              assignedTo: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              taskType: {\n                in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],\n              },\n            },\n          ],\n        },\n      ];\n    }\n    \n    const data = await payloadClient.getPatientTasks({\n      limit,\n      page,\n      where: whereClause,\n      sort: '-dueDate', // Sort by due date descending\n    });\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error fetching patient tasks:', error);\n    return createErrorResponse('Failed to fetch patient tasks');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patients/[id]/tasks',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patients\\\\[id]\\\\tasks\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patients/[id]/tasks/route\",\n        pathname: \"/api/patients/[id]/tasks\",\n        filename: \"route\",\n        bundlePath: \"app/api/patients/[id]/tasks/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patients\\\\[id]\\\\tasks\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "params", "payloadClient", "createPayloadClient", "url", "URL", "parseInt", "searchParams", "get", "page", "taskType", "status", "priority", "assignedTo", "<PERSON><PERSON><PERSON><PERSON>", "patient", "equals", "id", "role", "and", "or", "payloadUserId", "created<PERSON>y", "in", "data", "getPatientTasks", "limit", "where", "sort", "createSuccessResponse", "error", "console", "createErrorResponse", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}