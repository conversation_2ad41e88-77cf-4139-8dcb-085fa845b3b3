try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="04364084-27a0-489b-8bf2-dc04c02a1d4a",e._sentryDebugIdIdentifier="sentry-dbid-04364084-27a0-489b-8bf2-dc04c02a1d4a")}catch(e){}(()=>{var e={};e.id=3912,e.ids=[3912],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10531:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(24443);r(60222);var a=r(16586),n=r(29693),i=r(72595);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16777:(e,t,r)=>{Promise.resolve().then(r.bind(r,67657))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26170:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=r(29703),a=r(85544),n=r(62458),i=r(77821),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["dashboard",{children:["patients",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81873)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/patients/page",pathname:"/dashboard/patients",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},26882:(e,t,r)=>{"use strict";r.d(t,{C5:()=>y,MJ:()=>h,Rr:()=>g,eI:()=>x,lR:()=>f,lV:()=>d,zB:()=>u});var s=r(24443),a=r(60222),n=r(16586),i=r(95550),o=r(72595),l=r(18984);let d=i.Op,c=a.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,s.jsx)(i.xI,{...e,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})}),m=()=>{let e=a.useContext(c),t=a.useContext(p),{getFieldState:r}=(0,i.xW)(),s=(0,i.lN)({name:e.name}),n=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...n}},p=a.createContext({});function x({className:e,...t}){let r=a.useId();return(0,s.jsx)(p.Provider,{value:{id:r},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...t})})}function f({className:e,...t}){let{error:r,formItemId:a}=m();return(0,s.jsx)(l.J,{"data-slot":"form-label","data-error":!!r,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function h({...e}){let{error:t,formItemId:r,formDescriptionId:a,formMessageId:i}=m();return(0,s.jsx)(n.DX,{"data-slot":"form-control",id:r,"aria-describedby":t?`${a} ${i}`:`${a}`,"aria-invalid":!!t,...e,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function g({className:e,...t}){let{formDescriptionId:r}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,o.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"FormDescription","data-sentry-source-file":"form.tsx"})}function y({className:e,...t}){let{error:r,formMessageId:a}=m(),n=r?String(r?.message??""):t.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,o.cn)("text-destructive text-sm",e),...t,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:n}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32300:(e,t,r)=>{"use strict";r.d(t,{t:()=>a});let s={nav:{dashboard:"仪表板",appointments:"预约管理",patients:"患者管理",treatments:"治疗项目",admin:"系统管理",account:"账户",profile:"个人资料",login:"登录",overview:"概览"},dashboard:{title:"诊所控制台 \uD83C\uDFE5",subtitle:"欢迎使用您的诊所管理系统",metrics:{todayAppointments:"今日预约",recentPatients:"近期患者",totalPatients:"患者总数",activetreatments:"可用治疗",scheduledForToday:"今日安排",appointmentsScheduledForToday:"今日安排的预约",newPatientsThisWeek:"本周新患者",patientsRegisteredInLast7Days:"过去7天注册的患者",totalRegisteredPatients:"注册患者总数",completePatientDatabase:"完整患者数据库",treatmentOptionsAvailable:"可用治疗选项",fullServiceCatalog:"完整服务目录",active:"活跃",last7Days:"过去7天",allTime:"全部时间",available:"可用"},errors:{loadingDashboard:"加载仪表板时出错",failedToLoadMetrics:"无法加载仪表板数据"}},appointments:{title:"预约管理",subtitle:"管理患者预约和排程",newAppointment:"新建预约",editAppointment:"编辑预约",appointmentDetails:"预约详情",appointmentsCount:"个预约",loadingAppointments:"加载预约中...",noAppointments:"暂无预约",filters:{all:"全部",today:"今天",thisWeek:"本周",thisMonth:"本月",status:"状态",dateRange:"日期范围"},status:{scheduled:"已安排",confirmed:"已确认",inProgress:"进行中",completed:"已完成",cancelled:"已取消",noShow:"未到场"},form:{patient:"患者",selectPatient:"选择患者",treatment:"治疗项目",selectTreatment:"选择治疗项目",date:"日期",time:"时间",notes:"备注",notesPlaceholder:"预约备注（可选）",status:"状态"}},patients:{title:"患者管理",subtitle:"管理患者信息和病历",newPatient:"新建患者",editPatient:"编辑患者",patientDetails:"患者详情",patientsCount:"位患者",loadingPatients:"加载患者中...",noPatients:"暂无患者",searchPlaceholder:"按姓名、电话或邮箱搜索患者",form:{fullName:"姓名",fullNamePlaceholder:"请输入患者姓名",phone:"电话",phonePlaceholder:"请输入电话号码",email:"邮箱",emailPlaceholder:"请输入邮箱地址（可选）",medicalNotes:"病历备注",medicalNotesPlaceholder:"请输入病历备注（可选）"}},treatments:{title:"治疗项目",subtitle:"管理诊所治疗服务",newTreatment:"新建治疗",editTreatment:"编辑治疗",treatmentDetails:"治疗详情",treatmentsCount:"个治疗项目",loadingTreatments:"加载治疗项目中...",noTreatments:"暂无治疗项目",form:{name:"治疗名称",namePlaceholder:"请输入治疗名称",description:"治疗描述",descriptionPlaceholder:"请输入治疗描述",duration:"治疗时长",durationPlaceholder:"请输入治疗时长（分钟）",price:"价格",pricePlaceholder:"请输入价格"}},admin:{title:"系统管理",subtitle:"管理用户权限和系统设置",userManagement:"用户管理",roleManagement:"角色管理",systemSettings:"系统设置",users:"用户",roles:{admin:"管理员",doctor:"医生",frontDesk:"前台"}},common:{actions:{save:"保存",cancel:"取消",edit:"编辑",delete:"删除",view:"查看",search:"搜索",filter:"筛选",reset:"重置",submit:"提交",close:"关闭",confirm:"确认",back:"返回",next:"下一步",previous:"上一步",add:"添加",remove:"移除",update:"更新",create:"创建"},status:{loading:"加载中...",success:"成功",error:"错误",warning:"警告",info:"信息",pending:"待处理",active:"活跃",inactive:"非活跃",enabled:"已启用",disabled:"已禁用"},time:{today:"今天",yesterday:"昨天",tomorrow:"明天",thisWeek:"本周",lastWeek:"上周",nextWeek:"下周",thisMonth:"本月",lastMonth:"上月",nextMonth:"下月",thisYear:"今年",lastYear:"去年",nextYear:"明年"},confirmDialog:{title:"确认操作",deleteTitle:"确认删除",deleteMessage:"您确定要删除这个项目吗？此操作无法撤销。",cancelTitle:"确认取消",cancelMessage:"您确定要取消吗？未保存的更改将丢失。",saveTitle:"确认保存",saveMessage:"您确定要保存这些更改吗？"}},validation:{required:"此字段为必填项",email:"请输入有效的邮箱地址",phone:"请输入有效的电话号码",minLength:"至少需要 {min} 个字符",maxLength:"最多允许 {max} 个字符",number:"请输入有效的数字",positive:"请输入正数",date:"请选择有效的日期",time:"请选择有效的时间"},errors:{general:"发生了未知错误，请稍后重试",network:"网络连接错误，请检查您的网络连接",unauthorized:"您没有权限执行此操作",notFound:"请求的资源未找到",serverError:"服务器错误，请稍后重试",validationError:"输入数据验证失败",loadFailed:"加载数据失败",saveFailed:"保存数据失败",deleteFailed:"删除数据失败",updateFailed:"更新数据失败",createFailed:"创建数据失败"},success:{saved:"保存成功",deleted:"删除成功",updated:"更新成功",created:"创建成功",sent:"发送成功",uploaded:"上传成功",downloaded:"下载成功"}};function a(e,t){let r=e.split("."),a=s;for(let t of r)if(!a||"object"!=typeof a||!(t in a))return console.warn(`Translation key not found: ${e}`),e;else a=a[t];return"string"!=typeof a?(console.warn(`Translation value is not a string: ${e}`),e):t?a.replace(/\{(\w+)\}/g,(e,r)=>t[r]?.toString()||e):a}},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41634:(e,t,r)=>{"use strict";r.d(t,{K:()=>n});var s=r(24443),a=r(75922);function n({open:e,onOpenChange:t,title:r,description:n,confirmText:i="确认",cancelText:o="取消",variant:l="default",onConfirm:d,loading:c=!1}){return(0,s.jsx)(a.Lt,{open:e,onOpenChange:t,"data-sentry-element":"AlertDialog","data-sentry-component":"ConfirmationDialog","data-sentry-source-file":"confirmation-dialog.tsx",children:(0,s.jsxs)(a.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,s.jsxs)(a.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,s.jsx)(a.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"confirmation-dialog.tsx",children:r}),(0,s.jsx)(a.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"confirmation-dialog.tsx",children:n})]}),(0,s.jsxs)(a.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,s.jsx)(a.Zr,{disabled:c,"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"confirmation-dialog.tsx",children:o}),(0,s.jsx)(a.Rx,{onClick:d,disabled:c,className:"destructive"===l?"bg-destructive text-destructive-foreground hover:bg-destructive/90":"","data-sentry-element":"AlertDialogAction","data-sentry-source-file":"confirmation-dialog.tsx",children:c?"加载中...":i})]})]})})}},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57602:(e,t,r)=>{"use strict";async function s(e,t){let r=`/api${e}`;try{let e=await fetch(r,{headers:{"Content-Type":"application/json",...t?.headers},...t}),s=await e.json();if(!e.ok){if(s.error)throw Error(s.error);throw Error(`API request failed: ${e.status} ${e.statusText}`)}return s}catch(e){throw console.error(`API request to ${r} failed:`,e),e}}r.d(t,{RG:()=>a,_M:()=>i,cU:()=>o,fJ:()=>n});let a={getAll:async e=>{let t=new URLSearchParams;e?.limit&&t.append("limit",e.limit.toString()),e?.page&&t.append("page",e.page.toString()),e?.where&&e.where.patient?.equals&&t.append("where[patient][equals]",e.where.patient.equals);let r=t.toString()?`?${t.toString()}`:"";return s(`/appointments${r}`)},getById:async e=>s(`/appointments/${e}`),create:async e=>s("/appointments",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>s(`/appointments/${e}`,{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>s(`/appointments/${e}`,{method:"DELETE"})},n={getAll:async e=>{let t=new URLSearchParams;e?.limit&&t.append("limit",e.limit.toString()),e?.page&&t.append("page",e.page.toString()),e?.search&&t.append("where[or][0][fullName][contains]",e.search);let r=t.toString()?`?${t.toString()}`:"";return s(`/patients${r}`)},getById:async e=>s(`/patients/${e}`),create:async e=>s("/patients",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>s(`/patients/${e}`,{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>s(`/patients/${e}`,{method:"DELETE"})},i={getAll:async e=>{let t=new URLSearchParams;e?.limit&&t.append("limit",e.limit.toString()),e?.page&&t.append("page",e.page.toString());let r=t.toString()?`?${t.toString()}`:"";return s(`/treatments${r}`)},getById:async e=>s(`/treatments/${e}`),create:async e=>s("/treatments",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>s(`/treatments/${e}`,{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>s(`/treatments/${e}`,{method:"DELETE"})},o=async()=>{try{let e=new Date().toISOString().split("T")[0],t=(await a.getAll({limit:1e3})).docs.filter(t=>t.appointmentDate.startsWith(e)).length,r=new Date;r.setDate(r.getDate()-7);let s=await n.getAll({limit:1e3}),o=s.docs.filter(e=>new Date(e.createdAt)>=r).length,l=await i.getAll({limit:1e3});return{todayAppointments:t,recentPatients:o,totalPatients:s.totalDocs,activetreatments:l.totalDocs}}catch(e){return console.error("Failed to fetch dashboard metrics:",e),{todayAppointments:0,recentPatients:0,totalPatients:0,activetreatments:0}}}},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67657:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>F});var s=r(24443),a=r(60222),n=r(79153),i=r(74482),o=r(34769),l=r(72007),d=r(33284),c=r(19342),u=r(10531),m=r(24658),p=r(20866),x=r(17838),f=r(49958),h=r(9752),g=r(49230),y=r(68343),v=r(57602),j=r(95550),b=r(80395),N=r(13875),w=r(61780),P=r(26882),C=r(81646),A=r(85001);function D(e,t){let r={patient:"患者",appointment:"预约",treatment:"治疗项目",user:"用户"}[t.toLowerCase()]||t;switch(e){case"create":return`${r}创建成功！`;case"update":return`${r}更新成功！`;case"delete":return`${r}删除成功！`;case"complete":return`${r}已标记为完成！`;case"cancel":return`${r}取消成功！`;case"sync":return`${r}同步成功！`;default:return`${r}${e}完成！`}}var S=r(32300);let k=N.Ik({fullName:N.Yj().min(2,"Full name must be at least 2 characters").max(100,"Full name cannot exceed 100 characters").regex(/^[a-zA-Z\s'-]+$/,"Full name can only contain letters, spaces, hyphens, and apostrophes"),phone:N.Yj().min(10,"Phone number must be at least 10 digits").max(15,"Phone number cannot exceed 15 digits").regex(/^[\+]?[1-9][\d]{0,15}$/,"Please enter a valid phone number (e.g., +********** or **********)"),email:N.Yj().email("Please enter a valid email address (e.g., <EMAIL>)").optional().or(N.eu("")),medicalNotes:N.Yj().max(2e3,"Medical notes cannot exceed 2000 characters").optional()});function q({open:e,onOpenChange:t,patient:r,onSuccess:n}){let[o,l]=(0,a.useState)(!1),u=!!r,m=(0,j.mN)({resolver:(0,b.u)(k),defaultValues:{fullName:r?.fullName||"",phone:r?.phone||"",email:r?.email||"",medicalNotes:r?.medicalNotes||""}}),p=async e=>{l(!0);let s=A.toast.loading(function(e,t){let r={patient:"患者",appointment:"预约",treatment:"治疗项目",user:"用户"}[t.toLowerCase()]||t;switch(e){case"create":return`正在创建${r}...`;case"update":return`正在更新${r}...`;case"delete":return`正在删除${r}...`;case"load":return`正在加载${r}...`;case"save":return`正在保存${r}...`;case"sync":return`正在同步${r}...`;default:return`正在处理${r}...`}}(u?"update":"create","patient"));try{let a={fullName:e.fullName,phone:e.phone,email:e.email||void 0,medicalNotes:e.medicalNotes||void 0};u?(await v.fJ.update(r.id,a),A.toast.success(D("update","patient"),{id:s})):(await v.fJ.create(a),A.toast.success(D("create","patient"),{id:s})),n?.(),t(!1),m.reset()}catch(t){console.error("Failed to save patient:",t);let e=function(e){if(!navigator.onLine)return"无网络连接。请检查您的网络连接后重试。";if("AbortError"===e.name||e.message?.includes("timeout"))return"请求超时。请重试。";if(e.error||e.message){let t=e.error||e.message;if(t.includes("duplicate key")||t.includes("unique constraint"))return t.includes("phone")?"此电话号码已被注册。请使用不同的电话号码。":t.includes("email")?"此邮箱地址已被注册。请使用不同的邮箱地址。":t.includes("name")?"此名称已被使用。请选择不同的名称。":"此信息已被使用。请检查您的输入后重试。";if(t.includes("validation")||t.includes("invalid"))return"请检查您的输入，确保所有必填字段都正确填写。";if(t.includes("permission")||t.includes("unauthorized")||t.includes("forbidden"))return"您没有权限执行此操作。请联系您的管理员。";if(t.includes("not found")||t.includes("does not exist"))return"找不到请求的项目。它可能已被删除或移动。";if(t.includes("appointment")){if(t.includes("conflict")||t.includes("overlap"))return"此预约时间与其他预约冲突。请选择不同的时间。";if(t.includes("past"))return"无法安排过去的预约。请选择未来的日期和时间。"}if(t.includes("patient")&&t.includes("appointments"))return"无法删除有预约记录的患者。请先取消或完成所有预约。";if(t.includes("treatment")&&t.includes("appointments"))return"无法删除有预约记录的治疗项目。请先取消或完成所有预约。";if(t.length<100&&!t.includes("Error:")&&!t.includes("failed"))return t}if(e.status)switch(e.status){case 400:return"请求无效。请检查您的输入后重试。";case 401:return"您没有权限执行此操作。请登录后重试。";case 403:return"您没有权限执行此操作。";case 404:return"找不到请求的项目。";case 409:return"此操作与现有数据冲突。请检查您的输入。";case 422:return"提供的数据无效。请检查您的输入后重试。";case 429:return"请求过于频繁。请稍等片刻后重试。";case 500:return"服务器错误。请稍后重试。";case 502:case 503:case 504:return"服务暂时不可用。请稍后重试。";default:return"发生了意外错误。请重试。"}return"发生了意外错误。请重试，如果问题持续存在，请联系技术支持。"}(t);A.toast.error(e,{id:s})}finally{l(!1)}};return(0,s.jsx)(w.lG,{open:e,onOpenChange:t,"data-sentry-element":"Dialog","data-sentry-component":"PatientFormDialog","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,s.jsxs)(w.Cf,{className:"sm:max-w-[500px]","data-sentry-element":"DialogContent","data-sentry-source-file":"patient-form-dialog.tsx",children:[(0,s.jsxs)(w.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"patient-form-dialog.tsx",children:[(0,s.jsx)(w.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"patient-form-dialog.tsx",children:u?(0,S.t)("patients.editPatient"):(0,S.t)("patients.newPatient")}),(0,s.jsx)(w.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"patient-form-dialog.tsx",children:u?"更新下方的患者信息。":"填写患者详细信息以创建新记录。"})]}),(0,s.jsx)(P.lV,{...m,"data-sentry-element":"Form","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,s.jsxs)("form",{onSubmit:m.handleSubmit(p),className:"space-y-4",children:[(0,s.jsx)(P.zB,{control:m.control,name:"fullName",render:({field:e})=>(0,s.jsxs)(P.eI,{children:[(0,s.jsxs)(P.lR,{children:[(0,S.t)("patients.form.fullName")," *"]}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.form.fullNamePlaceholder"),...e})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"请输入患者的完整法定姓名，与身份证件上的姓名一致"}),(0,s.jsx)(P.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"}),(0,s.jsx)(P.zB,{control:m.control,name:"phone",render:({field:e})=>(0,s.jsxs)(P.eI,{children:[(0,s.jsxs)(P.lR,{children:[(0,S.t)("patients.form.phone")," *"]}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.form.phonePlaceholder"),type:"tel",...e})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"用于预约提醒和更新的主要联系电话"}),(0,s.jsx)(P.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"}),(0,s.jsx)(P.zB,{control:m.control,name:"email",render:({field:e})=>(0,s.jsxs)(P.eI,{children:[(0,s.jsxs)(P.lR,{children:[(0,S.t)("patients.form.email")," (可选)"]}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.form.emailPlaceholder"),type:"email",...e})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"用于预约确认和电子收据"}),(0,s.jsx)(P.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"}),(0,s.jsx)(i.Bk,{permission:"canEditMedicalNotes","data-sentry-element":"PermissionGate","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,s.jsx)(P.zB,{control:m.control,name:"medicalNotes",render:({field:e})=>(0,s.jsxs)(P.eI,{children:[(0,s.jsx)(P.lR,{children:(0,S.t)("patients.form.medicalNotes")}),(0,s.jsx)(P.MJ,{children:(0,s.jsx)(C.T,{placeholder:(0,S.t)("patients.form.medicalNotesPlaceholder"),className:"min-h-[100px]",...e})}),(0,s.jsx)(P.C5,{}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"机密医疗信息 - 仅限医务人员查看"})]}),"data-sentry-element":"FormField","data-sentry-source-file":"patient-form-dialog.tsx"})}),(0,s.jsxs)(w.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"patient-form-dialog.tsx",children:[(0,s.jsx)(d.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"patient-form-dialog.tsx",children:(0,S.t)("common.actions.cancel")}),(0,s.jsx)(d.$,{type:"submit",disabled:o,"data-sentry-element":"Button","data-sentry-source-file":"patient-form-dialog.tsx",children:o?"保存中...":u?"更新患者":"创建患者"})]})]})})]})})}var $=r(41634);function F(){let{userId:e,isLoaded:t}=(0,n.d)(),{hasPermission:r}=(0,i.It)(),[j,b]=(0,a.useState)([]),[N,w]=(0,a.useState)(!0),[P,C]=(0,a.useState)(null),[D,k]=(0,a.useState)(""),[F,T]=(0,a.useState)([]),[I,E]=(0,a.useState)(!1),[_,M]=(0,a.useState)(),[z,R]=(0,a.useState)(!1),[L,O]=(0,a.useState)(),[B,J]=(0,a.useState)(!1);t&&!e&&(0,o.redirect)("/auth/sign-in");let U=async()=>{try{w(!0);let e=await v.fJ.getAll({limit:100});b(e.docs),T(e.docs),C(null)}catch(e){console.error("Failed to fetch patients:",e),C((0,S.t)("patients.loadingPatients"))}finally{w(!1)}},G=()=>{M(void 0),E(!0)},W=e=>{M(e),E(!0)},Y=async e=>{try{if(J(!0),(await v.RG.getAll({limit:1,where:{patient:{equals:e.id}}})).docs.length>0)return void A.toast.error("无法删除有预约记录的患者。请先取消或完成所有预约。");O(e),R(!0)}catch(e){console.error("Failed to check patient appointments:",e),A.toast.error("验证患者预约失败")}finally{J(!1)}},V=async()=>{if(L){J(!0);try{await v.fJ.delete(L.id),A.toast.success("患者删除成功"),R(!1),O(void 0),U()}catch(e){console.error("Failed to delete patient:",e),A.toast.error("删除患者失败")}finally{J(!1)}}};return!t||N?(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,S.t)("patients.loadingPatients")})]})})}):(0,s.jsxs)(l.A,{"data-sentry-element":"PageContainer","data-sentry-component":"PatientsPage","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"size-6","data-sentry-element":"IconUsers","data-sentry-source-file":"page.tsx"}),(0,S.t)("patients.title")]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,S.t)("patients.subtitle")})]}),(0,s.jsx)(i.Bk,{permission:"canCreatePatients","data-sentry-element":"PermissionGate","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)(d.$,{className:"flex items-center gap-2",onClick:G,"data-sentry-element":"Button","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(p.A,{className:"size-4","data-sentry-element":"IconPlus","data-sentry-source-file":"page.tsx"}),(0,S.t)("patients.newPatient")]})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1 max-w-sm",children:[(0,s.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"page.tsx"}),(0,s.jsx)(c.p,{placeholder:(0,S.t)("patients.searchPlaceholder"),value:D,onChange:e=>k(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"page.tsx"})]}),(0,s.jsxs)(u.E,{variant:"outline","data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:[F.length," / ",j.length," ",(0,S.t)("patients.patientsCount")]})]}),P&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,s.jsx)("p",{className:"text-red-800",children:P})}),!P&&(0,s.jsx)("div",{className:"rounded-lg border",children:(0,s.jsx)("div",{className:"p-4",children:0===F.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(m.A,{className:"size-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:D?"未找到患者":"暂无患者注册"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:D?"请尝试调整搜索条件。":"开始注册您的第一位患者。"}),!D&&(0,s.jsx)(i.Bk,{permission:"canCreatePatients",children:(0,s.jsxs)(d.$,{onClick:G,children:[(0,s.jsx)(p.A,{className:"size-4 mr-2"}),(0,S.t)("patients.newPatient")]})})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:D&&`"${D}" 的搜索结果`}),(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:F.map(e=>(0,s.jsx)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:e.fullName}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["患者编号: ","string"==typeof e.id?e.id.slice(-8):e.id]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>W(e),className:"h-8 w-8 p-0",children:(0,s.jsx)(f.A,{className:"h-4 w-4"})}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>Y(e),className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",disabled:B,children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(g.A,{className:"size-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium",children:e.phone})]}),e.email&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,s.jsx)(y.A,{className:"size-4 text-muted-foreground"}),(0,s.jsx)("span",{children:e.email})]})]}),(0,s.jsx)(i.Bk,{permission:"canViewMedicalNotes",children:e.medicalNotes&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"病历备注:"}),(0,s.jsx)("p",{className:"text-sm bg-muted p-2 rounded text-muted-foreground line-clamp-2",children:e.medicalNotes})]})}),(0,s.jsx)("div",{className:"pt-2 border-t",children:(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["注册时间: ",new Date(e.createdAt).toLocaleDateString()]})})]})},e.id))})]})})})]}),(0,s.jsx)(q,{open:I,onOpenChange:E,patient:_,onSuccess:()=>{E(!1),M(void 0),U()},"data-sentry-element":"PatientFormDialog","data-sentry-source-file":"page.tsx"}),(0,s.jsx)($.K,{open:z,onOpenChange:R,title:"删除患者",description:`您确定要删除患者 ${L?.fullName} 吗？此操作无法撤销。`,confirmText:"删除",variant:"destructive",onConfirm:V,loading:B,"data-sentry-element":"ConfirmationDialog","data-sentry-source-file":"page.tsx"})]})}},72007:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(24443);r(60222);var a=r(67529);function n({children:e,scrollable:t=!0}){return(0,s.jsx)(s.Fragment,{children:t?(0,s.jsx)(a.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74921:(e,t,r)=>{Promise.resolve().then(r.bind(r,81873))},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79153:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>d,d:()=>c});var s=r(22371),a=r(95505),n=r(26393),i=r(60222),o=r.n(i);let l=o().createContext(null);function d({authPromise:e,children:t}){return o().createElement(l.Provider,{value:e},t)}function c(){let e=(0,n.useRouter)(),t=o().useContext(l),r=t;return(t&&"then"in t&&(r=o().use(t)),e)?(0,s.As)():(0,a.hP)(r)}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81646:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var s=r(24443);r(60222);var a=r(72595);function n({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},81873:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var a=r(63033),n=r(91611),i=r(19761),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\page.tsx","default");let l={...a},d="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;s="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let s,a,n;try{let e=d?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/patients",componentType:"Page",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}}):o;let c=void 0,u=void 0,m=void 0,p=s},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,3875,395,8774,7494,6273],()=>r(26170));module.exports=s})();
//# sourceMappingURL=page.js.map