import {
  IconAlertTriangle,
  IconArrowRight,
  IconArrowLeft,
  IconCalendar,
  IconCheck,
  IconChevronLeft,
  IconChevronRight,
  IconChevronDown,
  IconCommand,
  IconCreditCard,
  IconFile,
  IconFileText,
  IconHelpCircle,
  IconPhoto,
  IconDeviceLaptop,
  IconLayoutDashboard,
  IconLoader2,
  IconLogin,
  IconProps,
  IconShoppingBag,
  IconMoon,
  IconDotsVertical,
  IconPizza,
  IconPlus,
  IconSettings,
  IconStethoscope,
  IconSun,
  IconTrash,
  IconBrandTwitter,
  IconUser,
  IconUserCircle,
  IconUserEdit,
  IconUsers,
  IconUserX,
  IconX,
  IconLayoutKanban,
  IconBrandGithub,
  // CRM specific icons
  IconPhone,
  IconMail,
  IconMessageCircle,
  IconClock,
  IconSearch,
  IconFilter,
  IconEye,
  IconEdit,
  IconActivity,
  IconTrendingUp,
  IconDownload,
  IconRefresh
} from '@tabler/icons-react';

export type Icon = React.ComponentType<IconProps>;

export const Icons = {
  dashboard: IconLayoutDashboard,
  logo: IconCommand,
  login: IconLogin,
  close: IconX,
  product: IconShoppingBag,
  spinner: IconLoader2,
  kanban: IconLayoutKanban,
  chevronLeft: IconChevronLeft,
  chevronRight: IconChevronRight,
  trash: IconTrash,
  employee: IconUserX,
  post: IconFileText,
  page: IconFile,
  userPen: IconUserEdit,
  user2: IconUserCircle,
  media: IconPhoto,
  settings: IconSettings,
  billing: IconCreditCard,
  ellipsis: IconDotsVertical,
  add: IconPlus,
  warning: IconAlertTriangle,
  user: IconUser,
  arrowRight: IconArrowRight,
  help: IconHelpCircle,
  pizza: IconPizza,
  sun: IconSun,
  moon: IconMoon,
  laptop: IconDeviceLaptop,
  github: IconBrandGithub,
  twitter: IconBrandTwitter,
  check: IconCheck,
  // Medical clinic specific icons
  calendar: IconCalendar,
  users: IconUsers,
  medical: IconStethoscope
};

// Export individual icons for CRM components
export {
  IconAlertTriangle,
  IconArrowRight,
  IconArrowLeft,
  IconCalendar,
  IconCheck,
  IconChevronDown,
  IconCreditCard,
  IconFileText,
  IconPlus,
  IconStethoscope,
  IconUser,
  IconUsers,
  IconX,
  // CRM specific exports
  IconPhone,
  IconMail,
  IconMessageCircle,
  IconClock,
  IconSearch,
  IconFilter,
  IconEye,
  IconEdit,
  IconActivity,
  IconTrendingUp,
  IconDownload,
  IconRefresh
};
