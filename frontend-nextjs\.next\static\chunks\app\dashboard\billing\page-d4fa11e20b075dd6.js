try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0230b817-b712-4c91-8995-e9ad171e72e5",e._sentryDebugIdIdentifier="sentry-dbid-0230b817-b712-4c91-8995-e9ad171e72e5")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3613],{25192:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});var a=s(52880);s(99004);var n=s(54651);function r(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},40548:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>i,TN:()=>o,XL:()=>d});var a=s(52880);s(99004);var n=s(85017),r=s(54651);let l=(0,n.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(l({variant:s}),t),...n,"data-sentry-component":"Alert","data-sentry-source-file":"alert.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,r.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...s,"data-sentry-component":"AlertTitle","data-sentry-source-file":"alert.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...s,"data-sentry-component":"AlertDescription","data-sentry-source-file":"alert.tsx"})}},42278:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>o,j7:()=>i,tU:()=>l});var a=s(52880);s(99004);var n=s(21747),r=s(54651);function l(e){let{className:t,...s}=e;return(0,a.jsx)(n.bL,{"data-slot":"tabs",className:(0,r.cn)("flex flex-col gap-2",t),...s,"data-sentry-element":"TabsPrimitive.Root","data-sentry-component":"Tabs","data-sentry-source-file":"tabs.tsx"})}function i(e){let{className:t,...s}=e;return(0,a.jsx)(n.B8,{"data-slot":"tabs-list",className:(0,r.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...s,"data-sentry-element":"TabsPrimitive.List","data-sentry-component":"TabsList","data-sentry-source-file":"tabs.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(n.l9,{"data-slot":"tabs-trigger",className:(0,r.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s,"data-sentry-element":"TabsPrimitive.Trigger","data-sentry-component":"TabsTrigger","data-sentry-source-file":"tabs.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)(n.UC,{"data-slot":"tabs-content",className:(0,r.cn)("flex-1 outline-none",t),...s,"data-sentry-element":"TabsPrimitive.Content","data-sentry-component":"TabsContent","data-sentry-source-file":"tabs.tsx"})}},56420:(e,t,s)=>{"use strict";s.d(t,{C5:()=>g,MJ:()=>f,Rr:()=>y,eI:()=>h,lR:()=>p,lV:()=>o,zB:()=>m});var a=s(52880),n=s(99004),r=s(50516),l=s(38406),i=s(54651),d=s(84692);let o=l.Op,c=n.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(c.Provider,{value:{name:t.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,a.jsx)(l.xI,{...t,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})})},u=()=>{let e=n.useContext(c),t=n.useContext(x),{getFieldState:s}=(0,l.xW)(),a=(0,l.lN)({name:e.name}),r=s(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...r}},x=n.createContext({});function h(e){let{className:t,...s}=e,r=n.useId();return(0,a.jsx)(x.Provider,{value:{id:r},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...s})})}function p(e){let{className:t,...s}=e,{error:n,formItemId:r}=u();return(0,a.jsx)(d.J,{"data-slot":"form-label","data-error":!!n,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:r,...s,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function f(e){let{...t}=e,{error:s,formItemId:n,formDescriptionId:l,formMessageId:i}=u();return(0,a.jsx)(r.DX,{"data-slot":"form-control",id:n,"aria-describedby":s?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!s,...t,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function y(e){let{className:t,...s}=e,{formDescriptionId:n}=u();return(0,a.jsx)("p",{"data-slot":"form-description",id:n,className:(0,i.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"FormDescription","data-sentry-source-file":"form.tsx"})}function g(e){var t;let{className:s,...n}=e,{error:r,formMessageId:l}=u(),d=r?String(null!=(t=null==r?void 0:r.message)?t:""):n.children;return d?(0,a.jsx)("p",{"data-slot":"form-message",id:l,className:(0,i.cn)("text-destructive text-sm",s),...n,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:d}):null}},57437:(e,t,s)=>{"use strict";s.d(t,{BillingTabs:()=>eq});var a=s(52880),n=s(99004),r=s(42278),l=s(88151),i=s(62054),d=s(29980),o=s(92708),c=s(18183),m=s(49314),u=s(66444),x=s(71861),h=s(26368),p=s(40773),f=s(4629);let y={AUTH_REQUIRED:{message:"请先登录以继续操作",severity:"error"},AUTH_SERVICE_ERROR:{message:"认证服务暂时不可用，请稍后重试",severity:"error"},CLERK_USER_FETCH_ERROR:{message:"获取用户信息失败，请重新登录",severity:"error"},USER_EMAIL_MISSING:{message:"用户邮箱信息缺失，请联系管理员",severity:"error"},VALIDATION_ERROR:{message:"输入数据验证失败，请检查表单内容",severity:"error"},INVALID_JSON:{message:"数据格式错误，请刷新页面重试",severity:"error"},INVALID_LIMIT:{message:"分页参数错误",severity:"warning"},INVALID_PAGE:{message:"页码参数错误",severity:"warning"},SUBTOTAL_MISMATCH:{message:"账单金额计算错误，请重新检查",severity:"error"},INVALID_PAYMENT_AMOUNT:{message:"支付金额无效",severity:"error"},BILL_NOT_FOUND:{message:"账单不存在或已被删除",severity:"error"},PAYMENT_METHOD_ERROR:{message:"支付方式验证失败",severity:"error"},INSUFFICIENT_PERMISSIONS:{message:"权限不足，无法执行此操作",severity:"error"},BACKEND_ERROR:{message:"后端服务错误",severity:"error"},BACKEND_SERVICE_ERROR:{message:"后端服务暂时不可用，请稍后重试",severity:"error"},DATABASE_ERROR:{message:"数据库操作失败，请稍后重试",severity:"error"},NETWORK_ERROR:{message:"网络连接错误，请检查网络连接",severity:"error"},RATE_LIMIT_EXCEEDED:{message:"操作过于频繁，请稍后再试",severity:"error"},SUSPICIOUS_ACTIVITY:{message:"检测到可疑活动，操作已被阻止",severity:"error"},SESSION_EXPIRED:{message:"会话已过期，请重新登录",severity:"error"},INVALID_AUTHENTICATION:{message:"身份验证失败",severity:"error"},UNAUTHORIZED_ACCESS:{message:"未授权访问，请先登录",severity:"error"},PAYMENT_GATEWAY_ERROR:{message:"支付网关错误，请稍后重试",severity:"error"},CARD_DECLINED:{message:"银行卡被拒绝，请检查卡片状态",severity:"error"},CARD_EXPIRED:{message:"银行卡已过期，请使用其他卡片",severity:"error"},INVALID_CARD_NUMBER:{message:"银行卡号无效",severity:"error"},TRANSACTION_TIMEOUT:{message:"交易超时，请重新尝试",severity:"error"},DUPLICATE_TRANSACTION:{message:"重复交易，请勿重复提交",severity:"error"},INSUFFICIENT_FUNDS:{message:"余额不足，无法完成支付",severity:"error"},PAYMENT_ALREADY_PROCESSED:{message:"支付已处理，请勿重复操作",severity:"error"},DEPOSIT_EXPIRED:{message:"押金已过期",severity:"error"},DEPOSIT_INSUFFICIENT_BALANCE:{message:"押金余额不足",severity:"error"},REFUND_AMOUNT_EXCEEDS_PAYMENT:{message:"退款金额超过支付金额",severity:"error"},DEPOSIT_NOT_FOUND:{message:"未找到指定押金记录",severity:"error"},CONFIGURATION_ERROR:{message:"系统配置错误，请联系管理员",severity:"error"},EXTERNAL_SERVICE_ERROR:{message:"外部服务错误，请稍后重试",severity:"error"},INTERNAL_ERROR:{message:"系统内部错误，请稍后重试",severity:"error"},UNKNOWN_ERROR:{message:"发生未知错误，请联系技术支持",severity:"error"}};class g{static getInstance(){return g.instance||(g.instance=new g),g.instance}handleAPIError(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showToast:a=!0,logError:n=!0,context:r="API",fallbackMessage:l="操作失败，请稍后重试"}=s;if((null==e?void 0:e.code)&&y[e.code]){let s=y[e.code];t={code:e.code,message:e.message||s.message,userMessage:e.message||s.message,severity:s.severity,details:e.details,timestamp:new Date,context:r}}else t={code:"UNKNOWN_ERROR",message:(null==e?void 0:e.message)||"Unknown error occurred",userMessage:l,severity:"error",details:e,timestamp:new Date,context:r};return n&&this.logError(t),a&&this.showErrorToast(t),t}handleNetworkError(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s={code:"NETWORK_ERROR",message:(null==e?void 0:e.message)||"Network request failed",userMessage:"网络连接错误，请检查网络连接后重试",severity:"error",details:e,timestamp:new Date,context:t.context||"Network"};return!1!==t.logError&&this.logError(s),!1!==t.showToast&&this.showErrorToast(s),s}handleValidationError(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=e[0],a={code:"VALIDATION_ERROR",message:"Validation failed",userMessage:(null==s?void 0:s.message)||"表单验证失败，请检查输入内容",severity:"error",details:e,timestamp:new Date,context:t.context||"Validation"};return!1!==t.logError&&this.logError(a),!1!==t.showToast&&this.showErrorToast(a),a}showSuccess(e,t){f.toast.success(e,{description:t,duration:3e3})}showWarning(e,t){f.toast.warning(e,{description:t,duration:4e3})}showInfo(e,t){f.toast.info(e,{description:t,duration:3e3})}logError(e){console.error("[".concat(e.context,"] ").concat(e.code,": ").concat(e.message),{userMessage:e.userMessage,details:e.details,timestamp:e.timestamp}),this.errorLog.push(e),this.errorLog.length>100&&this.errorLog.shift()}showErrorToast(e){let t={duration:"error"===e.severity?5e3:4e3};switch(e.severity){case"error":f.toast.error(e.userMessage,t);break;case"warning":f.toast.warning(e.userMessage,t);break;case"info":f.toast.info(e.userMessage,t)}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}constructor(){this.errorLog=[]}}let j=g.getInstance(),b=(e,t)=>j.handleAPIError(e,t),v=(e,t)=>j.handleNetworkError(e,t),N=async function(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3,n=arguments.length>3?arguments[3]:void 0;for(let r=1;r<=s;r++)try{return await e()}catch(l){if(t=l,r===s)throw b(l,{context:n||"Retry",fallbackMessage:"操作失败，已重试".concat(s,"次")}),l;let e=a*Math.pow(2,r-1);await new Promise(t=>setTimeout(t,e))}throw t};class w extends Error{constructor(e,t,s,a){super(e),this.status=t,this.code=s,this.details=a,this.name="BillingAPIError"}}async function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,a="".concat("/api").concat(e),n={...t,headers:{"Content-Type":"application/json",...t.headers}},r=async()=>{try{let e=await fetch(a,n);if(!e.ok){let t;try{t=await e.json()}catch(s){t={error:"HTTP ".concat(e.status,": ").concat(e.statusText),code:"HTTP_".concat(e.status),message:e.statusText}}let a=new w(t.error||t.message||"HTTP ".concat(e.status,": ").concat(e.statusText),e.status,t.code||"HTTP_".concat(e.status),t.details);throw b(t,{context:(null==s?void 0:s.context)||"API Request",showToast:!1}),a}return await e.json()}catch(t){if(t instanceof w)throw t;let e=new w(t instanceof Error?t.message:"Network error occurred",0,"NETWORK_ERROR",t);throw v(t,{context:(null==s?void 0:s.context)||"API Request",showToast:!1}),e}};return(!t.method||["GET","HEAD","OPTIONS"].includes(t.method.toUpperCase()))&&(null==s?void 0:s.maxRetries)?N(r,s.maxRetries,1e3,s.context):r()}let I={async fetchBills(e){let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.status)&&t.append("status",e.status),(null==e?void 0:e.patientId)&&t.append("patient",e.patientId),(null==e?void 0:e.dateFrom)&&t.append("dateFrom",e.dateFrom),(null==e?void 0:e.dateTo)&&t.append("dateTo",e.dateTo);let s=t.toString();return C("/bills".concat(s?"?".concat(s):""),{},{maxRetries:3,context:"Fetch Bills"})},fetchBill:async e=>C("/bills/".concat(e)),createBill:async e=>C("/bills",{method:"POST",body:JSON.stringify(e)}),updateBill:async(e,t)=>C("/bills/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),deleteBill:async e=>C("/bills/".concat(e),{method:"DELETE"}),async generateFromAppointment(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"treatment";return C("/bills/generate-from-appointment",{method:"POST",body:JSON.stringify({appointmentId:e,billType:t})})},async checkAppointmentBill(e){try{let t=(await I.fetchBills({limit:1})).docs.find(t=>{var s;return"object"==typeof t.appointment&&(null==(s=t.appointment)?void 0:s.id)===e});return{hasBill:!!t,bill:t||void 0}}catch(e){return console.error("Failed to check appointment bill:",e),{hasBill:!1}}}},T={async fetchPayments(e){let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.billId)&&t.append("bill",e.billId),(null==e?void 0:e.patientId)&&t.append("patient",e.patientId),(null==e?void 0:e.paymentMethod)&&t.append("paymentMethod",e.paymentMethod),(null==e?void 0:e.status)&&t.append("paymentStatus",e.status),(null==e?void 0:e.dateFrom)&&t.append("dateFrom",e.dateFrom),(null==e?void 0:e.dateTo)&&t.append("dateTo",e.dateTo);let s=t.toString();return C("/payments".concat(s?"?".concat(s):""))},fetchPayment:async e=>C("/payments/".concat(e)),processPayment:async e=>C("/payments",{method:"POST",body:JSON.stringify(e)}),updatePayment:async(e,t)=>C("/payments/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),processRefund:async(e,t)=>C("/payments/".concat(e,"/refund"),{method:"POST",body:JSON.stringify(t)})},A={getDailyRevenue:async e=>C("/reports/daily-revenue?date=".concat(e)),getMonthlyRevenue:async(e,t)=>C("/reports/monthly-revenue?year=".concat(e,"&month=").concat(t)),getOutstandingBalances:async()=>C("/reports/outstanding-balances"),async getFinancialReport(e){let t=new URLSearchParams;return(null==e?void 0:e.startDate)&&t.set("startDate",e.startDate),(null==e?void 0:e.endDate)&&t.set("endDate",e.endDate),(null==e?void 0:e.type)&&t.set("type",e.type),C("/reports/financial?".concat(t.toString()))}},S={formatCurrency:e=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:"USD"}).format(e),calculateBillTotal(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return e+s-t},getPaymentMethodName:e=>({cash:"现金",card:"银行卡",wechat:"微信支付",alipay:"支付宝",transfer:"银行转账",deposit:"押金抵扣",installment:"分期付款"})[e]||e,getBillStatusName:e=>({draft:"草稿",sent:"已发送",confirmed:"已确认",paid:"已支付",cancelled:"已取消"})[e]||e,getPaymentStatusName:e=>({pending:"待处理",completed:"已完成",failed:"失败",refunded:"已退款"})[e]||e,getDepositStatusName:e=>({active:"有效",used:"已使用",refunded:"已退还",expired:"已过期"})[e]||e,validatePaymentAmount:(e,t)=>e>0&&e<=t};var R=s(77362),E=s(38406),D=s(90290),F=s(73259);F.ai().min(0,"金额不能为负数");let B=F.Yj().min(1,"此字段为必填项"),P=F.Yj(),k=e=>!(e<0)&&(e.toString().split(".")[1]||"").length<=2,M=F.Ik({itemType:F.k5(["treatment","consultation","material","service"],{required_error:"请选择项目类型",invalid_type_error:"无效的项目类型"}),itemName:B.max(100,"项目名称不能超过100个字符"),description:P.max(500,"描述不能超过500个字符").optional(),quantity:F.ai().min(.01,"数量必须大于0").max(9999,"数量不能超过9999").refine(e=>(e.toString().split(".")[1]||"").length<=3,"数量最多支持3位小数"),unitPrice:F.ai().min(0,"单价不能为负数").max(999999.99,"单价不能超过999,999.99").refine(k,"单价格式无效，最多支持2位小数"),discountRate:F.ai().min(0,"折扣率不能为负数").max(100,"折扣率不能超过100%").optional()}).refine(e=>!e.discountRate||!(e.discountRate>0)||0!==e.unitPrice,{message:"单价为0时不能设置折扣",path:["discountRate"]}),_=F.Ik({patient:B.uuid("请选择有效的患者"),appointment:P.uuid("请选择有效的预约").optional().or(F.eu("")),treatment:P.uuid("请选择有效的治疗项目").optional().or(F.eu("")),billType:F.k5(["treatment","consultation","deposit","additional"],{required_error:"请选择账单类型",invalid_type_error:"无效的账单类型"}),description:B.min(2,"账单描述至少需要2个字符").max(200,"账单描述不能超过200个字符"),notes:P.max(1e3,"备注不能超过1000个字符").optional(),dueDate:F.Yj().min(1,"请选择到期日期").refine(e=>{try{return!0}catch(e){return!1}},"请输入有效的日期").refine(e=>{let t=new Date(e),s=new Date;return s.setHours(0,0,0,0),t>=s},"到期日期不能是过去的日期").refine(e=>{let t=new Date(e),s=new Date;return s.setFullYear(s.getFullYear()+2),t<=s},"到期日期不能超过2年"),discountAmount:F.ai().min(0,"折扣金额不能为负数").max(999999.99,"折扣金额不能超过999,999.99").refine(k,"折扣金额格式无效").optional(),taxAmount:F.ai().min(0,"税费金额不能为负数").max(999999.99,"税费金额不能超过999,999.99").refine(k,"税费金额格式无效").optional(),items:F.YO(M).min(1,"至少需要一个账单项目").max(50,"账单项目不能超过50个")}).refine(e=>{let t=e.items.reduce((e,t)=>{let s=t.quantity*t.unitPrice,a=s*((t.discountRate||0)/100);return e+(s-a)},0),s=e.discountAmount||0;return t+(e.taxAmount||0)-s>=0},{message:"账单总金额不能为负数",path:["discountAmount"]}).refine(e=>{let t=e.items.reduce((e,t)=>{let s=t.quantity*t.unitPrice,a=s*((t.discountRate||0)/100);return e+(s-a)},0);return(e.discountAmount||0)<=t},{message:"折扣金额不能超过项目小计",path:["discountAmount"]}),O=F.Ik({amount:F.ai().min(.01,"支付金额必须大于0").max(999999.99,"支付金额不能超过999,999.99").refine(k,"支付金额格式无效，最多支持2位小数"),paymentMethod:F.k5(["cash","card","wechat","alipay","transfer","installment"],{required_error:"请选择支付方式",invalid_type_error:"无效的支付方式"}),transactionId:F.Yj().max(100,"交易ID不能超过100个字符").optional().or(F.eu("")),notes:P.max(500,"备注不能超过500个字符").optional()}).refine(e=>!["card","wechat","alipay","transfer"].includes(e.paymentMethod)||e.transactionId&&e.transactionId.trim().length>0,{message:"此支付方式需要提供交易ID",path:["transactionId"]});F.Ik({fullName:B.min(2,"姓名至少需要2个字符").max(50,"姓名不能超过50个字符").regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/,"姓名只能包含中文、英文和空格"),phone:B.regex(/^1[3-9]\d{9}$/,"请输入有效的手机号码"),email:F.Yj().email("请输入有效的邮箱地址").max(100,"邮箱地址不能超过100个字符").optional().or(F.eu("")),medicalNotes:P.max(2e3,"医疗备注不能超过2000个字符").optional()}),F.Ik({status:F.k5(["draft","sent","confirmed","paid","cancelled"],{required_error:"请选择账单状态",invalid_type_error:"无效的账单状态"}),notes:P.max(500,"状态更新备注不能超过500个字符").optional()}),F.Ik({search:P.max(100,"搜索关键词不能超过100个字符").optional(),status:F.k5(["draft","sent","confirmed","paid","cancelled"]).optional(),billType:F.k5(["treatment","consultation","deposit","additional"]).optional(),patientId:P.uuid("请选择有效的患者").optional().or(F.eu("")),dateFrom:F.Yj().optional().refine(e=>{if(!e)return!0;try{return!0}catch(e){return!1}},"请输入有效的开始日期"),dateTo:F.Yj().optional().refine(e=>{if(!e)return!0;try{return!0}catch(e){return!1}},"请输入有效的结束日期"),amountMin:F.ai().min(0,"最小金额不能为负数").max(999999.99,"最小金额不能超过999,999.99").optional(),amountMax:F.ai().min(0,"最大金额不能为负数").max(999999.99,"最大金额不能超过999,999.99").optional()}).refine(e=>!e.dateFrom||!e.dateTo||new Date(e.dateFrom)<=new Date(e.dateTo),{message:"开始日期不能晚于结束日期",path:["dateTo"]}).refine(e=>void 0===e.amountMin||void 0===e.amountMax||e.amountMin<=e.amountMax,{message:"最小金额不能大于最大金额",path:["amountMax"]});let L={bill:{created:e=>{f.toast.success("账单创建成功！",{description:"账单编号: ".concat(e.billNumber),duration:4e3})},updated:e=>{f.toast.success("账单更新成功！",{description:"账单编号: ".concat(e.billNumber),duration:4e3})},deleted:e=>{f.toast.success("账单删除成功！",{description:"账单编号: ".concat(e),duration:4e3})},statusUpdated:(e,t,s)=>{let a={draft:"草稿",sent:"已发送",confirmed:"已确认",paid:"已支付",cancelled:"已取消"};f.toast.success("账单状态已更新！",{description:"".concat(e.billNumber,": ").concat(a[t]," → ").concat(a[s]),duration:5e3})},generateFromAppointment:(e,t)=>{f.toast.success("从预约生成账单成功！",{description:"预约日期: ".concat(t,"，账单编号: ").concat(e.billNumber),duration:4e3})},loadError:e=>{f.toast.error("加载账单失败",{description:e||"请检查网络连接后重试",duration:5e3})},createError:e=>{f.toast.error("创建账单失败",{description:e||"请检查输入信息后重试",duration:5e3})},updateError:e=>{f.toast.error("更新账单失败",{description:e||"请稍后重试",duration:5e3})},deleteError:e=>{f.toast.error("删除账单失败",{description:e||"请稍后重试",duration:5e3})},validationError:e=>{f.toast.error("账单验证失败",{description:e,duration:5e3})}},payment:{processed:e=>{f.toast.success("支付处理成功！",{description:"支付金额: ".concat(S.formatCurrency(e.amount),"，支付编号: ").concat(e.paymentNumber),duration:5e3})},receiptGenerated:e=>{f.toast.success("收据生成成功！",{description:"收据编号: ".concat(e.receiptNumber||"待生成"),duration:4e3})},refunded:(e,t)=>{f.toast.success("退款处理成功！",{description:"退款金额: ".concat(S.formatCurrency(t),"，支付编号: ").concat(e.paymentNumber),duration:5e3})},statusUpdated:(e,t,s)=>{let a={pending:"待处理",completed:"已完成",failed:"失败",refunded:"已退款"};f.toast.success("支付状态已更新！",{description:"".concat(e.paymentNumber,": ").concat(a[t]," → ").concat(a[s]),duration:4e3})},processError:e=>{f.toast.error("支付处理失败",{description:e||"请检查支付信息后重试",duration:5e3})},refundError:e=>{f.toast.error("退款处理失败",{description:e||"请联系管理员处理",duration:5e3})},validationError:e=>{f.toast.error("支付验证失败",{description:e,duration:5e3})},amountExceeded:e=>{f.toast.error("支付金额超限",{description:"最大支付金额: ".concat(S.formatCurrency(e)),duration:5e3})}},receipt:{printed:e=>{f.toast.success("收据打印成功！",{description:"收据编号: ".concat(e),duration:3e3})},downloaded:e=>{f.toast.success("收据下载成功！",{description:"收据编号: ".concat(e),duration:3e3})},printError:()=>{f.toast.error("收据打印失败",{description:"请检查打印机设置",duration:4e3})},downloadError:()=>{f.toast.error("收据下载失败",{description:"请稍后重试",duration:4e3})},notFound:e=>{f.toast.error("收据未找到",{description:"收据编号: ".concat(e),duration:4e3})}},system:{loading:e=>{f.toast.loading("".concat(e,"中..."),{duration:1/0})},networkError:()=>{f.toast.error("网络连接失败",{description:"请检查网络连接后重试",duration:5e3})},permissionDenied:e=>{f.toast.error("权限不足",{description:"您没有权限执行: ".concat(e),duration:5e3})},dataRefreshed:()=>{f.toast.success("数据刷新成功",{duration:2e3})},dataRefreshError:()=>{f.toast.error("数据刷新失败",{description:"请稍后重试",duration:4e3})},operationCancelled:e=>{f.toast.info("".concat(e,"已取消"),{duration:2e3})},featureNotImplemented:e=>{f.toast.info("".concat(e,"功能开发中..."),{description:"敬请期待",duration:3e3})}},validation:{requiredField:e=>{f.toast.error("字段验证失败",{description:"".concat(e,"为必填项"),duration:4e3})},invalidFormat:(e,t)=>{f.toast.error("格式验证失败",{description:"".concat(e,"格式应为: ").concat(t),duration:4e3})},duplicateEntry:(e,t)=>{f.toast.error("重复条目",{description:"".concat(e,' "').concat(t,'" 已存在'),duration:4e3})},unsavedChanges:()=>{f.toast.warning("有未保存的更改",{description:"请保存后再继续",duration:4e3})},confirmAction:e=>{f.toast.warning("请确认操作",{description:"即将执行: ".concat(e),duration:5e3})}}};class z{debounce(e,t){for(var s=arguments.length,a=Array(s>2?s-2:0),n=2;n<s;n++)a[n-2]=arguments[n];let r=this.timeouts.get(e);r&&clearTimeout(r);let l=setTimeout(()=>{t(...a),this.timeouts.delete(e)},this.delay);this.timeouts.set(e,l)}clear(e){if(e){let t=this.timeouts.get(e);t&&(clearTimeout(t),this.timeouts.delete(e))}else this.timeouts.forEach(e=>clearTimeout(e)),this.timeouts.clear()}constructor(e=500){this.timeouts=new Map,this.delay=e}}class V{validateField(e,t,s,a){this.debouncer.debounce(e,this.performFieldValidation.bind(this),e,t,s,a)}performFieldValidation(e,t,s,a){try{let n=this.setNestedValue({},e,t),r={...s,...n},l=this.schema.safeParse(r),i=l.success?[]:l.error.errors.filter(t=>t.path.join(".")===e).map(t=>({field:e,message:t.message,code:t.code,severity:"error"})),d=this.generateWarnings(e,t,s),o={isValid:0===i.length,errors:i,warnings:d};a&&a(o)}catch(t){console.error("Field validation error:",t),a&&a({isValid:!1,errors:[{field:e,message:"验证过程中发生错误",code:"VALIDATION_ERROR",severity:"error"}],warnings:[]})}}setNestedValue(e,t,s){let a=t.split("."),n=e;for(let e=0;e<a.length-1;e++){let t=a[e];t in n||(n[t]={}),n=n[t]}return n[a[a.length-1]]=s,e}generateWarnings(e,t,s){let a=[];switch(e){case"amount":"number"==typeof t&&t>1e4&&a.push({field:e,message:"金额较大，请确认是否正确",suggestion:"检查金额是否输入正确"});break;case"dueDate":if(t){let s=new Date(t),n=new Date,r=Math.ceil((s.getTime()-n.getTime())/864e5);r>365?a.push({field:e,message:"到期日期距离现在超过一年",suggestion:"考虑设置更近的到期日期"}):r<7&&a.push({field:e,message:"到期日期较近",suggestion:"确保有足够时间处理账单"})}break;case"discountAmount":"number"==typeof t&&t>0&&s.items&&t>.5*s.items.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0)&&a.push({field:e,message:"折扣金额超过小计的50%",suggestion:"确认折扣金额是否正确"});break;case"unitPrice":"number"==typeof t&&0===t&&a.push({field:e,message:"单价为0，确认是否为免费项目",suggestion:"如果不是免费项目，请输入正确单价"})}return a}cleanup(){this.debouncer.clear()}constructor(e,t=300){this.schema=e,this.debouncer=new z(t)}}class q{validateForm(e){try{let t=this.schema.safeParse(e);if(t.success)return{isValid:!0,errors:[],warnings:this.generateFormWarnings(e)};let s=t.error.errors.map(e=>({field:e.path.join("."),message:e.message,code:e.code,severity:"error"}));return{isValid:!1,errors:s,warnings:this.generateFormWarnings(e)}}catch(e){return console.error("Form validation error:",e),{isValid:!1,errors:[{field:"form",message:"表单验证过程中发生错误",code:"FORM_VALIDATION_ERROR",severity:"error"}],warnings:[]}}}generateFormWarnings(e){let t=[];if(e.items&&Array.isArray(e.items)){let s=e.items.length;s>20&&t.push({field:"items",message:"账单包含".concat(s,"个项目，较多"),suggestion:"考虑合并相似项目或分拆为多个账单"}),e.items.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0)>5e4&&t.push({field:"form",message:"账单总金额较大",suggestion:"确认金额计算是否正确"})}return t}getFieldValidator(e){return this.fieldValidators.has(e)||this.fieldValidators.set(e,new V(this.schema)),this.fieldValidators.get(e)}cleanup(){this.fieldValidators.forEach(e=>e.cleanup()),this.fieldValidators.clear()}constructor(e){this.fieldValidators=new Map,this.schema=e}}let J={isValidPaymentAmount:(e,t)=>{let s=t.remainingAmount||0;return e>s?{valid:!1,reason:"支付金额不能超过待付金额 $".concat(s.toFixed(2))}:e<=0?{valid:!1,reason:"支付金额必须大于0"}:{valid:!0}}};var U=s(42094),$=s(25192),W=s(45450),H=s(86540),Z=s(56420),Y=s(35355),G=s(27709),X=s(59196),K=s(12987),Q=s(750),ee=s(72486),et=s(68046);let es={name:"美丽诊所",address:"北京市朝阳区美丽街123号",phone:"010-12345678",email:"<EMAIL>",taxId:"91110000000000000X"},ea=(0,n.forwardRef)((e,t)=>{let{payment:s,bill:n,clinicInfo:r=es}=e,i="object"==typeof s.patient?s.patient:null,d=n||("object"==typeof s.bill?s.bill:null);return(0,a.jsx)("div",{ref:t,className:"max-w-md mx-auto bg-white",children:(0,a.jsxs)(H.Zp,{className:"shadow-none border-none",children:[(0,a.jsxs)(H.aR,{className:"text-center pb-4",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h1",{className:"text-xl font-bold",children:r.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:r.address}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["电话: ",r.phone,r.email&&" | 邮箱: ".concat(r.email)]}),r.taxId&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["税号: ",r.taxId]})]}),(0,a.jsx)(Y.Separator,{className:"my-4"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"收款收据"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"收据编号:"}),(0,a.jsx)("span",{className:"font-mono",children:s.receiptNumber||"待生成"})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"支付编号:"}),(0,a.jsx)("span",{className:"font-mono",children:s.paymentNumber})]})]})]}),(0,a.jsxs)(H.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"支付信息"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"患者姓名:"}),(0,a.jsx)("span",{children:(null==i?void 0:i.fullName)||"未知患者"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付日期:"}),(0,a.jsx)("span",{children:new Date(s.paymentDate).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付方式:"}),(0,a.jsx)("span",{children:S.getPaymentMethodName(s.paymentMethod)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付状态:"}),(0,a.jsx)(l.E,{variant:"completed"===s.paymentStatus?"default":"secondary",children:S.getPaymentStatusName(s.paymentStatus)})]})]}),s.transactionId&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"交易ID:"}),(0,a.jsx)("span",{className:"font-mono text-xs",children:s.transactionId})]})]}),(0,a.jsx)(Y.Separator,{}),d&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"账单信息"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"账单编号:"}),(0,a.jsx)("span",{className:"font-mono",children:d.billNumber})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"账单类型:"}),(0,a.jsx)("span",{children:S.getBillStatusName(d.billType)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"服务描述:"}),(0,a.jsx)("span",{className:"text-right max-w-32 truncate",children:d.description})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"账单总额:"}),(0,a.jsx)("span",{children:S.formatCurrency(d.totalAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"已支付:"}),(0,a.jsx)("span",{className:"text-green-600",children:S.formatCurrency(d.paidAmount||0)})]}),(d.remainingAmount||0)>0&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"待支付:"}),(0,a.jsx)("span",{className:"text-red-600",children:S.formatCurrency(d.remainingAmount||0)})]})]})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"本次支付"}),(0,a.jsx)("div",{className:"bg-muted/30 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-lg font-medium",children:"支付金额:"}),(0,a.jsx)("span",{className:"text-xl font-bold text-green-600",children:S.formatCurrency(s.amount)})]})})]}),s.notes&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"font-medium text-sm border-b pb-1",children:"备注"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:s.notes})]})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["感谢您选择",r.name]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["如有疑问，请联系我们: ",r.phone]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["打印时间: ",new Date().toLocaleString("zh-CN")]})]})]})]})})});ea.displayName="Receipt";var en=s(61654),er=s(82168);function el(e){let{payment:t,bill:s,isOpen:r,onClose:l}=e,d=(0,n.useRef)(null),o=async()=>{try{L.system.featureNotImplemented("PDF下载")}catch(e){console.error("PDF generation failed:",e),L.receipt.downloadError()}};return t?(0,a.jsx)(R.lG,{open:r,onOpenChange:l,"data-sentry-element":"Dialog","data-sentry-component":"ReceiptDialog","data-sentry-source-file":"receipt-dialog.tsx",children:(0,a.jsxs)(R.Cf,{className:"max-w-lg max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"receipt-dialog.tsx",children:[(0,a.jsx)(R.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"receipt-dialog.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(R.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"receipt-dialog.tsx",children:["收据 - ",t.receiptNumber||t.paymentNumber]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{if(!d.current)return;let e=window.open("","_blank");if(!e)return void L.receipt.printError();let s=d.current.innerHTML;e.document.write("\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>收据 - ".concat((null==t?void 0:t.receiptNumber)||(null==t?void 0:t.paymentNumber),"</title>\n          <meta charset=\"utf-8\">\n          <style>\n            body {\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n              margin: 0;\n              padding: 20px;\n              background: white;\n            }\n            .max-w-md {\n              max-width: 28rem;\n            }\n            .mx-auto {\n              margin-left: auto;\n              margin-right: auto;\n            }\n            .bg-white {\n              background-color: white;\n            }\n            .shadow-none {\n              box-shadow: none;\n            }\n            .border-none {\n              border: none;\n            }\n            .text-center {\n              text-align: center;\n            }\n            .text-xl {\n              font-size: 1.25rem;\n            }\n            .text-lg {\n              font-size: 1.125rem;\n            }\n            .text-sm {\n              font-size: 0.875rem;\n            }\n            .text-xs {\n              font-size: 0.75rem;\n            }\n            .font-bold {\n              font-weight: 700;\n            }\n            .font-semibold {\n              font-weight: 600;\n            }\n            .font-medium {\n              font-weight: 500;\n            }\n            .font-mono {\n              font-family: ui-monospace, SFMono-Regular, monospace;\n            }\n            .space-y-1 > * + * {\n              margin-top: 0.25rem;\n            }\n            .space-y-2 > * + * {\n              margin-top: 0.5rem;\n            }\n            .space-y-4 > * + * {\n              margin-top: 1rem;\n            }\n            .pb-4 {\n              padding-bottom: 1rem;\n            }\n            .pb-1 {\n              padding-bottom: 0.25rem;\n            }\n            .p-3 {\n              padding: 0.75rem;\n            }\n            .my-4 {\n              margin-top: 1rem;\n              margin-bottom: 1rem;\n            }\n            .border-b {\n              border-bottom: 1px solid #e5e7eb;\n            }\n            .grid {\n              display: grid;\n            }\n            .grid-cols-2 {\n              grid-template-columns: repeat(2, minmax(0, 1fr));\n            }\n            .gap-2 {\n              gap: 0.5rem;\n            }\n            .flex {\n              display: flex;\n            }\n            .justify-between {\n              justify-content: space-between;\n            }\n            .items-center {\n              align-items: center;\n            }\n            .text-right {\n              text-align: right;\n            }\n            .max-w-32 {\n              max-width: 8rem;\n            }\n            .truncate {\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n            .text-muted-foreground {\n              color: #6b7280;\n            }\n            .text-green-600 {\n              color: #059669;\n            }\n            .text-red-600 {\n              color: #dc2626;\n            }\n            .bg-muted\\/30 {\n              background-color: rgba(243, 244, 246, 0.3);\n            }\n            .rounded-lg {\n              border-radius: 0.5rem;\n            }\n            hr {\n              border: none;\n              border-top: 1px solid #e5e7eb;\n              margin: 1rem 0;\n            }\n            @media print {\n              body {\n                padding: 0;\n              }\n              .no-print {\n                display: none;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          ").concat(s,"\n        </body>\n      </html>\n    ")),e.document.close(),e.focus(),setTimeout(()=>{e.print(),e.close()},250),L.receipt.printed((null==t?void 0:t.receiptNumber)||(null==t?void 0:t.paymentNumber)||"")},"data-sentry-element":"Button","data-sentry-source-file":"receipt-dialog.tsx",children:[(0,a.jsx)(en.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPrinter","data-sentry-source-file":"receipt-dialog.tsx"}),"打印"]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:o,"data-sentry-element":"Button","data-sentry-source-file":"receipt-dialog.tsx",children:[(0,a.jsx)(er.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconDownload","data-sentry-source-file":"receipt-dialog.tsx"}),"下载PDF"]}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:l,"data-sentry-element":"Button","data-sentry-source-file":"receipt-dialog.tsx",children:(0,a.jsx)(et.A,{className:"h-4 w-4","data-sentry-element":"IconX","data-sentry-source-file":"receipt-dialog.tsx"})})]})]})}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(ea,{ref:d,payment:t,bill:s,"data-sentry-element":"Receipt","data-sentry-source-file":"receipt-dialog.tsx"})})]})}):null}let ei=[{value:"cash",label:"现金",icon:G.A,description:"现金支付",requiresTransactionId:!1},{value:"card",label:"银行卡",icon:p.A,description:"银行卡刷卡支付",requiresTransactionId:!0},{value:"wechat",label:"微信支付",icon:X.A,description:"微信扫码支付",requiresTransactionId:!0},{value:"alipay",label:"支付宝",icon:K.A,description:"支付宝扫码支付",requiresTransactionId:!0},{value:"transfer",label:"银行转账",icon:Q.A,description:"银行转账支付",requiresTransactionId:!0},{value:"installment",label:"分期付款",icon:ee.A,description:"分期付款",requiresTransactionId:!1}];function ed(e){let{bill:t,onSuccess:s,onCancel:r,isOpen:l=!0}=e,[d,o]=(0,n.useState)(!1),[c,u]=(0,n.useState)(""),[x,h]=(0,n.useState)(null),[p,f]=(0,n.useState)(!1),y=(0,E.mN)({resolver:(0,D.u)(O),defaultValues:{amount:t.remainingAmount||0,paymentMethod:"cash",transactionId:"",notes:""}}),g=ei.find(e=>e.value===c),j=t.remainingAmount||0,b=async e=>{try{o(!0);let a=J.isValidPaymentAmount(e.amount,t);if(!a.valid)return void L.payment.validationError(a.reason||"支付金额无效");let n=await T.processPayment({bill:t.id,patient:"object"==typeof t.patient?t.patient.id:t.patientId,amount:e.amount,paymentMethod:e.paymentMethod,transactionId:e.transactionId||void 0,notes:e.notes||void 0});L.payment.processed(n),h(n),f(!0),s&&s(n),y.reset()}catch(t){console.error("Payment processing failed:",t);let e=t instanceof w?t.message:void 0;L.payment.processError(e)}finally{o(!1)}};return l?(0,a.jsxs)(H.Zp,{className:"w-full max-w-2xl mx-auto","data-sentry-element":"Card","data-sentry-component":"PaymentForm","data-sentry-source-file":"payment-form.tsx",children:[(0,a.jsx)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"payment-form.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"payment-form.tsx",children:[(0,a.jsx)(m.A,{className:"h-5 w-5","data-sentry-element":"IconReceipt","data-sentry-source-file":"payment-form.tsx"}),"处理支付"]}),(0,a.jsxs)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"payment-form.tsx",children:["为账单 ",t.billNumber," 处理支付"]})]}),r&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:r,children:(0,a.jsx)(et.A,{className:"h-4 w-4"})})]})}),(0,a.jsxs)(H.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"payment-form.tsx",children:[(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4 space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"患者:"}),(0,a.jsx)("span",{className:"text-sm",children:"object"==typeof t.patient?t.patient.fullName:"未知患者"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"账单总额:"}),(0,a.jsx)("span",{className:"text-sm font-semibold",children:S.formatCurrency(t.totalAmount)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"已支付:"}),(0,a.jsx)("span",{className:"text-sm text-green-600",children:S.formatCurrency(t.paidAmount||0)})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"payment-form.tsx"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"待支付:"}),(0,a.jsx)("span",{className:"text-lg font-bold text-red-600",children:S.formatCurrency(j)})]})]}),(0,a.jsx)(Z.lV,{...y,"data-sentry-element":"Form","data-sentry-source-file":"payment-form.tsx",children:(0,a.jsxs)("form",{onSubmit:y.handleSubmit(b),className:"space-y-6",children:[(0,a.jsx)(Z.zB,{control:y.control,name:"amount",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"支付金额"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground",children:"$"}),(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0.01",max:j,placeholder:"0.00",className:"pl-8",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})]})}),(0,a.jsxs)(Z.Rr,{children:["最大支付金额: ",S.formatCurrency(j)]}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"payment-form.tsx"}),(0,a.jsx)(Z.zB,{control:y.control,name:"paymentMethod",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"支付方式"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:t.value,onValueChange:e=>{t.onChange(e),u(e)},children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择支付方式"})}),(0,a.jsx)(W.gC,{children:ei.map(e=>{let t=e.icon;return(0,a.jsx)(W.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t,{className:"h-4 w-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.value)})})]})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"payment-form.tsx"}),(null==g?void 0:g.requiresTransactionId)&&(0,a.jsx)(Z.zB,{control:y.control,name:"transactionId",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"交易ID"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{placeholder:"输入第三方支付平台的交易ID",...t})}),(0,a.jsxs)(Z.Rr,{children:["请输入",g.label,"的交易ID或流水号"]}),(0,a.jsx)(Z.C5,{})]})}}),(0,a.jsx)(Z.zB,{control:y.control,name:"notes",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"备注 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)($.T,{placeholder:"支付相关备注信息...",className:"resize-none",rows:3,...t})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"payment-form.tsx"}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)(i.$,{type:"submit",disabled:d,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"payment-form.tsx",children:d?"处理中...":"确认支付"}),r&&(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:r,disabled:d,children:"取消"})]})]})})]}),(0,a.jsx)(el,{payment:x,bill:t,isOpen:p,onClose:()=>{f(!1),h(null)},"data-sentry-element":"ReceiptDialog","data-sentry-source-file":"payment-form.tsx"})]}):null}function eo(e){let{bill:t,isOpen:s,onClose:n,onSuccess:r}=e;return t?(0,a.jsx)(R.lG,{open:s,onOpenChange:n,"data-sentry-element":"Dialog","data-sentry-component":"PaymentDialog","data-sentry-source-file":"payment-dialog.tsx",children:(0,a.jsxs)(R.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"payment-dialog.tsx",children:[(0,a.jsx)(R.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"payment-dialog.tsx",children:(0,a.jsxs)(R.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"payment-dialog.tsx",children:["处理支付 - ",t.billNumber]})}),(0,a.jsx)(ed,{bill:t,onSuccess:e=>{r&&r(e),n()},onCancel:n,isOpen:!0,"data-sentry-element":"PaymentForm","data-sentry-source-file":"payment-dialog.tsx"})]})}):null}var ec=s(84692),em=s(57165),eu=s(93900);let ex=[{value:"treatment",label:"治疗账单"},{value:"consultation",label:"咨询账单"},{value:"deposit",label:"押金账单"},{value:"additional",label:"补充账单"}],eh=[{value:"treatment",label:"治疗项目"},{value:"consultation",label:"咨询服务"},{value:"material",label:"材料费用"},{value:"service",label:"其他服务"}];function ep(e){var t;let{bill:s,patients:r=[],appointments:l=[],treatments:d=[],onSuccess:o,onCancel:c,isOpen:x=!0}=e,[h,p]=(0,n.useState)(!1),[f,y]=(0,n.useState)({subtotal:0,totalAmount:0}),[g]=(0,n.useState)(()=>new q(_)),[j,b]=(0,n.useState)({}),v=!!s,N=(0,E.mN)({resolver:(0,D.u)(_),defaultValues:{patient:(null==s?void 0:s.patientId)?String(s.patientId):"",appointment:(null==s?void 0:s.appointmentId)?String(s.appointmentId):"",treatment:(null==s?void 0:s.treatmentId)?String(s.treatmentId):"",billType:(null==s?void 0:s.billType)||"treatment",description:(null==s?void 0:s.description)||"",notes:(null==s?void 0:s.notes)||"",dueDate:(null==s?void 0:s.dueDate)?new Date(s.dueDate).toISOString().split("T")[0]:"",discountAmount:(null==s?void 0:s.discountAmount)||0,taxAmount:(null==s?void 0:s.taxAmount)||0,items:(null==s||null==(t=s.items)?void 0:t.map(e=>({itemType:e.itemType,itemName:e.itemName,description:e.description||"",quantity:e.quantity,unitPrice:e.unitPrice,discountRate:e.discountRate||0})))||[{itemType:"treatment",itemName:"",description:"",quantity:1,unitPrice:0,discountRate:0}]}}),{fields:C,append:T,remove:A}=(0,E.jz)({control:N.control,name:"items"}),R=N.watch("items"),F=N.watch("discountAmount"),B=N.watch("taxAmount");(0,n.useEffect)(()=>{let e=R.reduce((e,t)=>{let s=(t.quantity||0)*(t.unitPrice||0),a=s*((t.discountRate||0)/100);return e+(s-a)},0),t=S.calculateBillTotal(e,F||0,B||0);y({subtotal:e,totalAmount:t})},[R,F,B]);let P=async e=>{try{let t;p(!0);let a={patient:parseInt(e.patient),appointment:e.appointment&&"none"!==e.appointment?parseInt(e.appointment):void 0,treatment:e.treatment&&"none"!==e.treatment?parseInt(e.treatment):void 0,billType:e.billType,subtotal:f.subtotal,discountAmount:e.discountAmount||0,taxAmount:e.taxAmount||0,totalAmount:f.totalAmount,description:e.description,notes:e.notes||void 0,dueDate:e.dueDate,items:e.items.map(e=>({itemType:e.itemType,itemName:e.itemName,description:e.description||void 0,quantity:e.quantity,unitPrice:e.unitPrice,discountRate:e.discountRate||0}))};v&&s?(t=await I.updateBill(s.id,a),L.bill.updated(t)):(t=await I.createBill(a),L.bill.created(t)),o&&o(t),v||N.reset()}catch(t){console.error("Bill operation failed:",t);let e=t instanceof w?t.message:void 0;v?L.bill.updateError(e):L.bill.createError(e)}finally{p(!1)}},k=e=>{C.length>1?A(e):L.validation.confirmAction("至少需要保留一个账单项目")};return x?(0,a.jsxs)(H.Zp,{className:"w-full max-w-4xl mx-auto","data-sentry-element":"Card","data-sentry-component":"BillForm","data-sentry-source-file":"bill-form.tsx",children:[(0,a.jsx)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"bill-form.tsx",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"bill-form.tsx",children:[(0,a.jsx)(m.A,{className:"h-5 w-5","data-sentry-element":"IconReceipt","data-sentry-source-file":"bill-form.tsx"}),v?"编辑账单":"创建账单"]}),(0,a.jsx)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"bill-form.tsx",children:v?"编辑账单 ".concat(null==s?void 0:s.billNumber):"创建新的账单"})]}),c&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:c,children:(0,a.jsx)(et.A,{className:"h-4 w-4"})})]})}),(0,a.jsx)(H.Wu,{className:"space-y-6","data-sentry-element":"CardContent","data-sentry-source-file":"bill-form.tsx",children:(0,a.jsx)(Z.lV,{...N,"data-sentry-element":"Form","data-sentry-source-file":"bill-form.tsx",children:(0,a.jsxs)("form",{onSubmit:N.handleSubmit(P),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(Z.zB,{control:N.control,name:"patient",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsxs)(Z.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(em.A,{className:"h-4 w-4"}),"患者"]}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:t.value,onValueChange:t.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择患者"})}),(0,a.jsx)(W.gC,{children:r.map(e=>(0,a.jsxs)(W.eb,{value:String(e.id),children:[e.fullName," - ",e.phone]},e.id))})]})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:N.control,name:"billType",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"账单类型"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:t.value,onValueChange:t.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择账单类型"})}),(0,a.jsx)(W.gC,{children:ex.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:N.control,name:"appointment",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"关联预约 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:t.value||"",onValueChange:t.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择预约"})}),(0,a.jsxs)(W.gC,{children:[(0,a.jsx)(W.eb,{value:"none",children:"无关联预约"}),l.map(e=>(0,a.jsxs)(W.eb,{value:e.id,children:[new Date(e.appointmentDate).toLocaleDateString("zh-CN")," -","object"==typeof e.treatment?e.treatment.name:"未知治疗"]},e.id))]})]})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:N.control,name:"dueDate",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsxs)(Z.lR,{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4"}),"到期日期"]}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{type:"date",...t})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"})]}),(0,a.jsx)(Z.zB,{control:N.control,name:"description",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"账单描述"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{placeholder:"输入账单描述...",...t})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:N.control,name:"notes",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"备注 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)($.T,{placeholder:"账单相关备注...",className:"resize-none",rows:3,...t})}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"账单项目"}),(0,a.jsxs)(i.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{T({itemType:"treatment",itemName:"",description:"",quantity:1,unitPrice:0,discountRate:0})},"data-sentry-element":"Button","data-sentry-source-file":"bill-form.tsx",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"bill-form.tsx"}),"添加项目"]})]}),(0,a.jsx)("div",{className:"space-y-4",children:C.map((e,t)=>(0,a.jsxs)(H.Zp,{className:"p-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-4 items-end",children:[(0,a.jsx)(Z.zB,{control:N.control,name:"items.".concat(t,".itemType"),render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"类型"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsxs)(W.l6,{value:t.value,onValueChange:t.onChange,children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{})}),(0,a.jsx)(W.gC,{children:eh.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))})]})}),(0,a.jsx)(Z.C5,{})]})}}),(0,a.jsx)(Z.zB,{control:N.control,name:"items.".concat(t,".itemName"),render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"项目名称"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{placeholder:"项目名称",...t})}),(0,a.jsx)(Z.C5,{})]})}}),(0,a.jsx)(Z.zB,{control:N.control,name:"items.".concat(t,".quantity"),render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"数量"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0.01",placeholder:"1",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,a.jsx)(Z.C5,{})]})}}),(0,a.jsx)(Z.zB,{control:N.control,name:"items.".concat(t,".unitPrice"),render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"单价"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,a.jsx)(Z.C5,{})]})}}),(0,a.jsx)(Z.zB,{control:N.control,name:"items.".concat(t,".discountRate"),render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"折扣率 (%)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{type:"number",step:"0.1",min:"0",max:"100",placeholder:"0",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,a.jsx)(Z.C5,{})]})}}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(i.$,{type:"button",variant:"outline",size:"sm",onClick:()=>k(t),disabled:C.length<=1,children:(0,a.jsx)(eu.A,{className:"h-4 w-4"})})})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(Z.zB,{control:N.control,name:"items.".concat(t,".description"),render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"项目描述 (可选)"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)($.T,{placeholder:"项目详细描述...",className:"resize-none",rows:2,...t})}),(0,a.jsx)(Z.C5,{})]})}})}),(0,a.jsx)("div",{className:"mt-2 text-right",children:(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["小计: ",S.formatCurrency((()=>{let e=R[t];if(!e)return 0;let s=(e.quantity||0)*(e.unitPrice||0),a=s*((e.discountRate||0)/100);return s-a})())]})})]},e.id))})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(Z.zB,{control:N.control,name:"discountAmount",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"额外折扣金额"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,a.jsx)(Z.Rr,{children:"在项目折扣基础上的额外折扣"}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsx)(Z.zB,{control:N.control,name:"taxAmount",render:e=>{let{field:t}=e;return(0,a.jsxs)(Z.eI,{children:[(0,a.jsx)(Z.lR,{children:"税费金额"}),(0,a.jsx)(Z.MJ,{children:(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",...t,onChange:e=>t.onChange(parseFloat(e.target.value)||0)})}),(0,a.jsx)(Z.Rr,{children:"需要添加的税费金额"}),(0,a.jsx)(Z.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(ec.J,{"data-sentry-element":"Label","data-sentry-source-file":"bill-form.tsx",children:"账单总计"}),(0,a.jsxs)("div",{className:"bg-muted/50 rounded-lg p-3 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"项目小计:"}),(0,a.jsx)("span",{children:S.formatCurrency(f.subtotal)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"额外折扣:"}),(0,a.jsxs)("span",{children:["-",S.formatCurrency(F||0)]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{children:"税费:"}),(0,a.jsxs)("span",{children:["+",S.formatCurrency(B||0)]})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-form.tsx"}),(0,a.jsxs)("div",{className:"flex justify-between font-semibold",children:[(0,a.jsx)("span",{children:"总金额:"}),(0,a.jsx)("span",{className:"text-lg",children:S.formatCurrency(f.totalAmount)})]})]})]})]}),(0,a.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,a.jsx)(i.$,{type:"submit",disabled:h,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"bill-form.tsx",children:h?v?"更新中...":"创建中...":v?"更新账单":"创建账单"}),c&&(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:c,disabled:h,children:"取消"})]})]})})})]}):null}function ef(e){let{bill:t,isOpen:s,onClose:r,onSuccess:l}=e,[i,d]=(0,n.useState)([]),[o,c]=(0,n.useState)([]),[m,u]=(0,n.useState)([]),[x,h]=(0,n.useState)(!1);(0,n.useEffect)(()=>{s&&p()},[s]);let p=async()=>{try{h(!0);let[e,t,s]=await Promise.all([fetch("/api/patients").then(e=>e.json()),fetch("/api/appointments").then(e=>e.json()),fetch("/api/treatments").then(e=>e.json())]);d(e.docs||[]),c(t.docs||[]),u(s.docs||[])}catch(e){console.error("Failed to fetch required data:",e),f.toast.error("加载数据失败，请稍后重试")}finally{h(!1)}},y=!!t;return(0,a.jsx)(R.lG,{open:s,onOpenChange:r,"data-sentry-element":"Dialog","data-sentry-component":"BillDialog","data-sentry-source-file":"bill-dialog.tsx",children:(0,a.jsxs)(R.Cf,{className:"max-w-5xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"bill-dialog.tsx",children:[(0,a.jsx)(R.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"bill-dialog.tsx",children:(0,a.jsx)(R.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"bill-dialog.tsx",children:y?"编辑账单 - ".concat(null==t?void 0:t.billNumber):"创建新账单"})}),x?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,a.jsx)("span",{className:"ml-2 text-muted-foreground",children:"加载数据中..."})]}):(0,a.jsx)(ep,{bill:t||void 0,patients:i,appointments:o,treatments:m,onSuccess:e=>{l&&l(e),r()},onCancel:r,isOpen:!0})]})})}var ey=s(40548),eg=s(92775),ej=s(68065),eb=s(60382),ev=s(46976);let eN={draft:["sent","cancelled"],sent:["confirmed","cancelled"],confirmed:["paid","cancelled"],paid:[],cancelled:[]},ew={draft:{label:"草稿",color:"bg-gray-100 text-gray-800",icon:eg.A,description:"账单正在编辑中"},sent:{label:"已发送",color:"bg-blue-100 text-blue-800",icon:ej.A,description:"账单已发送给患者"},confirmed:{label:"已确认",color:"bg-yellow-100 text-yellow-800",icon:eb.A,description:"患者已确认账单"},paid:{label:"已支付",color:"bg-green-100 text-green-800",icon:p.A,description:"账单已完全支付"},cancelled:{label:"已取消",color:"bg-red-100 text-red-800",icon:et.A,description:"账单已取消"}};function eC(e){let{bill:t,onStatusUpdate:s,trigger:r}=e,{hasPermission:o}=(0,d.It)(),[c,m]=(0,n.useState)(!1),[u,x]=(0,n.useState)(t.status),[p,y]=(0,n.useState)(""),[g,j]=(0,n.useState)(!1),b=ew[t.status],v=eN[t.status]||[],N=o("canEditBills")&&v.length>0,C=async()=>{if(u===t.status)return void f.toast.warning("请选择不同的状态");try{if(j(!0),!v.includes(u))return void f.toast.error("无效的状态转换");if("paid"===u&&(t.remainingAmount||0)>0)return void f.toast.error("账单还有未支付金额，无法标记为已支付");let e={status:u};p.trim()&&(e.notes=t.notes?"".concat(t.notes,"\n\n[状态更新] ").concat(p.trim()):"[状态更新] ".concat(p.trim()));let a=await I.updateBill(t.id,e);f.toast.success("账单状态已更新为: ".concat(ew[u].label)),s&&s(a),m(!1),y("")}catch(t){console.error("Failed to update bill status:",t);let e=t instanceof w?t.message:"状态更新失败，请稍后重试";f.toast.error(e)}finally{j(!1)}},T=e=>{switch(e){case"sent":return"发送账单后，患者将收到账单通知";case"confirmed":return"确认账单表示患者已同意账单内容";case"paid":return"标记为已支付前，请确保所有款项已收到";case"cancelled":return"取消账单后将无法恢复，请谨慎操作";default:return null}};return N?(0,a.jsxs)(R.lG,{open:c,onOpenChange:m,"data-sentry-element":"Dialog","data-sentry-component":"BillStatusManager","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(R.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"bill-status-manager.tsx",children:r||(0,a.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"更新状态"]})}),(0,a.jsxs)(R.Cf,{className:"max-w-md","data-sentry-element":"DialogContent","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsxs)(R.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(R.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"bill-status-manager.tsx",children:"更新账单状态"}),(0,a.jsxs)(R.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"bill-status-manager.tsx",children:["账单编号: ",t.billNumber]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{className:"text-sm font-medium","data-sentry-element":"Label","data-sentry-source-file":"bill-status-manager.tsx",children:"当前状态"}),(0,a.jsxs)("div",{className:"mt-1",children:[(0,a.jsxs)(l.E,{className:b.color,"data-sentry-element":"Badge","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(b.icon,{className:"h-3 w-3 mr-1","data-sentry-element":"currentStatusConfig.icon","data-sentry-source-file":"bill-status-manager.tsx"}),b.label]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:b.description})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{htmlFor:"status-select",className:"text-sm font-medium","data-sentry-element":"Label","data-sentry-source-file":"bill-status-manager.tsx",children:"新状态"}),(0,a.jsxs)(W.l6,{value:u,onValueChange:e=>x(e),"data-sentry-element":"Select","data-sentry-source-file":"bill-status-manager.tsx",children:[(0,a.jsx)(W.bq,{className:"mt-1","data-sentry-element":"SelectTrigger","data-sentry-source-file":"bill-status-manager.tsx",children:(0,a.jsx)(W.yv,{placeholder:"选择新状态","data-sentry-element":"SelectValue","data-sentry-source-file":"bill-status-manager.tsx"})}),(0,a.jsx)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"bill-status-manager.tsx",children:v.map(e=>{let t=ew[e],s=t.icon;return(0,a.jsx)(W.eb,{value:e,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.label})]})},e)})})]})]}),u!==t.status&&T(u)&&(0,a.jsxs)(ey.Fc,{children:[(0,a.jsx)(ev.A,{className:"h-4 w-4"}),(0,a.jsx)(ey.TN,{children:T(u)})]}),"paid"===u&&(t.remainingAmount||0)>0&&(0,a.jsxs)(ey.Fc,{variant:"destructive",children:[(0,a.jsx)(ev.A,{className:"h-4 w-4"}),(0,a.jsxs)(ey.TN,{children:["账单还有 ",S.formatCurrency(t.remainingAmount||0)," 未支付， 无法标记为已支付状态。"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{htmlFor:"notes",className:"text-sm font-medium","data-sentry-element":"Label","data-sentry-source-file":"bill-status-manager.tsx",children:"备注 (可选)"}),(0,a.jsx)($.T,{id:"notes",placeholder:"添加状态更新的备注...",value:p,onChange:e=>y(e.target.value),className:"mt-1 resize-none",rows:3,"data-sentry-element":"Textarea","data-sentry-source-file":"bill-status-manager.tsx"})]}),(0,a.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,a.jsx)(i.$,{onClick:C,disabled:g||u===t.status||"paid"===u&&(t.remainingAmount||0)>0,className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"bill-status-manager.tsx",children:g?"更新中...":"确认更新"}),(0,a.jsx)(i.$,{variant:"outline",onClick:()=>{m(!1),x(t.status),y("")},disabled:g,"data-sentry-element":"Button","data-sentry-source-file":"bill-status-manager.tsx",children:"取消"})]})]})]})]}):(0,a.jsxs)(l.E,{className:b.color,children:[(0,a.jsx)(b.icon,{className:"h-3 w-3 mr-1"}),b.label]})}var eI=s(34901),eT=s(60664),eA=s(29118),eS=s(42486);let eR=[{value:"draft",label:"草稿"},{value:"sent",label:"已发送"},{value:"confirmed",label:"已确认"},{value:"paid",label:"已支付"},{value:"cancelled",label:"已取消"}],eE=[{value:"treatment",label:"治疗账单"},{value:"consultation",label:"咨询账单"},{value:"deposit",label:"押金账单"},{value:"additional",label:"补充账单"}];function eD(e){var t,s,r;let{filters:d,onFiltersChange:o,patients:c=[],className:m}=e,[u,x]=(0,n.useState)(!1),[h,p]=(0,n.useState)(d),f=Object.values(d).some(e=>void 0!==e&&""!==e&&null!==e),y=Object.values(d).filter(e=>void 0!==e&&""!==e&&null!==e).length,g=(e,t)=>{p({...h,[e]:t})},j=()=>{let e={};p(e),o(e),x(!1)},b=e=>{let t={...d};delete t[e],o(t)};return(0,a.jsxs)("div",{className:"space-y-3 ".concat(m),"data-sentry-component":"BillFilters","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(eT.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","data-sentry-element":"IconSearch","data-sentry-source-file":"bill-filters.tsx"}),(0,a.jsx)(U.p,{placeholder:"搜索账单编号、患者姓名或描述...",value:d.search||"",onChange:e=>o({...d,search:e.target.value}),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]}),(0,a.jsxs)(eI.AM,{open:u,onOpenChange:x,"data-sentry-element":"Popover","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eI.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsxs)(i.$,{variant:"outline",className:"relative","data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eA.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"bill-filters.tsx"}),"高级筛选",y>0&&(0,a.jsx)(l.E,{variant:"secondary",className:"ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs",children:y})]})}),(0,a.jsx)(eI.hl,{className:"w-80 p-4",align:"end","data-sentry-element":"PopoverContent","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium",children:"高级筛选"}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>x(!1),"data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsx)(et.A,{className:"h-4 w-4","data-sentry-element":"IconX","data-sentry-source-file":"bill-filters.tsx"})})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-filters.tsx"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(ec.J,{className:"flex items-center gap-2","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eg.A,{className:"h-4 w-4","data-sentry-element":"IconFileText","data-sentry-source-file":"bill-filters.tsx"}),"账单状态"]}),(0,a.jsxs)(W.l6,{value:h.status||"",onValueChange:e=>g("status",e||void 0),"data-sentry-element":"Select","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsx)(W.yv,{placeholder:"选择状态","data-sentry-element":"SelectValue","data-sentry-source-file":"bill-filters.tsx"})}),(0,a.jsxs)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.eb,{value:"","data-sentry-element":"SelectItem","data-sentry-source-file":"bill-filters.tsx",children:"全部状态"}),eR.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(ec.J,{"data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"账单类型"}),(0,a.jsxs)(W.l6,{value:h.billType||"",onValueChange:e=>g("billType",e||void 0),"data-sentry-element":"Select","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.bq,{"data-sentry-element":"SelectTrigger","data-sentry-source-file":"bill-filters.tsx",children:(0,a.jsx)(W.yv,{placeholder:"选择类型","data-sentry-element":"SelectValue","data-sentry-source-file":"bill-filters.tsx"})}),(0,a.jsxs)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(W.eb,{value:"","data-sentry-element":"SelectItem","data-sentry-source-file":"bill-filters.tsx",children:"全部类型"}),eE.map(e=>(0,a.jsx)(W.eb,{value:e.value,children:e.label},e.value))]})]})]}),c.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(ec.J,{className:"flex items-center gap-2",children:[(0,a.jsx)(em.A,{className:"h-4 w-4"}),"患者"]}),(0,a.jsxs)(W.l6,{value:h.patientId||"",onValueChange:e=>g("patientId",e||void 0),children:[(0,a.jsx)(W.bq,{children:(0,a.jsx)(W.yv,{placeholder:"选择患者"})}),(0,a.jsxs)(W.gC,{children:[(0,a.jsx)(W.eb,{value:"",children:"全部患者"}),c.map(e=>(0,a.jsxs)(W.eb,{value:e.id,children:[e.fullName," - ",e.phone]},e.id))]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(ec.J,{className:"flex items-center gap-2","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"bill-filters.tsx"}),"日期范围"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"开始日期"}),(0,a.jsx)(U.p,{type:"date",value:h.dateFrom||"",onChange:e=>g("dateFrom",e.target.value||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"结束日期"}),(0,a.jsx)(U.p,{type:"date",value:h.dateTo||"",onChange:e=>g("dateTo",e.target.value||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(ec.J,{className:"flex items-center gap-2","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:[(0,a.jsx)(eS.A,{className:"h-4 w-4","data-sentry-element":"IconCurrencyYuan","data-sentry-source-file":"bill-filters.tsx"}),"金额范围"]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"最小金额"}),(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0",placeholder:"0.00",value:h.amountMin||"",onChange:e=>g("amountMin",parseFloat(e.target.value)||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(ec.J,{className:"text-xs text-muted-foreground","data-sentry-element":"Label","data-sentry-source-file":"bill-filters.tsx",children:"最大金额"}),(0,a.jsx)(U.p,{type:"number",step:"0.01",min:"0",placeholder:"无限制",value:h.amountMax||"",onChange:e=>g("amountMax",parseFloat(e.target.value)||void 0),className:"text-sm","data-sentry-element":"Input","data-sentry-source-file":"bill-filters.tsx"})]})]})]}),(0,a.jsx)(Y.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"bill-filters.tsx"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.$,{onClick:()=>{o(h),x(!1)},className:"flex-1","data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:"应用筛选"}),(0,a.jsx)(i.$,{variant:"outline",onClick:j,"data-sentry-element":"Button","data-sentry-source-file":"bill-filters.tsx",children:"清除"})]})]})})]})]}),f&&(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[d.status&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["状态: ",null==(t=eR.find(e=>e.value===d.status))?void 0:t.label,(0,a.jsx)("button",{onClick:()=>b("status"),className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(et.A,{className:"h-3 w-3"})})]}),d.billType&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["类型: ",null==(s=eE.find(e=>e.value===d.billType))?void 0:s.label,(0,a.jsx)("button",{onClick:()=>b("billType"),className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(et.A,{className:"h-3 w-3"})})]}),d.patientId&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["患者: ",null==(r=c.find(e=>e.id===d.patientId))?void 0:r.fullName,(0,a.jsx)("button",{onClick:()=>b("patientId"),className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(et.A,{className:"h-3 w-3"})})]}),(d.dateFrom||d.dateTo)&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["日期: ",d.dateFrom||"开始"," ~ ",d.dateTo||"结束",(0,a.jsx)("button",{onClick:()=>{b("dateFrom"),b("dateTo")},className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(et.A,{className:"h-3 w-3"})})]}),(void 0!==d.amountMin||void 0!==d.amountMax)&&(0,a.jsxs)(l.E,{variant:"secondary",className:"flex items-center gap-1",children:["金额: ",d.amountMin?S.formatCurrency(d.amountMin):"0"," ~ ",d.amountMax?S.formatCurrency(d.amountMax):"∞",(0,a.jsx)("button",{onClick:()=>{b("amountMin"),b("amountMax")},className:"ml-1 hover:bg-muted rounded-full p-0.5",children:(0,a.jsx)(et.A,{className:"h-3 w-3"})})]}),f&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:j,className:"h-6 px-2",children:"清除全部"})]})]})}let eF=e=>{let{status:t}=e;return(0,a.jsx)(l.E,{className:(e=>{switch(e.toLowerCase()){case"paid":return"bg-green-100 text-green-800 hover:bg-green-200";case"pending":return"bg-yellow-100 text-yellow-800 hover:bg-yellow-200";case"partial":return"bg-blue-100 text-blue-800 hover:bg-blue-200";case"overdue":return"bg-red-100 text-red-800 hover:bg-red-200";default:return"bg-gray-100 text-gray-800 hover:bg-gray-200"}})(t),"data-sentry-element":"Badge","data-sentry-component":"StatusBadge","data-sentry-source-file":"billing-list.tsx",children:(e=>{switch(e.toLowerCase()){case"paid":return"已支付";case"pending":return"待支付";case"partial":return"部分支付";case"overdue":return"逾期";case"cancelled":return"已取消";default:return e}})(t)})},eB=e=>{let{type:t}=e;return(0,a.jsx)(l.E,{variant:"outline",className:(e=>{switch(e.toLowerCase()){case"treatment":return"bg-purple-100 text-purple-800 hover:bg-purple-200";case"consultation":return"bg-orange-100 text-orange-800 hover:bg-orange-200";case"deposit":return"bg-cyan-100 text-cyan-800 hover:bg-cyan-200";case"additional":return"bg-pink-100 text-pink-800 hover:bg-pink-200";default:return"bg-gray-100 text-gray-800 hover:bg-gray-200"}})(t),"data-sentry-element":"Badge","data-sentry-component":"BillTypeBadge","data-sentry-source-file":"billing-list.tsx",children:(e=>{switch(e.toLowerCase()){case"treatment":return"治疗";case"consultation":return"咨询";case"deposit":return"押金";case"additional":return"附加";default:return e}})(t)})};function eP(){let{hasPermission:e}=(0,d.It)(),[t,s]=(0,n.useState)([]),[r,l]=(0,n.useState)(!0),[y,g]=(0,n.useState)(null),[j,b]=(0,n.useState)({}),[v,N]=(0,n.useState)(1),[C,T]=(0,n.useState)(1),[A,R]=(0,n.useState)(!1),[E,D]=(0,n.useState)([]),[F,B]=(0,n.useState)(null),[P,k]=(0,n.useState)(!1),[M,_]=(0,n.useState)(null),[O,L]=(0,n.useState)(!1),[z,V]=(0,n.useState)(!1),q=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{l(1===e),g(null);let a=await I.fetchBills({page:e,limit:10,search:t.search||void 0,status:t.status||void 0,patientId:t.patientId||void 0,dateFrom:t.dateFrom||void 0,dateTo:t.dateTo||void 0});s(a.docs),N(a.page),T(a.totalPages)}catch(t){console.error("Failed to fetch bills:",t);let e=t instanceof w?t.message:"加载账单失败，请稍后重试。";g(e),f.toast.error(e)}finally{l(!1),R(!1)}},J=async()=>{try{let e=await fetch("/api/patients"),t=await e.json();D(t.docs||[])}catch(e){console.error("Failed to fetch patients:",e)}};(0,n.useEffect)(()=>{q(1,j),J()},[]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{q(1,j)},500);return()=>clearTimeout(e)},[j]);let U=async()=>{R(!0),await q(v,j)},$=()=>{_(null),V(!0),L(!0)},W=e=>{f.toast.info("查看账单: ".concat(e.billNumber))},H=e=>{_(e),V(!1),L(!0)},Z=e=>{B(e),k(!0)},Y=e=>{s(t=>t.map(t=>t.id===e.id?e:t)),f.toast.success("账单状态已更新")},G=e=>{q(e,j)};return r?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载账单中..."})]})}):y&&0===t.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-600 mb-2",children:"加载失败"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:y}),(0,a.jsxs)(i.$,{onClick:U,variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"重试"]})]})}):0===t.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"暂无账单"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"开始创建您的第一个账单。"}),(0,a.jsx)(d.Bk,{permission:"canCreateBills",children:(0,a.jsxs)(i.$,{onClick:$,children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"新建账单"]})})]})}):(0,a.jsxs)("div",{className:"space-y-4","data-sentry-component":"BillingList","data-sentry-source-file":"billing-list.tsx",children:[(0,a.jsx)(eD,{filters:j,onFiltersChange:e=>{b(e),N(1)},patients:E,"data-sentry-element":"BillFilters","data-sentry-source-file":"billing-list.tsx"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:U,disabled:A,"data-sentry-element":"Button","data-sentry-source-file":"billing-list.tsx",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2 ".concat(A?"animate-spin":""),"data-sentry-element":"IconRefresh","data-sentry-source-file":"billing-list.tsx"}),"刷新"]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["共 ",t.length," 个账单"]})]}),(0,a.jsx)(d.Bk,{permission:"canCreateBills","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-list.tsx",children:(0,a.jsxs)(i.$,{onClick:$,"data-sentry-element":"Button","data-sentry-source-file":"billing-list.tsx",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconPlus","data-sentry-source-file":"billing-list.tsx"}),"新建账单"]})})]}),y&&t.length>0&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:y})]})}),(0,a.jsx)("div",{className:"grid gap-4",children:t.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h4",{className:"font-medium text-lg",children:e.billNumber}),(0,a.jsx)(eB,{type:e.billType}),(0,a.jsx)(eC,{bill:e,onStatusUpdate:Y,trigger:(0,a.jsx)(eF,{status:e.status})})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[(0,a.jsxs)("p",{children:["患者: ","object"==typeof e.patient?e.patient.fullName:"未知患者"]}),(0,a.jsxs)("p",{children:["描述: ",e.description]}),(0,a.jsxs)("p",{children:["开票日期: ",new Date(e.issueDate).toLocaleDateString("zh-CN")]})]})]}),(0,a.jsxs)("div",{className:"text-right space-y-1",children:[(0,a.jsx)("div",{className:"text-lg font-semibold",children:S.formatCurrency(e.totalAmount)}),(e.remainingAmount||0)>0&&(0,a.jsxs)("div",{className:"text-sm text-red-600",children:["待收: ",S.formatCurrency(e.remainingAmount||0)]}),(e.paidAmount||0)>0&&(0,a.jsxs)("div",{className:"text-sm text-green-600",children:["已收: ",S.formatCurrency(e.paidAmount||0)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-2 border-t",children:[(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["到期日期: ",new Date(e.dueDate).toLocaleDateString("zh-CN")]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>W(e),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})}),(0,a.jsx)(d.Bk,{permission:"canEditBills",children:(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>H(e),children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})}),(e.remainingAmount||0)>0&&(0,a.jsx)(d.Bk,{permission:"canProcessPayments",children:(0,a.jsxs)(i.$,{variant:"default",size:"sm",onClick:()=>Z(e),children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"收款"]})})]})]})]},e.id))}),C>1&&(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 pt-4",children:[(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>G(v-1),disabled:v<=1,children:"上一页"}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["第 ",v," 页，共 ",C," 页"]}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>G(v+1),disabled:v>=C,children:"下一页"})]}),(0,a.jsx)(eo,{bill:F,isOpen:P,onClose:()=>{k(!1),B(null)},onSuccess:e=>{f.toast.success("支付处理成功！收据编号: ".concat(e.receiptNumber||"待生成")),q(v,j)},"data-sentry-element":"PaymentDialog","data-sentry-source-file":"billing-list.tsx"}),(0,a.jsx)(ef,{bill:M,isOpen:O,onClose:()=>{L(!1),_(null),V(!1)},onSuccess:e=>{f.toast.success("账单".concat(z?"创建":"更新","成功！账单编号: ").concat(e.billNumber)),q(v,j)},"data-sentry-element":"BillDialog","data-sentry-source-file":"billing-list.tsx"})]})}var ek=s(47889);function eM(e){let{onBillGenerated:t,className:s}=e,{hasPermission:r}=(0,d.It)(),[o,u]=(0,n.useState)([]),[x,h]=(0,n.useState)(!0),[p,f]=(0,n.useState)(null),[y,g]=(0,n.useState)("treatment"),j=async()=>{try{h(!0);let e=await fetch("/api/appointments?status=completed&limit=50"),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch appointments");let s=(await I.fetchBills({limit:100})).docs,a=t.docs.map(e=>{let t=s.find(t=>{var s;return"object"==typeof t.appointment&&(null==(s=t.appointment)?void 0:s.id)===e.id});return{...e,hasBill:!!t,billId:null==t?void 0:t.id,billNumber:null==t?void 0:t.billNumber}});u(a)}catch(e){console.error("Failed to fetch appointments:",e),L.system.dataRefreshError()}finally{h(!1)}};if((0,n.useEffect)(()=>{j()},[]),!r("canCreateBills"))return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(ev.A,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"权限不足"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"您没有权限从预约生成账单"})]})});let b=async e=>{try{f(e.id);let s=await I.generateFromAppointment(e.id,y);L.bill.generateFromAppointment(s,new Date(e.appointmentDate).toLocaleDateString("zh-CN")),u(t=>t.map(t=>t.id===e.id?{...t,hasBill:!0,billId:s.id,billNumber:s.billNumber}:t)),t&&t(s)}catch(t){console.error("Failed to generate bill:",t);let e=t instanceof w?t.message:void 0;L.bill.createError(e)}finally{f(null)}},v=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"confirmed":return"bg-blue-100 text-blue-800";case"scheduled":return"bg-yellow-100 text-yellow-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},N=e=>({scheduled:"已预约",confirmed:"已确认",completed:"已完成",cancelled:"已取消","no-show":"未到诊"})[e]||e;if(x)return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载已完成预约中..."})]})});let C=o.filter(e=>!e.hasBill);return(0,a.jsxs)("div",{className:"space-y-6 ".concat(s),"data-sentry-component":"AppointmentToBill","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"size-6","data-sentry-element":"IconReceipt","data-sentry-source-file":"appointment-to-bill.tsx"}),"预约生成账单"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"从已完成的预约自动生成账单"})]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:j,disabled:x,"data-sentry-element":"Button","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2","data-sentry-element":"IconRefresh","data-sentry-source-file":"appointment-to-bill.tsx"}),"刷新"]})]}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(H.ZB,{className:"text-lg","data-sentry-element":"CardTitle","data-sentry-source-file":"appointment-to-bill.tsx",children:"账单类型设置"}),(0,a.jsx)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"appointment-to-bill.tsx",children:"选择生成账单的默认类型"})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)(W.l6,{value:y,onValueChange:g,"data-sentry-element":"Select","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(W.bq,{className:"w-64","data-sentry-element":"SelectTrigger","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(W.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"appointment-to-bill.tsx"})}),(0,a.jsxs)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(W.eb,{value:"treatment","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-to-bill.tsx",children:"治疗账单"}),(0,a.jsx)(W.eb,{value:"consultation","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-to-bill.tsx",children:"咨询账单"}),(0,a.jsx)(W.eb,{value:"additional","data-sentry-element":"SelectItem","data-sentry-source-file":"appointment-to-bill.tsx",children:"补充账单"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(H.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:o.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"已完成预约"})]})})}),(0,a.jsx)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(H.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.filter(e=>e.hasBill).length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"已生成账单"})]})})}),(0,a.jsx)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsx)(H.Wu,{className:"pt-6","data-sentry-element":"CardContent","data-sentry-source-file":"appointment-to-bill.tsx",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:C.length}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"待生成账单"})]})})})]}),0===C.length?(0,a.jsx)(H.Zp,{children:(0,a.jsx)(H.Wu,{className:"py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(eb.A,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"全部预约已生成账单"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"所有已完成的预约都已生成对应的账单"})]})})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"待生成账单的预约"}),(0,a.jsx)("div",{className:"grid gap-4",children:C.map(e=>(0,a.jsx)(H.Zp,{className:"hover:shadow-md transition-shadow",children:(0,a.jsx)(H.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-3 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(l.E,{className:v(e.status),children:N(e.status)}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["预约ID: ",e.id]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:new Date(e.appointmentDate).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(em.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"object"==typeof e.patient?e.patient.fullName:"未知患者"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ek.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:"object"==typeof e.treatment?e.treatment.name:"未知治疗"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(eS.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{children:S.formatCurrency(e.price||0)})]})]})]}),(0,a.jsx)("div",{className:"ml-4",children:(0,a.jsx)(i.$,{onClick:()=>b(e),disabled:p===e.id,className:"min-w-24",children:p===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"生成中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"生成账单"]})})})]})})},e.id))})]}),(0,a.jsxs)(ey.Fc,{"data-sentry-element":"Alert","data-sentry-source-file":"appointment-to-bill.tsx",children:[(0,a.jsx)(ev.A,{className:"h-4 w-4","data-sentry-element":"IconAlertTriangle","data-sentry-source-file":"appointment-to-bill.tsx"}),(0,a.jsx)(ey.TN,{"data-sentry-element":"AlertDescription","data-sentry-source-file":"appointment-to-bill.tsx",children:"生成的账单将包含预约的治疗项目、价格和患者信息。您可以在账单列表中进一步编辑账单详情。"})]})]})}function e_(e){let{className:t}=e,{hasPermission:s}=(0,d.It)(),[r,u]=(0,n.useState)([]),[h,p]=(0,n.useState)(!0),[y,g]=(0,n.useState)(null),[j,b]=(0,n.useState)(""),[v,N]=(0,n.useState)(null),[C,I]=(0,n.useState)(!1),[A,R]=(0,n.useState)(!1),E=async e=>{try{p(!0),g(null);let t=(await T.fetchPayments({limit:50,status:"completed"})).docs.filter(e=>e.receiptNumber);e&&(t=t.filter(t=>{var s,a,n;return(null==(s=t.receiptNumber)?void 0:s.toLowerCase().includes(e.toLowerCase()))||"object"==typeof t.bill&&(null==(a=t.bill.billNumber)?void 0:a.toLowerCase().includes(e.toLowerCase()))||"object"==typeof t.patient&&(null==(n=t.patient.fullName)?void 0:n.toLowerCase().includes(e.toLowerCase()))})),u(t)}catch(t){console.error("Failed to fetch payments:",t);let e=t instanceof w?t.message:"加载收据失败，请稍后重试。";g(e),f.toast.error(e)}finally{p(!1),R(!1)}};(0,n.useEffect)(()=>{E()},[]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{void 0!==j&&E(j)},500);return()=>clearTimeout(e)},[j]);let D=async()=>{R(!0),await E(j)},F=e=>{N(e),I(!0)},B=e=>{N(e),I(!0)},P=r.filter(e=>{var t;return(null==(t=e.receiptNumber)?void 0:t.toLowerCase().includes(j.toLowerCase()))||e.paymentNumber.toLowerCase().includes(j.toLowerCase())||"object"==typeof e.patient&&e.patient.fullName.toLowerCase().includes(j.toLowerCase())});return h?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载收据中..."})]})}):y&&0===r.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-600 mb-2",children:"加载失败"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:y}),(0,a.jsxs)(i.$,{onClick:D,variant:"outline",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"重试"]})]})}):(0,a.jsxs)("div",{className:"space-y-4 ".concat(t),"data-sentry-component":"ReceiptManager","data-sentry-source-file":"receipt-manager.tsx",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"size-6","data-sentry-element":"IconReceipt","data-sentry-source-file":"receipt-manager.tsx"}),"收据管理"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"查看、打印和管理支付收据"})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-1 max-w-md",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(eT.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4","data-sentry-element":"IconSearch","data-sentry-source-file":"receipt-manager.tsx"}),(0,a.jsx)(U.p,{placeholder:"搜索收据编号、支付编号或患者姓名...",value:j,onChange:e=>b(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"receipt-manager.tsx"})]}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:D,disabled:A,"data-sentry-element":"Button","data-sentry-source-file":"receipt-manager.tsx",children:(0,a.jsx)(c.A,{className:"h-4 w-4 ".concat(A?"animate-spin":""),"data-sentry-element":"IconRefresh","data-sentry-source-file":"receipt-manager.tsx"})})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:["共 ",P.length," 张收据"]})]}),y&&r.length>0&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:y})]})}),0===P.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"暂无收据"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:j?"没有找到匹配的收据":"还没有生成任何收据"})]})}):(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:P.map(e=>(0,a.jsxs)(H.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,a.jsx)(H.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(H.ZB,{className:"text-sm font-medium",children:e.receiptNumber}),(0,a.jsxs)(H.BT,{className:"text-xs",children:["支付编号: ",e.paymentNumber]})]}),(0,a.jsx)(l.E,{variant:"completed"===e.paymentStatus?"default":"secondary",children:S.getPaymentStatusName(e.paymentStatus)})]})}),(0,a.jsxs)(H.Wu,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"患者:"}),(0,a.jsx)("span",{children:"object"==typeof e.patient?e.patient.fullName:"未知患者"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付方式:"}),(0,a.jsx)("span",{children:S.getPaymentMethodName(e.paymentMethod)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"支付日期:"}),(0,a.jsx)("span",{children:new Date(e.paymentDate).toLocaleDateString("zh-CN")})]}),(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"金额:"}),(0,a.jsx)("span",{className:"text-green-600",children:S.formatCurrency(e.amount)})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pt-2 border-t",children:[(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>F(e),className:"flex-1",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-1"}),"查看"]}),(0,a.jsx)(d.Bk,{permission:"canGenerateReceipts",children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>B(e),children:(0,a.jsx)(en.A,{className:"h-4 w-4"})})})]})]})]},e.id))}),(0,a.jsx)(el,{payment:v,isOpen:C,onClose:()=>{I(!1),N(null)},"data-sentry-element":"ReceiptDialog","data-sentry-source-file":"receipt-manager.tsx"})]})}var eO=s(26151),eL=s(7964);function ez(){let{hasPermission:e}=(0,d.It)(),[t,s]=(0,n.useState)({dailyRevenue:null,monthlyRevenue:null,outstandingBalances:null}),[r,o]=(0,n.useState)(!0),[m,u]=(0,n.useState)(null),[x,h]=(0,n.useState)(new Date().toISOString().split("T")[0]),[y,g]=(0,n.useState)({year:new Date().getFullYear(),month:new Date().getMonth()+1}),[j,b]=(0,n.useState)(!1),v=async()=>{try{o(!0),u(null);let[e,t,a]=await Promise.all([A.getDailyRevenue(x).catch(()=>null),A.getMonthlyRevenue(y.year,y.month).catch(()=>null),A.getOutstandingBalances().catch(()=>null)]);s({dailyRevenue:e,monthlyRevenue:t,outstandingBalances:a})}catch(t){console.error("Failed to fetch financial metrics:",t);let e=t instanceof w?t.message:"加载财务数据失败，请稍后重试";u(e),f.toast.error(e)}finally{o(!1),b(!1)}};if((0,n.useEffect)(()=>{v()},[x,y]),!e("canViewDetailedFinancials"))return(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(ev.A,{className:"h-12 w-12 text-yellow-500 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"权限不足"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"您没有权限查看详细的财务报表"})]})});let N=e=>{switch(e){case"cash":default:return G.A;case"card":return p.A;case"wechat":case"alipay":return X.A;case"transfer":return Q.A}};return r?(0,a.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"加载财务数据中..."})]})}):(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"FinancialDashboard","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,a.jsx)(eO.A,{className:"size-6","data-sentry-element":"IconChartBar","data-sentry-source-file":"financial-dashboard.tsx"}),"财务报表"]}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"查看收入统计、支付分析和应收账款"})]}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:()=>{b(!0),v()},disabled:j,"data-sentry-element":"Button","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2 ".concat(j?"animate-spin":""),"data-sentry-element":"IconRefresh","data-sentry-source-file":"financial-dashboard.tsx"}),"刷新数据"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(ee.A,{className:"h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"financial-dashboard.tsx"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"日期选择:"}),(0,a.jsx)("input",{type:"date",value:x,onChange:e=>h(e.target.value),className:"px-3 py-1 border rounded-md text-sm"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:"月份选择:"}),(0,a.jsxs)(W.l6,{value:"".concat(y.year,"-").concat(y.month),onValueChange:e=>{let[t,s]=e.split("-").map(Number);g({year:t,month:s})},"data-sentry-element":"Select","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(W.bq,{className:"w-40","data-sentry-element":"SelectTrigger","data-sentry-source-file":"financial-dashboard.tsx",children:(0,a.jsx)(W.yv,{"data-sentry-element":"SelectValue","data-sentry-source-file":"financial-dashboard.tsx"})}),(0,a.jsx)(W.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"financial-dashboard.tsx",children:Array.from({length:12},(e,t)=>{let s=t+1,n=new Date().getFullYear();return(0,a.jsxs)(W.eb,{value:"".concat(n,"-").concat(s),children:[n,"年",s,"月"]},"".concat(n,"-").concat(s))})})]})]})]}),m&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(ev.A,{className:"h-4 w-4 text-red-500 mr-2"}),(0,a.jsx)("span",{className:"text-red-700 text-sm",children:m})]})}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(eL.A,{className:"h-5 w-5","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"financial-dashboard.tsx"}),"日收入统计"]}),(0,a.jsxs)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"financial-dashboard.tsx",children:[new Date(x).toLocaleDateString("zh-CN")," 的收入详情"]})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"financial-dashboard.tsx",children:t.dailyRevenue?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:S.formatCurrency(t.dailyRevenue.totalRevenue)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"总收入"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.dailyRevenue.paymentCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"支付笔数"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.dailyRevenue.paymentCount>0?S.formatCurrency(t.dailyRevenue.totalRevenue/t.dailyRevenue.paymentCount):S.formatCurrency(0)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"平均金额"})]})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"支付方式分布"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:Object.entries(t.dailyRevenue.paymentMethods).map(e=>{let[t,s]=e,n=N(t);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:S.getPaymentMethodName(t)})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"text-sm font-semibold",children:S.formatCurrency(s.amount)}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[s.count," 笔"]})]})]},t)})})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"暂无当日收入数据"})})]}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(eO.A,{className:"h-5 w-5","data-sentry-element":"IconChartBar","data-sentry-source-file":"financial-dashboard.tsx"}),"月度收入统计"]}),(0,a.jsxs)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"financial-dashboard.tsx",children:[y.year,"年",y.month,"月的收入趋势"]})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"financial-dashboard.tsx",children:t.monthlyRevenue?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-green-600",children:S.formatCurrency(t.monthlyRevenue.totalRevenue)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"月度总收入"})]}),(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"每日收入明细"}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:t.monthlyRevenue.dailyBreakdown.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-2 hover:bg-muted/50 rounded",children:[(0,a.jsx)("span",{className:"text-sm",children:new Date(e.date).toLocaleDateString("zh-CN")}),(0,a.jsx)("span",{className:"font-medium",children:S.formatCurrency(e.revenue)})]},e.date))})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"暂无月度收入数据"})})]}),(0,a.jsxs)(H.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsxs)(H.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"financial-dashboard.tsx",children:[(0,a.jsx)(ev.A,{className:"h-5 w-5","data-sentry-element":"IconAlertTriangle","data-sentry-source-file":"financial-dashboard.tsx"}),"应收账款"]}),(0,a.jsx)(H.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"financial-dashboard.tsx",children:"待收款项和逾期账单统计"})]}),(0,a.jsx)(H.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"financial-dashboard.tsx",children:t.outstandingBalances?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:S.formatCurrency(t.outstandingBalances.totalOutstanding)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"总待收金额"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:S.formatCurrency(t.outstandingBalances.overdueAmount)}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"逾期金额"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:t.outstandingBalances.billsCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"待收账单"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:t.outstandingBalances.overdueBillsCount}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"逾期账单"})]})]}),t.outstandingBalances.bills.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.Separator,{}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium mb-3",children:"逾期账单详情"}),(0,a.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:t.outstandingBalances.bills.filter(e=>e.daysOverdue>0).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:e.billNumber}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.patient})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-semibold text-red-600",children:S.formatCurrency(e.amount)}),(0,a.jsxs)(l.E,{variant:"destructive",className:"text-xs",children:["逾期 ",e.daysOverdue," 天"]})]})]},e.id))})]})]})]}):(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"暂无应收账款数据"})})]})]})}var eV=s(95108);function eq(e){let{defaultTab:t="bills",className:s}=e,{hasPermission:l}=(0,d.It)(),[i,o]=(0,n.useState)(t);return(0,a.jsx)("div",{className:s,"data-sentry-component":"BillingTabs","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(r.tU,{value:i,onValueChange:o,className:"space-y-6","data-sentry-element":"Tabs","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsxs)(r.j7,{className:"grid w-full grid-cols-2 lg:grid-cols-4","data-sentry-element":"TabsList","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsxs)(r.Xi,{value:"bills",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(m.A,{className:"h-4 w-4","data-sentry-element":"IconReceipt","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"账单管理"}),(0,a.jsx)("span",{className:"sm:hidden",children:"账单"})]}),(0,a.jsx)(d.Bk,{permission:"canCreateBills","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(r.Xi,{value:"generate",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(eV.A,{className:"h-4 w-4","data-sentry-element":"IconCalendarEvent","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"预约生成"}),(0,a.jsx)("span",{className:"sm:hidden",children:"生成"})]})}),(0,a.jsx)(d.Bk,{permission:"canGenerateReceipts","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(r.Xi,{value:"receipts",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(eg.A,{className:"h-4 w-4","data-sentry-element":"IconFileText","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"收据管理"}),(0,a.jsx)("span",{className:"sm:hidden",children:"收据"})]})}),(0,a.jsx)(d.Bk,{permission:"canViewDetailedFinancials","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsxs)(r.Xi,{value:"reports",className:"flex items-center gap-2","data-sentry-element":"TabsTrigger","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsx)(eO.A,{className:"h-4 w-4","data-sentry-element":"IconChartBar","data-sentry-source-file":"billing-tabs.tsx"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"财务报表"}),(0,a.jsx)("span",{className:"sm:hidden",children:"报表"})]})})]}),(0,a.jsxs)(r.av,{value:"bills",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold tracking-tight mb-2",children:"账单管理"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6",children:"管理所有账单，处理支付和查看账单状态"})]}),(0,a.jsx)(eP,{"data-sentry-element":"BillingList","data-sentry-source-file":"billing-tabs.tsx"})]}),(0,a.jsx)(d.Bk,{permission:"canCreateBills","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(r.av,{value:"generate",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(eM,{onBillGenerated:e=>{f.toast.success("账单生成成功！账单编号: ".concat(e.billNumber)),o("bills")},"data-sentry-element":"AppointmentToBill","data-sentry-source-file":"billing-tabs.tsx"})})}),(0,a.jsx)(d.Bk,{permission:"canGenerateReceipts","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(r.av,{value:"receipts",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(e_,{"data-sentry-element":"ReceiptManager","data-sentry-source-file":"billing-tabs.tsx"})})}),(0,a.jsx)(d.Bk,{permission:"canViewDetailedFinancials","data-sentry-element":"PermissionGate","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(r.av,{value:"reports",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"billing-tabs.tsx",children:(0,a.jsx)(ez,{"data-sentry-element":"FinancialDashboard","data-sentry-source-file":"billing-tabs.tsx"})})})]})})}},60485:(e,t,s)=>{Promise.resolve().then(s.bind(s,57437)),Promise.resolve().then(s.bind(s,90917))},86540:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,X9:()=>o,ZB:()=>i,Zp:()=>r,aR:()=>l,wL:()=>m});var a=s(52880);s(99004);var n=s(54651);function r(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...s,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...s,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...s,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...s,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...s,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6677,7905,1359,4089,4629,7131,2090,3530,2350,290,3485,229,6821,9442,4579,9253,7358],()=>t(60485)),_N_E=e.O()}]);