{"version": 3, "file": "../app/dashboard/billing/page.js", "mappings": "gcEmBI,sBAAsB,0JFlBX,eAAqB,WAAW,SAAW,eAAe,CAAC,CAAC,OAAO,CAAC,EAAI,qGAAsG,KAAM,CAAO,OAAC,CAAC,CAAC,aCK9L,eAAeA,IAC5B,GAAM,IADsBA,GAAAA,CAE1BC,CAAM,CACP,CAAG,MAAMC,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,QACV,EAGOC,CAAAA,CAHH,CAGGA,CAHM,CAGNA,GAAAA,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAcC,qBAAoB,iBAAgBC,uBAAsB,eAAcC,yBAAwB,YAClH,SAAAC,CAAAA,EAAAA,EAAAA,IAAAA,CAACC,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,4CAEbP,CAAAA,EAAAA,EAAAA,GAAAA,CAACM,CAAAA,KAAAA,CAAAA,CAAIC,SAAU,qCACb,SAAAF,CAAAA,EAAAA,EAAAA,IAAAA,CAACC,CAAAA,KAAAA,CAAAA,WACCD,CAAAA,EAAAA,EAAAA,IAAAA,CAACG,CAAAA,IAAAA,CAAAA,CAAGD,SAAU,uEACZP,CAAAA,EAAAA,EAAAA,GAAAA,CAACS,CAAAA,EAAAA,CAAYF,QAAZE,CAAsB,UAASP,qBAAoB,eAAcE,yBAAwB,cAAa,UAGzGJ,CAAAA,EAAAA,EAAAA,GAAAA,CAACU,CAAAA,GAAAA,CAAAA,CAAEH,SAAU,yBAAwB,oCAOzCP,CAAAA,EAAAA,EAAAA,GAAAA,CAACW,CAAAA,EAAAA,WAAAA,CAAAA,CAAYT,qBAAoB,eAAcE,yBAAwB,mBAlBpEQ,CAAAA,EAAAA,EAAAA,QAAAA,CAAS,iBAqBpB,CCxBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZC,EAO8B,CAClD,KAAK,CAAE,CADa,CAP6B,CAQvB,EAAS,CADa,GACT,CACrC,IAD0C,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,GChF9B,6GCAA,oDCAA,qGCAA,mECAA,uYCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,qCAAmI,CAoBvJ,kGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,qGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,+BACA,8BAEA,cACA,YACA,WACA,CAAK,CACL,UACA,YACA,CACA,CAAC,0BC5FD,oDCAA,0CCAA,uCAAwK,CAExK,uCAAiK,yBCFjK,oDCAA,kDCAA,gDCAA,wGCAA,gECAA,iDCAA,kECAA,4gBCAA,8ECCA,MAAe,eAAqB,UAAW,SAAW,eAAe,CAAC,CAAC,OAAO,CAAC,EAAI,0CAA2C,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,wCAAwC,CAAM,UAAQ,EAAC,CAAC,mBCDvN,uCAAwK,CAExK,uCAAiK,yBCFjK,sDCAA,wDCAA,4ECCA,CAAe,mBAAqB,WAAW,cAAgB,mBAAmB,CAAC,CAAC,OAAO,CAAC,EAAI,sCAAuC,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,SAAU,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,CAAO,OAAC,CAAC,CAAC,yBCDxO,uECAA,oDCAA,kECAA,yDCAA,kMCMA,SAASC,EAAO,CACd,GAAGC,EAC+C,EAClD,MAAO,UAACC,EAAAA,EAAoB,EAACC,YAAU,SAAU,GAAGF,CAAK,CAAEb,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASc,EAAc,CACrB,GAAGH,EACkD,EACrD,MAAO,UAACC,EAAAA,EAAuB,EAACC,YAAU,iBAAkB,GAAGF,CAAK,CAAEb,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACpL,CACA,SAASe,EAAa,CACpB,GAAGJ,EACiD,EACpD,MAAO,UAACC,EAAAA,EAAsB,EAACC,YAAU,gBAAiB,GAAGF,CAAK,CAAEb,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,cAChL,CAMA,SAASgB,EAAc,WACrBb,CAAS,CACT,GAAGQ,EACkD,EACrD,MAAO,UAACC,EAAAA,EAAuB,EAACC,YAAU,iBAAiBV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0Jd,GAAa,GAAGQ,CAAK,CAAEb,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACxW,CACA,SAASkB,EAAc,WACrBf,CAAS,UACTgB,CAAQ,CACR,GAAGR,EACkD,EACrD,MAAO,WAACI,EAAAA,CAAaF,YAAU,gBAAgBf,sBAAoB,eAAeC,wBAAsB,gBAAgBC,0BAAwB,uBAC5I,UAACgB,EAAAA,CAAclB,sBAAoB,gBAAgBE,0BAAwB,eAC3E,WAACY,EAAAA,EAAuB,EAACC,YAAU,iBAAiBV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+Wd,GAAa,GAAGQ,CAAK,CAAEb,sBAAoB,0BAA0BE,0BAAwB,uBAC3gBmB,EACD,WAACP,EAAAA,EAAqB,EAACT,UAAU,oWAAoWL,sBAAoB,wBAAwBE,0BAAwB,uBACvc,UAACoB,EAAAA,CAAKA,CAAAA,CAACtB,sBAAoB,QAAQE,0BAAwB,eAC3D,UAACqB,OAAAA,CAAKlB,UAAU,mBAAU,kBAIpC,CACA,SAASmB,EAAa,WACpBnB,CAAS,CACT,GAAGQ,EACyB,EAC5B,MAAO,UAACT,MAAAA,CAAIW,YAAU,gBAAgBV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgDd,GAAa,GAAGQ,CAAK,CAAEZ,wBAAsB,eAAeC,0BAAwB,cAC1L,CACA,SAASuB,EAAa,WACpBpB,CAAS,CACT,GAAGQ,EACyB,EAC5B,MAAO,UAACT,MAAAA,CAAIW,YAAU,gBAAgBV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0Dd,GAAa,GAAGQ,CAAK,CAAEZ,wBAAsB,eAAeC,0BAAwB,cACpM,CACA,SAASwB,EAAY,WACnBrB,CAAS,CACT,GAAGQ,EACgD,EACnD,MAAO,UAACC,EAAAA,EAAqB,EAACC,YAAU,eAAeV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCd,GAAa,GAAGQ,CAAK,CAAEb,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5O,CACA,SAASyB,EAAkB,WACzBtB,CAAS,CACT,GAAGQ,EACsD,EACzD,MAAO,UAACC,EAAAA,EAA2B,EAACC,YAAU,qBAAqBV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCd,GAAa,GAAGQ,CAAK,CAAEb,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,cAC/P,0BCvEA,uMCKA,SAAS0B,EAAW,WAClBvB,CAAS,UACTgB,CAAQ,CACR,GAAGR,EACmD,EACtD,MAAO,WAACgB,EAAAA,EAAwB,EAACd,YAAU,cAAcV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYd,GAAa,GAAGQ,CAAK,CAAEb,sBAAoB,2BAA2BC,wBAAsB,aAAaC,0BAAwB,4BAChN,UAAC2B,EAAAA,EAA4B,EAACd,YAAU,uBAAuBV,UAAU,qJAAqJL,sBAAoB,+BAA+BE,0BAAwB,2BACtSmB,IAEH,UAACS,EAAAA,CAAU9B,sBAAoB,YAAYE,0BAAwB,oBACnE,UAAC2B,EAAAA,EAA0B,EAAC7B,sBAAoB,6BAA6BE,0BAAwB,sBAE3G,CACA,SAAS4B,EAAU,WACjBzB,CAAS,aACT0B,EAAc,UAAU,CACxB,GAAGlB,EACkE,EACrE,MAAO,UAACgB,EAAAA,EAAuC,EAACd,YAAU,wBAAwBgB,YAAaA,EAAa1B,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsE,aAAhBY,GAA8B,6CAA8D,eAAhBA,GAAgC,+CAAgD1B,GAAa,GAAGQ,CAAK,CAAEb,sBAAoB,0CAA0CC,wBAAsB,YAAYC,0BAAwB,2BACvd,UAAC2B,EAAAA,EAAmC,EAACd,YAAU,oBAAoBV,UAAU,yCAAyCL,sBAAoB,sCAAsCE,0BAAwB,qBAE9M,yTE1BA,qDCAA,2DCAA,4LtCCe,SAAqB,WAAW,SAAW,eAAe,CAAC,CAAC,OAAO,CAAC,EAAI,qGAAsG,KAAM,CAAO,OAAC,CAAC,CAAC,yDuCuB7M,IAAM8B,EAA8F,CAElGC,cAAe,CAAEC,QAAS,YAAaC,SAAU,OAAQ,EACzDC,mBAAoB,CAAEF,QAAS,kBAAmBC,SAAU,OAAQ,EACpEE,uBAAwB,CAAEH,QAAS,iBAAkBC,SAAU,OAAQ,EACvEG,mBAAoB,CAAEJ,QAAS,kBAAmBC,SAAU,OAAQ,EAGpEI,iBAAkB,CAAEL,QAAS,mBAAoBC,SAAU,OAAQ,EACnEK,aAAc,CAAEN,QAAS,iBAAkBC,SAAU,OAAQ,EAC7DM,cAAe,CAAEP,QAAS,SAAUC,SAAU,SAAU,EACxDO,aAAc,CAAER,QAAS,SAAUC,SAAU,SAAU,EACvDQ,kBAAmB,CAAET,QAAS,iBAAkBC,SAAU,OAAQ,EAGlES,uBAAwB,CAAEV,QAAS,SAAUC,SAAU,OAAQ,EAC/DU,eAAgB,CAAEX,QAAS,aAAcC,SAAU,OAAQ,EAC3DW,qBAAsB,CAAEZ,QAAS,WAAYC,SAAU,OAAQ,EAC/DY,yBAA0B,CAAEb,QAAS,eAAgBC,SAAU,OAAQ,EAGvEa,cAAe,CAAEd,QAAS,SAAUC,SAAU,OAAQ,EACtDc,sBAAuB,CAAEf,QAAS,kBAAmBC,SAAU,OAAQ,EACvEe,eAAgB,CAAEhB,QAAS,gBAAiBC,SAAU,OAAQ,EAC9DgB,cAAe,CAAEjB,QAAS,iBAAkBC,SAAU,OAAQ,EAG9DiB,oBAAqB,CAAElB,QAAS,eAAgBC,SAAU,OAAQ,EAClEkB,oBAAqB,CAAEnB,QAAS,iBAAkBC,SAAU,OAAQ,EACpEmB,gBAAiB,CAAEpB,QAAS,cAAeC,SAAU,OAAQ,EAC7DoB,uBAAwB,CAAErB,QAAS,SAAUC,SAAU,OAAQ,EAC/DqB,oBAAqB,CAAEtB,QAAS,aAAcC,SAAU,OAAQ,EAGhEsB,sBAAuB,CAAEvB,QAAS,eAAgBC,SAAU,OAAQ,EACpEuB,cAAe,CAAExB,QAAS,iBAAkBC,SAAU,OAAQ,EAC9DwB,aAAc,CAAEzB,QAAS,iBAAkBC,SAAU,OAAQ,EAC7DyB,oBAAqB,CAAE1B,QAAS,SAAUC,SAAU,OAAQ,EAC5D0B,oBAAqB,CAAE3B,QAAS,aAAcC,SAAU,OAAQ,EAChE2B,sBAAuB,CAAE5B,QAAS,cAAeC,SAAU,OAAQ,EACnE4B,mBAAoB,CAAE7B,QAAS,cAAeC,SAAU,OAAQ,EAChE6B,0BAA2B,CAAE9B,QAAS,eAAgBC,SAAU,OAAQ,EAGxE8B,gBAAiB,CAAE/B,QAAS,QAASC,SAAU,OAAQ,EACvD+B,6BAA8B,CAAEhC,QAAS,SAAUC,SAAU,OAAQ,EACrEgC,8BAA+B,CAAEjC,QAAS,aAAcC,SAAU,OAAQ,EAC1EiC,kBAAmB,CAAElC,QAAS,YAAaC,SAAU,OAAQ,EAG7DkC,oBAAqB,CAAEnC,QAAS,gBAAiBC,SAAU,OAAQ,EACnEmC,uBAAwB,CAAEpC,QAAS,eAAgBC,SAAU,OAAQ,EAGrEoC,eAAgB,CAAErC,QAAS,eAAgBC,SAAU,OAAQ,EAC7DqC,cAAe,CAAEtC,QAAS,iBAAkBC,SAAU,OAAQ,CAChE,CAGO,OAAMsC,EAIX,aAAsB,MAFdC,QAAAA,CAA2B,EAAE,CAIrC,OAAOC,aAAmC,CAIxC,OAHI,EAAqBC,QAAQ,EAAE,CACjCH,EAAoBG,QAAQ,CAAG,IAAIH,CAAAA,EAE9BA,EAAoBG,QAAQ,CAMrCC,eAAeC,CAAU,CAAEC,EAA+B,CAAC,CAAC,CAAgB,CAC1E,IAOIC,EAPE,WACJC,GAAY,CAAI,UAChBC,GAAW,CAAI,SACfC,EAAU,KAAK,iBACfC,EAAkB,YAAY,CAC/B,CAAGL,EAIJ,GAAID,GAAOO,MAAQrD,CAAc,CAAC8C,EAAMO,IAAI,CAAC,CAAE,CAC7C,IAAMC,EAAYtD,CAAc,CAAC8C,EAAMO,IAAI,CAAC,CAC5CL,EAAe,CACbK,KAAMP,EAAMO,IAAI,CAChBnD,QAAS4C,EAAM5C,OAAO,EAAIoD,EAAUpD,OAAO,CAC3CqD,YAAaT,EAAM5C,OAAO,EAAIoD,EAAUpD,OAAO,CAC/CC,SAAUmD,EAAUnD,QAAQ,CAC5BqD,QAASV,EAAMU,OAAO,CACtBC,UAAW,IAAIC,KACfP,SACF,CACF,MAEEH,CAFK,CAEU,CACbK,KAAM,gBACNnD,QAAS4C,GAAO5C,SAAW,yBAC3BqD,YAAaH,EACbjD,SAAU,QACVqD,QAASV,EACTW,UAAW,IAAIC,aACfP,CACF,EAWF,OARID,GACF,IAAI,CAACA,EADO,MACC,CAACF,GAGZC,GACF,IAAI,CAACU,GADQ,WACM,CAACX,GAGfA,CACT,CAKAY,mBAAmBd,CAAU,CAAEC,EAA+B,CAAC,CAAC,CAAgB,CAC9E,IAAMc,EAA6B,CACjCR,KAAM,gBACNnD,QAAS4C,GAAO5C,SAAW,yBAC3BqD,YAAa,oBACbpD,SAAU,QACVqD,QAASV,EACTW,UAAW,IAAIC,KACfP,QAASJ,EAAQI,OAAO,EAAI,SAC9B,EAUA,OARyB,IAArBJ,EAAQG,CAAoB,OAAZ,EAClB,IAAI,CAACA,QAAQ,CAACW,GAGZd,CAAsB,MAAdE,CAAqB,QAAZ,EACnB,IAAI,CAACU,cAAc,CAACE,GAGfA,CACT,CAKAC,sBAAsBC,CAAuB,CAAEhB,EAA+B,CAAC,CAAC,CAAgB,CAC9F,IAAMiB,EAAaD,CAAgB,CAAC,EAAE,CAChCE,EAAgC,CACpCZ,KAAM,mBACNnD,QAAS,oBACTqD,YAAaS,GAAY9D,SAAW,iBACpCC,SAAU,QACVqD,QAASO,EACTN,UAAW,IAAIC,KACfP,QAASJ,EAAQI,OAAO,EAAI,YAC9B,EAUA,MARyB,KAArBJ,EAAQG,QAAQ,EAClB,IAAI,CAACA,QAAQ,CAACe,IAGU,IAAtBlB,EAAQE,CAAqB,QAAZ,EACnB,IAAI,CAACU,cAAc,CAACM,GAGfA,CACT,CAKAC,YAAYhE,CAAe,CAAEiE,CAAoB,CAAQ,CACvDC,EAAAA,KAAKA,CAACC,OAAO,CAACnE,EAAS,aACrBiE,EACAG,SAAU,GACZ,EACF,CAKAC,YAAYrE,CAAe,CAAEiE,CAAoB,CAAQ,CACvDC,EAAAA,KAAKA,CAACI,OAAO,CAACtE,EAAS,aACrBiE,EACAG,SAAU,GACZ,EACF,CAKAG,SAASvE,CAAe,CAAEiE,CAAoB,CAAQ,CACpDC,EAAAA,KAAKA,CAACM,IAAI,CAACxE,EAAS,aAClBiE,EACAG,SAAU,GACZ,EACF,CAKA,SAAiBxB,CAAmB,CAAQ,CAC1C6B,QAAQ7B,KAAK,CAAC,CAAC,CAAC,EAAEA,EAAMK,OAAO,CAAC,EAAE,EAAEL,EAAMO,IAAI,CAAC,EAAE,EAAEP,EAAM5C,OAAO,EAAE,CAAE,CAClEqD,YAAaT,EAAMS,WAAW,CAC9BC,QAASV,EAAMU,OAAO,CACtBC,UAAWX,EAAMW,SAAS,GAI5B,IAAI,CAACf,QAAQ,CAACkC,IAAI,CAAC9B,GACf,IAAI,CAACJ,QAAQ,CAACmC,MAAM,CAAG,KACzB,IAAI,CAACnC,QAAQ,CAACoC,KAAK,EAEvB,CAKA,eAAuBhC,CAAmB,CAAQ,CAChD,IAAMiC,EAAe,CACnBT,SAA6B,UAAnBxB,EAAM3C,QAAQ,CAAe,IAAO,GAChD,EAEA,OAAQ2C,EAAM3C,QAAQ,EACpB,IAAK,QACHiE,EAAAA,KAAKA,CAACtB,KAAK,CAACA,EAAMS,WAAW,CAAEwB,GAC/B,KACF,KAAK,UACHX,EAAAA,KAAKA,CAACI,OAAO,CAAC1B,EAAMS,WAAW,CAAEwB,GACjC,KACF,KAAK,OACHX,EAAAA,KAAKA,CAACM,IAAI,CAAC5B,EAAMS,WAAW,CAAEwB,EAElC,CACF,CAKAC,aAA8B,CAC5B,MAAO,IAAI,IAAI,CAACtC,QAAQ,CAAC,CAM3BuC,eAAsB,CACpB,IAAI,CAACvC,QAAQ,CAAG,EAAE,CAEtB,CAGO,IAAMwC,EAAsBzC,EAAoBE,WAAW,GAAG,EAEvC,CAACG,EAAYC,IACzCmC,EAAoBrC,cAAc,CAACC,EAAOC,GAE/Ba,EAAqB,CAACd,EAAYC,CAFM,GAGnDmC,EAAoBtB,kBAAkB,CAACd,EAAOC,GA8BnCoC,EAAmB,IA9ByB,EA+BvDC,EACAC,EAAqB,CAAC,CACtBC,EAAoB,GAAI,CACxBnC,KAEA,IAAIoC,EAEJ,IAAK,IAAIC,EAAU,EAAGA,GAAWH,EAAYG,IAC3C,GAAI,CACF,EAFoD,KAE7C,MAAMJ,GACf,CAAE,MAAOtC,EAAO,CAGd,GAFAyC,EAAYzC,EAER0C,IAAYH,EAKd,MAJAxC,EAAeC,EADW,CAExBK,QAASA,GAAW,QACpBC,gBAAiB,CAAC,QAAQ,EAAEiC,EAAW,CAAC,CAAC,GAErCvC,EAIR,IAAM2C,EAAQH,EAAYI,KAAKC,GAAG,CAAC,EAAGH,EAAU,EAChD,OAAM,IAAII,QAAQC,GAAWC,WAAWD,EAASJ,GACnD,CAGF,MAAMF,CACR,CCzUO,CDyUL,MCzUWQ,UAAwBC,MACnCC,YACE/F,CAAe,CACf,CAAsB,CACtB,CAAoB,CACpB,CAAoB,CACpB,CACA,KAAK,CAACA,GAAAA,IAAAA,CAJCgG,MAAAA,CAAAA,EAAAA,IAAAA,CACA7C,IAAAA,CAAAA,EAAAA,IAAAA,CACAG,OAAAA,CAAAA,EAGP,IAAI,CAAC2C,IAAI,CAAG,iBACd,CACF,CAGA,eAAeC,EACbC,CAAgB,CAChBtD,EAAuB,CAAC,CAAC,CACzBuD,CAAwD,EAExD,IAAMC,EAAM,GAAGC,IAAWH,GAAU,CAM9BI,EAAsB,CAC1B,GAAG1D,CAAO,CACV2D,QAAS,CALT,eAAgB,mBAOd,GAAG3D,EAAQ2D,OAAO,CAEtB,EAEMC,EAAc,UAClB,GAAI,CACF,IAAMC,EAAW,MAAMC,MAAMN,EAAKE,GAElC,GAAI,CAACG,EAASE,EAAE,CAAE,KACZC,EACJ,GAAI,CACFA,EAAY,MAAMH,EAASI,IAAI,EACjC,CAAE,KAAM,CACND,EAAY,CACVjE,MAAO,CAAC,KAAK,EAAE8D,EAASV,MAAM,CAAC,EAAE,EAAEU,EAASK,UAAU,EAAE,CACxD5D,KAAM,CAAC,KAAK,EAAEuD,EAASV,MAAM,EAAE,CAC/BhG,QAAS0G,EAASK,UAAU,CAEhC,CAEA,IAAMC,EAAW,IAAInB,EACnBgB,EAAUjE,KAAK,EAAIiE,EAAU7G,OAAO,EAAI,CAAC,KAAK,EAAE0G,EAASV,MAAM,CAAC,EAAE,EAAEU,EAASK,UAAU,EAAE,CACzFL,EAASV,MAAM,CACfa,EAAU1D,IAAI,EAAI,CAAC,KAAK,EAAEuD,EAASV,MAAM,EAAE,CAC3Ca,EAAUvD,OAAO,CASnB,OALAX,EAAekE,EAAW,CACxB5D,QAASmD,CADGzD,EACWM,SAAW,cAClCF,WAAW,CACb,GAEMiE,CACR,CAJqB,OAMd,MAAMN,EAASI,IAAI,EAC5B,CAAE,MAAOlE,EAAO,CACd,GAAIA,aAAiBiD,EACnB,MAAMjD,EAIR,IAAMe,EAAe,CALiB,GAKbkC,EACvBjD,aAAiBkD,MAAQlD,EAAM5C,OAAO,CAAG,yBACzC,EACA,gBACA4C,EAQF,OALAc,EAAmBd,EAAO,CACxBK,QAASmD,GAAcnD,EADPS,OACkB,cAClCX,UAAW,EACb,GAEMY,CACR,CACF,QAKA,CAFqB,CAACd,EAAQoE,MAAM,EAAI,CAAC,MAAO,OAAQ,UAAU,CAACC,QAAQ,CAACrE,EAAQoE,MAAM,CAACE,WAAW,MAElFf,GAAcjB,WACzBF,CADqC,CAE1CwB,EACAL,EAAajB,UAAU,CACvB,IACAiB,EAAanD,OAAO,EAIjBwD,GACT,CAGO,IAAMW,EAAW,CAItB,MAAMC,WAAWC,CAQhB,EACC,IAAMC,EAAe,IAAIC,gBAErBF,GAAQG,MAAMF,EAAaG,MAAM,CAAC,OAAQJ,EAAOG,IAAI,CAACE,QAAQ,IAC9DL,GAAQM,OAAOL,EAAaG,MAAM,CAAC,QAASJ,EAAOM,KAAK,CAACD,QAAQ,IACjEL,GAAQO,QAAQN,EAAaG,MAAM,CAAC,SAAUJ,EAAOO,MAAM,EAC3DP,GAAQtB,QAAQuB,EAAaG,MAAM,CAAC,SAAUJ,EAAOtB,MAAM,EAC3DsB,GAAQQ,WAAWP,EAAaG,MAAM,CAAC,UAAWJ,EAAOQ,SAAS,EAClER,GAAQS,UAAUR,EAAaG,MAAM,CAAC,WAAYJ,EAAOS,QAAQ,EACjET,GAAQU,QAAQT,EAAaG,MAAM,CAAC,SAAUJ,EAAOU,MAAM,EAE/D,IAAMC,EAAcV,EAAaI,QAAQ,GAGzC,OAAOzB,EAFU,CAAC,MAAM,EAGtBC,EAHsC,CAAC,CAAC,EAAE8B,EAAAA,CAAa,CAAG,IAAI,CAI9D,CAAC,EACD,CAAE9C,WAAY,EAAGlC,QAAS,aAAc,EAE5C,EAKMiF,UAAN,MAAgBC,EAAU,CACjBjC,EAAiB,CAAC,OAAO,EAAEiC,EAAAA,CAAI,EAMlCC,WAAN,MAAiBC,GAqBRnC,EAAiB,GADzB,MACmC,CAChCe,OAAQ,OACRqB,KAAMC,KAAKC,SAAS,CAACH,EACvB,GAMII,WAAN,MAAiBN,EAAU,IAClBjC,EAAiB,CAAC,GAD2B,IACpB,EAAEiC,EAAAA,CAAI,CAAE,CACtClB,OAAQ,QACRqB,KAAMC,KAAKC,SAAS,CAACE,EACvB,GAMIC,WAAN,MAAiBR,EAAU,CAClBjC,EAAiB,CAAC,OAAO,EAAEiC,EAAAA,CAAI,CAAE,CACtClB,OAAQ,QACV,GAMI2B,wBAAN,MAA8BC,EAAuBC,EAAmB,SAArB,EAAgC,GAC1E5C,EAAiB,mCAAoC,CAC1De,OAAQ,OACRqB,KAAMC,KAAKC,SAAS,CAAC,eAAEK,WAAeC,CAAS,EACjD,GAMF,MAAMC,qBAAqBF,CAAqB,EAC9C,GAAI,CAOF,IAAMG,EAAOtC,CANI,MAAMU,EAASC,UAAU,CAAC,CACzCO,MAAO,CAGT,IAEsBqB,IAAI,CAACC,IAAI,CAACF,GACF,UAA5B,OAAOA,EAAKG,WAAW,EAAiBH,EAAKG,WAAW,EAAEhB,KAAOU,GAGnE,MAAO,CACLO,QAAS,CAAC,CAACJ,EACXA,KAAMA,QAAQK,CAChB,CACF,CAAE,MAAOzG,EAAO,CAEd,OADA6B,QAAQ7B,KAAK,CAAC,oCAAqCA,GAC5C,CAAEwG,SAAS,CAAM,CAC1B,CACF,CACF,EAAE,EAGyB,CAIzB,MAAME,cAAchC,CASnB,EACC,IAAMC,EAAe,IAAIC,gBAErBF,GAAQG,MAAMF,EAAaG,MAAM,CAAC,OAAQJ,EAAOG,IAAI,CAACE,QAAQ,IAC9DL,GAAQM,OAAOL,EAAaG,MAAM,CAAC,QAASJ,EAAOM,KAAK,CAACD,QAAQ,IACjEL,GAAQiC,QAAQhC,EAAaG,MAAM,CAAC,OAAQJ,EAAOiC,MAAM,EACzDjC,GAAQQ,WAAWP,EAAaG,MAAM,CAAC,UAAWJ,EAAOQ,SAAS,EAClER,GAAQkC,eAAejC,EAAaG,MAAM,CAAC,gBAAiBJ,EAAOkC,aAAa,EAChFlC,GAAQtB,QAAQuB,EAAaG,MAAM,CAAC,gBAAiBJ,EAAOtB,MAAM,EAClEsB,GAAQS,UAAUR,EAAaG,MAAM,CAAC,WAAYJ,EAAOS,QAAQ,EACjET,GAAQU,QAAQT,EAAaG,MAAM,CAAC,SAAUJ,EAAOU,MAAM,EAE/D,IAAMC,EAAcV,EAAaI,QAAQ,GAGzC,OAAOzB,EAFU,CAAC,QAE0BC,CAFjB,EAAE8B,EAAc,CAAC,CAAC,EAAEA,EAAAA,CAAa,CAAG,IAAI,CAGrE,EAKMwB,aAAN,MAAmBtB,EAAU,CACpBjC,EAAoB,CAAC,UAAU,EAAEiC,EAAAA,CAAI,EAMxCuB,eAAN,MAAqBC,GAQZzD,EAAoB,MAD5B,MACyC,CACtCe,OAAQ,OACRqB,KAAMC,KAAKC,SAAS,CAACmB,EACvB,GAMIC,cAAN,MAAoBzB,EAAU,IACrBjC,EAAoB,CAAC,GAD8B,OACpB,EAAEiC,EAAAA,CAAI,CAAE,CAC5ClB,OAAQ,QACRqB,KAAMC,KAAKC,SAAS,CAACE,EACvB,GAMImB,cAAN,MAAoBC,EAAmBC,IAK9B7D,EAAoB,CALQ,GAIpC,OACuC,EAAE4D,EAAU,OAAO,CAAC,CAAE,CAC1D7C,OAAQ,OACRqB,KAAMC,KAAKC,SAAS,CAACuB,EACvB,EAEJ,EAAE,EAGwB,CAIlBC,gBAAN,MAAsBC,GAMb/D,CANyB,CAMd,CAAC,4BAA4B,EAAE+D,EAAAA,CAAM,EAMnDC,kBAAN,MAAwBC,EAAcC,EAAF,EAM3BlE,CAN0C,CAM/B,CAAC,8BAA8B,EAAEiE,EAAK,OAAO,EAAEC,EAAAA,CAAO,EAMpEC,uBAAN,SAcSnE,EAAW,iCAMpB,MAAMoE,mBAAmBhD,CAIxB,EACC,IAAMC,EAAe,IAAIC,gBAKzB,OAJIF,GAAQiD,WAAWhD,EAAaiD,GAAG,CAAC,YAAalD,EAAOiD,SAAS,EACjEjD,GAAQmD,SAASlD,EAAaiD,GAAG,CAAC,UAAWlD,EAAOmD,OAAO,EAC3DnD,GAAQoD,MAAMnD,EAAaiD,GAAG,CAAC,OAAQlD,EAAOoD,IAAI,EAE/CxE,EAAW,CAAC,mBAAmB,EAAEqB,EAAaI,QAAQ,IAAI,CACnE,CACF,EAAE,EAsH0B,CAI1BgD,eAAeC,GACN,GADoB,CAChBC,KAAKC,YAAY,CAAC,QAAS,CACpCC,MAAO,WACPC,SAAU,KACZ,GAAGC,MAAM,CAACL,wBAaZM,GAUSC,CATiC,CACtCC,CAF+B,IAEzB,KACNC,KAAM,MACNC,OAAQ,OACRC,OAAQ,MACRC,SAAU,OACVC,QAAS,OACTC,YAAa,OACf,CACc,CAACzE,EAAO,EAAIA,oBAM5B0E,GAQSC,CAPkC,CACvCC,CAF4B,KAErB,KACPC,KAAM,MACNC,UAAW,MACXC,KAAM,MACNC,UAAW,MACb,CACe,CAACjG,EAAO,EAAIA,uBAM7BkG,GAOSN,CANkC,CACvCO,CAF+B,OAEtB,MACTC,UAAW,MACXC,OAAQ,KACRC,SAAU,MACZ,CACe,CAACtG,EAAO,EAAIA,CAsB/B,EAAE,gDCjkBqBuG,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,WACzC,IAAMC,EAAiBF,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,WACnCE,EAAiBH,EAAAA,EAAQ,GAKzBI,EAAmB,GACvB,EAAIC,GAAQ,GAEU,CAACA,EAAMjF,IAFP,IAEe,GAAGkF,KAAK,CAAC,IAAI,CAAC,EAAE,EAAI,GAAC,CAAGlI,MAAM,EAC3C,EAkBbmI,EAAiBP,EAAAA,EAAQ,CAAC,CACrCQ,SAAUR,EAAAA,EAAM,CAAC,CAAC,YAAa,eAAgB,WAAY,UAAU,CAAE,CACrES,eAAgB,UAChBC,mBAAoB,SACtB,GACAC,SAAUT,EAAeU,GAAG,CAAC,IAAK,kBAClClJ,YAAayI,EAAeS,GAAG,CAAC,IAAK,gBAAgBC,QAAQ,GAC7DC,SAAUd,EAAAA,EAAQ,GACfC,GAAG,CAAC,IAAM,WACVW,GAAG,CAAC,KAAM,cACVG,MAAM,CAAC,GACgB,CAACC,EAAI5F,QAAQ,GAAGkF,KAAK,CAAC,IAAI,CAAC,EAAE,EAAI,GAAC,CAAGlI,MAAM,EACzC,EACvB,cACL6I,UAAWjB,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,WACPW,GAAG,CAAC,UAAW,oBACfG,MAAM,CAACX,EAAkB,mBAC5Bc,aAAclB,EAAAA,EAAQ,GACnBC,GAAG,CAAC,EAAG,YACPW,GAAG,CAAC,IAAK,eACTC,QAAQ,EACb,GAAGE,MAAM,CAAC,IAEJI,EAAKD,YAAY,IAAIC,EAAKD,YAAY,EAAG,GAAwB,GAAG,CAAtBC,EAAKF,SAAS,CAI/D,CACDxN,QAAS,cACT2N,KAAM,CAAC,eAAe,GAIXC,EAAiBrB,EAAAA,EAAQ,CAAC,CACrCsB,QAASpB,EAAeqB,IAAI,CAAC,YAC7B3E,YAAauD,EAAeoB,IAAI,CAAC,YAAYV,QAAQ,GAAGW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KACrEyB,UAAWtB,EAAeoB,IAAI,CAAC,cAAcV,QAAQ,GAAGW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KACrEzD,SAAUyD,EAAAA,EAAM,CAAC,CAAC,YAAa,eAAgB,UAAW,aAAa,CAAE,CACvES,eAAgB,UAChBC,mBAAoB,SACtB,GACAhJ,YAAawI,EACVD,GAAG,CAAC,EAAG,gBACPW,GAAG,CAAC,IAAK,kBACZc,MAAOvB,EAAeS,GAAG,CAAC,IAAM,iBAAiBC,QAAQ,GACzDc,QAAS3B,EAAAA,EAAQ,GACdC,GAAG,CAAC,EAAG,WACPc,MAAM,CAAC,IACN,GAAI,CAEF,OAAO,CACT,CAAE,KAAM,CACN,OAAO,CACT,CACF,EAAG,YACFA,MAAM,CAvEmB,IAC5B,IAAMa,EAAY,IAAI3K,KAAKyG,GACrBmE,EAAQ,IAAI5K,KAElB,OADA4K,EAAMC,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBF,GAAaC,CACtB,EAkEmC,gBAC9Bd,MAAM,CAjE2B,IACpC,IAAMa,EAAY,IAAI3K,KAAKyG,GACrBqE,EAAU,IAAI9K,KAEpB,OADA8K,EAAQC,WAAW,CAACD,EAAQE,WAAW,GAAK,GACrCL,CADyC,EAC5BG,CACtB,EA4D2C,cACzCG,KA/DwE,UA+DxDlC,EAAAA,EAAQ,GACrBC,GAAG,CAAC,EAAG,aACPW,GAAG,CAAC,UAAW,sBACfG,MAAM,CAACX,EAAkB,YACzBS,QAAQ,GACXsB,UAAWnC,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,aACPW,GAAG,CAAC,UAAW,sBACfG,MAAM,CAACX,EAAkB,YACzBS,QAAQ,GACXuB,MAAOpC,EAAAA,EAAO,CAACO,GACZN,GAAG,CAAC,EAAG,cACPW,GAAG,CAAC,GAAI,cACb,GAAGG,MAAM,CAAC,IAER,IAAMsB,EAAalB,EAAKiB,KAAK,CAACE,MAAM,CAAC,CAACC,EAAKC,KACzC,IAAMC,EAAYD,EAAK1B,QAAQ,CAAG0B,EAAKvB,SAAS,CAC1CyB,EAAeD,EAAa,EAACD,EAAKtB,MAAN,MAAkB,GAAI,EAAK,IAAE,CAC/D,OAAOqB,EAAOE,GAAYC,CAAZD,CAAuB,EACpC,GAEGP,EAAiBf,EAAKe,cAAc,EAAI,EAI9C,OAFmBG,EADDlB,GAAKgB,QACSA,CADA,GAAI,EACQD,GAEvB,CACvB,EAAG,CACDzO,QAAS,aACT2N,KAAM,CAAC,iBAAiB,GACvBL,MAAM,CAAC,IAER,IAAMsB,EAAalB,EAAKiB,KAAK,CAACE,MAAM,CAAC,CAACC,EAAKC,KACzC,IAAMC,EAAYD,EAAK1B,QAAQ,CAAG0B,EAAKvB,SAAS,CAC1CyB,EAAeD,EAAa,IAAMvB,MAAN,MAAkB,GAAI,EAAK,IAAE,CAC/D,OAAOqB,GAAOE,EAAYC,CAAAA,CAAW,EACpC,GAGH,MAAOR,CADgBf,EAAKe,cAAc,GAAI,GACrBG,CAC3B,EAAG,CACD5O,QAAS,eACT2N,KAAM,CAAC,iBAAiB,GACvB,EAG8BpB,EAAAA,EAAQ,CAAC,CACxC3B,OAAQ2B,EAAAA,EAAQ,GACbC,GAAG,CAAC,IAAM,aACVW,GAAG,CAAC,UAAW,sBACfG,MAAM,CAACX,EAAkB,qBAC5BnD,cAAe+C,EAAAA,EAAM,CAAC,CAAC,OAAQ,OAAQ,SAAU,SAAU,WAAY,cAAc,CAAE,CACrFS,eAAgB,UAChBC,mBAAoB,SACtB,GACAiC,cAAe3C,EAAAA,EAAQ,GACpBY,GAAG,CAAC,IAAK,kBACTC,QAAQ,GACRW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KAChB0B,MAAOvB,EAAeS,GAAG,CAAC,IAAK,gBAAgBC,QAAQ,EACzD,GAAGE,MAAM,CAAC,GAGR,CAAI6B,CADmC,OAAQ,SAAU,SAAU,WAAW,CAC5CjI,QAAQ,CAACwG,EAAKlE,aAAa,GAAG,EAClD0F,aAAa,EAAIxB,EAAKwB,aAAa,CAACE,IAAI,GAAGzK,MAAM,CAAG,EAGjE,CACD3E,QAAS,gBACT2N,KAAM,CAAC,gBAAgB,GACtB,EAG8BpB,EAAQ,CAAC,CACxC8C,SAAU5C,EACPD,GAAG,CAAC,EAAG,cACPW,GAAG,CAAC,GAAI,eACRmC,KAAK,CAAC,6BAA8B,kBACvCC,MAAO9C,EACJ6C,KAAK,CAACE,gBAAY,cACrBC,MAAOlD,EAAAA,EAAQ,GACZkD,KAAK,CAAC,cACNtC,GAAG,CAAC,IAAK,kBACTC,QAAQ,GACRW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KAChBmD,aAAchD,EAAeS,GAAG,CAAC,IAAM,mBAAmBC,QAAQ,EACpE,GAAG,EAGmCb,EAAQ,CAAC,CAC7CvG,OAAQuG,EAAAA,EAAM,CAAC,CAAC,QAAS,OAAQ,YAAa,OAAQ,YAAY,CAAE,CAClES,eAAgB,UAChBC,mBAAoB,SACtB,GACAgB,MAAOvB,EAAeS,GAAG,CAAC,IAAK,oBAAoBC,QAAQ,EAC7D,GAAG,EAG6Bb,EAAQ,CAAC,CACvC1E,OAAQ6E,EAAeS,GAAG,CAAC,IAAK,mBAAmBC,QAAQ,GAC3DpH,OAAQuG,EAAAA,EAAM,CAAC,CAAC,QAAS,OAAQ,YAAa,OAAQ,YAAY,EAAEa,QAAQ,GAC5EtE,SAAUyD,EAAAA,EAAM,CAAC,CAAC,YAAa,eAAgB,UAAW,aAAa,EAAEa,QAAQ,GACjFtF,UAAW4E,EAAeoB,IAAI,CAAC,YAAYV,QAAQ,GAAGW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KACnExE,SAAUwE,EAAAA,EAAQ,GACfa,QAAQ,GACRE,MAAM,CAAC,IACN,GAAI,CAACrD,EAAM,MAAO,GAClB,GAAI,CAEF,OAAO,CACT,CAAE,KAAM,CACN,OAAO,CACT,CACF,EAAG,cACLjC,OAAQuE,EAAAA,EAAQ,GACba,QAAQ,GACRE,MAAM,CAAC,IACN,GAAI,CAACrD,EAAM,OAAO,EAClB,GAAI,CAEF,OAAO,CACT,CAAE,KAAM,CACN,OAAO,CACT,CACF,EAAG,cACL0F,UAAWpD,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,aACPW,GAAG,CAAC,UAAW,sBACfC,QAAQ,GACXwC,UAAWrD,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,aACPW,GAAG,CAAC,UAAW,sBACfC,QAAQ,EACb,GAAGE,MAAM,CAAC,GAER,CAAII,EAAK3F,QAAQ,GAAI2F,EAAK1F,MAAM,EAAE,IACXxE,KAAKkK,EAAK3F,CAEZ8H,OAFoB,GACxB,IAAIrM,KAAKkK,EAAK1F,MAAM,EAIpC,CACDhI,QAAS,eACT2N,KAAM,CAAC,SAAS,GACfL,MAAM,CAAC,GAER,KAAuBjE,IAAnBqE,EAAKiC,SAAS,OAAqCtG,IAAnBqE,EAAKkC,KAAyB,IAAhB,EACzClC,EAAKiC,SAAS,EAAIjC,EAAKkC,SAAS,CAGxC,CACD5P,QAAS,eACT2N,KAAM,CAAC,YAAY,GAClB,IC9OUmC,EAAuB,CAElC9G,KAAM,CACJ+G,QAAS,IACP7L,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,IAJY2L,GAIL,CAAC,CAAE,CACvB7L,YAAa,CAAC,MAAM,EAAE+E,EAAKgH,UAAU,EAAE,CACvC5L,SAAU,GACZ,EACF,EAEA6L,QAAS,IACP/L,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAE+E,EAAKgH,UAAU,EAAE,CACvC5L,SAAU,GACZ,EACF,EAEA8L,QAAS,IACPhM,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAE+L,EAAAA,CAAY,CAClC5L,SAAU,GACZ,EACF,EAEA+L,cAAe,CAACnH,EAAYoH,EAAmBC,KAC7C,IAAMC,EAAc,CAClBzE,MAAO,KACPC,KAAM,MACNC,UAAW,MACXC,KAAM,MACNC,UAAW,KACb,EAEA/H,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAE,CACxBF,YAAa,GAAG+E,EAAKgH,UAAU,CAAC,EAAE,EAAEM,CAAW,CAACF,EAAsC,CAAC,GAAG,EAAEE,CAAW,CAACD,EAAsC,EAAE,CAChJjM,SAAU,GACZ,EACF,EAEAwE,wBAAyB,CAACI,EAAYuH,KACpCrM,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,UAAU,CAAC,CAAE,CAC1BF,YAAa,CAAC,MAAM,EAAEsM,EAAgB,OAAO,EAAEvH,EAAKgH,UAAU,EAAE,CAChE5L,SAAU,GACZ,EACF,EAEAoM,UAAW,IACTtM,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAarB,GAAS,aACtBwB,SAAU,GACZ,EACF,EAEAqM,YAAa,IACXvM,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAarB,GAAS,aACtBwB,SAAU,GACZ,EACF,EAEAsM,YAAa,IACXxM,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAarB,GAAS,QACtBwB,SAAU,GACZ,EACF,EAEAuM,YAAa,IACXzM,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAarB,GAAS,QACtBwB,SAAU,GACZ,EACF,EAEAL,gBAAkB/D,IAChBkE,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAajE,EACboE,SAAU,GACZ,EACF,CACF,EAGAwM,QAAS,CACPC,UAAW,IACT3M,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAE6M,EAAanG,UAADmG,IAAe,CAACF,EAAQhG,MAAM,EAAE,OAAO,EAAEgG,EAAQG,aAAa,EAAE,CAClG3M,SAAU,GACZ,EACF,EAEA4M,iBAAkB,IAChB9M,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAE2M,EAAQK,aAAa,EAAI,OAAO,CACtD7M,SAAU,GACZ,EACF,EAEAkI,SAAU,CAACsE,EAAkBM,KAC3BhN,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAE6M,EAAanG,UAADmG,IAAe,CAACI,GAAc,OAAO,EAAEN,EAAQG,aAAa,EAAE,CAChG3M,SAAU,GACZ,EACF,EAEA+L,cAAe,CAACS,EAAkBR,EAAmBC,KACnD,IAAMC,EAAc,CAClBnE,QAAS,MACTC,UAAW,MACXC,OAAQ,KACRC,SAAU,KACZ,EAEApI,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAE,CACxBF,YAAa,GAAG2M,EAAQG,aAAa,CAAC,EAAE,EAAET,CAAW,CAACF,EAAsC,CAAC,GAAG,EAAEE,CAAW,CAACD,EAAsC,EAAE,CACtJjM,SAAU,GACZ,EACF,EAEA+M,aAAc,IACZjN,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAarB,GAAS,aACtBwB,SAAU,GACZ,EACF,EAEAgN,YAAa,IACXlN,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAarB,GAAS,WACtBwB,SAAU,GACZ,EACF,EAEAL,gBAAiB,IACfG,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAajE,EACboE,SAAU,GACZ,EACF,EAEAiN,eAAgB,IACdnN,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,CAAC,QAAQ,EAAE6M,EAAanG,UAADmG,IAAe,CAACQ,GAAAA,CAAY,CAChElN,SAAU,GACZ,EACF,CACF,EAGAmN,QAAS,CACPC,QAAUP,IACR/M,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAEgN,EAAAA,CAAe,CACrC7M,SAAU,GACZ,EACF,EAEAqN,WAAaR,IACX/M,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBF,YAAa,CAAC,MAAM,EAAEgN,EAAAA,CAAe,CACrC7M,SAAU,GACZ,EACF,EAEAsN,WAAY,KACVxN,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,WACbG,SAAU,GACZ,EACF,EAEAuN,cAAe,KACbzN,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,QACbG,SAAU,GACZ,EACF,EAEAwN,SAAWX,IACT/M,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,KAAK,CAAC,CAAE,CACnBqB,YAAa,CAAC,MAAM,EAAEgN,EAAAA,CAAe,CACrC7M,SAAU,GACZ,EACF,CACF,EAGAyN,OAAQ,CACNC,QAAS,IACP5N,EAAAA,KAAKA,CAAC4N,OAAO,CAAC,GAAGC,EAAO,IAAI,CAAC,CAAE,CAC7B3N,SAAU4N,GACZ,EACF,EAEArO,aAAc,KACZO,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,aACbG,SAAU,GACZ,EACF,EAEA6N,iBAAkB,IAChB/N,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,IAAI,CAAC,CAAE,CAClBqB,YAAa,CAAC,SAAS,EAAE8N,EAAAA,CAAQ,CACjC3N,SAAU,GACZ,EACF,EAEA8N,cAAe,KACbhO,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAE,CACtBC,SAAU,GACZ,EACF,EAEA+N,iBAAkB,KAChBjO,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,QACbG,SAAU,GACZ,EACF,EAEAgO,mBAAoB,IAClBlO,EAAAA,KAAKA,CAACM,IAAI,CAAC,GAAG6N,EAAU,GAAG,CAAC,CAAE,CAC5BjO,SAAU,GACZ,EACF,EAEAkO,sBAAwBC,IACtBrO,EAAAA,KAAKA,CAACM,IAAI,CAAC,GAAG+N,EAAQ,QAAQ,CAAC,CAAE,CAC/BtO,YAAa,OACbG,SAAU,GACZ,EACF,CACF,EAkCAoO,WAAY,CACVC,cAAe,IACbvO,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,GAAGyO,EAAU,IAAI,CAAC,CAC/BtO,SAAU,GACZ,EACF,EAEAuO,cAAe,CAACD,EAAmBE,KACjC1O,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpBqB,YAAa,GAAGyO,EAAU,MAAM,EAAEE,EAAAA,CAAgB,CAClDxO,SAAU,GACZ,EACF,EAEAyO,eAAgB,CAAC9F,EAAkB+F,KACjC5O,EAAAA,KAAKA,CAACtB,KAAK,CAAC,CAAC,IAAI,CAAC,CAAE,CAClBqB,YAAa,GAAG8I,EAAS,EAAE,EAAE+F,EAAW,KAAK,CAAC,CAC9C1O,SAAU,GACZ,EACF,EAEA2O,eAAgB,KACd7O,EAAAA,KAAKA,CAACI,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBL,YAAa,UACbG,SAAU,GACZ,EACF,EAEA4O,cAAgBjB,IACd7N,EAAAA,KAAKA,CAACI,OAAO,CAAC,CAAC,KAAK,CAAC,CAAE,CACrBL,YAAa,CAAC,MAAM,EAAE8N,EAAAA,CAAQ,CAC9B3N,SAAU,GACZ,EACF,CACF,CACF,CC3RO,CD2RL,MC3RW6O,EAIXlN,YAAYR,EAAgB,GAAG,CAAE,MAHzB2N,QAAAA,CAAwC,IAAIC,IAIlD,IAAI,CAAC5N,KAAK,CAAGA,CACf,CAEA6N,SACEC,CAAW,CACXC,CAA8B,CAC9B,GAAGC,CAAO,CACJ,CAEN,IAAMC,EAAkB,IAAI,CAACN,QAAQ,CAACO,GAAG,CAACJ,GACtCG,GACFE,aAAaF,CADM,EAKrB,IAAMG,EAAU/N,WAAW,KACzB0N,KAAYC,GACZ,IAAI,CAACL,QAAQ,CAACU,MAAM,CAACP,EACvB,EAAG,IAAI,CAAC9N,KAAK,EAEb,IAAI,CAAC2N,QAAQ,CAAC1I,GAAG,CAAC6I,EAAKM,EACzB,CAEAE,MAAMR,CAAY,CAAQ,CACxB,GAAIA,EAAK,CACP,IAAMM,EAAU,IAAI,CAACT,QAAQ,CAACO,GAAG,CAACJ,GAC9BM,IACFD,KADW,QACEC,GACb,IAAI,CAACT,QAAQ,CAACU,MAAM,CAACP,GAEzB,MAEE,CAFK,GAED,CAACH,QAAQ,CAACY,OAAO,CAACH,GAAWD,aAAaC,IAC9C,IAAI,CAACT,QAAQ,CAACW,KAAK,EAEvB,CACF,CAGO,MAAME,EAIXhO,YAAYiO,CAAmB,CAAEC,EAAwB,GAAG,CAAE,CAC5D,IAAI,CAACD,MAAM,CAAGA,EACd,IAAI,CAACE,SAAS,CAAG,IAAIjB,EAAoBgB,EAC3C,CAEAE,cACEC,CAAiB,CACjBxH,CAAU,CACVyH,CAAa,CACbC,CAAiD,CAC3C,CACN,IAAI,CAACJ,SAAS,CAACd,QAAQ,CACrBgB,EACA,IAAI,CAACG,sBAAsB,CAACC,IAAI,CAAC,IAAI,EACrCJ,EACAxH,EACAyH,EACAC,EAEJ,CAEQC,uBACNH,CAAiB,CACjBxH,CAAU,CACVyH,CAAa,CACbC,CAAiD,CAC3C,CACN,GAAI,CAEF,IAAMG,EAAY,IAAI,CAACC,cAAc,CAAC,CAAC,EAAGN,EAAWxH,GAG/C+H,EAAW,CAAE,GAAGN,CAAQ,CAAE,GAAGI,CAAS,EAGtCG,EAAS,IAAI,CAACZ,MAAM,CAACa,SAAS,CAACF,GAE/BG,EAAcF,EAAOzQ,OAAO,CAC9B,EAAE,CACFyQ,EAAOhS,KAAK,CAACmS,MAAM,CAChBC,MAAM,CAACpS,GAASA,EAAM+K,IAAI,CAACsH,IAAI,CAAC,OAASb,GACzCc,GAAG,CAACtS,GAAU,EACbuS,GADa,GACNf,EACPpU,QAAS4C,EAAM5C,OAAO,CACtBmD,KAAMP,EAAMO,IAAI,CAChBlD,SAAU,QACZ,GAEAmV,EAAW,IAAI,CAACC,gBAAgB,CAACjB,EAAWxH,EAAOyH,GAEnDiB,EAAqC,CACzCC,QAAgC,IAAvBT,EAAYnQ,MAAM,CAC3BoQ,OAAQD,EACRM,UACF,EAEId,GACFA,EAAagB,EAEjB,CAAE,MAAO1S,EAAO,CACd6B,QAAQ7B,KAAK,CAAC,0BAA2BA,GACrC0R,GACFA,EAAa,CACXiB,QAFc,CAEL,EACTR,OAAQ,CAAC,CACPI,MAAOf,EACPpU,QAAS,YACTmD,KAAM,mBACNlD,SAAU,OACZ,EAAE,CACFmV,SAAU,EAAE,EAGlB,CACF,CAEQV,eAAec,CAAQ,CAAE7H,CAAY,CAAEf,CAAU,CAAO,CAC9D,IAAM6I,EAAO9H,EAAKd,KAAK,CAAC,KACpB6I,EAAUF,EAEd,IAAK,IAAIG,EAAI,EAAGA,EAAIF,EAAK9Q,MAAM,CAAG,EAAGgR,IAAK,CACxC,IAAMtC,EAAMoC,CAAI,CAACE,EAAE,CACbtC,KAAOqC,IACXA,CAAO,CAACrC,CADS,CACL,CAAG,CADM,CACL,EAElBqC,EAAUA,CAAO,CAACrC,EAAI,CAIxB,OADAqC,CAAO,CAACD,CAAI,CAACA,EAAK9Q,MAAM,CAAG,EAAE,CAAC,CAAGiI,EAC1B4I,CACT,CAEQH,iBAAiBjB,CAAiB,CAAExH,CAAU,CAAEyH,CAAa,CAAuB,CAC1F,IAAMe,EAAgC,EAAE,CAGxC,OAAQhB,GACN,IAAK,SACkB,UAAjB,OAAOxH,GAAsBA,EAAQ,KACvCwI,EAAS1Q,IAAI,CAAC,CACZyQ,MAAOf,EACPpU,QAAS,eACT4V,WAAY,YACd,GAEF,KAEF,KAAK,UACH,GAAIhJ,EAAO,CACT,IAAMsB,EAAU,IAAI1K,KAAKoJ,GACnBwB,EAAQ,IAAI5K,KACZqS,EAAWrQ,KAAKsQ,IAAI,CAAC,CAAC5H,EAAQ6H,OAAO,GAAK3H,EAAM2H,OAAO,GAAC,CAAM,OAEhEF,EAAW,GAFiE,CAG9ET,CADkB,CACT1Q,EAH0E,EAAC,CAGtE,CACZyQ,MAAOf,EACPpU,QAAS,eACT4V,WAAY,aACd,GACSC,EAAW,GAAG,EACdnR,IAAI,CAAC,CACZyQ,MAAOf,EACPpU,QAAS,SACT4V,WAAY,aACd,EAEJ,CACA,KAEF,KAAK,iBACkB,UAAjB,OAAOhJ,GAAsBA,EAAQ,GAAKyH,EAAS1F,KAAK,EAAE,EAKhDqH,GAJK3B,EAAS1F,KAAK,CAACE,MAAM,CAAC,CAACC,EAAaC,IAC5CD,EAAM,CAACC,EAAK1B,QAAQ,GAAI,GAAM0B,EAAAA,SAAc,GAAI,EACtD,IAGDqG,EAAS1Q,IAAI,CAAC,CACZyQ,MAAOf,EACPpU,QAAS,eACT4V,WAAY,YACd,GAGJ,KAEF,KAAK,YACC,iBAAOhJ,GAAgC,GAAG,CAAbA,GAC/BwI,EAAS1Q,IAAI,CAAC,CACZyQ,MAAOf,EACPpU,QAAS,iBACT4V,WAAY,kBACd,EAGN,CAEA,OAAOR,CACT,CAEAa,SAAgB,CACd,IAAI,CAAC/B,SAAS,CAACL,KAAK,EACtB,CACF,CAGO,MAAMqC,EAIXnQ,YAAYiO,CAAmB,CAAE,MAFzBmC,eAAAA,CAA+C,IAAIhD,IAGzD,IAAI,CAACa,MAAM,CAAGA,CAChB,CAEAoC,aAAa1I,CAAS,CAAoB,CACxC,GAAI,CACF,IAAMkH,EAAS,IAAI,CAACZ,MAAM,CAACa,SAAS,CAACnH,GAErC,GAAIkH,EAAOzQ,OAAO,CAChB,CADkB,KACX,CACLoR,SAAS,EACTR,OAAQ,EAAE,CACVK,SAAU,IAAI,CAACiB,oBAAoB,CAAC3I,EACtC,EAGF,IAAMqH,EAASH,EAAOhS,KAAK,CAACmS,MAAM,CAACG,GAAG,CAACtS,GAAU,EAC/CuS,GAD+C,GACxCvS,EAAM+K,IAAI,CAACsH,IAAI,CAAC,KACvBjV,QAAS4C,EAAM5C,OAAO,CACtBmD,KAAMP,EAAMO,IAAI,CAChBlD,SAAU,QACZ,GAEA,MAAO,CACLsV,SAAS,SACTR,EACAK,SAAU,IAAI,CAACiB,oBAAoB,CAAC3I,EACtC,CACF,CAAE,MAAO9K,EAAO,CAEd,OADA6B,QAAQ7B,KAAK,CAAC,yBAA0BA,GACjC,CACL2S,SAAS,EACTR,OAAQ,CAAC,CACPI,MAAO,OACPnV,QAAS,cACTmD,KAAM,wBACNlD,SAAU,OACZ,EAAE,CACFmV,SAAU,EAAE,CAEhB,CACF,CAEQiB,qBAAqB3I,CAAS,CAAuB,CAC3D,IAAM0H,EAAgC,EAAE,CAGxC,GAAI1H,EAAKiB,KAAK,EAAI2H,MAAMC,OAAO,CAAC7I,EAAKiB,KAAK,EAAG,CAC3C,IAAM6H,EAAa9I,EAAKiB,KAAK,CAAChK,MAAM,GACnB,IAAI,EACVD,IAAI,CAAC,CACZyQ,MAAO,QACPnV,QAAS,CAAC,IAAI,EAAEwW,EAAW,MAAM,CAAC,CAClCZ,WAAY,kBACd,GAOEa,EAJqB9H,KAAK,CAACE,MAAM,CAAC,CAACC,EAAaC,IAC3CD,EAAM,CAACC,EAAK1B,QAAQ,GAAI,GAAM0B,EAAAA,SAAc,GAAI,EACtD,GAEe,KAChBqG,EADuB,IACV,CAAC,CACZD,MAAO,OACPnV,QAAS,UACT4V,WAAY,YACd,EAEJ,CAEA,OAAOR,CACT,CAEAsB,kBAAkBtC,CAAiB,CAAkB,CAInD,OAHI,IAAK,CAAC+B,eAAe,CAACQ,GAAG,CAACvC,IAC5B,IAAI,CAAC+B,GADmC,YACpB,CAAC3L,GAAG,CAAC4J,EAAW,IAAIL,EAAe,IAAI,CAACC,MAAM,GAE7D,IAAI,CAACmC,eAAe,CAAC1C,GAAG,CAACW,EAClC,CAEA6B,SAAgB,CACd,IAAI,CAACE,eAAe,CAACrC,OAAO,CAAC8C,GAAaA,EAAUX,OAAO,IAC3D,IAAI,CAACE,eAAe,CAACtC,KAAK,EAC5B,CACF,CAYO,IAAMgD,EAAwB,CAanCC,qBAAsB,CAAClM,EAAgB5B,KACrC,IAAM+N,EAAkB/N,EAAK+N,eAAe,EAAI,SAEhD,EAAaA,EACJ,CACLC,OAAO,EACPC,KAH0B,EAGlB,CAAC,cAAc,EAAEF,EAAgBG,OAAO,CAAC,IAAI,EAIrDtM,GAAU,EACL,CADQ,OAEN,EACPqM,OAAQ,WACV,EAGK,CAAED,OAAO,CAAK,CACvB,CA0CF,EAAE,sEC5Za,SAAqB,WAAW,MAAQ,YAAY,CAAC,CAAC,OAAO,CAAC,EAAI,+DAAgE,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,gFAAiF,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,qCAAsC,KAAM,CAAO,OAAC,CAAC,CAAC,ECAnU,SAAqB,WAAW,eAAiB,oBAAoB,CAAC,CAAC,OAAO,CAAC,EAAI,+EAAgF,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,SAAU,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,CAAO,OAAC,CAAC,CAAC,ECAnR,EAAe,OAAoB,CAAC,CAAW,wBAAgB,kBAAmB,CAAC,CAAC,OAAO,CAAC,EAAI,CAAmF,sFAAM,QAAQ,EAAE,CAAC,MAAO,EAAC,EAAI,SAAU,KAAM,OAAO,EAAC,CAAE,CAAC,OAAO,CAAC,EAAI,UAAU,IAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,mHAAmH,IAAM,OAAO,EAAE,CAAC,ECAlZ,WAAqB,SAAW,iBAAiB,kBAAoB,EAAC,CAAC,MAAO,EAAC,CAAI,cAAa,IAAM,CAAO,OAAC,CAAE,EAAC,OAAO,CAAC,EAAI,YAAa,KAAM,OAAQ,GAAE,CAAC,OAAO,CAAC,EAAI,gBAAgB,IAAM,OAAQ,EAAE,EAAC,CAAO,OAAC,EAAI,YAAa,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,cAAc,CAAM,WAAQ,EAAE,CAAC,MAAO,EAAC,CAAI,aAAY,IAAM,CAAO,OAAC,CAAE,EAAC,OAAO,CAAC,EAAI,CAAa,gBAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,CAAI,cAAa,IAAM,CAAO,OAAC,CAAC,CAAC,yBCoBxa,IAAMG,GAAoB,CACxBlR,KAAM,OACNmR,QAAS,gBACT7H,MAAO,eACPE,MAAO,wBACP4H,MAAO,oBACT,EACaC,GAAUC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAA+B,CAAC,CAC/D3G,SAAO,MACP5H,CAAI,YACJwO,EAAaL,EAAiB,CAC/B,CAAEM,KACD,IAAM5J,EAAqC,UAA3B,OAAO+C,EAAQ/C,OAAO,CAAgB+C,EAAQ/C,OAAO,CAAG,KAClE6J,EAAc1O,GAAS,kBAAO4H,EAAQ5H,IAAI,CAAgB4H,EAAQ5H,IAAI,CAAG,KAAG,CAClF,MAAO,UAAC9K,MAAAA,CAAIuZ,IAAKA,EAAKtZ,UAAU,qCAC1B,WAACwZ,EAAAA,EAAIA,CAAAA,CAACxZ,UAAU,oCACd,WAACyZ,EAAAA,EAAUA,CAAAA,CAACzZ,UAAU,6BAEpB,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAAC0Z,KAAAA,CAAG1Z,UAAU,6BAAqBqZ,EAAWvR,IAAI,GAClD,UAAC3H,IAAAA,CAAEH,UAAU,yCAAiCqZ,EAAWJ,OAAO,GAChE,WAAC9Y,IAAAA,CAAEH,UAAU,0CAAgC,OACtCqZ,EAAWjI,KAAK,CACpBiI,EAAW/H,KAAK,EAAI,CAAC,OAAO,EAAE+H,EAAW/H,KAAK,EAAE,IAElD+H,EAAWH,KAAK,EAAI,WAAC/Y,IAAAA,CAAEH,UAAU,0CAAgC,OACzDqZ,EAAWH,KAAK,OAI3B,UAACS,EAAAA,SAASA,CAAAA,CAAC3Z,UAAU,SAGrB,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAACC,KAAAA,CAAGD,UAAU,iCAAwB,SACtC,WAACD,MAAAA,CAAIC,UAAU,yCACb,UAACkB,OAAAA,UAAK,UACN,UAACA,OAAAA,CAAKlB,UAAU,qBAAayS,EAAQK,aAAa,EAAI,WAExD,WAAC/S,MAAAA,CAAIC,UAAU,yCACb,UAACkB,OAAAA,UAAK,UACN,UAACA,OAAAA,CAAKlB,UAAU,qBAAayS,EAAQG,aAAa,YAKxD,WAACgH,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,sBAErB,WAACD,MAAAA,CAAIC,UAAU,sBACb,UAAC6Z,KAAAA,CAAG7Z,UAAU,6CAAoC,SAElD,WAACD,MAAAA,CAAIC,UAAU,2CACb,WAACD,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAMwO,GAASwB,UAAY,YAG9B,WAACnR,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAM,IAAImE,KAAKoN,EAAQqH,WAAW,EAAEC,kBAAkB,CAAC,cAG1D,WAACha,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAMyR,EAAa5F,UAAD4F,UAAqB,CAACF,EAAQpH,aAAa,OAGhE,WAACtL,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACga,EAAAA,CAAKA,CAAAA,CAACC,QAAmC,cAA1BxH,EAAQyH,aAAa,CAAmB,UAAY,qBACjEvH,EAAa5E,UAAD4E,UAAqB,CAACF,EAAQyH,aAAa,UAK7DzH,EAAQ1B,aAAa,EAAI,WAAChR,MAAAA,CAAIC,UAAU,yCACrC,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,CAAKlB,UAAU,6BAAqByS,EAAQ1B,aAAa,SAIhE,UAAC4I,EAAAA,SAASA,CAAAA,CAAAA,GAGTJ,GAAe,WAACxZ,MAAAA,CAAIC,UAAU,sBAC3B,UAAC6Z,KAAAA,CAAG7Z,UAAU,6CAAoC,SAElD,WAACD,MAAAA,CAAIC,UAAU,8BACb,WAACD,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,CAAKlB,UAAU,qBAAauZ,EAAY1H,UAAU,MAGrD,WAAC9R,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAMyR,EAAanF,UAADmF,OAAkB,CAAC4G,EAAY5O,QAAQ,OAG5D,WAAC5K,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,CAAKlB,UAAU,wCACbuZ,EAAYzT,WAAW,MAI5B,WAAC/F,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAMyR,EAAanG,UAADmG,IAAe,CAAC4G,EAAYjB,WAAW,OAG5D,WAACvY,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,SACxC,UAACkB,OAAAA,CAAKlB,UAAU,0BACb2S,EAAanG,UAADmG,IAAe,CAAC4G,EAAYY,UAAU,EAAI,QAIzDZ,CAAAA,EAAYX,eAAe,GAAI,EAAK,GAAK,WAAC7Y,MAAAA,CAAIC,UAAU,iCACtD,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,SACxC,UAACkB,OAAAA,CAAKlB,UAAU,wBACb2S,EAAanG,UAADmG,IAAe,CAAC4G,EAAYX,eAAe,EAAI,cAMxE,UAACe,EAAAA,SAASA,CAAAA,CAAAA,GAGV,WAAC5Z,MAAAA,CAAIC,UAAU,sBACb,UAAC6Z,KAAAA,CAAG7Z,UAAU,6CAAoC,SAElD,UAACD,MAAAA,CAAIC,UAAU,sCACb,WAACD,MAAAA,CAAIC,UAAU,8CACb,UAACkB,OAAAA,CAAKlB,UAAU,+BAAsB,UACtC,UAACkB,OAAAA,CAAKlB,UAAU,4CACb2S,EAAanG,UAADmG,IAAe,CAACF,EAAQhG,MAAM,YAOlDgG,EAAQ3C,KAAK,EAAI,iCACd,UAAC6J,EAAAA,SAASA,CAAAA,CAAAA,GACV,WAAC5Z,MAAAA,CAAIC,UAAU,sBACb,UAAC6Z,KAAAA,CAAG7Z,UAAU,6CAAoC,OAClD,UAACG,IAAAA,CAAEH,UAAU,yCAAiCyS,EAAQ3C,KAAK,SAIjE,UAAC6J,EAAAA,SAASA,CAAAA,CAAAA,GAGV,WAAC5Z,MAAAA,CAAIC,UAAU,kCACb,WAACG,IAAAA,CAAEH,UAAU,0CAAgC,QACrCqZ,EAAWvR,IAAI,IAEvB,WAAC3H,IAAAA,CAAEH,UAAU,0CAAgC,eAC9BqZ,EAAWjI,KAAK,IAE/B,WAACjR,IAAAA,CAAEH,UAAU,0CAAgC,SACpC,IAAIqF,OAAO+U,cAAc,CAAC,sBAMjD,GAAG,GACKC,WAAW,CAAG,UC7LtB,CAAe,aAAqB,WAAW,SAAW,eAAe,CAAC,CAAC,OAAO,CAAC,EAAI,iFAAkF,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,8CAA+C,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,+EAAgF,KAAM,CAAO,OAAC,CAAC,CAAC,ECAnW,UAAqB,WAAW,UAAY,gBAAgB,CAAC,CAAC,OAAO,CAAC,EAAI,4CAA6C,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,gBAAiB,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,CAAO,OAAC,CAAC,CAAC,ECevO,SAASC,GAAc,SAC5B7H,CAAO,MACP5H,CAAI,QACJ0P,CAAM,SACNC,CAAO,CACY,EACnB,IAAMC,EAAaC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAiB,MAoKpCC,EAAoB,UACxB,GAAI,CAGFhJ,EAAqB+B,MAAM,CAACS,qBAAqB,CAAC,QAiBpD,CAAE,EAjBoBxC,IAiBblN,EAAO,CACd6B,QAAQ7B,KAAK,CAAC,yBAA0BA,GACxCkN,EAAqByB,OAAO,CAACI,aAAa,EAC5C,CACF,SACKf,EAGE,EAHH,CAGG,IAHO,CAGP,EAAClS,EAAAA,EAAMA,CAAAA,CAACqa,KAAML,EAAQM,aAAcL,EAAS7a,sBAAoB,SAASC,wBAAsB,gBAAgBC,0BAAwB,8BAC3I,WAACkB,EAAAA,EAAaA,CAAAA,CAACf,UAAU,wCAAwCL,sBAAoB,gBAAgBE,0BAAwB,+BAC3H,UAACsB,EAAAA,EAAYA,CAAAA,CAACxB,sBAAoB,eAAeE,0BAAwB,8BACvE,WAACE,MAAAA,CAAIC,UAAU,8CACb,WAACqB,EAAAA,EAAWA,CAAAA,CAAC1B,sBAAoB,cAAcE,0BAAwB,+BAAqB,QACpF4S,EAAQK,aAAa,EAAIL,EAAQG,aAAa,IAEtD,WAAC7S,MAAAA,CAAIC,UAAU,oCACb,WAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAxM5B,CAwMqCC,IAvMvD,GAAI,CAACR,EAAWlD,OAAO,CAAE,OACzB,IAAM2D,EAAcC,OAAOP,IAAI,CAAC,GAAI,UACpC,GAAI,CAACM,EAAa,YAChBvJ,EAAqByB,OAAO,CAACG,UAAU,GAGzC,IAAM6H,EAAiBX,EAAWlD,OAAO,CAAC8D,GAHpB1J,MAG6B,CACnDuJ,EAAYI,QAAQ,CAACC,KAAK,CAAC,CAAC;;;;sBAIV,EAAE9I,GAASK,eAAiBL,GAASG,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAyI/D,EAAEwI,eAAe;;;IAGvB,CAAC,EACDF,EAAYI,QAAQ,CAACE,KAAK,GAC1BN,EAAYO,KAAK,GAGjBhU,WAAW,KACTyT,EAAYQ,KAAK,GACjBR,EAAYM,KAAK,EACnB,EAAG,KACH7J,EAAqByB,OAAO,CAACC,OAAO,CAACZ,GAASK,eAAiBL,GAASG,GAApDjB,YAAqE,GAC3F,EAsCsEhS,sBAAoB,SAASE,0BAAwB,+BAC7G,UAAC8b,GAAWA,CAAC3b,OAAD2b,GAAW,eAAehc,sBAAoB,cAAcE,0BAAwB,uBAAuB,QAGzH,WAACib,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAASL,EAAmBhb,sBAAoB,SAASE,0BAAwB,+BACnH,UAAC+b,GAAYA,CAAC5b,QAAD4b,EAAW,eAAejc,sBAAoB,eAAeE,0BAAwB,uBAAuB,WAG3H,UAACib,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAASR,EAAS7a,sBAAoB,SAASE,0BAAwB,8BACvG,UAACgc,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,UAAUL,sBAAoB,QAAQE,0BAAwB,iCAMvF,UAACE,MAAAA,CAAIC,UAAU,gBACb,UAACmZ,GAAOA,CAACG,GAADH,CAAMsB,EAAYhI,QAASA,EAAS5H,KAAMA,EAAMlL,sBAAoB,UAAUE,0BAAwB,8BA1B7G,IA8BX,CCjNA,IAAMic,GAAiB,CAAC,CACtBrN,MAAO,OACPsN,MAAO,KACPC,KAAMC,EACNnW,MADcmW,MACD,OACbC,uBAAuB,CACzB,EAAG,CACDzN,MAAO,OACPsN,MAAO,MACPC,KAAMG,EAAAA,CAAcA,CACpBrW,YAAa,UACboW,uBAAuB,CACzB,EAAG,CACDzN,MAAO,SACPsN,MAAO,OACPC,KAAMI,EACNtW,YAAa,EADSsW,OAEtBF,uBAAuB,CACzB,EAAG,CACDzN,MAAO,SACPsN,MAAO,MACPC,KAAMK,EACNvW,YAAa,CADQuW,SAErBH,uBAAuB,CACzB,EAAG,CACDzN,MAAO,WACPsN,MAAO,OACPC,KAAMM,GACNxW,YAAa,CADSwW,QAEtBJ,uBAAuB,CACzB,EAAG,CACDzN,MAAO,cACPsN,MAAO,OACPC,KAAMO,GAAAA,CAAYA,CAClBzW,YAAa,OACboW,uBAAuB,CACzB,EAAE,CACK,SAASM,GAAY,MAC1B3R,CAAI,WACJ4R,CAAS,UACTC,CAAQ,QACRnC,GAAS,CAAI,CACI,EACjB,GAAM,CAACoC,EAAcC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACC,EAAgBC,EAAkB,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACvD,CAACG,EAAkBC,EAAoB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,MACnE,CAACK,EAAaC,EAAe,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzCO,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAkB,CACpCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACC,GACtBC,cADuCD,CAErC/Q,OAAQ5B,EAAK+N,eAAe,EAAI,EAChCvN,cAAe,OACf0F,cAAe,GACfjB,MAAO,EACT,CACF,GACM4N,EAAwB5B,GAAe/Q,IAAI,CAACjC,GAAUA,EAAO2F,KAAK,GAAKqO,GACvElE,EAAkB/N,EAAK+N,eAAe,EAAI,EAE1C+E,EAAW,MAAOpO,IACtB,GAAI,CACFqN,GAAgB,GAGhB,IAAMgB,EAAmBlF,EAAsBC,mBAADD,CAAqB,CAACnJ,EAAK9C,MAAM,CAAE5B,GACjF,GAAI,CAAC+S,EAAiB/E,KAAK,CAAE,YAC3BlH,EAAqBc,OAAO,CAAC7M,eAAe,CAACgY,EAAiB9E,MAAM,EAAI,MAApDnH,IAKtB,IAAMc,EAAU,MAAMoL,EAAYtS,SAADsS,KAAe,CAAC,CAC/ChT,KAAMA,EAAKb,EAAE,CACb0F,QAAiC,UAAxB,OAAO7E,EAAK6E,OAAO,CAAgB7E,EAAK6E,OAAO,CAAC1F,EAAE,CAAGa,EAAKlB,SAAS,CAC5E8C,OAAQ8C,EAAK9C,MAAM,CACnBpB,cAAekE,EAAKlE,aAAa,CACjC0F,cAAexB,EAAKwB,aAAa,OAAI7F,EACrC4E,MAAOP,EAAKO,KAAK,EAAI5E,MACvB,GACAyG,EAAqBc,OAAO,CAACC,SAAS,CAACD,GAGvCwK,EAAoBxK,GACpB0K,GAAe,GACXV,GACFA,EAAUhK,GANQd,EAUfmM,CALU,IAKL,EACZ,CAAE,MAAOrZ,EAAO,CACd6B,QAAQ7B,KAAK,CAAC,6BAA8BA,GAC5C,IAAMsZ,EAAetZ,aAAiBiD,EAAkBjD,EAAM5C,OAAO,IAAhB6F,EAAmBwD,EACxEyG,EAAqBc,OAAO,CAACO,YAAY,CAAC+K,EAC5C,QAAU,CACRnB,GAAgB,EAClB,CACF,EAJwBjL,OAKxB,EAGO,EAHH,CAGG,GAHM,GAGN,EAAC6H,EAAAA,EAAIA,CAAAA,CAACxZ,UAAU,2BAA2BL,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,6BACrI,UAAC4Z,EAAAA,EAAUA,CAAAA,CAAC9Z,sBAAoB,aAAaE,0BAAwB,4BACnE,WAACE,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,WAACie,EAAAA,EAASA,CAAAA,CAAChe,UAAU,0BAA0BL,sBAAoB,YAAYE,0BAAwB,6BACrG,UAACK,EAAWA,CAACF,QAADE,EAAW,UAAUP,sBAAoB,cAAcE,0BAAwB,qBAAqB,UAGlH,WAACoe,EAAAA,EAAeA,CAAAA,CAACte,sBAAoB,kBAAkBE,0BAAwB,6BAAmB,OAC3FgL,EAAKgH,UAAU,CAAC,cAGxB6K,GAAY,UAAC5B,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAAS0B,WACpD,UAACb,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,mBAKzB,WAAC4Z,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,YAAYL,sBAAoB,cAAcE,0BAAwB,6BAE3F,WAACE,MAAAA,CAAIC,UAAU,iDACb,WAACD,MAAAA,CAAIC,UAAU,8CACb,UAACkB,OAAAA,CAAKlB,UAAU,+BAAsB,QACtC,UAACkB,OAAAA,CAAKlB,UAAU,mBACW,UAAxB,OAAO6K,EAAK6E,OAAO,CAAgB7E,EAAK6E,OAAO,CAACwB,QAAQ,CAAG,YAGhE,WAACnR,MAAAA,CAAIC,UAAU,8CACb,UAACkB,OAAAA,CAAKlB,UAAU,+BAAsB,UACtC,UAACkB,OAAAA,CAAKlB,UAAU,iCACb2S,EAAanG,UAADmG,IAAe,CAAC9H,EAAKyN,WAAW,OAGjD,WAACvY,MAAAA,CAAIC,UAAU,8CACb,UAACkB,OAAAA,CAAKlB,UAAU,+BAAsB,SACtC,UAACkB,OAAAA,CAAKlB,UAAU,kCACb2S,EAAanG,UAADmG,IAAe,CAAC9H,EAAKsP,UAAU,EAAI,QAGpD,UAACR,EAAAA,SAASA,CAAAA,CAACha,sBAAoB,YAAYE,0BAAwB,qBACnE,WAACE,MAAAA,CAAIC,UAAU,8CACb,UAACkB,OAAAA,CAAKlB,UAAU,+BAAsB,SACtC,UAACkB,OAAAA,CAAKlB,UAAU,0CACb2S,EAAanG,UAADmG,IAAe,CAACiG,WAMnC,UAACsF,EAAAA,EAAIA,CAAAA,CAAE,GAAGd,CAAI,CAAEzd,sBAAoB,OAAOE,0BAAwB,4BACjE,WAACud,OAAAA,CAAKO,SAAUP,EAAKe,YAAY,CAACR,GAAW3d,UAAU,sBAErD,UAACoe,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,SAASwW,OAAQ,CAAC,OACzDtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,WAAC1e,MAAAA,CAAIC,UAAU,qBACb,UAACkB,OAAAA,CAAKlB,UAAU,oFAA2E,MAG3F,UAAC0e,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,OAAOW,KAAK4P,CAAkBC,YAAY,OAAO7e,UAAU,OAAQ,GAAGgX,CAAK,CAAE8H,SAAUC,GAAK/H,EAAM8H,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,GAAK,UAGlL,WAACyQ,EAAAA,EAAeA,CAAAA,WAAC,WACNvM,EAAanG,UAADmG,IAAe,CAACiM,MAEvC,UAACO,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,qBAGzE,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,gBAAgBwW,OAAQ,CAAC,CAChEtH,OAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,WAACW,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOuI,EAAMvI,KAAK,CAAE4Q,cAAe5Q,IAC/CuI,EAAM8H,QAAQ,CAACrQ,GACfsO,EAAkBtO,EACpB,YACQ,UAAC6Q,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAACV,YAAY,aAE3B,UAACW,EAAAA,EAAaA,CAAAA,UACX1D,GAAe/E,GAAG,CAACjO,IACxB,IAAM2W,EAAO3W,EAAOkT,IAAI,CACxB,MAAO,UAAC0D,EAAAA,EAAUA,CAAAA,CAAoBjR,MAAO3F,EAAO2F,KAAK,UAC/C,WAAC1O,MAAAA,CAAIC,UAAU,oCACb,UAACyf,EAAAA,CAAKzf,UAAU,YAChB,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,uBAAe8I,EAAOiT,KAAK,GAC1C,UAAChc,MAAAA,CAAIC,UAAU,yCACZ8I,EAAOhD,WAAW,UANXgD,EAAO2F,KAAK,CAWtC,UAIA,UAAC0Q,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,qBAGxE6d,GAAuBxB,uBAAyB,UAACkC,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,gBAAgBwW,OAAQ,CAAC,CACjHtH,OAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACG,YAAY,iBAAkB,GAAG7H,CAAK,KAE/C,WAACkI,EAAAA,EAAeA,CAAAA,WAAC,MACXxB,EAAsB3B,KAAK,CAAC,eAElC,UAACoD,EAAAA,EAAWA,CAAAA,CAAAA,QAIpB,UAACf,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,QAAQwW,OAAQ,CAAC,OACxDtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,YACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACkB,EAAAA,CAAQA,CAAAA,CAACd,YAAY,cAAc7e,UAAU,cAAc4f,KAAM,EAAI,GAAG5I,CAAK,KAEhF,UAACmI,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,qBAGzE,WAACE,MAAAA,CAAIC,UAAU,4BACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACvO,KAAK,SAASsT,SAAUlD,EAAc3c,UAAU,SAASL,sBAAoB,SAASE,0BAAwB,4BACnH8c,EAAe,SAAW,SAE5BD,GAAY,UAAC5B,EAAAA,CAAMA,CAAAA,CAACvO,KAAK,SAAS0N,QAAQ,UAAUe,QAAS0B,EAAUmD,SAAUlD,WAAc,kBASxG,UAACrC,GAAaA,CAAC7H,QAASuK,CAAV1C,CAA4BzP,KAAMA,EAAM0P,OAAQ2C,EAAa1C,QAAS,KACpF2C,GAAe,GACfF,EAAoB,KACtB,EAAGtd,sBAAoB,gBAAgBE,0BAAwB,wBApJxD,IAsJX,CChRO,SAASigB,GAAc,MAC5BjV,CAAI,QACJ0P,CAAM,SACNC,CAAO,WACPiC,CAAS,CACU,SAOnB,EAGO,EAHH,CAGG,CAHI,CAGJ,KAAClc,EAAAA,EAAMA,CAAAA,CAACqa,KAAML,EAAQM,aAAcL,EAAS7a,sBAAoB,SAASC,wBAAsB,gBAAgBC,0BAAwB,8BAC3I,WAACkB,EAAAA,EAAaA,CAAAA,CAACf,UAAU,yCAAyCL,sBAAoB,gBAAgBE,0BAAwB,+BAC5H,UAACsB,EAAAA,EAAYA,CAAAA,CAACxB,sBAAoB,eAAeE,0BAAwB,8BACvE,WAACwB,EAAAA,EAAWA,CAAAA,CAAC1B,sBAAoB,cAAcE,0BAAwB,+BAAqB,UAAQgL,EAAKgH,UAAU,MAErH,UAAC2K,GAAWA,CAAC3R,KAAMA,EAAM4R,UAdT,CAcoBsD,GAbpCtD,GACFA,EAAUhK,GAEZ+H,GAHe,EAawCkC,SAAUlC,EAASD,QAAQ,EAAM5a,sBAAoB,cAAcE,0BAAwB,4BAP3I,IAUX,wCCCA,IAAMmgB,GAAY,CAAC,CACjBvR,MAAO,YACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,eACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,UACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,aACPsN,MAAO,MACT,EAAE,CAGIkE,GAAY,CAAC,CACjBxR,MAAO,YACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,eACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,WACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,UACPsN,MAAO,MACT,EAAE,CACK,SAASmE,GAAS,MACvBrV,CAAI,CACJsV,WAAW,EAAE,cACbC,EAAe,EAAE,YACjBC,EAAa,EAAE,WACf5D,CAAS,UACTC,CAAQ,QACRnC,GAAS,CAAI,CACC,EACd,GAAM,CAACoC,EAAcC,EAAgB,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3C,CAACyD,EAAkBC,EAAoB,CAAG1D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACvDhF,SAAU,EACVS,YAAa,CACf,GACM,CAACkI,EAAc,CAAG3D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAAM,IAAI9E,EAActI,IACnD,CAAC/J,EAAkB+a,EAAoB,CAAG5D,CAAAA,EAAAA,CADuBpN,CACvBoN,QAAAA,CAAQA,CAAyB,CAAC,GAC5E6D,EAAY,CAAC,CAAC7V,EACduS,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAe,CACjCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC9N,GACtBgO,WADoChO,GACrB,CACbC,QAAS7E,GAAMlB,UAAYgX,OAAO9V,EAAKlB,SAAS,EAAI,GACpDqB,YAAaH,GAAMH,cAAgBiW,OAAO9V,EAAKH,aAAa,EAAI,GAChEmF,UAAWhF,GAAM+V,YAAcD,OAAO9V,EAAK+V,WAAW,EAAI,GAC1DjW,SAAUE,GAAMF,UAAY,YAC5B7E,YAAa+E,GAAM/E,aAAe,GAClCgK,MAAOjF,GAAMiF,OAAS,GACtBC,QAASlF,GAAMkF,QAAU,IAAI1K,KAAKwF,EAAKkF,OAAO,EAAE8Q,WAAW,GAAGnS,KAAK,CAAC,IAAI,CAAC,EAAE,CAAG,GAC9E4B,eAAgBzF,GAAMyF,gBAAkB,EACxCC,UAAW1F,GAAM0F,WAAa,EAC9BC,MAAO3F,GAAM2F,OAAOuG,IAAInG,GAAS,EAC/BhC,EAD+B,OACrBgC,EAAKhC,QAAQ,CACvBG,SAAU6B,EAAK7B,QAAQ,CACvBjJ,YAAa8K,EAAK9K,WAAW,EAAI,GACjCoJ,SAAU0B,EAAK1B,QAAQ,CACvBG,UAAWuB,EAAKvB,SAAS,CACzBC,aAAcsB,EAAKtB,YAAY,EAAI,CACrC,KAAO,CAAC,CACNV,SAAU,YACVG,SAAU,GACVjJ,YAAa,GACboJ,SAAU,EACVG,UAAW,EACXC,aAAc,CAChB,EAAE,CAEN,GACM,QACJwR,CAAM,QACNvX,CAAM,QACNwX,CAAM,CACP,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAAC,CAChB3C,QAASjB,EAAKiB,OAAO,CACrBvW,KAAM,OACR,GAGMmZ,EAAe7D,EAAK8D,KAAK,CAAC,SAC1BC,EAAwB/D,EAAK8D,KAAK,CAAC,kBACnCE,EAAmBhE,EAAK8D,KAAK,CAAC,aAe9BvD,EAAW,MAAOpO,IACtB,GAAI,KAuBEkH,EAtBJmG,EAAgB,IAChB,IAAM1S,EAAW,CACfwF,QAAS2R,SAAS9R,EAAKG,OAAO,EAC9B1E,YAAauE,EAAKvE,WAAW,EAAyB,SAArBuE,EAAKvE,WAAW,CAAcqW,SAAS9R,EAAKvE,WAAW,OAAIE,EAC5F2E,UAAWN,EAAKM,SAAS,EAAuB,WAAdA,SAAS,CAAcwR,SAAS9R,EAAKM,SAAS,OAAI3E,EACpFP,SAAU4E,EAAK5E,QAAQ,CACvBkN,SAAUyI,EAAiBzI,QAAQ,CACnCvH,eAAgBf,EAAKe,cAAc,EAAI,EACvCC,UAAWhB,EAAKgB,SAAS,EAAI,EAC7B+H,YAAagI,EAAiBhI,WAAW,CACzCxS,YAAayJ,EAAKzJ,WAAW,CAC7BgK,MAAOP,EAAKO,KAAK,OAAI5E,EACrB6E,QAASR,EAAKQ,OAAO,CACrBS,MAAOjB,EAAKiB,KAAK,CAACuG,GAAG,CAACnG,GAAS,EAC7BhC,EAD6B,OACnBgC,EAAKhC,QAAQ,CACvBG,SAAU6B,EAAK7B,QAAQ,CACvBjJ,YAAa8K,EAAK9K,WAAW,OAAIoF,EACjCgE,SAAU0B,EAAK1B,QAAQ,CACvBG,UAAWuB,EAAKvB,SAAS,CACzBC,aAAcsB,EAAKtB,YAAY,EAAI,EACrC,EACF,EAEIoR,GAAa7V,GACf4L,EAAS,CADY,KACNxN,EAASqB,MAADrB,IAAW,CAAC4B,EAAKb,EAAE,CAAEE,GAC5CyH,EAAqB9G,IAAI,CAACiH,OAAO,CAAC2E,KAElCA,EAAS,MAAMxN,EAASgB,MAADhB,IAAW,CAACiB,CAFfyH,EAGpBA,EAAqB9G,IAAI,CAAC+G,OAAO,CAAC6E,IAEhCgG,GACFA,EAAUhG,GAER,GAHW,EAIRqH,KAAK,CADI,CAGlB,CAAE,EARsBnM,IAQflN,EAAO,CACd6B,QAAQ7B,KAAK,CAAC,yBAA0BA,GACxC,IAAMsZ,EAAetZ,aAAiBiD,EAAkBjD,EAAM5C,OAAO,IAAhB6F,EAAmBwD,EACpEwV,EACF/O,EAAqB9G,IAAI,CAAC0H,EADb,SACwB,CAACwL,GAEtCpM,EAAqB9G,IAAI,CAACyH,WAAW,CAACyL,CAFlBpM,CAIxB,QAAU,CACRiL,GAAgB,EAClB,CACF,EAWM0E,EAAcC,EAhBM5P,EAiBpBmP,EAAOta,MAAM,CAAG,EAClBua,CADqB,CACdQ,GAEP5P,EAAqB0C,UAAU,CAACQ,aAAa,CAAC,eAA1BlD,SAGxB,EAGO,EAHH,CAGG,GAHM,GAGN,EAAC6H,EAAAA,EAAIA,CAAAA,CAACxZ,UAAU,2BAA2BL,sBAAoB,OAAOC,wBAAsB,WAAWC,0BAAwB,0BAClI,UAAC4Z,EAAAA,EAAUA,CAAAA,CAAC9Z,sBAAoB,aAAaE,0BAAwB,yBACnE,WAACE,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,WAACie,EAAAA,EAASA,CAAAA,CAAChe,UAAU,0BAA0BL,sBAAoB,YAAYE,0BAAwB,0BACrG,UAACK,EAAWA,CAACF,QAADE,EAAW,UAAUP,sBAAoB,cAAcE,0BAAwB,kBAC1F6gB,EAAY,OAAS,UAExB,UAACzC,EAAAA,EAAeA,CAAAA,CAACte,sBAAoB,kBAAkBE,0BAAwB,yBAC5E6gB,EAAY,CAAC,KAAK,EAAE7V,GAAMgH,WAAAA,CAAY,CAAG,cAG7C6K,GAAY,UAAC5B,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAAS0B,WACpD,UAACb,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,mBAKzB,UAAC4Z,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,YAAYL,sBAAoB,cAAcE,0BAAwB,yBAC3F,UAACqe,EAAAA,EAAIA,CAAAA,CAAE,GAAGd,CAAI,CAAEzd,sBAAoB,OAAOE,0BAAwB,yBACjE,WAACud,OAAAA,CAAKO,SAAUP,EAAKe,YAAY,CAACR,GAAW3d,UAAU,sBAErD,WAACD,MAAAA,CAAIC,UAAU,kDAEb,UAACoe,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,UAAUwW,OAAQ,CAAC,OAC1DtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,WAACC,EAAAA,EAASA,CAAAA,CAACxe,UAAU,oCACnB,UAACwhB,GAAAA,CAAQA,CAAAA,CAACxhB,UAAU,YAAY,QAGlC,UAACye,EAAAA,EAAWA,CAAAA,UACV,WAACW,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOuI,EAAMvI,KAAK,CAAE4Q,cAAerI,EAAM8H,QAAQ,WACvD,UAACQ,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAACV,YAAY,WAE3B,UAACW,EAAAA,EAAaA,CAAAA,UACXW,EAASpJ,GAAG,CAACrH,GAAW,WAACgQ,EAAAA,EAAUA,CAAAA,CAAkBjR,MAAOkS,OAAOjR,EAAQ1F,EAAE,YACzE0F,EAAQwB,QAAQ,CAAC,MAAIxB,EAAQ0B,KAAK,GADG1B,EAAQ1F,EAAE,UAM1D,UAACmV,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAGzE,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,WAAWwW,OAAQ,CAAC,CAC3DtH,OAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,WAACW,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOuI,EAAMvI,KAAK,CAAE4Q,cAAerI,EAAM8H,QAAQ,WACvD,UAACQ,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAACV,YAAY,aAE3B,UAACW,EAAAA,EAAaA,CAAAA,UACXQ,GAAUjJ,GAAG,CAACxK,GAAQ,UAACmT,EAAAA,EAAUA,CAAAA,CAAkBjR,MAAOlC,EAAKkC,KAAK,UAChElC,EAAKwP,KAAK,EADyBxP,EAAKkC,KAAK,UAMxD,UAAC0Q,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAGzE,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,cAAcwW,OAAQ,CAAC,OAC9DtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,cACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,WAACW,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOuI,EAAMvI,KAAK,EAAI,GAAI4Q,cAAerI,EAAM8H,QAAQ,WAC7D,UAACQ,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAACV,YAAY,WAE3B,WAACW,EAAAA,EAAaA,CAAAA,WACZ,UAACE,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,gBAAO,UACxB2R,EAAarJ,GAAG,CAAC/L,GAAe,WAAC0U,EAAAA,EAAUA,CAAAA,CAAsBjR,MAAOzD,EAAYhB,EAAE,WAClF,IAAI3E,KAAK2F,EAAYoH,eAAe,EAAE2H,kBAAkB,CAAC,SAAS,KACjC,UAAjC,OAAO/O,EAAY6E,SAAS,CAAgB7E,EAAY6E,SAAS,CAAC/H,IAAI,CAAG,SAF5BkD,EAAYhB,EAAE,WAOtE,UAACmV,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAGzE,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,UAAUwW,OAAQ,CAAC,OAC1DtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,WAACC,EAAAA,EAASA,CAAAA,CAACxe,UAAU,oCACnB,UAACuc,GAAAA,CAAYA,CAAAA,CAACvc,UAAU,YAAY,UAGtC,UAACye,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,OAAQ,GAAGyK,CAAK,KAE9B,UAACmI,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,qBAI3E,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,cAAcwW,OAAQ,CAAC,OAC9DtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACG,YAAY,YAAa,GAAG7H,CAAK,KAE1C,UAACmI,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAGzE,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,QAAQwW,OAAQ,CAAC,OACxDtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,YACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACkB,EAAAA,CAAQA,CAAAA,CAACd,YAAY,YAAY7e,UAAU,cAAc4f,KAAM,EAAI,GAAG5I,CAAK,KAE9E,UAACmI,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAEzE,UAAC8Z,EAAAA,SAASA,CAAAA,CAACha,sBAAoB,YAAYE,0BAAwB,kBAGnE,WAACE,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,8CACb,UAAC6Z,KAAAA,CAAG7Z,UAAU,iCAAwB,SACtC,WAAC8a,EAAAA,CAAMA,CAAAA,CAACvO,KAAK,SAAS0N,QAAQ,UAAUc,KAAK,KAAKC,QAvJhD,CAuJyDyG,IAtJvElY,EAAO,CACLqF,SAAU,YACVG,SAAU,GACVjJ,YAAa,GACboJ,SAAU,EACVG,UAAW,EACXC,aAAc,CAChB,EACF,EA8IkF3P,sBAAoB,SAASE,0BAAwB,0BACvH,UAAC6hB,EAAAA,CAAQA,CAAAA,CAAC1hB,UAAU,eAAeL,sBAAoB,WAAWE,0BAAwB,kBAAkB,aAMhH,UAACE,MAAAA,CAAIC,UAAU,qBACZ8gB,EAAO/J,GAAG,CAAC,CAACC,EAAOuK,IAAU,WAAC/H,EAAAA,EAAIA,CAAAA,CAAgBxZ,UAAU,gBACzD,WAACD,MAAAA,CAAIC,UAAU,4DAEb,UAACoe,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAM,CAAC,MAAM,EAAEyZ,EAAM,SAAS,CAAC,CAAEjD,OAAQ,CAAC,OAC9EtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,OACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,WAACW,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOuI,EAAMvI,KAAK,CAAE4Q,cAAerI,EAAM8H,QAAQ,WACvD,UAACQ,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAAAA,KAEd,UAACC,EAAAA,EAAaA,CAAAA,UACXS,GAAUlJ,GAAG,CAACxK,GAAQ,UAACmT,EAAAA,EAAUA,CAAAA,CAAkBjR,MAAOlC,EAAKkC,KAAK,UAChElC,EAAKwP,KAAK,EADyBxP,EAAKkC,KAAK,UAMxD,UAAC0Q,EAAAA,EAAWA,CAAAA,CAAAA,QAIlB,UAACf,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAM,CAAC,MAAM,EAAEyZ,EAAM,SAAS,CAAC,CAAEjD,OAAQ,CAAC,OAC9EtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACG,YAAY,OAAQ,GAAG7H,CAAK,KAErC,UAACmI,EAAAA,EAAWA,CAAAA,CAAAA,QAIlB,UAACf,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAM,CAAC,MAAM,EAAEyZ,EAAM,SAAS,CAAC,CAAEjD,OAAQ,CAAC,OAC9EtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,OACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,OAAOwQ,YAAY,IAAK,GAAG7H,CAAK,CAAE8H,SAAUC,GAAK/H,EAAM8H,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,GAAK,OAErI,UAAC0Q,EAAAA,EAAWA,CAAAA,CAAAA,QAIlB,UAACf,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAM,CAAC,MAAM,EAAEyZ,EAAM,UAAU,CAAC,CAAEjD,OAAQ,CAAC,CAC/EtH,OAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,OACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,IAAIwQ,YAAY,OAAQ,GAAG7H,CAAK,CAAE8H,SAAUC,GAAK/H,EAAM8H,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,GAAK,OAErI,UAAC0Q,EAAAA,EAAWA,CAAAA,CAAAA,QAIlB,UAACf,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAM,CAAC,MAAM,EAAEyZ,EAAM,aAAa,CAAC,CAAEjD,OAAQ,CAAC,CAClFtH,OAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,YACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,MAAMtQ,IAAI,IAAIW,IAAI,MAAM6P,YAAY,IAAK,GAAG7H,CAAK,CAAE8H,SAAUC,GAAK/H,EAAM8H,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,GAAK,OAE3I,UAAC0Q,EAAAA,EAAWA,CAAAA,CAAAA,QAIlB,UAACpf,MAAAA,CAAIC,UAAU,6BACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACvO,KAAK,SAAS0N,QAAQ,UAAUc,KAAK,KAAKC,QAAS,IAAMsG,EAAWC,GAAQ1B,SAAUiB,EAAOta,MAAM,EAAI,WAC7G,UAACmb,GAAAA,CAASA,CAAAA,CAAC3hB,UAAU,mBAM3B,UAACD,MAAAA,CAAIC,UAAU,gBACb,UAACoe,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAM,CAAC,MAAM,EAAEyZ,EAAM,YAAY,CAAC,CAAEjD,OAAQ,CAAC,OACjFtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,cACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACkB,EAAAA,CAAQA,CAAAA,CAACd,YAAY,YAAY7e,UAAU,cAAc4f,KAAM,EAAI,GAAG5I,CAAK,KAE9E,UAACmI,EAAAA,EAAWA,CAAAA,CAAAA,UAKpB,UAACpf,MAAAA,CAAIC,UAAU,2BACb,WAACkB,OAAAA,CAAKlB,UAAU,0CAAgC,OACzC2S,EAAanG,UAADmG,IAAe,CAAC,MACnC,IAAM/B,EAAOqQ,CAAY,CAACM,EAAM,CAChC,GAAI,CAAC3Q,EAAM,OAAO,EAClB,IAAMC,EAAY,CAACD,EAAK1B,QAAQ,GAAI,GAAM0B,EAAAA,SAAc,GAAI,EACtDE,EAAeD,EAAa,EAACD,EAAKtB,MAAN,MAAkB,EAAI,GAAK,IAAE,CAC/D,OAAOuB,EAAYC,EACrB,YAhGqCkG,EAAMhN,EAAE,QAuGrD,UAAC2P,EAAAA,SAASA,CAAAA,CAACha,sBAAoB,YAAYE,0BAAwB,kBAGnE,WAACE,MAAAA,CAAIC,UAAU,kDAEb,UAACoe,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,iBAAiBwW,OAAQ,CAAC,OACjEtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,WACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,IAAIwQ,YAAY,OAAQ,GAAG7H,CAAK,CAAE8H,SAAUC,GAAK/H,EAAM8H,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,GAAK,OAErI,UAACyQ,EAAAA,EAAeA,CAAAA,UAAC,kBAGjB,UAACC,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAGzE,UAACue,EAAAA,EAASA,CAAAA,CAACC,QAASjB,EAAKiB,OAAO,CAAEvW,KAAK,YAAYwW,OAAQ,CAAC,OAC5DtH,CAAK,CACN,GAAK,WAACuH,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,IAAIwQ,YAAY,OAAQ,GAAG7H,CAAK,CAAE8H,SAAUC,GAAK/H,EAAM8H,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,GAAK,OAErI,UAACyQ,EAAAA,EAAeA,CAAAA,UAAC,cAGjB,UAACC,EAAAA,EAAWA,CAAAA,CAAAA,MACDxf,sBAAoB,YAAYE,0BAAwB,kBAGzE,WAACE,MAAAA,CAAIC,UAAU,sBACb,UAAC4hB,GAAAA,CAAKA,CAAAA,CAACjiB,sBAAoB,QAAQE,0BAAwB,yBAAgB,SAC3E,WAACE,MAAAA,CAAIC,UAAU,iDACb,WAACD,MAAAA,CAAIC,UAAU,yCACb,UAACkB,OAAAA,UAAK,UACN,UAACA,OAAAA,UAAMyR,EAAanG,UAADmG,IAAe,CAAC2N,EAAiBzI,QAAQ,OAE9D,WAAC9X,MAAAA,CAAIC,UAAU,yCACb,UAACkB,OAAAA,UAAK,UACN,WAACA,OAAAA,WAAK,IAAEyR,EAAanG,UAADmG,IAAe,CAACwO,GAAyB,SAE/D,WAACphB,MAAAA,CAAIC,UAAU,yCACb,UAACkB,OAAAA,UAAK,QACN,WAACA,OAAAA,WAAK,IAAEyR,EAAanG,UAADmG,IAAe,CAACyO,GAAoB,SAE1D,UAACzH,EAAAA,SAASA,CAAAA,CAACha,sBAAoB,YAAYE,0BAAwB,kBACnE,WAACE,MAAAA,CAAIC,UAAU,+CACb,UAACkB,OAAAA,UAAK,SACN,UAACA,OAAAA,CAAKlB,UAAU,mBACb2S,EAAanG,UAADmG,IAAe,CAAC2N,EAAiBhI,WAAW,gBAQnE,WAACvY,MAAAA,CAAIC,UAAU,4BACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACvO,KAAK,SAASsT,SAAUlD,EAAc3c,UAAU,SAASL,sBAAoB,SAASE,0BAAwB,yBACnH8c,EAAe+D,EAAY,SAAW,SAAWA,EAAY,OAAS,SAExEhE,GAAY,UAAC5B,EAAAA,CAAMA,CAAAA,CAACvO,KAAK,SAAS0N,QAAQ,UAAUe,QAAS0B,EAAUmD,SAAUlD,WAAc,oBApTnG,IA4TX,CC3fO,SAASkF,GAAW,MACzBhX,CAAI,CACJ0P,QAAM,CACNC,SAAO,WACPiC,CAAS,CACO,EAChB,GAAM,CAAC0D,EAAU2B,EAAY,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EAChD,CAACuD,EAAc2B,EAAgB,CAAGlF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EAC5D,CAACwD,EAAY2B,EAAc,CAAGnF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EACtD,CAAClJ,EAASsO,EAAW,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GA+BjC6D,EAAY,CAAC,CAAC7V,EACpB,MAAO,UAACtK,EAAAA,EAAMA,CAAAA,CAACqa,KAAML,EAAQM,aAAcL,EAAS7a,sBAAoB,SAASC,wBAAsB,aAAaC,0BAAwB,2BACxI,WAACkB,EAAAA,EAAaA,CAAAA,CAACf,UAAU,yCAAyCL,sBAAoB,gBAAgBE,0BAAwB,4BAC5H,UAACsB,EAAAA,EAAYA,CAAAA,CAACxB,sBAAoB,eAAeE,0BAAwB,2BACvE,UAACwB,EAAAA,EAAWA,CAAAA,CAAC1B,sBAAoB,cAAcE,0BAAwB,2BACpE6gB,EAAY,CAAC,OAAO,EAAE7V,GAAMgH,WAAAA,CAAY,CAAG,YAI/C8B,EAAU,WAAC5T,MAAAA,CAAIC,UAAU,kDACtB,UAACD,MAAAA,CAAIC,UAAU,gEACf,UAACkB,OAAAA,CAAKlB,UAAU,sCAA6B,gBACtC,UAACkgB,GAAQA,CAACrV,IAADqV,CAAOrV,QAAQK,EAAWiV,SAAUA,EAAUC,aAAcA,EAAcC,WAAYA,EAAY5D,UAlBrGyF,CAkBgHnC,GAjBjItD,GACFA,EAAUyF,GAEZ1H,GACF,EAasJkC,SAAUlC,EAASD,QAAQ,QAGnL,iEC9CA,IAAM4H,GAA8C,CAClDzU,MAAO,CAAC,OAAQ,YAAY,CAC5BC,KAAM,CAAC,YAAa,YAAY,CAChCC,UAAW,CAAC,OAAQ,YAAY,CAChCC,KAAM,EAAE,CAERC,UAAW,EAAE,CAAC,CAIVsU,GAAe,CACnB1U,MAAO,CACLqO,MAAO,KACPsG,MAAO,WAP6C,iBAQpDrG,KAAMsG,GAAAA,CAAYA,CAClBxc,YAAa,SACf,EACA6H,KAAM,CACJoO,MAAO,MACPsG,MAAO,4BACPrG,KAAMuG,GAAAA,CAAQA,CACdzc,YAAa,UACf,EACA8H,UAAW,CACTmO,MAAO,MACPsG,MAAO,gCACPrG,KAAMwG,GAAAA,CAASA,CACf1c,YAAa,SACf,EACA+H,KAAM,CACJkO,MAAO,MACPsG,MAAO,8BACPrG,KAAMG,EAAAA,CAAcA,CACpBrW,YAAa,SACf,EACAgI,UAAW,CACTiO,MAAO,MACPsG,MAAO,0BACPrG,KAAMH,GAAAA,CAAKA,CACX/V,YAAa,OACf,CACF,EACO,SAAS2c,GAAkB,MAChC5X,CAAI,gBACJ6X,CAAc,SACdC,CAAO,CACgB,EACvB,GAAM,CACJC,eAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACtI,EAAQuI,EAAU,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/B,CAACkG,EAAgBC,EAAkB,CAAGnG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAChS,EAAKhD,MAAM,EAC1D,CAACiI,EAAOmT,EAAS,CAAGpG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAC7B,CAACqG,EAAYC,EAAc,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvCuG,EAAsBhB,EAAY,CAACvX,EAAKhD,MAAM,CAAC,CAC/Cwb,EAAuBlB,EAAiB,CAACtX,EAAKhD,MAAM,CAAC,EAAI,EAAE,CAC3Dyb,EAAkBV,EAAc,iBAAmBS,EAAqB7c,MAAM,CAAG,EACjF+c,EAAqB,UACzB,GAAIR,IAAmBlY,EAAKhD,MAAM,CAAE,YAClC9B,EAAAA,KAAKA,CAACI,OAAO,CAAC,YAGhB,GAAI,CAIF,GAHAgd,GAAc,GAGV,CAACE,EAAqBta,QAAQ,CAACga,GAAiB,YAClDhd,EAAAA,KAAKA,CAACtB,KAAK,CAAC,WAKd,GAAuB,SAAnBse,GAA6B,CAAClY,EAAK+N,eAAe,GAAI,EAAK,EAAG,YAChE7S,EAAAA,KAAKA,CAACtB,KAAK,CAAC,sBAGd,IAAM8F,EAA4B,CAChC1C,OAAQkb,CACV,EAGIjT,EAAMmB,IAAI,IAAI,CAChB1G,EAAWuF,KAAK,CAAGjF,EAAKiF,KAAK,CAAG,GAAGjF,EAAKiF,KAAK,CAAC;AAAA;AAAA,OAAW,EAAEA,EAAMmB,IAAI,IAAI,CAAG,CAAC,OAAO,EAAEnB,EAAMmB,IAAI,MAElG,IAAMuS,EAAc,MAAMva,EAASqB,MAADrB,IAAW,CAAC4B,EAAKb,EAAE,CAAEO,GACvDxE,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,UAAU,EAAEoc,EAAY,CAACW,EAAe,CAAChH,KAAK,EAAE,EAC3D2G,GACFA,EAAec,GAEjBV,EAAU,IACVG,EAAS,GACX,CAAE,MAAOxe,EAAO,CACd6B,QAAQ7B,KAAK,CAAC,gCAAiCA,GAC/C,IAAMsZ,EAAetZ,aAAiBiD,EAAkBjD,EAAM5C,OAAO,CAAG,GAAnB6F,YACrD3B,EAAAA,KAAKA,CAACtB,KAAK,CAACsZ,EACd,QAAU,CACRoF,GAAc,EAChB,CACF,EACMM,EAAoB5b,IACxB,OAAQA,GACN,IAAK,OACH,MAAO,iBACT,KAAK,YACH,MAAO,iBACT,KAAK,OACH,MAAO,oBACT,KAAK,YACH,MAAO,kBACT,SACE,OAAO,IACX,CACF,SACA,EAMO,EANH,CAMG,QAACtH,EAAAA,EAAMA,CAAAA,CAACqa,KAAML,EAAQM,aAAciI,EAAWnjB,sBAAoB,SAASC,wBAAsB,oBAAoBC,0BAAwB,oCACjJ,UAACc,EAAAA,EAAaA,CAAAA,CAAC+iB,OAAO,IAAC/jB,sBAAoB,gBAAgBE,0BAAwB,mCAChF8iB,GAAW,WAAC7H,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,eACvC,UAAC4I,EAAAA,CAAQA,CAAAA,CAAC3jB,UAAU,iBAAiB,YAK3C,WAACe,EAAAA,EAAaA,CAAAA,CAACf,UAAU,WAAWL,sBAAoB,gBAAgBE,0BAAwB,oCAC9F,WAACsB,EAAAA,EAAYA,CAAAA,CAACxB,sBAAoB,eAAeE,0BAAwB,oCACvE,UAACwB,EAAAA,EAAWA,CAAAA,CAAC1B,sBAAoB,cAAcE,0BAAwB,mCAA0B,WACjG,WAACyB,EAAAA,EAAiBA,CAAAA,CAAC3B,sBAAoB,oBAAoBE,0BAAwB,oCAA0B,SACpGgL,EAAKgH,UAAU,OAI1B,WAAC9R,MAAAA,CAAIC,UAAU,sBAEb,WAACD,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,sBAAsBL,sBAAoB,QAAQE,0BAAwB,mCAA0B,SACrH,WAACE,MAAAA,CAAIC,UAAU,iBACb,WAACga,EAAAA,CAAKA,CAAAA,CAACha,UAAWojB,EAAoBf,KAAK,CAAE1iB,sBAAoB,QAAQE,0BAAwB,oCAC/F,UAACujB,EAAoBpH,IAAI,EAAChc,UAAU,eAAeL,sBAAoB,2BAA2BE,0BAAwB,4BACzHujB,EAAoBrH,KAAK,IAE5B,UAAC5b,IAAAA,CAAEH,UAAU,8CACVojB,EAAoBtd,WAAW,SAMtC,WAAC/F,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAACgC,QAAQ,gBAAgB5jB,UAAU,sBAAsBL,sBAAoB,QAAQE,0BAAwB,mCAA0B,QAG7I,WAACuf,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOsU,EAAgB1D,cAAe5Q,GAASuU,EAAkBvU,GAAiC9O,sBAAoB,SAASE,0BAAwB,oCAC7J,UAACyf,EAAAA,EAAaA,CAAAA,CAACtf,UAAU,OAAOL,sBAAoB,gBAAgBE,0BAAwB,mCAC1F,UAAC0f,EAAAA,EAAWA,CAAAA,CAACV,YAAY,QAAQlf,sBAAoB,cAAcE,0BAAwB,8BAE7F,UAAC2f,EAAAA,EAAaA,CAAAA,CAAC7f,sBAAoB,gBAAgBE,0BAAwB,mCACxEwjB,EAAqBtM,GAAG,CAAClP,IAC1B,IAAMO,EAASga,EAAY,CAACva,EAAoC,CAC1D4X,EAAOrX,EAAO4T,IAAI,CACxB,MAAO,UAAC0D,EAAAA,EAAUA,CAAAA,CAAcjR,MAAO5G,WACjC,WAAC9H,MAAAA,CAAIC,UAAU,oCACb,UAACyf,EAAAA,CAAKzf,UAAU,YAChB,UAACkB,OAAAA,UAAMkH,EAAO2T,KAAK,OAHHlU,EAM1B,WAMHkb,IAAmBlY,EAAKhD,MAAM,EAAI4b,EAAiBV,IAAmB,WAACc,GAAAA,EAAKA,CAAAA,WACzE,UAACC,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,YAC7B,UAAC+jB,GAAAA,EAAgBA,CAAAA,UACdN,EAAiBV,QAKJ,SAAnBA,GAA6B,CAAClY,EAAK+N,eAAe,GAAI,EAAK,GAAK,WAACiL,GAAAA,EAAKA,CAAAA,CAAC5J,QAAQ,wBAC5E,UAAC6J,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,YAC7B,WAAC+jB,GAAAA,EAAgBA,CAAAA,WAAC,QACVpR,EAAanG,UAADmG,IAAe,CAAC9H,EAAK+N,eAAe,EAAI,GAAG,0BAMnE,WAAC7Y,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAACgC,QAAQ,QAAQ5jB,UAAU,sBAAsBL,sBAAoB,QAAQE,0BAAwB,mCAA0B,YAGrI,UAAC8f,EAAAA,CAAQA,CAAAA,CAAC3V,GAAG,QAAQ6U,YAAY,eAAepQ,MAAOqB,EAAOgP,SAAUC,GAAKkE,EAASlE,EAAEE,MAAM,CAACxQ,KAAK,EAAGzO,UAAU,mBAAmB4f,KAAM,EAAGjgB,sBAAoB,WAAWE,0BAAwB,+BAItM,WAACE,MAAAA,CAAIC,UAAU,4BACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACE,QAASuI,EAAoB1D,SAAUqD,GAAcH,IAAmBlY,EAAKhD,MAAM,EAAuB,SAAnBkb,GAA6B,CAAClY,EAAK+N,eAAe,GAAI,EAAK,EAAG5Y,UAAU,SAASL,sBAAoB,SAASE,0BAAwB,mCAClOqjB,EAAa,SAAW,SAE3B,UAACpI,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUe,QAAS,KACnC8H,EAAU,IACVE,EAAkBnY,EAAKhD,MAAM,EAC7Bob,EAAS,GACX,EAAGpD,SAAUqD,EAAYvjB,sBAAoB,SAASE,0BAAwB,mCAA0B,mBA9FvG,WAACma,EAAAA,CAAKA,CAAAA,CAACha,UAAWojB,EAAoBf,KAAK,WAC9C,UAACe,EAAoBpH,IAAI,EAAChc,UAAU,iBACnCojB,EAAoBrH,KAAK,GAmGlC,wCC5Oe,UAAqB,WAAW,eAAiB,oBAAoB,CAAC,CAAC,OAAO,CAAC,EAAI,iBAAkB,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,SAAU,KAAM,CAAO,OAAC,CAAC,CAAC,EC+BrN,IAAMiI,GAAe,CAAC,CACpBvV,MAAO,QACPsN,MAAO,IACT,EAAG,CACDtN,MAAO,OACPsN,MAAO,KACT,EAAG,CACDtN,MAAO,YACPsN,MAAO,KACT,EAAG,CACDtN,MAAO,OACPsN,MAAO,KACT,EAAG,CACDtN,MAAO,YACPsN,MAAO,KACT,EAAE,CACIiE,GAAY,CAAC,CACjBvR,MAAO,WADMuR,CAEbjE,MAAO,MACT,EAAG,CACDtN,MAAO,eACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,UACPsN,MAAO,MACT,EAAG,CACDtN,MAAO,aACPsN,MAAO,MACT,EAAE,CACK,SAASkI,GAAY,SAC1BC,CAAO,iBACPC,CAAe,UACfhE,EAAW,EAAE,WACbngB,CAAS,CACQ,EACjB,GAAM,CAACua,EAAQuI,EAAU,CAAGjG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/B,CAACuH,EAAcC,EAAgB,CAAGxH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAoBqH,GAC9DI,EAAmBC,OAAOC,MAAM,CAACN,GAASO,IAAI,CAAChW,QAAmBvD,IAAVuD,GAAiC,KAAVA,GAA0B,OAAVA,GAC/FiW,EAAoBH,OAAOC,MAAM,CAACN,GAASrN,MAAM,CAACpI,QAAmBvD,IAAVuD,GAAiC,KAAVA,GAA0B,OAAVA,GAAgBjI,MAAM,CACxHme,EAAqB,CAACzP,EAA8BzG,KAKxD4V,EAJmB,CACjB,GAAGD,CAAY,CACf,CAAClP,EAAI,CAAEzG,CACT,EAEF,CADkBmW,CAMZC,EAAe,KACnB,IAAMC,EAAkC,CAAC,EACzCT,EAAgBS,GAChBX,EAAgBW,GAChBhC,GAAU,EACZ,EACMiC,EAAoB,IACxB,IAAMH,EAAa,CACjB,GAAGV,CAAO,CAEZ,QAAOU,CAAU,CAAC1P,EAAI,CACtBiP,EAAgBS,EAClB,EACA,MAAO,WAAC7kB,MAAAA,CAAIC,UAAW,CAAC,UAAU,EAAEA,EAAAA,CAAW,CAAEJ,wBAAsB,cAAcC,0BAAwB,6BAEzG,WAACE,MAAAA,CAAIC,UAAU,oCACb,WAACD,MAAAA,CAAIC,UAAU,4BACb,UAACglB,GAAAA,CAAUA,CAAAA,CAAChlB,UAAU,mFAAmFL,sBAAoB,aAAaE,0BAAwB,qBAClK,UAAC6e,EAAAA,CAAKA,CAAAA,CAACG,YAAY,oBAAoBpQ,MAAOyV,EAAQxa,MAAM,EAAI,GAAIoV,SAAUC,GAAKoF,EAAgB,CACnG,GAAGD,CAAO,CACVxa,OAAQqV,EAAEE,MAAM,CAACxQ,KAAK,GACpBzO,UAAU,QAAQL,sBAAoB,QAAQE,0BAAwB,wBAI1E,WAAColB,GAAAA,EAAOA,CAAAA,CAACrK,KAAML,EAAQM,aAAciI,EAAWnjB,sBAAoB,UAAUE,0BAAwB,6BACpG,UAACqlB,GAAAA,EAAcA,CAAAA,CAACxB,OAAO,IAAC/jB,sBAAoB,iBAAiBE,0BAAwB,4BACnF,WAACib,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUja,UAAU,WAAWL,sBAAoB,SAASE,0BAAwB,6BAClG,UAACslB,GAAAA,CAAUA,CAAAA,CAACnlB,UAAU,eAAeL,sBAAoB,aAAaE,0BAAwB,qBAAqB,OAElH6kB,EAAoB,GAAK,UAAC1K,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYja,UAAU,kFAC1D0kB,SAKT,UAACU,GAAAA,EAAcA,CAAAA,CAACplB,UAAU,WAAWqlB,MAAM,MAAM1lB,sBAAoB,iBAAiBE,0BAAwB,4BAC5G,WAACE,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,8CACb,UAACslB,KAAAA,CAAGtlB,UAAU,uBAAc,SAC5B,UAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAAS,IAAM8H,GAAU,GAAQnjB,sBAAoB,SAASE,0BAAwB,4BACtH,UAACgc,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,UAAUL,sBAAoB,QAAQE,0BAAwB,0BAInF,UAAC8Z,EAAAA,SAASA,CAAAA,CAACha,sBAAoB,YAAYE,0BAAwB,qBAGnE,WAACE,MAAAA,CAAIC,UAAU,sBACb,WAAC4hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,0BAA0BL,sBAAoB,QAAQE,0BAAwB,6BAC7F,UAACyiB,GAAAA,CAAYA,CAAAA,CAACtiB,UAAU,UAAUL,sBAAoB,eAAeE,0BAAwB,qBAAqB,UAGpH,WAACuf,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAO2V,EAAavc,MAAM,EAAI,GAAIwX,cAAe5Q,GAASkW,EAAmB,SAAUlW,GAASvD,QAAYvL,sBAAoB,SAASE,0BAAwB,6BACvK,UAACyf,EAAAA,EAAaA,CAAAA,CAAC3f,sBAAoB,gBAAgBE,0BAAwB,4BACzE,UAAC0f,EAAAA,EAAWA,CAAAA,CAACV,YAAY,OAAOlf,sBAAoB,cAAcE,0BAAwB,uBAE5F,WAAC2f,EAAAA,EAAaA,CAAAA,CAAC7f,sBAAoB,gBAAgBE,0BAAwB,6BACzE,UAAC6f,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,GAAG9O,sBAAoB,aAAaE,0BAAwB,4BAAmB,SAChGmkB,GAAajN,GAAG,CAAClP,GAAU,UAAC6X,EAAAA,EAAUA,CAAAA,CAAoBjR,MAAO5G,EAAO4G,KAAK,UACzE5G,EAAOkU,KAAK,EAD4BlU,EAAO4G,KAAK,YAQ/D,WAAC1O,MAAAA,CAAIC,UAAU,sBACb,UAAC4hB,GAAAA,CAAKA,CAAAA,CAACjiB,sBAAoB,QAAQE,0BAAwB,4BAAmB,SAC9E,WAACuf,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAO2V,EAAazZ,QAAQ,EAAI,GAAI0U,cAAe5Q,GAASkW,EAAmB,WAAYlW,GAASvD,QAAYvL,sBAAoB,SAASE,0BAAwB,6BAC3K,UAACyf,EAAAA,EAAaA,CAAAA,CAAC3f,sBAAoB,gBAAgBE,0BAAwB,4BACzE,UAAC0f,EAAAA,EAAWA,CAAAA,CAACV,YAAY,OAAOlf,sBAAoB,cAAcE,0BAAwB,uBAE5F,WAAC2f,EAAAA,EAAaA,CAAAA,CAAC7f,sBAAoB,gBAAgBE,0BAAwB,6BACzE,UAAC6f,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,GAAG9O,sBAAoB,aAAaE,0BAAwB,4BAAmB,SAChGmgB,GAAUjJ,GAAG,CAACxK,GAAQ,UAACmT,EAAdM,EAAwBN,CAAAA,CAAkBjR,MAAOlC,EAAKkC,KAAK,UAChElC,EAAKwP,KAAK,EADyBxP,EAAKkC,KAAK,YAQvD0R,EAAS3Z,MAAM,CAAG,GAAK,WAACzG,MAAAA,CAAIC,UAAU,sBACnC,WAAC4hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,oCACf,UAACwhB,GAAAA,CAAQA,CAAAA,CAACxhB,UAAU,YAAY,QAGlC,WAACof,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAO2V,EAAaza,SAAS,EAAI,GAAI0V,cAAe5Q,GAASkW,EAAmB,YAAalW,QAASvD,aAC5G,UAACoU,EAAAA,EAAaA,CAAAA,UACZ,UAACC,EAAAA,EAAWA,CAAAA,CAACV,YAAY,WAE3B,WAACW,EAAAA,EAAaA,CAAAA,WACZ,UAACE,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,YAAG,SACpB0R,EAASpJ,GAAG,CAACrH,GAAW,WAACgQ,EAAAA,EAAUA,CAAAA,CAAkBjR,MAAOiB,EAAQ1F,EAAE,WAClE0F,EAAQwB,QAAQ,CAAC,MAAIxB,EAAQ0B,KAAK,GADG1B,EAAQ1F,EAAE,YAQ5D,WAACjK,MAAAA,CAAIC,UAAU,sBACb,WAAC4hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,0BAA0BL,sBAAoB,QAAQE,0BAAwB,6BAC7F,UAAC0c,GAAAA,CAAYA,CAAAA,CAACvc,UAAU,UAAUL,sBAAoB,eAAeE,0BAAwB,qBAAqB,UAGpH,WAACE,MAAAA,CAAIC,UAAU,mCACb,WAACD,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,gCAAgCL,sBAAoB,QAAQE,0BAAwB,4BAAmB,SACxH,UAAC6e,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,OAAOkC,MAAO2V,EAAaxa,QAAQ,EAAI,GAAIkV,SAAUC,GAAK4F,EAAmB,WAAY5F,EAAEE,MAAM,CAACxQ,KAAK,OAAIvD,GAAYlL,UAAU,UAAUL,sBAAoB,QAAQE,0BAAwB,wBAE7M,WAACE,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,gCAAgCL,sBAAoB,QAAQE,0BAAwB,4BAAmB,SACxH,UAAC6e,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,OAAOkC,MAAO2V,EAAava,MAAM,EAAI,GAAIiV,SAAUC,GAAK4F,EAAmB,SAAU5F,EAAEE,MAAM,CAACxQ,KAAK,OAAIvD,GAAYlL,UAAU,UAAUL,sBAAoB,QAAQE,0BAAwB,8BAM7M,WAACE,MAAAA,CAAIC,UAAU,sBACb,WAAC4hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,0BAA0BL,sBAAoB,QAAQE,0BAAwB,6BAC7F,UAAC0lB,GAAgBA,CAACvlB,UAAU,EAAXulB,QAAqB5lB,sBAAoB,mBAAmBE,0BAAwB,qBAAqB,UAG5H,WAACE,MAAAA,CAAIC,UAAU,mCACb,WAACD,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,gCAAgCL,sBAAoB,QAAQE,0BAAwB,4BAAmB,SACxH,UAAC6e,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,IAAIwQ,YAAY,OAAOpQ,MAAO2V,EAAa5S,SAAS,EAAI,GAAIsN,SAAUC,GAAK4F,EAAmB,YAAa3F,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,QAAKvD,GAAYlL,UAAU,UAAUL,sBAAoB,QAAQE,0BAAwB,wBAEpQ,WAACE,MAAAA,WACC,UAAC6hB,GAAAA,CAAKA,CAAAA,CAAC5hB,UAAU,gCAAgCL,sBAAoB,QAAQE,0BAAwB,4BAAmB,SACxH,UAAC6e,EAAAA,CAAKA,CAAAA,CAACnS,KAAK,SAASoS,KAAK,OAAOtQ,IAAI,IAAIwQ,YAAY,MAAMpQ,MAAO2V,EAAa3S,SAAS,EAAI,GAAIqN,SAAUC,GAAK4F,EAAmB,YAAa3F,WAAWD,EAAEE,MAAM,CAACxQ,KAAK,QAAKvD,GAAYlL,UAAU,UAAUL,sBAAoB,QAAQE,0BAAwB,8BAKvQ,UAAC8Z,EAAAA,SAASA,CAAAA,CAACha,sBAAoB,YAAYE,0BAAwB,qBAGnE,WAACE,MAAAA,CAAIC,UAAU,uBACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACE,QAjJD,CAiJUwK,IAhJ7BrB,EAAgBC,GAChBtB,GAAU,EACZ,EA8I6C9iB,UAAU,SAASL,sBAAoB,SAASE,0BAAwB,4BAAmB,SAG1H,UAACib,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUe,QAAS6J,EAAcllB,sBAAoB,SAASE,0BAAwB,4BAAmB,qBAUlIykB,GAAoB,WAACvkB,MAAAA,CAAIC,UAAU,iCAC/BkkB,EAAQrc,MAAM,EAAI,WAACmS,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYja,UAAU,oCAA0B,OACzEgkB,GAAajZ,IAAI,CAAC0a,GAAKA,EAAEhX,KAAK,GAAKyV,EAAQrc,MAAM,GAAGkU,MACzD,UAAC2J,SAAAA,CAAO1K,QAAS,IAAM+J,EAAkB,UAAW/kB,UAAU,kDAC5D,UAAC6b,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,iBAItBkkB,EAAQvZ,QAAQ,EAAI,WAACqP,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYja,UAAU,oCAA0B,OAC3EggB,GAAUjV,IAAI,CAAC4a,GAAKA,EAAElX,KAAK,GAAKyV,CAAvBlE,CAA+BrV,QAAQ,GAAGoR,MACxD,UAAC2J,SAAAA,CAAO1K,QAAS,IAAM+J,EAAkB,YAAa/kB,UAAU,kDAC9D,UAAC6b,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,iBAItBkkB,EAAQva,SAAS,EAAI,WAACqQ,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYja,UAAU,oCAA0B,OAC5EmgB,EAASpV,IAAI,CAAC5K,GAAKA,EAAE6J,EAAE,GAAKka,EAAQva,SAAS,GAAGuH,SACrD,UAACwU,SAAAA,CAAO1K,QAAS,IAAM+J,EAAkB,aAAc/kB,UAAU,kDAC/D,UAAC6b,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,iBAIrBkkB,CAAAA,EAAQta,QAAQ,EAAIsa,EAAQra,MAAAA,GAAW,WAACmQ,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYja,UAAU,oCAA0B,OAC/FkkB,EAAQta,QAAQ,EAAI,KAAK,MAAIsa,EAAQra,MAAM,EAAI,KACpD,UAAC6b,SAAAA,CAAO1K,QAAS,KACrB+J,EAAkB,YAClBA,EAAkB,SACpB,EAAG/kB,UAAU,kDACL,UAAC6b,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,iBAIrBkkB,MAAsBhZ,IAAtBgZ,EAAQ1S,SAAS,EAAkB0S,KAAsBhZ,MAAduG,SAAS,CAAa,EAAM,WAACuI,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,YAAYja,UAAU,oCAA0B,OAC/HkkB,EAAQ1S,SAAS,CAAGmB,EAAanG,UAADmG,IAAe,CAACuR,EAAQ1S,SAAS,EAAI,IAAI,MAAI0S,EAAQzS,SAAS,CAAGkB,EAAanG,UAADmG,IAAe,CAACuR,EAAQzS,SAAS,EAAI,IACvJ,UAACiU,SAAAA,CAAO1K,QAAS,KACrB+J,EAAkB,aAClBA,EAAkB,YACpB,EAAG/kB,UAAU,kDACL,UAAC6b,GAAAA,CAAKA,CAAAA,CAAC7b,UAAU,iBAItBskB,GAAoB,UAACxJ,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAAS6J,EAAc7kB,UAAU,oBAAW,cAK7G,CC3OA,IAAM4lB,GAAc,CAAC,QACnB/d,CAAM,CAGP,GAiCQ,UAACmS,EAAAA,CAAKA,CAAAA,CAACha,UAAW6lB,CAhCF,IACrB,OAAQhe,EAAOie,WAAW,IACxB,IAAK,OACH,MAAO,gDACT,KAAK,UACH,MAAO,mDACT,KAAK,UACH,MAAO,6CACT,KAAK,UACH,MAAO,0CACT,KAAK,IACH,MAAO,6CAGX,EACF,EAiBwCje,GAASlI,sBAAoB,QAAQC,wBAAsB,cAAcC,0BAAwB,4BACpIkmB,CAjBiB,IACpB,OAAQle,EAAOie,WAAW,IACxB,IAAK,OACH,MAAO,KACT,KAAK,UACH,MAAO,KACT,KAAK,UACH,MAAO,MACT,KAAK,UACH,MAAO,IACT,KAAK,YACH,MAAO,KACT,SACE,OAAOje,CACX,EACF,EAEmBA,KAKfme,GAAgB,CAAC,MACrBzZ,CAAI,CAGL,GA6BQ,UAACyN,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,UAAUja,UAAWimB,CA5BtB,IACnB,OAAQ1Z,EAAKuZ,WAAW,IACtB,IAAK,YACH,MAAO,mDACT,KAAK,eACH,MAAO,mDACT,KAAK,UACH,MAAO,6CACT,KAAK,aACH,MAAO,6CACT,SACE,MAAO,6CACX,EACF,EAewDvZ,GAAO5M,sBAAoB,QAAQC,wBAAsB,gBAAgBC,0BAAwB,4BACpJqmB,CAfe,IAClB,OAAQ3Z,EAAKuZ,WAAW,IACtB,IAAK,YACH,MAAO,IACT,KAAK,eACH,MAAO,IACT,KAAK,UACH,MAAO,IACT,KAAK,aACH,MAAO,IACT,SACE,OAAOvZ,CACX,EACF,EAEiBA,KAGZ,SAAS4Z,KACd,GAAM,eACJvD,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACuD,EAAOC,EAAS,CAAGxJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,EAAE,EACvC,CAAClJ,EAASsO,EAAW,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACpY,EAAO6hB,EAAS,CAAGzJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAACqH,EAASqC,EAAW,CAAG1J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAoB,CAAC,GACrD,CAAC2J,EAAaC,EAAe,CAAG5J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACzC,CAAC6J,EAAYC,EAAc,CAAG9J,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACvC,CAAC+J,EAAYC,EAAc,CAAGhK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,CAACsD,EAAU2B,EAAY,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAIpC,EAAE,EACA,CAACiK,EAAwBC,EAA0B,CAAGlK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MAC5E,CAACmK,EAAqBC,EAAuB,CAAGpK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACzD,CAACqK,EAAqBC,EAAuB,CAAGtK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,MACtE,CAACuK,EAAkBC,EAAoB,CAAGxK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACyK,EAAgBC,EAAkB,CAAG1K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAG/C3T,EAAa,MAAOI,EAAe,CAAC,CAAEke,EAAoC,CAAC,CAAC,IAChF,GAAI,CACFvF,EAAoB,IAAT3Y,GACXgd,EAAS,MACT,IAAM/d,EAAW,MAAMU,EAASC,MAADD,IAAW,CAAC,MACzCK,EACAG,MAAO,GACPC,OAAQ8d,EAAe9d,MAAM,EAAIwB,OACjCrD,OAAQ2f,EAAe3f,MAAM,OAAIqD,EACjCvB,UAAW6d,EAAe7d,SAAS,OAAIuB,EACvCtB,SAAU4d,EAAe5d,QAAQ,OAAIsB,EACrCrB,OAAQ2d,EAAe3d,MAAM,EAAIqB,MACnC,GACAmb,EAAS9d,EAASuC,IAAI,EACtB2b,EAAele,EAASe,IAAI,EAC5Bqd,EAAcpe,EAASme,UAAU,CACnC,CAAE,MAAOe,EAAK,CACZnhB,QAAQ7B,KAAK,CAAC,yBAA0BgjB,GACxC,IAAM1J,EAAe0J,aAAe/f,EAAkB+f,EAAI5lB,OAAO,CAAG,GAAjB6F,aACnD4e,EAASvI,GACThY,EAAAA,KAAKA,CAACtB,KAAK,CAACsZ,EACd,QAAU,CACRkE,EAAW,IACX4E,EAAc,GAChB,CACF,EA4BMa,EAAgB,UACpBb,GAAc,GACd,MAAM3d,EAAWsd,EAAatC,EAChC,EAKMyD,EAAgB,KACpBR,EAAuB,MACvBI,EAAkB,IAClBF,EAAoB,GACtB,EACMO,EAAiB,IACrB7hB,EAAAA,KAAKA,CAACM,IAAI,CAAC,CAAC,MAAM,EAAEwE,EAAKgH,UAAU,EAAE,CAEvC,EACMgW,EAAiB,IACrBV,EAAuBtc,GACvB0c,EAAkB,IAClBF,GAAoB,EACtB,EACMS,EAAgB,IACpBf,EAA0Blc,GAC1Boc,GAAuB,EACzB,EAqBM1D,EAAsBC,IAE1B6C,EAAS0B,GAAaA,EAAUhR,GAAG,CAAClM,GAAQA,EAAKb,EAAE,GAAKwZ,EAAYxZ,EAAE,CAAGwZ,EAAc3Y,IACvF9E,EAAAA,KAAKA,CAACC,OAAO,CAAC,UAChB,EACMgiB,EAAoB1e,IACxBJ,EAAWI,EAAM4a,EACnB,SACA,EACS,OADI,CACJ,EAACnkB,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6EACf,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,kBAIzCyE,GAA0B,GAAG,CAApB2hB,EAAM5f,MAAM,CAChB,UAACzG,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACioB,EAAAA,CAAeA,CAAAA,CAACjoB,UAAU,wCAC3B,UAAC6Z,KAAAA,CAAG7Z,UAAU,mDAA0C,SACxD,UAACG,IAAAA,CAAEH,UAAU,sCAA8ByE,IAC3C,WAACqW,EAAAA,CAAMA,CAAAA,CAACE,QAAS0M,EAAezN,QAAQ,oBACtC,UAACiO,EAAAA,CAAWA,CAAAA,CAACloB,UAAU,iBAAiB,aAM9ComB,GAAoB,GAAd5f,MAAM,CACP,UAACzG,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACE,EAAWA,CAACF,QAADE,EAAW,iDACvB,UAAC2Z,KAAAA,CAAG7Z,UAAU,oCAA2B,SACzC,UAACG,IAAAA,CAAEH,UAAU,sCAA6B,iBAG1C,UAACmoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,0BACzB,WAACtN,EAAAA,CAAMA,CAAAA,CAACE,QAAS2M,YACf,UAACjG,EAAAA,CAAQA,CAAAA,CAAC1hB,UAAU,iBAAiB,iBAO1C,WAACD,MAAAA,CAAIC,UAAU,YAAYJ,wBAAsB,cAAcC,0BAAwB,6BAE1F,UAACokB,GAAWA,CAACC,OAADD,CAAUC,EAASC,gBA1FP,CA0FwBkE,GAzFlD9B,EAAW3B,GACX6B,EAAe,EACjB,EAuFyEtG,SAAUA,EAAUxgB,sBAAoB,SAxFlD,KAwFgEE,0BAAwB,qBAGnJ,WAACE,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,CAAIC,UAAU,wCACb,WAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS0M,EAAe7H,SAAU+G,EAAYjnB,sBAAoB,SAASE,0BAAwB,6BACrI,UAACqoB,EAAAA,CAAWA,CAAAA,CAACloB,UAAW,CAAC,aAAa,EAAE4mB,EAAa,eAAiB,IAAI,CAAEjnB,sBAAoB,cAAcE,0BAAwB,qBAAqB,QAG7J,WAACE,MAAAA,CAAIC,UAAU,0CAAgC,KAC1ComB,EAAM5f,MAAM,CAAC,aAGpB,UAAC2hB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,iBAAiBzoB,sBAAoB,iBAAiBE,0BAAwB,4BACvG,WAACib,EAAAA,CAAMA,CAAAA,CAACE,QAAS2M,EAAehoB,sBAAoB,SAASE,0BAAwB,6BACnF,UAAC6hB,EAAAA,CAAQA,CAAAA,CAAC1hB,UAAU,eAAeL,sBAAoB,WAAWE,0BAAwB,qBAAqB,eAOpH4E,GAAS2hB,EAAM5f,MAAM,CAAG,GAAK,UAACzG,MAAAA,CAAIC,UAAU,0DACzC,WAACD,MAAAA,CAAIC,UAAU,8BACb,UAACioB,EAAAA,CAAeA,CAAAA,CAACjoB,UAAU,8BAC3B,UAACkB,OAAAA,CAAKlB,UAAU,gCAAwByE,SAK9C,UAAC1E,MAAAA,CAAIC,UAAU,sBACZomB,EAAMrP,GAAG,CAAClM,GAAQ,WAAC9K,MAAAA,CAAkBC,UAAU,4CAC5C,WAACD,MAAAA,CAAIC,UAAU,6CACb,WAACD,MAAAA,CAAIC,UAAU,sBACb,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACslB,KAAAA,CAAGtlB,UAAU,+BAAuB6K,EAAKgH,UAAU,GACpD,UAACmU,GAAAA,CAAczZ,KAAM1B,EAAKF,QAAQ,GAClC,UAAC8X,GAAiBA,CAAC5X,KAAMA,EAAM6X,MAAbD,SAA6Bc,EAAoBZ,QAAS,UAACiD,GAAAA,CAAY/d,OAAQgD,EAAKhD,MAAM,QAE9G,WAAC9H,MAAAA,CAAIC,UAAU,0CACb,WAACG,IAAAA,WAAE,OAA6B,UAAxB,OAAO0K,EAAK6E,OAAO,CAAgB7E,EAAK6E,OAAO,CAACwB,QAAQ,CAAG,UACnE,WAAC/Q,IAAAA,WAAE,OAAK0K,EAAK/E,WAAW,IACxB,WAAC3F,IAAAA,WAAE,SAAO,IAAIkF,KAAKwF,EAAKyd,SAAS,EAAEvO,kBAAkB,CAAC,kBAI1D,WAACha,MAAAA,CAAIC,UAAU,iCACb,UAACD,MAAAA,CAAIC,UAAU,iCACZ2S,EAAanG,UAADmG,IAAe,CAAC9H,EAAKyN,WAAW,IAE7CzN,GAAK+N,eAAe,GAAI,EAAK,GAAK,WAAC7Y,MAAAA,CAAIC,UAAU,iCAAuB,OACjE2S,EAAanG,UAADmG,IAAe,CAAC9H,EAAK+N,eAAe,EAAI,MAE3D/N,CAAAA,EAAKsP,UAAU,GAAI,EAAK,GAAK,WAACpa,MAAAA,CAAIC,UAAU,mCAAyB,OAC9D2S,EAAanG,UAADmG,IAAe,CAAC9H,EAAKsP,UAAU,EAAI,YAK5D,WAACpa,MAAAA,CAAIC,UAAU,4DACb,WAACD,MAAAA,CAAIC,UAAU,0CAAgC,SACtC,IAAIqF,KAAKwF,EAAKkF,OAAO,EAAEgK,kBAAkB,CAAC,YAGnD,WAACha,MAAAA,CAAIC,UAAU,oCACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAAS,IAAM4M,EAAe/c,YAC9D,UAAC0d,EAAAA,CAAOA,CAAAA,CAACvoB,UAAU,cAErB,UAACmoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,wBACzB,UAACtN,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,QAAQc,KAAK,KAAKC,QAAS,IAAM6M,EAAehd,YAC9D,UAAC8Y,EAAAA,CAAQA,CAAAA,CAAC3jB,UAAU,gBAGtB6K,CAAAA,EAAK+N,eAAe,GAAI,EAAK,GAAK,UAACuP,EAAAA,EAAcA,CAAAA,CAACC,WAAW,8BAC3D,WAACtN,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS,IAAM8M,EAAcjd,aAC/D,UAACsR,EAAAA,CAAcA,CAAAA,CAACnc,UAAU,iBAAiB,kBA5C5B6K,EAAKb,EAAE,KAsDrC0c,EAAa,GAAK,WAAC3mB,MAAAA,CAAIC,UAAU,4DAC9B,UAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS,IAAMgN,EAAiBxB,EAAc,GAAI3G,SAAU2G,GAAe,WAAG,QAGlH,WAACtlB,OAAAA,CAAKlB,UAAU,0CAAgC,KAC3CwmB,EAAY,QAAME,EAAW,QAElC,UAAC5L,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS,IAAMgN,EAAiBxB,EAAc,GAAI3G,SAAU2G,GAAeE,WAAY,WAM/H,UAAC5G,GAAaA,CAACjV,KAAMic,EAAwBvM,EAA/BuF,KAAuCkH,EAAqBxM,QAjK7C,CAiKsDgO,IAhKrFvB,GAAuB,GACvBF,EAA0B,KAC5B,EA8JiHtK,UAtKpF,CAsK+FgM,GArK1H1iB,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,aAAa,EAAEyM,EAAQK,aAAa,EAAI,OAAO,EAE9D5J,EAAWsd,EAAatC,EAC1B,EAkKkJvkB,sBAAoB,gBAAgBE,0BAAwB,qBAG1M,UAACgiB,GAAUA,CAAChX,KAAMqc,CAAPrF,CAA4BtH,OAAQ6M,EAAkB5M,QA1JvC,CA0JgDkO,IAzJ5ErB,GAAoB,GACpBF,EAAuB,MACvBI,GAAkB,EACpB,EAsJqG9K,UAhK3E,CAgKsFkM,GA9J9G5iB,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,EAAE,EAAE4N,EADa,KAAO,KACb,SAAS,EAAE/I,EAAKgH,UAAU,EAAE,EAEtD3I,EAAWsd,EAAatC,EAC1B,EA2JmIvkB,sBAAoB,aAAaE,0BAAwB,uBAE9L,iBCzXO,SAAS+oB,GAAkB,iBAChCC,CAAe,WACf7oB,CAAS,CACc,EACvB,GAAM,eACJ4iB,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACzC,EAAc2B,EAAgB,CAAGlF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA8B,EAAE,EAC1E,CAAClJ,EAASsO,EAAW,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACiM,EAAYC,EAAc,CAAGlM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MACtD,CAACmM,EAAkBC,EAAoB,CAAGpM,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,aAC3DqM,EAA6B,UACjC,GAAI,CACFjH,GAAW,GAGX,IAAMkH,EAAuB,MAAM3gB,MAAM,+CACnC4gB,EAAmB,MAAMD,EAAqBxgB,IAAI,GACxD,GAAI,CAACwgB,EAAqB1gB,EAAE,CAC1B,CAD4B,KAClBd,MAAMyhB,EAAiB3kB,KAAK,EAAI,gCAO5C,IAAM4kB,EAAgBC,CAHA,MAAMrgB,EAASC,MAADD,IAAW,CAAC,CAC9CQ,MAAO,GACT,IACoCqB,IAAI,CAGlCye,EAA0DH,EAAiBte,IAAI,CAACiM,GAAG,CAAC,IACxF,IAAMyS,EAAeH,EAActe,IAAI,CAACF,GAAoC,UAA5B,OAAOA,EAAKG,WAAW,EAAiBH,EAAKG,WAAW,EAAEhB,KAAOgB,EAAYhB,EAAE,EAC/H,MAAO,CACL,GAAGgB,CAAW,CACdC,QAAS,CAAC,CAACue,EACXpe,OAAQoe,GAAcxf,GACtB6H,WAAY2X,GAAc3X,UAC5B,CACF,GACAkQ,EAAgBwH,EAClB,CAAE,MAAO9kB,EAAO,CACd6B,QAAQ7B,KAAK,CAAC,gCAAiCA,GAC/CkN,EAAqB+B,MAAM,CAACM,gBAAgB,EAC9C,QAAU,CACRiO,GAAW,EACb,CACF,EAMA,GAAI,CAACW,EAAc,kBACjB,CADoC,KAC7B,UAAC7iB,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAAC8jB,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,2CAC7B,UAAC6Z,KAAAA,CAAG7Z,UAAU,sCAA6B,SAC3C,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,sBAM7C,IAAMypB,EAAqB,MAAOze,IAChC,GAAI,CACF+d,EAAc/d,EAAYhB,EAAE,EAC5B,IAAMzB,EAAW,MAAMU,EAASwB,MAADxB,iBAAwB,CAAC+B,EAAYhB,EAAE,CAAEgf,GACxErX,EAAqB9G,IAAI,CAACJ,uBAAuB,CAAClC,EAAU,IAAIlD,KAA5CsM,EAA6DS,eAAe,EAAE2H,kBAAkB,CAAC,UAGrHgI,EAAgB2H,GAAQA,EAAK3S,GAAG,CAAC4S,GAAOA,EAAI3f,EAAE,GAAKgB,EAAYhB,EAAE,CAAG,CAClE,GAAG2f,CAAG,CACN1e,SAAS,EACTG,OAAQ7C,EAASyB,EAAE,CACnB6H,WAAYtJ,EAASsJ,UAAU,EAC7B8X,IACAd,GACFA,EAAgBtgB,EAEpB,CAAE,MAAO9D,EAAO,CAHO,QAIbA,KAAK,CAAC,2BAA4BA,GAC1C,IAAMsZ,EAAetZ,aAAiBiD,EAAkBjD,EAAM5C,OAAO,IAAhB6F,EAAmBwD,EACxEyG,EAAqB9G,IAAI,CAACyH,WAAW,CAACyL,EACxC,QAAU,CACRgL,EAAc,KAChB,CACF,EACMa,EALkBjY,IAMtB,OAAQ9J,GACN,IAAK,YACH,MAAO,6BACT,KAAK,YACH,MAAO,2BACT,KAAK,YACH,MAAO,+BACT,KAAK,YACH,MAAO,yBACT,SACE,MAAO,2BACX,CACF,EACMgiB,EAA4B,GAQzBC,CAPQ,CACbC,UAAW,MACXnc,UAAW,MACXK,UAAW,MACXH,UAAW,MACX,UAAW,MACb,CACa,CAACjG,EAA8B,EAAIA,EAElD,GAAI8L,EACF,MAAO,UAAC5T,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6EACf,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,qBAI7C,IAAMgqB,EAAuB5J,EAAavJ,MAAM,CAAC8S,GAAO,CAACA,EAAI1e,OAAO,EACpE,MAAO,WAAClL,MAAAA,CAAIC,UAAW,CAAC,UAAU,EAAEA,EAAAA,CAAW,CAAEJ,wBAAsB,oBAAoBC,0BAAwB,oCAE/G,WAACE,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,WAACE,KAAAA,CAAGD,UAAU,sEACZ,UAACE,EAAWA,CAACF,QAADE,EAAW,SAASP,sBAAoB,cAAcE,0BAAwB,4BAA4B,YAGxH,UAACM,IAAAA,CAAEH,UAAU,iCAAwB,qBAIvC,WAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUe,QAASkO,EAA4BrJ,SAAUlM,EAAShU,sBAAoB,SAASE,0BAAwB,oCACrI,UAACqoB,EAAAA,CAAWA,CAAAA,CAACloB,UAAU,eAAeL,sBAAoB,cAAcE,0BAAwB,4BAA4B,WAMhI,WAAC2Z,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,oCACvD,WAAC4Z,EAAAA,EAAUA,CAAAA,CAAC9Z,sBAAoB,aAAaE,0BAAwB,oCACnE,UAACme,EAAAA,EAASA,CAAAA,CAAChe,UAAU,UAAUL,sBAAoB,YAAYE,0BAAwB,mCAA0B,WACjH,UAACoe,EAAAA,EAAeA,CAAAA,CAACte,sBAAoB,kBAAkBE,0BAAwB,mCAA0B,mBAI3G,UAAC+Z,EAAAA,EAAWA,CAAAA,CAACja,sBAAoB,cAAcE,0BAAwB,mCACrE,WAACuf,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAOua,EAAkB3J,cAAe4J,EAAqBtpB,sBAAoB,SAASE,0BAAwB,oCACxH,UAACyf,EAAAA,EAAaA,CAAAA,CAACtf,UAAU,OAAOL,sBAAoB,gBAAgBE,0BAAwB,mCAC1F,UAAC0f,EAAAA,EAAWA,CAAAA,CAAC5f,sBAAoB,cAAcE,0BAAwB,8BAEzE,WAAC2f,EAAAA,EAAaA,CAAAA,CAAC7f,sBAAoB,gBAAgBE,0BAAwB,oCACzE,UAAC6f,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,YAAY9O,sBAAoB,aAAaE,0BAAwB,mCAA0B,SACjH,UAAC6f,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,eAAe9O,sBAAoB,aAAaE,0BAAwB,mCAA0B,SACpH,UAAC6f,EAAAA,EAAUA,CAAAA,CAACjR,MAAM,aAAa9O,sBAAoB,aAAaE,0BAAwB,mCAA0B,oBAO1H,WAACE,MAAAA,CAAIC,UAAU,kDACb,UAACwZ,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,mCACvD,UAAC+Z,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,OAAOL,sBAAoB,cAAcE,0BAAwB,mCACtF,WAACE,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,4CACZogB,EAAa5Z,MAAM,GAEtB,UAACzG,MAAAA,CAAIC,UAAU,yCAAgC,iBAKrD,UAACwZ,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,mCACvD,UAAC+Z,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,OAAOL,sBAAoB,cAAcE,0BAAwB,mCACtF,WAACE,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6CACZogB,EAAavJ,MAAM,CAAC8S,GAAOA,EAAI1e,OAAO,EAAEzE,MAAM,GAEjD,UAACzG,MAAAA,CAAIC,UAAU,yCAAgC,iBAKrD,UAACwZ,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,mCACvD,UAAC+Z,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,OAAOL,sBAAoB,cAAcE,0BAAwB,mCACtF,WAACE,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8CACZgqB,EAAqBxjB,MAAM,GAE9B,UAACzG,MAAAA,CAAIC,UAAU,yCAAgC,oBAOtB,IAAhCgqB,EAAqBxjB,MAAM,CAAS,UAACgT,EAAAA,EAAIA,CAAAA,UACtC,UAACI,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,gBACrB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACwiB,GAAAA,CAASA,CAAAA,CAACxiB,UAAU,0CACrB,UAAC6Z,KAAAA,CAAG7Z,UAAU,oCAA2B,cACzC,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,6BAKjC,WAACD,MAAAA,CAAIC,UAAU,sBACvB,UAAC6Z,KAAAA,CAAG7Z,UAAU,iCAAwB,aAEtC,UAACD,MAAAA,CAAIC,UAAU,sBACZgqB,EAAqBjT,GAAG,CAAC/L,GAAe,UAACwO,EAAAA,EAAIA,CAAAA,CAAsBxZ,UAAU,6CAC1E,UAAC4Z,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,eACrB,WAACD,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,CAAIC,UAAU,6BAEb,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACga,EAAAA,CAAKA,CAAAA,CAACha,UAAW4pB,EAA0B5e,EAAYnD,MAAM,WAC3DgiB,EAA0B7e,EAAYnD,MAAM,IAE/C,WAAC3G,OAAAA,CAAKlB,UAAU,0CAAgC,SACvCgL,EAAYhB,EAAE,OAKzB,WAACjK,MAAAA,CAAIC,UAAU,yEACb,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACuc,GAAAA,CAAYA,CAAAA,CAACvc,UAAU,kCACxB,UAACkB,OAAAA,UACE,IAAImE,KAAK2F,EAAYoH,eAAe,EAAE2H,kBAAkB,CAAC,cAI9D,WAACha,MAAAA,CAAIC,UAAU,oCACb,UAACwhB,GAAAA,CAAQA,CAAAA,CAACxhB,UAAU,kCACpB,UAACkB,OAAAA,UACiC,UAA/B,OAAO8J,EAAY0E,OAAO,CAAgB1E,EAAY0E,OAAO,CAACwB,QAAQ,CAAG,YAI9E,WAACnR,MAAAA,CAAIC,UAAU,oCACb,UAACiqB,GAAAA,CAAeA,CAAAA,CAACjqB,UAAU,kCAC3B,UAACkB,OAAAA,UACmC,UAAjC,OAAO8J,EAAY6E,SAAS,CAAgB7E,EAAY6E,SAAS,CAAC/H,IAAI,CAAG,YAI9E,WAAC/H,MAAAA,CAAIC,UAAU,oCACb,UAACulB,GAAgBA,CAACvlB,UAAU,EAAXulB,gCACjB,UAACrkB,OAAAA,UACEyR,EAAanG,UAADmG,IAAe,CAAC3H,EAAYkf,KAAK,EAAI,cAO1D,UAACnqB,MAAAA,CAAIC,UAAU,gBACb,UAAC8a,EAAAA,CAAMA,CAAAA,CAACE,QAAS,IAAMyO,EAAmBze,GAAc6U,SAAUiJ,IAAe9d,EAAYhB,EAAE,CAAEhK,UAAU,oBACxG8oB,IAAe9d,EAAYhB,EAAE,CAAG,iCAC7B,UAACjK,MAAAA,CAAIC,UAAU,mEAAuE,YAElF,iCACJ,UAACE,EAAWA,CAACF,QAADE,EAAW,iBAAiB,oBArDJ8K,EAAYhB,EAAE,QAiExE,WAAC6Z,GAAAA,EAAKA,CAAAA,CAAClkB,sBAAoB,QAAQE,0BAAwB,oCACzD,UAACikB,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,UAAUL,sBAAoB,oBAAoBE,0BAAwB,4BACvG,UAACkkB,GAAAA,EAAgBA,CAAAA,CAACpkB,sBAAoB,mBAAmBE,0BAAwB,mCAA0B,qDAKnH,CClSO,SAASsqB,GAAe,WAC7BnqB,CAAS,CACW,EACpB,GAAM,eACJ4iB,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACuH,EAAUC,EAAY,CAAGxN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EAChD,CAAClJ,EAASsO,EAAW,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACpY,EAAO6hB,EAAS,CAAGzJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAACyN,EAAYC,EAAc,CAAG1N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAAC2N,EAAiBC,EAAmB,CAAG5N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,MACjE,CAAC6N,EAAmBC,EAAqB,CAAG9N,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrD,CAAC+J,EAAYC,EAAc,CAAGhK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAGvC1R,EAAgB,MAAOzB,IAC3B,GAAI,CACFuY,GAAW,GACXqE,EAAS,MAOT,IAAIsE,EAAuBriB,CANV,MAAMsV,EAAY1S,SAAD0S,IAAc,CAAC,CAC/CpU,MAAO,GACP5B,OAAQ,WACV,CADsB,CACtB,EAGoCiD,IAAI,CAAC+L,MAAM,CAACpE,GAAWA,EAAQK,WAJd,EAI2B,CAG5EpJ,KACFkhB,EAAuBA,CADb,CACkC/T,MAAM,CAACpE,GAAWA,EAAQK,aAAa,EAAEgT,cAAc/c,SAASW,EAAOoc,WAAW,KAA+B,UAAxB,OAAOrT,EAAQ5H,IAAI,EAAiB4H,EAAQ5H,IAAI,CAACgH,UAAU,EAAEiU,cAAc/c,SAASW,EAAOoc,WAAW,KAAkC,UAA3B,OAAOrT,EAAQ/C,OAAO,EAAiB+C,EAAQ/C,OAAO,CAACwB,QAAQ,EAAE4U,cAAc/c,SAASW,EAAOoc,WAAW,MAE9VuE,EAAYO,EACd,CAAE,MAAOnD,EAAK,CACZnhB,QAAQ7B,KAAK,CAAC,4BAA6BgjB,GAC3C,IAAM1J,EAAe0J,aAAe/f,EAAkB+f,EAAI5lB,OAAO,CAAG,GAAjB6F,aACnD4e,EAASvI,GACThY,EAAAA,KAAKA,CAACtB,KAAK,CAACsZ,EACd,QAAU,CACRkE,GAAW,GACX4E,GAAc,EAChB,CACF,EAgBMa,EAAgB,UACpBb,GAAc,GACd,MAAM1b,EAAcmf,EACtB,EACMO,EAAqBpY,IACzBgY,EAAmBhY,GACnBkY,GAAqB,EACvB,EACMG,EAAqB,IAEzBL,EAAmBhY,GACnBkY,GAAqB,EAEvB,EACMI,EAAmBX,EAASvT,MAAM,CAACpE,GAAWA,EAAQK,aAAa,EAAEgT,cAAc/c,SAASuhB,EAAWxE,WAAW,KAAOrT,EAAQG,aAAa,CAACkT,WAAW,GAAG/c,QAAQ,CAACuhB,EAAWxE,WAAW,KAAkC,UAA3B,OAAOrT,EAAQ/C,OAAO,EAAiB+C,EAAQ/C,OAAO,CAACwB,QAAQ,CAAC4U,WAAW,GAAG/c,QAAQ,CAACuhB,EAAWxE,WAAW,YACtT,EACS,OADI,CACJ,EAAC/lB,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6EACf,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,kBAIzCyE,GAA6B,GAAG,CAAvB2lB,EAAS5jB,MAAM,CACnB,UAACzG,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACioB,EAAAA,CAAeA,CAAAA,CAACjoB,UAAU,wCAC3B,UAAC6Z,KAAAA,CAAG7Z,UAAU,mDAA0C,SACxD,UAACG,IAAAA,CAAEH,UAAU,sCAA8ByE,IAC3C,WAACqW,EAAAA,CAAMA,CAAAA,CAACE,QAAS0M,EAAezN,QAAQ,oBACtC,UAACiO,EAAAA,CAAWA,CAAAA,CAACloB,UAAU,iBAAiB,aAM3C,WAACD,MAAAA,CAAIC,UAAW,CAAC,UAAU,EAAEA,EAAAA,CAAW,CAAEJ,wBAAsB,iBAAiBC,0BAAwB,gCAE5G,UAACE,MAAAA,CAAIC,UAAU,6CACb,WAACD,MAAAA,WACC,WAACE,KAAAA,CAAGD,UAAU,sEACZ,UAACE,EAAWA,CAACF,QAADE,EAAW,SAASP,sBAAoB,cAAcE,0BAAwB,wBAAwB,UAGpH,UAACM,IAAAA,CAAEH,UAAU,iCAAwB,sBAOzC,WAACD,MAAAA,CAAIC,UAAU,oDACb,WAACD,MAAAA,CAAIC,UAAU,oDACb,WAACD,MAAAA,CAAIC,UAAU,4BACb,UAACglB,GAAAA,CAAUA,CAAAA,CAAChlB,UAAU,mFAAmFL,sBAAoB,aAAaE,0BAAwB,wBAClK,UAAC6e,EAAAA,CAAKA,CAAAA,CAACG,YAAY,sBAAsBpQ,MAAO6b,EAAYxL,SAAUC,GAAKwL,EAAcxL,EAAEE,MAAM,CAACxQ,KAAK,EAAGzO,UAAU,QAAQL,sBAAoB,QAAQE,0BAAwB,2BAElL,UAACib,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS0M,EAAe7H,SAAU+G,EAAYjnB,sBAAoB,SAASE,0BAAwB,+BACrI,UAACqoB,EAAAA,CAAWA,CAAAA,CAACloB,UAAW,CAAC,QAAQ,EAAE4mB,EAAa,eAAiB,IAAI,CAAEjnB,sBAAoB,cAAcE,0BAAwB,6BAIrI,WAACE,MAAAA,CAAIC,UAAU,0CAAgC,KAC1C+qB,EAAiBvkB,MAAM,CAAC,aAK9B/B,GAAS2lB,EAAS5jB,MAAM,CAAG,GAAK,UAACzG,MAAAA,CAAIC,UAAU,0DAC5C,WAACD,MAAAA,CAAIC,UAAU,8BACb,UAACioB,EAAAA,CAAeA,CAAAA,CAACjoB,UAAU,8BAC3B,UAACkB,OAAAA,CAAKlB,UAAU,gCAAwByE,SAKjB,IAA5BsmB,EAAiBvkB,MAAM,CAAS,UAACzG,MAAAA,CAAIC,UAAU,iDAC5C,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACE,EAAWA,CAACF,QAADE,EAAW,iDACvB,UAAC2Z,KAAAA,CAAG7Z,UAAU,oCAA2B,SACzC,UAACG,IAAAA,CAAEH,UAAU,iCACVsqB,EAAa,YAAc,mBAGzB,UAACvqB,MAAAA,CAAIC,UAAU,oDACrB+qB,EAAiBhU,GAAG,CAACtE,GAAW,WAAC+G,EAAAA,EAAIA,CAAAA,CAAkBxZ,UAAU,8CAC9D,UAACyZ,EAAAA,EAAUA,CAAAA,CAACzZ,UAAU,gBACpB,WAACD,MAAAA,CAAIC,UAAU,6CACb,WAACD,MAAAA,WACC,UAACie,EAAAA,EAASA,CAAAA,CAAChe,UAAU,+BAClByS,EAAQK,aAAa,GAExB,WAACmL,EAAAA,EAAeA,CAAAA,CAACje,UAAU,oBAAU,SAC5ByS,EAAQG,aAAa,OAGhC,UAACoH,EAAAA,CAAKA,CAAAA,CAACC,QAAmC,cAA1BxH,EAAQyH,aAAa,CAAmB,UAAY,qBACjEvH,EAAa5E,UAAD4E,UAAqB,CAACF,EAAQyH,aAAa,SAK9D,WAACN,EAAAA,EAAWA,CAAAA,CAAC5Z,UAAU,sBACrB,WAACD,MAAAA,CAAIC,UAAU,8BACb,WAACD,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,QACxC,UAACkB,OAAAA,UAC6B,UAA3B,OAAOuR,EAAQ/C,OAAO,CAAgB+C,EAAQ/C,OAAO,CAACwB,QAAQ,CAAG,YAGtE,WAACnR,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAMyR,EAAa5F,UAAD4F,UAAqB,CAACF,EAAQpH,aAAa,OAEhE,WAACtL,MAAAA,CAAIC,UAAU,iCACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,UACxC,UAACkB,OAAAA,UAAM,IAAImE,KAAKoN,EAAQqH,WAAW,EAAEC,kBAAkB,CAAC,cAE1D,WAACha,MAAAA,CAAIC,UAAU,6CACb,UAACkB,OAAAA,CAAKlB,UAAU,iCAAwB,QACxC,UAACkB,OAAAA,CAAKlB,UAAU,0BACb2S,EAAanG,UAADmG,IAAe,CAACF,EAAQhG,MAAM,UAKjD,WAAC1M,MAAAA,CAAIC,UAAU,kDACb,WAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS,IAAM6P,EAAkBpY,GAAUzS,UAAU,mBACvF,UAACuoB,EAAAA,CAAOA,CAAAA,CAACvoB,UAAU,iBAAiB,QAGtC,UAACmoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,+BACzB,UAACtN,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAAS,IAAM8P,EAAmBrY,YACpE,UAACkJ,GAAWA,CAAC3b,OAAD2b,GAAW,wBAhDSlJ,EAAQzI,EAAE,KAyD1D,UAACsQ,GAAaA,CAAC7H,QAAS+X,CAAVlQ,CAA2BC,OAAQmQ,EAAmBlQ,QAAS,KAC7EmQ,GAAqB,GACrBF,EAAmB,KACrB,EAAG9qB,sBAAoB,gBAAgBE,0BAAwB,0BAEnE,CC3NA,OAAe,OAAoB,CAAC,CAAW,qBAAa,eAAgB,CAAC,CAAC,OAAO,CAAC,EAAI,CAA4E,+EAAM,QAAQ,EAAE,CAAC,MAAO,EAAC,EAAI,4EAA6E,KAAM,OAAO,EAAC,CAAE,CAAC,OAAO,CAAC,EAAI,4EAA4E,IAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,WAAW,IAAM,OAAO,EAAE,CAAC,cC6C1a,SAASmrB,KACd,GAAM,CACJpI,eAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACoI,EAASC,EAAW,CAAGrO,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAmB,CACvDsO,aAAc,KACdC,eAAgB,KAChBC,oBAAqB,IACvB,GACM,CAAC1X,EAASsO,EAAW,CAAGpF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACpY,EAAO6hB,EAAS,CAAGzJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAACyO,EAAcC,EAAgB,CAAG1O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAAIxX,OAAOwb,WAAW,GAAGnS,KAAK,CAAC,IAAI,CAAC,EAAE,EACjF,CAAC8c,EAAeC,EAAiB,CAAG5O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CACjD7Q,KAAM,IAAI3G,OAAOgL,WAAW,GAC5BpE,MAAO,IAAI5G,OAAOqmB,QAAQ,GAAK,CACjC,GACM,CAAC9E,EAAYC,EAAc,CAAGhK,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC8O,EAAe,UACnB,GAAI,CACF1J,GAAW,GACXqE,EAAS,MACT,GAAM,CAAC6E,EAAcC,EAAgBC,EAAoB,CAAG,MAAM9jB,QAAQqkB,GAAG,CAAC,CAACC,EAAWhgB,QAADggB,OAAgB,CAACP,GAAcQ,KAAK,CAAC,IAAM,MAAOD,EAAW9f,QAAD8f,SAAkB,CAACL,EAAcxf,IAAI,CAAEwf,EAAcvf,KAAK,EAAE6f,KAAK,CAAC,IAAM,MAAOD,EAAW3f,QAAD2f,cAAuB,GAAGC,KAAK,CAAC,IAAM,MAAM,EAC1RZ,EAAW,cACTC,iBACAC,sBACAC,CACF,EACF,CAAE,MAAO5D,EAAK,CACZnhB,QAAQ7B,KAAK,CAAC,qCAAsCgjB,GACpD,IAAM1J,EAAe0J,aAAe/f,EAAkB+f,EAAI5lB,OAAO,CAAG,GAAjB6F,cACnD4e,EAASvI,GACThY,EAAAA,KAAKA,CAACtB,KAAK,CAACsZ,EACd,QAAU,CACRkE,GAAW,GACX4E,GAAc,EAChB,CACF,EAMA,GAAI,CAACjE,EAAc,6BACjB,CAD+C,KACxC,UAAC7iB,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAAC8jB,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,2CAC7B,UAAC6Z,KAAAA,CAAG7Z,UAAU,sCAA6B,SAC3C,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,wBAU7C,IAAM+rB,EAAuB,IAC3B,OAAQjjB,GACN,IAAK,OASL,QARE,OAAOmT,CACT,KAAK,EADYA,KAEf,OAAOE,EAAAA,CAAcA,KAClB,SACL,IAAK,SACH,OAAOC,CACT,KAAK,UADoBA,CAEvB,OAAOE,EAGX,CACF,SACA,EACS,EANoBA,CAMpB,IADI,CACJ,EAACvc,MAAAA,CAAIC,UAAU,iDAClB,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6EACf,UAACG,IAAAA,CAAEH,UAAU,iCAAwB,oBAItC,WAACD,MAAAA,CAAIC,UAAU,YAAYJ,wBAAsB,qBAAqBC,0BAAwB,oCAEjG,WAACE,MAAAA,CAAIC,UAAU,8CACb,WAACD,MAAAA,WACC,WAACE,KAAAA,CAAGD,UAAU,sEACZ,UAACgsB,GAAYA,CAAChsB,QAADgsB,EAAW,SAASrsB,sBAAoB,eAAeE,0BAAwB,4BAA4B,UAG1H,UAACM,IAAAA,CAAEH,UAAU,iCAAwB,wBAIvC,WAAC8a,EAAAA,CAAMA,CAAAA,CAACb,QAAQ,UAAUc,KAAK,KAAKC,QAvCpB,CAuC6B0M,IAtCjDb,EAAc,IACd8E,GACF,EAoCkE9L,SAAU+G,EAAYjnB,sBAAoB,SAASE,0BAAwB,oCACrI,UAACqoB,EAAAA,CAAWA,CAAAA,CAACloB,UAAW,CAAC,aAAa,EAAE4mB,EAAa,eAAiB,IAAI,CAAEjnB,sBAAoB,cAAcE,0BAAwB,4BAA4B,aAMtK,WAACE,MAAAA,CAAIC,UAAU,oCACb,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACuc,GAAAA,CAAYA,CAAAA,CAACvc,UAAU,UAAUL,sBAAoB,eAAeE,0BAAwB,4BAC7F,UAACqB,OAAAA,CAAKlB,UAAU,+BAAsB,UACtC,UAACisB,QAAAA,CAAM1f,KAAK,OAAOkC,MAAO6c,EAAcxM,SAAUC,GAAKwM,EAAgBxM,EAAEE,MAAM,CAACxQ,KAAK,EAAGzO,UAAU,2CAEpG,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACkB,OAAAA,CAAKlB,UAAU,+BAAsB,UACtC,WAACof,EAAAA,EAAMA,CAAAA,CAAC3Q,MAAO,GAAG+c,EAAcxf,IAAI,CAAC,CAAC,EAAEwf,EAAcvf,KAAK,EAAE,CAAEoT,cAAe5Q,IAC9E,GAAM,CAACzC,EAAMC,EAAM,CAAGwC,EAAMC,KAAK,CAAC,KAAKqI,GAAG,CAACmV,QAC3CT,EAAiB,MACfzf,EACAC,OACF,EACF,EAAGtM,sBAAoB,SAASE,0BAAwB,oCACpD,UAACyf,EAAAA,EAAaA,CAAAA,CAACtf,UAAU,OAAOL,sBAAoB,gBAAgBE,0BAAwB,mCAC1F,UAAC0f,EAAAA,EAAWA,CAAAA,CAAC5f,sBAAoB,cAAcE,0BAAwB,8BAEzE,UAAC2f,EAAAA,EAAaA,CAAAA,CAAC7f,sBAAoB,gBAAgBE,0BAAwB,mCACxEsY,MAAMgU,IAAI,CAAC,CACZ3lB,OAAQ,EACV,EAAG,CAAC4lB,EAAG5U,KACL,IAAMvL,EAAQuL,EAAI,EACZxL,EAAO,IAAI3G,OAAOgL,WAAW,GACnC,MAAO,WAACqP,EAAAA,EAAUA,CAAAA,CAA0BjR,MAAO,GAAGzC,EAAK,CAAC,EAAEC,EAAAA,CAAO,WAC9DD,EAAK,IAAEC,EAAM,MADI,GAAGD,EAAK,CAAC,EAAEC,EAAAA,CAAO,CAG5C,cAOLxH,GAAS,UAAC1E,MAAAA,CAAIC,UAAU,0DACrB,WAACD,MAAAA,CAAIC,UAAU,8BACb,UAAC8jB,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,8BAC7B,UAACkB,OAAAA,CAAKlB,UAAU,gCAAwByE,SAK9C,WAAC+U,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,oCACvD,WAAC4Z,EAAAA,EAAUA,CAAAA,CAAC9Z,sBAAoB,aAAaE,0BAAwB,oCACnE,WAACme,EAAAA,EAASA,CAAAA,CAAChe,UAAU,0BAA0BL,sBAAoB,YAAYE,0BAAwB,oCACrG,UAACwsB,GAAAA,CAAcA,CAAAA,CAACrsB,UAAU,UAAUL,sBAAoB,iBAAiBE,0BAAwB,4BAA4B,WAG/H,WAACoe,EAAAA,EAAeA,CAAAA,CAACte,sBAAoB,kBAAkBE,0BAAwB,oCAC5E,IAAIwF,KAAKimB,GAAcvR,kBAAkB,CAAC,SAAS,eAGxD,UAACH,EAAAA,EAAWA,CAAAA,CAACja,sBAAoB,cAAcE,0BAAwB,mCACpEorB,EAAQE,YAAY,CAAG,WAACprB,MAAAA,CAAIC,UAAU,sBACnC,WAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6CACZ2S,EAAanG,UAADmG,IAAe,CAACsY,EAAQE,YAAY,CAACmB,YAAY,IAEhE,UAACvsB,MAAAA,CAAIC,UAAU,yCAAgC,WAEjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8BACZirB,EAAQE,YAAY,CAACoB,YAAY,GAEpC,UAACxsB,MAAAA,CAAIC,UAAU,yCAAgC,YAEjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8BACZirB,EAAQE,YAAY,CAACoB,YAAY,CAAG,EAAI5Z,EAAanG,UAADmG,IAAe,CAACsY,EAAQE,YAAY,CAACmB,YAAY,CAAGrB,EAAQE,YAAY,CAACoB,YAAY,EAAI5Z,EAAanG,UAADmG,IAAe,CAAC,KAE5K,UAAC5S,MAAAA,CAAIC,UAAU,yCAAgC,eAInD,UAAC2Z,EAAAA,SAASA,CAAAA,CAAAA,GAEV,WAAC5Z,MAAAA,WACC,UAACulB,KAAAA,CAAGtlB,UAAU,4BAAmB,WACjC,UAACD,MAAAA,CAAIC,UAAU,gEACZukB,OAAOiI,OAAO,CAACvB,EAAQE,YAAY,CAACrP,cAAc,EAAE/E,GAAG,CAAC,CAAC,CAACjO,EAAQyG,EAAK,IAC1E,IAAMkQ,EAAOsM,EAAqBjjB,GAClC,MAAO,WAAC/I,MAAAA,CAAiBC,UAAU,yEAC3B,WAACD,MAAAA,CAAIC,UAAU,oCACb,UAACyf,EAAAA,CAAKzf,UAAU,YAChB,UAACkB,OAAAA,CAAKlB,UAAU,+BACb2S,EAAa5F,UAAD4F,UAAqB,CAAC7J,QAGvC,WAAC/I,MAAAA,CAAIC,UAAU,uBACb,UAACD,MAAAA,CAAIC,UAAU,iCACZ2S,EAAanG,UAADmG,IAAe,CAACpD,EAAK9C,MAAM,IAE1C,WAAC1M,MAAAA,CAAIC,UAAU,0CACZuP,EAAKkd,KAAK,CAAC,aAZP3jB,EAgBnB,WAGO,UAAC/I,MAAAA,CAAIC,UAAU,kDAAyC,kBAOvE,WAACwZ,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,oCACvD,WAAC4Z,EAAAA,EAAUA,CAAAA,CAAC9Z,sBAAoB,aAAaE,0BAAwB,oCACnE,WAACme,EAAAA,EAASA,CAAAA,CAAChe,UAAU,0BAA0BL,sBAAoB,YAAYE,0BAAwB,oCACrG,UAACmsB,GAAYA,CAAChsB,QAADgsB,EAAW,UAAUrsB,sBAAoB,eAAeE,0BAAwB,4BAA4B,YAG3H,WAACoe,EAAAA,EAAeA,CAAAA,CAACte,sBAAoB,kBAAkBE,0BAAwB,oCAC5E2rB,EAAcxf,IAAI,CAAC,IAAEwf,EAAcvf,KAAK,CAAC,eAG9C,UAAC2N,EAAAA,EAAWA,CAAAA,CAACja,sBAAoB,cAAcE,0BAAwB,mCACpEorB,EAAQG,cAAc,CAAG,WAACrrB,MAAAA,CAAIC,UAAU,sBACrC,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,6CACZ2S,EAAanG,UAADmG,IAAe,CAACsY,EAAQG,cAAc,CAACkB,YAAY,IAElE,UAACvsB,MAAAA,CAAIC,UAAU,yCAAgC,aAGjD,UAAC2Z,EAAAA,SAASA,CAAAA,CAAAA,GAEV,WAAC5Z,MAAAA,WACC,UAACulB,KAAAA,CAAGtlB,UAAU,4BAAmB,WACjC,UAACD,MAAAA,CAAIC,UAAU,8CACZirB,EAAQG,cAAc,CAACsB,cAAc,CAAC3V,GAAG,CAAC4V,GAAO,WAAC5sB,MAAAA,CAAmBC,UAAU,4EAC5E,UAACkB,OAAAA,CAAKlB,UAAU,mBACb,IAAIqF,KAAKsnB,EAAI7gB,IAAI,EAAEiO,kBAAkB,CAAC,WAEzC,UAAC7Y,OAAAA,CAAKlB,UAAU,uBACb2S,EAAanG,UAADmG,IAAe,CAACga,EAAIC,OAAO,MALcD,EAAI7gB,IAAI,WAUjE,UAAC/L,MAAAA,CAAIC,UAAU,kDAAyC,kBAOvE,WAACwZ,EAAAA,EAAIA,CAAAA,CAAC7Z,sBAAoB,OAAOE,0BAAwB,oCACvD,WAAC4Z,EAAAA,EAAUA,CAAAA,CAAC9Z,sBAAoB,aAAaE,0BAAwB,oCACnE,WAACme,EAAAA,EAASA,CAAAA,CAAChe,UAAU,0BAA0BL,sBAAoB,YAAYE,0BAAwB,oCACrG,UAACikB,GAAAA,CAAiBA,CAAAA,CAAC9jB,UAAU,UAAUL,sBAAoB,oBAAoBE,0BAAwB,4BAA4B,UAGrI,UAACoe,EAAAA,EAAeA,CAAAA,CAACte,sBAAoB,kBAAkBE,0BAAwB,mCAA0B,mBAI3G,UAAC+Z,EAAAA,EAAWA,CAAAA,CAACja,sBAAoB,cAAcE,0BAAwB,mCACpEorB,EAAQI,mBAAmB,CAAG,WAACtrB,MAAAA,CAAIC,UAAU,sBAC1C,WAACD,MAAAA,CAAIC,UAAU,kDACb,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8CACZ2S,EAAanG,UAADmG,IAAe,CAACsY,EAAQI,mBAAmB,CAACwB,gBAAgB,IAE3E,UAAC9sB,MAAAA,CAAIC,UAAU,yCAAgC,aAEjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,2CACZ2S,EAAanG,UAADmG,IAAe,CAACsY,EAAQI,mBAAmB,CAACyB,aAAa,IAExE,UAAC/sB,MAAAA,CAAIC,UAAU,yCAAgC,YAEjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,8BACZirB,EAAQI,mBAAmB,CAAC0B,UAAU,GAEzC,UAAChtB,MAAAA,CAAIC,UAAU,yCAAgC,YAEjD,WAACD,MAAAA,CAAIC,UAAU,wBACb,UAACD,MAAAA,CAAIC,UAAU,2CACZirB,EAAQI,mBAAmB,CAAC2B,iBAAiB,GAEhD,UAACjtB,MAAAA,CAAIC,UAAU,yCAAgC,eAIlDirB,EAAQI,mBAAmB,CAACjF,KAAK,CAAC5f,MAAM,CAAG,GAAK,iCAC7C,UAACmT,EAAAA,SAASA,CAAAA,CAAAA,GACV,WAAC5Z,MAAAA,WACC,UAACulB,KAAAA,CAAGtlB,UAAU,4BAAmB,WACjC,UAACD,MAAAA,CAAIC,UAAU,8CACZirB,EAAQI,mBAAmB,CAACjF,KAAK,CAACvP,MAAM,CAAChM,GAAQA,EAAKoiB,WAAW,CAAG,GAAGlW,GAAG,CAAClM,GAAQ,WAAC9K,MAAAA,CAAkBC,UAAU,oEAC7G,WAACD,MAAAA,WACC,UAACA,MAAAA,CAAIC,UAAU,+BAAuB6K,EAAKgH,UAAU,GACrD,UAAC9R,MAAAA,CAAIC,UAAU,yCAAiC6K,EAAK6E,OAAO,MAE9D,WAAC3P,MAAAA,CAAIC,UAAU,uBACb,UAACD,MAAAA,CAAIC,UAAU,sCACZ2S,EAAanG,UAADmG,IAAe,CAAC9H,EAAK4B,MAAM,IAE1C,WAACuN,EAAAA,CAAKA,CAAAA,CAACC,QAAQ,cAAcja,UAAU,oBAAU,MAC3C6K,EAAKoiB,WAAW,CAAC,aAViEpiB,EAAKb,EAAE,cAiBtG,UAACjK,MAAAA,CAAIC,UAAU,kDAAyC,oBAM7E,CCxWA,OAAe,QAAqB,UAAW,gBAAkB,qBAAqB,CAAC,CAAC,OAAO,CAAC,EAAI,iFAAkF,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,YAAY,CAAM,UAAQ,GAAE,CAAC,OAAO,CAAC,EAAI,WAAW,IAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,gBAAgB,CAAM,UAAQ,EAAC,CAAC,ECevW,SAASI,GAAY,CAC1B8sB,aAAa,OAAO,CACpBltB,WAAS,CACQ,EACjB,GAAM,eACJ4iB,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACsK,EAAWC,EAAa,CAAGvQ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAACqQ,GAM3C,MAAO,UAACntB,MAAAA,CAAIC,UAAWA,EAAWJ,wBAAsB,cAAcC,0BAAwB,4BAC1F,WAACwtB,EAAAA,EAAIA,CAAAA,CAAC5e,MAAO0e,EAAW9N,cAAe+N,EAAcptB,UAAU,YAAYL,sBAAoB,OAAOE,0BAAwB,6BAC5H,WAACytB,EAAAA,EAAQA,CAAAA,CAACttB,UAAU,yCAAyCL,sBAAoB,WAAWE,0BAAwB,6BAClH,WAAC0tB,EAAAA,EAAWA,CAAAA,CAAC9e,MAAM,QAAQzO,UAAU,0BAA0BL,sBAAoB,cAAcE,0BAAwB,6BACvH,UAACK,EAAWA,CAACF,QAADE,EAAW,UAAUP,sBAAoB,cAAcE,0BAAwB,qBAC3F,UAACqB,OAAAA,CAAKlB,UAAU,4BAAmB,SACnC,UAACkB,OAAAA,CAAKlB,UAAU,qBAAY,UAG9B,UAACmoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,iBAAiBzoB,sBAAoB,iBAAiBE,0BAAwB,4BACvG,WAAC0tB,EAAAA,EAAWA,CAAAA,CAAC9e,MAAM,WAAWzO,UAAU,0BAA0BL,sBAAoB,cAAcE,0BAAwB,6BAC1H,UAAC2tB,GAAiBA,CAACxtB,UAAU,GAAXwtB,OAAqB7tB,sBAAoB,oBAAoBE,0BAAwB,qBACvG,UAACqB,OAAAA,CAAKlB,UAAU,4BAAmB,SACnC,UAACkB,OAAAA,CAAKlB,UAAU,qBAAY,YAIhC,UAACmoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,sBAAsBzoB,sBAAoB,iBAAiBE,0BAAwB,4BAC5G,WAAC0tB,EAAAA,EAAWA,CAAAA,CAAC9e,MAAM,WAAWzO,UAAU,0BAA0BL,sBAAoB,cAAcE,0BAAwB,6BAC1H,UAACyiB,GAAAA,CAAYA,CAAAA,CAACtiB,UAAU,UAAUL,sBAAoB,eAAeE,0BAAwB,qBAC7F,UAACqB,OAAAA,CAAKlB,UAAU,4BAAmB,SACnC,UAACkB,OAAAA,CAAKlB,UAAU,qBAAY,YAIhC,UAACmoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,4BAA4BzoB,sBAAoB,iBAAiBE,0BAAwB,4BAClH,WAAC0tB,EAAAA,EAAWA,CAAAA,CAAC9e,MAAM,UAAUzO,UAAU,0BAA0BL,sBAAoB,cAAcE,0BAAwB,6BACzH,UAACmsB,GAAYA,CAAChsB,QAADgsB,EAAW,UAAUrsB,sBAAoB,eAAeE,0BAAwB,qBAC7F,UAACqB,OAAAA,CAAKlB,UAAU,4BAAmB,SACnC,UAACkB,OAAAA,CAAKlB,UAAU,qBAAY,eAMlC,WAACytB,EAAAA,EAAWA,CAAAA,CAAChf,MAAM,QAAQzO,UAAU,YAAYL,sBAAoB,cAAcE,0BAAwB,6BACzG,WAACE,MAAAA,WACC,UAACE,KAAAA,CAAGD,UAAU,kDAAyC,SACvD,UAACG,IAAAA,CAAEH,UAAU,sCAA6B,0BAI5C,UAACmmB,GAAWA,CAACxmB,OAADwmB,eAAqB,cAActmB,0BAAwB,wBAIzE,UAACsoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,iBAAiBzoB,sBAAoB,iBAAiBE,0BAAwB,4BACvG,UAAC4tB,EAAAA,EAAWA,CAAAA,CAAChf,MAAM,WAAWzO,UAAU,YAAYL,sBAAoB,cAAcE,0BAAwB,4BAC5G,UAAC+oB,GAAiBA,CAACC,aAADD,GArDC/d,CAqDiB6iB,GApD5C3nB,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,aAAa,EAAE6E,EAAKgH,UAAU,EAAE,EAE/Cub,EAAa,QACf,EAiDmEztB,sBAAoB,oBAAoBE,0BAAwB,yBAK7H,UAACsoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,sBAAsBzoB,sBAAoB,iBAAiBE,0BAAwB,4BAC5G,UAAC4tB,EAAAA,EAAWA,CAAAA,CAAChf,MAAM,WAAWzO,UAAU,YAAYL,sBAAoB,cAAcE,0BAAwB,4BAC5G,UAACsqB,GAAcA,CAACxqB,UAADwqB,YAAqB,iBAAiBtqB,0BAAwB,yBAKjF,UAACsoB,EAAAA,EAAcA,CAAAA,CAACC,WAAW,4BAA4BzoB,sBAAoB,iBAAiBE,0BAAwB,4BAClH,UAAC4tB,EAAAA,EAAWA,CAAAA,CAAChf,MAAM,UAAUzO,UAAU,YAAYL,sBAAoB,cAAcE,0BAAwB,4BAC3G,UAACmrB,GAAkBA,CAACrrB,cAADqrB,QAAqB,qBAAqBnrB,0BAAwB,6BAKjG,0BChGA,wDCAA,gECAA,wDCAA,sDCAA,yDCAA,iDCAA,2DCAA,yHCEA,SAAS8f,EAAS,WAChB3f,CAAS,CACT,GAAGQ,EAC8B,EACjC,MAAO,UAACmtB,WAAAA,CAASjtB,YAAU,WAAWV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucd,GAAa,GAAGQ,CAAK,CAAEZ,wBAAsB,WAAWC,0BAAwB,gBAC7kB,wFCLe,SAASH,EAAc,UACpCsB,CAAQ,YACR4sB,GAAa,CAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAACrsB,EAAAA,UAAUA,CAAAA,CAACvB,UAAU,iCAChC,UAACD,MAAAA,CAAIC,UAAU,mCAA2BgB,MAC5B,UAACjB,MAAAA,CAAIC,UAAU,mCAA2BgB,KAElE,0BCdA,iDCAA,yDCAA,8+BCGA,IAAM6sB,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,oOAAqO,CAC7PC,SAAU,CACR9T,QAAS,CACP+T,QAAS,+BACTC,YAAa,mGACf,CACF,EACAC,gBAAiB,CACfjU,QAAS,SACX,CACF,GACA,SAAS4J,EAAM,WACb7jB,CAAS,SACTia,CAAO,CACP,GAAGzZ,EAC8D,EACjE,MAAO,UAACT,MAAAA,CAAIW,YAAU,QAAQytB,KAAK,QAAQnuB,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC+sB,EAAc,SACrE5T,CACF,GAAIja,GAAa,GAAGQ,CAAK,CAAEZ,wBAAsB,QAAQC,0BAAwB,aACnF,CACA,SAASuuB,EAAW,WAClBpuB,CAAS,CACT,GAAGQ,EACyB,EAC5B,MAAO,UAACT,MAAAA,CAAIW,YAAU,cAAcV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8DAA+Dd,GAAa,GAAGQ,CAAK,CAAEZ,wBAAsB,aAAaC,0BAAwB,aACrM,CACA,SAASkkB,EAAiB,WACxB/jB,CAAS,CACT,GAAGQ,EACyB,EAC5B,MAAO,UAACT,MAAAA,CAAIW,YAAU,oBAAoBV,UAAWc,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iGAAkGd,GAAa,GAAGQ,CAAK,CAAEZ,wBAAsB,mBAAmBC,0BAAwB,aACpP,yBClCA,qSCwBM,EAAe,UAGf,CAAC,EAAsB,EAAkB,CAAI,OAAkB,CAAC,EAAc,CAClF,IAAiB,CAClB,EACK,EAAiB,QAAiB,CAAC,EAcnC,CAAC,EAAiB,EAAiB,CACvC,EAA0C,GAUtC,EAAkC,IACtC,GAXsD,gBAYpD,WACA,EACA,KAAM,cACN,eACA,QACA,GAAQ,EACV,CAAI,EACE,EAAc,EAAe,GAC7B,EAAmB,SADwB,IACM,EACjD,CAAC,EAAiB,EAAkB,CAAU,YAAS,GAAnB,CACnC,CAD2D,EACpD,EAAO,EAAO,CAAI,IAAJ,CAAI,EAAoB,CAAC,CACnD,KAAM,EACN,YAAa,EACb,SAAU,CACZ,CAAC,EAED,MACE,UAAiB,KAAhB,CAAsB,GAAG,EACxB,mBAAC,GACC,MAAO,EACP,UAAW,OAAK,CAAC,aACjB,OACA,EACA,aAAc,EACd,aAAoB,cAAY,IAAM,EAAQ,GAAc,CAAC,GAAW,CAAC,EAAQ,EAAZ,GAAW,aAChF,EACA,kBAAyB,cAAY,IAAM,GAAmB,GAAO,CAAH,CAAK,EACvE,qBAA4B,cAAY,IAAM,GAAmB,GAAQ,CAAC,CAAJ,QACtE,WAEC,GACH,CACF,CAEJ,EAEA,EAAQ,YAAc,EAMtB,IAAM,EAAc,eAsBpB,CAhB4B,aAC1B,CAAC,EAAwC,KACvC,GAAM,gBAAE,EAAgB,GAAG,EAAY,CAAI,EACrC,EAAU,EAAkB,EAAa,CADR,EAEjC,EAAc,EAAe,GAC7B,IAFuD,OACZ,QACzC,uBAAmB,EAAqB,CAAI,EAOpD,OALM,YAAU,KACd,IACO,IAAM,KACZ,CAAC,EAAmB,EAFH,EAIb,SAH6B,CAGZ,KAAhB,CAAwB,GAAG,EAAc,GAAG,EAAa,IAAK,EAAc,CACtF,GAGY,YAAc,EAM5B,IAAM,EAAe,iBAMf,EAAuB,aAC3B,CAAC,EAAyC,KACxC,GAAM,gBAAE,EAAgB,GAAG,EAAa,CAAI,EACtC,EAAU,EAAkB,EAAc,EADR,CAElC,EAAc,EAAe,GAC7B,EAAqB,EAFmC,CAEnC,IAAe,CAAC,CADM,CACQ,EAAQ,UAAU,EAErE,EACJ,UAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,gBAAc,SACd,gBAAe,EAAQ,KACvB,gBAAe,EAAQ,UACvB,aAAY,EAAS,EAAQ,IAAI,EAChC,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAM,QAAS,EAAQ,YAAY,IAIrE,OAAO,EAAQ,gBACb,EAEA,UAAiB,KAAhB,CAAuB,SAAO,EAAE,GAAG,EACjC,WACH,CAEJ,GAGF,EAAe,YAAc,EAM7B,IAAM,EAAc,gBAGd,CAAC,EAAgB,EAAgB,CAAI,EAAyC,EAAa,CAC/F,QADqC,GACzB,MACd,CAAC,EAgBK,EAA8C,IAClD,GAAM,CAAE,4BAAgB,WAAY,YAAU,EAAU,CAAI,EACtD,EAAU,EAAkB,EAAa,GAC/C,MACE,UAAC,GAAe,MAAO,aAAgB,EACrC,mBAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACvC,mBAAC,GAAe,CAAf,CAAgB,SAAO,YAAC,WACtB,EACH,EACF,EACF,CAEJ,EAEA,EAAc,YAAc,EAM5B,IAAM,EAAe,iBAUf,EAAuB,aAC3B,CAAC,EAAyC,KACxC,IAAM,EAAgB,EAAiB,EAAc,EAAM,cAAc,EACnE,YAAE,EAAa,EAAc,WAAY,GAAG,EAAa,CAAI,EAC7D,EAAU,EAAkB,EAAc,EAAM,cAAc,EACpE,MACE,UAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAQ,KACtC,WAAQ,MACP,UAAC,GAAqB,GAAG,EAAc,IAAK,EAAc,EAE1D,UAAC,GAAwB,GAAG,EAAc,IAAK,EAAc,EAEjE,CAEJ,GAGF,EAAe,YAAc,EAQ7B,IAAM,EAA4B,aAChC,CAAC,EAA6C,KAC5C,IAAM,EAAU,EAAkB,EAAc,EAAM,cAAc,EAC9D,EAAmB,SAAuB,IAAI,EAC9C,EAAe,OAAe,CAAC,EAAc,GAC7C,EAA+B,KADwB,GACxB,EAAO,GAQ5C,EARiD,KAG3C,YAAU,KACd,IAAM,EAAU,EAAW,QAC3B,GAAI,EAAS,OAAO,OAAU,CAAC,EACjC,EAAG,CAAC,CAAC,CADmC,CAItC,UAAC,GAAY,CAAZ,CAAa,GAAI,IAAI,CAAE,eAAc,GACpC,mBAAC,GACE,GAAG,EACJ,IAAK,EAGL,UAAW,EAAQ,KACnB,6BAA2B,EAC3B,iBAAkB,OAAoB,CAAC,EAAM,iBAAkB,IAC7D,EAAM,eAAe,EACjB,EAAwB,QAAS,GAAQ,WAAW,SAAS,MAAM,CACzE,CAAC,EACD,qBAAsB,OAAoB,CACxC,EAAM,qBACN,IACE,IAAM,EAAgB,EAAM,OAAO,cAC7B,EAAyC,IAAzB,EAAc,SAA0C,IAA1B,EAAc,QAGlE,EAAuB,QAFuB,EAEb,EAFZ,EAAc,QAAgB,CAGrD,EACA,CAAE,0BAA0B,CAAM,GAIpC,eAAgB,OAAoB,CAClC,EAAM,eACL,GAAU,EAAM,eAAe,EAChC,CAAE,0BAA0B,CAAM,EACpC,EACF,CACF,CAEJ,GAGI,EAA+B,aACnC,CAAC,EAA6C,KAC5C,IAAM,EAAU,EAAkB,EAAc,EAAM,cAAc,EAC9D,EAAgC,UAAO,GACvC,EAD4C,EACX,QAAO,GAE9C,EAFmD,IAGjD,UAAC,GACE,GAAG,EACJ,IAAK,EACL,WAAW,EACX,6BAA6B,EAC7B,iBAAkB,IAChB,EAAM,mBAAmB,GAEpB,EAAM,kBAAkB,CACvB,EAAyB,QAAS,GAAQ,WAAW,SAAS,MAAM,EAExE,EAAM,eAAe,GAGvB,EAAwB,SAAU,EAClC,EAAyB,SAAU,CACrC,EACA,kBAAmB,IACjB,EAAM,oBAAoB,GAErB,EAF0B,gBAEpB,EAAkB,CAC3B,EAAwB,SAAU,EAC9B,eAAmD,GAA7C,OAAO,cAAc,OAC7B,EAAyB,SAAU,IAOvC,IAAM,EAAS,EAAM,SACW,WAAW,EACtB,KADsB,EAAS,SAAS,IACxC,EAD8C,cACxC,CAAe,EAMF,YAApC,EAAM,OAAO,cAAc,MAAsB,EAAyB,SAAS,EAC/E,eAAe,CAEzB,GAGN,GA+BI,EAA2B,aAC/B,CAAC,EAA6C,KAC5C,GAAM,gBACJ,YACA,kBACA,mBACA,8BACA,kBACA,uBACA,iBACA,oBACA,EACA,GAAG,EACL,CAAI,EACE,EAAU,EAAkB,EAAc,GAC1C,EAAc,EAAe,GAMnC,IAP8D,EAK9D,KAJiD,EAIjD,CAAc,CAAC,EAGb,UAAC,GAAU,CAAV,CACC,SAAO,EACP,MAAI,EACJ,QAAS,EACT,iBAAkB,EAClB,mBAAoB,EAEpB,mBAAC,IAAgB,CAAhB,CACC,SAAO,8BACP,oBACA,kBACA,EACA,uBACA,iBACA,UAAW,IAAM,EAAQ,cAAa,GAEtC,EAF2C,OAE3C,UAAiB,KAAhB,CACC,aAAY,EAAS,EAAQ,IAAI,EACjC,KAAK,SACL,GAAI,EAAQ,UACX,GAAG,EACH,GAAG,EACJ,IAAK,EACL,MAAO,CACL,GAAG,EAAa,MAGd,2CAA4C,uCAC5C,0CAA2C,sCAC3C,2CAA4C,uCAC5C,gCAAiC,mCACjC,iCAAkC,mCAEtC,GACF,EACF,EAGN,GAOI,EAAa,eA4CnB,SAAS,EAAS,GAAe,OACxB,EAAO,OAAS,QACzB,CA1BA,EAf2B,WACzB,CAAC,EAAuC,KACtC,GAAM,gBAAE,EAAgB,GAAG,EAAW,CAAI,EACpC,EAAU,EAAkB,EADI,GAEtC,MACE,UAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACJ,GAAG,EACJ,IAAK,EACL,QAAS,OAAoB,CAAC,EAAM,QAAS,IAAM,EAAQ,cAAa,GAAM,EAAD,CAAC,EAMzE,YAAc,EAoB3B,EAR2B,WACzB,CAAC,EAAuC,KACtC,GAAM,gBAAE,EAAgB,GAAG,EAAW,CAAI,EACpC,EAAc,EAAe,EADG,CAEtC,MAAO,KAD0C,GAC1C,EAAiB,KAAhB,CAAuB,GAAG,EAAc,GAAG,EAAY,IAAK,EAAc,CACpF,GAGW,YAdM,EAcQ,aAQ3B,IAAMwuB,EAAO,EAEP,EAAU,EACV,EAAS,EACTC,EAAU", "sources": ["webpack://next-shadcn-dashboard-starter/../../../src/icons/IconReceipt.ts", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/billing/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/?1469", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconRefresh.ts", "webpack://next-shadcn-dashboard-starter/?abcc", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconAlertCircle.ts", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/dialog.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/scroll-area.tsx", "webpack://next-shadcn-dashboard-starter/?b8d0", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/./src/lib/billing-error-handler.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/api/billing.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/validation/billing-schemas.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/billing-notifications.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/validation/validation-utils.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconCash.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconDeviceMobile.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconBrandAlipay.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconBuildingBank.ts", "webpack://next-shadcn-dashboard-starter/./src/components/billing/receipt.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconPrinter.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconDownload.ts", "webpack://next-shadcn-dashboard-starter/./src/components/billing/receipt-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/payment-form.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/payment-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/bill-form.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/bill-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/bill-status-manager.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconCurrencyYuan.ts", "webpack://next-shadcn-dashboard-starter/./src/components/billing/bill-filters.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/billing-list.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/appointment-to-bill.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/billing/receipt-manager.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconChartBar.ts", "webpack://next-shadcn-dashboard-starter/./src/components/billing/financial-dashboard.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconCalendarEvent.ts", "webpack://next-shadcn-dashboard-starter/./src/components/billing/billing-tabs.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/textarea.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/page-container.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/alert.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\"", "webpack://next-shadcn-dashboard-starter/../src/popover.tsx"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'receipt', 'IconReceipt', [[\"path\",{\"d\":\"M5 21v-16a2 2 0 0 1 2 -2h10a2 2 0 0 1 2 2v16l-3 -2l-2 2l-2 -2l-2 2l-2 -2l-3 2m4 -14h6m-6 4h6m-2 4h2\",\"key\":\"svg-0\"}]]);", "import { auth } from '@clerk/nextjs/server';\nimport { redirect } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport { BillingTabs } from '@/components/billing/billing-tabs';\nimport { IconReceipt } from '@tabler/icons-react';\nimport { t } from '@/lib/translations';\nexport default async function BillingPage() {\n  const {\n    userId\n  } = await auth();\n  if (!userId) {\n    return redirect('/auth/sign-in');\n  }\n  return <PageContainer data-sentry-element=\"PageContainer\" data-sentry-component=\"BillingPage\" data-sentry-source-file=\"page.tsx\">\n      <div className='flex flex-1 flex-col space-y-4'>\n        {/* Header */}\n        <div className='flex items-center justify-between'>\n          <div>\n            <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>\n              <IconReceipt className='size-6' data-sentry-element=\"IconReceipt\" data-sentry-source-file=\"page.tsx\" />\n              财务管理\n            </h2>\n            <p className='text-muted-foreground'>\n              管理账单、支付记录、收据和财务报表\n            </p>\n          </div>\n        </div>\n\n        {/* Billing Tabs - Client Component */}\n        <BillingTabs data-sentry-element=\"BillingTabs\" data-sentry-source-file=\"page.tsx\" />\n      </div>\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/billing',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/billing',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/billing',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/billing',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\billing\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'billing',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\billing\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\billing\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/billing/page\",\n        pathname: \"/dashboard/billing\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "import(/* webpackMode: \"eager\", webpackExports: [\"BillingTabs\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\billing\\\\billing-tabs.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'refresh', 'IconRefresh', [[\"path\",{\"d\":\"M20 11a8.1 8.1 0 0 0 -15.5 -2m-.5 -4v4h4\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M4 13a8.1 8.1 0 0 0 15.5 2m.5 4v-4h-4\",\"key\":\"svg-1\"}]]);", "import(/* webpackMode: \"eager\", webpackExports: [\"BillingTabs\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\billing\\\\billing-tabs.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'alert-circle', 'IconAlertCircle', [[\"path\",{\"d\":\"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M12 8v4\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 16h.01\",\"key\":\"svg-2\"}]]);", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot='dialog' {...props} data-sentry-element=\"DialogPrimitive.Root\" data-sentry-component=\"Dialog\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} data-sentry-element=\"DialogPrimitive.Trigger\" data-sentry-component=\"DialogTrigger\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} data-sentry-element=\"DialogPrimitive.Portal\" data-sentry-component=\"DialogPortal\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} data-sentry-element=\"DialogPrimitive.Close\" data-sentry-component=\"DialogClose\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return <DialogPrimitive.Overlay data-slot='dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"DialogPrimitive.Overlay\" data-sentry-component=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return <DialogPortal data-slot='dialog-portal' data-sentry-element=\"DialogPortal\" data-sentry-component=\"DialogContent\" data-sentry-source-file=\"dialog.tsx\">\r\n      <DialogOverlay data-sentry-element=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />\r\n      <DialogPrimitive.Content data-slot='dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"DialogPrimitive.Content\" data-sentry-source-file=\"dialog.tsx\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\" data-sentry-element=\"DialogPrimitive.Close\" data-sentry-source-file=\"dialog.tsx\">\r\n          <XIcon data-sentry-element=\"XIcon\" data-sentry-source-file=\"dialog.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>;\n}\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"DialogHeader\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"DialogFooter\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return <DialogPrimitive.Title data-slot='dialog-title' className={cn('text-lg leading-none font-semibold', className)} {...props} data-sentry-element=\"DialogPrimitive.Title\" data-sentry-component=\"DialogTitle\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return <DialogPrimitive.Description data-slot='dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"DialogPrimitive.Description\" data-sentry-component=\"DialogDescription\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nexport { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger };", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "'use client';\n\nimport * as React from 'react';\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\nimport { cn } from '@/lib/utils';\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.Root\" data-sentry-component=\"ScrollArea\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.Viewport data-slot='scroll-area-viewport' className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1' data-sentry-element=\"ScrollAreaPrimitive.Viewport\" data-sentry-source-file=\"scroll-area.tsx\">\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n      <ScrollAreaPrimitive.Corner data-sentry-element=\"ScrollAreaPrimitive.Corner\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.Root>;\n}\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return <ScrollAreaPrimitive.ScrollAreaScrollbar data-slot='scroll-area-scrollbar' orientation={orientation} className={cn('flex touch-none p-px transition-colors select-none', orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent', orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaScrollbar\" data-sentry-component=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.ScrollAreaThumb data-slot='scroll-area-thumb' className='bg-border relative flex-1 rounded-full' data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaThumb\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>;\n}\nexport { ScrollArea, ScrollBar };", "\nexport { deleteKeylessAction as \"7f7b45347fd50452ee6e2850ded1018991a7b086f0\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { syncKeylessConfigAction as \"7f909588461cb83e855875f4939d6f26e4ae81b49e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { createOrReadKeylessAction as \"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { invalidateCacheAction as \"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\server-actions.js\"\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "// Comprehensive error handling system for billing operations\n// Provides consistent error handling, logging, and user feedback\n\nimport { toast } from 'sonner';\n\n// Error types and interfaces\nexport interface BillingError {\n  code: string;\n  message: string;\n  userMessage: string;\n  severity: 'error' | 'warning' | 'info';\n  details?: any;\n  timestamp: Date;\n  context?: string;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  context?: string;\n  fallbackMessage?: string;\n}\n\n// Error code mappings with user-friendly messages\nconst ERROR_MESSAGES: Record<string, { message: string; severity: 'error' | 'warning' | 'info' }> = {\n  // Authentication errors\n  AUTH_REQUIRED: { message: '请先登录以继续操作', severity: 'error' },\n  AUTH_SERVICE_ERROR: { message: '认证服务暂时不可用，请稍后重试', severity: 'error' },\n  CLERK_USER_FETCH_ERROR: { message: '获取用户信息失败，请重新登录', severity: 'error' },\n  USER_EMAIL_MISSING: { message: '用户邮箱信息缺失，请联系管理员', severity: 'error' },\n\n  // Validation errors\n  VALIDATION_ERROR: { message: '输入数据验证失败，请检查表单内容', severity: 'error' },\n  INVALID_JSON: { message: '数据格式错误，请刷新页面重试', severity: 'error' },\n  INVALID_LIMIT: { message: '分页参数错误', severity: 'warning' },\n  INVALID_PAGE: { message: '页码参数错误', severity: 'warning' },\n  SUBTOTAL_MISMATCH: { message: '账单金额计算错误，请重新检查', severity: 'error' },\n\n  // Business logic errors\n  INVALID_PAYMENT_AMOUNT: { message: '支付金额无效', severity: 'error' },\n  BILL_NOT_FOUND: { message: '账单不存在或已被删除', severity: 'error' },\n  PAYMENT_METHOD_ERROR: { message: '支付方式验证失败', severity: 'error' },\n  INSUFFICIENT_PERMISSIONS: { message: '权限不足，无法执行此操作', severity: 'error' },\n\n  // Backend/Service errors\n  BACKEND_ERROR: { message: '后端服务错误', severity: 'error' },\n  BACKEND_SERVICE_ERROR: { message: '后端服务暂时不可用，请稍后重试', severity: 'error' },\n  DATABASE_ERROR: { message: '数据库操作失败，请稍后重试', severity: 'error' },\n  NETWORK_ERROR: { message: '网络连接错误，请检查网络连接', severity: 'error' },\n\n  // Security errors\n  RATE_LIMIT_EXCEEDED: { message: '操作过于频繁，请稍后再试', severity: 'error' },\n  SUSPICIOUS_ACTIVITY: { message: '检测到可疑活动，操作已被阻止', severity: 'error' },\n  SESSION_EXPIRED: { message: '会话已过期，请重新登录', severity: 'error' },\n  INVALID_AUTHENTICATION: { message: '身份验证失败', severity: 'error' },\n  UNAUTHORIZED_ACCESS: { message: '未授权访问，请先登录', severity: 'error' },\n\n  // Payment processing errors\n  PAYMENT_GATEWAY_ERROR: { message: '支付网关错误，请稍后重试', severity: 'error' },\n  CARD_DECLINED: { message: '银行卡被拒绝，请检查卡片状态', severity: 'error' },\n  CARD_EXPIRED: { message: '银行卡已过期，请使用其他卡片', severity: 'error' },\n  INVALID_CARD_NUMBER: { message: '银行卡号无效', severity: 'error' },\n  TRANSACTION_TIMEOUT: { message: '交易超时，请重新尝试', severity: 'error' },\n  DUPLICATE_TRANSACTION: { message: '重复交易，请勿重复提交', severity: 'error' },\n  INSUFFICIENT_FUNDS: { message: '余额不足，无法完成支付', severity: 'error' },\n  PAYMENT_ALREADY_PROCESSED: { message: '支付已处理，请勿重复操作', severity: 'error' },\n\n  // Deposit and refund errors\n  DEPOSIT_EXPIRED: { message: '押金已过期', severity: 'error' },\n  DEPOSIT_INSUFFICIENT_BALANCE: { message: '押金余额不足', severity: 'error' },\n  REFUND_AMOUNT_EXCEEDS_PAYMENT: { message: '退款金额超过支付金额', severity: 'error' },\n  DEPOSIT_NOT_FOUND: { message: '未找到指定押金记录', severity: 'error' },\n\n  // System configuration errors\n  CONFIGURATION_ERROR: { message: '系统配置错误，请联系管理员', severity: 'error' },\n  EXTERNAL_SERVICE_ERROR: { message: '外部服务错误，请稍后重试', severity: 'error' },\n\n  // Generic errors\n  INTERNAL_ERROR: { message: '系统内部错误，请稍后重试', severity: 'error' },\n  UNKNOWN_ERROR: { message: '发生未知错误，请联系技术支持', severity: 'error' },\n};\n\n// Error handler class\nexport class BillingErrorHandler {\n  private static instance: BillingErrorHandler;\n  private errorLog: BillingError[] = [];\n\n  private constructor() {}\n\n  static getInstance(): BillingErrorHandler {\n    if (!BillingErrorHandler.instance) {\n      BillingErrorHandler.instance = new BillingErrorHandler();\n    }\n    return BillingErrorHandler.instance;\n  }\n\n  /**\n   * Handle API response errors\n   */\n  handleAPIError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const {\n      showToast = true,\n      logError = true,\n      context = 'API',\n      fallbackMessage = '操作失败，请稍后重试'\n    } = options;\n\n    let billingError: BillingError;\n\n    if (error?.code && ERROR_MESSAGES[error.code]) {\n      const errorInfo = ERROR_MESSAGES[error.code];\n      billingError = {\n        code: error.code,\n        message: error.message || errorInfo.message,\n        userMessage: error.message || errorInfo.message,\n        severity: errorInfo.severity,\n        details: error.details,\n        timestamp: new Date(),\n        context\n      };\n    } else {\n      // Handle unknown errors\n      billingError = {\n        code: 'UNKNOWN_ERROR',\n        message: error?.message || 'Unknown error occurred',\n        userMessage: fallbackMessage,\n        severity: 'error',\n        details: error,\n        timestamp: new Date(),\n        context\n      };\n    }\n\n    if (logError) {\n      this.logError(billingError);\n    }\n\n    if (showToast) {\n      this.showErrorToast(billingError);\n    }\n\n    return billingError;\n  }\n\n  /**\n   * Handle network/fetch errors\n   */\n  handleNetworkError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const networkError: BillingError = {\n      code: 'NETWORK_ERROR',\n      message: error?.message || 'Network request failed',\n      userMessage: '网络连接错误，请检查网络连接后重试',\n      severity: 'error',\n      details: error,\n      timestamp: new Date(),\n      context: options.context || 'Network'\n    };\n\n    if (options.logError !== false) {\n      this.logError(networkError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(networkError);\n    }\n\n    return networkError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  handleValidationError(validationErrors: any[], options: ErrorHandlerOptions = {}): BillingError {\n    const firstError = validationErrors[0];\n    const validationError: BillingError = {\n      code: 'VALIDATION_ERROR',\n      message: 'Validation failed',\n      userMessage: firstError?.message || '表单验证失败，请检查输入内容',\n      severity: 'error',\n      details: validationErrors,\n      timestamp: new Date(),\n      context: options.context || 'Validation'\n    };\n\n    if (options.logError !== false) {\n      this.logError(validationError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(validationError);\n    }\n\n    return validationError;\n  }\n\n  /**\n   * Show success message\n   */\n  showSuccess(message: string, description?: string): void {\n    toast.success(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Show warning message\n   */\n  showWarning(message: string, description?: string): void {\n    toast.warning(message, {\n      description,\n      duration: 4000,\n    });\n  }\n\n  /**\n   * Show info message\n   */\n  showInfo(message: string, description?: string): void {\n    toast.info(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Log error to console and internal log\n   */\n  private logError(error: BillingError): void {\n    console.error(`[${error.context}] ${error.code}: ${error.message}`, {\n      userMessage: error.userMessage,\n      details: error.details,\n      timestamp: error.timestamp\n    });\n\n    // Add to internal error log (keep last 100 errors)\n    this.errorLog.push(error);\n    if (this.errorLog.length > 100) {\n      this.errorLog.shift();\n    }\n  }\n\n  /**\n   * Show error toast notification\n   */\n  private showErrorToast(error: BillingError): void {\n    const toastOptions = {\n      duration: error.severity === 'error' ? 5000 : 4000,\n    };\n\n    switch (error.severity) {\n      case 'error':\n        toast.error(error.userMessage, toastOptions);\n        break;\n      case 'warning':\n        toast.warning(error.userMessage, toastOptions);\n        break;\n      case 'info':\n        toast.info(error.userMessage, toastOptions);\n        break;\n    }\n  }\n\n  /**\n   * Get error log for debugging\n   */\n  getErrorLog(): BillingError[] {\n    return [...this.errorLog];\n  }\n\n  /**\n   * Clear error log\n   */\n  clearErrorLog(): void {\n    this.errorLog = [];\n  }\n}\n\n// Convenience functions for common error handling patterns\nexport const billingErrorHandler = BillingErrorHandler.getInstance();\n\nexport const handleAPIError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleAPIError(error, options);\n\nexport const handleNetworkError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleNetworkError(error, options);\n\nexport const handleValidationError = (errors: any[], options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleValidationError(errors, options);\n\nexport const showSuccess = (message: string, description?: string) => \n  billingErrorHandler.showSuccess(message, description);\n\nexport const showWarning = (message: string, description?: string) => \n  billingErrorHandler.showWarning(message, description);\n\nexport const showInfo = (message: string, description?: string) => \n  billingErrorHandler.showInfo(message, description);\n\n// Error boundary helper for React components\nexport const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(\n  fn: T,\n  context?: string\n): T => {\n  return (async (...args: any[]) => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      handleAPIError(error, { context });\n      throw error;\n    }\n  }) as T;\n};\n\n// Retry utility with exponential backoff\nexport const retryWithBackoff = async <T>(\n  fn: () => Promise<T>,\n  maxRetries: number = 3,\n  baseDelay: number = 1000,\n  context?: string\n): Promise<T> => {\n  let lastError: any;\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      return await fn();\n    } catch (error) {\n      lastError = error;\n      \n      if (attempt === maxRetries) {\n        handleAPIError(error, { \n          context: context || 'Retry',\n          fallbackMessage: `操作失败，已重试${maxRetries}次`\n        });\n        throw error;\n      }\n\n      // Exponential backoff delay\n      const delay = baseDelay * Math.pow(2, attempt - 1);\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n\n  throw lastError;\n};\n", "// Billing API client functions for medical clinic system\n// Handles bills, payments, and financial operations with comprehensive error handling\n\nimport { Bill, Payment, BillItem, PayloadResponse } from '@/types/clinic';\nimport {\n  handleAPIError,\n  handleNetworkError,\n  retryWithBackoff,\n  BillingError\n} from '@/lib/billing-error-handler';\n\n// API base URL - using relative paths for Next.js API routes\nconst API_BASE = '/api';\n\n// Enhanced error handling utility\nexport class BillingAPIError extends Error {\n  constructor(\n    message: string,\n    public status?: number,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'BillingAPIError';\n  }\n}\n\n// Enhanced API request handler with comprehensive error handling and retry logic\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {},\n  retryOptions?: { maxRetries?: number; context?: string }\n): Promise<T> {\n  const url = `${API_BASE}${endpoint}`;\n\n  const defaultHeaders = {\n    'Content-Type': 'application/json',\n  };\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  const makeRequest = async (): Promise<T> => {\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        let errorData;\n        try {\n          errorData = await response.json();\n        } catch {\n          errorData = {\n            error: `HTTP ${response.status}: ${response.statusText}`,\n            code: `HTTP_${response.status}`,\n            message: response.statusText\n          };\n        }\n\n        const apiError = new BillingAPIError(\n          errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData.code || `HTTP_${response.status}`,\n          errorData.details\n        );\n\n        // Handle the error through the error handler\n        handleAPIError(errorData, {\n          context: retryOptions?.context || 'API Request',\n          showToast: false // Don't show toast here, let the calling function decide\n        });\n\n        throw apiError;\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error instanceof BillingAPIError) {\n        throw error;\n      }\n\n      // Handle network errors\n      const networkError = new BillingAPIError(\n        error instanceof Error ? error.message : 'Network error occurred',\n        0,\n        'NETWORK_ERROR',\n        error\n      );\n\n      handleNetworkError(error, {\n        context: retryOptions?.context || 'API Request',\n        showToast: false\n      });\n\n      throw networkError;\n    }\n  };\n\n  // Use retry logic for GET requests and other idempotent operations\n  const isIdempotent = !options.method || ['GET', 'HEAD', 'OPTIONS'].includes(options.method.toUpperCase());\n\n  if (isIdempotent && retryOptions?.maxRetries) {\n    return retryWithBackoff(\n      makeRequest,\n      retryOptions.maxRetries,\n      1000,\n      retryOptions.context\n    );\n  }\n\n  return makeRequest();\n}\n\n// Bill API Functions\nexport const billsAPI = {\n  /**\n   * Fetch all bills with optional filtering and pagination\n   */\n  async fetchBills(params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n    status?: string;\n    patientId?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Bill>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n    if (params?.status) searchParams.append('status', params.status);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/bills${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Bill>>(\n      endpoint,\n      {},\n      { maxRetries: 3, context: 'Fetch Bills' }\n    );\n  },\n\n  /**\n   * Fetch a specific bill by ID\n   */\n  async fetchBill(id: string): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`);\n  },\n\n  /**\n   * Create a new bill\n   */\n  async createBill(billData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    billType: 'treatment' | 'consultation' | 'deposit' | 'additional';\n    subtotal: number;\n    discountAmount?: number;\n    taxAmount?: number;\n    totalAmount: number;\n    description: string;\n    notes?: string;\n    dueDate: string;\n    items?: Array<{\n      itemType: 'treatment' | 'consultation' | 'material' | 'service';\n      itemName: string;\n      description?: string;\n      quantity: number;\n      unitPrice: number;\n      discountRate?: number;\n    }>;\n  }): Promise<Bill> {\n    return apiRequest<Bill>('/bills', {\n      method: 'POST',\n      body: JSON.stringify(billData),\n    });\n  },\n\n  /**\n   * Update an existing bill\n   */\n  async updateBill(id: string, updateData: Partial<Bill>): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Delete a bill\n   */\n  async deleteBill(id: string): Promise<void> {\n    return apiRequest<void>(`/bills/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  /**\n   * Generate bill from appointment\n   */\n  async generateFromAppointment(appointmentId: string, billType: string = 'treatment'): Promise<Bill> {\n    return apiRequest<Bill>('/bills/generate-from-appointment', {\n      method: 'POST',\n      body: JSON.stringify({ appointmentId, billType }),\n    });\n  },\n\n  /**\n   * Check if appointment already has a bill\n   */\n  async checkAppointmentBill(appointmentId: string): Promise<{ hasBill: boolean; bill?: Bill }> {\n    try {\n      const response = await billsAPI.fetchBills({\n        limit: 1,\n        // Note: This would need backend support for filtering by appointment\n        // For now, we'll fetch and filter client-side in components\n      });\n\n      const bill = response.docs.find(bill =>\n        typeof bill.appointment === 'object' && bill.appointment?.id === appointmentId\n      );\n\n      return {\n        hasBill: !!bill,\n        bill: bill || undefined,\n      };\n    } catch (error) {\n      console.error('Failed to check appointment bill:', error);\n      return { hasBill: false };\n    }\n  },\n};\n\n// Payment API Functions\nexport const paymentsAPI = {\n  /**\n   * Fetch all payments with optional filtering\n   */\n  async fetchPayments(params?: {\n    page?: number;\n    limit?: number;\n    billId?: string;\n    patientId?: string;\n    paymentMethod?: string;\n    status?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Payment>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.billId) searchParams.append('bill', params.billId);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);\n    if (params?.status) searchParams.append('paymentStatus', params.status);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/payments${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Payment>>(endpoint);\n  },\n\n  /**\n   * Fetch a specific payment by ID\n   */\n  async fetchPayment(id: string): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`);\n  },\n\n  /**\n   * Process a new payment\n   */\n  async processPayment(paymentData: {\n    bill: string;\n    patient: string;\n    amount: number;\n    paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';\n    transactionId?: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>('/payments', {\n      method: 'POST',\n      body: JSON.stringify(paymentData),\n    });\n  },\n\n  /**\n   * Update payment status\n   */\n  async updatePayment(id: string, updateData: Partial<Payment>): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Process refund\n   */\n  async processRefund(paymentId: string, refundData: {\n    amount: number;\n    reason: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${paymentId}/refund`, {\n      method: 'POST',\n      body: JSON.stringify(refundData),\n    });\n  },\n};\n\n// Financial reporting API functions\nexport const reportsAPI = {\n  /**\n   * Get daily revenue report\n   */\n  async getDailyRevenue(date: string): Promise<{\n    date: string;\n    totalRevenue: number;\n    paymentCount: number;\n    paymentMethods: Record<string, { amount: number; count: number }>;\n  }> {\n    return apiRequest(`/reports/daily-revenue?date=${date}`);\n  },\n\n  /**\n   * Get monthly revenue report\n   */\n  async getMonthlyRevenue(year: number, month: number): Promise<{\n    year: number;\n    month: number;\n    totalRevenue: number;\n    dailyBreakdown: Array<{ date: string; revenue: number }>;\n  }> {\n    return apiRequest(`/reports/monthly-revenue?year=${year}&month=${month}`);\n  },\n\n  /**\n   * Get outstanding balances report\n   */\n  async getOutstandingBalances(): Promise<{\n    totalOutstanding: number;\n    overdueAmount: number;\n    billsCount: number;\n    overdueBillsCount: number;\n    bills: Array<{\n      id: string;\n      billNumber: string;\n      patient: string;\n      amount: number;\n      dueDate: string;\n      daysOverdue: number;\n    }>;\n  }> {\n    return apiRequest('/reports/outstanding-balances');\n  },\n\n  /**\n   * Generate financial report\n   */\n  async getFinancialReport(params?: {\n    startDate?: string;\n    endDate?: string;\n    type?: 'summary' | 'detailed';\n  }): Promise<any> {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    if (params?.type) searchParams.set('type', params.type);\n\n    return apiRequest(`/reports/financial?${searchParams.toString()}`);\n  },\n};\n\n// Deposit API functions\nexport const depositsAPI = {\n  /**\n   * Create a new deposit\n   */\n  async createDeposit(depositData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    depositType: 'treatment' | 'appointment' | 'material';\n    amount: number;\n    purpose: string;\n    notes?: string;\n    expiryDate?: string;\n  }): Promise<any> {\n    return apiRequest('/deposits', {\n      method: 'POST',\n      body: JSON.stringify(depositData),\n    });\n  },\n\n  /**\n   * Get deposits with filtering and pagination\n   */\n  async getDeposits(params?: {\n    page?: number;\n    limit?: number;\n    patient?: string;\n    status?: string;\n    depositType?: string;\n  }): Promise<PayloadResponse<any>> {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.patient) searchParams.set('where[patient][equals]', params.patient);\n    if (params?.status) searchParams.set('where[status][equals]', params.status);\n    if (params?.depositType) searchParams.set('where[depositType][equals]', params.depositType);\n\n    return apiRequest(`/deposits?${searchParams.toString()}`);\n  },\n\n  /**\n   * Get deposit by ID\n   */\n  async getDepositById(id: string): Promise<any> {\n    return apiRequest(`/deposits/${id}`);\n  },\n\n  /**\n   * Update deposit\n   */\n  async updateDeposit(id: string, updateData: {\n    status?: string;\n    usedAmount?: number;\n    notes?: string;\n  }): Promise<any> {\n    return apiRequest(`/deposits/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Apply deposit to bill\n   */\n  async applyToBill(depositId: string, billId: string, amount: number): Promise<any> {\n    return apiRequest('/deposits/apply-to-bill', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        billId,\n        amount,\n      }),\n    });\n  },\n\n  /**\n   * Process deposit refund\n   */\n  async processRefund(depositId: string, refundAmount: number, refundReason: string, refundMethod: string = 'cash'): Promise<any> {\n    return apiRequest('/deposits/refund', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        refundAmount,\n        refundReason,\n        refundMethod,\n      }),\n    });\n  },\n};\n\n// Receipt API functions\nexport const receiptsAPI = {\n  /**\n   * Generate receipt for payment\n   */\n  async generateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`);\n  },\n\n  /**\n   * Regenerate receipt number (admin only)\n   */\n  async regenerateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`, {\n      method: 'POST',\n    });\n  },\n};\n\n\n\n// BillingAPIError is already exported above at line 16\n\n// Export utility functions\nexport const billingUtils = {\n  /**\n   * Format currency amount for display\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  },\n\n  /**\n   * Calculate bill total with discounts and taxes\n   */\n  calculateBillTotal(subtotal: number, discountAmount: number = 0, taxAmount: number = 0): number {\n    return subtotal + taxAmount - discountAmount;\n  },\n\n  /**\n   * Get payment method display name\n   */\n  getPaymentMethodName(method: string): string {\n    const methods: Record<string, string> = {\n      cash: '现金',\n      card: '银行卡',\n      wechat: '微信支付',\n      alipay: '支付宝',\n      transfer: '银行转账',\n      deposit: '押金抵扣',\n      installment: '分期付款',\n    };\n    return methods[method] || method;\n  },\n\n  /**\n   * Get bill status display name\n   */\n  getBillStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      draft: '草稿',\n      sent: '已发送',\n      confirmed: '已确认',\n      paid: '已支付',\n      cancelled: '已取消',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get payment status display name\n   */\n  getPaymentStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      pending: '待处理',\n      completed: '已完成',\n      failed: '失败',\n      refunded: '已退款',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get deposit status display name\n   */\n  getDepositStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      active: '有效',\n      used: '已使用',\n      refunded: '已退还',\n      expired: '已过期',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Validate payment amount against bill balance\n   */\n  validatePaymentAmount(amount: number, billBalance: number): boolean {\n    return amount > 0 && amount <= billBalance;\n  },\n};\n", "// Comprehensive validation schemas for billing forms\n// Provides robust client-side validation with detailed error messages in Chinese\n\nimport * as z from 'zod';\n\n// Common validation patterns\nconst positiveNumber = z.number().min(0, '金额不能为负数');\nconst requiredString = z.string().min(1, '此字段为必填项');\nconst optionalString = z.string();\nconst phoneRegex = /^1[3-9]\\d{9}$/;\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n// Custom validation functions\nconst validateCurrency = (value: number) => {\n  if (value < 0) return false;\n  // Check for reasonable decimal places (max 2)\n  const decimalPlaces = (value.toString().split('.')[1] || '').length;\n  return decimalPlaces <= 2;\n};\n\nconst validateDateNotInPast = (date: string) => {\n  const inputDate = new Date(date);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return inputDate >= today;\n};\n\nconst validateDateNotTooFarInFuture = (date: string) => {\n  const inputDate = new Date(date);\n  const maxDate = new Date();\n  maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future\n  return inputDate <= maxDate;\n};\n\n// Bill Item validation schema\nexport const billItemSchema = z.object({\n  itemType: z.enum(['treatment', 'consultation', 'material', 'service'], {\n    required_error: '请选择项目类型',\n    invalid_type_error: '无效的项目类型',\n  }),\n  itemName: requiredString.max(100, '项目名称不能超过100个字符'),\n  description: optionalString.max(500, '描述不能超过500个字符').optional(),\n  quantity: z.number()\n    .min(0.01, '数量必须大于0')\n    .max(9999, '数量不能超过9999')\n    .refine((val) => {\n      const decimalPlaces = (val.toString().split('.')[1] || '').length;\n      return decimalPlaces <= 3;\n    }, '数量最多支持3位小数'),\n  unitPrice: z.number()\n    .min(0, '单价不能为负数')\n    .max(999999.99, '单价不能超过999,999.99')\n    .refine(validateCurrency, '单价格式无效，最多支持2位小数'),\n  discountRate: z.number()\n    .min(0, '折扣率不能为负数')\n    .max(100, '折扣率不能超过100%')\n    .optional(),\n}).refine((data) => {\n  // Validate that discount rate makes sense\n  if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {\n    return false;\n  }\n  return true;\n}, {\n  message: '单价为0时不能设置折扣',\n  path: ['discountRate'],\n});\n\n// Bill form validation schema\nexport const billFormSchema = z.object({\n  patient: requiredString.uuid('请选择有效的患者'),\n  appointment: optionalString.uuid('请选择有效的预约').optional().or(z.literal('')),\n  treatment: optionalString.uuid('请选择有效的治疗项目').optional().or(z.literal('')),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional'], {\n    required_error: '请选择账单类型',\n    invalid_type_error: '无效的账单类型',\n  }),\n  description: requiredString\n    .min(2, '账单描述至少需要2个字符')\n    .max(200, '账单描述不能超过200个字符'),\n  notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),\n  dueDate: z.string()\n    .min(1, '请选择到期日期')\n    .refine((date) => {\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的日期')\n    .refine(validateDateNotInPast, '到期日期不能是过去的日期')\n    .refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),\n  discountAmount: z.number()\n    .min(0, '折扣金额不能为负数')\n    .max(999999.99, '折扣金额不能超过999,999.99')\n    .refine(validateCurrency, '折扣金额格式无效')\n    .optional(),\n  taxAmount: z.number()\n    .min(0, '税费金额不能为负数')\n    .max(999999.99, '税费金额不能超过999,999.99')\n    .refine(validateCurrency, '税费金额格式无效')\n    .optional(),\n  items: z.array(billItemSchema)\n    .min(1, '至少需要一个账单项目')\n    .max(50, '账单项目不能超过50个'),\n}).refine((data) => {\n  // Validate that bill has reasonable total\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  const taxAmount = data.taxAmount || 0;\n  const finalTotal = itemsTotal + taxAmount - discountAmount;\n  \n  return finalTotal >= 0;\n}, {\n  message: '账单总金额不能为负数',\n  path: ['discountAmount'],\n}).refine((data) => {\n  // Validate discount doesn't exceed subtotal\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  return discountAmount <= itemsTotal;\n}, {\n  message: '折扣金额不能超过项目小计',\n  path: ['discountAmount'],\n});\n\n// Payment form validation schema\nexport const paymentFormSchema = z.object({\n  amount: z.number()\n    .min(0.01, '支付金额必须大于0')\n    .max(999999.99, '支付金额不能超过999,999.99')\n    .refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),\n  paymentMethod: z.enum(['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'], {\n    required_error: '请选择支付方式',\n    invalid_type_error: '无效的支付方式',\n  }),\n  transactionId: z.string()\n    .max(100, '交易ID不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  notes: optionalString.max(500, '备注不能超过500个字符').optional(),\n}).refine((data) => {\n  // Require transaction ID for certain payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(data.paymentMethod)) {\n    return data.transactionId && data.transactionId.trim().length > 0;\n  }\n  return true;\n}, {\n  message: '此支付方式需要提供交易ID',\n  path: ['transactionId'],\n});\n\n// Patient form validation schema (for billing context)\nexport const patientFormSchema = z.object({\n  fullName: requiredString\n    .min(2, '姓名至少需要2个字符')\n    .max(50, '姓名不能超过50个字符')\n    .regex(/^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, '姓名只能包含中文、英文和空格'),\n  phone: requiredString\n    .regex(phoneRegex, '请输入有效的手机号码'),\n  email: z.string()\n    .email('请输入有效的邮箱地址')\n    .max(100, '邮箱地址不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional(),\n});\n\n// Bill status update validation schema\nexport const billStatusUpdateSchema = z.object({\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled'], {\n    required_error: '请选择账单状态',\n    invalid_type_error: '无效的账单状态',\n  }),\n  notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional(),\n});\n\n// Filter validation schema\nexport const billFilterSchema = z.object({\n  search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled']).optional(),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional']).optional(),\n  patientId: optionalString.uuid('请选择有效的患者').optional().or(z.literal('')),\n  dateFrom: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的开始日期'),\n  dateTo: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的结束日期'),\n  amountMin: z.number()\n    .min(0, '最小金额不能为负数')\n    .max(999999.99, '最小金额不能超过999,999.99')\n    .optional(),\n  amountMax: z.number()\n    .min(0, '最大金额不能为负数')\n    .max(999999.99, '最大金额不能超过999,999.99')\n    .optional(),\n}).refine((data) => {\n  // Validate date range\n  if (data.dateFrom && data.dateTo) {\n    const fromDate = new Date(data.dateFrom);\n    const toDate = new Date(data.dateTo);\n    return fromDate <= toDate;\n  }\n  return true;\n}, {\n  message: '开始日期不能晚于结束日期',\n  path: ['dateTo'],\n}).refine((data) => {\n  // Validate amount range\n  if (data.amountMin !== undefined && data.amountMax !== undefined) {\n    return data.amountMin <= data.amountMax;\n  }\n  return true;\n}, {\n  message: '最小金额不能大于最大金额',\n  path: ['amountMax'],\n});\n\n// Export types for TypeScript\nexport type BillItemFormData = z.infer<typeof billItemSchema>;\nexport type BillFormData = z.infer<typeof billFormSchema>;\nexport type PaymentFormData = z.infer<typeof paymentFormSchema>;\nexport type PatientFormData = z.infer<typeof patientFormSchema>;\nexport type BillStatusUpdateData = z.infer<typeof billStatusUpdateSchema>;\nexport type BillFilterData = z.infer<typeof billFilterSchema>;\n\n// Validation helper functions\nexport const validateBillItem = (data: unknown) => {\n  return billItemSchema.safeParse(data);\n};\n\nexport const validateBillForm = (data: unknown) => {\n  return billFormSchema.safeParse(data);\n};\n\nexport const validatePaymentForm = (data: unknown) => {\n  return paymentFormSchema.safeParse(data);\n};\n\nexport const validatePatientForm = (data: unknown) => {\n  return patientFormSchema.safeParse(data);\n};\n\nexport const validateBillStatusUpdate = (data: unknown) => {\n  return billStatusUpdateSchema.safeParse(data);\n};\n\nexport const validateBillFilter = (data: unknown) => {\n  return billFilterSchema.safeParse(data);\n};\n\n// Custom validation error formatter\nexport const formatValidationErrors = (errors: z.ZodError) => {\n  return errors.errors.map(error => ({\n    field: error.path.join('.'),\n    message: error.message,\n    code: error.code,\n  }));\n};\n", "// Comprehensive toast notification utilities for billing actions\n// Provides consistent messaging in Chinese for all billing operations\n\nimport { toast } from 'sonner';\nimport { Bill, Payment, BillItem } from '@/types/clinic';\nimport { billingUtils } from './api/billing';\n\nexport const billingNotifications = {\n  // Bill-related notifications\n  bill: {\n    created: (bill: Bill) => {\n      toast.success(`账单创建成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (bill: Bill) => {\n      toast.success(`账单更新成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (billNumber: string) => {\n      toast.success(`账单删除成功！`, {\n        description: `账单编号: ${billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    statusUpdated: (bill: Bill, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        draft: '草稿',\n        sent: '已发送',\n        confirmed: '已确认',\n        paid: '已支付',\n        cancelled: '已取消',\n      };\n      \n      toast.success(`账单状态已更新！`, {\n        description: `${bill.billNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 5000,\n      });\n    },\n\n    generateFromAppointment: (bill: Bill, appointmentDate: string) => {\n      toast.success(`从预约生成账单成功！`, {\n        description: `预约日期: ${appointmentDate}，账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    loadError: (error?: string) => {\n      toast.error(`加载账单失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    createError: (error?: string) => {\n      toast.error(`创建账单失败`, {\n        description: error || '请检查输入信息后重试',\n        duration: 5000,\n      });\n    },\n\n    updateError: (error?: string) => {\n      toast.error(`更新账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    deleteError: (error?: string) => {\n      toast.error(`删除账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`账单验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Payment-related notifications\n  payment: {\n    processed: (payment: Payment) => {\n      toast.success(`支付处理成功！`, {\n        description: `支付金额: ${billingUtils.formatCurrency(payment.amount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    receiptGenerated: (payment: Payment) => {\n      toast.success(`收据生成成功！`, {\n        description: `收据编号: ${payment.receiptNumber || '待生成'}`,\n        duration: 4000,\n      });\n    },\n\n    refunded: (payment: Payment, refundAmount: number) => {\n      toast.success(`退款处理成功！`, {\n        description: `退款金额: ${billingUtils.formatCurrency(refundAmount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    statusUpdated: (payment: Payment, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        pending: '待处理',\n        completed: '已完成',\n        failed: '失败',\n        refunded: '已退款',\n      };\n\n      toast.success(`支付状态已更新！`, {\n        description: `${payment.paymentNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 4000,\n      });\n    },\n\n    processError: (error?: string) => {\n      toast.error(`支付处理失败`, {\n        description: error || '请检查支付信息后重试',\n        duration: 5000,\n      });\n    },\n\n    refundError: (error?: string) => {\n      toast.error(`退款处理失败`, {\n        description: error || '请联系管理员处理',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`支付验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n\n    amountExceeded: (maxAmount: number) => {\n      toast.error(`支付金额超限`, {\n        description: `最大支付金额: ${billingUtils.formatCurrency(maxAmount)}`,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Receipt-related notifications\n  receipt: {\n    printed: (receiptNumber: string) => {\n      toast.success(`收据打印成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    downloaded: (receiptNumber: string) => {\n      toast.success(`收据下载成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    printError: () => {\n      toast.error(`收据打印失败`, {\n        description: '请检查打印机设置',\n        duration: 4000,\n      });\n    },\n\n    downloadError: () => {\n      toast.error(`收据下载失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    notFound: (receiptNumber: string) => {\n      toast.error(`收据未找到`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // General system notifications\n  system: {\n    loading: (action: string) => {\n      toast.loading(`${action}中...`, {\n        duration: Infinity, // Will be dismissed manually\n      });\n    },\n\n    networkError: () => {\n      toast.error(`网络连接失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    permissionDenied: (action: string) => {\n      toast.error(`权限不足`, {\n        description: `您没有权限执行: ${action}`,\n        duration: 5000,\n      });\n    },\n\n    dataRefreshed: () => {\n      toast.success(`数据刷新成功`, {\n        duration: 2000,\n      });\n    },\n\n    dataRefreshError: () => {\n      toast.error(`数据刷新失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    operationCancelled: (operation: string) => {\n      toast.info(`${operation}已取消`, {\n        duration: 2000,\n      });\n    },\n\n    featureNotImplemented: (feature: string) => {\n      toast.info(`${feature}功能开发中...`, {\n        description: '敬请期待',\n        duration: 3000,\n      });\n    },\n  },\n\n  // Financial reporting notifications\n  financial: {\n    reportGenerated: (reportType: string, period: string) => {\n      toast.success(`${reportType}生成成功！`, {\n        description: `报表期间: ${period}`,\n        duration: 4000,\n      });\n    },\n\n    reportError: (reportType: string, error?: string) => {\n      toast.error(`${reportType}生成失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    dataExported: (format: string) => {\n      toast.success(`数据导出成功！`, {\n        description: `格式: ${format}`,\n        duration: 3000,\n      });\n    },\n\n    exportError: (error?: string) => {\n      toast.error(`数据导出失败`, {\n        description: error || '请稍后重试',\n        duration: 4000,\n      });\n    },\n  },\n\n  // Validation and warning notifications\n  validation: {\n    requiredField: (fieldName: string) => {\n      toast.error(`字段验证失败`, {\n        description: `${fieldName}为必填项`,\n        duration: 4000,\n      });\n    },\n\n    invalidFormat: (fieldName: string, expectedFormat: string) => {\n      toast.error(`格式验证失败`, {\n        description: `${fieldName}格式应为: ${expectedFormat}`,\n        duration: 4000,\n      });\n    },\n\n    duplicateEntry: (itemType: string, identifier: string) => {\n      toast.error(`重复条目`, {\n        description: `${itemType} \"${identifier}\" 已存在`,\n        duration: 4000,\n      });\n    },\n\n    unsavedChanges: () => {\n      toast.warning(`有未保存的更改`, {\n        description: '请保存后再继续',\n        duration: 4000,\n      });\n    },\n\n    confirmAction: (action: string) => {\n      toast.warning(`请确认操作`, {\n        description: `即将执行: ${action}`,\n        duration: 5000,\n      });\n    },\n  },\n};\n\n// Utility function to dismiss all toasts\nexport const dismissAllToasts = () => {\n  toast.dismiss();\n};\n\n// Utility function to show custom toast with consistent styling\nexport const showCustomToast = (\n  type: 'success' | 'error' | 'warning' | 'info',\n  title: string,\n  description?: string,\n  duration: number = 4000\n) => {\n  const toastFunction = toast[type];\n  toastFunction(title, {\n    description,\n    duration,\n  });\n};\n", "// Validation utilities for real-time form validation and error handling\n// Provides consistent validation feedback across all billing forms\n\nimport { z } from 'zod';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Validation result interface\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: ValidationError[];\n  warnings: ValidationWarning[];\n}\n\nexport interface ValidationError {\n  field: string;\n  message: string;\n  code: string;\n  severity: 'error' | 'warning';\n}\n\nexport interface ValidationWarning {\n  field: string;\n  message: string;\n  suggestion?: string;\n}\n\n// Real-time validation debounce utility\nexport class ValidationDebouncer {\n  private timeouts: Map<string, NodeJS.Timeout> = new Map();\n  private readonly delay: number;\n\n  constructor(delay: number = 500) {\n    this.delay = delay;\n  }\n\n  debounce<T extends any[]>(\n    key: string,\n    callback: (...args: T) => void,\n    ...args: T\n  ): void {\n    // Clear existing timeout for this key\n    const existingTimeout = this.timeouts.get(key);\n    if (existingTimeout) {\n      clearTimeout(existingTimeout);\n    }\n\n    // Set new timeout\n    const timeout = setTimeout(() => {\n      callback(...args);\n      this.timeouts.delete(key);\n    }, this.delay);\n\n    this.timeouts.set(key, timeout);\n  }\n\n  clear(key?: string): void {\n    if (key) {\n      const timeout = this.timeouts.get(key);\n      if (timeout) {\n        clearTimeout(timeout);\n        this.timeouts.delete(key);\n      }\n    } else {\n      // Clear all timeouts\n      this.timeouts.forEach(timeout => clearTimeout(timeout));\n      this.timeouts.clear();\n    }\n  }\n}\n\n// Field-level validation utility\nexport class FieldValidator {\n  private schema: z.ZodSchema;\n  private debouncer: ValidationDebouncer;\n\n  constructor(schema: z.ZodSchema, debounceDelay: number = 300) {\n    this.schema = schema;\n    this.debouncer = new ValidationDebouncer(debounceDelay);\n  }\n\n  validateField(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    this.debouncer.debounce(\n      fieldPath,\n      this.performFieldValidation.bind(this),\n      fieldPath,\n      value,\n      fullData,\n      onValidation\n    );\n  }\n\n  private performFieldValidation(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    try {\n      // Create a partial object with just this field\n      const fieldData = this.setNestedValue({}, fieldPath, value);\n      \n      // Merge with existing data\n      const testData = { ...fullData, ...fieldData };\n      \n      // Validate the full object but focus on this field\n      const result = this.schema.safeParse(testData);\n      \n      const fieldErrors = result.success \n        ? []\n        : result.error.errors\n            .filter(error => error.path.join('.') === fieldPath)\n            .map(error => ({\n              field: fieldPath,\n              message: error.message,\n              code: error.code,\n              severity: 'error' as const,\n            }));\n\n      const warnings = this.generateWarnings(fieldPath, value, fullData);\n\n      const validationResult: ValidationResult = {\n        isValid: fieldErrors.length === 0,\n        errors: fieldErrors,\n        warnings,\n      };\n\n      if (onValidation) {\n        onValidation(validationResult);\n      }\n    } catch (error) {\n      console.error('Field validation error:', error);\n      if (onValidation) {\n        onValidation({\n          isValid: false,\n          errors: [{\n            field: fieldPath,\n            message: '验证过程中发生错误',\n            code: 'VALIDATION_ERROR',\n            severity: 'error',\n          }],\n          warnings: [],\n        });\n      }\n    }\n  }\n\n  private setNestedValue(obj: any, path: string, value: any): any {\n    const keys = path.split('.');\n    let current = obj;\n    \n    for (let i = 0; i < keys.length - 1; i++) {\n      const key = keys[i];\n      if (!(key in current)) {\n        current[key] = {};\n      }\n      current = current[key];\n    }\n    \n    current[keys[keys.length - 1]] = value;\n    return obj;\n  }\n\n  private generateWarnings(fieldPath: string, value: any, fullData: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate context-specific warnings\n    switch (fieldPath) {\n      case 'amount':\n        if (typeof value === 'number' && value > 10000) {\n          warnings.push({\n            field: fieldPath,\n            message: '金额较大，请确认是否正确',\n            suggestion: '检查金额是否输入正确',\n          });\n        }\n        break;\n\n      case 'dueDate':\n        if (value) {\n          const dueDate = new Date(value);\n          const today = new Date();\n          const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n          \n          if (daysDiff > 365) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期距离现在超过一年',\n              suggestion: '考虑设置更近的到期日期',\n            });\n          } else if (daysDiff < 7) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期较近',\n              suggestion: '确保有足够时间处理账单',\n            });\n          }\n        }\n        break;\n\n      case 'discountAmount':\n        if (typeof value === 'number' && value > 0 && fullData.items) {\n          const subtotal = fullData.items.reduce((sum: number, item: any) => {\n            return sum + (item.quantity || 0) * (item.unitPrice || 0);\n          }, 0);\n          \n          if (value > subtotal * 0.5) {\n            warnings.push({\n              field: fieldPath,\n              message: '折扣金额超过小计的50%',\n              suggestion: '确认折扣金额是否正确',\n            });\n          }\n        }\n        break;\n\n      case 'unitPrice':\n        if (typeof value === 'number' && value === 0) {\n          warnings.push({\n            field: fieldPath,\n            message: '单价为0，确认是否为免费项目',\n            suggestion: '如果不是免费项目，请输入正确单价',\n          });\n        }\n        break;\n    }\n\n    return warnings;\n  }\n\n  cleanup(): void {\n    this.debouncer.clear();\n  }\n}\n\n// Form-level validation utility\nexport class FormValidator {\n  private schema: z.ZodSchema;\n  private fieldValidators: Map<string, FieldValidator> = new Map();\n\n  constructor(schema: z.ZodSchema) {\n    this.schema = schema;\n  }\n\n  validateForm(data: any): ValidationResult {\n    try {\n      const result = this.schema.safeParse(data);\n      \n      if (result.success) {\n        return {\n          isValid: true,\n          errors: [],\n          warnings: this.generateFormWarnings(data),\n        };\n      }\n\n      const errors = result.error.errors.map(error => ({\n        field: error.path.join('.'),\n        message: error.message,\n        code: error.code,\n        severity: 'error' as const,\n      }));\n\n      return {\n        isValid: false,\n        errors,\n        warnings: this.generateFormWarnings(data),\n      };\n    } catch (error) {\n      console.error('Form validation error:', error);\n      return {\n        isValid: false,\n        errors: [{\n          field: 'form',\n          message: '表单验证过程中发生错误',\n          code: 'FORM_VALIDATION_ERROR',\n          severity: 'error',\n        }],\n        warnings: [],\n      };\n    }\n  }\n\n  private generateFormWarnings(data: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate form-level warnings\n    if (data.items && Array.isArray(data.items)) {\n      const totalItems = data.items.length;\n      if (totalItems > 20) {\n        warnings.push({\n          field: 'items',\n          message: `账单包含${totalItems}个项目，较多`,\n          suggestion: '考虑合并相似项目或分拆为多个账单',\n        });\n      }\n\n      const totalAmount = data.items.reduce((sum: number, item: any) => {\n        return sum + (item.quantity || 0) * (item.unitPrice || 0);\n      }, 0);\n\n      if (totalAmount > 50000) {\n        warnings.push({\n          field: 'form',\n          message: '账单总金额较大',\n          suggestion: '确认金额计算是否正确',\n        });\n      }\n    }\n\n    return warnings;\n  }\n\n  getFieldValidator(fieldPath: string): FieldValidator {\n    if (!this.fieldValidators.has(fieldPath)) {\n      this.fieldValidators.set(fieldPath, new FieldValidator(this.schema));\n    }\n    return this.fieldValidators.get(fieldPath)!;\n  }\n\n  cleanup(): void {\n    this.fieldValidators.forEach(validator => validator.cleanup());\n    this.fieldValidators.clear();\n  }\n}\n\n// Validation error display utility\nexport const displayValidationErrors = (errors: ValidationError[]): void => {\n  errors.forEach(error => {\n    if (error.severity === 'error') {\n      billingNotifications.validation.requiredField(error.field);\n    }\n  });\n};\n\n// Business logic validation\nexport const validateBusinessRules = {\n  // Check if bill can be marked as paid\n  canMarkAsPaid: (bill: any): { valid: boolean; reason?: string } => {\n    if ((bill.remainingAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '账单还有未支付金额，无法标记为已支付',\n      };\n    }\n    return { valid: true };\n  },\n\n  // Check if payment amount is valid for bill\n  isValidPaymentAmount: (amount: number, bill: any): { valid: boolean; reason?: string } => {\n    const remainingAmount = bill.remainingAmount || 0;\n    \n    if (amount > remainingAmount) {\n      return {\n        valid: false,\n        reason: `支付金额不能超过待付金额 $${remainingAmount.toFixed(2)}`,\n      };\n    }\n    \n    if (amount <= 0) {\n      return {\n        valid: false,\n        reason: '支付金额必须大于0',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill can be deleted\n  canDeleteBill: (bill: any): { valid: boolean; reason?: string } => {\n    if (bill.status === 'paid') {\n      return {\n        valid: false,\n        reason: '已支付的账单不能删除',\n      };\n    }\n    \n    if ((bill.paidAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '已有支付记录的账单不能删除',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill status transition is valid\n  isValidStatusTransition: (fromStatus: string, toStatus: string): { valid: boolean; reason?: string } => {\n    const validTransitions: Record<string, string[]> = {\n      draft: ['sent', 'cancelled'],\n      sent: ['confirmed', 'cancelled'],\n      confirmed: ['paid', 'cancelled'],\n      paid: [], // No transitions from paid\n      cancelled: [], // No transitions from cancelled\n    };\n\n    const allowedTransitions = validTransitions[fromStatus] || [];\n    \n    if (!allowedTransitions.includes(toStatus)) {\n      return {\n        valid: false,\n        reason: `不能从\"${fromStatus}\"状态转换到\"${toStatus}\"状态`,\n      };\n    }\n    \n    return { valid: true };\n  },\n};\n\n// Export validation constants\nexport const VALIDATION_CONSTANTS = {\n  MAX_BILL_AMOUNT: 999999.99,\n  MAX_ITEMS_PER_BILL: 50,\n  MAX_DESCRIPTION_LENGTH: 200,\n  MAX_NOTES_LENGTH: 1000,\n  MIN_PAYMENT_AMOUNT: 0.01,\n  MAX_DISCOUNT_RATE: 100,\n  DEBOUNCE_DELAY: 300,\n} as const;\n", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'cash', 'IconCash', [[\"path\",{\"d\":\"M7 15h-3a1 1 0 0 1 -1 -1v-8a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v3\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M7 9m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 14a2 2 0 1 0 4 0a2 2 0 0 0 -4 0\",\"key\":\"svg-2\"}]]);", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'device-mobile', 'IconDeviceMobile', [[\"path\",{\"d\":\"M6 5a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2v-14z\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M11 4h2\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 17v.01\",\"key\":\"svg-2\"}]]);", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'brand-alipay', 'IconBrandAlipay', [[\"path\",{\"d\":\"M19 3h-14a2 2 0 0 0 -2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2 -2v-14a2 2 0 0 0 -2 -2z\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M7 7h10\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 3v7\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M21 17.314c-2.971 -1.923 -15 -8.779 -15 -1.864c0 1.716 1.52 2.55 2.985 2.55c3.512 0 6.814 -5.425 6.814 -8h-6.604\",\"key\":\"svg-3\"}]]);", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'building-bank', 'IconBuildingBank', [[\"path\",{\"d\":\"M3 21l18 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M3 10l18 0\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M5 6l7 -3l7 3\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M4 10l0 11\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M20 10l0 11\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M8 14l0 3\",\"key\":\"svg-5\"}],[\"path\",{\"d\":\"M12 14l0 3\",\"key\":\"svg-6\"}],[\"path\",{\"d\":\"M16 14l0 3\",\"key\":\"svg-7\"}]]);", "'use client';\n\nimport { forwardRef } from 'react';\nimport { Card, CardContent, CardHeader } from '@/components/ui/card';\nimport { Separator } from '@/components/ui/separator';\nimport { Badge } from '@/components/ui/badge';\nimport { Payment, Bill } from '@/types/clinic';\nimport { billingUtils } from '@/lib/api/billing';\ninterface ReceiptProps {\n  payment: Payment;\n  bill?: Bill;\n  clinicInfo?: {\n    name: string;\n    address: string;\n    phone: string;\n    email?: string;\n    taxId?: string;\n  };\n}\n\n// Default clinic information - this would typically come from settings\nconst defaultClinicInfo = {\n  name: '美丽诊所',\n  address: '北京市朝阳区美丽街123号',\n  phone: '010-12345678',\n  email: '<EMAIL>',\n  taxId: '91110000000000000X'\n};\nexport const Receipt = forwardRef<HTMLDivElement, ReceiptProps>(({\n  payment,\n  bill,\n  clinicInfo = defaultClinicInfo\n}, ref) => {\n  const patient = typeof payment.patient === 'object' ? payment.patient : null;\n  const paymentBill = bill || (typeof payment.bill === 'object' ? payment.bill : null);\n  return <div ref={ref} className=\"max-w-md mx-auto bg-white\">\n        <Card className=\"shadow-none border-none\">\n          <CardHeader className=\"text-center pb-4\">\n            {/* Clinic Header */}\n            <div className=\"space-y-1\">\n              <h1 className=\"text-xl font-bold\">{clinicInfo.name}</h1>\n              <p className=\"text-sm text-muted-foreground\">{clinicInfo.address}</p>\n              <p className=\"text-sm text-muted-foreground\">\n                电话: {clinicInfo.phone}\n                {clinicInfo.email && ` | 邮箱: ${clinicInfo.email}`}\n              </p>\n              {clinicInfo.taxId && <p className=\"text-xs text-muted-foreground\">\n                  税号: {clinicInfo.taxId}\n                </p>}\n            </div>\n            \n            <Separator className=\"my-4\" />\n            \n            {/* Receipt Title */}\n            <div className=\"space-y-2\">\n              <h2 className=\"text-lg font-semibold\">收款收据</h2>\n              <div className=\"flex justify-between text-sm\">\n                <span>收据编号:</span>\n                <span className=\"font-mono\">{payment.receiptNumber || '待生成'}</span>\n              </div>\n              <div className=\"flex justify-between text-sm\">\n                <span>支付编号:</span>\n                <span className=\"font-mono\">{payment.paymentNumber}</span>\n              </div>\n            </div>\n          </CardHeader>\n\n          <CardContent className=\"space-y-4\">\n            {/* Payment Information */}\n            <div className=\"space-y-2\">\n              <h3 className=\"font-medium text-sm border-b pb-1\">支付信息</h3>\n              \n              <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">患者姓名:</span>\n                  <span>{patient?.fullName || '未知患者'}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">支付日期:</span>\n                  <span>{new Date(payment.paymentDate).toLocaleDateString('zh-CN')}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">支付方式:</span>\n                  <span>{billingUtils.getPaymentMethodName(payment.paymentMethod)}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-muted-foreground\">支付状态:</span>\n                  <Badge variant={payment.paymentStatus === 'completed' ? 'default' : 'secondary'}>\n                    {billingUtils.getPaymentStatusName(payment.paymentStatus)}\n                  </Badge>\n                </div>\n              </div>\n\n              {payment.transactionId && <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-muted-foreground\">交易ID:</span>\n                  <span className=\"font-mono text-xs\">{payment.transactionId}</span>\n                </div>}\n            </div>\n\n            <Separator />\n\n            {/* Bill Information */}\n            {paymentBill && <div className=\"space-y-2\">\n                <h3 className=\"font-medium text-sm border-b pb-1\">账单信息</h3>\n                \n                <div className=\"space-y-1 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">账单编号:</span>\n                    <span className=\"font-mono\">{paymentBill.billNumber}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">账单类型:</span>\n                    <span>{billingUtils.getBillStatusName(paymentBill.billType)}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">服务描述:</span>\n                    <span className=\"text-right max-w-32 truncate\">\n                      {paymentBill.description}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">账单总额:</span>\n                    <span>{billingUtils.formatCurrency(paymentBill.totalAmount)}</span>\n                  </div>\n                  \n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">已支付:</span>\n                    <span className=\"text-green-600\">\n                      {billingUtils.formatCurrency(paymentBill.paidAmount || 0)}\n                    </span>\n                  </div>\n                  \n                  {(paymentBill.remainingAmount || 0) > 0 && <div className=\"flex justify-between\">\n                      <span className=\"text-muted-foreground\">待支付:</span>\n                      <span className=\"text-red-600\">\n                        {billingUtils.formatCurrency(paymentBill.remainingAmount || 0)}\n                      </span>\n                    </div>}\n                </div>\n              </div>}\n\n            <Separator />\n\n            {/* Payment Amount */}\n            <div className=\"space-y-2\">\n              <h3 className=\"font-medium text-sm border-b pb-1\">本次支付</h3>\n              \n              <div className=\"bg-muted/30 rounded-lg p-3\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-lg font-medium\">支付金额:</span>\n                  <span className=\"text-xl font-bold text-green-600\">\n                    {billingUtils.formatCurrency(payment.amount)}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Notes */}\n            {payment.notes && <>\n                <Separator />\n                <div className=\"space-y-2\">\n                  <h3 className=\"font-medium text-sm border-b pb-1\">备注</h3>\n                  <p className=\"text-sm text-muted-foreground\">{payment.notes}</p>\n                </div>\n              </>}\n\n            <Separator />\n\n            {/* Footer */}\n            <div className=\"text-center space-y-2\">\n              <p className=\"text-xs text-muted-foreground\">\n                感谢您选择{clinicInfo.name}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                如有疑问，请联系我们: {clinicInfo.phone}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">\n                打印时间: {new Date().toLocaleString('zh-CN')}\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>;\n});\nReceipt.displayName = 'Receipt';", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'printer', 'IconPrinter', [[\"path\",{\"d\":\"M17 17h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-14a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M17 9v-4a2 2 0 0 0 -2 -2h-6a2 2 0 0 0 -2 2v4\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M7 13m0 2a2 2 0 0 1 2 -2h6a2 2 0 0 1 2 2v4a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2z\",\"key\":\"svg-2\"}]]);", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'download', 'IconDownload', [[\"path\",{\"d\":\"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M7 11l5 5l5 -5\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M12 4l0 12\",\"key\":\"svg-2\"}]]);", "'use client';\n\nimport { useRef } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { Receipt } from './receipt';\nimport { Payment, Bill } from '@/types/clinic';\nimport { IconPrinter, IconDownload, IconX } from '@tabler/icons-react';\nimport { toast } from 'sonner';\nimport { billingNotifications } from '@/lib/billing-notifications';\ninterface ReceiptDialogProps {\n  payment: Payment | null;\n  bill?: Bill;\n  isOpen: boolean;\n  onClose: () => void;\n}\nexport function ReceiptDialog({\n  payment,\n  bill,\n  isOpen,\n  onClose\n}: ReceiptDialogProps) {\n  const receiptRef = useRef<HTMLDivElement>(null);\n  const handlePrint = () => {\n    if (!receiptRef.current) return;\n    const printWindow = window.open('', '_blank');\n    if (!printWindow) {\n      billingNotifications.receipt.printError();\n      return;\n    }\n    const receiptContent = receiptRef.current.innerHTML;\n    printWindow.document.write(`\n      <!DOCTYPE html>\n      <html>\n        <head>\n          <title>收据 - ${payment?.receiptNumber || payment?.paymentNumber}</title>\n          <meta charset=\"utf-8\">\n          <style>\n            body {\n              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n              margin: 0;\n              padding: 20px;\n              background: white;\n            }\n            .max-w-md {\n              max-width: 28rem;\n            }\n            .mx-auto {\n              margin-left: auto;\n              margin-right: auto;\n            }\n            .bg-white {\n              background-color: white;\n            }\n            .shadow-none {\n              box-shadow: none;\n            }\n            .border-none {\n              border: none;\n            }\n            .text-center {\n              text-align: center;\n            }\n            .text-xl {\n              font-size: 1.25rem;\n            }\n            .text-lg {\n              font-size: 1.125rem;\n            }\n            .text-sm {\n              font-size: 0.875rem;\n            }\n            .text-xs {\n              font-size: 0.75rem;\n            }\n            .font-bold {\n              font-weight: 700;\n            }\n            .font-semibold {\n              font-weight: 600;\n            }\n            .font-medium {\n              font-weight: 500;\n            }\n            .font-mono {\n              font-family: ui-monospace, SFMono-Regular, monospace;\n            }\n            .space-y-1 > * + * {\n              margin-top: 0.25rem;\n            }\n            .space-y-2 > * + * {\n              margin-top: 0.5rem;\n            }\n            .space-y-4 > * + * {\n              margin-top: 1rem;\n            }\n            .pb-4 {\n              padding-bottom: 1rem;\n            }\n            .pb-1 {\n              padding-bottom: 0.25rem;\n            }\n            .p-3 {\n              padding: 0.75rem;\n            }\n            .my-4 {\n              margin-top: 1rem;\n              margin-bottom: 1rem;\n            }\n            .border-b {\n              border-bottom: 1px solid #e5e7eb;\n            }\n            .grid {\n              display: grid;\n            }\n            .grid-cols-2 {\n              grid-template-columns: repeat(2, minmax(0, 1fr));\n            }\n            .gap-2 {\n              gap: 0.5rem;\n            }\n            .flex {\n              display: flex;\n            }\n            .justify-between {\n              justify-content: space-between;\n            }\n            .items-center {\n              align-items: center;\n            }\n            .text-right {\n              text-align: right;\n            }\n            .max-w-32 {\n              max-width: 8rem;\n            }\n            .truncate {\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n            .text-muted-foreground {\n              color: #6b7280;\n            }\n            .text-green-600 {\n              color: #059669;\n            }\n            .text-red-600 {\n              color: #dc2626;\n            }\n            .bg-muted\\\\/30 {\n              background-color: rgba(243, 244, 246, 0.3);\n            }\n            .rounded-lg {\n              border-radius: 0.5rem;\n            }\n            hr {\n              border: none;\n              border-top: 1px solid #e5e7eb;\n              margin: 1rem 0;\n            }\n            @media print {\n              body {\n                padding: 0;\n              }\n              .no-print {\n                display: none;\n              }\n            }\n          </style>\n        </head>\n        <body>\n          ${receiptContent}\n        </body>\n      </html>\n    `);\n    printWindow.document.close();\n    printWindow.focus();\n\n    // Wait for content to load then print\n    setTimeout(() => {\n      printWindow.print();\n      printWindow.close();\n    }, 250);\n    billingNotifications.receipt.printed(payment?.receiptNumber || payment?.paymentNumber || '');\n  };\n  const handleDownloadPDF = async () => {\n    try {\n      // This would typically use a library like jsPDF or html2pdf\n      // For now, we'll show a placeholder message\n      billingNotifications.system.featureNotImplemented('PDF下载');\n\n      // Example implementation with html2pdf (would need to install the library):\n      /*\n      const html2pdf = (await import('html2pdf.js')).default;\n      const element = receiptRef.current;\n      \n      const opt = {\n        margin: 0.5,\n        filename: `receipt-${payment?.receiptNumber || payment?.paymentNumber}.pdf`,\n        image: { type: 'jpeg', quality: 0.98 },\n        html2canvas: { scale: 2 },\n        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }\n      };\n      \n      html2pdf().set(opt).from(element).save();\n      */\n    } catch (error) {\n      console.error('PDF generation failed:', error);\n      billingNotifications.receipt.downloadError();\n    }\n  };\n  if (!payment) {\n    return null;\n  }\n  return <Dialog open={isOpen} onOpenChange={onClose} data-sentry-element=\"Dialog\" data-sentry-component=\"ReceiptDialog\" data-sentry-source-file=\"receipt-dialog.tsx\">\n      <DialogContent className=\"max-w-lg max-h-[90vh] overflow-y-auto\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"receipt-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"receipt-dialog.tsx\">\n          <div className=\"flex items-center justify-between\">\n            <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"receipt-dialog.tsx\">\n              收据 - {payment.receiptNumber || payment.paymentNumber}\n            </DialogTitle>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handlePrint} data-sentry-element=\"Button\" data-sentry-source-file=\"receipt-dialog.tsx\">\n                <IconPrinter className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconPrinter\" data-sentry-source-file=\"receipt-dialog.tsx\" />\n                打印\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" onClick={handleDownloadPDF} data-sentry-element=\"Button\" data-sentry-source-file=\"receipt-dialog.tsx\">\n                <IconDownload className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconDownload\" data-sentry-source-file=\"receipt-dialog.tsx\" />\n                下载PDF\n              </Button>\n              <Button variant=\"ghost\" size=\"sm\" onClick={onClose} data-sentry-element=\"Button\" data-sentry-source-file=\"receipt-dialog.tsx\">\n                <IconX className=\"h-4 w-4\" data-sentry-element=\"IconX\" data-sentry-source-file=\"receipt-dialog.tsx\" />\n              </Button>\n            </div>\n          </div>\n        </DialogHeader>\n        \n        <div className=\"mt-4\">\n          <Receipt ref={receiptRef} payment={payment} bill={bill} data-sentry-element=\"Receipt\" data-sentry-source-file=\"receipt-dialog.tsx\" />\n        </div>\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { paymentFormSchema, PaymentFormData } from '@/lib/validation/billing-schemas';\nimport { FormValidator, validateBusinessRules } from '@/lib/validation/validation-utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { IconCreditCard, IconCash, IconDeviceMobile, IconBrandAlipay, IconBuildingBank, IconCalendar, IconReceipt, IconX } from '@tabler/icons-react';\nimport { Bill, Payment } from '@/types/clinic';\nimport { paymentsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { toast } from 'sonner';\nimport { ReceiptDialog } from './receipt-dialog';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Remove the local schema since we're now using the centralized one\n\ninterface PaymentFormProps {\n  bill: Bill;\n  onSuccess?: (payment: Payment) => void;\n  onCancel?: () => void;\n  isOpen?: boolean;\n}\n\n// Payment method options with icons and labels\nconst paymentMethods = [{\n  value: 'cash',\n  label: '现金',\n  icon: IconCash,\n  description: '现金支付',\n  requiresTransactionId: false\n}, {\n  value: 'card',\n  label: '银行卡',\n  icon: IconCreditCard,\n  description: '银行卡刷卡支付',\n  requiresTransactionId: true\n}, {\n  value: 'wechat',\n  label: '微信支付',\n  icon: IconDeviceMobile,\n  description: '微信扫码支付',\n  requiresTransactionId: true\n}, {\n  value: 'alipay',\n  label: '支付宝',\n  icon: IconBrandAlipay,\n  description: '支付宝扫码支付',\n  requiresTransactionId: true\n}, {\n  value: 'transfer',\n  label: '银行转账',\n  icon: IconBuildingBank,\n  description: '银行转账支付',\n  requiresTransactionId: true\n}, {\n  value: 'installment',\n  label: '分期付款',\n  icon: IconCalendar,\n  description: '分期付款',\n  requiresTransactionId: false\n}];\nexport function PaymentForm({\n  bill,\n  onSuccess,\n  onCancel,\n  isOpen = true\n}: PaymentFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [selectedMethod, setSelectedMethod] = useState<string>('');\n  const [completedPayment, setCompletedPayment] = useState<Payment | null>(null);\n  const [showReceipt, setShowReceipt] = useState(false);\n  const form = useForm<PaymentFormData>({\n    resolver: zodResolver(paymentFormSchema),\n    defaultValues: {\n      amount: bill.remainingAmount || 0,\n      paymentMethod: 'cash',\n      transactionId: '',\n      notes: ''\n    }\n  });\n  const selectedPaymentMethod = paymentMethods.find(method => method.value === selectedMethod);\n  const remainingAmount = bill.remainingAmount || 0;\n  const maxPaymentAmount = remainingAmount;\n  const onSubmit = async (data: PaymentFormData) => {\n    try {\n      setIsSubmitting(true);\n\n      // Business rule validation\n      const amountValidation = validateBusinessRules.isValidPaymentAmount(data.amount, bill);\n      if (!amountValidation.valid) {\n        billingNotifications.payment.validationError(amountValidation.reason || '支付金额无效');\n        return;\n      }\n\n      // Process payment\n      const payment = await paymentsAPI.processPayment({\n        bill: bill.id,\n        patient: typeof bill.patient === 'object' ? bill.patient.id : bill.patientId,\n        amount: data.amount,\n        paymentMethod: data.paymentMethod,\n        transactionId: data.transactionId || undefined,\n        notes: data.notes || undefined\n      });\n      billingNotifications.payment.processed(payment);\n\n      // Store payment for receipt display\n      setCompletedPayment(payment);\n      setShowReceipt(true);\n      if (onSuccess) {\n        onSuccess(payment);\n      }\n\n      // Reset form\n      form.reset();\n    } catch (error) {\n      console.error('Payment processing failed:', error);\n      const errorMessage = error instanceof BillingAPIError ? error.message : undefined;\n      billingNotifications.payment.processError(errorMessage);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  if (!isOpen) {\n    return null;\n  }\n  return <Card className=\"w-full max-w-2xl mx-auto\" data-sentry-element=\"Card\" data-sentry-component=\"PaymentForm\" data-sentry-source-file=\"payment-form.tsx\">\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"payment-form.tsx\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"payment-form.tsx\">\n              <IconReceipt className=\"h-5 w-5\" data-sentry-element=\"IconReceipt\" data-sentry-source-file=\"payment-form.tsx\" />\n              处理支付\n            </CardTitle>\n            <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"payment-form.tsx\">\n              为账单 {bill.billNumber} 处理支付\n            </CardDescription>\n          </div>\n          {onCancel && <Button variant=\"ghost\" size=\"sm\" onClick={onCancel}>\n              <IconX className=\"h-4 w-4\" />\n            </Button>}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"payment-form.tsx\">\n        {/* Bill Summary */}\n        <div className=\"bg-muted/50 rounded-lg p-4 space-y-2\">\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">患者:</span>\n            <span className=\"text-sm\">\n              {typeof bill.patient === 'object' ? bill.patient.fullName : '未知患者'}\n            </span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">账单总额:</span>\n            <span className=\"text-sm font-semibold\">\n              {billingUtils.formatCurrency(bill.totalAmount)}\n            </span>\n          </div>\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">已支付:</span>\n            <span className=\"text-sm text-green-600\">\n              {billingUtils.formatCurrency(bill.paidAmount || 0)}\n            </span>\n          </div>\n          <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"payment-form.tsx\" />\n          <div className=\"flex justify-between items-center\">\n            <span className=\"text-sm font-medium\">待支付:</span>\n            <span className=\"text-lg font-bold text-red-600\">\n              {billingUtils.formatCurrency(remainingAmount)}\n            </span>\n          </div>\n        </div>\n\n        {/* Payment Form */}\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"payment-form.tsx\">\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Payment Amount */}\n            <FormField control={form.control} name=\"amount\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>支付金额</FormLabel>\n                  <FormControl>\n                    <div className=\"relative\">\n                      <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground\">\n                        $\n                      </span>\n                      <Input type=\"number\" step=\"0.01\" min=\"0.01\" max={maxPaymentAmount} placeholder=\"0.00\" className=\"pl-8\" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                    </div>\n                  </FormControl>\n                  <FormDescription>\n                    最大支付金额: {billingUtils.formatCurrency(maxPaymentAmount)}\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"payment-form.tsx\" />\n\n            {/* Payment Method */}\n            <FormField control={form.control} name=\"paymentMethod\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>支付方式</FormLabel>\n                  <FormControl>\n                    <Select value={field.value} onValueChange={value => {\n                field.onChange(value);\n                setSelectedMethod(value);\n              }}>\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"选择支付方式\" />\n                      </SelectTrigger>\n                      <SelectContent>\n                        {paymentMethods.map(method => {\n                    const Icon = method.icon;\n                    return <SelectItem key={method.value} value={method.value}>\n                              <div className=\"flex items-center gap-2\">\n                                <Icon className=\"h-4 w-4\" />\n                                <div>\n                                  <div className=\"font-medium\">{method.label}</div>\n                                  <div className=\"text-xs text-muted-foreground\">\n                                    {method.description}\n                                  </div>\n                                </div>\n                              </div>\n                            </SelectItem>;\n                  })}\n                      </SelectContent>\n                    </Select>\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"payment-form.tsx\" />\n\n            {/* Transaction ID (conditional) */}\n            {selectedPaymentMethod?.requiresTransactionId && <FormField control={form.control} name=\"transactionId\" render={({\n            field\n          }) => <FormItem>\n                    <FormLabel>交易ID</FormLabel>\n                    <FormControl>\n                      <Input placeholder=\"输入第三方支付平台的交易ID\" {...field} />\n                    </FormControl>\n                    <FormDescription>\n                      请输入{selectedPaymentMethod.label}的交易ID或流水号\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>} />}\n\n            {/* Notes */}\n            <FormField control={form.control} name=\"notes\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>备注 (可选)</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder=\"支付相关备注信息...\" className=\"resize-none\" rows={3} {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"payment-form.tsx\" />\n\n            {/* Action Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button type=\"submit\" disabled={isSubmitting} className=\"flex-1\" data-sentry-element=\"Button\" data-sentry-source-file=\"payment-form.tsx\">\n                {isSubmitting ? '处理中...' : '确认支付'}\n              </Button>\n              {onCancel && <Button type=\"button\" variant=\"outline\" onClick={onCancel} disabled={isSubmitting}>\n                  取消\n                </Button>}\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n\n      {/* Receipt Dialog */}\n      <ReceiptDialog payment={completedPayment} bill={bill} isOpen={showReceipt} onClose={() => {\n      setShowReceipt(false);\n      setCompletedPayment(null);\n    }} data-sentry-element=\"ReceiptDialog\" data-sentry-source-file=\"payment-form.tsx\" />\n    </Card>;\n}", "'use client';\n\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';\nimport { PaymentForm } from './payment-form';\nimport { Bill, Payment } from '@/types/clinic';\ninterface PaymentDialogProps {\n  bill: Bill | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess?: (payment: Payment) => void;\n}\nexport function PaymentDialog({\n  bill,\n  isOpen,\n  onClose,\n  onSuccess\n}: PaymentDialogProps) {\n  const handleSuccess = (payment: Payment) => {\n    if (onSuccess) {\n      onSuccess(payment);\n    }\n    onClose();\n  };\n  if (!bill) {\n    return null;\n  }\n  return <Dialog open={isOpen} onOpenChange={onClose} data-sentry-element=\"Dialog\" data-sentry-component=\"PaymentDialog\" data-sentry-source-file=\"payment-dialog.tsx\">\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"payment-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"payment-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"payment-dialog.tsx\">处理支付 - {bill.billNumber}</DialogTitle>\n        </DialogHeader>\n        <PaymentForm bill={bill} onSuccess={handleSuccess} onCancel={onClose} isOpen={true} data-sentry-element=\"PaymentForm\" data-sentry-source-file=\"payment-dialog.tsx\" />\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm, useFieldArray } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { billFormSchema, BillFormData } from '@/lib/validation/billing-schemas';\nimport { FormValidator, ValidationResult } from '@/lib/validation/validation-utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Separator } from '@/components/ui/separator';\nimport { IconPlus, IconTrash, IconReceipt, IconX, IconCalculator, IconUser, IconCalendar } from '@tabler/icons-react';\nimport { Bill, Patient, Appointment, Treatment, BillItem } from '@/types/clinic';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { toast } from 'sonner';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Remove the local schemas since we're now using the centralized ones\n\ninterface BillFormProps {\n  bill?: Bill;\n  patients?: Patient[];\n  appointments?: Appointment[];\n  treatments?: Treatment[];\n  onSuccess?: (bill: Bill) => void;\n  onCancel?: () => void;\n  isOpen?: boolean;\n}\n\n// Bill type options\nconst billTypes = [{\n  value: 'treatment',\n  label: '治疗账单'\n}, {\n  value: 'consultation',\n  label: '咨询账单'\n}, {\n  value: 'deposit',\n  label: '押金账单'\n}, {\n  value: 'additional',\n  label: '补充账单'\n}];\n\n// Item type options\nconst itemTypes = [{\n  value: 'treatment',\n  label: '治疗项目'\n}, {\n  value: 'consultation',\n  label: '咨询服务'\n}, {\n  value: 'material',\n  label: '材料费用'\n}, {\n  value: 'service',\n  label: '其他服务'\n}];\nexport function BillForm({\n  bill,\n  patients = [],\n  appointments = [],\n  treatments = [],\n  onSuccess,\n  onCancel,\n  isOpen = true\n}: BillFormProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [calculatedTotals, setCalculatedTotals] = useState({\n    subtotal: 0,\n    totalAmount: 0\n  });\n  const [formValidator] = useState(() => new FormValidator(billFormSchema));\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const isEditing = !!bill;\n  const form = useForm<BillFormData>({\n    resolver: zodResolver(billFormSchema),\n    defaultValues: {\n      patient: bill?.patientId ? String(bill.patientId) : '',\n      appointment: bill?.appointmentId ? String(bill.appointmentId) : '',\n      treatment: bill?.treatmentId ? String(bill.treatmentId) : '',\n      billType: bill?.billType || 'treatment',\n      description: bill?.description || '',\n      notes: bill?.notes || '',\n      dueDate: bill?.dueDate ? new Date(bill.dueDate).toISOString().split('T')[0] : '',\n      discountAmount: bill?.discountAmount || 0,\n      taxAmount: bill?.taxAmount || 0,\n      items: bill?.items?.map(item => ({\n        itemType: item.itemType,\n        itemName: item.itemName,\n        description: item.description || '',\n        quantity: item.quantity,\n        unitPrice: item.unitPrice,\n        discountRate: item.discountRate || 0\n      })) || [{\n        itemType: 'treatment' as const,\n        itemName: '',\n        description: '',\n        quantity: 1,\n        unitPrice: 0,\n        discountRate: 0\n      }]\n    }\n  });\n  const {\n    fields,\n    append,\n    remove\n  } = useFieldArray({\n    control: form.control,\n    name: 'items'\n  });\n\n  // Watch form values for real-time calculation\n  const watchedItems = form.watch('items');\n  const watchedDiscountAmount = form.watch('discountAmount');\n  const watchedTaxAmount = form.watch('taxAmount');\n\n  // Calculate totals whenever items change\n  useEffect(() => {\n    const subtotal = watchedItems.reduce((sum, item) => {\n      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n      return sum + (itemTotal - itemDiscount);\n    }, 0);\n    const totalAmount = billingUtils.calculateBillTotal(subtotal, watchedDiscountAmount || 0, watchedTaxAmount || 0);\n    setCalculatedTotals({\n      subtotal,\n      totalAmount\n    });\n  }, [watchedItems, watchedDiscountAmount, watchedTaxAmount]);\n  const onSubmit = async (data: BillFormData) => {\n    try {\n      setIsSubmitting(true);\n      const billData = {\n        patient: parseInt(data.patient),\n        appointment: data.appointment && data.appointment !== 'none' ? parseInt(data.appointment) : undefined,\n        treatment: data.treatment && data.treatment !== 'none' ? parseInt(data.treatment) : undefined,\n        billType: data.billType,\n        subtotal: calculatedTotals.subtotal,\n        discountAmount: data.discountAmount || 0,\n        taxAmount: data.taxAmount || 0,\n        totalAmount: calculatedTotals.totalAmount,\n        description: data.description,\n        notes: data.notes || undefined,\n        dueDate: data.dueDate,\n        items: data.items.map(item => ({\n          itemType: item.itemType,\n          itemName: item.itemName,\n          description: item.description || undefined,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          discountRate: item.discountRate || 0\n        }))\n      };\n      let result: Bill;\n      if (isEditing && bill) {\n        result = await billsAPI.updateBill(bill.id, billData as any);\n        billingNotifications.bill.updated(result);\n      } else {\n        result = await billsAPI.createBill(billData as any);\n        billingNotifications.bill.created(result);\n      }\n      if (onSuccess) {\n        onSuccess(result);\n      }\n      if (!isEditing) {\n        form.reset();\n      }\n    } catch (error) {\n      console.error('Bill operation failed:', error);\n      const errorMessage = error instanceof BillingAPIError ? error.message : undefined;\n      if (isEditing) {\n        billingNotifications.bill.updateError(errorMessage);\n      } else {\n        billingNotifications.bill.createError(errorMessage);\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  const addItem = () => {\n    append({\n      itemType: 'treatment',\n      itemName: '',\n      description: '',\n      quantity: 1,\n      unitPrice: 0,\n      discountRate: 0\n    });\n  };\n  const removeItem = (index: number) => {\n    if (fields.length > 1) {\n      remove(index);\n    } else {\n      billingNotifications.validation.confirmAction('至少需要保留一个账单项目');\n    }\n  };\n  if (!isOpen) {\n    return null;\n  }\n  return <Card className=\"w-full max-w-4xl mx-auto\" data-sentry-element=\"Card\" data-sentry-component=\"BillForm\" data-sentry-source-file=\"bill-form.tsx\">\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"bill-form.tsx\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"bill-form.tsx\">\n              <IconReceipt className=\"h-5 w-5\" data-sentry-element=\"IconReceipt\" data-sentry-source-file=\"bill-form.tsx\" />\n              {isEditing ? '编辑账单' : '创建账单'}\n            </CardTitle>\n            <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"bill-form.tsx\">\n              {isEditing ? `编辑账单 ${bill?.billNumber}` : '创建新的账单'}\n            </CardDescription>\n          </div>\n          {onCancel && <Button variant=\"ghost\" size=\"sm\" onClick={onCancel}>\n              <IconX className=\"h-4 w-4\" />\n            </Button>}\n        </div>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"bill-form.tsx\">\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"bill-form.tsx\">\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {/* Patient Selection */}\n              <FormField control={form.control} name=\"patient\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel className=\"flex items-center gap-2\">\n                      <IconUser className=\"h-4 w-4\" />\n                      患者\n                    </FormLabel>\n                    <FormControl>\n                      <Select value={field.value} onValueChange={field.onChange}>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"选择患者\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {patients.map(patient => <SelectItem key={patient.id} value={String(patient.id)}>\n                              {patient.fullName} - {patient.phone}\n                            </SelectItem>)}\n                        </SelectContent>\n                      </Select>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n              {/* Bill Type */}\n              <FormField control={form.control} name=\"billType\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>账单类型</FormLabel>\n                    <FormControl>\n                      <Select value={field.value} onValueChange={field.onChange}>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"选择账单类型\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          {billTypes.map(type => <SelectItem key={type.value} value={type.value}>\n                              {type.label}\n                            </SelectItem>)}\n                        </SelectContent>\n                      </Select>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n              {/* Appointment (Optional) */}\n              <FormField control={form.control} name=\"appointment\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>关联预约 (可选)</FormLabel>\n                    <FormControl>\n                      <Select value={field.value || ''} onValueChange={field.onChange}>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"选择预约\" />\n                        </SelectTrigger>\n                        <SelectContent>\n                          <SelectItem value=\"none\">无关联预约</SelectItem>\n                          {appointments.map(appointment => <SelectItem key={appointment.id} value={appointment.id}>\n                              {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')} - \n                              {typeof appointment.treatment === 'object' ? appointment.treatment.name : '未知治疗'}\n                            </SelectItem>)}\n                        </SelectContent>\n                      </Select>\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n              {/* Due Date */}\n              <FormField control={form.control} name=\"dueDate\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel className=\"flex items-center gap-2\">\n                      <IconCalendar className=\"h-4 w-4\" />\n                      到期日期\n                    </FormLabel>\n                    <FormControl>\n                      <Input type=\"date\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n            </div>\n\n            {/* Description */}\n            <FormField control={form.control} name=\"description\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>账单描述</FormLabel>\n                  <FormControl>\n                    <Input placeholder=\"输入账单描述...\" {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n            {/* Notes */}\n            <FormField control={form.control} name=\"notes\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>备注 (可选)</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder=\"账单相关备注...\" className=\"resize-none\" rows={3} {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n            <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"bill-form.tsx\" />\n\n            {/* Bill Items Section */}\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold\">账单项目</h3>\n                <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={addItem} data-sentry-element=\"Button\" data-sentry-source-file=\"bill-form.tsx\">\n                  <IconPlus className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconPlus\" data-sentry-source-file=\"bill-form.tsx\" />\n                  添加项目\n                </Button>\n              </div>\n\n              {/* Bill Items List */}\n              <div className=\"space-y-4\">\n                {fields.map((field, index) => <Card key={field.id} className=\"p-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4 items-end\">\n                      {/* Item Type */}\n                      <FormField control={form.control} name={`items.${index}.itemType`} render={({\n                    field\n                  }) => <FormItem>\n                            <FormLabel>类型</FormLabel>\n                            <FormControl>\n                              <Select value={field.value} onValueChange={field.onChange}>\n                                <SelectTrigger>\n                                  <SelectValue />\n                                </SelectTrigger>\n                                <SelectContent>\n                                  {itemTypes.map(type => <SelectItem key={type.value} value={type.value}>\n                                      {type.label}\n                                    </SelectItem>)}\n                                </SelectContent>\n                              </Select>\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>} />\n\n                      {/* Item Name */}\n                      <FormField control={form.control} name={`items.${index}.itemName`} render={({\n                    field\n                  }) => <FormItem>\n                            <FormLabel>项目名称</FormLabel>\n                            <FormControl>\n                              <Input placeholder=\"项目名称\" {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>} />\n\n                      {/* Quantity */}\n                      <FormField control={form.control} name={`items.${index}.quantity`} render={({\n                    field\n                  }) => <FormItem>\n                            <FormLabel>数量</FormLabel>\n                            <FormControl>\n                              <Input type=\"number\" step=\"0.01\" min=\"0.01\" placeholder=\"1\" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>} />\n\n                      {/* Unit Price */}\n                      <FormField control={form.control} name={`items.${index}.unitPrice`} render={({\n                    field\n                  }) => <FormItem>\n                            <FormLabel>单价</FormLabel>\n                            <FormControl>\n                              <Input type=\"number\" step=\"0.01\" min=\"0\" placeholder=\"0.00\" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>} />\n\n                      {/* Discount Rate */}\n                      <FormField control={form.control} name={`items.${index}.discountRate`} render={({\n                    field\n                  }) => <FormItem>\n                            <FormLabel>折扣率 (%)</FormLabel>\n                            <FormControl>\n                              <Input type=\"number\" step=\"0.1\" min=\"0\" max=\"100\" placeholder=\"0\" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>} />\n\n                      {/* Remove Button */}\n                      <div className=\"flex items-center\">\n                        <Button type=\"button\" variant=\"outline\" size=\"sm\" onClick={() => removeItem(index)} disabled={fields.length <= 1}>\n                          <IconTrash className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n\n                    {/* Item Description */}\n                    <div className=\"mt-4\">\n                      <FormField control={form.control} name={`items.${index}.description`} render={({\n                    field\n                  }) => <FormItem>\n                            <FormLabel>项目描述 (可选)</FormLabel>\n                            <FormControl>\n                              <Textarea placeholder=\"项目详细描述...\" className=\"resize-none\" rows={2} {...field} />\n                            </FormControl>\n                            <FormMessage />\n                          </FormItem>} />\n                    </div>\n\n                    {/* Item Total Display */}\n                    <div className=\"mt-2 text-right\">\n                      <span className=\"text-sm text-muted-foreground\">\n                        小计: {billingUtils.formatCurrency((() => {\n                      const item = watchedItems[index];\n                      if (!item) return 0;\n                      const itemTotal = (item.quantity || 0) * (item.unitPrice || 0);\n                      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n                      return itemTotal - itemDiscount;\n                    })())}\n                      </span>\n                    </div>\n                  </Card>)}\n              </div>\n            </div>\n\n            <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"bill-form.tsx\" />\n\n            {/* Totals Section */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {/* Discount Amount */}\n              <FormField control={form.control} name=\"discountAmount\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>额外折扣金额</FormLabel>\n                    <FormControl>\n                      <Input type=\"number\" step=\"0.01\" min=\"0\" placeholder=\"0.00\" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                    </FormControl>\n                    <FormDescription>\n                      在项目折扣基础上的额外折扣\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n              {/* Tax Amount */}\n              <FormField control={form.control} name=\"taxAmount\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>税费金额</FormLabel>\n                    <FormControl>\n                      <Input type=\"number\" step=\"0.01\" min=\"0\" placeholder=\"0.00\" {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                    </FormControl>\n                    <FormDescription>\n                      需要添加的税费金额\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"bill-form.tsx\" />\n\n              {/* Total Display */}\n              <div className=\"space-y-2\">\n                <Label data-sentry-element=\"Label\" data-sentry-source-file=\"bill-form.tsx\">账单总计</Label>\n                <div className=\"bg-muted/50 rounded-lg p-3 space-y-1\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span>项目小计:</span>\n                    <span>{billingUtils.formatCurrency(calculatedTotals.subtotal)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>额外折扣:</span>\n                    <span>-{billingUtils.formatCurrency(watchedDiscountAmount || 0)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span>税费:</span>\n                    <span>+{billingUtils.formatCurrency(watchedTaxAmount || 0)}</span>\n                  </div>\n                  <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"bill-form.tsx\" />\n                  <div className=\"flex justify-between font-semibold\">\n                    <span>总金额:</span>\n                    <span className=\"text-lg\">\n                      {billingUtils.formatCurrency(calculatedTotals.totalAmount)}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex gap-3 pt-4\">\n              <Button type=\"submit\" disabled={isSubmitting} className=\"flex-1\" data-sentry-element=\"Button\" data-sentry-source-file=\"bill-form.tsx\">\n                {isSubmitting ? isEditing ? '更新中...' : '创建中...' : isEditing ? '更新账单' : '创建账单'}\n              </Button>\n              {onCancel && <Button type=\"button\" variant=\"outline\" onClick={onCancel} disabled={isSubmitting}>\n                  取消\n                </Button>}\n            </div>\n          </form>\n        </Form>\n      </CardContent>\n    </Card>;\n}", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';\nimport { BillForm } from './bill-form';\nimport { Bill, Patient, Appointment, Treatment } from '@/types/clinic';\nimport { toast } from 'sonner';\ninterface BillDialogProps {\n  bill?: Bill | null;\n  isOpen: boolean;\n  onClose: () => void;\n  onSuccess?: (bill: Bill) => void;\n}\nexport function BillDialog({\n  bill,\n  isOpen,\n  onClose,\n  onSuccess\n}: BillDialogProps) {\n  const [patients, setPatients] = useState<Patient[]>([]);\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [treatments, setTreatments] = useState<Treatment[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  // Fetch required data when dialog opens\n  useEffect(() => {\n    if (isOpen) {\n      fetchRequiredData();\n    }\n  }, [isOpen]);\n  const fetchRequiredData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch patients, appointments, and treatments\n      // These would typically come from your API\n      const [patientsResponse, appointmentsResponse, treatmentsResponse] = await Promise.all([fetch('/api/patients').then(res => res.json()), fetch('/api/appointments').then(res => res.json()), fetch('/api/treatments').then(res => res.json())]);\n      setPatients(patientsResponse.docs || []);\n      setAppointments(appointmentsResponse.docs || []);\n      setTreatments(treatmentsResponse.docs || []);\n    } catch (error) {\n      console.error('Failed to fetch required data:', error);\n      toast.error('加载数据失败，请稍后重试');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSuccess = (newBill: Bill) => {\n    if (onSuccess) {\n      onSuccess(newBill);\n    }\n    onClose();\n  };\n  const isEditing = !!bill;\n  return <Dialog open={isOpen} onOpenChange={onClose} data-sentry-element=\"Dialog\" data-sentry-component=\"BillDialog\" data-sentry-source-file=\"bill-dialog.tsx\">\n      <DialogContent className=\"max-w-5xl max-h-[90vh] overflow-y-auto\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"bill-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"bill-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"bill-dialog.tsx\">\n            {isEditing ? `编辑账单 - ${bill?.billNumber}` : '创建新账单'}\n          </DialogTitle>\n        </DialogHeader>\n        \n        {loading ? <div className=\"flex items-center justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n            <span className=\"ml-2 text-muted-foreground\">加载数据中...</span>\n          </div> : <BillForm bill={bill || undefined} patients={patients} appointments={appointments} treatments={treatments} onSuccess={handleSuccess} onCancel={onClose} isOpen={true} />}\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Label } from '@/components/ui/label';\nimport { Badge } from '@/components/ui/badge';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { IconEdit, IconCheck, IconX, IconAlertTriangle, IconMail, IconFileText, IconCreditCard } from '@tabler/icons-react';\nimport { Bill } from '@/types/clinic';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { toast } from 'sonner';\ninterface BillStatusManagerProps {\n  bill: Bill;\n  onStatusUpdate?: (updatedBill: Bill) => void;\n  trigger?: React.ReactNode;\n}\n\n// Define valid status transitions\nconst statusTransitions: Record<string, string[]> = {\n  draft: ['sent', 'cancelled'],\n  sent: ['confirmed', 'cancelled'],\n  confirmed: ['paid', 'cancelled'],\n  paid: [],\n  // Final status - no transitions allowed\n  cancelled: [] // Final status - no transitions allowed\n};\n\n// Status display configuration\nconst statusConfig = {\n  draft: {\n    label: '草稿',\n    color: 'bg-gray-100 text-gray-800',\n    icon: IconFileText,\n    description: '账单正在编辑中'\n  },\n  sent: {\n    label: '已发送',\n    color: 'bg-blue-100 text-blue-800',\n    icon: IconMail,\n    description: '账单已发送给患者'\n  },\n  confirmed: {\n    label: '已确认',\n    color: 'bg-yellow-100 text-yellow-800',\n    icon: IconCheck,\n    description: '患者已确认账单'\n  },\n  paid: {\n    label: '已支付',\n    color: 'bg-green-100 text-green-800',\n    icon: IconCreditCard,\n    description: '账单已完全支付'\n  },\n  cancelled: {\n    label: '已取消',\n    color: 'bg-red-100 text-red-800',\n    icon: IconX,\n    description: '账单已取消'\n  }\n};\nexport function BillStatusManager({\n  bill,\n  onStatusUpdate,\n  trigger\n}: BillStatusManagerProps) {\n  const {\n    hasPermission\n  } = useRole();\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedStatus, setSelectedStatus] = useState(bill.status);\n  const [notes, setNotes] = useState('');\n  const [isUpdating, setIsUpdating] = useState(false);\n  const currentStatusConfig = statusConfig[bill.status];\n  const availableTransitions = statusTransitions[bill.status] || [];\n  const canUpdateStatus = hasPermission('canEditBills') && availableTransitions.length > 0;\n  const handleStatusUpdate = async () => {\n    if (selectedStatus === bill.status) {\n      toast.warning('请选择不同的状态');\n      return;\n    }\n    try {\n      setIsUpdating(true);\n\n      // Validate transition\n      if (!availableTransitions.includes(selectedStatus)) {\n        toast.error('无效的状态转换');\n        return;\n      }\n\n      // Special validation for paid status\n      if (selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0) {\n        toast.error('账单还有未支付金额，无法标记为已支付');\n        return;\n      }\n      const updateData: Partial<Bill> = {\n        status: selectedStatus as Bill['status']\n      };\n\n      // Add notes if provided\n      if (notes.trim()) {\n        updateData.notes = bill.notes ? `${bill.notes}\\n\\n[状态更新] ${notes.trim()}` : `[状态更新] ${notes.trim()}`;\n      }\n      const updatedBill = await billsAPI.updateBill(bill.id, updateData);\n      toast.success(`账单状态已更新为: ${statusConfig[selectedStatus].label}`);\n      if (onStatusUpdate) {\n        onStatusUpdate(updatedBill);\n      }\n      setIsOpen(false);\n      setNotes('');\n    } catch (error) {\n      console.error('Failed to update bill status:', error);\n      const errorMessage = error instanceof BillingAPIError ? error.message : '状态更新失败，请稍后重试';\n      toast.error(errorMessage);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n  const getStatusWarning = (status: string) => {\n    switch (status) {\n      case 'sent':\n        return '发送账单后，患者将收到账单通知';\n      case 'confirmed':\n        return '确认账单表示患者已同意账单内容';\n      case 'paid':\n        return '标记为已支付前，请确保所有款项已收到';\n      case 'cancelled':\n        return '取消账单后将无法恢复，请谨慎操作';\n      default:\n        return null;\n    }\n  };\n  if (!canUpdateStatus) {\n    return <Badge className={currentStatusConfig.color}>\n        <currentStatusConfig.icon className=\"h-3 w-3 mr-1\" />\n        {currentStatusConfig.label}\n      </Badge>;\n  }\n  return <Dialog open={isOpen} onOpenChange={setIsOpen} data-sentry-element=\"Dialog\" data-sentry-component=\"BillStatusManager\" data-sentry-source-file=\"bill-status-manager.tsx\">\n      <DialogTrigger asChild data-sentry-element=\"DialogTrigger\" data-sentry-source-file=\"bill-status-manager.tsx\">\n        {trigger || <Button variant=\"outline\" size=\"sm\">\n            <IconEdit className=\"h-4 w-4 mr-2\" />\n            更新状态\n          </Button>}\n      </DialogTrigger>\n      \n      <DialogContent className=\"max-w-md\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"bill-status-manager.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"bill-status-manager.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"bill-status-manager.tsx\">更新账单状态</DialogTitle>\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"bill-status-manager.tsx\">\n            账单编号: {bill.billNumber}\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4\">\n          {/* Current Status */}\n          <div>\n            <Label className=\"text-sm font-medium\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-status-manager.tsx\">当前状态</Label>\n            <div className=\"mt-1\">\n              <Badge className={currentStatusConfig.color} data-sentry-element=\"Badge\" data-sentry-source-file=\"bill-status-manager.tsx\">\n                <currentStatusConfig.icon className=\"h-3 w-3 mr-1\" data-sentry-element=\"currentStatusConfig.icon\" data-sentry-source-file=\"bill-status-manager.tsx\" />\n                {currentStatusConfig.label}\n              </Badge>\n              <p className=\"text-xs text-muted-foreground mt-1\">\n                {currentStatusConfig.description}\n              </p>\n            </div>\n          </div>\n\n          {/* New Status Selection */}\n          <div>\n            <Label htmlFor=\"status-select\" className=\"text-sm font-medium\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-status-manager.tsx\">\n              新状态\n            </Label>\n            <Select value={selectedStatus} onValueChange={value => setSelectedStatus(value as typeof selectedStatus)} data-sentry-element=\"Select\" data-sentry-source-file=\"bill-status-manager.tsx\">\n              <SelectTrigger className=\"mt-1\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"bill-status-manager.tsx\">\n                <SelectValue placeholder=\"选择新状态\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"bill-status-manager.tsx\" />\n              </SelectTrigger>\n              <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"bill-status-manager.tsx\">\n                {availableTransitions.map(status => {\n                const config = statusConfig[status as keyof typeof statusConfig];\n                const Icon = config.icon;\n                return <SelectItem key={status} value={status}>\n                      <div className=\"flex items-center gap-2\">\n                        <Icon className=\"h-4 w-4\" />\n                        <span>{config.label}</span>\n                      </div>\n                    </SelectItem>;\n              })}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Status Warning */}\n          {selectedStatus !== bill.status && getStatusWarning(selectedStatus) && <Alert>\n              <IconAlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                {getStatusWarning(selectedStatus)}\n              </AlertDescription>\n            </Alert>}\n\n          {/* Special validation for paid status */}\n          {selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0 && <Alert variant=\"destructive\">\n              <IconAlertTriangle className=\"h-4 w-4\" />\n              <AlertDescription>\n                账单还有 {billingUtils.formatCurrency(bill.remainingAmount || 0)} 未支付，\n                无法标记为已支付状态。\n              </AlertDescription>\n            </Alert>}\n\n          {/* Notes */}\n          <div>\n            <Label htmlFor=\"notes\" className=\"text-sm font-medium\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-status-manager.tsx\">\n              备注 (可选)\n            </Label>\n            <Textarea id=\"notes\" placeholder=\"添加状态更新的备注...\" value={notes} onChange={e => setNotes(e.target.value)} className=\"mt-1 resize-none\" rows={3} data-sentry-element=\"Textarea\" data-sentry-source-file=\"bill-status-manager.tsx\" />\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-2 pt-2\">\n            <Button onClick={handleStatusUpdate} disabled={isUpdating || selectedStatus === bill.status || selectedStatus === 'paid' && (bill.remainingAmount || 0) > 0} className=\"flex-1\" data-sentry-element=\"Button\" data-sentry-source-file=\"bill-status-manager.tsx\">\n              {isUpdating ? '更新中...' : '确认更新'}\n            </Button>\n            <Button variant=\"outline\" onClick={() => {\n            setIsOpen(false);\n            setSelectedStatus(bill.status);\n            setNotes('');\n          }} disabled={isUpdating} data-sentry-element=\"Button\" data-sentry-source-file=\"bill-status-manager.tsx\">\n              取消\n            </Button>\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>;\n}\n\n// Export status configuration for use in other components\nexport { statusConfig, statusTransitions };", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'currency-yuan', 'IconCurrencyYuan', [[\"path\",{\"d\":\"M12 19v-7l-5 -7\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M17 5l-5 7\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M8 13h8\",\"key\":\"svg-2\"}]]);", "'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { IconFilter, IconX, IconSearch, IconCalendar, IconCurrencyYuan, IconUser, IconFileText } from '@tabler/icons-react';\nimport { billingUtils } from '@/lib/api/billing';\nexport interface BillFilterOptions {\n  search?: string;\n  status?: string;\n  billType?: string;\n  patientId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n  amountMin?: number;\n  amountMax?: number;\n}\ninterface BillFiltersProps {\n  filters: BillFilterOptions;\n  onFiltersChange: (filters: BillFilterOptions) => void;\n  patients?: Array<{\n    id: string;\n    fullName: string;\n    phone: string;\n  }>;\n  className?: string;\n}\nconst billStatuses = [{\n  value: 'draft',\n  label: '草稿'\n}, {\n  value: 'sent',\n  label: '已发送'\n}, {\n  value: 'confirmed',\n  label: '已确认'\n}, {\n  value: 'paid',\n  label: '已支付'\n}, {\n  value: 'cancelled',\n  label: '已取消'\n}];\nconst billTypes = [{\n  value: 'treatment',\n  label: '治疗账单'\n}, {\n  value: 'consultation',\n  label: '咨询账单'\n}, {\n  value: 'deposit',\n  label: '押金账单'\n}, {\n  value: 'additional',\n  label: '补充账单'\n}];\nexport function BillFilters({\n  filters,\n  onFiltersChange,\n  patients = [],\n  className\n}: BillFiltersProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [localFilters, setLocalFilters] = useState<BillFilterOptions>(filters);\n  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '' && value !== null);\n  const activeFilterCount = Object.values(filters).filter(value => value !== undefined && value !== '' && value !== null).length;\n  const handleFilterChange = (key: keyof BillFilterOptions, value: any) => {\n    const newFilters = {\n      ...localFilters,\n      [key]: value\n    };\n    setLocalFilters(newFilters);\n  };\n  const applyFilters = () => {\n    onFiltersChange(localFilters);\n    setIsOpen(false);\n  };\n  const clearFilters = () => {\n    const emptyFilters: BillFilterOptions = {};\n    setLocalFilters(emptyFilters);\n    onFiltersChange(emptyFilters);\n    setIsOpen(false);\n  };\n  const clearSingleFilter = (key: keyof BillFilterOptions) => {\n    const newFilters = {\n      ...filters\n    };\n    delete newFilters[key];\n    onFiltersChange(newFilters);\n  };\n  return <div className={`space-y-3 ${className}`} data-sentry-component=\"BillFilters\" data-sentry-source-file=\"bill-filters.tsx\">\n      {/* Search Bar */}\n      <div className=\"flex items-center gap-2\">\n        <div className=\"relative flex-1\">\n          <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" data-sentry-element=\"IconSearch\" data-sentry-source-file=\"bill-filters.tsx\" />\n          <Input placeholder=\"搜索账单编号、患者姓名或描述...\" value={filters.search || ''} onChange={e => onFiltersChange({\n          ...filters,\n          search: e.target.value\n        })} className=\"pl-10\" data-sentry-element=\"Input\" data-sentry-source-file=\"bill-filters.tsx\" />\n        </div>\n        \n        {/* Advanced Filters Popover */}\n        <Popover open={isOpen} onOpenChange={setIsOpen} data-sentry-element=\"Popover\" data-sentry-source-file=\"bill-filters.tsx\">\n          <PopoverTrigger asChild data-sentry-element=\"PopoverTrigger\" data-sentry-source-file=\"bill-filters.tsx\">\n            <Button variant=\"outline\" className=\"relative\" data-sentry-element=\"Button\" data-sentry-source-file=\"bill-filters.tsx\">\n              <IconFilter className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconFilter\" data-sentry-source-file=\"bill-filters.tsx\" />\n              高级筛选\n              {activeFilterCount > 0 && <Badge variant=\"secondary\" className=\"ml-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs\">\n                  {activeFilterCount}\n                </Badge>}\n            </Button>\n          </PopoverTrigger>\n          \n          <PopoverContent className=\"w-80 p-4\" align=\"end\" data-sentry-element=\"PopoverContent\" data-sentry-source-file=\"bill-filters.tsx\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <h4 className=\"font-medium\">高级筛选</h4>\n                <Button variant=\"ghost\" size=\"sm\" onClick={() => setIsOpen(false)} data-sentry-element=\"Button\" data-sentry-source-file=\"bill-filters.tsx\">\n                  <IconX className=\"h-4 w-4\" data-sentry-element=\"IconX\" data-sentry-source-file=\"bill-filters.tsx\" />\n                </Button>\n              </div>\n\n              <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"bill-filters.tsx\" />\n\n              {/* Status Filter */}\n              <div className=\"space-y-2\">\n                <Label className=\"flex items-center gap-2\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">\n                  <IconFileText className=\"h-4 w-4\" data-sentry-element=\"IconFileText\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  账单状态\n                </Label>\n                <Select value={localFilters.status || ''} onValueChange={value => handleFilterChange('status', value || undefined)} data-sentry-element=\"Select\" data-sentry-source-file=\"bill-filters.tsx\">\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"bill-filters.tsx\">\n                    <SelectValue placeholder=\"选择状态\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  </SelectTrigger>\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"bill-filters.tsx\">\n                    <SelectItem value=\"\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"bill-filters.tsx\">全部状态</SelectItem>\n                    {billStatuses.map(status => <SelectItem key={status.value} value={status.value}>\n                        {status.label}\n                      </SelectItem>)}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Bill Type Filter */}\n              <div className=\"space-y-2\">\n                <Label data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">账单类型</Label>\n                <Select value={localFilters.billType || ''} onValueChange={value => handleFilterChange('billType', value || undefined)} data-sentry-element=\"Select\" data-sentry-source-file=\"bill-filters.tsx\">\n                  <SelectTrigger data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"bill-filters.tsx\">\n                    <SelectValue placeholder=\"选择类型\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  </SelectTrigger>\n                  <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"bill-filters.tsx\">\n                    <SelectItem value=\"\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"bill-filters.tsx\">全部类型</SelectItem>\n                    {billTypes.map(type => <SelectItem key={type.value} value={type.value}>\n                        {type.label}\n                      </SelectItem>)}\n                  </SelectContent>\n                </Select>\n              </div>\n\n              {/* Patient Filter */}\n              {patients.length > 0 && <div className=\"space-y-2\">\n                  <Label className=\"flex items-center gap-2\">\n                    <IconUser className=\"h-4 w-4\" />\n                    患者\n                  </Label>\n                  <Select value={localFilters.patientId || ''} onValueChange={value => handleFilterChange('patientId', value || undefined)}>\n                    <SelectTrigger>\n                      <SelectValue placeholder=\"选择患者\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"\">全部患者</SelectItem>\n                      {patients.map(patient => <SelectItem key={patient.id} value={patient.id}>\n                          {patient.fullName} - {patient.phone}\n                        </SelectItem>)}\n                    </SelectContent>\n                  </Select>\n                </div>}\n\n              {/* Date Range Filter */}\n              <div className=\"space-y-2\">\n                <Label className=\"flex items-center gap-2\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">\n                  <IconCalendar className=\"h-4 w-4\" data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  日期范围\n                </Label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">开始日期</Label>\n                    <Input type=\"date\" value={localFilters.dateFrom || ''} onChange={e => handleFilterChange('dateFrom', e.target.value || undefined)} className=\"text-sm\" data-sentry-element=\"Input\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  </div>\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">结束日期</Label>\n                    <Input type=\"date\" value={localFilters.dateTo || ''} onChange={e => handleFilterChange('dateTo', e.target.value || undefined)} className=\"text-sm\" data-sentry-element=\"Input\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  </div>\n                </div>\n              </div>\n\n              {/* Amount Range Filter */}\n              <div className=\"space-y-2\">\n                <Label className=\"flex items-center gap-2\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">\n                  <IconCurrencyYuan className=\"h-4 w-4\" data-sentry-element=\"IconCurrencyYuan\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  金额范围\n                </Label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">最小金额</Label>\n                    <Input type=\"number\" step=\"0.01\" min=\"0\" placeholder=\"0.00\" value={localFilters.amountMin || ''} onChange={e => handleFilterChange('amountMin', parseFloat(e.target.value) || undefined)} className=\"text-sm\" data-sentry-element=\"Input\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  </div>\n                  <div>\n                    <Label className=\"text-xs text-muted-foreground\" data-sentry-element=\"Label\" data-sentry-source-file=\"bill-filters.tsx\">最大金额</Label>\n                    <Input type=\"number\" step=\"0.01\" min=\"0\" placeholder=\"无限制\" value={localFilters.amountMax || ''} onChange={e => handleFilterChange('amountMax', parseFloat(e.target.value) || undefined)} className=\"text-sm\" data-sentry-element=\"Input\" data-sentry-source-file=\"bill-filters.tsx\" />\n                  </div>\n                </div>\n              </div>\n\n              <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"bill-filters.tsx\" />\n\n              {/* Action Buttons */}\n              <div className=\"flex gap-2\">\n                <Button onClick={applyFilters} className=\"flex-1\" data-sentry-element=\"Button\" data-sentry-source-file=\"bill-filters.tsx\">\n                  应用筛选\n                </Button>\n                <Button variant=\"outline\" onClick={clearFilters} data-sentry-element=\"Button\" data-sentry-source-file=\"bill-filters.tsx\">\n                  清除\n                </Button>\n              </div>\n            </div>\n          </PopoverContent>\n        </Popover>\n      </div>\n\n      {/* Active Filters Display */}\n      {hasActiveFilters && <div className=\"flex flex-wrap gap-2\">\n          {filters.status && <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              状态: {billStatuses.find(s => s.value === filters.status)?.label}\n              <button onClick={() => clearSingleFilter('status')} className=\"ml-1 hover:bg-muted rounded-full p-0.5\">\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>}\n          \n          {filters.billType && <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              类型: {billTypes.find(t => t.value === filters.billType)?.label}\n              <button onClick={() => clearSingleFilter('billType')} className=\"ml-1 hover:bg-muted rounded-full p-0.5\">\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>}\n          \n          {filters.patientId && <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              患者: {patients.find(p => p.id === filters.patientId)?.fullName}\n              <button onClick={() => clearSingleFilter('patientId')} className=\"ml-1 hover:bg-muted rounded-full p-0.5\">\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>}\n          \n          {(filters.dateFrom || filters.dateTo) && <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              日期: {filters.dateFrom || '开始'} ~ {filters.dateTo || '结束'}\n              <button onClick={() => {\n          clearSingleFilter('dateFrom');\n          clearSingleFilter('dateTo');\n        }} className=\"ml-1 hover:bg-muted rounded-full p-0.5\">\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>}\n          \n          {(filters.amountMin !== undefined || filters.amountMax !== undefined) && <Badge variant=\"secondary\" className=\"flex items-center gap-1\">\n              金额: {filters.amountMin ? billingUtils.formatCurrency(filters.amountMin) : '0'} ~ {filters.amountMax ? billingUtils.formatCurrency(filters.amountMax) : '∞'}\n              <button onClick={() => {\n          clearSingleFilter('amountMin');\n          clearSingleFilter('amountMax');\n        }} className=\"ml-1 hover:bg-muted rounded-full p-0.5\">\n                <IconX className=\"h-3 w-3\" />\n              </button>\n            </Badge>}\n          \n          {hasActiveFilters && <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters} className=\"h-6 px-2\">\n              清除全部\n            </Button>}\n        </div>}\n    </div>;\n}", "'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { IconPlus, IconReceipt, IconSearch, IconEdit, IconEye, IconCreditCard, IconCash, IconAlertCircle, IconRefresh } from '@tabler/icons-react';\nimport { Bill, Payment } from '@/types/clinic';\nimport { toast } from 'sonner';\nimport { t } from '@/lib/translations';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { PaymentDialog } from './payment-dialog';\nimport { BillDialog } from './bill-dialog';\nimport { BillStatusManager } from './bill-status-manager';\nimport { BillFilters, BillFilterOptions } from './bill-filters';\n\n// Status mapping for bill status\nconst getStatusVariant = (status: string): \"default\" | \"secondary\" | \"destructive\" | \"outline\" => {\n  switch (status) {\n    case 'paid':\n      return 'default';\n    case 'confirmed':\n      return 'secondary';\n    case 'cancelled':\n      return 'destructive';\n    default:\n      return 'outline';\n  }\n};\nconst getStatusColor = (status: string): string => {\n  switch (status) {\n    case 'paid':\n      return 'text-green-600 bg-green-50 border-green-200';\n    case 'confirmed':\n      return 'text-blue-600 bg-blue-50 border-blue-200';\n    case 'cancelled':\n      return 'text-red-600 bg-red-50 border-red-200';\n    case 'draft':\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n    case 'sent':\n      return 'text-yellow-600 bg-yellow-50 border-yellow-200';\n    default:\n      return 'text-gray-600 bg-gray-50 border-gray-200';\n  }\n};\n\n// Status badge component\nconst StatusBadge = ({\n  status\n}: {\n  status: string;\n}) => {\n  const getStatusColor = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'paid':\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';\n      case 'partial':\n        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';\n      case 'overdue':\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\n      case 'cancelled':\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n      default:\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n    }\n  };\n  const getStatusText = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'paid':\n        return '已支付';\n      case 'pending':\n        return '待支付';\n      case 'partial':\n        return '部分支付';\n      case 'overdue':\n        return '逾期';\n      case 'cancelled':\n        return '已取消';\n      default:\n        return status;\n    }\n  };\n  return <Badge className={getStatusColor(status)} data-sentry-element=\"Badge\" data-sentry-component=\"StatusBadge\" data-sentry-source-file=\"billing-list.tsx\">\n      {getStatusText(status)}\n    </Badge>;\n};\n\n// Bill type badge component\nconst BillTypeBadge = ({\n  type\n}: {\n  type: string;\n}) => {\n  const getTypeColor = (type: string) => {\n    switch (type.toLowerCase()) {\n      case 'treatment':\n        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';\n      case 'consultation':\n        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';\n      case 'deposit':\n        return 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200';\n      case 'additional':\n        return 'bg-pink-100 text-pink-800 hover:bg-pink-200';\n      default:\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n    }\n  };\n  const getTypeText = (type: string) => {\n    switch (type.toLowerCase()) {\n      case 'treatment':\n        return '治疗';\n      case 'consultation':\n        return '咨询';\n      case 'deposit':\n        return '押金';\n      case 'additional':\n        return '附加';\n      default:\n        return type;\n    }\n  };\n  return <Badge variant=\"outline\" className={getTypeColor(type)} data-sentry-element=\"Badge\" data-sentry-component=\"BillTypeBadge\" data-sentry-source-file=\"billing-list.tsx\">\n      {getTypeText(type)}\n    </Badge>;\n};\nexport function BillingList() {\n  const {\n    hasPermission\n  } = useRole();\n  const [bills, setBills] = useState<Bill[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [filters, setFilters] = useState<BillFilterOptions>({});\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [refreshing, setRefreshing] = useState(false);\n  const [patients, setPatients] = useState<Array<{\n    id: string;\n    fullName: string;\n    phone: string;\n  }>>([]);\n  const [selectedBillForPayment, setSelectedBillForPayment] = useState<Bill | null>(null);\n  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);\n  const [selectedBillForEdit, setSelectedBillForEdit] = useState<Bill | null>(null);\n  const [isBillDialogOpen, setIsBillDialogOpen] = useState(false);\n  const [isCreatingBill, setIsCreatingBill] = useState(false);\n\n  // Fetch bills from API\n  const fetchBills = async (page: number = 1, currentFilters: BillFilterOptions = {}) => {\n    try {\n      setLoading(page === 1);\n      setError(null);\n      const response = await billsAPI.fetchBills({\n        page,\n        limit: 10,\n        search: currentFilters.search || undefined,\n        status: currentFilters.status || undefined,\n        patientId: currentFilters.patientId || undefined,\n        dateFrom: currentFilters.dateFrom || undefined,\n        dateTo: currentFilters.dateTo || undefined\n      });\n      setBills(response.docs);\n      setCurrentPage(response.page);\n      setTotalPages(response.totalPages);\n    } catch (err) {\n      console.error('Failed to fetch bills:', err);\n      const errorMessage = err instanceof BillingAPIError ? err.message : '加载账单失败，请稍后重试。';\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // Fetch patients for filter dropdown\n  const fetchPatients = async () => {\n    try {\n      const response = await fetch('/api/patients');\n      const data = await response.json();\n      setPatients(data.docs || []);\n    } catch (error) {\n      console.error('Failed to fetch patients:', error);\n    }\n  };\n\n  // Initial load\n  useEffect(() => {\n    fetchBills(1, filters);\n    fetchPatients();\n  }, []);\n\n  // Handle filter changes with debouncing\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      fetchBills(1, filters);\n    }, 500);\n    return () => clearTimeout(timeoutId);\n  }, [filters]);\n\n  // Refresh bills\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchBills(currentPage, filters);\n  };\n  const handleFiltersChange = (newFilters: BillFilterOptions) => {\n    setFilters(newFilters);\n    setCurrentPage(1); // Reset to first page when filters change\n  };\n  const handleNewBill = () => {\n    setSelectedBillForEdit(null);\n    setIsCreatingBill(true);\n    setIsBillDialogOpen(true);\n  };\n  const handleViewBill = (bill: Bill) => {\n    toast.info(`查看账单: ${bill.billNumber}`);\n    // TODO: Implement bill view dialog\n  };\n  const handleEditBill = (bill: Bill) => {\n    setSelectedBillForEdit(bill);\n    setIsCreatingBill(false);\n    setIsBillDialogOpen(true);\n  };\n  const handlePayment = (bill: Bill) => {\n    setSelectedBillForPayment(bill);\n    setIsPaymentDialogOpen(true);\n  };\n  const handlePaymentSuccess = (payment: Payment) => {\n    toast.success(`支付处理成功！收据编号: ${payment.receiptNumber || '待生成'}`);\n    // Refresh the bills list to show updated payment status\n    fetchBills(currentPage, filters);\n  };\n  const handleClosePaymentDialog = () => {\n    setIsPaymentDialogOpen(false);\n    setSelectedBillForPayment(null);\n  };\n  const handleBillSuccess = (bill: Bill) => {\n    const action = isCreatingBill ? '创建' : '更新';\n    toast.success(`账单${action}成功！账单编号: ${bill.billNumber}`);\n    // Refresh the bills list\n    fetchBills(currentPage, filters);\n  };\n  const handleCloseBillDialog = () => {\n    setIsBillDialogOpen(false);\n    setSelectedBillForEdit(null);\n    setIsCreatingBill(false);\n  };\n  const handleStatusUpdate = (updatedBill: Bill) => {\n    // Update the bill in the local state\n    setBills(prevBills => prevBills.map(bill => bill.id === updatedBill.id ? updatedBill : bill));\n    toast.success('账单状态已更新');\n  };\n  const handlePageChange = (page: number) => {\n    fetchBills(page, filters);\n  };\n  if (loading) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载账单中...</p>\n        </div>\n      </div>;\n  }\n  if (error && bills.length === 0) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-red-600 mb-2\">加载失败</h3>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <Button onClick={handleRefresh} variant=\"outline\">\n            <IconRefresh className=\"h-4 w-4 mr-2\" />\n            重试\n          </Button>\n        </div>\n      </div>;\n  }\n  if (bills.length === 0) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconReceipt className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n          <h3 className=\"text-lg font-medium mb-2\">暂无账单</h3>\n          <p className=\"text-muted-foreground mb-4\">\n            开始创建您的第一个账单。\n          </p>\n          <PermissionGate permission=\"canCreateBills\">\n            <Button onClick={handleNewBill}>\n              <IconPlus className=\"h-4 w-4 mr-2\" />\n              新建账单\n            </Button>\n          </PermissionGate>\n        </div>\n      </div>;\n  }\n  return <div className=\"space-y-4\" data-sentry-component=\"BillingList\" data-sentry-source-file=\"billing-list.tsx\">\n      {/* Advanced Filters */}\n      <BillFilters filters={filters} onFiltersChange={handleFiltersChange} patients={patients} data-sentry-element=\"BillFilters\" data-sentry-source-file=\"billing-list.tsx\" />\n\n      {/* Header with actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh} disabled={refreshing} data-sentry-element=\"Button\" data-sentry-source-file=\"billing-list.tsx\">\n            <IconRefresh className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} data-sentry-element=\"IconRefresh\" data-sentry-source-file=\"billing-list.tsx\" />\n            刷新\n          </Button>\n          <div className=\"text-sm text-muted-foreground\">\n            共 {bills.length} 个账单\n          </div>\n        </div>\n        <PermissionGate permission=\"canCreateBills\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-list.tsx\">\n          <Button onClick={handleNewBill} data-sentry-element=\"Button\" data-sentry-source-file=\"billing-list.tsx\">\n            <IconPlus className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconPlus\" data-sentry-source-file=\"billing-list.tsx\" />\n            新建账单\n          </Button>\n        </PermissionGate>\n      </div>\n\n      {/* Error banner for non-fatal errors */}\n      {error && bills.length > 0 && <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-center\">\n            <IconAlertCircle className=\"h-4 w-4 text-red-500 mr-2\" />\n            <span className=\"text-red-700 text-sm\">{error}</span>\n          </div>\n        </div>}\n\n      {/* Bills Grid */}\n      <div className=\"grid gap-4\">\n        {bills.map(bill => <div key={bill.id} className=\"border rounded-lg p-4 space-y-3\">\n            <div className=\"flex items-start justify-between\">\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center gap-2\">\n                  <h4 className=\"font-medium text-lg\">{bill.billNumber}</h4>\n                  <BillTypeBadge type={bill.billType} />\n                  <BillStatusManager bill={bill} onStatusUpdate={handleStatusUpdate} trigger={<StatusBadge status={bill.status} />} />\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  <p>患者: {typeof bill.patient === 'object' ? bill.patient.fullName : '未知患者'}</p>\n                  <p>描述: {bill.description}</p>\n                  <p>开票日期: {new Date(bill.issueDate).toLocaleDateString('zh-CN')}</p>\n                </div>\n              </div>\n              \n              <div className=\"text-right space-y-1\">\n                <div className=\"text-lg font-semibold\">\n                  {billingUtils.formatCurrency(bill.totalAmount)}\n                </div>\n                {(bill.remainingAmount || 0) > 0 && <div className=\"text-sm text-red-600\">\n                    待收: {billingUtils.formatCurrency(bill.remainingAmount || 0)}\n                  </div>}\n                {(bill.paidAmount || 0) > 0 && <div className=\"text-sm text-green-600\">\n                    已收: {billingUtils.formatCurrency(bill.paidAmount || 0)}\n                  </div>}\n              </div>\n            </div>\n            \n            <div className=\"flex items-center justify-between pt-2 border-t\">\n              <div className=\"text-xs text-muted-foreground\">\n                到期日期: {new Date(bill.dueDate).toLocaleDateString('zh-CN')}\n              </div>\n              \n              <div className=\"flex items-center gap-2\">\n                <Button variant=\"ghost\" size=\"sm\" onClick={() => handleViewBill(bill)}>\n                  <IconEye className=\"h-4 w-4\" />\n                </Button>\n                <PermissionGate permission=\"canEditBills\">\n                  <Button variant=\"ghost\" size=\"sm\" onClick={() => handleEditBill(bill)}>\n                    <IconEdit className=\"h-4 w-4\" />\n                  </Button>\n                </PermissionGate>\n                {(bill.remainingAmount || 0) > 0 && <PermissionGate permission=\"canProcessPayments\">\n                    <Button variant=\"default\" size=\"sm\" onClick={() => handlePayment(bill)}>\n                      <IconCreditCard className=\"h-4 w-4 mr-1\" />\n                      收款\n                    </Button>\n                  </PermissionGate>}\n              </div>\n            </div>\n          </div>)}\n      </div>\n\n      {/* Pagination */}\n      {totalPages > 1 && <div className=\"flex items-center justify-center space-x-2 pt-4\">\n          <Button variant=\"outline\" size=\"sm\" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage <= 1}>\n            上一页\n          </Button>\n          <span className=\"text-sm text-muted-foreground\">\n            第 {currentPage} 页，共 {totalPages} 页\n          </span>\n          <Button variant=\"outline\" size=\"sm\" onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage >= totalPages}>\n            下一页\n          </Button>\n        </div>}\n\n      {/* Payment Dialog */}\n      <PaymentDialog bill={selectedBillForPayment} isOpen={isPaymentDialogOpen} onClose={handleClosePaymentDialog} onSuccess={handlePaymentSuccess} data-sentry-element=\"PaymentDialog\" data-sentry-source-file=\"billing-list.tsx\" />\n\n      {/* Bill Creation/Edit Dialog */}\n      <BillDialog bill={selectedBillForEdit} isOpen={isBillDialogOpen} onClose={handleCloseBillDialog} onSuccess={handleBillSuccess} data-sentry-element=\"BillDialog\" data-sentry-source-file=\"billing-list.tsx\" />\n    </div>;\n}", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { IconReceipt, IconCalendar, IconUser, IconStethoscope, IconCurrencyYuan, IconCheck, IconAlertTriangle, IconRefresh } from '@tabler/icons-react';\nimport { Appointment, Bill } from '@/types/clinic';\nimport { billsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { billingNotifications } from '@/lib/billing-notifications';\nimport { toast } from 'sonner';\ninterface AppointmentToBillProps {\n  onBillGenerated?: (bill: Bill) => void;\n  className?: string;\n}\ninterface AppointmentWithBillStatus extends Appointment {\n  hasBill?: boolean;\n  billId?: string;\n  billNumber?: string;\n}\nexport function AppointmentToBill({\n  onBillGenerated,\n  className\n}: AppointmentToBillProps) {\n  const {\n    hasPermission\n  } = useRole();\n  const [appointments, setAppointments] = useState<AppointmentWithBillStatus[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [generating, setGenerating] = useState<string | null>(null);\n  const [selectedBillType, setSelectedBillType] = useState<string>('treatment');\n  const fetchCompletedAppointments = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch completed appointments\n      const appointmentsResponse = await fetch('/api/appointments?status=completed&limit=50');\n      const appointmentsData = await appointmentsResponse.json();\n      if (!appointmentsResponse.ok) {\n        throw new Error(appointmentsData.error || 'Failed to fetch appointments');\n      }\n\n      // Fetch existing bills to check which appointments already have bills\n      const billsResponse = await billsAPI.fetchBills({\n        limit: 100\n      });\n      const existingBills = billsResponse.docs;\n\n      // Map appointments with bill status\n      const appointmentsWithBillStatus: AppointmentWithBillStatus[] = appointmentsData.docs.map((appointment: Appointment) => {\n        const existingBill = existingBills.find(bill => typeof bill.appointment === 'object' && bill.appointment?.id === appointment.id);\n        return {\n          ...appointment,\n          hasBill: !!existingBill,\n          billId: existingBill?.id,\n          billNumber: existingBill?.billNumber\n        };\n      });\n      setAppointments(appointmentsWithBillStatus);\n    } catch (error) {\n      console.error('Failed to fetch appointments:', error);\n      billingNotifications.system.dataRefreshError();\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchCompletedAppointments();\n  }, []);\n\n  // Check permissions after all hooks\n  if (!hasPermission('canCreateBills')) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertTriangle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">权限不足</h3>\n          <p className=\"text-muted-foreground\">\n            您没有权限从预约生成账单\n          </p>\n        </div>\n      </div>;\n  }\n  const handleGenerateBill = async (appointment: AppointmentWithBillStatus) => {\n    try {\n      setGenerating(appointment.id);\n      const response = await billsAPI.generateFromAppointment(appointment.id, selectedBillType);\n      billingNotifications.bill.generateFromAppointment(response, new Date(appointment.appointmentDate).toLocaleDateString('zh-CN'));\n\n      // Update the appointment status locally\n      setAppointments(prev => prev.map(apt => apt.id === appointment.id ? {\n        ...apt,\n        hasBill: true,\n        billId: response.id,\n        billNumber: response.billNumber\n      } : apt));\n      if (onBillGenerated) {\n        onBillGenerated(response);\n      }\n    } catch (error) {\n      console.error('Failed to generate bill:', error);\n      const errorMessage = error instanceof BillingAPIError ? error.message : undefined;\n      billingNotifications.bill.createError(errorMessage);\n    } finally {\n      setGenerating(null);\n    }\n  };\n  const getAppointmentStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800';\n      case 'confirmed':\n        return 'bg-blue-100 text-blue-800';\n      case 'scheduled':\n        return 'bg-yellow-100 text-yellow-800';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800';\n      default:\n        return 'bg-gray-100 text-gray-800';\n    }\n  };\n  const getAppointmentStatusLabel = (status: string) => {\n    const labels = {\n      scheduled: '已预约',\n      confirmed: '已确认',\n      completed: '已完成',\n      cancelled: '已取消',\n      'no-show': '未到诊'\n    };\n    return labels[status as keyof typeof labels] || status;\n  };\n  if (loading) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载已完成预约中...</p>\n        </div>\n      </div>;\n  }\n  const unbilledAppointments = appointments.filter(apt => !apt.hasBill);\n  return <div className={`space-y-6 ${className}`} data-sentry-component=\"AppointmentToBill\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold tracking-tight flex items-center gap-2\">\n            <IconReceipt className=\"size-6\" data-sentry-element=\"IconReceipt\" data-sentry-source-file=\"appointment-to-bill.tsx\" />\n            预约生成账单\n          </h2>\n          <p className=\"text-muted-foreground\">\n            从已完成的预约自动生成账单\n          </p>\n        </div>\n        <Button variant=\"outline\" onClick={fetchCompletedAppointments} disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          <IconRefresh className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconRefresh\" data-sentry-source-file=\"appointment-to-bill.tsx\" />\n          刷新\n        </Button>\n      </div>\n\n      {/* Bill Type Selection */}\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          <CardTitle className=\"text-lg\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"appointment-to-bill.tsx\">账单类型设置</CardTitle>\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n            选择生成账单的默认类型\n          </CardDescription>\n        </CardHeader>\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          <Select value={selectedBillType} onValueChange={setSelectedBillType} data-sentry-element=\"Select\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n            <SelectTrigger className=\"w-64\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n              <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"appointment-to-bill.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n              <SelectItem value=\"treatment\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"appointment-to-bill.tsx\">治疗账单</SelectItem>\n              <SelectItem value=\"consultation\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"appointment-to-bill.tsx\">咨询账单</SelectItem>\n              <SelectItem value=\"additional\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"appointment-to-bill.tsx\">补充账单</SelectItem>\n            </SelectContent>\n          </Select>\n        </CardContent>\n      </Card>\n\n      {/* Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          <CardContent className=\"pt-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-blue-600\">\n                {appointments.length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">已完成预约</div>\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          <CardContent className=\"pt-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-green-600\">\n                {appointments.filter(apt => apt.hasBill).length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">已生成账单</div>\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card data-sentry-element=\"Card\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          <CardContent className=\"pt-6\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-orange-600\">\n                {unbilledAppointments.length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">待生成账单</div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Appointments List */}\n      {unbilledAppointments.length === 0 ? <Card>\n          <CardContent className=\"py-8\">\n            <div className=\"text-center\">\n              <IconCheck className=\"h-12 w-12 text-green-500 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium mb-2\">全部预约已生成账单</h3>\n              <p className=\"text-muted-foreground\">\n                所有已完成的预约都已生成对应的账单\n              </p>\n            </div>\n          </CardContent>\n        </Card> : <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold\">待生成账单的预约</h3>\n          \n          <div className=\"grid gap-4\">\n            {unbilledAppointments.map(appointment => <Card key={appointment.id} className=\"hover:shadow-md transition-shadow\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"space-y-3 flex-1\">\n                      {/* Appointment Header */}\n                      <div className=\"flex items-center gap-3\">\n                        <Badge className={getAppointmentStatusColor(appointment.status)}>\n                          {getAppointmentStatusLabel(appointment.status)}\n                        </Badge>\n                        <span className=\"text-sm text-muted-foreground\">\n                          预约ID: {appointment.id}\n                        </span>\n                      </div>\n\n                      {/* Appointment Details */}\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm\">\n                        <div className=\"flex items-center gap-2\">\n                          <IconCalendar className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {new Date(appointment.appointmentDate).toLocaleDateString('zh-CN')}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <IconUser className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {typeof appointment.patient === 'object' ? appointment.patient.fullName : '未知患者'}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <IconStethoscope className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {typeof appointment.treatment === 'object' ? appointment.treatment.name : '未知治疗'}\n                          </span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <IconCurrencyYuan className=\"h-4 w-4 text-muted-foreground\" />\n                          <span>\n                            {billingUtils.formatCurrency(appointment.price || 0)}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Generate Bill Button */}\n                    <div className=\"ml-4\">\n                      <Button onClick={() => handleGenerateBill(appointment)} disabled={generating === appointment.id} className=\"min-w-24\">\n                        {generating === appointment.id ? <>\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                            生成中...\n                          </> : <>\n                            <IconReceipt className=\"h-4 w-4 mr-2\" />\n                            生成账单\n                          </>}\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>)}\n          </div>\n        </div>}\n\n      {/* Info Alert */}\n      <Alert data-sentry-element=\"Alert\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n        <IconAlertTriangle className=\"h-4 w-4\" data-sentry-element=\"IconAlertTriangle\" data-sentry-source-file=\"appointment-to-bill.tsx\" />\n        <AlertDescription data-sentry-element=\"AlertDescription\" data-sentry-source-file=\"appointment-to-bill.tsx\">\n          生成的账单将包含预约的治疗项目、价格和患者信息。您可以在账单列表中进一步编辑账单详情。\n        </AlertDescription>\n      </Alert>\n    </div>;\n}", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { IconSearch, IconReceipt, IconPrinter, IconDownload, IconEye, IconRefresh, IconAlertCircle } from '@tabler/icons-react';\nimport { Payment, Bill } from '@/types/clinic';\nimport { paymentsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { ReceiptDialog } from './receipt-dialog';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { toast } from 'sonner';\ninterface ReceiptManagerProps {\n  className?: string;\n}\nexport function ReceiptManager({\n  className\n}: ReceiptManagerProps) {\n  const {\n    hasPermission\n  } = useRole();\n  const [payments, setPayments] = useState<Payment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);\n  const [showReceiptDialog, setShowReceiptDialog] = useState(false);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Fetch payments with receipts\n  const fetchPayments = async (search?: string) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await paymentsAPI.fetchPayments({\n        limit: 50,\n        status: 'completed' // Only show completed payments\n      });\n\n      // Filter payments that have receipt numbers and apply search filter\n      let paymentsWithReceipts = response.docs.filter(payment => payment.receiptNumber);\n\n      // Apply search filter if provided\n      if (search) {\n        paymentsWithReceipts = paymentsWithReceipts.filter(payment => payment.receiptNumber?.toLowerCase().includes(search.toLowerCase()) || typeof payment.bill === 'object' && payment.bill.billNumber?.toLowerCase().includes(search.toLowerCase()) || typeof payment.patient === 'object' && payment.patient.fullName?.toLowerCase().includes(search.toLowerCase()));\n      }\n      setPayments(paymentsWithReceipts);\n    } catch (err) {\n      console.error('Failed to fetch payments:', err);\n      const errorMessage = err instanceof BillingAPIError ? err.message : '加载收据失败，请稍后重试。';\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  // Initial load\n  useEffect(() => {\n    fetchPayments();\n  }, []);\n\n  // Handle search with debouncing\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      if (searchTerm !== undefined) {\n        fetchPayments(searchTerm);\n      }\n    }, 500);\n    return () => clearTimeout(timeoutId);\n  }, [searchTerm]);\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    await fetchPayments(searchTerm);\n  };\n  const handleViewReceipt = (payment: Payment) => {\n    setSelectedPayment(payment);\n    setShowReceiptDialog(true);\n  };\n  const handlePrintReceipt = (payment: Payment) => {\n    // This would trigger the print functionality\n    setSelectedPayment(payment);\n    setShowReceiptDialog(true);\n    // The receipt dialog will handle the printing\n  };\n  const filteredPayments = payments.filter(payment => payment.receiptNumber?.toLowerCase().includes(searchTerm.toLowerCase()) || payment.paymentNumber.toLowerCase().includes(searchTerm.toLowerCase()) || typeof payment.patient === 'object' && payment.patient.fullName.toLowerCase().includes(searchTerm.toLowerCase()));\n  if (loading) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载收据中...</p>\n        </div>\n      </div>;\n  }\n  if (error && payments.length === 0) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold text-red-600 mb-2\">加载失败</h3>\n          <p className=\"text-muted-foreground mb-4\">{error}</p>\n          <Button onClick={handleRefresh} variant=\"outline\">\n            <IconRefresh className=\"h-4 w-4 mr-2\" />\n            重试\n          </Button>\n        </div>\n      </div>;\n  }\n  return <div className={`space-y-4 ${className}`} data-sentry-component=\"ReceiptManager\" data-sentry-source-file=\"receipt-manager.tsx\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold tracking-tight flex items-center gap-2\">\n            <IconReceipt className=\"size-6\" data-sentry-element=\"IconReceipt\" data-sentry-source-file=\"receipt-manager.tsx\" />\n            收据管理\n          </h2>\n          <p className=\"text-muted-foreground\">\n            查看、打印和管理支付收据\n          </p>\n        </div>\n      </div>\n\n      {/* Search and Actions */}\n      <div className=\"flex items-center justify-between gap-4\">\n        <div className=\"flex items-center gap-2 flex-1 max-w-md\">\n          <div className=\"relative flex-1\">\n            <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4\" data-sentry-element=\"IconSearch\" data-sentry-source-file=\"receipt-manager.tsx\" />\n            <Input placeholder=\"搜索收据编号、支付编号或患者姓名...\" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className=\"pl-10\" data-sentry-element=\"Input\" data-sentry-source-file=\"receipt-manager.tsx\" />\n          </div>\n          <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh} disabled={refreshing} data-sentry-element=\"Button\" data-sentry-source-file=\"receipt-manager.tsx\">\n            <IconRefresh className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} data-sentry-element=\"IconRefresh\" data-sentry-source-file=\"receipt-manager.tsx\" />\n          </Button>\n        </div>\n        \n        <div className=\"text-sm text-muted-foreground\">\n          共 {filteredPayments.length} 张收据\n        </div>\n      </div>\n\n      {/* Error banner for non-fatal errors */}\n      {error && payments.length > 0 && <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-center\">\n            <IconAlertCircle className=\"h-4 w-4 text-red-500 mr-2\" />\n            <span className=\"text-red-700 text-sm\">{error}</span>\n          </div>\n        </div>}\n\n      {/* Receipts List */}\n      {filteredPayments.length === 0 ? <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <IconReceipt className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium mb-2\">暂无收据</h3>\n            <p className=\"text-muted-foreground\">\n              {searchTerm ? '没有找到匹配的收据' : '还没有生成任何收据'}\n            </p>\n          </div>\n        </div> : <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {filteredPayments.map(payment => <Card key={payment.id} className=\"hover:shadow-md transition-shadow\">\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-start justify-between\">\n                  <div>\n                    <CardTitle className=\"text-sm font-medium\">\n                      {payment.receiptNumber}\n                    </CardTitle>\n                    <CardDescription className=\"text-xs\">\n                      支付编号: {payment.paymentNumber}\n                    </CardDescription>\n                  </div>\n                  <Badge variant={payment.paymentStatus === 'completed' ? 'default' : 'secondary'}>\n                    {billingUtils.getPaymentStatusName(payment.paymentStatus)}\n                  </Badge>\n                </div>\n              </CardHeader>\n\n              <CardContent className=\"space-y-3\">\n                <div className=\"space-y-1 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">患者:</span>\n                    <span>\n                      {typeof payment.patient === 'object' ? payment.patient.fullName : '未知患者'}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">支付方式:</span>\n                    <span>{billingUtils.getPaymentMethodName(payment.paymentMethod)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-muted-foreground\">支付日期:</span>\n                    <span>{new Date(payment.paymentDate).toLocaleDateString('zh-CN')}</span>\n                  </div>\n                  <div className=\"flex justify-between font-medium\">\n                    <span className=\"text-muted-foreground\">金额:</span>\n                    <span className=\"text-green-600\">\n                      {billingUtils.formatCurrency(payment.amount)}\n                    </span>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-2 pt-2 border-t\">\n                  <Button variant=\"outline\" size=\"sm\" onClick={() => handleViewReceipt(payment)} className=\"flex-1\">\n                    <IconEye className=\"h-4 w-4 mr-1\" />\n                    查看\n                  </Button>\n                  <PermissionGate permission=\"canGenerateReceipts\">\n                    <Button variant=\"outline\" size=\"sm\" onClick={() => handlePrintReceipt(payment)}>\n                      <IconPrinter className=\"h-4 w-4\" />\n                    </Button>\n                  </PermissionGate>\n                </div>\n              </CardContent>\n            </Card>)}\n        </div>}\n\n      {/* Receipt Dialog */}\n      <ReceiptDialog payment={selectedPayment} isOpen={showReceiptDialog} onClose={() => {\n      setShowReceiptDialog(false);\n      setSelectedPayment(null);\n    }} data-sentry-element=\"ReceiptDialog\" data-sentry-source-file=\"receipt-manager.tsx\" />\n    </div>;\n}", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'chart-bar', 'IconChartBar', [[\"path\",{\"d\":\"M3 13a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M15 9a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v10a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M9 5a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v14a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M4 20h14\",\"key\":\"svg-3\"}]]);", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Badge } from '@/components/ui/badge';\nimport { Separator } from '@/components/ui/separator';\nimport { IconTrendingUp, IconTrendingDown, IconCash, IconCreditCard, IconDeviceMobile, IconBuildingBank, IconAlertTriangle, IconRefresh, IconCalendar, IconChartBar } from '@tabler/icons-react';\nimport { reportsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { toast } from 'sonner';\ninterface FinancialMetrics {\n  dailyRevenue: {\n    date: string;\n    totalRevenue: number;\n    paymentCount: number;\n    paymentMethods: Record<string, {\n      amount: number;\n      count: number;\n    }>;\n  } | null;\n  monthlyRevenue: {\n    year: number;\n    month: number;\n    totalRevenue: number;\n    dailyBreakdown: Array<{\n      date: string;\n      revenue: number;\n    }>;\n  } | null;\n  outstandingBalances: {\n    totalOutstanding: number;\n    overdueAmount: number;\n    billsCount: number;\n    overdueBillsCount: number;\n    bills: Array<{\n      id: string;\n      billNumber: string;\n      patient: string;\n      amount: number;\n      dueDate: string;\n      daysOverdue: number;\n    }>;\n  } | null;\n}\nexport function FinancialDashboard() {\n  const {\n    hasPermission\n  } = useRole();\n  const [metrics, setMetrics] = useState<FinancialMetrics>({\n    dailyRevenue: null,\n    monthlyRevenue: null,\n    outstandingBalances: null\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);\n  const [selectedMonth, setSelectedMonth] = useState({\n    year: new Date().getFullYear(),\n    month: new Date().getMonth() + 1\n  });\n  const [refreshing, setRefreshing] = useState(false);\n  const fetchMetrics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const [dailyRevenue, monthlyRevenue, outstandingBalances] = await Promise.all([reportsAPI.getDailyRevenue(selectedDate).catch(() => null), reportsAPI.getMonthlyRevenue(selectedMonth.year, selectedMonth.month).catch(() => null), reportsAPI.getOutstandingBalances().catch(() => null)]);\n      setMetrics({\n        dailyRevenue,\n        monthlyRevenue,\n        outstandingBalances\n      });\n    } catch (err) {\n      console.error('Failed to fetch financial metrics:', err);\n      const errorMessage = err instanceof BillingAPIError ? err.message : '加载财务数据失败，请稍后重试';\n      setError(errorMessage);\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  useEffect(() => {\n    fetchMetrics();\n  }, [selectedDate, selectedMonth]);\n\n  // Check permissions after all hooks\n  if (!hasPermission('canViewDetailedFinancials')) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <IconAlertTriangle className=\"h-12 w-12 text-yellow-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">权限不足</h3>\n          <p className=\"text-muted-foreground\">\n            您没有权限查看详细的财务报表\n          </p>\n        </div>\n      </div>;\n  }\n  const handleRefresh = () => {\n    setRefreshing(true);\n    fetchMetrics();\n  };\n  const getPaymentMethodIcon = (method: string) => {\n    switch (method) {\n      case 'cash':\n        return IconCash;\n      case 'card':\n        return IconCreditCard;\n      case 'wechat':\n      case 'alipay':\n        return IconDeviceMobile;\n      case 'transfer':\n        return IconBuildingBank;\n      default:\n        return IconCash;\n    }\n  };\n  if (loading) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">加载财务数据中...</p>\n        </div>\n      </div>;\n  }\n  return <div className=\"space-y-6\" data-sentry-component=\"FinancialDashboard\" data-sentry-source-file=\"financial-dashboard.tsx\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold tracking-tight flex items-center gap-2\">\n            <IconChartBar className=\"size-6\" data-sentry-element=\"IconChartBar\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n            财务报表\n          </h2>\n          <p className=\"text-muted-foreground\">\n            查看收入统计、支付分析和应收账款\n          </p>\n        </div>\n        <Button variant=\"outline\" size=\"sm\" onClick={handleRefresh} disabled={refreshing} data-sentry-element=\"Button\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          <IconRefresh className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} data-sentry-element=\"IconRefresh\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n          刷新数据\n        </Button>\n      </div>\n\n      {/* Date Controls */}\n      <div className=\"flex items-center gap-4\">\n        <div className=\"flex items-center gap-2\">\n          <IconCalendar className=\"h-4 w-4\" data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n          <span className=\"text-sm font-medium\">日期选择:</span>\n          <input type=\"date\" value={selectedDate} onChange={e => setSelectedDate(e.target.value)} className=\"px-3 py-1 border rounded-md text-sm\" />\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <span className=\"text-sm font-medium\">月份选择:</span>\n          <Select value={`${selectedMonth.year}-${selectedMonth.month}`} onValueChange={value => {\n          const [year, month] = value.split('-').map(Number);\n          setSelectedMonth({\n            year,\n            month\n          });\n        }} data-sentry-element=\"Select\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            <SelectTrigger className=\"w-40\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"financial-dashboard.tsx\">\n              <SelectValue data-sentry-element=\"SelectValue\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"financial-dashboard.tsx\">\n              {Array.from({\n              length: 12\n            }, (_, i) => {\n              const month = i + 1;\n              const year = new Date().getFullYear();\n              return <SelectItem key={`${year}-${month}`} value={`${year}-${month}`}>\n                    {year}年{month}月\n                  </SelectItem>;\n            })}\n            </SelectContent>\n          </Select>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {error && <div className=\"bg-red-50 border border-red-200 rounded-md p-3\">\n          <div className=\"flex items-center\">\n            <IconAlertTriangle className=\"h-4 w-4 text-red-500 mr-2\" />\n            <span className=\"text-red-700 text-sm\">{error}</span>\n          </div>\n        </div>}\n\n      {/* Daily Revenue Card */}\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"financial-dashboard.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            <IconTrendingUp className=\"h-5 w-5\" data-sentry-element=\"IconTrendingUp\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n            日收入统计\n          </CardTitle>\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            {new Date(selectedDate).toLocaleDateString('zh-CN')} 的收入详情\n          </CardDescription>\n        </CardHeader>\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          {metrics.dailyRevenue ? <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {billingUtils.formatCurrency(metrics.dailyRevenue.totalRevenue)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">总收入</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold\">\n                    {metrics.dailyRevenue.paymentCount}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">支付笔数</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold\">\n                    {metrics.dailyRevenue.paymentCount > 0 ? billingUtils.formatCurrency(metrics.dailyRevenue.totalRevenue / metrics.dailyRevenue.paymentCount) : billingUtils.formatCurrency(0)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">平均金额</div>\n                </div>\n              </div>\n\n              <Separator />\n\n              <div>\n                <h4 className=\"font-medium mb-3\">支付方式分布</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                  {Object.entries(metrics.dailyRevenue.paymentMethods).map(([method, data]) => {\n                const Icon = getPaymentMethodIcon(method);\n                return <div key={method} className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\n                        <div className=\"flex items-center gap-2\">\n                          <Icon className=\"h-4 w-4\" />\n                          <span className=\"text-sm font-medium\">\n                            {billingUtils.getPaymentMethodName(method)}\n                          </span>\n                        </div>\n                        <div className=\"text-right\">\n                          <div className=\"text-sm font-semibold\">\n                            {billingUtils.formatCurrency(data.amount)}\n                          </div>\n                          <div className=\"text-xs text-muted-foreground\">\n                            {data.count} 笔\n                          </div>\n                        </div>\n                      </div>;\n              })}\n                </div>\n              </div>\n            </div> : <div className=\"text-center py-8 text-muted-foreground\">\n              暂无当日收入数据\n            </div>}\n        </CardContent>\n      </Card>\n\n      {/* Monthly Revenue Card */}\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"financial-dashboard.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            <IconChartBar className=\"h-5 w-5\" data-sentry-element=\"IconChartBar\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n            月度收入统计\n          </CardTitle>\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            {selectedMonth.year}年{selectedMonth.month}月的收入趋势\n          </CardDescription>\n        </CardHeader>\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          {metrics.monthlyRevenue ? <div className=\"space-y-4\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-green-600\">\n                  {billingUtils.formatCurrency(metrics.monthlyRevenue.totalRevenue)}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">月度总收入</div>\n              </div>\n\n              <Separator />\n\n              <div>\n                <h4 className=\"font-medium mb-3\">每日收入明细</h4>\n                <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n                  {metrics.monthlyRevenue.dailyBreakdown.map(day => <div key={day.date} className=\"flex justify-between items-center p-2 hover:bg-muted/50 rounded\">\n                      <span className=\"text-sm\">\n                        {new Date(day.date).toLocaleDateString('zh-CN')}\n                      </span>\n                      <span className=\"font-medium\">\n                        {billingUtils.formatCurrency(day.revenue)}\n                      </span>\n                    </div>)}\n                </div>\n              </div>\n            </div> : <div className=\"text-center py-8 text-muted-foreground\">\n              暂无月度收入数据\n            </div>}\n        </CardContent>\n      </Card>\n\n      {/* Outstanding Balances Card */}\n      <Card data-sentry-element=\"Card\" data-sentry-source-file=\"financial-dashboard.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            <IconAlertTriangle className=\"h-5 w-5\" data-sentry-element=\"IconAlertTriangle\" data-sentry-source-file=\"financial-dashboard.tsx\" />\n            应收账款\n          </CardTitle>\n          <CardDescription data-sentry-element=\"CardDescription\" data-sentry-source-file=\"financial-dashboard.tsx\">\n            待收款项和逾期账单统计\n          </CardDescription>\n        </CardHeader>\n        <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"financial-dashboard.tsx\">\n          {metrics.outstandingBalances ? <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-orange-600\">\n                    {billingUtils.formatCurrency(metrics.outstandingBalances.totalOutstanding)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">总待收金额</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {billingUtils.formatCurrency(metrics.outstandingBalances.overdueAmount)}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">逾期金额</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold\">\n                    {metrics.outstandingBalances.billsCount}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">待收账单</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-red-600\">\n                    {metrics.outstandingBalances.overdueBillsCount}\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">逾期账单</div>\n                </div>\n              </div>\n\n              {metrics.outstandingBalances.bills.length > 0 && <>\n                  <Separator />\n                  <div>\n                    <h4 className=\"font-medium mb-3\">逾期账单详情</h4>\n                    <div className=\"space-y-2 max-h-60 overflow-y-auto\">\n                      {metrics.outstandingBalances.bills.filter(bill => bill.daysOverdue > 0).map(bill => <div key={bill.id} className=\"flex items-center justify-between p-3 border rounded-lg\">\n                          <div>\n                            <div className=\"font-medium text-sm\">{bill.billNumber}</div>\n                            <div className=\"text-xs text-muted-foreground\">{bill.patient}</div>\n                          </div>\n                          <div className=\"text-right\">\n                            <div className=\"font-semibold text-red-600\">\n                              {billingUtils.formatCurrency(bill.amount)}\n                            </div>\n                            <Badge variant=\"destructive\" className=\"text-xs\">\n                              逾期 {bill.daysOverdue} 天\n                            </Badge>\n                          </div>\n                        </div>)}\n                    </div>\n                  </div>\n                </>}\n            </div> : <div className=\"text-center py-8 text-muted-foreground\">\n              暂无应收账款数据\n            </div>}\n        </CardContent>\n      </Card>\n    </div>;\n}", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'calendar-event', 'IconCalendarEvent', [[\"path\",{\"d\":\"M4 5m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M16 3l0 4\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M8 3l0 4\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M4 11l16 0\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M8 15h2v2h-2z\",\"key\":\"svg-4\"}]]);", "'use client';\n\nimport { useState } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';\nimport { BillingList } from './billing-list';\nimport { AppointmentToBill } from './appointment-to-bill';\nimport { ReceiptManager } from './receipt-manager';\nimport { FinancialDashboard } from './financial-dashboard';\nimport { IconReceipt, IconCalendarEvent, IconFileText, IconChartBar } from '@tabler/icons-react';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { Bill } from '@/types/clinic';\nimport { toast } from 'sonner';\ninterface BillingTabsProps {\n  defaultTab?: string;\n  className?: string;\n}\nexport function BillingTabs({\n  defaultTab = 'bills',\n  className\n}: BillingTabsProps) {\n  const {\n    hasPermission\n  } = useRole();\n  const [activeTab, setActiveTab] = useState(defaultTab);\n  const handleBillGenerated = (bill: Bill) => {\n    toast.success(`账单生成成功！账单编号: ${bill.billNumber}`);\n    // Switch to bills tab to show the newly created bill\n    setActiveTab('bills');\n  };\n  return <div className={className} data-sentry-component=\"BillingTabs\" data-sentry-source-file=\"billing-tabs.tsx\">\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\" data-sentry-element=\"Tabs\" data-sentry-source-file=\"billing-tabs.tsx\">\n        <TabsList className=\"grid w-full grid-cols-2 lg:grid-cols-4\" data-sentry-element=\"TabsList\" data-sentry-source-file=\"billing-tabs.tsx\">\n          <TabsTrigger value=\"bills\" className=\"flex items-center gap-2\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <IconReceipt className=\"h-4 w-4\" data-sentry-element=\"IconReceipt\" data-sentry-source-file=\"billing-tabs.tsx\" />\n            <span className=\"hidden sm:inline\">账单管理</span>\n            <span className=\"sm:hidden\">账单</span>\n          </TabsTrigger>\n          \n          <PermissionGate permission=\"canCreateBills\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <TabsTrigger value=\"generate\" className=\"flex items-center gap-2\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"billing-tabs.tsx\">\n              <IconCalendarEvent className=\"h-4 w-4\" data-sentry-element=\"IconCalendarEvent\" data-sentry-source-file=\"billing-tabs.tsx\" />\n              <span className=\"hidden sm:inline\">预约生成</span>\n              <span className=\"sm:hidden\">生成</span>\n            </TabsTrigger>\n          </PermissionGate>\n          \n          <PermissionGate permission=\"canGenerateReceipts\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <TabsTrigger value=\"receipts\" className=\"flex items-center gap-2\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"billing-tabs.tsx\">\n              <IconFileText className=\"h-4 w-4\" data-sentry-element=\"IconFileText\" data-sentry-source-file=\"billing-tabs.tsx\" />\n              <span className=\"hidden sm:inline\">收据管理</span>\n              <span className=\"sm:hidden\">收据</span>\n            </TabsTrigger>\n          </PermissionGate>\n          \n          <PermissionGate permission=\"canViewDetailedFinancials\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <TabsTrigger value=\"reports\" className=\"flex items-center gap-2\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"billing-tabs.tsx\">\n              <IconChartBar className=\"h-4 w-4\" data-sentry-element=\"IconChartBar\" data-sentry-source-file=\"billing-tabs.tsx\" />\n              <span className=\"hidden sm:inline\">财务报表</span>\n              <span className=\"sm:hidden\">报表</span>\n            </TabsTrigger>\n          </PermissionGate>\n        </TabsList>\n\n        {/* Bills Management Tab */}\n        <TabsContent value=\"bills\" className=\"space-y-6\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"billing-tabs.tsx\">\n          <div>\n            <h2 className=\"text-2xl font-bold tracking-tight mb-2\">账单管理</h2>\n            <p className=\"text-muted-foreground mb-6\">\n              管理所有账单，处理支付和查看账单状态\n            </p>\n          </div>\n          <BillingList data-sentry-element=\"BillingList\" data-sentry-source-file=\"billing-tabs.tsx\" />\n        </TabsContent>\n\n        {/* Generate from Appointments Tab */}\n        <PermissionGate permission=\"canCreateBills\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-tabs.tsx\">\n          <TabsContent value=\"generate\" className=\"space-y-6\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <AppointmentToBill onBillGenerated={handleBillGenerated} data-sentry-element=\"AppointmentToBill\" data-sentry-source-file=\"billing-tabs.tsx\" />\n          </TabsContent>\n        </PermissionGate>\n\n        {/* Receipt Management Tab */}\n        <PermissionGate permission=\"canGenerateReceipts\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-tabs.tsx\">\n          <TabsContent value=\"receipts\" className=\"space-y-6\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <ReceiptManager data-sentry-element=\"ReceiptManager\" data-sentry-source-file=\"billing-tabs.tsx\" />\n          </TabsContent>\n        </PermissionGate>\n\n        {/* Financial Reports Tab */}\n        <PermissionGate permission=\"canViewDetailedFinancials\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"billing-tabs.tsx\">\n          <TabsContent value=\"reports\" className=\"space-y-6\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"billing-tabs.tsx\">\n            <FinancialDashboard data-sentry-element=\"FinancialDashboard\" data-sentry-source-file=\"billing-tabs.tsx\" />\n          </TabsContent>\n        </PermissionGate>\n      </Tabs>\n    </div>;\n}", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst alertVariants = cva('relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current', {\n  variants: {\n    variant: {\n      default: 'bg-card text-card-foreground',\n      destructive: 'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\n  return <div data-slot='alert' role='alert' className={cn(alertVariants({\n    variant\n  }), className)} {...props} data-sentry-component=\"Alert\" data-sentry-source-file=\"alert.tsx\" />;\n}\nfunction AlertTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-title' className={cn('col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight', className)} {...props} data-sentry-component=\"AlertTitle\" data-sentry-source-file=\"alert.tsx\" />;\n}\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='alert-description' className={cn('text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed', className)} {...props} data-sentry-component=\"AlertDescription\" data-sentry-source-file=\"alert.tsx\" />;\n}\nexport { Alert, AlertTitle, AlertDescription };", "module.exports = require(\"events\");", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Slot } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Popover\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPOVER_NAME = 'Popover';\n\ntype ScopedProps<P> = P & { __scopePopover?: Scope };\nconst [createPopoverContext, createPopoverScope] = createContextScope(POPOVER_NAME, [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype PopoverContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  hasCustomAnchor: boolean;\n  onCustomAnchorAdd(): void;\n  onCustomAnchorRemove(): void;\n  modal: boolean;\n};\n\nconst [PopoverProvider, usePopoverContext] =\n  createPopoverContext<PopoverContextValue>(POPOVER_NAME);\n\ninterface PopoverProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  modal?: boolean;\n}\n\nconst Popover: React.FC<PopoverProps> = (props: ScopedProps<PopoverProps>) => {\n  const {\n    __scopePopover,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = false,\n  } = props;\n  const popperScope = usePopperScope(__scopePopover);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [hasCustomAnchor, setHasCustomAnchor] = React.useState(false);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <PopoverProvider\n        scope={__scopePopover}\n        contentId={useId()}\n        triggerRef={triggerRef}\n        open={open}\n        onOpenChange={setOpen}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n        hasCustomAnchor={hasCustomAnchor}\n        onCustomAnchorAdd={React.useCallback(() => setHasCustomAnchor(true), [])}\n        onCustomAnchorRemove={React.useCallback(() => setHasCustomAnchor(false), [])}\n        modal={modal}\n      >\n        {children}\n      </PopoverProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nPopover.displayName = POPOVER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopoverAnchor';\n\ntype PopoverAnchorElement = React.ElementRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface PopoverAnchorProps extends PopperAnchorProps {}\n\nconst PopoverAnchor = React.forwardRef<PopoverAnchorElement, PopoverAnchorProps>(\n  (props: ScopedProps<PopoverAnchorProps>, forwardedRef) => {\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n\n    React.useEffect(() => {\n      onCustomAnchorAdd();\n      return () => onCustomAnchorRemove();\n    }, [onCustomAnchorAdd, onCustomAnchorRemove]);\n\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nPopoverAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'PopoverTrigger';\n\ntype PopoverTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface PopoverTriggerProps extends PrimitiveButtonProps {}\n\nconst PopoverTrigger = React.forwardRef<PopoverTriggerElement, PopoverTriggerProps>(\n  (props: ScopedProps<PopoverTriggerProps>, forwardedRef) => {\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n\n    const trigger = (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n\n    return context.hasCustomAnchor ? (\n      trigger\n    ) : (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        {trigger}\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nPopoverTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'PopoverPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createPopoverContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface PopoverPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst PopoverPortal: React.FC<PopoverPortalProps> = (props: ScopedProps<PopoverPortalProps>) => {\n  const { __scopePopover, forceMount, children, container } = props;\n  const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n  return (\n    <PortalProvider scope={__scopePopover} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nPopoverPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopoverContent';\n\ninterface PopoverContentProps extends PopoverContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst PopoverContent = React.forwardRef<PopoverContentTypeElement, PopoverContentProps>(\n  (props: ScopedProps<PopoverContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <PopoverContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <PopoverContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nPopoverContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PopoverContentTypeElement = PopoverContentImplElement;\ninterface PopoverContentTypeProps\n  extends Omit<PopoverContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst PopoverContentModal = React.forwardRef<PopoverContentTypeElement, PopoverContentTypeProps>(\n  (props: ScopedProps<PopoverContentTypeProps>, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const isRightClickOutsideRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <RemoveScroll as={Slot} allowPinchZoom>\n        <PopoverContentImpl\n          {...props}\n          ref={composedRefs}\n          // we make sure we're not trapping once it's been closed\n          // (closed !== unmounted when animating out)\n          trapFocus={context.open}\n          disableOutsidePointerEvents\n          onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n            event.preventDefault();\n            if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n          })}\n          onPointerDownOutside={composeEventHandlers(\n            props.onPointerDownOutside,\n            (event) => {\n              const originalEvent = event.detail.originalEvent;\n              const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n              const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n              isRightClickOutsideRef.current = isRightClick;\n            },\n            { checkForDefaultPrevented: false }\n          )}\n          // When focus is trapped, a `focusout` event may still happen.\n          // We make sure we don't trigger our `onDismiss` in such case.\n          onFocusOutside={composeEventHandlers(\n            props.onFocusOutside,\n            (event) => event.preventDefault(),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\nconst PopoverContentNonModal = React.forwardRef<PopoverContentTypeElement, PopoverContentTypeProps>(\n  (props: ScopedProps<PopoverContentTypeProps>, forwardedRef) => {\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <PopoverContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PopoverContentImplElement = React.ElementRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface PopoverContentImplProps\n  extends Omit<PopperContentProps, 'onPlaced'>,\n    Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * Whether focus should be trapped within the `Popover`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst PopoverContentImpl = React.forwardRef<PopoverContentImplElement, PopoverContentImplProps>(\n  (props: ScopedProps<PopoverContentImplProps>, forwardedRef) => {\n    const {\n      __scopePopover,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      ...contentProps\n    } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n\n    // Make sure the whole tree has focus guards as our `Popover` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <FocusScope\n        asChild\n        loop\n        trapped={trapFocus}\n        onMountAutoFocus={onOpenAutoFocus}\n        onUnmountAutoFocus={onCloseAutoFocus}\n      >\n        <DismissableLayer\n          asChild\n          disableOutsidePointerEvents={disableOutsidePointerEvents}\n          onInteractOutside={onInteractOutside}\n          onEscapeKeyDown={onEscapeKeyDown}\n          onPointerDownOutside={onPointerDownOutside}\n          onFocusOutside={onFocusOutside}\n          onDismiss={() => context.onOpenChange(false)}\n        >\n          <PopperPrimitive.Content\n            data-state={getState(context.open)}\n            role=\"dialog\"\n            id={context.contentId}\n            {...popperScope}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...contentProps.style,\n              // re-namespace exposed content custom properties\n              ...{\n                '--radix-popover-content-transform-origin': 'var(--radix-popper-transform-origin)',\n                '--radix-popover-content-available-width': 'var(--radix-popper-available-width)',\n                '--radix-popover-content-available-height': 'var(--radix-popper-available-height)',\n                '--radix-popover-trigger-width': 'var(--radix-popper-anchor-width)',\n                '--radix-popover-trigger-height': 'var(--radix-popper-anchor-height)',\n              },\n            }}\n          />\n        </DismissableLayer>\n      </FocusScope>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'PopoverClose';\n\ntype PopoverCloseElement = React.ElementRef<typeof Primitive.button>;\ninterface PopoverCloseProps extends PrimitiveButtonProps {}\n\nconst PopoverClose = React.forwardRef<PopoverCloseElement, PopoverCloseProps>(\n  (props: ScopedProps<PopoverCloseProps>, forwardedRef) => {\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nPopoverClose.displayName = CLOSE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopoverArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopoverArrow';\n\ntype PopoverArrowElement = React.ElementRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface PopoverArrowProps extends PopperArrowProps {}\n\nconst PopoverArrow = React.forwardRef<PopoverArrowElement, PopoverArrowProps>(\n  (props: ScopedProps<PopoverArrowProps>, forwardedRef) => {\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nPopoverArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Popover;\nconst Anchor = PopoverAnchor;\nconst Trigger = PopoverTrigger;\nconst Portal = PopoverPortal;\nconst Content = PopoverContent;\nconst Close = PopoverClose;\nconst Arrow = PopoverArrow;\n\nexport {\n  createPopoverScope,\n  //\n  Popover,\n  PopoverAnchor,\n  PopoverTrigger,\n  PopoverPortal,\n  PopoverContent,\n  PopoverClose,\n  PopoverArrow,\n  //\n  Root,\n  Anchor,\n  Trigger,\n  Portal,\n  Content,\n  Close,\n  Arrow,\n};\nexport type {\n  PopoverProps,\n  PopoverAnchorProps,\n  PopoverTriggerProps,\n  PopoverPortalProps,\n  PopoverContentProps,\n  PopoverCloseProps,\n  PopoverArrowProps,\n};\n"], "names": ["BillingPage", "userId", "auth", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "_jsxs", "div", "className", "h2", "IconReceipt", "p", "BillingTabs", "redirect", "serverComponentModule.default", "Dialog", "props", "DialogPrimitive", "data-slot", "DialogTrigger", "DialogPortal", "DialogOverlay", "cn", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "XIcon", "span", "DialogHeader", "<PERSON><PERSON><PERSON><PERSON>er", "DialogTitle", "DialogDescription", "ScrollArea", "ScrollAreaPrimitive", "<PERSON><PERSON>Bar", "orientation", "ERROR_MESSAGES", "AUTH_REQUIRED", "message", "severity", "AUTH_SERVICE_ERROR", "CLERK_USER_FETCH_ERROR", "USER_EMAIL_MISSING", "VALIDATION_ERROR", "INVALID_JSON", "INVALID_LIMIT", "INVALID_PAGE", "SUBTOTAL_MISMATCH", "INVALID_PAYMENT_AMOUNT", "BILL_NOT_FOUND", "PAYMENT_METHOD_ERROR", "INSUFFICIENT_PERMISSIONS", "BACKEND_ERROR", "BACKEND_SERVICE_ERROR", "DATABASE_ERROR", "NETWORK_ERROR", "RATE_LIMIT_EXCEEDED", "SUSPICIOUS_ACTIVITY", "SESSION_EXPIRED", "INVALID_AUTHENTICATION", "UNAUTHORIZED_ACCESS", "PAYMENT_GATEWAY_ERROR", "CARD_DECLINED", "CARD_EXPIRED", "INVALID_CARD_NUMBER", "TRANSACTION_TIMEOUT", "DUPLICATE_TRANSACTION", "INSUFFICIENT_FUNDS", "PAYMENT_ALREADY_PROCESSED", "DEPOSIT_EXPIRED", "DEPOSIT_INSUFFICIENT_BALANCE", "REFUND_AMOUNT_EXCEEDS_PAYMENT", "DEPOSIT_NOT_FOUND", "CONFIGURATION_ERROR", "EXTERNAL_SERVICE_ERROR", "INTERNAL_ERROR", "UNKNOWN_ERROR", "Billing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorLog", "getInstance", "instance", "handleAPIError", "error", "options", "billingError", "showToast", "logError", "context", "fallbackMessage", "code", "errorInfo", "userMessage", "details", "timestamp", "Date", "showErrorToast", "handleNetworkError", "networkError", "handleValidationError", "validationErrors", "firstError", "validationError", "showSuccess", "description", "toast", "success", "duration", "showWarning", "warning", "showInfo", "info", "console", "push", "length", "shift", "toastOptions", "getErrorLog", "clearErrorLog", "billingError<PERSON>andler", "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "maxRetries", "baseDelay", "lastError", "attempt", "delay", "Math", "pow", "Promise", "resolve", "setTimeout", "BillingAPIError", "Error", "constructor", "status", "name", "apiRequest", "endpoint", "retryOptions", "url", "API_BASE", "config", "headers", "makeRequest", "response", "fetch", "ok", "errorData", "json", "statusText", "apiError", "method", "includes", "toUpperCase", "billsAPI", "fetchBills", "params", "searchParams", "URLSearchParams", "page", "append", "toString", "limit", "search", "patientId", "dateFrom", "dateTo", "queryString", "fetchBill", "id", "createBill", "billData", "body", "JSON", "stringify", "updateBill", "updateData", "deleteBill", "generateFromAppointment", "appointmentId", "billType", "checkAppointmentBill", "bill", "docs", "find", "appointment", "hasBill", "undefined", "fetchPayments", "billId", "paymentMethod", "fetchPayment", "processPayment", "paymentData", "updatePayment", "processRefund", "paymentId", "refundData", "getDailyRevenue", "date", "getMonthlyRevenue", "year", "month", "getOutstandingBalances", "getFinancialReport", "startDate", "set", "endDate", "type", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getPaymentMethodName", "methods", "cash", "card", "wechat", "alipay", "transfer", "deposit", "installment", "getBillStatusName", "statuses", "draft", "sent", "confirmed", "paid", "cancelled", "getPaymentStatusName", "pending", "completed", "failed", "refunded", "z", "min", "requiredString", "optionalString", "validateCurrency", "value", "split", "billItemSchema", "itemType", "required_error", "invalid_type_error", "itemName", "max", "optional", "quantity", "refine", "val", "unitPrice", "discountRate", "data", "path", "billFormSchema", "patient", "uuid", "or", "treatment", "notes", "dueDate", "inputDate", "today", "setHours", "maxDate", "setFullYear", "getFullYear", "discountAmount", "taxAmount", "items", "itemsTotal", "reduce", "sum", "item", "itemTotal", "itemDiscount", "transactionId", "methodsRequiringTransactionId", "trim", "fullName", "regex", "phone", "phoneRegex", "email", "medicalNotes", "amountMin", "amountMax", "toDate", "billingNotifications", "created", "bill<PERSON><PERSON><PERSON>", "updated", "deleted", "statusUpdated", "oldStatus", "newStatus", "statusNames", "appointmentDate", "loadError", "createError", "updateError", "deleteError", "payment", "processed", "billingUtils", "paymentNumber", "receiptGenerated", "receiptNumber", "refundAmount", "processError", "refundError", "amountExceeded", "maxAmount", "receipt", "printed", "downloaded", "printError", "downloadError", "notFound", "system", "loading", "action", "Infinity", "permissionDenied", "dataRefreshed", "dataRefreshError", "operationCancelled", "operation", "featureNotImplemented", "feature", "validation", "requiredField", "fieldName", "invalidFormat", "expectedFormat", "duplicateEntry", "identifier", "unsavedChanges", "confirmAction", "ValidationDebouncer", "timeouts", "Map", "debounce", "key", "callback", "args", "existingTimeout", "get", "clearTimeout", "timeout", "delete", "clear", "for<PERSON>ach", "FieldValidator", "schema", "deboun<PERSON><PERSON><PERSON><PERSON>", "debouncer", "validateField", "fieldPath", "fullData", "onValidation", "performFieldValidation", "bind", "fieldData", "setNestedValue", "testData", "result", "safeParse", "fieldErrors", "errors", "filter", "join", "map", "field", "warnings", "generateWarnings", "validationResult", "<PERSON><PERSON><PERSON><PERSON>", "obj", "keys", "current", "i", "suggestion", "daysDiff", "ceil", "getTime", "subtotal", "cleanup", "FormValidator", "fieldValidators", "validateForm", "generateFormWarnings", "Array", "isArray", "totalItems", "totalAmount", "getFieldValidator", "has", "validator", "validateBusinessRules", "isValidPaymentAmount", "remainingAmount", "valid", "reason", "toFixed", "defaultClinicInfo", "address", "taxId", "Receipt", "forwardRef", "clinicInfo", "ref", "paymentBill", "Card", "<PERSON><PERSON><PERSON><PERSON>", "h1", "Separator", "<PERSON><PERSON><PERSON><PERSON>", "h3", "paymentDate", "toLocaleDateString", "Badge", "variant", "paymentStatus", "paidAmount", "toLocaleString", "displayName", "ReceiptDialog", "isOpen", "onClose", "receiptRef", "useRef", "handleDownloadPDF", "open", "onOpenChange", "<PERSON><PERSON>", "size", "onClick", "handlePrint", "printWindow", "window", "receiptContent", "innerHTML", "document", "write", "close", "focus", "print", "IconPrinter", "IconDownload", "IconX", "paymentMethods", "label", "icon", "IconCash", "requiresTransactionId", "IconCreditCard", "IconDeviceMobile", "IconBrandAlipay", "IconBuildingBank", "IconCalendar", "PaymentForm", "onSuccess", "onCancel", "isSubmitting", "setIsSubmitting", "useState", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMethod", "completedPayment", "setCompletedPayment", "showReceipt", "setShowReceipt", "form", "useForm", "resolver", "zodResolver", "paymentFormSchema", "defaultValues", "selectedPaymentMethod", "onSubmit", "amountValidation", "paymentsAPI", "reset", "errorMessage", "CardTitle", "CardDescription", "Form", "handleSubmit", "FormField", "control", "render", "FormItem", "FormLabel", "FormControl", "Input", "step", "maxPaymentAmount", "placeholder", "onChange", "e", "parseFloat", "target", "FormDescription", "FormMessage", "Select", "onValueChange", "SelectTrigger", "SelectValue", "SelectContent", "Icon", "SelectItem", "Textarea", "rows", "disabled", "PaymentDialog", "handleSuccess", "billTypes", "itemTypes", "<PERSON><PERSON><PERSON>", "patients", "appointments", "treatments", "calculatedTotals", "setCalculatedTotals", "formValidator", "setValidationErrors", "isEditing", "String", "treatmentId", "toISOString", "fields", "remove", "useFieldArray", "watchedItems", "watch", "watchedDiscountAmount", "watchedTaxAmount", "parseInt", "removeItem", "index", "IconUser", "addItem", "IconPlus", "IconTrash", "Label", "<PERSON><PERSON><PERSON><PERSON>", "setPatients", "setAppointments", "setTreatments", "setLoading", "newBill", "statusTransitions", "statusConfig", "color", "IconFileText", "IconMail", "IconCheck", "Bill<PERSON><PERSON>usManager", "onStatusUpdate", "trigger", "hasPermission", "useRole", "setIsOpen", "selectedStatus", "setSelectedStatus", "setNotes", "isUpdating", "setIsUpdating", "currentStatusConfig", "availableTransitions", "canUpdateStatus", "handleStatusUpdate", "updatedBill", "getStatusWarning", "<PERSON><PERSON><PERSON><PERSON>", "IconEdit", "htmlFor", "<PERSON><PERSON>", "IconAlertTriangle", "AlertDescription", "billStatuses", "<PERSON><PERSON><PERSON><PERSON>", "filters", "onFiltersChange", "localFilters", "setLocalFilters", "hasActiveFilters", "Object", "values", "some", "activeFilterCount", "handleFilterChange", "newFilters", "clearFilters", "emptyFilters", "clearSingleFilter", "IconSearch", "Popover", "PopoverTrigger", "IconFilter", "PopoverC<PERSON>nt", "align", "h4", "IconCurrencyYuan", "applyFilters", "s", "button", "t", "StatusBadge", "getStatusColor", "toLowerCase", "getStatusText", "BillTypeBadge", "getTypeColor", "getTypeText", "BillingList", "bills", "setBills", "setError", "setFilters", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "refreshing", "setRefreshing", "selectedBillForPayment", "setSelectedBillForPayment", "isPaymentDialogOpen", "setIsPaymentDialogOpen", "selectedBillForEdit", "setSelectedBillForEdit", "isBillDialogOpen", "setIsBillDialogOpen", "isCreatingBill", "setIsCreatingBill", "currentFilters", "err", "handleRefresh", "handleNewBill", "handleViewBill", "handleEditBill", "handlePayment", "prevBills", "handlePageChange", "IconAlertCircle", "IconRefresh", "PermissionGate", "permission", "handleFiltersChange", "issueDate", "IconEye", "handleClosePaymentDialog", "handlePaymentSuccess", "handleCloseBillDialog", "handleBillSuccess", "AppointmentToBill", "onBillGenerated", "generating", "setGenerating", "selectedBillType", "setSelectedBillType", "fetchCompletedAppointments", "appointmentsResponse", "appointmentsData", "existingBills", "billsResponse", "appointmentsWithBillStatus", "existingBill", "handleGenerateBill", "prev", "apt", "getAppointmentStatusColor", "getAppointmentStatusLabel", "labels", "scheduled", "unbilledAppointments", "IconStethoscope", "price", "ReceiptManager", "payments", "setPayments", "searchTerm", "setSearchTerm", "selectedPayment", "setSelectedPayment", "showReceiptDialog", "setShowReceiptDialog", "paymentsWithReceipts", "handleViewReceipt", "handlePrintReceipt", "filteredPayments", "FinancialDashboard", "metrics", "setMetrics", "dailyRevenue", "monthlyRevenue", "outstandingBalances", "selectedDate", "setSelectedDate", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "getMonth", "fetchMetrics", "all", "reportsAPI", "catch", "getPaymentMethodIcon", "IconChartBar", "input", "Number", "from", "_", "IconTrendingUp", "totalRevenue", "paymentCount", "entries", "count", "dailyBreakdown", "day", "revenue", "totalOutstanding", "overdueAmount", "billsCount", "overdueBillsCount", "daysOverdue", "defaultTab", "activeTab", "setActiveTab", "Tabs", "TabsList", "TabsTrigger", "IconCalendarEvent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleBillGenerated", "textarea", "scrollable", "alertVariants", "cva", "variants", "default", "destructive", "defaultVariants", "role", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Root", "Content"], "sourceRoot": ""}