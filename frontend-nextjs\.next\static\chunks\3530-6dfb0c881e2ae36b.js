try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="24df4385-ce96-4e51-91f0-8db0593b2373",e._sentryDebugIdIdentifier="sentry-dbid-24df4385-ce96-4e51-91f0-8db0593b2373")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3530],{5908:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(23278).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},10751:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(99004);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},29205:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(23278).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},60795:(e,t,r)=>{r.d(t,{In:()=>eD,JU:()=>eH,LM:()=>e_,PP:()=>eF,UC:()=>eM,VF:()=>eG,WT:()=>eP,YJ:()=>eA,ZL:()=>eL,bL:()=>eR,l9:()=>eE,p4:()=>eV,q7:()=>eB,wn:()=>eK,wv:()=>eO});var n=r(99004),l=r(32909),o=r(67051),a=r(84732),i=r(59694),s=r(39552),d=r(38774),u=r(51825),c=r(17430),p=r(6280),f=r(40201),v=r(29548),h=r(97677),m=r(55173),w=r(51452),g=r(50516),y=r(36962),x=r(18608),b=r(88072),S=r(10751),C=r(74227),j=r(10144),k=r(92350),T=r(52880),I=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],R="Select",[E,P,D]=(0,i.N)(R),[L,M]=(0,d.A)(R,[D,h.Bk]),_=(0,h.Bk)(),[A,H]=L(R),[B,V]=L(R),G=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:w,form:g}=e,y=_(t),[b,S]=n.useState(null),[C,j]=n.useState(null),[k,I]=n.useState(!1),N=(0,u.jH)(c),[R=!1,P]=(0,x.i)({prop:l,defaultProp:o,onChange:a}),[D,L]=(0,x.i)({prop:i,defaultProp:s,onChange:d}),M=n.useRef(null),H=!b||g||!!b.closest("form"),[V,G]=n.useState(new Set),F=Array.from(V).map(e=>e.props.value).join(";");return(0,T.jsx)(h.bL,{...y,children:(0,T.jsxs)(A,{required:w,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:j,valueNodeHasChildren:k,onValueNodeHasChildrenChange:I,contentId:(0,v.B)(),value:D,onValueChange:L,open:R,onOpenChange:P,dir:N,triggerPointerDownPosRef:M,disabled:m,children:[(0,T.jsx)(E.Provider,{scope:t,children:(0,T.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{G(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{G(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),H?(0,T.jsxs)(eT,{"aria-hidden":!0,required:w,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===D?(0,T.jsx)("option",{value:""}):null,Array.from(V)]},F):null]})})};G.displayName=R;var F="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=_(r),d=H(F,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=P(r),f=n.useRef("touch"),[v,m,g]=eI(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=eN(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),g()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(h.Mz,{asChild:!0,...i,children:(0,T.jsx)(w.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":ek(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&I.includes(e.key)&&(y(),e.preventDefault())})})})});K.displayName=F;var O="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=H(O,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,b.N)(()=>{u(c)},[u,c]),(0,T.jsx)(w.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(d.value)?(0,T.jsx)(T.Fragment,{children:a}):o})});U.displayName=O;var W=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,T.jsx)(w.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});W.displayName="SelectIcon";var q=e=>(0,T.jsx)(m.Z,{asChild:!0,...e});q.displayName="SelectPortal";var z="SelectContent",Z=n.forwardRef((e,t)=>{let r=H(z,e.__scopeSelect),[o,a]=n.useState();return((0,b.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,T.jsx)(J,{...e,ref:t}):o?l.createPortal((0,T.jsx)(X,{scope:e.__scopeSelect,children:(0,T.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});Z.displayName=z;var[X,Y]=L(z),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C,...I}=e,N=H(z,r),[R,E]=n.useState(null),[D,L]=n.useState(null),M=(0,s.s)(t,e=>E(e)),[_,A]=n.useState(null),[B,V]=n.useState(null),G=P(r),[F,K]=n.useState(!1),O=n.useRef(!1);n.useEffect(()=>{if(R)return(0,j.Eq)(R)},[R]),(0,p.Oh)();let U=n.useCallback(e=>{let[t,...r]=G().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&D&&(D.scrollTop=0),r===n&&D&&(D.scrollTop=D.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[G,D]),W=n.useCallback(()=>U([_,R]),[U,_,R]);n.useEffect(()=>{F&&W()},[F,W]);let{onOpenChange:q,triggerPointerDownPosRef:Z}=N;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=Z.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=Z.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||q(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,q,Z]),n.useEffect(()=>{let e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[q]);let[Y,J]=eI(e=>{let t=G().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eN(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),ee=n.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==N.value&&N.value===t||n)&&(A(e),n&&(O.current=!0))},[N.value]),et=n.useCallback(()=>null==R?void 0:R.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==N.value&&N.value===t||n)&&V(e)},[N.value]),en="popper"===l?$:Q,el=en===$?{side:u,sideOffset:v,align:h,alignOffset:m,arrowPadding:w,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:S,avoidCollisions:C}:{};return(0,T.jsx)(X,{scope:r,content:R,viewport:D,onViewportChange:L,itemRefCallback:ee,selectedItem:_,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:B,position:l,isPositioned:F,searchRef:Y,children:(0,T.jsx)(k.A,{as:g.DX,allowPinchZoom:!0,children:(0,T.jsx)(f.n,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=N.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,T.jsx)(en,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...I,...el,onPlaced:()=>K(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...I.style},onKeyDown:(0,a.m)(I.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||J(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=G().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=H(z,r),d=Y(z,r),[u,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=P(r),m=n.useRef(!1),g=n.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:S,focusSelectedItem:C}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&x&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.left=p+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.q)(a,[10,Math.max(10,c-d)]);u.style.minWidth=s+"px",u.style.right=p+"px"}let a=h(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),w=parseInt(c.borderBottomWidth,10),g=f+v+d+parseInt(c.paddingBottom,10)+w,b=Math.min(5*x.offsetHeight,g),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,I=x.offsetHeight/2,N=f+v+(x.offsetTop+I);if(N<=T){let e=a.length>0&&x===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,I+(e?k:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+w);u.style.height=N+t+"px"}else{let e=a.length>0&&x===a[0].ref.current;u.style.top="0px";let t=Math.max(T,f+y.offsetTop+(e?j:0)+I);u.style.height=t+(g-N)+"px",y.scrollTop=N-T+y.offsetTop}u.style.margin="".concat(10,"px 0"),u.style.minHeight=b+"px",u.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,u,p,y,x,S,i.dir,l]);(0,b.N)(()=>j(),[j]);let[k,I]=n.useState();(0,b.N)(()=>{p&&I(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===g.current&&(j(),null==C||C(),g.current=!1)},[j,C]);return(0,T.jsx)(ee,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,T.jsx)(w.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=_(r);return(0,T.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});$.displayName="SelectPopperPosition";var[ee,et]=L(z,{}),er="SelectViewport",en=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Y(er,r),d=et(er,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,T.jsx)(E.Slot,{scope:r,children:(0,T.jsx)(w.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});en.displayName=er;var el="SelectGroup",[eo,ea]=L(el),ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,T.jsx)(eo,{scope:r,id:l,children:(0,T.jsx)(w.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ei.displayName=el;var es="SelectLabel",ed=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ea(es,r);return(0,T.jsx)(w.sG.div,{id:l.id,...n,ref:t})});ed.displayName=es;var eu="SelectItem",[ec,ep]=L(eu),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=H(eu,r),c=Y(eu,r),p=u.value===l,[f,h]=n.useState(null!=i?i:""),[m,g]=n.useState(!1),y=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),x=(0,v.B)(),b=n.useRef("touch"),S=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ec,{scope:r,value:l,disabled:o,textId:x,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,T.jsx)(E.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,T.jsx)(w.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>g(!0)),onBlur:(0,a.m)(d.onBlur,()=>g(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{if(b.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ev="SelectItemText",eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=H(ev,r),u=Y(ev,r),c=ep(ev,r),p=V(ev,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=u.itemTextRefCallback)?void 0:t.call(u,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,g=n.useMemo(()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=p;return(0,b.N)(()=>(y(g),()=>x(g)),[y,x,g]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(w.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});eh.displayName=ev;var em="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(em,r).isSelected?(0,T.jsx)(w.sG.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=em;var eg="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=Y(eg,e.__scopeSelect),l=et(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=eg;var ex="SelectScrollDownButton",eb=n.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),l=et(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,b.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ex;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),d=P(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,b.N)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,T.jsx)(w.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,T.jsx)(w.sG.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var ej="SelectArrow";function ek(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=_(r),o=H(ej,r),a=Y(ej,r);return o.open&&"popper"===a.position?(0,T.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=ej;var eT=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),a=(0,s.s)(t,o),i=(0,S.Z)(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,T.jsx)(C.s,{asChild:!0,children:(0,T.jsx)("select",{...l,ref:a,defaultValue:r})})});function eI(e){let t=(0,y.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eN(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eT.displayName="BubbleSelect";var eR=G,eE=K,eP=U,eD=W,eL=q,eM=Z,e_=en,eA=ei,eH=ed,eB=ef,eV=eh,eG=ew,eF=ey,eK=eb,eO=eC},74227:(e,t,r)=>{r.d(t,{b:()=>i,s:()=>a});var n=r(99004),l=r(51452),o=r(52880),a=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var i=a}}]);