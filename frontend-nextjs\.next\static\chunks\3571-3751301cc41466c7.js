try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="0268e15c-8599-4e3d-8ce7-116fc7e1ba52",e._sentryDebugIdIdentifier="sentry-dbid-0268e15c-8599-4e3d-8ce7-116fc7e1ba52")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3571],{26230:(e,t,a)=>{a.d(t,{t:()=>r});let n={nav:{dashboard:"仪表板",appointments:"预约管理",patients:"患者管理",treatments:"治疗项目",admin:"系统管理",account:"账户",profile:"个人资料",login:"登录",overview:"概览"},dashboard:{title:"诊所控制台 \uD83C\uDFE5",subtitle:"欢迎使用您的诊所管理系统",metrics:{todayAppointments:"今日预约",recentPatients:"近期患者",totalPatients:"患者总数",activetreatments:"可用治疗",scheduledForToday:"今日安排",appointmentsScheduledForToday:"今日安排的预约",newPatientsThisWeek:"本周新患者",patientsRegisteredInLast7Days:"过去7天注册的患者",totalRegisteredPatients:"注册患者总数",completePatientDatabase:"完整患者数据库",treatmentOptionsAvailable:"可用治疗选项",fullServiceCatalog:"完整服务目录",active:"活跃",last7Days:"过去7天",allTime:"全部时间",available:"可用"},errors:{loadingDashboard:"加载仪表板时出错",failedToLoadMetrics:"无法加载仪表板数据"}},appointments:{title:"预约管理",subtitle:"管理患者预约和排程",newAppointment:"新建预约",editAppointment:"编辑预约",appointmentDetails:"预约详情",appointmentsCount:"个预约",loadingAppointments:"加载预约中...",noAppointments:"暂无预约",filters:{all:"全部",today:"今天",thisWeek:"本周",thisMonth:"本月",status:"状态",dateRange:"日期范围"},status:{scheduled:"已安排",confirmed:"已确认",inProgress:"进行中",completed:"已完成",cancelled:"已取消",noShow:"未到场"},form:{patient:"患者",selectPatient:"选择患者",treatment:"治疗项目",selectTreatment:"选择治疗项目",date:"日期",time:"时间",notes:"备注",notesPlaceholder:"预约备注（可选）",status:"状态"}},patients:{title:"患者管理",subtitle:"管理患者信息和病历",newPatient:"新建患者",editPatient:"编辑患者",patientDetails:"患者详情",patientsCount:"位患者",loadingPatients:"加载患者中...",noPatients:"暂无患者",searchPlaceholder:"按姓名、电话或邮箱搜索患者",form:{fullName:"姓名",fullNamePlaceholder:"请输入患者姓名",phone:"电话",phonePlaceholder:"请输入电话号码",email:"邮箱",emailPlaceholder:"请输入邮箱地址（可选）",medicalNotes:"病历备注",medicalNotesPlaceholder:"请输入病历备注（可选）"}},treatments:{title:"治疗项目",subtitle:"管理诊所治疗服务",newTreatment:"新建治疗",editTreatment:"编辑治疗",treatmentDetails:"治疗详情",treatmentsCount:"个治疗项目",loadingTreatments:"加载治疗项目中...",noTreatments:"暂无治疗项目",form:{name:"治疗名称",namePlaceholder:"请输入治疗名称",description:"治疗描述",descriptionPlaceholder:"请输入治疗描述",duration:"治疗时长",durationPlaceholder:"请输入治疗时长（分钟）",price:"价格",pricePlaceholder:"请输入价格"}},admin:{title:"系统管理",subtitle:"管理用户权限和系统设置",userManagement:"用户管理",roleManagement:"角色管理",systemSettings:"系统设置",users:"用户",roles:{admin:"管理员",doctor:"医生",frontDesk:"前台"}},common:{actions:{save:"保存",cancel:"取消",edit:"编辑",delete:"删除",view:"查看",search:"搜索",filter:"筛选",reset:"重置",submit:"提交",close:"关闭",confirm:"确认",back:"返回",next:"下一步",previous:"上一步",add:"添加",remove:"移除",update:"更新",create:"创建"},status:{loading:"加载中...",success:"成功",error:"错误",warning:"警告",info:"信息",pending:"待处理",active:"活跃",inactive:"非活跃",enabled:"已启用",disabled:"已禁用"},time:{today:"今天",yesterday:"昨天",tomorrow:"明天",thisWeek:"本周",lastWeek:"上周",nextWeek:"下周",thisMonth:"本月",lastMonth:"上月",nextMonth:"下月",thisYear:"今年",lastYear:"去年",nextYear:"明年"},confirmDialog:{title:"确认操作",deleteTitle:"确认删除",deleteMessage:"您确定要删除这个项目吗？此操作无法撤销。",cancelTitle:"确认取消",cancelMessage:"您确定要取消吗？未保存的更改将丢失。",saveTitle:"确认保存",saveMessage:"您确定要保存这些更改吗？"}},validation:{required:"此字段为必填项",email:"请输入有效的邮箱地址",phone:"请输入有效的电话号码",minLength:"至少需要 {min} 个字符",maxLength:"最多允许 {max} 个字符",number:"请输入有效的数字",positive:"请输入正数",date:"请选择有效的日期",time:"请选择有效的时间"},errors:{general:"发生了未知错误，请稍后重试",network:"网络连接错误，请检查您的网络连接",unauthorized:"您没有权限执行此操作",notFound:"请求的资源未找到",serverError:"服务器错误，请稍后重试",validationError:"输入数据验证失败",loadFailed:"加载数据失败",saveFailed:"保存数据失败",deleteFailed:"删除数据失败",updateFailed:"更新数据失败",createFailed:"创建数据失败"},success:{saved:"保存成功",deleted:"删除成功",updated:"更新成功",created:"创建成功",sent:"发送成功",uploaded:"上传成功",downloaded:"下载成功"}};function r(e,t){let a=e.split("."),r=n;for(let t of a)if(!r||"object"!=typeof r||!(t in r))return console.warn("Translation key not found: ".concat(e)),e;else r=r[t];return"string"!=typeof r?(console.warn("Translation value is not a string: ".concat(e)),e):t?r.replace(/\{(\w+)\}/g,(e,a)=>{var n;return(null==(n=t[a])?void 0:n.toString())||e}):r}},56420:(e,t,a)=>{a.d(t,{C5:()=>x,MJ:()=>y,Rr:()=>h,eI:()=>p,lR:()=>f,lV:()=>d,zB:()=>m});var n=a(52880),r=a(99004),o=a(50516),l=a(38406),i=a(54651),s=a(84692);let d=l.Op,c=r.createContext({}),m=e=>{let{...t}=e;return(0,n.jsx)(c.Provider,{value:{name:t.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,n.jsx)(l.xI,{...t,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})})},u=()=>{let e=r.useContext(c),t=r.useContext(g),{getFieldState:a}=(0,l.xW)(),n=(0,l.lN)({name:e.name}),o=a(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...o}},g=r.createContext({});function p(e){let{className:t,...a}=e,o=r.useId();return(0,n.jsx)(g.Provider,{value:{id:o},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,n.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...a})})}function f(e){let{className:t,...a}=e,{error:r,formItemId:o}=u();return(0,n.jsx)(s.J,{"data-slot":"form-label","data-error":!!r,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:o,...a,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function y(e){let{...t}=e,{error:a,formItemId:r,formDescriptionId:l,formMessageId:i}=u();return(0,n.jsx)(o.DX,{"data-slot":"form-control",id:r,"aria-describedby":a?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!a,...t,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function h(e){let{className:t,...a}=e,{formDescriptionId:r}=u();return(0,n.jsx)("p",{"data-slot":"form-description",id:r,className:(0,i.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"FormDescription","data-sentry-source-file":"form.tsx"})}function x(e){var t;let{className:a,...r}=e,{error:o,formMessageId:l}=u(),s=o?String(null!=(t=null==o?void 0:o.message)?t:""):r.children;return s?(0,n.jsx)("p",{"data-slot":"form-message",id:l,className:(0,i.cn)("text-destructive text-sm",a),...r,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:s}):null}},60541:(e,t,a)=>{a.d(t,{$v:()=>p,EO:()=>c,Lt:()=>i,Rx:()=>f,Zr:()=>y,ck:()=>u,r7:()=>g,wd:()=>m});var n=a(52880);a(99004);var r=a(46937),o=a(54651),l=a(62054);function i(e){let{...t}=e;return(0,n.jsx)(r.bL,{"data-slot":"alert-dialog",...t,"data-sentry-element":"AlertDialogPrimitive.Root","data-sentry-component":"AlertDialog","data-sentry-source-file":"alert-dialog.tsx"})}function s(e){let{...t}=e;return(0,n.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...t,"data-sentry-element":"AlertDialogPrimitive.Portal","data-sentry-component":"AlertDialogPortal","data-sentry-source-file":"alert-dialog.tsx"})}function d(e){let{className:t,...a}=e;return(0,n.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a,"data-sentry-element":"AlertDialogPrimitive.Overlay","data-sentry-component":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"})}function c(e){let{className:t,...a}=e;return(0,n.jsxs)(s,{"data-sentry-element":"AlertDialogPortal","data-sentry-component":"AlertDialogContent","data-sentry-source-file":"alert-dialog.tsx",children:[(0,n.jsx)(d,{"data-sentry-element":"AlertDialogOverlay","data-sentry-source-file":"alert-dialog.tsx"}),(0,n.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a,"data-sentry-element":"AlertDialogPrimitive.Content","data-sentry-source-file":"alert-dialog.tsx"})]})}function m(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a,"data-sentry-component":"AlertDialogHeader","data-sentry-source-file":"alert-dialog.tsx"})}function u(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a,"data-sentry-component":"AlertDialogFooter","data-sentry-source-file":"alert-dialog.tsx"})}function g(e){let{className:t,...a}=e;return(0,n.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,o.cn)("text-lg font-semibold",t),...a,"data-sentry-element":"AlertDialogPrimitive.Title","data-sentry-component":"AlertDialogTitle","data-sentry-source-file":"alert-dialog.tsx"})}function p(e){let{className:t,...a}=e;return(0,n.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-element":"AlertDialogPrimitive.Description","data-sentry-component":"AlertDialogDescription","data-sentry-source-file":"alert-dialog.tsx"})}function f(e){let{className:t,...a}=e;return(0,n.jsx)(r.rc,{className:(0,o.cn)((0,l.r)(),t),...a,"data-sentry-element":"AlertDialogPrimitive.Action","data-sentry-component":"AlertDialogAction","data-sentry-source-file":"alert-dialog.tsx"})}function y(e){let{className:t,...a}=e;return(0,n.jsx)(r.ZD,{className:(0,o.cn)((0,l.r)({variant:"outline"}),t),...a,"data-sentry-element":"AlertDialogPrimitive.Cancel","data-sentry-component":"AlertDialogCancel","data-sentry-source-file":"alert-dialog.tsx"})}},65674:(e,t,a)=>{a.d(t,{K:()=>o});var n=a(52880),r=a(60541);function o(e){let{open:t,onOpenChange:a,title:o,description:l,confirmText:i="确认",cancelText:s="取消",variant:d="default",onConfirm:c,loading:m=!1}=e;return(0,n.jsx)(r.Lt,{open:t,onOpenChange:a,"data-sentry-element":"AlertDialog","data-sentry-component":"ConfirmationDialog","data-sentry-source-file":"confirmation-dialog.tsx",children:(0,n.jsxs)(r.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,n.jsxs)(r.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,n.jsx)(r.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"confirmation-dialog.tsx",children:o}),(0,n.jsx)(r.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"confirmation-dialog.tsx",children:l})]}),(0,n.jsxs)(r.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"confirmation-dialog.tsx",children:[(0,n.jsx)(r.Zr,{disabled:m,"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"confirmation-dialog.tsx",children:s}),(0,n.jsx)(r.Rx,{onClick:c,disabled:m,className:"destructive"===d?"bg-destructive text-destructive-foreground hover:bg-destructive/90":"","data-sentry-element":"AlertDialogAction","data-sentry-source-file":"confirmation-dialog.tsx",children:m?"加载中...":i})]})]})})}},87378:(e,t,a)=>{async function n(e,t){let a="/api".concat(e);try{let e=await fetch(a,{headers:{"Content-Type":"application/json",...null==t?void 0:t.headers},...t}),n=await e.json();if(!e.ok){if(n.error)throw Error(n.error);throw Error("API request failed: ".concat(e.status," ").concat(e.statusText))}return n}catch(e){throw console.error("API request to ".concat(a," failed:"),e),e}}a.d(t,{RG:()=>r,_M:()=>l,cU:()=>i,fJ:()=>o});let r={getAll:async e=>{let t=new URLSearchParams;if((null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),null==e?void 0:e.where){var a;(null==(a=e.where.patient)?void 0:a.equals)&&t.append("where[patient][equals]",e.where.patient.equals)}let r=t.toString()?"?".concat(t.toString()):"";return n("/appointments".concat(r))},getById:async e=>n("/appointments/".concat(e)),create:async e=>n("/appointments",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>n("/appointments/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>n("/appointments/".concat(e),{method:"DELETE"})},o={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.search)&&t.append("where[or][0][fullName][contains]",e.search);let a=t.toString()?"?".concat(t.toString()):"";return n("/patients".concat(a))},getById:async e=>n("/patients/".concat(e)),create:async e=>n("/patients",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>n("/patients/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>n("/patients/".concat(e),{method:"DELETE"})},l={getAll:async e=>{let t=new URLSearchParams;(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.page)&&t.append("page",e.page.toString());let a=t.toString()?"?".concat(t.toString()):"";return n("/treatments".concat(a))},getById:async e=>n("/treatments/".concat(e)),create:async e=>n("/treatments",{method:"POST",body:JSON.stringify(e)}),update:async(e,t)=>n("/treatments/".concat(e),{method:"PATCH",body:JSON.stringify(t)}),delete:async e=>n("/treatments/".concat(e),{method:"DELETE"})},i=async()=>{try{let e=new Date().toISOString().split("T")[0],t=(await r.getAll({limit:1e3})).docs.filter(t=>t.appointmentDate.startsWith(e)).length,a=new Date;a.setDate(a.getDate()-7);let n=await o.getAll({limit:1e3}),i=n.docs.filter(e=>new Date(e.createdAt)>=a).length,s=await l.getAll({limit:1e3});return{todayAppointments:t,recentPatients:i,totalPatients:n.totalDocs,activetreatments:s.totalDocs}}catch(e){return console.error("Failed to fetch dashboard metrics:",e),{todayAppointments:0,recentPatients:0,totalPatients:0,activetreatments:0}}}}}]);