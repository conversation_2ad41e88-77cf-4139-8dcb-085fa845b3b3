{"version": 3, "file": "../app/dashboard/patients/[id]/page.js", "mappings": "shBAUA,IAAMA,EAAW,IAAM,UAACC,EAAAA,GAAeA,CAAAA,CAACC,UAAU,SAASC,sBAAoB,kBAAkBC,wBAAsB,WAAWC,0BAAwB,iBACpJC,EAAY,IAAM,UAACC,EAAAA,GAAgBA,CAAAA,CAACL,UAAU,SAASC,sBAAoB,mBAAmBC,wBAAsB,YAAYC,0BAAwB,iBAC9J,SAASG,EAAS,WAChBN,CAAS,YACTO,CAAU,iBACVC,GAAkB,CAAI,CACtB,GAAGC,EAC8B,EACjC,MAAO,UAACC,EAAAA,EAASA,CAAAA,CAACF,gBAAiBA,EAAiBR,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,MAAOX,GAAYO,WAAY,CAC/FK,OAAQ,kCACRC,MAAO,sBACPC,QAAS,wDACTC,cAAe,sBACfC,IAAK,0BACLC,WAAYN,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC,CAC5BC,QAAS,SACX,GAAI,0DACJC,oBAAqB,kBACrBC,gBAAiB,mBACjBC,MAAO,mCACPC,SAAU,OACVC,UAAW,iEACXC,IAAK,mBACLC,KAAMf,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kKAAkL,UAAfF,EAAMkB,IAAI,CAAe,uKAAyK,uCAC9WC,IAAKjB,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACO,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC,CACrBC,QAAS,OACX,GAAI,oDACJU,gBAAiB,iFACjBC,cAAe,+EACfC,aAAc,mIACdC,UAAW,mCACXC,YAAa,wEACbC,aAAc,mCACdC,iBAAkB,+DAClBC,WAAY,YACZ,GAAG7B,CACL,EAAG8B,WAAY,CACbC,SAAUxC,EACVyC,UAAWnC,CACb,EAAI,GAAGK,CAAK,CAAER,sBAAoB,YAAYC,wBAAsB,WAAWC,0BAAwB,gBACzG,yMC5BA,IAAMqC,EAAwB,CAC5B,aAAc,CACZC,MAAO,OACPC,KAAMC,EAAAA,EAASA,CACfC,MAAO,2CACT,EACA,MAAS,CACPH,MAAO,OACPC,KAAMG,EAAAA,EAAQA,CACdD,MAAO,8CACT,EACA,oBAAqB,CACnBH,MAAO,OACPC,KAAMI,EAAAA,EAAeA,CACrBF,MAAO,iDACT,EACA,kBAAmB,CACjBH,MAAO,OACPC,KAAMK,EAAAA,EAAQA,CACdH,MAAO,iDACT,EACA,uBAAwB,CACtBH,MAAO,OACPC,KAAMM,EAAAA,EAAiBA,CACvBJ,MAAO,iDACT,EACA,kBAAmB,CACjBH,MAAO,OACPC,KAAMO,EAAAA,EAAcA,CACpBL,MAAO,iDACT,CACF,EACMM,EAAe,CACnB,KAAQ,CACNT,MAAO,KACPG,MAAO,2BACT,EACA,cAAe,CACbH,MAAO,MACPG,MAAO,2BACT,EACA,SAAY,CACVH,MAAO,MACPG,MAAO,6BACT,EACA,OAAU,CACRH,MAAO,MACPG,MAAO,2BACT,CACF,EACMO,EAAiB,CACrB,IAAO,CACLV,MAAO,IACPG,MAAO,2BACT,EACA,OAAU,CACRH,MAAO,IACPG,MAAO,+BACT,EACA,KAAQ,CACNH,MAAO,IACPG,MAAO,yBACT,CACF,EACO,SAASQ,EAAoB,WAClCC,CAAS,cACTC,CAAY,SACZC,GAAU,CAAK,qBACfC,CAAmB,oBACnBC,CAAkB,WAClBzD,CAAS,CACgB,EACzB,GAAM,CAAC0D,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OAC/C,CAACG,EAAcC,EAAgB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OACnD,CAACK,EAAsBC,EAAwB,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAuBN,GAsBjFa,EAAsBC,IAC1B,IAAMC,EAAgB7B,CAAqB,CAAC4B,EAAK,EAAE1B,MAAQM,EAAAA,EAAiBA,CAC5E,MAAO,UAACqB,EAAAA,CAAcrE,UAAU,SAASC,sBAAoB,gBAAgBC,wBAAsB,qBAAqBC,0BAAwB,4BAClJ,EACMmE,EAAqB,GACzB,UAAI,OAAOC,EAAiC,SACrC,GAAGA,EAAYC,SAAS,EAAI,GAAG,CAAC,EAAED,EAAYE,QAAQ,EAAI,IAAI,CAACC,IAAI,IAAMH,EAAYI,KAAK,QAEnG,EACS,OADI,EACJ,EAACC,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,YACpB,UAAC6E,EAAAA,EAAUA,CAAAA,UACT,WAACC,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,oCACnB,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,WAAW,aAIpC,UAACgF,EAAAA,EAAWA,CAAAA,UACV,UAACC,MAAAA,CAAIjF,UAAU,qBACZ,sBAAa,CAACkF,GAAG,CAAC,CAACC,EAAGC,IAAM,UAACH,MAAAA,CAAYjF,UAAU,yBAChD,WAACiF,MAAAA,CAAIjF,UAAU,mCACb,UAACiF,MAAAA,CAAIjF,UAAU,oCACf,WAACiF,MAAAA,CAAIjF,UAAU,6BACb,UAACiF,MAAAA,CAAIjF,UAAU,kCACf,UAACiF,MAAAA,CAAIjF,UAAU,yCALgBoF,WAa1C,WAACR,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,EAAWC,sBAAoB,OAAOC,wBAAsB,sBAAsBC,0BAAwB,qCAC9H,WAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,qCACnE,WAAC8E,MAAAA,CAAIjF,UAAU,8CACb,WAAC8E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,qCACrG,UAAC4E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,SAASC,sBAAoB,YAAYE,0BAAwB,6BAA6B,QAEnH,UAACkF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,YAAYnB,UAAU,OAAOC,sBAAoB,QAAQE,0BAAwB,oCAC7F8D,EAAqBqB,MAAM,MAG/B9B,GAAuB,WAAC+B,EAAAA,CAAMA,CAAAA,CAACC,QAAShC,EAAqBiC,KAAK,eAC/D,UAACC,EAAAA,EAAQA,CAAAA,CAAC1F,UAAU,gBAAgB,aAM1C,WAACiF,MAAAA,CAAIjF,UAAU,4CACb,WAACiF,MAAAA,CAAIjF,UAAU,4BACb,UAAC2F,EAAAA,EAAUA,CAAAA,CAAC3F,UAAU,kFAAkFC,sBAAoB,aAAaE,0BAAwB,6BACjK,UAACyF,EAAAA,CAAKA,CAAAA,CAACC,YAAY,YAAYC,MAAOpC,EAAYqC,SAAUC,GAAKrC,EAAcqC,EAAEC,MAAM,CAACH,KAAK,EAAG9F,UAAU,QAAQC,sBAAoB,QAAQE,0BAAwB,gCAExK,WAAC+F,EAAAA,EAAMA,CAAAA,CAACJ,MAAOjC,EAAYsC,cAAerC,EAAe7D,sBAAoB,SAASE,0BAAwB,qCAC5G,WAACiG,EAAAA,EAAaA,CAAAA,CAACpG,UAAU,sBAAsBC,sBAAoB,gBAAgBE,0BAAwB,qCACzG,UAACkG,EAAAA,EAAUA,CAAAA,CAACrG,UAAU,cAAcC,sBAAoB,aAAaE,0BAAwB,6BAC7F,UAACmG,EAAAA,EAAWA,CAAAA,CAACT,YAAY,KAAK5F,sBAAoB,cAAcE,0BAAwB,gCAE1F,WAACoG,EAAAA,EAAaA,CAAAA,CAACtG,sBAAoB,gBAAgBE,0BAAwB,qCACzE,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,MAAM7F,sBAAoB,aAAaE,0BAAwB,oCAA2B,SAC3GsG,OAAOC,OAAO,CAAClE,GAAuB0C,GAAG,CAAC,CAAC,CAACY,EAAOa,EAAO,GAAK,UAACH,EAAAA,EAAUA,CAAAA,CAAaV,MAAOA,WAC1Fa,EAAOlE,KAAK,EADgEqD,UAKrF,WAACI,EAAAA,EAAMA,CAAAA,CAACJ,MAAO/B,EAAcoC,cAAenC,EAAiB/D,sBAAoB,SAASE,0BAAwB,qCAChH,UAACiG,EAAAA,EAAaA,CAAAA,CAACpG,UAAU,sBAAsBC,sBAAoB,gBAAgBE,0BAAwB,oCACzG,UAACmG,EAAAA,EAAWA,CAAAA,CAACT,YAAY,KAAK5F,sBAAoB,cAAcE,0BAAwB,+BAE1F,WAACoG,EAAAA,EAAaA,CAAAA,CAACtG,sBAAoB,gBAAgBE,0BAAwB,qCACzE,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,MAAM7F,sBAAoB,aAAaE,0BAAwB,oCAA2B,SAC3GsG,OAAOC,OAAO,CAACxD,GAAcgC,GAAG,CAAC,CAAC,CAACY,EAAOa,EAAO,GAAK,UAACH,EAAAA,EAAUA,CAAAA,CAAaV,MAAOA,WACjFa,EAAOlE,KAAK,EADuDqD,gBAQhF,UAACd,EAAAA,EAAWA,CAAAA,CAAC/E,sBAAoB,cAAcE,0BAAwB,oCACrE,UAACyG,EAAAA,UAAUA,CAAAA,CAAC5G,UAAU,YAAYC,sBAAoB,aAAaE,0BAAwB,oCACxD,IAAhC8D,EAAqBqB,MAAM,CAAS,WAACL,MAAAA,CAAIjF,UAAU,mDAChD,UAACgD,EAAAA,EAAiBA,CAAAA,CAAChD,UAAU,oCAC7B,UAAC6G,IAAAA,CAAE7G,UAAU,+BAAsB,WACnC,UAAC6G,IAAAA,CAAE7G,UAAU,mBAAU,oBAChB,UAACiF,MAAAA,CAAIjF,UAAU,qBACrBiE,EAAqBiB,GAAG,CAAC,CAAC4B,EAAaC,KAC1C,IAAMC,EAAaxE,CAAqB,CAACsE,EAAYG,eAAe,CAAC,CAC/DC,EAAchE,CAAY,CAAC4D,EAAYK,MAAM,CAAC,CAC9CC,EAAgBjE,CAAc,CAAC2D,EAAYO,QAAQ,CAAC,CAC1D,MAAO,WAACpC,MAAAA,CAAyBjF,UAAU,qBAElC+G,EAAQ9C,EAAqBqB,MAAM,CAAG,GAAK,UAACL,MAAAA,CAAIjF,UAAU,mDAE3D,WAACiF,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkE8C,GAAsB,oCAAqC+B,QAAS,IAAM/B,IAAqBqD,aAElL,UAAC7B,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gEAAiEqG,GAAYpE,OAAS,sDACtGuB,EAAmB2C,EAAYG,eAAe,IAIjD,WAAChC,MAAAA,CAAIjF,UAAU,2BACb,WAACiF,MAAAA,CAAIjF,UAAU,wDACb,UAACsH,KAAAA,CAAGtH,UAAU,6CACX8G,EAAYS,KAAK,GAEpB,WAACtC,MAAAA,CAAIjF,UAAU,kDACb,UAACqF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAAWkH,EAAYtE,KAAK,UAClDsE,EAAYzE,KAAK,GAEpB,UAAC4C,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAAWoH,EAAcxE,KAAK,UACpDwE,EAAc3E,KAAK,SAK1B,WAACwC,MAAAA,CAAIjF,UAAU,uEACb,WAACwH,OAAAA,CAAKxH,UAAU,oCACd,UAAC+C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,WACnBsE,EAAmBwC,EAAYvC,WAAW,KAE7C,WAACiD,OAAAA,CAAKxH,UAAU,oCACd,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,WACpByH,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAKZ,EAAYa,SAAS,SAIjDb,EAAYc,OAAO,EAAI,UAACf,IAAAA,CAAE7G,UAAU,2DAChC8G,EAAYc,OAAO,GAGvBd,EAAYe,gBAAgB,EAAI,WAAC5C,MAAAA,CAAIjF,UAAU,2FAC5C,UAAC8H,EAAAA,EAAcA,CAAAA,CAAC9H,UAAU,WAAW,OAEpC8G,EAAYiB,YAAY,EAAI,WAACP,OAAAA,WAAK,KAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAKZ,EAAYiB,YAAY,iBA5CrFjB,EAAYkB,EAAE,CAiDjC,WAKV,CC/OA,IAAMC,EAAiB,CACrB,iBAAkB,CAChBxF,MAAO,OACPC,KAAMC,EAAAA,EAASA,CACfC,MAAO,2CACT,EACA,yBAA0B,CACxBH,MAAO,OACPC,KAAMwF,EAAAA,EAAYA,CAClBtF,MAAO,8CACT,EACA,qBAAsB,CACpBH,MAAO,OACPC,KAAMI,EAAAA,EAAeA,CACrBF,MAAO,iDACT,EACA,oBAAqB,CACnBH,MAAO,OACPC,KAAMO,EAAAA,EAAcA,CACpBL,MAAO,iDACT,EACA,wBAAyB,CACvBH,MAAO,OACPC,KAAMyF,EAAAA,EAAYA,CAClBvF,MAAO,iDACT,EACA,yBAA0B,CACxBH,MAAO,OACPC,KAAMM,EAAAA,EAAiBA,CACvBJ,MAAO,iDACT,CACF,EACMM,EAAe,CACnB,QAAW,CACTT,MAAO,MACPG,CAHcM,KAGP,4BACPR,KAAMqC,EAAAA,EAASA,EAEjB,cAAe,CACbtC,MAAO,MACPG,MAAO,4BACPF,KAAMoF,EAAAA,EACR,EACA,UAAa,CACXrF,MAAO,MACPG,MAAO,8BACPF,KAAM0F,EAAAA,EAASA,EAEjB,UAAa,CACX3F,MAAO,MACPG,MAAO,0BACPF,KAAM2F,EAAAA,EAAKA,CAEf,EACMlF,EAAiB,CACrB,IAAO,CACLV,MAAO,IACPG,MAAO,GAHSO,wBAIlB,EACA,OAAU,CACRV,MAAO,IACPG,MAAO,+BACT,EACA,KAAQ,CACNH,MAAO,IACPG,MAAO,+BACT,EACA,OAAU,CACRH,MAAO,KACPG,MAAO,yBACT,CACF,EACO,SAAS0F,EAAY,WAC1BjF,CAAS,OACTkF,CAAK,SACLhF,GAAU,CAAK,cACfiF,CAAY,aACZC,CAAW,oBACXC,CAAkB,WAClB1I,CAAS,CACT2I,WAAW,MAAM,CACA,EACjB,GAAM,CAACjF,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OAC/C,CAACG,EAAcC,EAAgB,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OACnD,CAACgF,EAAgBC,EAAkB,CAAGjF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OACvD,CAACkF,EAAeC,EAAiB,CAAGnF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB2E,GA2B5DS,EAAc,IAClB,IAAM3E,EAAgB4D,CAAc,CAAC7D,EAAK,EAAE1B,MAAQyF,EAAAA,EAAYA,CAChE,MAAO,UAAC9D,EAAAA,CAAcrE,UAAU,SAASC,sBAAoB,gBAAgBC,wBAAsB,cAAcC,0BAAwB,oBAC3I,EACM8I,EAAoB,GACxB,UAAI,OAAOC,EAAgC,MACpC,GAAGA,EAAW1E,SAAS,EAAI,GAAG,CAAC,EAAE0E,EAAWzE,QAAQ,EAAI,IAAI,CAACC,IAAI,IAAMwE,EAAWvE,KAAK,CAE1FwE,EAAoBhC,GACjB2B,EAAcM,MAAM,CAACC,GAAQA,EAAKlC,MAAM,GAAKA,GAEhDmC,EAAqB,CAACD,EAAmBE,KACzCb,GACFA,EAAmBW,EAAKrB,EAAE,CAAEuB,EAEhC,EACA,GAAIhG,EACF,CALwB,KAKjB,CADI,EACJ,QAACqB,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,YACpB,UAAC6E,EAAAA,EAAUA,CAAAA,UACT,WAACC,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,oCACnB,UAACmI,EAAAA,EAAYA,CAAAA,CAACnI,UAAU,WAAW,YAIvC,UAACgF,EAAAA,EAAWA,CAAAA,UACV,UAACC,MAAAA,CAAIjF,UAAU,qBACZ,sBAAa,CAACkF,GAAG,CAAC,CAACC,EAAGC,IAAM,UAACH,MAAAA,CAAYjF,UAAU,yBAChD,WAACiF,MAAAA,CAAIjF,UAAU,mCACb,UAACiF,MAAAA,CAAIjF,UAAU,oCACf,WAACiF,MAAAA,CAAIjF,UAAU,6BACb,UAACiF,MAAAA,CAAIjF,UAAU,kCACf,UAACiF,MAAAA,CAAIjF,UAAU,yCALgBoF,WAajD,IAAMoE,EAAkBH,IACtB,IAAMrC,EAAaiB,CAAc,CAACoB,EAAKI,QAAQ,CAAC,CAC1CvC,EAAchE,CAAY,CAACmG,EAAKlC,MAAM,CAAC,CACvCC,EAAgBjE,CAAc,CAACkG,EAAKhC,QAAQ,CAAC,CAC7CqC,EAAUC,CAAAA,EAAAA,EAAAA,EAAAA,CAASA,CAACN,EAAKO,OAAO,GAAqB,cAAhBP,EAAKlC,MAAM,CACtD,MAAO,WAAClC,MAAAA,CAAkBjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2C8H,GAAe,mCAAoCiB,GAAW,4BAA6BlE,QAAS,IAAMiD,IAAcY,GAAOnJ,wBAAsB,iBAAiBC,0BAAwB,6BAC7Q,WAAC8E,MAAAA,CAAIjF,UAAU,wDACb,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,UAACiF,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0DqG,GAAYpE,OAAS,sDAC/FoG,EAAYK,EAAKI,QAAQ,IAE5B,UAACnC,KAAAA,CAAGtH,UAAU,6CACXqJ,EAAK9B,KAAK,MAGdmC,GAAW,UAACG,EAAAA,EAAiBA,CAAAA,CAAC7J,UAAU,yCAG1CqJ,EAAKS,WAAW,EAAI,UAACjD,IAAAA,CAAE7G,UAAU,2DAC7BqJ,EAAKS,WAAW,GAGrB,UAAC7E,MAAAA,CAAIjF,UAAU,wDACb,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,UAACqF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAAWoH,EAAcxE,KAAK,CAAE3C,sBAAoB,QAAQE,0BAAwB,4BAC1GiH,EAAc3E,KAAK,GAEtB,UAAC4C,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAAWkH,EAAYtE,KAAK,CAAE3C,sBAAoB,QAAQE,0BAAwB,4BACxG+G,EAAYzE,KAAK,QAKxB,WAACwC,MAAAA,CAAIjF,UAAU,4EACb,WAACwH,OAAAA,CAAKxH,UAAU,oCACd,UAAC+C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,SAASC,sBAAoB,WAAWE,0BAAwB,qBACnF8I,EAAkBI,EAAKH,UAAU,KAEpC,WAAC1B,OAAAA,CAAKxH,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2B+I,GAAW,sCACxD,UAACxB,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,SAASC,sBAAoB,eAAeE,0BAAwB,qBAC3FsH,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK2B,EAAKO,OAAO,SAIxClB,GAAsC,cAAhBW,EAAKlC,MAAM,EAAoB,WAAClC,MAAAA,CAAIjF,UAAU,4BAChEqJ,cAAKlC,MAAM,EAAkB,UAAC5B,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,UAAUqE,QAASQ,IAC7EA,EAAE+D,eAAe,GACjBT,EAAmBD,EAAM,cAC3B,EAAGrJ,UAAU,4BAAmB,OAGX,gBAAhBqJ,EAAKlC,MAAM,EAAsB,UAAC5B,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,UAAUqE,QAASQ,IACjFA,EAAE+D,eAAe,GACjBT,EAAmBD,EAAM,YAC3B,EAAGrJ,UAAU,4BAAmB,YAjDnBqJ,EAAKrB,EAAE,CAsD1B,EACA,MAAO,WAACpD,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,EAAWC,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,6BACtH,WAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,6BACnE,WAAC8E,MAAAA,CAAIjF,UAAU,8CACb,WAAC8E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,6BACrG,UAACgI,EAAAA,EAAYA,CAAAA,CAACnI,UAAU,SAASC,sBAAoB,eAAeE,0BAAwB,qBAAqB,OAEjH,UAACkF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,YAAYnB,UAAU,OAAOC,sBAAoB,QAAQE,0BAAwB,4BAC7F2I,EAAcxD,MAAM,MAGxBkD,GAAgB,WAACjD,EAAAA,CAAMA,CAAAA,CAACC,QAASgD,EAAc/C,KAAK,eACjD,UAACC,EAAAA,EAAQA,CAAAA,CAAC1F,UAAU,gBAAgB,aAM1C,WAACiF,MAAAA,CAAIjF,UAAU,4CACb,WAACiF,MAAAA,CAAIjF,UAAU,4BACb,UAAC2F,EAAAA,EAAUA,CAAAA,CAAC3F,UAAU,kFAAkFC,sBAAoB,aAAaE,0BAAwB,qBACjK,UAACyF,EAAAA,CAAKA,CAAAA,CAACC,YAAY,UAAUC,MAAOpC,EAAYqC,SAAUC,GAAKrC,EAAcqC,EAAEC,MAAM,CAACH,KAAK,EAAG9F,UAAU,QAAQC,sBAAoB,QAAQE,0BAAwB,wBAEtK,WAAC+F,EAAAA,EAAMA,CAAAA,CAACJ,MAAOjC,EAAYsC,cAAerC,EAAe7D,sBAAoB,SAASE,0BAAwB,6BAC5G,WAACiG,EAAAA,EAAaA,CAAAA,CAACpG,UAAU,sBAAsBC,sBAAoB,gBAAgBE,0BAAwB,6BACzG,UAACkG,EAAAA,EAAUA,CAAAA,CAACrG,UAAU,cAAcC,sBAAoB,aAAaE,0BAAwB,qBAC7F,UAACmG,EAAAA,EAAWA,CAAAA,CAACT,YAAY,KAAK5F,sBAAoB,cAAcE,0BAAwB,wBAE1F,WAACoG,EAAAA,EAAaA,CAAAA,CAACtG,sBAAoB,gBAAgBE,0BAAwB,6BACzE,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,MAAM7F,sBAAoB,aAAaE,0BAAwB,4BAAmB,SACnGsG,OAAOC,OAAO,CAACuB,GAAgB/C,GAAG,CAAC,CAAC,CAACY,EAAOa,EAAO,GAAK,UAACH,EAAAA,EAAUA,CAAAA,CAAaV,MAAOA,WACnFa,EAAOlE,KAAK,EADyDqD,UAK9E,WAACI,EAAAA,EAAMA,CAAAA,CAACJ,MAAO8C,EAAgBzC,cAAe0C,EAAmB5I,sBAAoB,SAASE,0BAAwB,6BACpH,UAACiG,EAAAA,EAAaA,CAAAA,CAACpG,UAAU,sBAAsBC,sBAAoB,gBAAgBE,0BAAwB,4BACzG,UAACmG,EAAAA,EAAWA,CAAAA,CAACT,YAAY,MAAM5F,sBAAoB,cAAcE,0BAAwB,uBAE3F,WAACoG,EAAAA,EAAaA,CAAAA,CAACtG,sBAAoB,gBAAgBE,0BAAwB,6BACzE,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,MAAM7F,sBAAoB,aAAaE,0BAAwB,4BAAmB,UACnGsG,OAAOC,OAAO,CAACvD,GAAgB+B,GAAG,CAAC,CAAC,CAACY,EAAOa,EAAO,GAAK,UAACH,CAA5BrD,CAA4BqD,EAAUA,CAAAA,CAAaV,MAAOA,WACnFa,EAAOlE,KAAK,EADyDqD,gBAQlF,UAACd,EAAAA,EAAWA,CAAAA,CAAC/E,sBAAoB,cAAcE,0BAAwB,4BACvD,WAAbwI,EAEH,WAACqB,CADa,CACbA,EAAIA,CAAAA,CAACC,aAAa,UAAUjK,UAAU,mBACjC,UAACkK,EAAAA,EAAQA,CAAAA,CAAClK,UAAU,mCACjByG,OAAOC,OAAO,CAACxD,GAAcgC,GAAG,CAAC,CAAC,CAACiC,EAAQR,EAAO,GAAK,SAA5BzD,EAA6BiH,EAAAA,EAAWA,CAAAA,CAAcrE,MAAOqB,EAAQnH,UAAU,oBACtG2G,EAAOlE,KAAK,CAAC,KAAG0G,EAAiBhC,GAAiC7B,MAAM,CAAC,MADJ6B,MAI3EV,OAAOC,OAAO,CAACxD,GAAcgC,GAAG,CAAC,CAAC,CAACiC,EAAQR,EAAO,GAAK,SAA5BzD,CAA6BkH,EAAAA,EAAWA,CAAAA,CAActE,MAAOqB,WACrF,UAACP,EAAAA,UAAUA,CAAAA,CAAC5G,UAAU,qBACpB,WAACiF,MAAAA,CAAIjF,UAAU,sBACZmJ,EAAiBhC,GAAiCjC,GAAG,CAACsE,GACO,IAA7DL,EAAiBhC,GAAiC7B,MAAM,EAAU,UAACL,MAAAA,CAAIjF,UAAU,kDAC9E,WAAC6G,IAAAA,WAAE,KAAGF,EAAOlE,KAAK,CAAC,gBAL2C0E,OAYhF,EADA,CACA,OAACP,EAAAA,EADW,QACDA,CAAAA,CAAC5G,UAAU,qBACU,IAAzB8I,EAAcxD,MAAM,CAAS,WAACL,MAAAA,CAAIjF,UAAU,mDACzC,UAACmI,EAAAA,EAAYA,CAAAA,CAACnI,UAAU,oCACxB,UAAC6G,IAAAA,CAAE7G,UAAU,+BAAsB,SACnC,UAAC6G,IAAAA,CAAE7G,UAAU,mBAAU,mBAChB,UAACiF,MAAAA,CAAIjF,UAAU,qBACrB8I,EAAc5D,GAAG,CAACsE,WAKnC,gBC1SA,IAAMa,EAAqB,CAAC,CAC1BjG,KAAM,aACN3B,MAAO,OACPqH,YAAa,aACbpH,KAAMC,EAAAA,EAASA,CACfC,MAAO,oCACP0H,QAAS,kBACX,EAAG,CACDlG,KAAM,QACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMG,EAAAA,EAAQA,CACdD,MAAO,sCACP0H,QAAS,mBACX,EAAG,CACDlG,KAAM,oBACN3B,MAAO,OACPqH,YAAa,YACbpH,KAAMI,EAAAA,EAAeA,CACrBF,MAAO,wCACP0H,QAAS,oBACX,EAAG,CACDlG,KAAM,kBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMK,EAAAA,EAAQA,CACdH,MAAO,wCACP0H,QAAS,oBACX,EAAG,CACDlG,KAAM,uBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMM,EAAAA,EAAiBA,CACvBJ,MAAO,wCACP0H,QAAS,oBACX,EAAG,CACDlG,KAAM,kBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMO,EAAAA,EAAcA,CACpBL,MAAO,wCACP0H,QAAS,oBACX,EAAE,CACIC,EAAc,CAAC,CACnBnG,KAAM,iBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMC,EAAAA,EAASA,CACfC,MAAO,oCACP0H,QAAS,kBACX,EAAG,CACDlG,KAAM,yBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMwF,EAAAA,EAAYA,CAClBtF,MAAO,sCACP0H,QAAS,mBACX,EAAG,CACDlG,KAAM,qBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMI,EAAAA,EAAeA,CACrBF,MAAO,wCACP0H,QAAS,oBACX,EAAG,CACDlG,KAAM,oBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMO,EAAAA,EAAcA,CACpBL,MAAO,wCACP0H,QAAS,oBACX,EAAG,CACDlG,KAAM,wBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMyF,EAAAA,EAAYA,CAClBvF,MAAO,wCACP0H,QAAS,oBACX,EAAG,CACDlG,KAAM,yBACN3B,MAAO,OACPqH,YAAa,WACbpH,KAAMM,EAAAA,EAAiBA,CACvBJ,MAAO,wCACP0H,QAAS,oBACX,EAAE,CACK,SAASE,EAAa,SAC3BC,CAAO,CACPjH,qBAAmB,cACnBgF,CAAY,uBACZkC,CAAqB,eACrBC,CAAa,CACb3K,WAAS,CACT4K,WAAU,CAAK,CACG,EAClB,GAAM,CAACC,EAAuBC,EAAyB,CAAGlH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7D,CAACmH,EAAgBC,EAAkB,CAAGpH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,UACrD,EACS,OADI,EACJ,EAACqB,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BX,aAEjD,WAACuF,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,UAAUqE,QAAS,IAAMhC,IAAsB,cAAexD,UAAU,+DAChG,UAAC2C,EAAAA,EAASA,CAAAA,CAAC3C,UAAU,gBAAgB,QAKvC,WAACuF,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,UAAUqE,QAAS,IAAMgD,IAAe,kBAAmBxI,UAAU,kEAC7F,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,gBAAgB,QAKtC0K,GAAyB,WAACnF,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,UAAUqE,QAASkF,EAAuB1K,UAAU,qEACpG,UAACkI,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,gBAAgB,QAK5C,WAACiL,EAAAA,EAAYA,CAAAA,WACX,UAACC,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,aAC1B,UAAC5F,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,mBACxB,UAACuE,EAAAA,EAAQA,CAAAA,CAAC1F,UAAU,eAGxB,WAACoL,EAAAA,EAAmBA,CAAAA,CAACC,MAAM,MAAMrL,UAAU,iBACzC,UAACsL,EAAAA,EAAiBA,CAAAA,UAAC,SACnB,UAACC,EAAAA,EAAqBA,CAAAA,CAAAA,GAErB/H,GAAuB,iCACpB,UAAC8H,EAAAA,EAAiBA,CAAAA,CAACtL,UAAU,qDAA4C,WAGxEqK,EAAmBmB,KAAK,CAAC,EAAG,GAAGtG,GAAG,CAACuG,IACtC,IAAMpH,EAAgBoH,EAAO/I,IAAI,CACjC,MAAO,WAACgJ,EAAAA,EAAgBA,CAAAA,CAAmBlG,QAAS,IAAMhC,EAAoBiI,EAAOrH,IAAI,EAAGpE,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iBAAkB8K,EAAOnB,OAAO,YAClI,UAACjG,EAAAA,CAAcrE,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,cAAe8K,EAAO7I,KAAK,IACvD6I,EAAOhJ,KAAK,GAFSgJ,EAAOrH,IAAI,CAI3C,GACI,UAACmH,EAAAA,EAAqBA,CAAAA,CAAAA,MAGzB/C,GAAgB,iCACb,UAAC8C,EAAAA,EAAiBA,CAAAA,CAACtL,UAAU,qDAA4C,SAGxEuK,EAAYiB,KAAK,CAAC,EAAG,GAAGtG,GAAG,CAACuG,IAC/B,IAAMpH,EAAgBoH,EAAO/I,IAAI,CACjC,MAAO,WAACgJ,EAAAA,EAAgBA,CAAAA,CAAmBlG,QAAS,IAAMgD,EAAaiD,EAAOrH,IAAI,EAAGpE,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iBAAkB8K,EAAOnB,OAAO,YAC3H,UAACjG,EAAAA,CAAcrE,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,cAAe8K,EAAO7I,KAAK,IACvD6I,EAAOhJ,KAAK,GAFSgJ,EAAOrH,IAAI,CAI3C,MAGCuG,GAAiB,iCACd,UAACY,EAAAA,EAAqBA,CAAAA,CAAAA,GACtB,WAACG,EAAAA,EAAgBA,CAAAA,CAAClG,QAASmF,EAAe3K,UAAU,4CAClD,UAACiD,EAAAA,EAAcA,CAAAA,CAACjD,UAAU,8BAA8B,sBAQjE,WAAC4E,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,EAAWC,sBAAoB,OAAOC,wBAAsB,eAAeC,0BAAwB,8BACvH,WAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,8BACnE,WAAC2E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,8BACrG,UAACuF,EAAAA,EAAQA,CAAAA,CAAC1F,UAAU,SAASC,sBAAoB,WAAWE,0BAAwB,sBAAsB,UAG5G,WAAC0G,IAAAA,CAAE7G,UAAU,0CAAgC,KACxCyK,EAAQkB,QAAQ,CAAC,gBAGxB,WAAC3G,EAAAA,EAAWA,CAAAA,CAAChF,UAAU,YAAYC,sBAAoB,cAAcE,0BAAwB,8BAE3F,WAAC8E,MAAAA,CAAIjF,UAAU,kDAEb,UAACuF,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUqE,QAAS,IAAMhC,IAAsB,cAAexD,UAAU,wGAAwGC,sBAAoB,SAASE,0BAAwB,6BACnP,WAAC8E,MAAAA,CAAIjF,UAAU,oCACb,UAAC2C,EAAAA,EAASA,CAAAA,CAAC3C,UAAU,SAASC,sBAAoB,YAAYE,0BAAwB,sBACtF,WAAC8E,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAc,SAC7B,UAACiF,MAAAA,CAAIjF,UAAU,yCAAgC,mBAMpD0K,GAAyB,UAACnF,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUqE,QAASkF,EAAuB1K,UAAU,qHAC1F,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,UAACkI,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,WACxB,WAACiF,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAc,SAC7B,UAACiF,MAAAA,CAAIjF,UAAU,yCAAgC,uBAOxDwD,GAAuB,UAACyB,MAAAA,UACrB,WAACgG,EAAAA,EAAYA,CAAAA,CAACW,KAAMf,EAAuBgB,aAAcf,YACvD,UAACI,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,aAC1B,WAAC5F,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUnB,UAAU,mCAClC,WAACwH,OAAAA,CAAKxH,UAAU,oCACd,UAACgD,EAAAA,EAAiBA,CAAAA,CAAChD,UAAU,WAAW,YAG1C,UAAC8L,EAAAA,EAAeA,CAAAA,CAAC9L,UAAU,gBAG/B,WAACoL,EAAAA,EAAmBA,CAAAA,CAACpL,UAAU,iCAC7B,UAACsL,EAAAA,EAAiBA,CAAAA,UAAC,WACnB,UAACC,EAAAA,EAAqBA,CAAAA,CAAAA,GACrBlB,EAAmBnF,GAAG,CAACuG,IAC1B,IAAMpH,EAAgBoH,EAAO/I,IAAI,CACjC,MAAO,UAACgJ,EAAAA,EAAgBA,CAAAA,CAAmBlG,QAAS,KAClDhC,EAAoBiI,EAAOrH,IAAI,EAC/B0G,GAAyB,EAC3B,EAAG9K,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qBAAsB8K,EAAOnB,OAAO,WAC7C,WAACrF,MAAAA,CAAIjF,UAAU,mCACb,UAACqE,EAAAA,CAAcrE,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gBAAiB8K,EAAO7I,KAAK,IAC1D,WAACqC,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAeyL,EAAOhJ,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCACZyL,EAAO3B,WAAW,UATH2B,EAAOrH,IAAI,CAc3C,WAMHoE,GAAgB,UAACvD,MAAAA,UACd,WAACgG,EAAAA,EAAYA,CAAAA,CAACW,KAAMb,EAAgBc,aAAcb,YAChD,UAACE,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,aAC1B,WAAC5F,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUnB,UAAU,mCAClC,WAACwH,OAAAA,CAAKxH,UAAU,oCACd,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,WAAW,YAGlC,UAAC8L,EAAAA,EAAeA,CAAAA,CAAC9L,UAAU,gBAG/B,WAACoL,EAAAA,EAAmBA,CAAAA,CAACpL,UAAU,iCAC7B,UAACsL,EAAAA,EAAiBA,CAAAA,UAAC,WACnB,UAACC,EAAAA,EAAqBA,CAAAA,CAAAA,GACrBhB,EAAYrF,GAAG,CAACuG,IACnB,IAAMpH,EAAgBoH,EAAO/I,IAAI,CACjC,MAAO,UAACgJ,EAAAA,EAAgBA,CAAAA,CAAmBlG,QAAS,KAClDgD,EAAaiD,EAAOrH,IAAI,EACxB4G,GAAkB,EACpB,EAAGhL,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qBAAsB8K,EAAOnB,OAAO,WAC7C,WAACrF,MAAAA,CAAIjF,UAAU,mCACb,UAACqE,EAAAA,CAAcrE,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gBAAiB8K,EAAO7I,KAAK,IAC1D,WAACqC,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAeyL,EAAOhJ,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCACZyL,EAAO3B,WAAW,UATH2B,EAAOrH,IAAI,CAc3C,WAMHuG,GAAiB,WAACpF,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUqE,QAASmF,EAAe3K,UAAU,oFAC1E,UAACiD,EAAAA,EAAcA,CAAAA,CAACjD,UAAU,gBAAgB,iBAKtD,gBCpRA,IAAMwC,EAAwB,CAC5B,aAAc,CACZC,MAAO,OACPC,KAAMC,EAAAA,EAHiBH,CAIvBI,MAAO,2CACT,EACA,MAAS,CACPH,MAAO,OACPC,KAAMG,EAAAA,EAAQA,CACdD,MAAO,8CACT,EACA,oBAAqB,CACnBH,MAAO,OACPC,KAAMI,EAAAA,EAAeA,CACrBF,MAAO,iDACT,EACA,kBAAmB,CACjBH,MAAO,OACPC,KAAMK,EAAAA,EAAQA,CACdH,MAAO,iDACT,EACA,uBAAwB,CACtBH,MAAO,OACPC,KAAMM,EAAAA,EAAiBA,CACvBJ,MAAO,iDACT,EACA,kBAAmB,CACjBH,MAAO,OACPC,KAAMO,EAAAA,EAAcA,CACpBL,MAAO,iDACT,CACF,EACMqF,EAAiB,CACrB,iBAAkB,CAChBxF,MAAO,KAFSwF,EAGhBvF,KAAMC,EAAAA,EAASA,CACfC,MAAO,2CACT,EACA,yBAA0B,CACxBH,MAAO,OACPC,KAAMwF,EAAAA,EAAYA,CAClBtF,MAAO,8CACT,EACA,qBAAsB,CACpBH,MAAO,OACPC,KAAMI,EAAAA,EAAeA,CACrBF,MAAO,iDACT,EACA,oBAAqB,CACnBH,MAAO,OACPC,KAAMO,EAAAA,EAAcA,CACpBL,MAAO,iDACT,EACA,wBAAyB,CACvBH,MAAO,OACPC,KAAMK,EAAAA,EAAQA,CACdH,MAAO,iDACT,EACA,yBAA0B,CACxBH,MAAO,OACPC,KAAMM,EAAAA,EAAiBA,CACvBJ,MAAO,iDACT,CACF,EACO,SAASmJ,EAAiB,WAC/B1I,CAAS,UACT2I,CAAQ,CACRzI,WAAU,CAAK,CACf0I,aAAW,YACXC,CAAU,WACVlM,CAAS,CACa,EACtB,GAAM,CAAC0D,EAAYC,EAAc,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAACC,EAAYC,EAAc,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OAC/C,CAACuI,EAAWC,EAAa,CAAGxI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,OAC7C,CAACyI,EAAkBC,EAAoB,CAAG1I,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiBoI,GA4BnEO,EAAc,IAClB,GAAIC,kBAAKpI,IAAI,CAAoB,CAE/B,IAAMuC,EAASnE,CAAqB,CADhBgK,EAAKC,IAAI,CACoBxF,eAAe,CAAC,CAC3D5C,EAAgBsC,GAAQjE,MAAQM,EAAAA,EAAiBA,CACvD,MAAO,UAACqB,EAAAA,CAAcrE,UAAU,UAClC,CAAO,CAEL,IAAM2G,EAASsB,CAAc,CAACoB,EADZoD,IAAI,CACahD,QAAQ,CAAC,CACtCpF,EAAgBsC,GAAQjE,MAAQqC,EAAAA,EAASA,CAC/C,MAAO,UAACV,EAAAA,CAAcrE,UAAU,UAClC,CACF,EACM0M,EAAe,IACnB,GAAkB,gBAAdF,EAAKpI,IAAI,CAAoB,CAC/B,IAAM0C,EAAc0F,EAAKC,IAAI,CAC7B,OAAOjK,CAAqB,CAACsE,EAAYG,eAAe,CAAC,EAAErE,OAAS,2CACtE,CAAO,CACL,IAAMyG,EAAOmD,EAAKC,IAAI,CACtB,OAAOxE,CAAc,CAACoB,EAAKI,QAAQ,CAAC,EAAE7G,OAAS,2CACjD,CACF,EACM+J,EAAe,GACnB,EACqB,EADjB,GAAQ,KACmB,OAApBC,EAA2B,SAC/B,GAAGA,EAAMpI,SAAS,EAAI,GAAG,CAAC,EAAEoI,EAAMnI,QAAQ,EAAI,IAAI,CAACC,IAAI,IAAMkI,EAAMjI,KAAK,CAF5D,KA4GrB,GAAIpB,EACF,MAAO,CADI,EACJ,QAACqB,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,YACpB,UAAC6E,EAAAA,EAAUA,CAAAA,UACT,WAACC,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,oCACnB,UAACgD,EAAAA,EAAiBA,CAAAA,CAAChD,UAAU,WAAW,YAI5C,UAACgF,EAAAA,EAAWA,CAAAA,UACV,UAACC,MAAAA,CAAIjF,UAAU,qBACZ,sBAAa,CAACkF,GAAG,CAAC,CAACC,EAAGC,IAAM,UAACH,MAAAA,CAAYjF,UAAU,yBAChD,WAACiF,MAAAA,CAAIjF,UAAU,mCACb,UAACiF,MAAAA,CAAIjF,UAAU,oCACf,WAACiF,MAAAA,CAAIjF,UAAU,6BACb,UAACiF,MAAAA,CAAIjF,UAAU,kCACf,UAACiF,MAAAA,CAAIjF,UAAU,yCALgBoF,WAajD,IAAMyH,EAAmBb,EAAS5C,MAAM,CAACoD,GAAQA,kBAAKpI,IAAI,EAAoBkB,MAAM,CAC9EwH,EAAYd,EAAS5C,MAAM,CAACoD,GAAsB,SAAdA,EAAKpI,IAAI,EAAakB,MAAM,CACtE,MAAO,WAACV,EAAAA,EAAIA,CAAAA,CAAC5E,UAAWA,EAAWC,sBAAoB,OAAOC,wBAAsB,mBAAmBC,0BAAwB,kCAC3H,WAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,kCACnE,WAAC2E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,kCACrG,UAAC6C,EAAAA,EAAiBA,CAAAA,CAAChD,UAAU,SAASC,sBAAoB,oBAAoBE,0BAAwB,0BAA0B,OAEhI,UAACkF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,YAAYnB,UAAU,OAAOC,sBAAoB,QAAQE,0BAAwB,iCAC7FkM,EAAiB/G,MAAM,MAK5B,WAACL,MAAAA,CAAIjF,UAAU,4CACb,WAACiF,MAAAA,CAAIjF,UAAU,4BACb,UAAC2F,EAAAA,EAAUA,CAAAA,CAAC3F,UAAU,kFAAkFC,sBAAoB,aAAaE,0BAAwB,0BACjK,UAACyF,EAAAA,CAAKA,CAAAA,CAACC,YAAY,YAAYC,MAAOpC,EAAYqC,SAAUC,GAAKrC,EAAcqC,EAAEC,MAAM,CAACH,KAAK,EAAG9F,UAAU,QAAQC,sBAAoB,QAAQE,0BAAwB,6BAExK,WAAC+F,EAAAA,EAAMA,CAAAA,CAACJ,MAAOjC,EAAYsC,cAAerC,EAAe7D,sBAAoB,SAASE,0BAAwB,kCAC5G,WAACiG,EAAAA,EAAaA,CAAAA,CAACpG,UAAU,sBAAsBC,sBAAoB,gBAAgBE,0BAAwB,kCACzG,UAACkG,EAAAA,EAAUA,CAAAA,CAACrG,UAAU,cAAcC,sBAAoB,aAAaE,0BAAwB,0BAC7F,UAACmG,EAAAA,EAAWA,CAAAA,CAACT,YAAY,KAAK5F,sBAAoB,cAAcE,0BAAwB,6BAE1F,WAACoG,EAAAA,EAAaA,CAAAA,CAACtG,sBAAoB,gBAAgBE,0BAAwB,kCACzE,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,MAAM7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACzG,UAAC4M,EAAAA,SAASA,CAAAA,CAAC/M,UAAU,OAAOC,sBAAoB,YAAYE,0BAAwB,0BACpF,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,aAAa7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SAChH,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,QAAQ7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SAC3G,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,oBAAoB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACvH,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,kBAAkB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACrH,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,uBAAuB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SAC1H,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,kBAAkB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACrH,UAAC4M,EAAAA,SAASA,CAAAA,CAAC/M,UAAU,OAAOC,sBAAoB,YAAYE,0BAAwB,0BACpF,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,iBAAiB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACpH,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,yBAAyB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SAC5H,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,qBAAqB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACxH,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,oBAAoB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SACvH,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,wBAAwB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,SAC3H,UAACqG,EAAAA,EAAUA,CAAAA,CAACV,MAAM,yBAAyB7F,sBAAoB,aAAaE,0BAAwB,iCAAwB,qBAMpI,UAAC6E,EAAAA,EAAWA,CAAAA,CAAC/E,sBAAoB,cAAcE,0BAAwB,iCACrE,WAAC6J,EAAAA,EAAIA,CAAAA,CAAClE,MAAOqG,EAAWhG,cAAeiG,EAAcpM,UAAU,SAASC,sBAAoB,OAAOE,0BAAwB,kCACzH,WAAC+J,EAAAA,EAAQA,CAAAA,CAAClK,UAAU,0BAA0BC,sBAAoB,WAAWE,0BAAwB,kCACnG,WAACgK,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,MAAM7F,sBAAoB,cAAcE,0BAAwB,kCAAwB,OACpG6L,EAAS1G,MAAM,CAAC,OAEvB,WAAC6E,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,cAAc7F,sBAAoB,cAAcE,0BAAwB,kCAAwB,OAC5G0M,EAAiB,OAExB,WAAC1C,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,OAAO7F,sBAAoB,cAAcE,0BAAwB,kCAAwB,OACrG2M,EAAU,UAInB,UAAC1C,EAAAA,EAAWA,CAAAA,CAACtE,MAAOqG,EAAWnM,UAAU,OAAOC,sBAAoB,cAAcE,0BAAwB,iCACxG,UAACyG,EAAAA,UAAUA,CAAAA,CAAC5G,UAAU,YAAYC,sBAAoB,aAAaE,0BAAwB,iCAC5D,IAA5BkM,EAAiB/G,MAAM,CAAS,WAACL,MAAAA,CAAIjF,UAAU,mDAC5C,UAACgD,EAAAA,EAAiBA,CAAAA,CAAChD,UAAU,oCAC7B,UAAC6G,IAAAA,CAAE7G,UAAU,+BAAsB,WACnC,UAAC6G,IAAAA,CAAE7G,UAAU,mBAAU,qBAChB,UAACiF,MAAAA,CAAIjF,UAAU,qBACrBqM,EAAiBnH,GAAG,CAhMV,CAACsH,EAAoBzF,KAC9C,IAAMiG,EAA8B,gBAAdR,EAAKpI,IAAI,CACzBqI,EAAOD,EAAKC,IAAI,CAChBQ,EAAYP,EAAaF,GAC/B,MAAO,WAACvH,MAAAA,CAAkBjF,UAAU,WAAWE,wBAAsB,qBAAqBC,0BAAwB,kCAE7G4G,EAAQsF,EAAiB/G,MAAM,CAAG,GAAK,UAACL,MAAAA,CAAIjF,UAAU,mDAEvD,WAACiF,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEsL,GAAe,oCAAqCzG,QAAS,IAAMyG,IAAcO,aAEpK,UAACvH,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8EAA+EsM,YAC/FV,EAAYC,KAIf,WAACvH,MAAAA,CAAIjF,UAAU,2BACb,WAACiF,MAAAA,CAAIjF,UAAU,wDACb,WAACiF,MAAAA,WACC,UAACqC,KAAAA,CAAGtH,UAAU,kDACXwM,EAAKjF,KAAK,GAEb,WAACtC,MAAAA,CAAIjF,UAAU,kEACb,UAACqF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAAU,UAAUC,sBAAoB,QAAQE,0BAAwB,iCAC9F6M,EAAgB,KAAO,OAE1B,WAACxF,OAAAA,CAAKxH,UAAU,oCACd,UAAC+C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,SAASC,sBAAoB,WAAWE,0BAAwB,0BACnF6M,EAAgBL,EAAaH,EAAKjI,WAAW,EAAI,CAAC,KAAK,EAAEoI,EAAaH,EAAKtD,UAAU,GAAG,UAK/F,WAACjE,MAAAA,CAAIjF,UAAU,kDACb,WAACqF,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAA2B,cAAhBwM,EAAKrF,MAAM,EAAoC,aAAhBqF,EAAKrF,MAAM,EAAmC,WAAhBqF,EAAKrF,MAAM,CAAgB,8BAAgD,gBAAhBqF,EAAKrF,MAAM,CAAqB,4BAA8B,4BAA6BlH,sBAAoB,QAAQE,0BAAwB,kCACxR,SAAhBqM,EAAKrF,MAAM,EAAe,KAC1BqF,kBAAKrF,MAAM,EAAsB,MACjB,aAAhBqF,EAAKrF,MAAM,EAAmB,MACd,WAAhBqF,EAAKrF,MAAM,EAAiB,MAC5BqF,cAAKrF,MAAM,EAAkB,MACb,cAAhBqF,EAAKrF,MAAM,EAAoB,MACf,cAAhBqF,EAAKrF,MAAM,EAAoB,SAElC,WAAC9B,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,UAAUnB,UAA6B,WAAlBwM,EAAKnF,QAAQ,EAAmC,SAAlBmF,EAAKnF,QAAQ,CAAc,0BAA8C,WAAlBmF,EAAKnF,QAAQ,CAAgB,gCAAkC,4BAA6BpH,sBAAoB,QAAQE,0BAAwB,kCACpP,QAAlBqM,EAAKnF,QAAQ,EAAc,IACT,WAAlBmF,EAAKnF,QAAQ,EAAiB,IACZ,SAAlBmF,EAAKnF,QAAQ,EAAe,IACV,WAAlBmF,EAAKnF,QAAQ,EAAiB,cAMpC2F,EAAgB,WAAC/H,MAAAA,WACZwH,EAA4B7E,OAAO,EAAI,UAACf,IAAAA,CAAE7G,UAAU,2DACjD,EAA6B4H,OAAO,GAEvC6E,EAA4B5E,gBAAgB,EAAI,WAAC5C,MAAAA,CAAIjF,UAAU,gGAC7D,UAAC8H,EAAAA,EAAcA,CAAAA,CAAC9H,UAAU,WAAW,OAEnCyM,EAA4B1E,YAAY,EAAI,WAACP,OAAAA,WAAK,KAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK,EAA6BK,YAAY,YAErH,WAAC9C,MAAAA,WACNwH,EAAqB3C,WAAW,EAAI,UAACjD,IAAAA,CAAE7G,UAAU,2DAC9C,EAAsB8J,WAAW,GAEtC,WAAC7E,MAAAA,CAAIjF,UAAU,kEACb,WAACwH,OAAAA,CAAKxH,UAAU,oCACd,UAACkI,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,WAAW,OAC9ByH,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK,EAAsBkC,OAAO,MAE1D6C,EAAqBS,WAAW,EAAI,WAAC1F,OAAAA,CAAKxH,UAAU,mDAClD,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,WAAW,OAC1ByM,EAAqBS,WAAW,CAAGzF,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK,EAAsBwF,WAAW,GAAM,eAMlH,WAACjI,MAAAA,CAAIjF,UAAU,mDACb,WAACwH,OAAAA,CAAKxH,UAAU,0CACbmN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAAC,IAAIzF,KAAK8E,EAAK7E,SAAS,GAAG,MAAIF,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK8E,EAAK7E,SAAS,MAGxFsE,CAAAA,GAAeC,CAAAA,CAAS,EAAM,WAACjH,MAAAA,CAAIjF,UAAU,oCAC1CiM,GAAe,WAAC1G,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,QAAQqE,QAASQ,IAC7DA,EAAE+D,eAAe,GACjBkC,EAAYO,EACd,EAAGxM,UAAU,6BACL,UAACoN,EAAAA,EAAOA,CAAAA,CAACpN,UAAU,gBAAgB,QAGtCkM,GAAc,WAAC3G,EAAAA,CAAMA,CAAAA,CAACE,KAAK,KAAKtE,QAAQ,QAAQqE,QAASQ,IAC5DA,EAAE+D,eAAe,GACjBmC,EAAWM,EACb,EAAGxM,UAAU,6BACL,UAACqN,EAAAA,EAAQA,CAAAA,CAACrN,UAAU,gBAAgB,sBA3FrCwM,EAAKxE,EAAE,CAmG1B,gBAgGF,sGC7UA,IAAMsF,EAAoBC,EAAAA,CAACA,CAACC,MAAM,CAAC,CACjCvG,gBAAiBsG,EAAAA,CAACA,CAACE,IAAI,CAAC,CAAC,aAAc,QAAS,oBAAqB,kBAAmB,uBAAwB,kBAAkB,EAClIlG,MAAOgG,EAAAA,CAACA,CAACG,MAAM,GAAGC,GAAG,CAAC,EAAG,WAAWC,GAAG,CAAC,IAAK,gBAC7CC,MAAON,EAAAA,CAACA,CAACG,MAAM,GAAGC,GAAG,CAAC,EAAG,WACzB/F,QAAS2F,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,GAC5BjG,iBAAkB0F,EAAAA,CAACA,CAACQ,OAAO,GAAGC,OAAO,EAAC,GACtCjG,aAAcwF,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,GACjCzG,SAAUkG,EAAAA,CAACA,CAACE,IAAI,CAAC,CAAC,MAAO,SAAU,OAAO,EAAEO,OAAO,CAAC,UACpD7G,OAAQoG,EAAAA,CAACA,CAACE,IAAI,CAAC,CAAC,OAAQ,cAAe,WAAY,SAAS,EAAEO,OAAO,CAAC,QACtEC,mBAAoBV,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,GACvCI,YAAaX,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,EAClC,GAYMK,EAAyB,CAAC,CAC9BrI,MAAO,aACPrD,MAAO,OACPqH,YAAa,YACf,EAAG,CACDhE,MAAO,QACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,oBACPrD,MAAO,OACPqH,YAAa,WACf,EAAG,CACDhE,MAAO,kBACPrD,MAAO,OACPqH,YAAa,YACf,EAAG,CACDhE,MAAO,uBACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,kBACPrD,MAAO,OACPqH,YAAa,UACf,EAAE,CACIsE,EAAkB,CAAC,CACvBtI,MAAO,MACPrD,MAAO,IACPqH,YAAa,OACf,EAAG,CACDhE,MAAO,SACPrD,MAAO,IACPqH,YAAa,OACf,EAAG,CACDhE,MAAO,OACPrD,MAAO,IACPqH,YAAa,aACf,EAAE,CACIuE,EAAgB,CAAC,CACrBvI,MAAO,OACPrD,MAAO,KACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,cACPrD,MAAO,MACPqH,YAAa,OACf,EAAG,CACDhE,MAAO,WACPrD,MAAO,MACPqH,YAAa,OACf,EAAG,CACDhE,MAAO,SACPrD,MAAO,MACPqH,YAAa,OACf,EAAE,CACK,SAASwE,EAAsB,MACpC1C,CAAI,CACJC,cAAY,WACZxI,CAAS,aACTkL,CAAW,aACXzH,CAAW,aACX0H,CAAW,UACXC,CAAQ,SACRlL,EAAU,EAAK,CACY,EAC3B,GAAM,CAACwE,EAAc2G,EAAgB,CAAG9K,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAC1C,CAAC+K,EAAcC,EAAgB,CAAGhL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,SAC3CiL,EAAY,CAAC,CAAC/H,EACdgI,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAsB,CACxCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC3B,GACtB4B,cAAe,CACbjI,gBAAiBuH,GAAsB,aACvCjH,MAAO,GACPsG,MAAO,GACPjG,QAAS,GACTC,kBAAkB,EAClBE,aAAc,GACdV,SAAU,SACVF,OAAQ,OACR8G,mBAAoB,GACpBC,YAAa,EACf,CACF,GAyCMiB,EAAe,MAAO1C,IAC1B,GAAI,CAEF,GAAIA,EAAK5E,gBAAgB,EAAIE,EAAc,CACzC,GAAM,CAACqH,EAAOC,EAAQ,CAAGV,EAAaW,KAAK,CAAC,KACtCC,EAAe,IAAI7H,KAAKK,GAC9BwH,EAAaC,QAAQ,CAACC,SAASL,GAAQK,SAASJ,IAChD5C,EAAK1E,YAAY,CAAGwH,EAAaG,WAAW,EAC9C,CACA,IAAMC,EAAuC,CAC3C,GAAGlD,CAAI,CACPhC,QAASpH,CACX,CACA,OAAMoL,EAASkB,GACf9D,GAAa,EACf,CAAE,MAAO+D,EAAO,CACdC,QAAQD,KAAK,CAAC,gCAAiCA,EACjD,CACF,EACME,EAAwBhB,EAAKiB,KAAK,CAAC,oBACzC,MAAO,UAACC,EAAAA,EAAMA,CAAAA,CAACpE,KAAMA,EAAMC,aAAcA,EAAc5L,sBAAoB,SAASC,wBAAsB,wBAAwBC,0BAAwB,uCACtJ,WAAC8P,EAAAA,EAAaA,CAAAA,CAACjQ,UAAU,yCAAyCC,sBAAoB,gBAAgBE,0BAAwB,wCAC5H,WAAC+P,EAAAA,EAAYA,CAAAA,CAACjQ,sBAAoB,eAAeE,0BAAwB,wCACvE,UAACgQ,EAAAA,EAAWA,CAAAA,CAAClQ,sBAAoB,cAAcE,0BAAwB,uCACpE0O,EAAY,SAAW,WAE1B,WAACuB,EAAAA,EAAiBA,CAAAA,CAACnQ,sBAAoB,oBAAoBE,0BAAwB,wCAA8B,OAC3G,UAACkQ,SAAAA,UAAQ9B,IAAqB,IAAEM,EAAY,KAAO,KAAK,aAIhE,UAACyB,EAAAA,EAAIA,CAAAA,CAAE,GAAGxB,CAAI,CAAE7O,sBAAoB,OAAOE,0BAAwB,uCACjE,WAAC2O,OAAAA,CAAKL,SAAUK,EAAKK,YAAY,CAACA,GAAenP,UAAU,sBAEzD,UAACuQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,kBAAkBC,OAAQ,CAAC,CAClEC,OAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,WACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAACT,YAAY,eAG7B,UAACU,EAAAA,EAAaA,CAAAA,UACX4H,EAAuBjJ,GAAG,CAAC6L,GAAU,UAACvK,EAAAA,EAAUA,CAAAA,CAAoBV,MAAOiL,EAAOjL,KAAK,UACpF,WAACb,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAe+Q,EAAOtO,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCAAiC+Q,EAAOjH,WAAW,OAHjBiH,EAAOjL,KAAK,QAQvE,UAACkL,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,gCAGzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,QAAQC,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,WACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAAClL,EAAAA,CAAKA,CAAAA,CAACC,YAAY,cAAe,GAAG8K,CAAK,KAE5C,UAACM,EAAAA,EAAeA,CAAAA,UAAC,sBAGjB,UAACD,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,gCAGzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,QAAQC,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,WACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACI,EAAAA,CAAQA,CAAAA,CAACrL,YAAY,yBAAyB7F,UAAU,gBAAiB,GAAG2Q,CAAK,KAEpF,UAACM,EAAAA,EAAeA,CAAAA,UAAC,sBAGjB,UAACD,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,gCAGzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,UAAUC,OAAQ,CAAC,CAC1DC,OAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACI,EAAAA,CAAQA,CAAAA,CAACrL,YAAY,uBAAuB7F,UAAU,eAAgB,GAAG2Q,CAAK,KAEjF,UAACM,EAAAA,EAAeA,CAAAA,UAAC,qBAGjB,UAACD,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,gCAGzE,WAAC8E,MAAAA,CAAIjF,UAAU,mCACb,UAACuQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,WAAWC,OAAQ,CAAC,OAC3DC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,QACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAAAA,OAGhB,UAACC,EAAAA,EAAaA,CAAAA,UACX6H,EAAgBlJ,GAAG,CAAC6L,GAAU,UAACvK,EAAAA,EAAUA,CAAAA,CAAoBV,MAAOiL,EAAOjL,KAAK,UAC7E,WAACb,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAe+Q,EAAOtO,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCAAiC+Q,EAAOjH,WAAW,OAHxBiH,EAAOjL,KAAK,QAQhE,UAACkL,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,gCAEzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,SAASC,OAAQ,CAAC,OACzDC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,OACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAAAA,OAGhB,UAACC,EAAAA,EAAaA,CAAAA,UACX8H,EAAcnJ,GAAG,CAAC6L,GAAU,UAACvK,EAAAA,EAAUA,CAAAA,CAAoBV,MAAOiL,EAAOjL,KAAK,UAC3E,WAACb,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAe+Q,EAAOtO,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCAAiC+Q,EAAOjH,WAAW,OAH1BiH,EAAOjL,KAAK,QAQ9D,UAACkL,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,mCAI3E,WAAC8E,MAAAA,CAAIjF,UAAU,sBACb,UAACuQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,mBAAmBC,OAAQ,CAAC,OACnEC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,CAAC5Q,UAAU,0DAClB,UAAC8Q,EAAAA,EAAWA,CAAAA,UACV,UAACK,EAAAA,CAAQA,CAAAA,CAACC,QAAST,EAAM7K,KAAK,CAAEuL,gBAAiBV,EAAM5K,QAAQ,KAEjE,WAACd,MAAAA,CAAIjF,UAAU,mCACb,UAAC6Q,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACI,EAAAA,EAAeA,CAAAA,UAAC,wBAIRhR,sBAAoB,YAAYE,0BAAwB,gCAExE2P,GAAyB,WAAC7K,MAAAA,CAAIjF,UAAU,mCACrC,WAACiF,MAAAA,WACC,UAAC4L,EAAAA,EAASA,CAAAA,UAAC,SACX,WAACS,EAAAA,EAAOA,CAAAA,WACN,UAACC,EAAAA,EAAcA,CAAAA,CAACpG,OAAO,aACrB,WAAC5F,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUnB,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6CAA8C,CAACoH,GAAgB,mCACrG,UAACG,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,iBACvB+H,EAAeN,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAACM,GAAgB,YAGnD,UAACyJ,EAAAA,EAAcA,CAAAA,CAACxR,UAAU,aAAaqL,MAAM,iBAC3C,UAAC/K,EAAAA,CAAQA,CAAAA,CAACqB,KAAK,SAAS8P,SAAU1J,EAAc2J,SAAUhD,EAAiBiD,SAAUC,GAAQA,EAAO,IAAIlK,KAAQmK,YAAY,cAKlI,WAAC5M,MAAAA,WACC,UAAC4L,EAAAA,EAASA,CAAAA,UAAC,SACX,WAAC5L,MAAAA,CAAIjF,UAAU,qBACb,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,qFACrB,UAAC4F,EAAAA,CAAKA,CAAAA,CAACxB,KAAK,OAAO0B,MAAO6I,EAAc5I,SAAUC,GAAK4I,EAAgB5I,EAAEC,MAAM,CAACH,KAAK,EAAG9F,UAAU,sBAM5G,WAAC8R,EAAAA,EAAYA,CAAAA,CAAC7R,sBAAoB,eAAeE,0BAAwB,wCACvE,UAACoF,EAAAA,CAAMA,CAAAA,CAACnB,KAAK,SAASjD,QAAQ,UAAUqE,QAAS,IAAMqG,GAAa,GAAQ8F,SAAUpO,EAAStD,sBAAoB,SAASE,0BAAwB,uCAA8B,OAGlL,UAACoF,EAAAA,CAAMA,CAAAA,CAACnB,KAAK,SAASuN,SAAUpO,EAAStD,sBAAoB,SAASE,0BAAwB,uCAC3FoD,EAAU,SAAWsL,EAAY,KAAO,mBAOzD,CC1VA,IAAMkD,EAAaxE,EAAAA,CAACA,CAACC,MAAM,CAAC,CAC1B/D,SAAU8D,EAAAA,CAACA,CAACE,IAAI,CAAC,CAAC,iBAAkB,yBAA0B,qBAAsB,oBAAqB,wBAAyB,yBAAyB,EAC3JlG,MAAOgG,EAAAA,CAACA,CAACG,MAAM,GAAGC,GAAG,CAAC,EAAG,WAAWC,GAAG,CAAC,IAAK,gBAC7C9D,YAAayD,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,GAChC5E,WAAYqE,EAAAA,CAACA,CAACG,MAAM,GAAGC,GAAG,CAAC,EAAG,UAC9B/D,QAAS2D,EAAAA,CAACA,CAACG,MAAM,GAAGC,GAAG,CAAC,EAAG,WAC3BtG,SAAUkG,EAAAA,CAACA,CAACE,IAAI,CAAC,CAAC,MAAO,SAAU,OAAQ,SAAS,EAAEO,OAAO,CAAC,UAC9D7G,OAAQoG,EAAAA,CAACA,CAACE,IAAI,CAAC,CAAC,UAAW,cAAe,YAAa,YAAY,EAAEO,OAAO,CAAC,WAC7EgE,mBAAoBzE,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,GACvCmE,gBAAiB1E,EAAAA,CAACA,CAACG,MAAM,GAAGI,QAAQ,EACtC,GAcMoE,EAAkB,CAAC,CACvBpM,MAAO,iBACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,yBACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,qBACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,oBACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,wBACPrD,MAAO,OACPqH,YAAa,UACf,EAAG,CACDhE,MAAO,yBACPrD,MAAO,OACPqH,YAAa,UACf,EAAE,CACIsE,EAAkB,CAAC,CACvBtI,MAAO,MACPrD,MAAO,IACPqH,MAHmBsE,MAGN,QACbxL,MAAO,eACT,EAAG,CACDkD,MAAO,SACPrD,MAAO,IACPqH,YAAa,QACblH,MAAO,iBACT,EAAG,CACDkD,MAAO,OACPrD,MAAO,IACPqH,YAAa,cACblH,MAAO,iBACT,EAAG,CACDkD,MAAO,SACPrD,MAAO,KACPqH,YAAa,cACblH,MAAO,cACT,EAAE,CACIyL,EAAgB,CAAC,CACrBvI,MAAO,UACPrD,MAAO,IAFU4L,EAGjBvE,YAAa,QACf,EAAG,CACDhE,MAAO,cACPrD,MAAO,MACPqH,YAAa,OACf,EAAG,CACDhE,MAAO,YACPrD,MAAO,MACPqH,YAAa,OACf,EAAG,CACDhE,MAAO,YACPrD,MAAO,MACPqH,YAAa,OACf,EAAE,CACK,SAASqI,EAAe,CAC7BvG,MAAI,CACJC,cAAY,WACZxI,CAAS,aACTkL,CAAW,MACXlF,CAAI,aACJmF,CAAW,sBACX4D,CAAoB,gBACpBC,CAAc,UACd5D,CAAQ,SACRlL,GAAU,CAAK,CACK,EACpB,GAAM,CAACqG,EAAS0I,EAAW,CAAG1O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAChC,CAAC2O,EAASC,EAAW,CAAG5O,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,SACjCiL,EAAY,CAAC,CAACxF,EACdyF,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAe,CACjCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC8C,GACtB7C,cAAe,CACbzF,SAAU+E,GAAsB,iBAChCjH,MAAO,GACPuC,YAAa,GACbZ,WAAY,GACZU,QAAS,GACTvC,SAAU,SACVF,OAAQ,UACR6K,mBAAoBI,GAAwB,GAC5CH,gBAAiB,EACnB,CACF,GAwCM9C,EAAe,MAAO1C,IAC1B,GAAI,CAEF,GAAI7C,EAAS,CACX,GAAM,CAACwF,EAAOC,EAAQ,CAAGkD,EAAQjD,KAAK,CAAC,KACjCC,EAAe,IAAI7H,KAAKkC,GAC9B2F,EAAaC,QAAQ,CAACC,SAASL,GAAQK,SAASJ,IAChD5C,EAAK7C,OAAO,CAAG2F,EAAaG,WAAW,EACzC,CACA,IAAMC,EAAgC,CACpC,GAAGlD,CAAI,CACPhC,QAASpH,CACX,CACA,OAAMoL,EAASkB,GACf9D,GAAa,EACf,CAAE,MAAO+D,EAAO,CACdC,QAAQD,KAAK,CAAC,yBAA0BA,EAC1C,CACF,EACM6C,EAAc3D,EAAKiB,KAAK,CAAC,UAY/B,OAXsBjB,EAAKiB,KAAK,CAAC,YAW1B,UAACC,EAAAA,EAAMA,CAAAA,CAACpE,KAAMA,EAAMC,aAAcA,EAAc5L,sBAAoB,SAASC,wBAAsB,iBAAiBC,0BAAwB,gCAC/I,WAAC8P,EAAAA,EAAaA,CAAAA,CAACjQ,UAAU,yCAAyCC,sBAAoB,gBAAgBE,0BAAwB,iCAC5H,WAAC+P,EAAAA,EAAYA,CAAAA,CAACjQ,sBAAoB,eAAeE,0BAAwB,iCACvE,UAACgQ,EAAAA,EAAWA,CAAAA,CAAClQ,sBAAoB,cAAcE,0BAAwB,gCACpE0O,EAAY,OAAS,SAExB,WAACuB,EAAAA,EAAiBA,CAAAA,CAACnQ,sBAAoB,oBAAoBE,0BAAwB,iCAAuB,OACpG,UAACkQ,SAAAA,UAAQ9B,IAAqB,IAAEM,EAAY,KAAO,KAAK,aAIhE,UAACyB,EAAAA,EAAIA,CAAAA,CAAE,GAAGxB,CAAI,CAAE7O,sBAAoB,OAAOE,0BAAwB,gCACjE,WAAC2O,OAAAA,CAAKL,SAAUK,EAAKK,YAAY,CAACA,GAAenP,UAAU,sBAEzD,UAACuQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,WAAWC,OAAQ,CAAC,OAC3DC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,WACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAACT,YAAY,eAG7B,UAACU,EAAAA,EAAaA,CAAAA,UACX2L,EAAgBhN,GAAG,CAAC6L,GAAU,UAACvK,EAAAA,EAAUA,CAAAA,CAAoBV,MAAOiL,EAAOjL,KAAK,UAC7E,WAACb,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAe+Q,EAAOtO,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCAAiC+Q,EAAOjH,WAAW,OAHxBiH,EAAOjL,KAAK,QAQhE,UAACkL,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,yBAGzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,QAAQC,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,WACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAAClL,EAAAA,CAAKA,CAAAA,CAACC,YAAY,WAAY,GAAG8K,CAAK,KAEzC,UAACM,EAAAA,EAAeA,CAAAA,UAAC,iBAGjB,UAACD,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,yBAGzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,cAAcC,OAAQ,CAAC,CAC9DC,OAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACI,EAAAA,CAAQA,CAAAA,CAACrL,YAAY,mBAAmB7F,UAAU,gBAAiB,GAAG2Q,CAAK,KAE9E,UAACM,EAAAA,EAAeA,CAAAA,UAAC,qBAGjB,UAACD,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,yBAGzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,aAAaC,OAAQ,CAAC,CAC7DC,OAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,UACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAACT,YAAY,iBACvB,WAACZ,MAAAA,CAAIjF,UAAU,oCACb,UAAC+C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,WACpB,UAACwH,OAAAA,UAAK,mBAKd,UAACjB,EAAAA,EAAaA,CAAAA,UACX8L,EAAenN,GAAG,CAAC0H,GAAS,UAACpG,EAAAA,EAAUA,CAAAA,CAAgBV,MAAO8G,EAAM5E,EAAE,UACnE,WAAC/C,MAAAA,CAAIjF,UAAU,oCACb,UAAC+C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,WACpB,WAACiF,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBACZ,GAAG4M,EAAMpI,SAAS,EAAI,GAAG,CAAC,EAAEoI,EAAMnI,QAAQ,EAAI,IAAI,CAACC,IAAI,IAAMkI,EAAMjI,KAAK,GAE3E,WAACM,MAAAA,CAAIjF,UAAU,0CACG,UAAf4M,EAAM8F,IAAI,EAAgB,MAC1B9F,aAAM8F,IAAI,EAAiB,KACZ,eAAf9F,EAAM8F,IAAI,EAAqB,eAVI9F,EAAM5E,EAAE,QAiB1D,UAACgJ,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,yBAGzE,WAAC8E,MAAAA,CAAIjF,UAAU,mCACb,WAACiF,MAAAA,WACC,UAAC4L,EAAAA,EAASA,CAAAA,CAAC5Q,sBAAoB,YAAYE,0BAAwB,gCAAuB,WAC1F,WAACmR,EAAAA,EAAOA,CAAAA,CAACrR,sBAAoB,UAAUE,0BAAwB,iCAC7D,UAACoR,EAAAA,EAAcA,CAAAA,CAACpG,OAAO,IAAClL,sBAAoB,iBAAiBE,0BAAwB,gCACnF,WAACoF,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUnB,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6CAA8C,CAACiJ,GAAW,yBAA0B3J,sBAAoB,SAASE,0BAAwB,iCAC/K,UAAC+H,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,eAAeC,sBAAoB,eAAeE,0BAAwB,yBACjGyJ,EAAUnC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAACmC,GAAW,YAGzC,UAAC4H,EAAAA,EAAcA,CAAAA,CAACxR,UAAU,aAAaqL,MAAM,QAAQpL,sBAAoB,iBAAiBE,0BAAwB,gCAChH,UAACG,EAAAA,CAAQA,CAAAA,CAACqB,KAAK,SAAS8P,SAAU7H,EAAS8H,SAAUY,EAAYX,SAAUC,GAAQA,EAAO,IAAIlK,KAAQmK,YAAY,IAAC5R,sBAAoB,WAAWE,0BAAwB,iCAKhL,WAAC8E,MAAAA,WACC,UAAC4L,EAAAA,EAASA,CAAAA,CAAC5Q,sBAAoB,YAAYE,0BAAwB,gCAAuB,SAC1F,WAAC8E,MAAAA,CAAIjF,UAAU,qBACb,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,mFAAmFC,sBAAoB,YAAYE,0BAAwB,yBAChK,UAACyF,EAAAA,CAAKA,CAAAA,CAACxB,KAAK,OAAO0B,MAAOyM,EAASxM,SAAUC,GAAKwM,EAAWxM,EAAEC,MAAM,CAACH,KAAK,EAAG9F,UAAU,QAAQC,sBAAoB,QAAQE,0BAAwB,kCAM1J,WAAC8E,MAAAA,CAAIjF,UAAU,mCACb,UAACuQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,WAAWC,OAAQ,CAAC,OAC3DC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,QACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAAAA,OAGhB,UAACC,EAAAA,EAAaA,CAAAA,UACX6H,EAAgBlJ,GAAG,CAAC6L,GAAU,UAACvK,EAAAA,EAAUA,CAAAA,CAAoBV,MAAOiL,CAArD3C,CAA4DtI,KAAK,UAC7E,WAACb,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,cAAeoQ,EAAOnO,KAAK,WAAImO,EAAOtO,KAAK,GAC9D,UAACwC,MAAAA,CAAIjF,UAAU,yCAAiC+Q,EAAOjH,WAAW,OAHxBiH,EAAOjL,KAAK,QAQhE,UAACkL,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,yBAEzE,UAACoQ,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,SAASC,OAAQ,CAAC,CACzDC,OAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAC,OACX,WAAC3K,EAAAA,EAAMA,CAAAA,CAACC,cAAewK,EAAM5K,QAAQ,CAAEkE,aAAc0G,EAAM7K,KAAK,WAC9D,UAACgL,EAAAA,EAAWA,CAAAA,UACV,UAAC1K,EAAAA,EAAaA,CAAAA,UACZ,UAACE,EAAAA,EAAWA,CAAAA,CAAAA,OAGhB,UAACC,EAAAA,EAAaA,CAAAA,UACX8H,EAAcnJ,GAAG,CAAC6L,GAAU,UAACvK,EAAAA,EAAUA,CAAAA,CAAoBV,KAA9CuI,CAAqD0C,EAAOjL,KAAK,UAC3E,WAACb,MAAAA,WACC,UAACA,MAAAA,CAAIjF,UAAU,uBAAe+Q,EAAOtO,KAAK,GAC1C,UAACwC,MAAAA,CAAIjF,UAAU,yCAAiC+Q,EAAOjH,WAAW,OAH1BiH,EAAOjL,KAAK,QAQ9D,UAACkL,EAAAA,EAAWA,CAAAA,CAAAA,MACD/Q,sBAAoB,YAAYE,0BAAwB,4BAI1D,cAAhBsS,GAA+B,UAAClC,EAAAA,EAASA,CAAAA,CAACC,QAAS1B,EAAK0B,OAAO,CAAEC,KAAK,kBAAkBC,OAAQ,CAAC,OAClGC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACL,UAACC,EAAAA,EAASA,CAAAA,UAAC,SACX,UAACC,EAAAA,EAAWA,CAAAA,UACV,UAACI,EAAAA,CAAQA,CAAAA,CAACrL,YAAY,iBAAiB7F,UAAU,eAAgB,GAAG2Q,CAAK,KAE3E,UAACM,EAAAA,EAAeA,CAAAA,UAAC,mBAGjB,UAACD,EAAAA,EAAWA,CAAAA,CAAAA,QAGpB,WAACc,EAAAA,EAAYA,CAAAA,CAAC7R,sBAAoB,eAAeE,0BAAwB,iCACvE,UAACoF,EAAAA,CAAMA,CAAAA,CAACnB,KAAK,SAASjD,QAAQ,UAAUqE,QAAS,IAAMqG,EAAa,IAAQ8F,SAAUpO,EAAStD,sBAAoB,SAASE,0BAAwB,gCAAuB,OAG3K,UAACoF,EAAAA,CAAMA,CAAAA,CAACnB,KAAK,SAASuN,SAAUpO,EAAStD,sBAAoB,SAASE,0BAAwB,gCAC3FoD,EAAU,SAAWsL,EAAY,KAAO,mBAOzD,sCC7YO,IAAM8D,GAAmB,CAE9B7L,YAAa,CACX8L,QAAS,IAUP,IAAMC,EATa,CACjB,aAAc,OACd,MAAS,OACT,oBAAqB,OACrB,kBAAmB,OACnB,uBAAwB,OACxB,kBAAmB,MACrB,CAE4B,CAAC/L,EAAYG,eAAe,CAAC,EAAI,OAC7D6L,EAAAA,KAAKA,CAACC,OAAO,CAAC,GAAGF,EAAU,KAAK,CAAC,CAAE,CACjC/I,YAAa,CAAC,IAAI,EAAEhD,EAAYS,KAAK,EAAE,CACvCyL,SAAU,GACZ,EACF,EAEAC,QAAS,IACPH,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAE,CACzBjJ,YAAa,CAAC,IAAI,EAAEhD,EAAYS,KAAK,EAAE,CACvCyL,SAAU,GACZ,EACF,EAEAE,QAAS,IACPJ,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,SAAS,CAAC,CAAE,CACzBjJ,YAAa,CAAC,KAAK,EAAEqJ,EAAAA,CAAkB,CACvCH,SAAU,GACZ,EACF,EAEAI,gBAAkBtM,IAChBgM,EAAAA,KAAKA,CAACO,IAAI,CAAC,CAAC,SAAS,CAAC,CAAE,CACtBvJ,YAAa,CAAC,MAAM,EAAEhD,EAAYS,KAAK,EAAE,CACzCyL,SAAU,GACZ,EACF,EAEAM,cAAe,CAACxM,EAAiCyM,EAAmBhK,KAClE,IAAMiK,EAAe,CACnB,KAAQ,KACR,cAAe,MACf,SAAY,MACZ,OAAU,KACZ,EAEAV,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBjJ,YAAa,GAAGhD,EAAYS,KAAK,CAAC,EAAE,EAAEiM,CAAY,CAACD,EAAuC,CAAC,GAAG,EAAEC,CAAY,CAACjK,EAAuC,EAAE,CACtJyJ,SAAU,GACZ,EACF,CACF,EAGA3J,KAAM,CACJuJ,QAAS,IAUP,IAAMC,EATa,CACjB,iBAAkB,OAClB,yBAA0B,OAC1B,qBAAsB,OACtB,oBAAqB,OACrB,wBAAyB,OACzB,yBAA0B,MAC5B,CAE4B,CAACxJ,EAAKI,QAAQ,CAAC,EAAI,KAC/CqJ,EAAAA,KAAKA,CAACC,OAAO,CAAC,GAAGF,EAAU,OAAO,CAAC,CAAE,CACnC/I,YAAa,CAAC,IAAI,EAAET,EAAK9B,KAAK,EAAE,CAChCyL,SAAU,GACZ,EACF,EAEAC,QAAS,IACPH,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBjJ,YAAa,CAAC,IAAI,EAAET,EAAK9B,KAAK,EAAE,CAChCyL,SAAU,GACZ,EACF,EAEAE,QAAS,IACPJ,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBjJ,YAAa,CAAC,KAAK,EAAE2J,EAAAA,CAAW,CAChCT,SAAU,GACZ,EACF,EAEAU,SAAU,CAACrK,EAAmBH,KAC5B,IAAMyK,EAAe,GAAGzK,EAAW1E,SAAS,EAAI,GAAG,CAAC,EAAE0E,EAAWzE,QAAQ,EAAI,IAAI,CAACC,IAAI,IAAMwE,EAAWvE,KAAK,CAC5GmO,EAAAA,KAAKA,CAACO,IAAI,CAAC,CAAC,KAAK,CAAC,CAAE,CAClBvJ,YAAa,GAAGT,EAAK9B,KAAK,CAAC,GAAG,EAAEoM,EAAAA,CAAc,CAC9CX,SAAU,GACZ,EACF,EAEAM,cAAe,CAACjK,EAAmBkK,EAAmBhK,KACpD,IAAMiK,EAAe,CACnB,QAAW,MACX,cAAe,MACf,UAAa,MACb,UAAa,KACf,EASAV,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAE,CACvBjJ,YAAa,GAAG8J,CAPhB,QAAW,IACX,cAAe,eACf,UAAa,IACb,UAAa,GACf,CAG8B,CAACrK,EAAuC,CAAC,CAAC,EAAEF,EAAK9B,KAAK,CAAC,EAAE,EAAEiM,CAAY,CAACD,EAAuC,CAAC,GAAG,EAAEC,CAAY,CAACjK,EAAuC,EAAE,CACvMyJ,SAAU,GACZ,EACF,EAEAa,UAAW,IACTf,EAAAA,KAAKA,CAACC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAE,CACxBjJ,YAAa,GAAGT,EAAK9B,KAAK,CAAC,OAAO,CAAC,CACnCyL,SAAU,GACZ,EACF,EAEAtJ,QAAUL,IACRyJ,EAAAA,KAAKA,CAACgB,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAE,CACxBhK,YAAa,GAAGT,EAAK9B,KAAK,CAAC,SAAS,EAAEE,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK2B,EAAKO,OAAO,IAAI,CAC9EoJ,SAAU,GACZ,EACF,EAEAe,SAAU,CAAC1K,EAAmB2K,KAC5B,IAAMC,EAAWD,EAAe,GAC5B,GAAGA,EAAa,IAAI,CAAC,CACrB,GAAGE,KAAKC,KAAK,CAACH,EAAe,IAAI,IAAI,CAAC,CAE1ClB,EAAAA,KAAKA,CAACO,IAAI,CAAC,CAAC,OAAO,CAAC,CAAE,CACpBvJ,YAAa,GAAGT,EAAK9B,KAAK,CAAC,IAAI,EAAE0M,EAAS,GAAG,CAAC,CAC9CjB,SAAU,GACZ,EACF,CACF,EA6FApD,MAAO,CACLwE,wBAAyB,IACvBtB,EAAAA,KAAKA,CAAClD,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAE,CACtB9F,YAAa8F,GAAS,aACtBoD,SAAU,GACZ,EACF,EAEAqB,iBAAkB,IAChBvB,EAAAA,KAAKA,CAAClD,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpB9F,YAAa8F,GAAS,aACtBoD,SAAU,GACZ,EACF,EAEAsB,aAAc,CAACC,EAAkB3E,KAM/BkD,EAAAA,KAAKA,CAAClD,KAAK,CAAC,GAAG4E,CAJb,YAAe,OACf,KAAQ,IACV,CAEyB,CAACD,EAAoC,EAAI,KAAK,IAAI,CAAC,CAAE,CAC5EzK,YAAa8F,GAAS,aACtBoD,SAAU,GACZ,EACF,EAEAyB,aAAc,CAACF,EAAkB3E,KAM/BkD,EAAAA,KAAKA,CAAClD,KAAK,CAAC,GAAG4E,CAJb,YAAe,OACf,KAAQ,IACV,CAEyB,CAACD,EAAoC,EAAI,KAAK,IAAI,CAAC,CAAE,CAC5EzK,YAAa8F,GAAS,aACtBoD,SAAU,GACZ,EACF,EAEA0B,WAAY,CAACC,EAAkB/E,KAO7BkD,EAAAA,KAAKA,CAAClD,KAAK,CAAC,GAAG4E,CALb,aAAgB,OAChB,MAAS,KACT,SAAY,KACd,CAEyB,CAACG,EAAoC,EAAI,KAAK,IAAI,CAAC,CAAE,CAC5E7K,YAAa8F,GAAS,aACtBoD,SAAU,GACZ,EACF,EAEA4B,iBAAmBnJ,IACjBqH,EAAAA,KAAKA,CAAClD,KAAK,CAAC,CAAC,IAAI,CAAC,CAAE,CAClB9F,YAAa,CAAC,SAAS,EAAE2B,EAAAA,CAAQ,CACjCuH,SAAU,GACZ,EACF,EAEA6B,aAAc,KACZ/B,EAAAA,KAAKA,CAAClD,KAAK,CAAC,CAAC,MAAM,CAAC,CAAE,CACpB9F,YAAa,aACbkJ,SAAU,GACZ,EACF,CACF,CAuBF,EAAE,SC/SsB8B,GAAkB,WACxCzR,CAAS,CACc,EACvB,IAAM0R,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClB,MACJC,CAAI,CACL,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACzK,EAAS0K,EAAW,CAAGvR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,MACjD,CAACN,EAAc8R,EAAgB,CAAGxR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAuB,EAAE,EACnE,CAAC2E,EAAO8M,EAAS,CAAGzR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,EAAE,EAC9C,CAACoI,EAAUsJ,EAAY,CAAG1R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAiB,EAAE,EACrD,CAACyO,EAAgBkD,EAAkB,CAAG3R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,EAAE,EACzD,CAACL,EAASiS,EAAW,CAAG5R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACuI,EAAWC,EAAa,CAAGxI,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,YAGrC,CAAC6R,EAAuBC,EAAyB,CAAG9R,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7D,CAAC+R,EAAgBC,EAAkB,CAAGhS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,CAACiS,EAAwBC,EAA0B,CAAGlS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACvE,CAACmS,EAAiBC,EAAmB,CAAGpS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzD,CAACqS,EAAoBC,EAAsB,CAAGtS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACtD,CAACuS,EAAaC,EAAe,CAAGxS,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACxC,CAACyS,EAAaC,EAAe,CAAG1S,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAqDzC2S,EAA0B,IAC9BT,EAA0B1R,GAAQ,IAClC8R,EAAsBM,QACtBd,EAAyB,GAC3B,EACMe,EAAmB,IACvBT,EAAmB5R,GAAQ,IAC3BgS,OAAeI,GACfZ,GAAkB,EACpB,EACMc,EAA0B,MAAOjK,IACrC6J,GAAe,GACf,GAAI,CACF,IAAMK,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CACxCC,QAAS5B,EAAMjN,EAAE,CACjBrD,MAAOsQ,EAAM6B,cAAc,CAAC,EAAE,EAAEC,cAAgB,GAChDvS,UAAWyQ,EAAMzQ,SAAS,EAAI,GAC9BC,SAAUwQ,EAAMxQ,QAAQ,EAAI,EAC9B,GACA,GAAIwR,EAAoB,CACtB,IAAMhD,EAAU,MAAM0D,EAAcK,wBAAwB,CAACf,EAAmBjO,EAAE,CAAEyE,GACpF2I,EAAgB6B,GAAQA,EAAK/R,GAAG,CAACsH,GAAQA,EAAKxE,EAAE,GAAKiO,EAAmBjO,EAAE,CAAGiL,EAAgCzG,IAC7GmG,GAAiB7L,WAAW,CAACmM,CAAbN,MAAoB,CAACM,EACvC,KAAO,CACL,IAAML,EAAU,MAAM+D,EAAcO,wBAAwB,CAACzK,GAC7D2I,EAAgB6B,GAAQ,CAACrE,KAAkCqE,EAAK,EAChEtE,GAAiB7L,WAAW,CAAC8L,CAAbD,MAAoB,CAACC,EACvC,CAGA,IAAMuE,EAAe,MAAMR,EAAcS,kBAAkB,CAAC/T,EAAW,CACrEgU,MAAO,GACT,GACA/B,EAAY6B,EAAaG,IAAI,EAAI,EAAE,CACrC,CAAE,MAAO1H,EAAO,CACdC,QAAQD,KAAK,CAAC,gCAAiCA,GAC/C+C,GAAiB/C,KAAK,CAACwE,OAAPzB,gBAA8B,CAAC/C,aAAiB2H,MAAQ3H,EAAM4H,OAAO,MAAGhB,EAC1F,QAAU,CACRF,GAAe,EACjB,CACF,EACMmB,EAAmB,MAAOhL,IAC9B6J,GAAe,GACf,GAAI,CACF,IAAMK,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CACxCC,QAAS5B,EAAMjN,EAAE,CACjBrD,MAAOsQ,EAAM6B,cAAc,CAAC,EAAE,EAAEC,cAAgB,GAChDvS,UAAWyQ,EAAMzQ,SAAS,EAAI,GAC9BC,SAAUwQ,EAAMxQ,QAAQ,EAAI,EAC9B,GACA,GAAI0R,EAAa,CACf,IAAMlD,EAAU,MAAM0D,EAAce,iBAAiB,CAACvB,EAAYnO,EAAE,CAAEyE,GACtE4I,EAAS4B,GAAQA,EAAK/R,GAAG,CAACsH,GAAQA,EAAKxE,EAAE,GAAKmO,EAAYnO,EAAE,CAAGiL,EAAUzG,IACzEmG,GAAiBtJ,IAAI,CAAC4J,OAAO,CAAbN,EAClB,KAAO,CACL,IAAMC,EAAU,MAAM+D,EAAcgB,iBAAiB,CAAClL,GACtD4I,EAAS4B,GAAQ,CAACrE,KAAYqE,EAAK,EACnCtE,GAAiBtJ,IAAI,CAACuJ,OAAO,CAAbD,EAClB,CAGA,IAAMwE,EAAe,MAAMR,EAAcS,kBAAkB,CAAC/T,EAAW,CACrEgU,MAAO,GACT,GACA/B,EAAY6B,EAAaG,IAAI,EAAI,EAAE,CACrC,CAAE,MAAO1H,EAAO,CACdC,QAAQD,KAAK,CAAC,yBAA0BA,GACxC+C,GAAiB/C,KAAK,CAACyE,OAAP1B,SAAuB,CAAC/C,aAAiB2H,MAAQ3H,EAAM4H,OAAO,MAAGhB,EACnF,QAAU,CACRF,EAAe,GACjB,CACF,EACMsB,EAA4B,KAEhC7C,EAAO8C,IAAI,CAAC,CAAC,sCAAsC,EAAExU,EAAAA,CAAW,CAClE,EACMyU,GAAoB,KAExB/C,EAAO8C,IAAI,CAAC,CAAC,6BAA6B,EAAExU,EAAAA,CAAW,CACzD,EACM0U,GAAyB,MAAOC,EAAgB7Q,KACpD,GAAI,CACF,IAAMwP,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAmBA,CAAC,CACxCC,QAAS5B,EAAMjN,EAAE,CACjBrD,MAAOsQ,EAAM6B,cAAc,CAAC,EAAE,EAAEC,cAAgB,GAChDvS,UAAWyQ,EAAMzQ,SAAS,EAAI,GAC9BC,SAAUwQ,EAAMxQ,QAAQ,EAAI,EAC9B,EACA,OAAMkS,EAAce,iBAAiB,CAACM,EAAQ,QAC5C7Q,CACF,GAGAkO,EAAS4B,GAAQA,EAAK/R,GAAG,CAACmE,GAAQA,EAAKrB,EAAE,GAAKgQ,EAAS,CACrD,GAAG3O,CAAI,CACPlC,QACF,EAAIkC,IACJ,IAAMA,EAAOd,EAAM0P,IAAI,CAACC,GAAKA,EAAElQ,EAAE,GAAKgQ,GAClC3O,GACFsJ,GADQ,IACa,CAACW,QAANX,KAAmB,CAACtJ,EAAMA,EAAKlC,MAAM,CAAEA,EAE3D,CAAE,MAAOyI,EAAO,CACdC,QAAQD,KAAK,CAAC,8BAA+BA,GAC7C+C,GAAiB/C,KAAK,CAAC0E,OAAP3B,KAAmB,CAAC,OAAQ/C,aAAiB2H,MAAQ3H,EAAM4H,OAAO,MAAGhB,EACvF,CACF,EACA,GAAIjT,EACF,MAAO,CADI,EACJ,OAAC0B,MAAAA,CAAIjF,UAAU,qBAClB,WAACiF,MAAAA,CAAIjF,UAAU,0BACb,UAACiF,MAAAA,CAAIjF,UAAU,uCACf,WAACiF,MAAAA,CAAIjF,UAAU,kDACb,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,UAACiF,MAAAA,CAAIjF,UAAU,6BACf,UAACiF,MAAAA,CAAIjF,UAAU,gCAEjB,WAACiF,MAAAA,CAAIjF,UAAU,sBACb,UAACiF,MAAAA,CAAIjF,UAAU,6BACf,UAACiF,MAAAA,CAAIjF,UAAU,wCAM3B,GAAI,CAACyK,EACH,MAAO,CADK,EACL,QAACxF,MAAAA,CAAIjF,UAAU,8BAClB,UAAC+C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,+CACpB,UAACmY,KAAAA,CAAGnY,UAAU,oCAA2B,UACzC,UAAC6G,IAAAA,CAAE7G,UAAU,sCAA6B,gBAC1C,WAACuF,EAAAA,CAAMA,CAAAA,CAACC,QAAS,IAAMuP,EAAOqD,IAAI,aAChC,UAACC,EAAAA,EAAaA,CAAAA,CAACrY,UAAU,gBAAgB,WAOjD,IAAMsY,GAAoBhV,EAAagC,MAAM,CACvCiT,GAAehQ,EAAMa,MAAM,CAACC,GAAQA,cAAKlC,MAAM,EAAgB7B,MAAM,CACrEkT,GAAejQ,EAAMa,MAAM,CAACC,GAAwB,cAAhBA,EAAKlC,MAAM,EAAoB,IAAIO,KAAK2B,EAAKO,OAAO,EAAI,IAAIlC,MAAQpC,MAAM,CAC9GmT,GAAkBnV,CAAY,CAAC,EAAE,CAEvC,CAFyC,KAElC,WAAC2B,MAAAA,CAAIjF,UAAU,GAFuD,SAE3CE,wBAAsB,oBAAoBC,0BAAwB,oCAEhG,WAAC8E,MAAAA,CAAIjF,UAAU,8CACb,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,WAACuF,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,QAAQsE,KAAK,KAAKD,QAAS,IAAMuP,EAAOqD,IAAI,GAAInY,sBAAoB,SAASE,0BAAwB,oCACnH,UAACkY,EAAAA,EAAaA,CAAAA,CAACrY,UAAU,cAAcC,sBAAoB,gBAAgBE,0BAAwB,4BAA4B,QAGjI,WAAC8E,MAAAA,WACC,UAACyT,KAAAA,CAAG1Y,UAAU,8BAAsByK,EAAQkB,QAAQ,GACpD,UAAC9E,IAAAA,CAAE7G,UAAU,iCAAwB,iBAGzC,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,UAACwK,EAAYA,CAACC,QAASA,CAAVD,CAAmBhH,oBAAqB+S,EAAyB/N,aAAciO,EAAkB/L,sBAAuBkN,EAA2BjN,cAAemN,GAAmBlN,OAAO,IAAC3K,sBAAoB,eAAeE,0BAAwB,4BACrQ,WAACoF,EAAAA,CAAMA,CAAAA,CAACpE,QAAQ,UAAUsE,KAAK,KAAKxF,sBAAoB,SAASE,0BAAwB,oCACvF,UAACkN,EAAAA,EAAQA,CAAAA,CAACrN,UAAU,cAAcC,sBAAoB,WAAWE,0BAAwB,4BAA4B,cAO3H,WAAC8E,MAAAA,CAAIjF,UAAU,kDAEb,UAACiF,MAAAA,CAAIjF,UAAU,yBACb,WAACgK,EAAAA,EAAIA,CAAAA,CAAClE,MAAOqG,EAAWhG,cAAeiG,EAAcpM,UAAU,SAASC,sBAAoB,OAAOE,0BAAwB,oCACzH,WAAC+J,EAAAA,EAAQA,CAAAA,CAAClK,UAAU,0BAA0BC,sBAAoB,WAAWE,0BAAwB,oCACnG,UAACgK,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,WAAW7F,sBAAoB,cAAcE,0BAAwB,mCAA0B,OAClH,WAACgK,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,eAAe7F,sBAAoB,cAAcE,0BAAwB,oCAA0B,SAC7GmY,GAAkB,OAE3B,WAACnO,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,QAAQ7F,sBAAoB,cAAcE,0BAAwB,oCAA0B,SACtGoI,EAAMjD,MAAM,CAAC,OAEtB,UAAC6E,EAAAA,EAAWA,CAAAA,CAACrE,MAAM,WAAW7F,sBAAoB,cAAcE,0BAAwB,mCAA0B,WAGpH,WAACiK,EAAAA,EAAWA,CAAAA,CAACtE,MAAM,WAAW9F,UAAU,YAAYC,sBAAoB,cAAcE,0BAAwB,oCAE5G,WAACyE,EAAAA,EAAIA,CAAAA,CAAC3E,sBAAoB,OAAOE,0BAAwB,oCACvD,UAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,mCACnE,WAAC2E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,oCACrG,UAAC4C,EAAAA,EAAQA,CAAAA,CAAC/C,UAAU,SAASC,sBAAoB,WAAWE,0BAAwB,4BAA4B,YAIpH,UAAC6E,EAAAA,EAAWA,CAAAA,CAAC/E,sBAAoB,cAAcE,0BAAwB,mCACrE,WAAC8E,MAAAA,CAAIjF,UAAU,mCACb,WAAC2Y,EAAAA,MAAMA,CAAAA,CAAC3Y,UAAU,UAAUC,sBAAoB,SAASE,0BAAwB,oCAC/E,UAACyY,EAAAA,WAAWA,CAAAA,CAACC,IAAK,iBAAOpO,EAAQqO,KAAK,CAAgBrO,EAAQqO,KAAK,EAAEC,SAAMvC,EAAWwC,IAAKvO,EAAQkB,QAAQ,CAAE1L,sBAAoB,cAAcE,0BAAwB,4BACvK,UAAC8Y,EAAAA,cAAcA,CAAAA,CAACjZ,UAAU,UAAUC,sBAAoB,iBAAiBE,0BAAwB,mCAC9FsK,EAAQkB,QAAQ,CAACH,KAAK,CAAC,EAAG,GAAG0N,WAAW,QAG7C,WAACjU,MAAAA,CAAIjF,UAAU,6BACb,WAACiF,MAAAA,CAAIjF,UAAU,kDACb,WAACiF,MAAAA,CAAIjF,UAAU,oCACb,UAAC2C,EAAAA,EAASA,CAAAA,CAAC3C,UAAU,+BAA+BC,sBAAoB,YAAYE,0BAAwB,4BAC5G,UAACqH,OAAAA,CAAKxH,UAAU,uBAAeyK,EAAQ0O,KAAK,MAE7C1O,EAAQ9F,KAAK,EAAI,WAACM,MAAAA,CAAIjF,UAAU,oCAC7B,UAAC6C,EAAAA,EAAQA,CAAAA,CAAC7C,UAAU,iCACpB,UAACwH,OAAAA,UAAMiD,EAAQ9F,KAAK,MAExB,WAACM,MAAAA,CAAIjF,UAAU,oCACb,UAACkI,EAAAA,EAAYA,CAAAA,CAAClI,UAAU,+BAA+BC,sBAAoB,eAAeE,0BAAwB,4BAClH,WAACqH,OAAAA,WAAK,SAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK+C,EAAQ2O,SAAS,SAEvD3O,EAAQ4O,SAAS,EAAI,WAACpU,MAAAA,CAAIjF,UAAU,oCACjC,UAAC+E,EAAAA,EAASA,CAAAA,CAAC/E,UAAU,iCACrB,WAACwH,OAAAA,WAAK,SAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,IAAIC,KAAK+C,EAAQ4O,SAAS,YAI3D5O,EAAQ6O,QAAQ,EAAI,WAACrU,MAAAA,CAAIjF,UAAU,oCAChC,UAACqF,EAAAA,CAAKA,CAAAA,CAAClE,QAASsJ,cAAQ6O,QAAQ,CAAiB,UAAY,qBACrC,YAArB7O,EAAQ6O,QAAQ,CAAiB,OAAS,SAE5C7O,EAAQtD,MAAM,EAAI,WAAC9B,EAAAA,CAAKA,CAAAA,CAAClE,QAAQ,oBACV,WAAnBsJ,EAAQtD,MAAM,EAAiB,KACZ,aAAnBsD,EAAQtD,MAAM,EAAmB,MACd,cAAnBsD,EAAQtD,MAAM,EAAoB,uBASnD,WAACvC,EAAAA,EAAIA,CAAAA,CAAC3E,sBAAoB,OAAOE,0BAAwB,oCACvD,UAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,mCACnE,WAAC2E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,oCACrG,UAACoZ,EAAAA,EAAYA,CAAAA,CAACvZ,UAAU,SAASC,sBAAoB,eAAeE,0BAAwB,4BAA4B,YAI5H,UAAC6E,EAAAA,EAAWA,CAAAA,CAAC/E,sBAAoB,cAAcE,0BAAwB,mCACrE,WAAC8E,MAAAA,CAAIjF,UAAU,kDACb,WAACiF,MAAAA,CAAIjF,UAAU,kDACb,UAACiF,MAAAA,CAAIjF,UAAU,4CAAoCsY,KACnD,UAACrT,MAAAA,CAAIjF,UAAU,yCAAgC,aAEjD,WAACiF,MAAAA,CAAIjF,UAAU,oDACb,UAACiF,MAAAA,CAAIjF,UAAU,8CAAsCuY,KACrD,UAACtT,MAAAA,CAAIjF,UAAU,yCAAgC,aAEjD,WAACiF,MAAAA,CAAIjF,UAAU,iDACb,UAACiF,MAAAA,CAAIjF,UAAU,2CAAmCwY,KAClD,UAACvT,MAAAA,CAAIjF,UAAU,yCAAgC,YAEjD,WAACiF,MAAAA,CAAIjF,UAAU,mDACb,UAACiF,MAAAA,CAAIjF,UAAU,6CACZyY,GAAkBtL,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAAC,IAAIzF,KAAK+Q,GAAgB9Q,SAAS,GAAK,MAE/E,UAAC1C,MAAAA,CAAIjF,UAAU,yCAAgC,uBAOzD,UAACoK,EAAAA,EAAWA,CAAAA,CAACtE,MAAM,eAAe7F,sBAAoB,cAAcE,0BAAwB,mCAC1F,UAACiD,EAAmBA,CAACC,UAAWA,EAAWC,IAAvBF,SAAqCE,EAAcE,oBAAqB+S,EAAyB9S,mBAAoBqD,GAAe+I,QAAQ2J,GAAG,CAAC,oBAAqB1S,GAAc7G,sBAAoB,sBAAsBE,0BAAwB,8BAG3Q,UAACiK,EAAAA,EAAWA,CAAAA,CAACtE,MAAM,QAAQ7F,sBAAoB,cAAcE,0BAAwB,mCACnF,UAACmI,EAAWA,CAACjF,QAADiF,EAAYjF,EAAWkF,MAAOA,EAAOC,aAAciO,EAAkBhO,YAAaY,GAAQwG,QAAQ2J,GAAG,CAAC,aAAcnQ,GAAOX,mBAAoBqP,GAAwBpP,SAAS,OAAO1I,sBAAoB,cAAcE,0BAAwB,8BAG/P,UAACiK,EAAAA,EAAWA,CAAAA,CAACtE,MAAM,WAAW7F,sBAAoB,cAAcE,0BAAwB,mCACtF,UAAC4L,EAAgBA,CAAC1I,UAAWA,EAAW2I,CAAvBD,QAAiCC,EAAUC,YAAaO,GAAQqD,QAAQ2J,GAAG,CAAC,sBAAuBhN,GAAON,WAAYM,GAAQqD,QAAQ2J,GAAG,CAAC,sBAAuBhN,GAAOvM,sBAAoB,mBAAmBE,0BAAwB,mCAM9P,WAAC8E,MAAAA,CAAIjF,UAAU,sBAEb,UAACwK,EAAYA,CAACC,QAASA,CAAVD,CAAmBhH,oBAAqB+S,EAAyB/N,aAAciO,EAAkB/L,sBAAuBkN,EAA2BjN,cAAemN,GAAmB7X,sBAAoB,eAAeE,0BAAwB,4BAG7P,WAACyE,EAAAA,EAAIA,CAAAA,CAAC3E,sBAAoB,OAAOE,0BAAwB,oCACvD,UAAC0E,EAAAA,EAAUA,CAAAA,CAAC5E,sBAAoB,aAAaE,0BAAwB,mCACnE,WAAC2E,EAAAA,EAASA,CAAAA,CAAC9E,UAAU,0BAA0BC,sBAAoB,YAAYE,0BAAwB,oCACrG,UAACsZ,EAAAA,EAAcA,CAAAA,CAACzZ,UAAU,SAASC,sBAAoB,iBAAiBE,0BAAwB,4BAA4B,YAIhI,UAAC6E,EAAAA,EAAWA,CAAAA,CAAC/E,sBAAoB,cAAcE,0BAAwB,mCACrE,WAAC8E,MAAAA,CAAIjF,UAAU,sBACZgM,EAASR,KAAK,CAAC,EAAG,GAAGtG,GAAG,CAACsH,GAAQ,WAACvH,MAAAA,CAAkBjF,UAAU,2CAC3D,UAACiF,MAAAA,CAAIjF,UAAU,uDACf,WAACiF,MAAAA,CAAIjF,UAAU,mBACb,UAAC6G,IAAAA,CAAE7G,UAAU,uBAAewM,EAAKjF,KAAK,GACtC,UAACV,IAAAA,CAAE7G,UAAU,yCACVmN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAAC,IAAIzF,KAAK8E,EAAK7E,SAAS,UALP6E,EAAKxE,EAAE,GAS9B,IAApBgE,EAAS1G,MAAM,EAAU,UAACuB,IAAAA,CAAE7G,UAAU,0DAAiD,yBAUlG,UAACsO,EAAqBA,CAAC1C,KAAM6J,EAAuB5J,WAA9ByC,EAA4CoH,EAA0BrS,UAAWA,EAAWkL,YAAa9D,EAAQkB,QAAQ,CAAE7E,YAAamP,EAAoBzH,YAAaqH,EAAwBpH,SAAUiI,EAAyBnT,QAAS8S,EAAapW,sBAAoB,wBAAwBE,0BAAwB,4BAEpV,UAACgS,EAAcA,CAACvG,KAAM+J,EAAgB9J,IAAvBsG,SAAqCyD,EAAmBvS,UAAWA,EAAWkL,YAAa9D,EAAQkB,QAAQ,CAAEtC,KAAM8M,EAAa3H,YAAauH,EAAiB1D,eAAgBA,EAAgB5D,SAAUgJ,EAAkBlU,QAAS8S,EAAapW,sBAAoB,iBAAiBE,0BAAwB,8BAElU,yBClaA,6GCAA,oDCAA,qGCAA,mECAA,0GCAA,oDCAA,iDCAA,oDCAA,kDCAA,+CCAA,wGCAA,iECAA,kDCAA,iECAA,uDCAA,sDCAA,uDCAA,wDCAA,qDCAA,sECAA,oDCAA,iECAA,0DCAA,kMCMA,SAAS6P,EAAO,CACd,GAAGvP,EAC+C,EAClD,MAAO,UAACiZ,EAAAA,EAAoB,EAACC,YAAU,SAAU,GAAGlZ,CAAK,CAAER,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASyZ,EAAc,CACrB,GAAGnZ,EACkD,EACrD,MAAO,UAACiZ,EAAAA,EAAuB,EAACC,YAAU,iBAAkB,GAAGlZ,CAAK,CAAER,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACpL,CACA,SAAS0Z,EAAa,CACpB,GAAGpZ,EACiD,EACpD,MAAO,UAACiZ,EAAAA,EAAsB,EAACC,YAAU,gBAAiB,GAAGlZ,CAAK,CAAER,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,cAChL,CAMA,SAAS2Z,EAAc,WACrB9Z,CAAS,CACT,GAAGS,EACkD,EACrD,MAAO,UAACiZ,EAAAA,EAAuB,EAACC,YAAU,iBAAiB3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JX,GAAa,GAAGS,CAAK,CAAER,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACxW,CACA,SAAS8P,EAAc,CACrBjQ,WAAS,UACT+Z,CAAQ,CACR,GAAGtZ,EACkD,EACrD,MAAO,WAACoZ,EAAAA,CAAaF,YAAU,gBAAgB1Z,sBAAoB,eAAeC,wBAAsB,gBAAgBC,0BAAwB,uBAC5I,UAAC2Z,EAAAA,CAAc7Z,sBAAoB,gBAAgBE,0BAAwB,eAC3E,WAACuZ,EAAAA,EAAuB,EAACC,YAAU,iBAAiB3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+WX,GAAa,GAAGS,CAAK,CAAER,sBAAoB,0BAA0BE,0BAAwB,uBAC3gB4Z,EACD,WAACL,EAAAA,EAAqB,EAAC1Z,UAAU,oWAAoWC,sBAAoB,wBAAwBE,0BAAwB,uBACvc,UAAC6Z,EAAAA,CAAKA,CAAAA,CAAC/Z,sBAAoB,QAAQE,0BAAwB,eAC3D,UAACqH,OAAAA,CAAKxH,UAAU,mBAAU,kBAIpC,CACA,SAASkQ,EAAa,WACpBlQ,CAAS,CACT,GAAGS,EACyB,EAC5B,MAAO,UAACwE,MAAAA,CAAI0U,YAAU,gBAAgB3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgDX,GAAa,GAAGS,CAAK,CAAEP,wBAAsB,eAAeC,0BAAwB,cAC1L,CACA,SAAS2R,EAAa,WACpB9R,CAAS,CACT,GAAGS,EACyB,EAC5B,MAAO,UAACwE,MAAAA,CAAI0U,YAAU,gBAAgB3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0DX,GAAa,GAAGS,CAAK,CAAEP,wBAAsB,eAAeC,0BAAwB,cACpM,CACA,SAASgQ,EAAY,WACnBnQ,CAAS,CACT,GAAGS,EACgD,EACnD,MAAO,UAACiZ,EAAAA,EAAqB,EAACC,YAAU,eAAe3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCX,GAAa,GAAGS,CAAK,CAAER,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5O,CACA,SAASiQ,EAAkB,CACzBpQ,WAAS,CACT,GAAGS,EACsD,EACzD,MAAO,UAACiZ,EAAAA,EAA2B,EAACC,YAAU,qBAAqB3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCX,GAAa,GAAGS,CAAK,CAAER,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,cAC/P,oCEpDI,sBAAsB,mMDdb8Z,EAAW,CACtB1S,KADsB,CACf,4BACT,EAMe,eAAe2S,EAAkBzZ,CAAgB,EAC9D,IAAM0Z,EAAS,IAATA,EAAe1Z,EAAM0Z,GAAN1Z,GAAY,CAMjC,OAHI,EAAQuH,EAAE,EAAFA,KAA2B,GAArBmS,EAAOnS,EAAE,EACzBoS,CAAAA,EAAAA,EAAAA,QAAAA,CAAAA,EAAAA,CAEKC,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAcC,UAAU,IAACta,qBAAoB,iBAAgBC,uBAAsB,qBAAoBC,yBAAwB,YACnI,SAAAka,CAAAA,EAAAA,EAAAA,GAAAA,CAACpV,CAAAA,KAAAA,CAAAA,CAAIjF,SAAU,oBACb,SAAAqa,CAAAA,EAAAA,EAAAA,GAAAA,CAACG,CAAAA,EAAAA,QAAAA,CAAAA,CAASC,QAAAA,CAAUJ,CAAAA,EAAAA,EAAAA,GAAAA,CAACK,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAqBza,qBAAoB,YAAWE,yBAAwB,YAC/F,SAAAka,CAAAA,EAAAA,EAAAA,GAAAA,CAACvF,CAAAA,EAAAA,OAAAA,CAAAA,CAAkBzR,SAAAA,CAAW8W,EAAOnS,EAAE,CAAE/H,qBAAoB,qBAAoBE,yBAAwB,mBAInH,CCnBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZwa,EAO8B,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,EAPC,CAQV,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAClC,CACrC,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,0BAA0B,CAC1C,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,EAOF,OAEE,EAA2B,CAlBN,IASL,iBASQ,ylBChF9B,qPCgBM,EAAgB,WAGhB,CAAC,EAAuB,EAAmB,CAAI,OAAkB,CAAC,GASlE,CAAC,EAAkB,EAAkB,CACzC,EAA4C,EAVuC,CAqB/E,EAAiB,QAZoB,IAYpB,CACrB,CAAC,EAAmC,KAClC,GAAM,iBACJ,OACA,EACA,QAAS,EACT,iBACA,oBACA,QACA,EAAQ,qBACR,OACA,EACA,GAAG,EACL,CAAI,EACE,CAAC,EAAQ,EAAS,CAAU,MAAV,IAAU,CAAmC,IAAI,EACnE,EAAe,OAAe,CAAC,EAAc,GAAU,EAAU,IACjE,CADsE,CAC7B,UAAO,GAEhD,EAFqD,CAErC,GAAS,GAAQ,CAAC,CAAC,EAAO,QAAQ,MAAM,EACxD,CAAC,CAD2D,EACjD,EAAO,EAAU,CAAI,OAAoB,CAAC,CACzD,KAAM,EACN,YAAa,EACb,SAAU,CACZ,CAAC,EACK,EAA+B,SAAO,GAU5C,IAVmD,GAC7C,YAAU,KACd,IAAM7L,EAAO,GAAQ,KACrB,GAAIA,EAAM,CACR,IAAM,EAAQ,IAAM,EAAW,EAAuB,OAAO,EAE7D,OADAA,EAAK,iBAAiB,QAAS,GACxB,EAD6B,EACvBA,EAAK,oBAAoB,QAAS,EACjD,CACF,EAFwD,CAEpD,EAAQ,EAAW,EAGrB,MAHoB,GAGpB,EAAC,GAAiB,MAAO,EAAiB,MAAO,EAAS,WACxD,oBAAC,IAAS,CAAC,OAAV,CACC,KAAK,SACL,KAAK,WACL,eAAc,EAAgB,GAAW,IAAJ,IAAc,EACnD,gBAAe,EACf,aAAY,EAAS,GACrB,IAD4B,YACb,EAAW,GAAK,gBAC/B,QACA,EACC,GAAG,EACJ,IAAK,EACL,UAAW,OAAoB,CAAC,EAAM,UAAW,IAE7B,QAAS,EAAvB,EAAM,KAAiB,EAAM,eAAe,CAClD,CAAC,EACD,QAAS,OAAoB,CAAC,EAAM,QAAS,IAC3C,EAAW,KAAkB,EAAgB,IAAsB,CAAC,GAChE,GADoD,CAEtD,EAAiC,CAFyB,CAAoB,KAE7C,CAAU,CAD1B,CACgC,qBAAqB,EAIlE,EAAkC,QAAS,GAAM,gBAAgB,EAEzE,CAAC,IAEF,GACC,UAAC,GACC,CADF,OACW,EACT,QAAS,CAAC,EAAiC,QAC3C,aACA,UACA,WACA,WACA,OACA,EAIA,MAAO,CAAE,UAAW,mBAAoB,EACxC,gBAAgB,EAAgB,IAA0B,IAC5D,CAEJ,CAEJ,GAGF,CAR0D,CAQjD,GARqD,QAQrD,CAAc,EAMvB,IAAM,EAAiB,oBAYjB,EAA0B,aAC9B,CAAC,EAA4C,KAC3C,GAAM,iBAAE,aAAiB,EAAY,GAAG,EAAe,CAAI,EACrD,EAAU,EAAmB,EAAgB,GACnD,CAFuD,KAGrD,MAFgE,EAEhE,EAAC,GAAQ,CAAR,CAAS,QAAS,GAAc,EAAgB,EAAQ,KAAK,IAAuB,IAAlB,EAAQ,MACzE,mBAAC,IAAS,CAAC,KAAV,CACC,aAAY,EAAS,EAAQ,KAAK,EAClC,gBAAe,EAAQ,SAAW,GAAK,OACtC,GAAG,EACJ,IAAK,EACL,MAAO,CAAE,cAAe,OAAQ,GAAG,EAAM,KAAM,GACjD,CACF,CAEJ,GAGF,EAAkB,YAAc,EAWhC,IAAM,EAAc,IAClB,GAAM,SAAE,UAAS,UAAS,GAAU,iBAAM,EAAgB,GAAG,EAAW,CAAI,EACtE,EAAY,IADsD,IACtD,CAAyB,IAAI,EACzC,EAAc,OAAW,CAAC,GAC1B,EAAc,EADmB,CACnB,IAAO,CAAC,GAGtB,IAH6B,OAG7B,CAAU,KACd,IAAM,EAAQ,EAAI,QAGZ,EADa,OAAO,yBAAyB,OADzB,iBAAiB,UACoB,SAAS,EAC1C,IAE9B,GAAI,IAAgB,GAAW,EAAY,CACzC,IAAM,EAAQ,IAAI,MAAM,QAAS,SAAE,CAAQ,CAAC,EAC5C,EAAM,cAAgB,EAAgB,GACtC,EAAW,EADkC,EAClC,CAAK,GAAO,EAAgB,IAAmB,GAC1D,EAAM,EAD4C,WAC5C,CAAc,EACtB,CACF,EAF6B,CAEzB,EAAa,EAAS,EAAQ,EAElC,GAFiC,CAE3B,EAA0B,UAAO,EAAgB,IAAmB,GAAZ,IAAI,EAEhE,UAAC,SACC,KAAK,WACL,eAAW,EACX,eAAgB,GAAkB,EAAkB,QACnD,GAAG,EACJ,SAAU,OACV,EACA,MAAO,CACL,GAAG,EAAM,MACT,GAAG,EACH,SAAU,WACV,cAAe,OACf,QAAS,EACT,OAAQ,CACV,GAGN,EAEA,SAAS,EAAgB,GAAoD,MACpE,mBACT,CAEA,SAAS,EAAS,GAAuB,OAChC,EAAgB,GAAW,IAAJ,YAAsB,EAAU,UAAY,WAC5E,2BCrNA,SAASqC,EAAS,WAChBnR,CAAS,CACT,EAFemR,CAEZ1Q,EACiD,EACpD,MAAO,UAACma,EAAsB,CAACjB,CAAD,WAAW,WAAW3Z,UAAWW,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8eAA+eX,GAAa,GAAGS,CAAK,CAAER,sBAAoB,yBAAyBC,wBAAsB,WAAWC,0BAAwB,wBAC1qB,UAACya,EAA2B,CAACjB,MAAD,MAAW,qBAAqB3Z,UAAU,gEAAgEC,sBAAoB,8BAA8BE,0BAAwB,wBAC9M,UAAC0a,EAAAA,CAASA,CAAAA,CAAC7a,UAAU,WAAWC,sBAAoB,YAAYE,0BAAwB,oBAGhG,mBCfA,sCAA4K,CAE5K,uCAAiK,sTCajK,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,WACA,CACA,UACA,OACA,CACA,uBAAiC,EACjC,MAvBA,IAAoB,uCAA0I,CAuB9J,yGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CAGA,EACA,CACO,CACP,CACA,QAzCA,IAAsB,uCAA4H,CAyClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EAEA,CAAO,CACP,CACA,QA1DA,IAAsB,uCAAiH,CA0DvI,gFACA,gBA1DA,IAAsB,uCAAuH,CA0D7I,sFACA,aA1DA,IAAsB,sCAAoH,CA0D1I,mFACA,WA1DA,IAAsB,4CAAgF,CA0DtG,+CACA,cA1DA,IAAsB,4CAAmF,CA0DzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,4GAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,qCACA,oCAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,yBCrGD,sDCAA,4DCAA,kDCAA,wDCAA,gECAA,wDCAA,sDCAA,yDCAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,qDCAA,uCAA4K,CAE5K,uCAAiK,yBCFjK,0ECaO,OAAM2a,EAGXC,YAAY9F,CAAuB,CAAE,CACnC,IAAI,CAACA,IAAI,CAAGA,CACd,CAOA,MAAc+F,YACZC,CAAgB,CAChBC,EAAiC,CAAC,CAAC,CACvB,CACZ,IAKInC,EACAoC,EANE,QAAEC,EAAS,KAAK,CAAEC,MAAI,QAAElB,CAAM,CAAE,CAAGe,EAavC,GAFAnC,EAAM,GAAGuC,WAAW,IAAI,UAAEL,GAAU,CAEhCd,EAAQ,CACV,IAAMoB,EAAe,IAAIC,gBACzB/U,OAAOC,OAAO,CAACyT,GAAQsB,OAAO,CAAC,CAAC,CAACC,EAAK5V,EAAM,UACtCA,GACFyV,EAAaI,KADDnF,CACO,CAACkF,EAAK5V,EAAM8V,OADN9V,CACc,GAE3C,GACIyV,EAAaK,CAJsB,MAAM,CAIpB,IAAI,IACpB,CAAC,CAAC,EAAEL,EAAaK,QAAQ,KAEpC,CAGAT,EAAiB,QACfC,EACAS,QAAS,CACP,eAAgB,mBAChB,kBAAmB,IAAI,CAAC5G,IAAI,CAAC4B,OAAO,CACpC,eAAgB,IAAI,CAAC5B,IAAI,CAACtQ,KAAK,CAEnC,EAwBE0W,GAAmB,OAAO,CAAlBD,GACVD,GAAeE,IAAI,CAAGS,KAAKC,SAAS,CAACV,EAAAA,EAGvC,GAAI,CACF,IAAMW,EAAW,MAAMC,MAAMlD,EAAKoC,GAElC,GAAI,CAACa,EAASE,EAAE,CAAE,CAChB,IAAMC,EAAY,MAAMH,EAASI,IAAI,EACrC,OAAU7E,MACR,CAAC,oBAAoB,EAAEyE,EAAS7U,MAAM,CAAC,CAAC,EAAE6U,EAASK,UAAU,CAAC,GAAG,EAAEF,EAAAA,CAAW,CAElF,CAEA,OAAO,MAAMH,EAASM,IAAI,EAC5B,CAAE,MAAO1M,EAAO,CAEd,MADAC,QAAQD,KAAK,CAAC,CAAC,uBAAuB,EAAEqL,EAAS,CAAC,CAAC,CAAErL,GAC/CA,CACR,CACF,CAKA,MAAM2M,gBAAgBpC,CAIrB,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,gBAAiB,CACvCb,OAAQ,CACNqC,MAAO,IACP,GAAGrC,CAAM,CAEb,EACF,CAEA,MAAMsC,eAAezU,CAAU,CAAE,CAC/B,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,cAAc,EAAEhT,EAAAA,CAAI,CAAE,CAC7CmS,OAAQ,CAAEqC,MAAO,GAAI,CACvB,EACF,CAEA,MAAME,kBAAkBjQ,CAAS,CAAE,CACjC,OAAO,IAAI,CAACuO,WAAW,CAAC,gBAAiB,CACvCI,OAAQ,OACRC,KAAM5O,CACR,EACF,CAEA,MAAMkQ,kBAAkB3U,CAAU,CAAEyE,CAAS,CAAE,CAC7C,OAAO,IAAI,CAACuO,WAAW,CAAC,CAAC,cAAc,EAAEhT,EAAAA,CAAI,CAAE,CAC7CoT,OAAQ,QACRC,KAAM5O,CACR,EACF,CAEA,MAAMmQ,kBAAkB5U,CAAU,CAAE,CAClC,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,cAAc,EAAEhT,EAAAA,CAAI,CAAE,CAC7CoT,OAAQ,QACV,EACF,CAKA,MAAMyB,YAAY1C,CAIjB,CAAE,CACD,IAAM2C,EAAmB,CAAEN,MAAO,IAAK,GAAGrC,CAAM,EAUhD,OAPIA,GAAQ4C,QAAQ,CAClBD,CAAW,CAAC,mCAAmC,CAAG3C,EAAO4C,MAAM,CAC/DD,CAAW,CAAC,gCAAgC,CAAG3C,EAAO4C,MAAM,CAC5DD,CAAW,CAAC,gCAAgC,CAAG3C,EAAO4C,MAAM,CAC5D,OAAOD,EAAYC,MAAM,EAAE,IAGlB,CAAC/B,WAAW,CAAC,WAHiC,CAGpB,CAAEb,OAAQ2C,CAAY,EAC7D,CAEA,MAAME,WAAWhV,CAAU,CAAE,CAC3B,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,UAAU,EAAEhT,EAAAA,CAAI,CAAE,CACzCmS,OAAQ,CAAEqC,MAAO,GAAI,CACvB,EACF,CAEA,MAAMS,cAAcxQ,CAAS,CAAE,CAC7B,OAAO,IAAI,CAACuO,WAAW,CAAC,YAAa,CACnCI,OAAQ,OACRC,KAAM5O,CACR,EACF,CAEA,MAAMyQ,cAAclV,CAAU,CAAEyE,CAAS,CAAE,CACzC,OAAO,IAAI,CAACuO,WAAW,CAAC,CAAC,UAAU,EAAEhT,EAAAA,CAAI,CAAE,CACzCoT,OAAQ,QACRC,KAAM5O,CACR,EACF,CAEA,MAAM0Q,cAAcnV,CAAU,CAAE,CAC9B,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,UAAU,EAAEhT,EAAAA,CAAI,CAAE,CACzCoT,OAAQ,QACV,EACF,CAKA,MAAMgC,cAAcjD,CAGnB,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,cAAe,CACrCb,OAAQ,CAAEqC,MAAO,IAAK,GAAGrC,CAAM,CACjC,EACF,CAEA,MAAMkD,aAAarV,CAAU,CAAE,CAC7B,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,YAAY,EAAEhT,EAAAA,CAAI,CAC7C,CAEA,MAAMsV,gBAAgB7Q,CAAS,CAAE,CAC/B,OAAO,IAAI,CAACuO,WAAW,CAAC,cAAe,CACrCI,OAAQ,OACRC,KAAM5O,CACR,EACF,CAEA,MAAM8Q,gBAAgBvV,CAAU,CAAEyE,CAAS,CAAE,CAC3C,OAAO,IAAI,CAACuO,WAAW,CAAC,CAAC,YAAY,EAAEhT,EAAAA,CAAI,CAAE,CAC3CoT,OAAQ,QACRC,KAAM5O,CACR,EACF,CAEA,MAAM+Q,gBAAgBxV,CAAU,CAAE,CAChC,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,YAAY,EAAEhT,EAAAA,CAAI,CAAE,CAC3CoT,OAAQ,QACV,EACF,CAKA,MAAMqC,SAAStD,CAGd,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,SAAU,CAChCb,OAAQ,CAAEqC,MAAO,IAAK,GAAGrC,CAAM,CACjC,EACF,CAEA,MAAMuD,WAAWC,CAAc,CAAElR,CAAS,CAAE,CAC1C,OAAO,IAAI,CAACuO,WAAW,CAAC,CAAC,OAAO,EAAE2C,EAAAA,CAAQ,CAAE,CAC1CvC,OAAQ,QACRC,KAAM5O,CACR,EACF,CAEA,MAAMmR,iBAAkB,CAEtB,OAAO,IAAI,CAAC5C,WAAW,CAAC,cAAe,CACrCI,OAAQ,OACRC,KAAM,CACJxE,QAAS,IAAI,CAAC5B,IAAI,CAAC4B,OAAO,CAC1BlS,MAAO,IAAI,CAACsQ,IAAI,CAACtQ,KAAK,CACtBH,UAAW,IAAI,CAACyQ,IAAI,CAACzQ,SAAS,CAC9BC,SAAU,IAAI,CAACwQ,IAAI,CAACxQ,QAAQ,CAEhC,EACF,CAEA,MAAMoZ,SAASC,CAKd,CAAE,CAED,GAAI,CAEF,IAAMC,EAAgB,MAAM,IAAI,CAAC/C,WAAW,CAAM,SAAU,CAC1Db,OAAQ,CACN6D,MAAOlC,KAAKC,SAAS,CAAC,CACpBlF,QAAS,CAAEoH,OAAQH,EAASjH,OAAO,CACrC,GACAQ,MAAO,CACT,CACF,GAEA,IAAI0G,EAAczG,IAAI,GAAIyG,GAAczG,IAAI,CAAChS,MAAM,EAAG,EA0BpD,OAXgB,MAAM,IAAI,CAAC0V,WAAW,CAAM,SAAU,CACpDI,OAAQ,OACRC,KAAM,CACJ1W,MAAOmZ,EAASnZ,KAAK,CACrBkS,QAASiH,EAASjH,OAAO,CACzBrS,UAAWsZ,EAAStZ,SAAS,CAC7BC,SAAUqZ,EAASrZ,QAAQ,CAC3BiO,KAAM,aACNwL,UAAW,IAAIxW,OAAOgI,WAAW,EACnC,CACF,EAzBuD,EAEvD,IAAMyO,EAAeJ,EAAczG,IAAI,CAAC,EAAE,CAU1C,OAAO8G,MATmB,IAAI,CAACpD,WAAW,CAAM,CAAC,OAAO,EAAEmD,EAAanW,EAAE,EAAE,CAAE,CAC3EoT,OAAQ,QACRC,KAAM,CACJ1W,MAAOmZ,EAASnZ,KAAK,CACrBH,UAAWsZ,EAAStZ,SAAS,CAC7BC,SAAUqZ,EAASrZ,QAAQ,CAC3ByZ,UAAW,IAAIxW,OAAOgI,WAAW,EACnC,CACF,EAEF,CAeF,CAAE,KAfO,CAeAE,EAAO,CAGd,OAFAC,QAAQD,KAAK,CAAC,mCAAoCA,GAE3C,CACL5H,GAAI,UACJrD,MAAOmZ,EAASnZ,KAAK,CACrBkS,QAASiH,EAASjH,OAAO,CACzBnE,KAAM,aACNlO,UAAWsZ,EAAStZ,SAAS,CAC7BC,SAAUqZ,EAASrZ,QAAQ,CAE/B,CACF,CAKA,MAAM4Z,uBAAuBlE,CAK5B,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,wBAAyB,CAC/Cb,OAAQ,CACNqC,MAAO,IACP,GAAGrC,CAAM,CAEb,EACF,CAEA,MAAMmE,sBAAsBtW,CAAU,CAAE,CACtC,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,sBAAsB,EAAEhT,EAAAA,CAAI,CAAE,CACrDmS,OAAQ,CAAEqC,MAAO,GAAI,CACvB,EACF,CAEA,MAAMtF,yBAAyBzK,CAAS,CAAE,CACxC,OAAO,IAAI,CAACuO,WAAW,CAAC,wBAAyB,CAC/CI,OAAQ,OACRC,KAAM5O,CACR,EACF,CAEA,MAAMuK,yBAAyBhP,CAAU,CAAEyE,CAAS,CAAE,CACpD,OAAO,IAAI,CAACuO,WAAW,CAAC,CAAC,sBAAsB,EAAEhT,EAAAA,CAAI,CAAE,CACrDoT,OAAQ,QACRC,KAAM5O,CACR,EACF,CAEA,MAAM8R,yBAAyBvW,CAAU,CAAE,CACzC,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,sBAAsB,EAAEhT,EAAAA,CAAI,CAAE,CACrDoT,OAAQ,QACV,EACF,CAKA,MAAMoD,gBAAgBrE,CAKrB,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,iBAAkB,CACxCb,OAAQ,CACNqC,MAAO,IACP,GAAGrC,CAAM,CAEb,EACF,CAEA,MAAMsE,eAAezW,CAAU,CAAE,CAC/B,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,eAAe,EAAEhT,EAAAA,CAAI,CAAE,CAC9CmS,OAAQ,CAAEqC,MAAO,GAAI,CACvB,EACF,CAEA,MAAM7E,kBAAkBlL,CAAS,CAAE,CACjC,OAAO,IAAI,CAACuO,WAAW,CAAC,iBAAkB,CACxCI,OAAQ,OACRC,KAAM5O,CACR,EACF,CAEA,MAAMiL,kBAAkB1P,CAAU,CAAEyE,CAAS,CAAE,CAC7C,OAAO,IAAI,CAACuO,WAAW,CAAC,CAAC,eAAe,EAAEhT,EAAAA,CAAI,CAAE,CAC9CoT,OAAQ,QACRC,KAAM5O,CACR,EACF,CAEA,MAAMiS,kBAAkB1W,CAAU,CAAE,CAClC,OAAO,IAAI,CAACgT,WAAW,CAAC,CAAC,eAAe,EAAEhT,EAAAA,CAAI,CAAE,CAC9CoT,OAAQ,QACV,EACF,CAKA,MAAMuD,gCAAgCtb,CAAiB,CAAE8W,CAMxD,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,CAAC,UAAU,EAAE3X,EAAU,aAAa,CAAC,CAAE,CAC7D8W,OAAQ,CACNqC,MAAO,IACP,GAAGrC,CAAM,CAEb,EACF,CAEA,MAAMyE,yBAAyBvb,CAAiB,CAAE8W,CAOjD,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,CAAC,UAAU,EAAE3X,EAAU,MAAM,CAAC,CAAE,CACtD8W,OAAQ,CACNqC,MAAO,IACP,GAAGrC,CAAM,CAEb,EACF,CAEA,MAAM/C,mBAAmB/T,CAAiB,CAAE8W,CAI3C,CAAE,CACD,OAAO,IAAI,CAACa,WAAW,CAAC,CAAC,UAAU,EAAE3X,EAAU,SAAS,CAAC,CAAE,CACzD8W,OAAQ,CACNqC,MAAO,IACP,GAAGrC,CAAM,CAEb,EACF,CACF,CAKO,SAASvD,EAAoB3B,CAAuB,EACzD,OAAO,IAAI6F,EAAc7F,EAC3B", "sources": ["webpack://next-shadcn-dashboard-starter/./src/components/ui/calendar.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/crm/interaction-timeline.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/crm/task-manager.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/crm/quick-actions.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/crm/communication-log.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/crm/interaction-form-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/crm/task-form-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/lib/crm-notifications.ts", "webpack://next-shadcn-dashboard-starter/./src/components/patients/patient-detail-view.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/dialog.tsx", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/patients/[id]/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/../src/checkbox.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/checkbox.tsx", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/?bfb7", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/?c8e3", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\"", "webpack://next-shadcn-dashboard-starter/./src/lib/payload-client.ts"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { DayPicker } from 'react-day-picker';\nimport type { ComponentProps } from 'react';\nimport { cn } from '@/lib/utils';\nimport { buttonVariants } from '@/components/ui/button';\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons';\n\n// Custom icons that meet the DayPicker requirements\nconst LeftIcon = () => <ChevronLeftIcon className='size-4' data-sentry-element=\"ChevronLeftIcon\" data-sentry-component=\"LeftIcon\" data-sentry-source-file=\"calendar.tsx\" />;\nconst RightIcon = () => <ChevronRightIcon className='size-4' data-sentry-element=\"ChevronRightIcon\" data-sentry-component=\"RightIcon\" data-sentry-source-file=\"calendar.tsx\" />;\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: ComponentProps<typeof DayPicker>) {\n  return <DayPicker showOutsideDays={showOutsideDays} className={cn('p-3', className)} classNames={{\n    months: 'flex flex-col sm:flex-row gap-2',\n    month: 'flex flex-col gap-4',\n    caption: 'flex justify-center pt-1 relative items-center w-full',\n    caption_label: 'text-sm font-medium',\n    nav: 'flex items-center gap-1',\n    nav_button: cn(buttonVariants({\n      variant: 'outline'\n    }), 'size-7 bg-transparent p-0 opacity-50 hover:opacity-100'),\n    nav_button_previous: 'absolute left-1',\n    nav_button_next: 'absolute right-1',\n    table: 'w-full border-collapse space-x-1',\n    head_row: 'flex',\n    head_cell: 'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]',\n    row: 'flex w-full mt-2',\n    cell: cn('relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md', props.mode === 'range' ? '[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md' : '[&:has([aria-selected])]:rounded-md'),\n    day: cn(buttonVariants({\n      variant: 'ghost'\n    }), 'size-8 p-0 font-normal aria-selected:opacity-100'),\n    day_range_start: 'day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground',\n    day_range_end: 'day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground',\n    day_selected: 'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\n    day_today: 'bg-accent text-accent-foreground',\n    day_outside: 'day-outside text-muted-foreground aria-selected:text-muted-foreground',\n    day_disabled: 'text-muted-foreground opacity-50',\n    day_range_middle: 'aria-selected:bg-accent aria-selected:text-accent-foreground',\n    day_hidden: 'invisible',\n    ...classNames\n  }} components={{\n    IconLeft: LeftIcon,\n    IconRight: RightIcon\n  }} {...props} data-sentry-element=\"DayPicker\" data-sentry-component=\"Calendar\" data-sentry-source-file=\"calendar.tsx\" />;\n}\nexport { Calendar };", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Separator } from '@/components/ui/separator';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { IconPhone, IconMail, IconStethoscope, IconUser, IconMessageCircle, IconCreditCard, IconSearch, IconFilter, IconPlus, IconClock, IconArrowRight } from '@/components/icons';\nimport { PatientInteraction, User } from '@/types/clinic';\nimport { formatDateTime } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\ninterface InteractionTimelineProps {\n  patientId: string;\n  interactions: PatientInteraction[];\n  loading?: boolean;\n  onCreateInteraction?: () => void;\n  onInteractionClick?: (interaction: PatientInteraction) => void;\n  className?: string;\n}\nconst interactionTypeConfig = {\n  'phone-call': {\n    label: '电话通话',\n    icon: IconPhone,\n    color: 'bg-blue-100 text-blue-800 border-blue-200'\n  },\n  'email': {\n    label: '邮件沟通',\n    icon: IconMail,\n    color: 'bg-green-100 text-green-800 border-green-200'\n  },\n  'consultation-note': {\n    label: '咨询记录',\n    icon: IconStethoscope,\n    color: 'bg-purple-100 text-purple-800 border-purple-200'\n  },\n  'in-person-visit': {\n    label: '到院就诊',\n    icon: IconUser,\n    color: 'bg-orange-100 text-orange-800 border-orange-200'\n  },\n  'treatment-discussion': {\n    label: '治疗讨论',\n    icon: IconMessageCircle,\n    color: 'bg-indigo-100 text-indigo-800 border-indigo-200'\n  },\n  'billing-inquiry': {\n    label: '账单咨询',\n    icon: IconCreditCard,\n    color: 'bg-yellow-100 text-yellow-800 border-yellow-200'\n  }\n};\nconst statusConfig = {\n  'open': {\n    label: '开放',\n    color: 'bg-gray-100 text-gray-800'\n  },\n  'in-progress': {\n    label: '进行中',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  'resolved': {\n    label: '已解决',\n    color: 'bg-green-100 text-green-800'\n  },\n  'closed': {\n    label: '已关闭',\n    color: 'bg-gray-100 text-gray-600'\n  }\n};\nconst priorityConfig = {\n  'low': {\n    label: '低',\n    color: 'bg-gray-100 text-gray-600'\n  },\n  'medium': {\n    label: '中',\n    color: 'bg-yellow-100 text-yellow-800'\n  },\n  'high': {\n    label: '高',\n    color: 'bg-red-100 text-red-800'\n  }\n};\nexport function InteractionTimeline({\n  patientId,\n  interactions,\n  loading = false,\n  onCreateInteraction,\n  onInteractionClick,\n  className\n}: InteractionTimelineProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<string>('all');\n  const [filterStatus, setFilterStatus] = useState<string>('all');\n  const [filteredInteractions, setFilteredInteractions] = useState<PatientInteraction[]>(interactions);\n\n  // Filter interactions based on search and filters\n  useEffect(() => {\n    let filtered = interactions;\n\n    // Search filter\n    if (searchTerm) {\n      filtered = filtered.filter(interaction => interaction.title.toLowerCase().includes(searchTerm.toLowerCase()) || interaction.outcome?.toLowerCase().includes(searchTerm.toLowerCase()) || typeof interaction.staffMember === 'object' && `${interaction.staffMember.firstName} ${interaction.staffMember.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n\n    // Type filter\n    if (filterType !== 'all') {\n      filtered = filtered.filter(interaction => interaction.interactionType === filterType);\n    }\n\n    // Status filter\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(interaction => interaction.status === filterStatus);\n    }\n    setFilteredInteractions(filtered);\n  }, [interactions, searchTerm, filterType, filterStatus]);\n  const getInteractionIcon = (type: PatientInteraction['interactionType']) => {\n    const IconComponent = interactionTypeConfig[type]?.icon || IconMessageCircle;\n    return <IconComponent className=\"size-4\" data-sentry-element=\"IconComponent\" data-sentry-component=\"getInteractionIcon\" data-sentry-source-file=\"interaction-timeline.tsx\" />;\n  };\n  const getStaffMemberName = (staffMember: User | string) => {\n    if (typeof staffMember === 'string') return '未知工作人员';\n    return `${staffMember.firstName || ''} ${staffMember.lastName || ''}`.trim() || staffMember.email;\n  };\n  if (loading) {\n    return <Card className={className}>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconClock className=\"size-5\" />\n            互动时间线\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => <div key={i} className=\"animate-pulse\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"size-8 bg-gray-200 rounded-full\" />\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n                  </div>\n                </div>\n              </div>)}\n          </div>\n        </CardContent>\n      </Card>;\n  }\n  return <Card className={className} data-sentry-element=\"Card\" data-sentry-component=\"InteractionTimeline\" data-sentry-source-file=\"interaction-timeline.tsx\">\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"interaction-timeline.tsx\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"interaction-timeline.tsx\">\n            <IconClock className=\"size-5\" data-sentry-element=\"IconClock\" data-sentry-source-file=\"interaction-timeline.tsx\" />\n            互动时间线\n            <Badge variant=\"secondary\" className=\"ml-2\" data-sentry-element=\"Badge\" data-sentry-source-file=\"interaction-timeline.tsx\">\n              {filteredInteractions.length}\n            </Badge>\n          </CardTitle>\n          {onCreateInteraction && <Button onClick={onCreateInteraction} size=\"sm\">\n              <IconPlus className=\"size-4 mr-2\" />\n              添加互动\n            </Button>}\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-2\">\n          <div className=\"relative flex-1\">\n            <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground\" data-sentry-element=\"IconSearch\" data-sentry-source-file=\"interaction-timeline.tsx\" />\n            <Input placeholder=\"搜索互动记录...\" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className=\"pl-10\" data-sentry-element=\"Input\" data-sentry-source-file=\"interaction-timeline.tsx\" />\n          </div>\n          <Select value={filterType} onValueChange={setFilterType} data-sentry-element=\"Select\" data-sentry-source-file=\"interaction-timeline.tsx\">\n            <SelectTrigger className=\"w-full sm:w-[140px]\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"interaction-timeline.tsx\">\n              <IconFilter className=\"size-4 mr-2\" data-sentry-element=\"IconFilter\" data-sentry-source-file=\"interaction-timeline.tsx\" />\n              <SelectValue placeholder=\"类型\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"interaction-timeline.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"interaction-timeline.tsx\">\n              <SelectItem value=\"all\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"interaction-timeline.tsx\">所有类型</SelectItem>\n              {Object.entries(interactionTypeConfig).map(([value, config]) => <SelectItem key={value} value={value}>\n                  {config.label}\n                </SelectItem>)}\n            </SelectContent>\n          </Select>\n          <Select value={filterStatus} onValueChange={setFilterStatus} data-sentry-element=\"Select\" data-sentry-source-file=\"interaction-timeline.tsx\">\n            <SelectTrigger className=\"w-full sm:w-[120px]\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"interaction-timeline.tsx\">\n              <SelectValue placeholder=\"状态\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"interaction-timeline.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"interaction-timeline.tsx\">\n              <SelectItem value=\"all\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"interaction-timeline.tsx\">所有状态</SelectItem>\n              {Object.entries(statusConfig).map(([value, config]) => <SelectItem key={value} value={value}>\n                  {config.label}\n                </SelectItem>)}\n            </SelectContent>\n          </Select>\n        </div>\n      </CardHeader>\n\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"interaction-timeline.tsx\">\n        <ScrollArea className=\"h-[600px]\" data-sentry-element=\"ScrollArea\" data-sentry-source-file=\"interaction-timeline.tsx\">\n          {filteredInteractions.length === 0 ? <div className=\"text-center py-8 text-muted-foreground\">\n              <IconMessageCircle className=\"size-12 mx-auto mb-4 opacity-50\" />\n              <p className=\"text-lg font-medium\">暂无互动记录</p>\n              <p className=\"text-sm\">开始记录与患者的沟通互动</p>\n            </div> : <div className=\"space-y-4\">\n              {filteredInteractions.map((interaction, index) => {\n            const typeConfig = interactionTypeConfig[interaction.interactionType];\n            const statusBadge = statusConfig[interaction.status];\n            const priorityBadge = priorityConfig[interaction.priority];\n            return <div key={interaction.id} className=\"relative\">\n                    {/* Timeline line */}\n                    {index < filteredInteractions.length - 1 && <div className=\"absolute left-4 top-12 bottom-0 w-px bg-border\" />}\n\n                    <div className={cn(\"flex items-start gap-3 p-4 rounded-lg border transition-colors\", onInteractionClick && \"cursor-pointer hover:bg-muted/50\")} onClick={() => onInteractionClick?.(interaction)}>\n                      {/* Icon */}\n                      <div className={cn(\"flex items-center justify-center size-8 rounded-full border-2\", typeConfig?.color || 'bg-gray-100 text-gray-600 border-gray-200')}>\n                        {getInteractionIcon(interaction.interactionType)}\n                      </div>\n\n                      {/* Content */}\n                      <div className=\"flex-1 min-w-0\">\n                        <div className=\"flex items-start justify-between gap-2 mb-2\">\n                          <h4 className=\"font-medium text-sm leading-tight\">\n                            {interaction.title}\n                          </h4>\n                          <div className=\"flex items-center gap-1 flex-shrink-0\">\n                            <Badge variant=\"outline\" className={statusBadge.color}>\n                              {statusBadge.label}\n                            </Badge>\n                            <Badge variant=\"outline\" className={priorityBadge.color}>\n                              {priorityBadge.label}\n                            </Badge>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center gap-4 text-xs text-muted-foreground mb-2\">\n                          <span className=\"flex items-center gap-1\">\n                            <IconUser className=\"size-3\" />\n                            {getStaffMemberName(interaction.staffMember)}\n                          </span>\n                          <span className=\"flex items-center gap-1\">\n                            <IconClock className=\"size-3\" />\n                            {formatDateTime(new Date(interaction.timestamp))}\n                          </span>\n                        </div>\n\n                        {interaction.outcome && <p className=\"text-sm text-muted-foreground line-clamp-2 mb-2\">\n                            {interaction.outcome}\n                          </p>}\n\n                        {interaction.followUpRequired && <div className=\"flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded\">\n                            <IconArrowRight className=\"size-3\" />\n                            需要跟进\n                            {interaction.followUpDate && <span>- {formatDateTime(new Date(interaction.followUpDate))}</span>}\n                          </div>}\n                      </div>\n                    </div>\n                  </div>;\n          })}\n            </div>}\n        </ScrollArea>\n      </CardContent>\n    </Card>;\n}", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { IconPlus, IconSearch, IconFilter, IconClock, IconUser, IconCalendar, IconPhone, IconStethoscope, IconCreditCard, IconFileText, IconMessageCircle, IconAlertTriangle, IconCheck, IconX, IconArrowRight } from '@/components/icons';\nimport { PatientTask, User } from '@/types/clinic';\nimport { formatDateTime, isOverdue } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\ninterface TaskManagerProps {\n  patientId?: string;\n  tasks: PatientTask[];\n  loading?: boolean;\n  onCreateTask?: () => void;\n  onTaskClick?: (task: PatientTask) => void;\n  onTaskStatusChange?: (taskId: string, status: PatientTask['status']) => void;\n  className?: string;\n  viewMode?: 'kanban' | 'list';\n}\nconst taskTypeConfig = {\n  'follow-up-call': {\n    label: '跟进电话',\n    icon: IconPhone,\n    color: 'bg-blue-100 text-blue-800 border-blue-200'\n  },\n  'appointment-scheduling': {\n    label: '预约安排',\n    icon: IconCalendar,\n    color: 'bg-green-100 text-green-800 border-green-200'\n  },\n  'treatment-reminder': {\n    label: '治疗提醒',\n    icon: IconStethoscope,\n    color: 'bg-purple-100 text-purple-800 border-purple-200'\n  },\n  'billing-follow-up': {\n    label: '账单跟进',\n    icon: IconCreditCard,\n    color: 'bg-yellow-100 text-yellow-800 border-yellow-200'\n  },\n  'medical-record-update': {\n    label: '病历更新',\n    icon: IconFileText,\n    color: 'bg-indigo-100 text-indigo-800 border-indigo-200'\n  },\n  'consultation-follow-up': {\n    label: '咨询跟进',\n    icon: IconMessageCircle,\n    color: 'bg-orange-100 text-orange-800 border-orange-200'\n  }\n};\nconst statusConfig = {\n  'pending': {\n    label: '待处理',\n    color: 'bg-gray-100 text-gray-800',\n    icon: IconClock\n  },\n  'in-progress': {\n    label: '进行中',\n    color: 'bg-blue-100 text-blue-800',\n    icon: IconArrowRight\n  },\n  'completed': {\n    label: '已完成',\n    color: 'bg-green-100 text-green-800',\n    icon: IconCheck\n  },\n  'cancelled': {\n    label: '已取消',\n    color: 'bg-red-100 text-red-800',\n    icon: IconX\n  }\n};\nconst priorityConfig = {\n  'low': {\n    label: '低',\n    color: 'bg-gray-100 text-gray-600'\n  },\n  'medium': {\n    label: '中',\n    color: 'bg-yellow-100 text-yellow-800'\n  },\n  'high': {\n    label: '高',\n    color: 'bg-orange-100 text-orange-800'\n  },\n  'urgent': {\n    label: '紧急',\n    color: 'bg-red-100 text-red-800'\n  }\n};\nexport function TaskManager({\n  patientId,\n  tasks,\n  loading = false,\n  onCreateTask,\n  onTaskClick,\n  onTaskStatusChange,\n  className,\n  viewMode = 'list'\n}: TaskManagerProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<string>('all');\n  const [filterStatus, setFilterStatus] = useState<string>('all');\n  const [filterPriority, setFilterPriority] = useState<string>('all');\n  const [filteredTasks, setFilteredTasks] = useState<PatientTask[]>(tasks);\n\n  // Filter tasks based on search and filters\n  useEffect(() => {\n    let filtered = tasks;\n\n    // Search filter\n    if (searchTerm) {\n      filtered = filtered.filter(task => task.title.toLowerCase().includes(searchTerm.toLowerCase()) || task.description?.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n\n    // Type filter\n    if (filterType !== 'all') {\n      filtered = filtered.filter(task => task.taskType === filterType);\n    }\n\n    // Status filter\n    if (filterStatus !== 'all') {\n      filtered = filtered.filter(task => task.status === filterStatus);\n    }\n\n    // Priority filter\n    if (filterPriority !== 'all') {\n      filtered = filtered.filter(task => task.priority === filterPriority);\n    }\n    setFilteredTasks(filtered);\n  }, [tasks, searchTerm, filterType, filterStatus, filterPriority]);\n  const getTaskIcon = (type: PatientTask['taskType']) => {\n    const IconComponent = taskTypeConfig[type]?.icon || IconFileText;\n    return <IconComponent className=\"size-4\" data-sentry-element=\"IconComponent\" data-sentry-component=\"getTaskIcon\" data-sentry-source-file=\"task-manager.tsx\" />;\n  };\n  const getAssignedToName = (assignedTo: User | string) => {\n    if (typeof assignedTo === 'string') return '未分配';\n    return `${assignedTo.firstName || ''} ${assignedTo.lastName || ''}`.trim() || assignedTo.email;\n  };\n  const getTasksByStatus = (status: PatientTask['status']) => {\n    return filteredTasks.filter(task => task.status === status);\n  };\n  const handleStatusChange = (task: PatientTask, newStatus: PatientTask['status']) => {\n    if (onTaskStatusChange) {\n      onTaskStatusChange(task.id, newStatus);\n    }\n  };\n  if (loading) {\n    return <Card className={className}>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconFileText className=\"size-5\" />\n            任务管理\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => <div key={i} className=\"animate-pulse\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"size-8 bg-gray-200 rounded-full\" />\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n                  </div>\n                </div>\n              </div>)}\n          </div>\n        </CardContent>\n      </Card>;\n  }\n  const renderTaskCard = (task: PatientTask) => {\n    const typeConfig = taskTypeConfig[task.taskType];\n    const statusBadge = statusConfig[task.status];\n    const priorityBadge = priorityConfig[task.priority];\n    const overdue = isOverdue(task.dueDate) && task.status !== 'completed';\n    return <div key={task.id} className={cn(\"p-4 rounded-lg border transition-colors\", onTaskClick && \"cursor-pointer hover:bg-muted/50\", overdue && \"border-red-200 bg-red-50\")} onClick={() => onTaskClick?.(task)} data-sentry-component=\"renderTaskCard\" data-sentry-source-file=\"task-manager.tsx\">\n        <div className=\"flex items-start justify-between gap-2 mb-3\">\n          <div className=\"flex items-center gap-2\">\n            <div className={cn(\"flex items-center justify-center size-6 rounded border\", typeConfig?.color || 'bg-gray-100 text-gray-600 border-gray-200')}>\n              {getTaskIcon(task.taskType)}\n            </div>\n            <h4 className=\"font-medium text-sm leading-tight\">\n              {task.title}\n            </h4>\n          </div>\n          {overdue && <IconAlertTriangle className=\"size-4 text-red-500 flex-shrink-0\" />}\n        </div>\n\n        {task.description && <p className=\"text-xs text-muted-foreground line-clamp-2 mb-3\">\n            {task.description}\n          </p>}\n\n        <div className=\"flex items-center justify-between gap-2 mb-3\">\n          <div className=\"flex items-center gap-1\">\n            <Badge variant=\"outline\" className={priorityBadge.color} data-sentry-element=\"Badge\" data-sentry-source-file=\"task-manager.tsx\">\n              {priorityBadge.label}\n            </Badge>\n            <Badge variant=\"outline\" className={statusBadge.color} data-sentry-element=\"Badge\" data-sentry-source-file=\"task-manager.tsx\">\n              {statusBadge.label}\n            </Badge>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between text-xs text-muted-foreground\">\n          <span className=\"flex items-center gap-1\">\n            <IconUser className=\"size-3\" data-sentry-element=\"IconUser\" data-sentry-source-file=\"task-manager.tsx\" />\n            {getAssignedToName(task.assignedTo)}\n          </span>\n          <span className={cn(\"flex items-center gap-1\", overdue && \"text-red-600 font-medium\")}>\n            <IconCalendar className=\"size-3\" data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"task-manager.tsx\" />\n            {formatDateTime(new Date(task.dueDate))}\n          </span>\n        </div>\n\n        {onTaskStatusChange && task.status !== 'completed' && <div className=\"flex gap-1 mt-3\">\n            {task.status === 'pending' && <Button size=\"sm\" variant=\"outline\" onClick={e => {\n          e.stopPropagation();\n          handleStatusChange(task, 'in-progress');\n        }} className=\"h-6 px-2 text-xs\">\n                开始\n              </Button>}\n            {task.status === 'in-progress' && <Button size=\"sm\" variant=\"outline\" onClick={e => {\n          e.stopPropagation();\n          handleStatusChange(task, 'completed');\n        }} className=\"h-6 px-2 text-xs\">\n                完成\n              </Button>}\n          </div>}\n      </div>;\n  };\n  return <Card className={className} data-sentry-element=\"Card\" data-sentry-component=\"TaskManager\" data-sentry-source-file=\"task-manager.tsx\">\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"task-manager.tsx\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"task-manager.tsx\">\n            <IconFileText className=\"size-5\" data-sentry-element=\"IconFileText\" data-sentry-source-file=\"task-manager.tsx\" />\n            任务管理\n            <Badge variant=\"secondary\" className=\"ml-2\" data-sentry-element=\"Badge\" data-sentry-source-file=\"task-manager.tsx\">\n              {filteredTasks.length}\n            </Badge>\n          </CardTitle>\n          {onCreateTask && <Button onClick={onCreateTask} size=\"sm\">\n              <IconPlus className=\"size-4 mr-2\" />\n              创建任务\n            </Button>}\n        </div>\n\n        {/* Search and Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-2\">\n          <div className=\"relative flex-1\">\n            <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground\" data-sentry-element=\"IconSearch\" data-sentry-source-file=\"task-manager.tsx\" />\n            <Input placeholder=\"搜索任务...\" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className=\"pl-10\" data-sentry-element=\"Input\" data-sentry-source-file=\"task-manager.tsx\" />\n          </div>\n          <Select value={filterType} onValueChange={setFilterType} data-sentry-element=\"Select\" data-sentry-source-file=\"task-manager.tsx\">\n            <SelectTrigger className=\"w-full sm:w-[140px]\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"task-manager.tsx\">\n              <IconFilter className=\"size-4 mr-2\" data-sentry-element=\"IconFilter\" data-sentry-source-file=\"task-manager.tsx\" />\n              <SelectValue placeholder=\"类型\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"task-manager.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"task-manager.tsx\">\n              <SelectItem value=\"all\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"task-manager.tsx\">所有类型</SelectItem>\n              {Object.entries(taskTypeConfig).map(([value, config]) => <SelectItem key={value} value={value}>\n                  {config.label}\n                </SelectItem>)}\n            </SelectContent>\n          </Select>\n          <Select value={filterPriority} onValueChange={setFilterPriority} data-sentry-element=\"Select\" data-sentry-source-file=\"task-manager.tsx\">\n            <SelectTrigger className=\"w-full sm:w-[120px]\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"task-manager.tsx\">\n              <SelectValue placeholder=\"优先级\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"task-manager.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"task-manager.tsx\">\n              <SelectItem value=\"all\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"task-manager.tsx\">所有优先级</SelectItem>\n              {Object.entries(priorityConfig).map(([value, config]) => <SelectItem key={value} value={value}>\n                  {config.label}\n                </SelectItem>)}\n            </SelectContent>\n          </Select>\n        </div>\n      </CardHeader>\n\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"task-manager.tsx\">\n        {viewMode === 'kanban' ?\n      // Kanban View\n      <Tabs defaultValue=\"pending\" className=\"w-full\">\n            <TabsList className=\"grid w-full grid-cols-4\">\n              {Object.entries(statusConfig).map(([status, config]) => <TabsTrigger key={status} value={status} className=\"text-xs\">\n                  {config.label} ({getTasksByStatus(status as PatientTask['status']).length})\n                </TabsTrigger>)}\n            </TabsList>\n            {Object.entries(statusConfig).map(([status, config]) => <TabsContent key={status} value={status}>\n                <ScrollArea className=\"h-[500px]\">\n                  <div className=\"space-y-3\">\n                    {getTasksByStatus(status as PatientTask['status']).map(renderTaskCard)}\n                    {getTasksByStatus(status as PatientTask['status']).length === 0 && <div className=\"text-center py-8 text-muted-foreground\">\n                        <p>暂无{config.label}任务</p>\n                      </div>}\n                  </div>\n                </ScrollArea>\n              </TabsContent>)}\n          </Tabs> :\n      // List View\n      <ScrollArea className=\"h-[600px]\">\n            {filteredTasks.length === 0 ? <div className=\"text-center py-8 text-muted-foreground\">\n                <IconFileText className=\"size-12 mx-auto mb-4 opacity-50\" />\n                <p className=\"text-lg font-medium\">暂无任务</p>\n                <p className=\"text-sm\">创建任务来跟进患者事务</p>\n              </div> : <div className=\"space-y-3\">\n                {filteredTasks.map(renderTaskCard)}\n              </div>}\n          </ScrollArea>}\n      </CardContent>\n    </Card>;\n}", "'use client';\n\nimport React, { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { IconPhone, IconMail, IconMessageCircle, IconCalendar, IconPlus, IconStethoscope, IconCreditCard, IconFileText, IconUser, IconChevronDown, IconClock } from '@/components/icons';\nimport { Patient } from '@/types/clinic';\nimport { cn } from '@/lib/utils';\ninterface QuickActionsProps {\n  patient: Patient;\n  onCreateInteraction?: (type: string) => void;\n  onCreateTask?: (type: string) => void;\n  onScheduleAppointment?: () => void;\n  onViewBilling?: () => void;\n  className?: string;\n  compact?: boolean;\n}\nconst interactionActions = [{\n  type: 'phone-call',\n  label: '记录电话',\n  description: '记录与患者的电话沟通',\n  icon: IconPhone,\n  color: 'text-blue-600 hover:text-blue-700',\n  bgColor: 'hover:bg-blue-50'\n}, {\n  type: 'email',\n  label: '记录邮件',\n  description: '记录邮件沟通内容',\n  icon: IconMail,\n  color: 'text-green-600 hover:text-green-700',\n  bgColor: 'hover:bg-green-50'\n}, {\n  type: 'consultation-note',\n  label: '咨询记录',\n  description: '添加咨询或诊疗记录',\n  icon: IconStethoscope,\n  color: 'text-purple-600 hover:text-purple-700',\n  bgColor: 'hover:bg-purple-50'\n}, {\n  type: 'in-person-visit',\n  label: '到院记录',\n  description: '记录患者到院就诊',\n  icon: IconUser,\n  color: 'text-orange-600 hover:text-orange-700',\n  bgColor: 'hover:bg-orange-50'\n}, {\n  type: 'treatment-discussion',\n  label: '治疗讨论',\n  description: '记录治疗方案讨论',\n  icon: IconMessageCircle,\n  color: 'text-indigo-600 hover:text-indigo-700',\n  bgColor: 'hover:bg-indigo-50'\n}, {\n  type: 'billing-inquiry',\n  label: '账单咨询',\n  description: '记录账单相关咨询',\n  icon: IconCreditCard,\n  color: 'text-yellow-600 hover:text-yellow-700',\n  bgColor: 'hover:bg-yellow-50'\n}];\nconst taskActions = [{\n  type: 'follow-up-call',\n  label: '跟进电话',\n  description: '安排跟进电话任务',\n  icon: IconPhone,\n  color: 'text-blue-600 hover:text-blue-700',\n  bgColor: 'hover:bg-blue-50'\n}, {\n  type: 'appointment-scheduling',\n  label: '预约安排',\n  description: '安排预约相关任务',\n  icon: IconCalendar,\n  color: 'text-green-600 hover:text-green-700',\n  bgColor: 'hover:bg-green-50'\n}, {\n  type: 'treatment-reminder',\n  label: '治疗提醒',\n  description: '创建治疗提醒任务',\n  icon: IconStethoscope,\n  color: 'text-purple-600 hover:text-purple-700',\n  bgColor: 'hover:bg-purple-50'\n}, {\n  type: 'billing-follow-up',\n  label: '账单跟进',\n  description: '创建账单跟进任务',\n  icon: IconCreditCard,\n  color: 'text-yellow-600 hover:text-yellow-700',\n  bgColor: 'hover:bg-yellow-50'\n}, {\n  type: 'medical-record-update',\n  label: '病历更新',\n  description: '安排病历更新任务',\n  icon: IconFileText,\n  color: 'text-indigo-600 hover:text-indigo-700',\n  bgColor: 'hover:bg-indigo-50'\n}, {\n  type: 'consultation-follow-up',\n  label: '咨询跟进',\n  description: '创建咨询跟进任务',\n  icon: IconMessageCircle,\n  color: 'text-orange-600 hover:text-orange-700',\n  bgColor: 'hover:bg-orange-50'\n}];\nexport function QuickActions({\n  patient,\n  onCreateInteraction,\n  onCreateTask,\n  onScheduleAppointment,\n  onViewBilling,\n  className,\n  compact = false\n}: QuickActionsProps) {\n  const [isInteractionMenuOpen, setIsInteractionMenuOpen] = useState(false);\n  const [isTaskMenuOpen, setIsTaskMenuOpen] = useState(false);\n  if (compact) {\n    return <div className={cn(\"flex items-center gap-2\", className)}>\n        {/* Quick Call Button */}\n        <Button size=\"sm\" variant=\"outline\" onClick={() => onCreateInteraction?.('phone-call')} className=\"text-blue-600 hover:text-blue-700 hover:bg-blue-50\">\n          <IconPhone className=\"size-4 mr-1\" />\n          电话\n        </Button>\n\n        {/* Quick Task Button */}\n        <Button size=\"sm\" variant=\"outline\" onClick={() => onCreateTask?.('follow-up-call')} className=\"text-green-600 hover:text-green-700 hover:bg-green-50\">\n          <IconClock className=\"size-4 mr-1\" />\n          任务\n        </Button>\n\n        {/* Schedule Appointment */}\n        {onScheduleAppointment && <Button size=\"sm\" variant=\"outline\" onClick={onScheduleAppointment} className=\"text-purple-600 hover:text-purple-700 hover:bg-purple-50\">\n            <IconCalendar className=\"size-4 mr-1\" />\n            预约\n          </Button>}\n\n        {/* More Actions Dropdown */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button size=\"sm\" variant=\"outline\">\n              <IconPlus className=\"size-4\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\" className=\"w-56\">\n            <DropdownMenuLabel>快速操作</DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            \n            {onCreateInteraction && <>\n                <DropdownMenuLabel className=\"text-xs font-normal text-muted-foreground\">\n                  添加互动记录\n                </DropdownMenuLabel>\n                {interactionActions.slice(0, 3).map(action => {\n              const IconComponent = action.icon;\n              return <DropdownMenuItem key={action.type} onClick={() => onCreateInteraction(action.type)} className={cn(\"cursor-pointer\", action.bgColor)}>\n                      <IconComponent className={cn(\"size-4 mr-2\", action.color)} />\n                      {action.label}\n                    </DropdownMenuItem>;\n            })}\n                <DropdownMenuSeparator />\n              </>}\n\n            {onCreateTask && <>\n                <DropdownMenuLabel className=\"text-xs font-normal text-muted-foreground\">\n                  创建任务\n                </DropdownMenuLabel>\n                {taskActions.slice(0, 3).map(action => {\n              const IconComponent = action.icon;\n              return <DropdownMenuItem key={action.type} onClick={() => onCreateTask(action.type)} className={cn(\"cursor-pointer\", action.bgColor)}>\n                      <IconComponent className={cn(\"size-4 mr-2\", action.color)} />\n                      {action.label}\n                    </DropdownMenuItem>;\n            })}\n              </>}\n\n            {onViewBilling && <>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem onClick={onViewBilling} className=\"cursor-pointer hover:bg-gray-50\">\n                  <IconCreditCard className=\"size-4 mr-2 text-gray-600\" />\n                  查看账单\n                </DropdownMenuItem>\n              </>}\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>;\n  }\n  return <Card className={className} data-sentry-element=\"Card\" data-sentry-component=\"QuickActions\" data-sentry-source-file=\"quick-actions.tsx\">\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"quick-actions.tsx\">\n        <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"quick-actions.tsx\">\n          <IconPlus className=\"size-5\" data-sentry-element=\"IconPlus\" data-sentry-source-file=\"quick-actions.tsx\" />\n          快速操作\n        </CardTitle>\n        <p className=\"text-sm text-muted-foreground\">\n          为 {patient.fullName} 执行常用操作\n        </p>\n      </CardHeader>\n      <CardContent className=\"space-y-4\" data-sentry-element=\"CardContent\" data-sentry-source-file=\"quick-actions.tsx\">\n        {/* Primary Actions */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3\">\n          {/* Quick Call */}\n          <Button variant=\"outline\" onClick={() => onCreateInteraction?.('phone-call')} className=\"h-auto p-4 text-left justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200\" data-sentry-element=\"Button\" data-sentry-source-file=\"quick-actions.tsx\">\n            <div className=\"flex items-center gap-3\">\n              <IconPhone className=\"size-5\" data-sentry-element=\"IconPhone\" data-sentry-source-file=\"quick-actions.tsx\" />\n              <div>\n                <div className=\"font-medium\">联系患者</div>\n                <div className=\"text-xs text-muted-foreground\">记录电话沟通</div>\n              </div>\n            </div>\n          </Button>\n\n          {/* Schedule Appointment */}\n          {onScheduleAppointment && <Button variant=\"outline\" onClick={onScheduleAppointment} className=\"h-auto p-4 text-left justify-start text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200\">\n              <div className=\"flex items-center gap-3\">\n                <IconCalendar className=\"size-5\" />\n                <div>\n                  <div className=\"font-medium\">安排预约</div>\n                  <div className=\"text-xs text-muted-foreground\">预约治疗或咨询</div>\n                </div>\n              </div>\n            </Button>}\n        </div>\n\n        {/* Interaction Actions */}\n        {onCreateInteraction && <div>\n            <DropdownMenu open={isInteractionMenuOpen} onOpenChange={setIsInteractionMenuOpen}>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"outline\" className=\"w-full justify-between\">\n                  <span className=\"flex items-center gap-2\">\n                    <IconMessageCircle className=\"size-4\" />\n                    添加互动记录\n                  </span>\n                  <IconChevronDown className=\"size-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent className=\"w-full min-w-[300px]\">\n                <DropdownMenuLabel>选择互动类型</DropdownMenuLabel>\n                <DropdownMenuSeparator />\n                {interactionActions.map(action => {\n              const IconComponent = action.icon;\n              return <DropdownMenuItem key={action.type} onClick={() => {\n                onCreateInteraction(action.type);\n                setIsInteractionMenuOpen(false);\n              }} className={cn(\"cursor-pointer p-3\", action.bgColor)}>\n                      <div className=\"flex items-start gap-3\">\n                        <IconComponent className={cn(\"size-4 mt-0.5\", action.color)} />\n                        <div>\n                          <div className=\"font-medium\">{action.label}</div>\n                          <div className=\"text-xs text-muted-foreground\">\n                            {action.description}\n                          </div>\n                        </div>\n                      </div>\n                    </DropdownMenuItem>;\n            })}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>}\n\n        {/* Task Actions */}\n        {onCreateTask && <div>\n            <DropdownMenu open={isTaskMenuOpen} onOpenChange={setIsTaskMenuOpen}>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"outline\" className=\"w-full justify-between\">\n                  <span className=\"flex items-center gap-2\">\n                    <IconClock className=\"size-4\" />\n                    创建跟进任务\n                  </span>\n                  <IconChevronDown className=\"size-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent className=\"w-full min-w-[300px]\">\n                <DropdownMenuLabel>选择任务类型</DropdownMenuLabel>\n                <DropdownMenuSeparator />\n                {taskActions.map(action => {\n              const IconComponent = action.icon;\n              return <DropdownMenuItem key={action.type} onClick={() => {\n                onCreateTask(action.type);\n                setIsTaskMenuOpen(false);\n              }} className={cn(\"cursor-pointer p-3\", action.bgColor)}>\n                      <div className=\"flex items-start gap-3\">\n                        <IconComponent className={cn(\"size-4 mt-0.5\", action.color)} />\n                        <div>\n                          <div className=\"font-medium\">{action.label}</div>\n                          <div className=\"text-xs text-muted-foreground\">\n                            {action.description}\n                          </div>\n                        </div>\n                      </div>\n                    </DropdownMenuItem>;\n            })}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>}\n\n        {/* Additional Actions */}\n        {onViewBilling && <Button variant=\"outline\" onClick={onViewBilling} className=\"w-full justify-start text-gray-600 hover:text-gray-700 hover:bg-gray-50\">\n            <IconCreditCard className=\"size-4 mr-2\" />\n            查看账单记录\n          </Button>}\n      </CardContent>\n    </Card>;\n}", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nimport { Separator } from '@/components/ui/separator';\nimport { IconMessageCircle, IconSearch, IconFilter, IconPhone, IconMail, IconStethoscope, IconUser, IconCreditCard, IconClock, IconArrowRight, IconEye, IconEdit, IconCalendar } from '@/components/icons';\nimport { PatientInteraction, PatientTask, TimelineItem, User } from '@/types/clinic';\nimport { formatDateTime, formatRelativeTime } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\ninterface CommunicationLogProps {\n  patientId: string;\n  timeline: TimelineItem[];\n  loading?: boolean;\n  onItemClick?: (item: TimelineItem) => void;\n  onEditItem?: (item: TimelineItem) => void;\n  className?: string;\n}\nconst interactionTypeConfig = {\n  'phone-call': {\n    label: '电话通话',\n    icon: IconPhone,\n    color: 'bg-blue-100 text-blue-800 border-blue-200'\n  },\n  'email': {\n    label: '邮件沟通',\n    icon: IconMail,\n    color: 'bg-green-100 text-green-800 border-green-200'\n  },\n  'consultation-note': {\n    label: '咨询记录',\n    icon: IconStethoscope,\n    color: 'bg-purple-100 text-purple-800 border-purple-200'\n  },\n  'in-person-visit': {\n    label: '到院就诊',\n    icon: IconUser,\n    color: 'bg-orange-100 text-orange-800 border-orange-200'\n  },\n  'treatment-discussion': {\n    label: '治疗讨论',\n    icon: IconMessageCircle,\n    color: 'bg-indigo-100 text-indigo-800 border-indigo-200'\n  },\n  'billing-inquiry': {\n    label: '账单咨询',\n    icon: IconCreditCard,\n    color: 'bg-yellow-100 text-yellow-800 border-yellow-200'\n  }\n};\nconst taskTypeConfig = {\n  'follow-up-call': {\n    label: '跟进电话',\n    icon: IconPhone,\n    color: 'bg-blue-100 text-blue-800 border-blue-200'\n  },\n  'appointment-scheduling': {\n    label: '预约安排',\n    icon: IconCalendar,\n    color: 'bg-green-100 text-green-800 border-green-200'\n  },\n  'treatment-reminder': {\n    label: '治疗提醒',\n    icon: IconStethoscope,\n    color: 'bg-purple-100 text-purple-800 border-purple-200'\n  },\n  'billing-follow-up': {\n    label: '账单跟进',\n    icon: IconCreditCard,\n    color: 'bg-yellow-100 text-yellow-800 border-yellow-200'\n  },\n  'medical-record-update': {\n    label: '病历更新',\n    icon: IconUser,\n    color: 'bg-indigo-100 text-indigo-800 border-indigo-200'\n  },\n  'consultation-follow-up': {\n    label: '咨询跟进',\n    icon: IconMessageCircle,\n    color: 'bg-orange-100 text-orange-800 border-orange-200'\n  }\n};\nexport function CommunicationLog({\n  patientId,\n  timeline,\n  loading = false,\n  onItemClick,\n  onEditItem,\n  className\n}: CommunicationLogProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState<string>('all');\n  const [activeTab, setActiveTab] = useState<string>('all');\n  const [filteredTimeline, setFilteredTimeline] = useState<TimelineItem[]>(timeline);\n\n  // Filter timeline based on search, filters, and active tab\n  useEffect(() => {\n    let filtered = timeline;\n\n    // Tab filter\n    if (activeTab !== 'all') {\n      filtered = filtered.filter(item => item.type === activeTab);\n    }\n\n    // Search filter\n    if (searchTerm) {\n      filtered = filtered.filter(item => item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.type === 'interaction' && (item.data as PatientInteraction).outcome?.toLowerCase().includes(searchTerm.toLowerCase()) || item.type === 'task' && (item.data as PatientTask).description?.toLowerCase().includes(searchTerm.toLowerCase()));\n    }\n\n    // Type filter\n    if (filterType !== 'all') {\n      filtered = filtered.filter(item => {\n        if (item.type === 'interaction') {\n          return (item.data as PatientInteraction).interactionType === filterType;\n        } else {\n          return (item.data as PatientTask).taskType === filterType;\n        }\n      });\n    }\n    setFilteredTimeline(filtered);\n  }, [timeline, searchTerm, filterType, activeTab]);\n  const getItemIcon = (item: TimelineItem) => {\n    if (item.type === 'interaction') {\n      const interaction = item.data as PatientInteraction;\n      const config = interactionTypeConfig[interaction.interactionType];\n      const IconComponent = config?.icon || IconMessageCircle;\n      return <IconComponent className=\"size-4\" />;\n    } else {\n      const task = item.data as PatientTask;\n      const config = taskTypeConfig[task.taskType];\n      const IconComponent = config?.icon || IconClock;\n      return <IconComponent className=\"size-4\" />;\n    }\n  };\n  const getItemColor = (item: TimelineItem) => {\n    if (item.type === 'interaction') {\n      const interaction = item.data as PatientInteraction;\n      return interactionTypeConfig[interaction.interactionType]?.color || 'bg-gray-100 text-gray-800 border-gray-200';\n    } else {\n      const task = item.data as PatientTask;\n      return taskTypeConfig[task.taskType]?.color || 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n  };\n  const getStaffName = (staff: User | string | undefined) => {\n    if (!staff) return '未知';\n    if (typeof staff === 'string') return '未知工作人员';\n    return `${staff.firstName || ''} ${staff.lastName || ''}`.trim() || staff.email;\n  };\n  const renderTimelineItem = (item: TimelineItem, index: number) => {\n    const isInteraction = item.type === 'interaction';\n    const data = item.data;\n    const itemColor = getItemColor(item);\n    return <div key={item.id} className=\"relative\" data-sentry-component=\"renderTimelineItem\" data-sentry-source-file=\"communication-log.tsx\">\n        {/* Timeline line */}\n        {index < filteredTimeline.length - 1 && <div className=\"absolute left-4 top-12 bottom-0 w-px bg-border\" />}\n\n        <div className={cn(\"flex items-start gap-3 p-4 rounded-lg border transition-colors\", onItemClick && \"cursor-pointer hover:bg-muted/50\")} onClick={() => onItemClick?.(item)}>\n          {/* Icon */}\n          <div className={cn(\"flex items-center justify-center size-8 rounded-full border-2 flex-shrink-0\", itemColor)}>\n            {getItemIcon(item)}\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-start justify-between gap-2 mb-2\">\n              <div>\n                <h4 className=\"font-medium text-sm leading-tight mb-1\">\n                  {item.title}\n                </h4>\n                <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                  <Badge variant=\"outline\" className=\"text-xs\" data-sentry-element=\"Badge\" data-sentry-source-file=\"communication-log.tsx\">\n                    {isInteraction ? '互动' : '任务'}\n                  </Badge>\n                  <span className=\"flex items-center gap-1\">\n                    <IconUser className=\"size-3\" data-sentry-element=\"IconUser\" data-sentry-source-file=\"communication-log.tsx\" />\n                    {isInteraction ? getStaffName(item.staffMember) : `分配给: ${getStaffName(item.assignedTo)}`}\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center gap-1 flex-shrink-0\">\n                <Badge variant=\"outline\" className={item.status === 'completed' || item.status === 'resolved' || item.status === 'closed' ? 'bg-green-100 text-green-800' : item.status === 'in-progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'} data-sentry-element=\"Badge\" data-sentry-source-file=\"communication-log.tsx\">\n                  {item.status === 'open' && '开放'}\n                  {item.status === 'in-progress' && '进行中'}\n                  {item.status === 'resolved' && '已解决'}\n                  {item.status === 'closed' && '已关闭'}\n                  {item.status === 'pending' && '待处理'}\n                  {item.status === 'completed' && '已完成'}\n                  {item.status === 'cancelled' && '已取消'}\n                </Badge>\n                <Badge variant=\"outline\" className={item.priority === 'urgent' || item.priority === 'high' ? 'bg-red-100 text-red-800' : item.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'} data-sentry-element=\"Badge\" data-sentry-source-file=\"communication-log.tsx\">\n                  {item.priority === 'low' && '低'}\n                  {item.priority === 'medium' && '中'}\n                  {item.priority === 'high' && '高'}\n                  {item.priority === 'urgent' && '紧急'}\n                </Badge>\n              </div>\n            </div>\n\n            {/* Content preview */}\n            {isInteraction ? <div>\n                {(data as PatientInteraction).outcome && <p className=\"text-sm text-muted-foreground line-clamp-2 mb-2\">\n                    {(data as PatientInteraction).outcome}\n                  </p>}\n                {(data as PatientInteraction).followUpRequired && <div className=\"flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded mb-2\">\n                    <IconArrowRight className=\"size-3\" />\n                    需要跟进\n                    {(data as PatientInteraction).followUpDate && <span>- {formatDateTime(new Date((data as PatientInteraction).followUpDate!))}</span>}\n                  </div>}\n              </div> : <div>\n                {(data as PatientTask).description && <p className=\"text-sm text-muted-foreground line-clamp-2 mb-2\">\n                    {(data as PatientTask).description}\n                  </p>}\n                <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n                  <span className=\"flex items-center gap-1\">\n                    <IconCalendar className=\"size-3\" />\n                    截止: {formatDateTime(new Date((data as PatientTask).dueDate))}\n                  </span>\n                  {(data as PatientTask).completedAt && <span className=\"flex items-center gap-1 text-green-600\">\n                      <IconClock className=\"size-3\" />\n                      完成: {(data as PatientTask).completedAt ? formatDateTime(new Date((data as PatientTask).completedAt!)) : '未完成'}\n                    </span>}\n                </div>\n              </div>}\n\n            {/* Timestamp and actions */}\n            <div className=\"flex items-center justify-between mt-3\">\n              <span className=\"text-xs text-muted-foreground\">\n                {formatRelativeTime(new Date(item.timestamp))} • {formatDateTime(new Date(item.timestamp))}\n              </span>\n              \n              {(onItemClick || onEditItem) && <div className=\"flex items-center gap-1\">\n                  {onItemClick && <Button size=\"sm\" variant=\"ghost\" onClick={e => {\n                e.stopPropagation();\n                onItemClick(item);\n              }} className=\"h-6 px-2 text-xs\">\n                      <IconEye className=\"size-3 mr-1\" />\n                      查看\n                    </Button>}\n                  {onEditItem && <Button size=\"sm\" variant=\"ghost\" onClick={e => {\n                e.stopPropagation();\n                onEditItem(item);\n              }} className=\"h-6 px-2 text-xs\">\n                      <IconEdit className=\"size-3 mr-1\" />\n                      编辑\n                    </Button>}\n                </div>}\n            </div>\n          </div>\n        </div>\n      </div>;\n  };\n  if (loading) {\n    return <Card className={className}>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconMessageCircle className=\"size-5\" />\n            沟通记录\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[...Array(3)].map((_, i) => <div key={i} className=\"animate-pulse\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"size-8 bg-gray-200 rounded-full\" />\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n                  </div>\n                </div>\n              </div>)}\n          </div>\n        </CardContent>\n      </Card>;\n  }\n  const interactionCount = timeline.filter(item => item.type === 'interaction').length;\n  const taskCount = timeline.filter(item => item.type === 'task').length;\n  return <Card className={className} data-sentry-element=\"Card\" data-sentry-component=\"CommunicationLog\" data-sentry-source-file=\"communication-log.tsx\">\n      <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"communication-log.tsx\">\n        <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"communication-log.tsx\">\n          <IconMessageCircle className=\"size-5\" data-sentry-element=\"IconMessageCircle\" data-sentry-source-file=\"communication-log.tsx\" />\n          沟通记录\n          <Badge variant=\"secondary\" className=\"ml-2\" data-sentry-element=\"Badge\" data-sentry-source-file=\"communication-log.tsx\">\n            {filteredTimeline.length}\n          </Badge>\n        </CardTitle>\n\n        {/* Search and Filters */}\n        <div className=\"flex flex-col sm:flex-row gap-2\">\n          <div className=\"relative flex-1\">\n            <IconSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground\" data-sentry-element=\"IconSearch\" data-sentry-source-file=\"communication-log.tsx\" />\n            <Input placeholder=\"搜索沟通记录...\" value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className=\"pl-10\" data-sentry-element=\"Input\" data-sentry-source-file=\"communication-log.tsx\" />\n          </div>\n          <Select value={filterType} onValueChange={setFilterType} data-sentry-element=\"Select\" data-sentry-source-file=\"communication-log.tsx\">\n            <SelectTrigger className=\"w-full sm:w-[140px]\" data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"communication-log.tsx\">\n              <IconFilter className=\"size-4 mr-2\" data-sentry-element=\"IconFilter\" data-sentry-source-file=\"communication-log.tsx\" />\n              <SelectValue placeholder=\"类型\" data-sentry-element=\"SelectValue\" data-sentry-source-file=\"communication-log.tsx\" />\n            </SelectTrigger>\n            <SelectContent data-sentry-element=\"SelectContent\" data-sentry-source-file=\"communication-log.tsx\">\n              <SelectItem value=\"all\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">所有类型</SelectItem>\n              <Separator className=\"my-1\" data-sentry-element=\"Separator\" data-sentry-source-file=\"communication-log.tsx\" />\n              <SelectItem value=\"phone-call\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">电话通话</SelectItem>\n              <SelectItem value=\"email\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">邮件沟通</SelectItem>\n              <SelectItem value=\"consultation-note\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">咨询记录</SelectItem>\n              <SelectItem value=\"in-person-visit\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">到院就诊</SelectItem>\n              <SelectItem value=\"treatment-discussion\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">治疗讨论</SelectItem>\n              <SelectItem value=\"billing-inquiry\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">账单咨询</SelectItem>\n              <Separator className=\"my-1\" data-sentry-element=\"Separator\" data-sentry-source-file=\"communication-log.tsx\" />\n              <SelectItem value=\"follow-up-call\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">跟进电话</SelectItem>\n              <SelectItem value=\"appointment-scheduling\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">预约安排</SelectItem>\n              <SelectItem value=\"treatment-reminder\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">治疗提醒</SelectItem>\n              <SelectItem value=\"billing-follow-up\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">账单跟进</SelectItem>\n              <SelectItem value=\"medical-record-update\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">病历更新</SelectItem>\n              <SelectItem value=\"consultation-follow-up\" data-sentry-element=\"SelectItem\" data-sentry-source-file=\"communication-log.tsx\">咨询跟进</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n      </CardHeader>\n\n      <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"communication-log.tsx\">\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\" data-sentry-element=\"Tabs\" data-sentry-source-file=\"communication-log.tsx\">\n          <TabsList className=\"grid w-full grid-cols-3\" data-sentry-element=\"TabsList\" data-sentry-source-file=\"communication-log.tsx\">\n            <TabsTrigger value=\"all\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"communication-log.tsx\">\n              全部 ({timeline.length})\n            </TabsTrigger>\n            <TabsTrigger value=\"interaction\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"communication-log.tsx\">\n              互动 ({interactionCount})\n            </TabsTrigger>\n            <TabsTrigger value=\"task\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"communication-log.tsx\">\n              任务 ({taskCount})\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value={activeTab} className=\"mt-4\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"communication-log.tsx\">\n            <ScrollArea className=\"h-[600px]\" data-sentry-element=\"ScrollArea\" data-sentry-source-file=\"communication-log.tsx\">\n              {filteredTimeline.length === 0 ? <div className=\"text-center py-8 text-muted-foreground\">\n                  <IconMessageCircle className=\"size-12 mx-auto mb-4 opacity-50\" />\n                  <p className=\"text-lg font-medium\">暂无沟通记录</p>\n                  <p className=\"text-sm\">开始记录与患者的互动和任务</p>\n                </div> : <div className=\"space-y-4\">\n                  {filteredTimeline.map(renderTimelineItem)}\n                </div>}\n            </ScrollArea>\n          </TabsContent>\n        </Tabs>\n      </CardContent>\n    </Card>;\n}", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Button } from '@/components/ui/button';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { IconCalendar, IconClock } from '@/components/icons';\nimport { PatientInteraction, PatientInteractionFormData } from '@/types/clinic';\nimport { formatDateTime } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\nconst interactionSchema = z.object({\n  interactionType: z.enum(['phone-call', 'email', 'consultation-note', 'in-person-visit', 'treatment-discussion', 'billing-inquiry']),\n  title: z.string().min(1, '请输入互动标题').max(200, '标题不能超过200个字符'),\n  notes: z.string().min(1, '请输入详细记录'),\n  outcome: z.string().optional(),\n  followUpRequired: z.boolean().default(false),\n  followUpDate: z.string().optional(),\n  priority: z.enum(['low', 'medium', 'high']).default('medium'),\n  status: z.enum(['open', 'in-progress', 'resolved', 'closed']).default('open'),\n  relatedAppointment: z.string().optional(),\n  relatedBill: z.string().optional()\n});\ntype InteractionFormData = z.infer<typeof interactionSchema>;\ninterface InteractionFormDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  patientId: string;\n  patientName: string;\n  interaction?: PatientInteraction;\n  defaultType?: string;\n  onSubmit: (data: PatientInteractionFormData) => Promise<void>;\n  loading?: boolean;\n}\nconst interactionTypeOptions = [{\n  value: 'phone-call',\n  label: '电话通话',\n  description: '记录与患者的电话沟通'\n}, {\n  value: 'email',\n  label: '邮件沟通',\n  description: '记录邮件往来内容'\n}, {\n  value: 'consultation-note',\n  label: '咨询记录',\n  description: '记录咨询或诊疗过程'\n}, {\n  value: 'in-person-visit',\n  label: '到院就诊',\n  description: '记录患者到院就诊情况'\n}, {\n  value: 'treatment-discussion',\n  label: '治疗讨论',\n  description: '记录治疗方案讨论'\n}, {\n  value: 'billing-inquiry',\n  label: '账单咨询',\n  description: '记录账单相关咨询'\n}];\nconst priorityOptions = [{\n  value: 'low',\n  label: '低',\n  description: '一般重要性'\n}, {\n  value: 'medium',\n  label: '中',\n  description: '中等重要性'\n}, {\n  value: 'high',\n  label: '高',\n  description: '高重要性，需要优先处理'\n}];\nconst statusOptions = [{\n  value: 'open',\n  label: '开放',\n  description: '新创建的互动记录'\n}, {\n  value: 'in-progress',\n  label: '进行中',\n  description: '正在处理中'\n}, {\n  value: 'resolved',\n  label: '已解决',\n  description: '问题已解决'\n}, {\n  value: 'closed',\n  label: '已关闭',\n  description: '互动已结束'\n}];\nexport function InteractionFormDialog({\n  open,\n  onOpenChange,\n  patientId,\n  patientName,\n  interaction,\n  defaultType,\n  onSubmit,\n  loading = false\n}: InteractionFormDialogProps) {\n  const [followUpDate, setFollowUpDate] = useState<Date | undefined>();\n  const [followUpTime, setFollowUpTime] = useState('09:00');\n  const isEditing = !!interaction;\n  const form = useForm<InteractionFormData>({\n    resolver: zodResolver(interactionSchema),\n    defaultValues: {\n      interactionType: defaultType as any || 'phone-call',\n      title: '',\n      notes: '',\n      outcome: '',\n      followUpRequired: false,\n      followUpDate: '',\n      priority: 'medium',\n      status: 'open',\n      relatedAppointment: '',\n      relatedBill: ''\n    }\n  });\n\n  // Reset form when dialog opens/closes or interaction changes\n  useEffect(() => {\n    if (open) {\n      if (interaction) {\n        form.reset({\n          interactionType: interaction.interactionType,\n          title: interaction.title,\n          notes: interaction.notes,\n          outcome: interaction.outcome || '',\n          followUpRequired: interaction.followUpRequired,\n          followUpDate: interaction.followUpDate || '',\n          priority: interaction.priority,\n          status: interaction.status,\n          relatedAppointment: typeof interaction.relatedAppointment === 'string' ? interaction.relatedAppointment : '',\n          relatedBill: typeof interaction.relatedBill === 'string' ? interaction.relatedBill : ''\n        });\n        if (interaction.followUpDate) {\n          const date = new Date(interaction.followUpDate);\n          setFollowUpDate(date);\n          setFollowUpTime(date.toTimeString().slice(0, 5));\n        }\n      } else {\n        form.reset({\n          interactionType: defaultType as any || 'phone-call',\n          title: '',\n          notes: '',\n          outcome: '',\n          followUpRequired: false,\n          followUpDate: '',\n          priority: 'medium',\n          status: 'open',\n          relatedAppointment: '',\n          relatedBill: ''\n        });\n        setFollowUpDate(undefined);\n        setFollowUpTime('09:00');\n      }\n    }\n  }, [open, interaction, defaultType, form]);\n  const handleSubmit = async (data: InteractionFormData) => {\n    try {\n      // Combine follow-up date and time\n      if (data.followUpRequired && followUpDate) {\n        const [hours, minutes] = followUpTime.split(':');\n        const combinedDate = new Date(followUpDate);\n        combinedDate.setHours(parseInt(hours), parseInt(minutes));\n        data.followUpDate = combinedDate.toISOString();\n      }\n      const formData: PatientInteractionFormData = {\n        ...data,\n        patient: patientId\n      };\n      await onSubmit(formData);\n      onOpenChange(false);\n    } catch (error) {\n      console.error('Error submitting interaction:', error);\n    }\n  };\n  const watchFollowUpRequired = form.watch('followUpRequired');\n  return <Dialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"Dialog\" data-sentry-component=\"InteractionFormDialog\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n            {isEditing ? '编辑互动记录' : '添加互动记录'}\n          </DialogTitle>\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n            为患者 <strong>{patientName}</strong> {isEditing ? '编辑' : '创建'}互动记录\n          </DialogDescription>\n        </DialogHeader>\n\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n          <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\n            {/* Interaction Type */}\n            <FormField control={form.control} name=\"interactionType\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>互动类型 *</FormLabel>\n                  <Select onValueChange={field.onChange} defaultValue={field.value}>\n                    <FormControl>\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"选择互动类型\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent>\n                      {interactionTypeOptions.map(option => <SelectItem key={option.value} value={option.value}>\n                          <div>\n                            <div className=\"font-medium\">{option.label}</div>\n                            <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                          </div>\n                        </SelectItem>)}\n                    </SelectContent>\n                  </Select>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n\n            {/* Title */}\n            <FormField control={form.control} name=\"title\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>互动标题 *</FormLabel>\n                  <FormControl>\n                    <Input placeholder=\"简要描述此次互动的主题\" {...field} />\n                  </FormControl>\n                  <FormDescription>\n                    请输入简洁明了的标题，方便后续查找\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n\n            {/* Notes */}\n            <FormField control={form.control} name=\"notes\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>详细记录 *</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder=\"详细记录互动内容、讨论要点、患者反馈等...\" className=\"min-h-[120px]\" {...field} />\n                  </FormControl>\n                  <FormDescription>\n                    请详细记录互动的具体内容和重要信息\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n\n            {/* Outcome */}\n            <FormField control={form.control} name=\"outcome\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>互动结果</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder=\"记录互动的结果、解决方案或后续安排...\" className=\"min-h-[80px]\" {...field} />\n                  </FormControl>\n                  <FormDescription>\n                    记录此次互动达成的结果或解决方案\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n\n            {/* Priority and Status */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <FormField control={form.control} name=\"priority\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>优先级</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {priorityOptions.map(option => <SelectItem key={option.value} value={option.value}>\n                            <div>\n                              <div className=\"font-medium\">{option.label}</div>\n                              <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                            </div>\n                          </SelectItem>)}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n\n              <FormField control={form.control} name=\"status\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>状态</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {statusOptions.map(option => <SelectItem key={option.value} value={option.value}>\n                            <div>\n                              <div className=\"font-medium\">{option.label}</div>\n                              <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                            </div>\n                          </SelectItem>)}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n            </div>\n\n            {/* Follow-up */}\n            <div className=\"space-y-4\">\n              <FormField control={form.control} name=\"followUpRequired\" render={({\n              field\n            }) => <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\n                    <FormControl>\n                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />\n                    </FormControl>\n                    <div className=\"space-y-1 leading-none\">\n                      <FormLabel>需要跟进</FormLabel>\n                      <FormDescription>\n                        勾选此项将自动创建跟进任务\n                      </FormDescription>\n                    </div>\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"interaction-form-dialog.tsx\" />\n\n              {watchFollowUpRequired && <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <FormLabel>跟进日期</FormLabel>\n                    <Popover>\n                      <PopoverTrigger asChild>\n                        <Button variant=\"outline\" className={cn(\"w-full justify-start text-left font-normal\", !followUpDate && \"text-muted-foreground\")}>\n                          <IconCalendar className=\"mr-2 h-4 w-4\" />\n                          {followUpDate ? formatDateTime(followUpDate) : \"选择日期\"}\n                        </Button>\n                      </PopoverTrigger>\n                      <PopoverContent className=\"w-auto p-0\" align=\"start\">\n                        <Calendar mode=\"single\" selected={followUpDate} onSelect={setFollowUpDate} disabled={date => date < new Date()} initialFocus />\n                      </PopoverContent>\n                    </Popover>\n                  </div>\n\n                  <div>\n                    <FormLabel>跟进时间</FormLabel>\n                    <div className=\"relative\">\n                      <IconClock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                      <Input type=\"time\" value={followUpTime} onChange={e => setFollowUpTime(e.target.value)} className=\"pl-10\" />\n                    </div>\n                  </div>\n                </div>}\n            </div>\n\n            <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)} disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"interaction-form-dialog.tsx\">\n                {loading ? '保存中...' : isEditing ? '更新' : '创建'}\n              </Button>\n            </DialogFooter>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Button } from '@/components/ui/button';\nimport { Calendar } from '@/components/ui/calendar';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { IconCalendar, IconClock, IconUser } from '@/components/icons';\nimport { PatientTask, PatientTaskFormData, User } from '@/types/clinic';\nimport { formatDateTime } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\nconst taskSchema = z.object({\n  taskType: z.enum(['follow-up-call', 'appointment-scheduling', 'treatment-reminder', 'billing-follow-up', 'medical-record-update', 'consultation-follow-up']),\n  title: z.string().min(1, '请输入任务标题').max(200, '标题不能超过200个字符'),\n  description: z.string().optional(),\n  assignedTo: z.string().min(1, '请选择负责人'),\n  dueDate: z.string().min(1, '请选择截止日期'),\n  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),\n  status: z.enum(['pending', 'in-progress', 'completed', 'cancelled']).default('pending'),\n  relatedInteraction: z.string().optional(),\n  completionNotes: z.string().optional()\n});\ntype TaskFormData = z.infer<typeof taskSchema>;\ninterface TaskFormDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  patientId: string;\n  patientName: string;\n  task?: PatientTask;\n  defaultType?: string;\n  relatedInteractionId?: string;\n  availableStaff: User[];\n  onSubmit: (data: PatientTaskFormData) => Promise<void>;\n  loading?: boolean;\n}\nconst taskTypeOptions = [{\n  value: 'follow-up-call',\n  label: '跟进电话',\n  description: '安排跟进电话任务'\n}, {\n  value: 'appointment-scheduling',\n  label: '预约安排',\n  description: '安排预约相关任务'\n}, {\n  value: 'treatment-reminder',\n  label: '治疗提醒',\n  description: '创建治疗提醒任务'\n}, {\n  value: 'billing-follow-up',\n  label: '账单跟进',\n  description: '创建账单跟进任务'\n}, {\n  value: 'medical-record-update',\n  label: '病历更新',\n  description: '安排病历更新任务'\n}, {\n  value: 'consultation-follow-up',\n  label: '咨询跟进',\n  description: '创建咨询跟进任务'\n}];\nconst priorityOptions = [{\n  value: 'low',\n  label: '低',\n  description: '一般重要性',\n  color: 'text-gray-600'\n}, {\n  value: 'medium',\n  label: '中',\n  description: '中等重要性',\n  color: 'text-yellow-600'\n}, {\n  value: 'high',\n  label: '高',\n  description: '高重要性，需要优先处理',\n  color: 'text-orange-600'\n}, {\n  value: 'urgent',\n  label: '紧急',\n  description: '紧急任务，需要立即处理',\n  color: 'text-red-600'\n}];\nconst statusOptions = [{\n  value: 'pending',\n  label: '待处理',\n  description: '新创建的任务'\n}, {\n  value: 'in-progress',\n  label: '进行中',\n  description: '正在处理中'\n}, {\n  value: 'completed',\n  label: '已完成',\n  description: '任务已完成'\n}, {\n  value: 'cancelled',\n  label: '已取消',\n  description: '任务已取消'\n}];\nexport function TaskFormDialog({\n  open,\n  onOpenChange,\n  patientId,\n  patientName,\n  task,\n  defaultType,\n  relatedInteractionId,\n  availableStaff,\n  onSubmit,\n  loading = false\n}: TaskFormDialogProps) {\n  const [dueDate, setDueDate] = useState<Date | undefined>();\n  const [dueTime, setDueTime] = useState('09:00');\n  const isEditing = !!task;\n  const form = useForm<TaskFormData>({\n    resolver: zodResolver(taskSchema),\n    defaultValues: {\n      taskType: defaultType as any || 'follow-up-call',\n      title: '',\n      description: '',\n      assignedTo: '',\n      dueDate: '',\n      priority: 'medium',\n      status: 'pending',\n      relatedInteraction: relatedInteractionId || '',\n      completionNotes: ''\n    }\n  });\n\n  // Reset form when dialog opens/closes or task changes\n  useEffect(() => {\n    if (open) {\n      if (task) {\n        form.reset({\n          taskType: task.taskType,\n          title: task.title,\n          description: task.description || '',\n          assignedTo: typeof task.assignedTo === 'string' ? task.assignedTo : task.assignedTo.id,\n          dueDate: task.dueDate,\n          priority: task.priority,\n          status: task.status,\n          relatedInteraction: typeof task.relatedInteraction === 'string' ? task.relatedInteraction : task.relatedInteraction?.id || '',\n          completionNotes: task.completionNotes || ''\n        });\n        const date = new Date(task.dueDate);\n        setDueDate(date);\n        setDueTime(date.toTimeString().slice(0, 5));\n      } else {\n        // Set default due date to tomorrow\n        const tomorrow = new Date();\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        setDueDate(tomorrow);\n        form.reset({\n          taskType: defaultType as any || 'follow-up-call',\n          title: '',\n          description: '',\n          assignedTo: '',\n          dueDate: '',\n          priority: 'medium',\n          status: 'pending',\n          relatedInteraction: relatedInteractionId || '',\n          completionNotes: ''\n        });\n        setDueTime('09:00');\n      }\n    }\n  }, [open, task, defaultType, relatedInteractionId, form]);\n  const handleSubmit = async (data: TaskFormData) => {\n    try {\n      // Combine due date and time\n      if (dueDate) {\n        const [hours, minutes] = dueTime.split(':');\n        const combinedDate = new Date(dueDate);\n        combinedDate.setHours(parseInt(hours), parseInt(minutes));\n        data.dueDate = combinedDate.toISOString();\n      }\n      const formData: PatientTaskFormData = {\n        ...data,\n        patient: patientId\n      };\n      await onSubmit(formData);\n      onOpenChange(false);\n    } catch (error) {\n      console.error('Error submitting task:', error);\n    }\n  };\n  const watchStatus = form.watch('status');\n  const watchTaskType = form.watch('taskType');\n\n  // Generate default title based on task type\n  useEffect(() => {\n    if (!isEditing && watchTaskType) {\n      const typeConfig = taskTypeOptions.find(opt => opt.value === watchTaskType);\n      if (typeConfig) {\n        form.setValue('title', `${typeConfig.label} - ${patientName}`);\n      }\n    }\n  }, [watchTaskType, patientName, isEditing, form]);\n  return <Dialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"Dialog\" data-sentry-component=\"TaskFormDialog\" data-sentry-source-file=\"task-form-dialog.tsx\">\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"task-form-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"task-form-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"task-form-dialog.tsx\">\n            {isEditing ? '编辑任务' : '创建任务'}\n          </DialogTitle>\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"task-form-dialog.tsx\">\n            为患者 <strong>{patientName}</strong> {isEditing ? '编辑' : '创建'}跟进任务\n          </DialogDescription>\n        </DialogHeader>\n\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"task-form-dialog.tsx\">\n          <form onSubmit={form.handleSubmit(handleSubmit)} className=\"space-y-6\">\n            {/* Task Type */}\n            <FormField control={form.control} name=\"taskType\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>任务类型 *</FormLabel>\n                  <Select onValueChange={field.onChange} defaultValue={field.value}>\n                    <FormControl>\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"选择任务类型\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent>\n                      {taskTypeOptions.map(option => <SelectItem key={option.value} value={option.value}>\n                          <div>\n                            <div className=\"font-medium\">{option.label}</div>\n                            <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                          </div>\n                        </SelectItem>)}\n                    </SelectContent>\n                  </Select>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n\n            {/* Title */}\n            <FormField control={form.control} name=\"title\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>任务标题 *</FormLabel>\n                  <FormControl>\n                    <Input placeholder=\"简要描述任务内容\" {...field} />\n                  </FormControl>\n                  <FormDescription>\n                    请输入简洁明了的任务标题\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n\n            {/* Description */}\n            <FormField control={form.control} name=\"description\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>任务描述</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder=\"详细描述任务要求和注意事项...\" className=\"min-h-[100px]\" {...field} />\n                  </FormControl>\n                  <FormDescription>\n                    详细描述任务的具体要求和执行步骤\n                  </FormDescription>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n\n            {/* Assigned To */}\n            <FormField control={form.control} name=\"assignedTo\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>分配给 *</FormLabel>\n                  <Select onValueChange={field.onChange} defaultValue={field.value}>\n                    <FormControl>\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"选择负责人\">\n                          <div className=\"flex items-center gap-2\">\n                            <IconUser className=\"size-4\" />\n                            <span>选择负责人</span>\n                          </div>\n                        </SelectValue>\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent>\n                      {availableStaff.map(staff => <SelectItem key={staff.id} value={staff.id}>\n                          <div className=\"flex items-center gap-2\">\n                            <IconUser className=\"size-4\" />\n                            <div>\n                              <div className=\"font-medium\">\n                                {`${staff.firstName || ''} ${staff.lastName || ''}`.trim() || staff.email}\n                              </div>\n                              <div className=\"text-xs text-muted-foreground\">\n                                {staff.role === 'admin' && '管理员'}\n                                {staff.role === 'doctor' && '医生'}\n                                {staff.role === 'front-desk' && '前台'}\n                              </div>\n                            </div>\n                          </div>\n                        </SelectItem>)}\n                    </SelectContent>\n                  </Select>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n\n            {/* Due Date and Time */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div>\n                <FormLabel data-sentry-element=\"FormLabel\" data-sentry-source-file=\"task-form-dialog.tsx\">截止日期 *</FormLabel>\n                <Popover data-sentry-element=\"Popover\" data-sentry-source-file=\"task-form-dialog.tsx\">\n                  <PopoverTrigger asChild data-sentry-element=\"PopoverTrigger\" data-sentry-source-file=\"task-form-dialog.tsx\">\n                    <Button variant=\"outline\" className={cn(\"w-full justify-start text-left font-normal\", !dueDate && \"text-muted-foreground\")} data-sentry-element=\"Button\" data-sentry-source-file=\"task-form-dialog.tsx\">\n                      <IconCalendar className=\"mr-2 h-4 w-4\" data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n                      {dueDate ? formatDateTime(dueDate) : \"选择日期\"}\n                    </Button>\n                  </PopoverTrigger>\n                  <PopoverContent className=\"w-auto p-0\" align=\"start\" data-sentry-element=\"PopoverContent\" data-sentry-source-file=\"task-form-dialog.tsx\">\n                    <Calendar mode=\"single\" selected={dueDate} onSelect={setDueDate} disabled={date => date < new Date()} initialFocus data-sentry-element=\"Calendar\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n                  </PopoverContent>\n                </Popover>\n              </div>\n\n              <div>\n                <FormLabel data-sentry-element=\"FormLabel\" data-sentry-source-file=\"task-form-dialog.tsx\">截止时间</FormLabel>\n                <div className=\"relative\">\n                  <IconClock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" data-sentry-element=\"IconClock\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n                  <Input type=\"time\" value={dueTime} onChange={e => setDueTime(e.target.value)} className=\"pl-10\" data-sentry-element=\"Input\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n                </div>\n              </div>\n            </div>\n\n            {/* Priority and Status */}\n            <div className=\"grid grid-cols-2 gap-4\">\n              <FormField control={form.control} name=\"priority\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>优先级</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {priorityOptions.map(option => <SelectItem key={option.value} value={option.value}>\n                            <div>\n                              <div className={cn(\"font-medium\", option.color)}>{option.label}</div>\n                              <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                            </div>\n                          </SelectItem>)}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n\n              <FormField control={form.control} name=\"status\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>状态</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {statusOptions.map(option => <SelectItem key={option.value} value={option.value}>\n                            <div>\n                              <div className=\"font-medium\">{option.label}</div>\n                              <div className=\"text-xs text-muted-foreground\">{option.description}</div>\n                            </div>\n                          </SelectItem>)}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"task-form-dialog.tsx\" />\n            </div>\n\n            {/* Completion Notes - only show if status is completed */}\n            {watchStatus === 'completed' && <FormField control={form.control} name=\"completionNotes\" render={({\n            field\n          }) => <FormItem>\n                    <FormLabel>完成备注</FormLabel>\n                    <FormControl>\n                      <Textarea placeholder=\"记录任务完成情况和总结...\" className=\"min-h-[80px]\" {...field} />\n                    </FormControl>\n                    <FormDescription>\n                      记录任务完成的具体情况和总结\n                    </FormDescription>\n                    <FormMessage />\n                  </FormItem>} />}\n\n            <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"task-form-dialog.tsx\">\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)} disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"task-form-dialog.tsx\">\n                取消\n              </Button>\n              <Button type=\"submit\" disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"task-form-dialog.tsx\">\n                {loading ? '保存中...' : isEditing ? '更新' : '创建'}\n              </Button>\n            </DialogFooter>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>;\n}", "// Comprehensive toast notification utilities for CRM actions\n// Provides consistent messaging in Chinese for all CRM operations\n\nimport { toast } from 'sonner';\nimport { PatientInteraction, PatientTask, User } from '@/types/clinic';\nimport { formatDateTime } from '@/lib/utils';\n\nexport const crmNotifications = {\n  // Patient Interaction notifications\n  interaction: {\n    created: (interaction: PatientInteraction) => {\n      const typeLabels = {\n        'phone-call': '电话通话',\n        'email': '邮件沟通',\n        'consultation-note': '咨询记录',\n        'in-person-visit': '到院就诊',\n        'treatment-discussion': '治疗讨论',\n        'billing-inquiry': '账单咨询',\n      };\n      \n      const typeLabel = typeLabels[interaction.interactionType] || '互动记录';\n      toast.success(`${typeLabel}创建成功！`, {\n        description: `标题: ${interaction.title}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (interaction: PatientInteraction) => {\n      toast.success(`互动记录更新成功！`, {\n        description: `标题: ${interaction.title}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (interactionTitle: string) => {\n      toast.success(`互动记录删除成功！`, {\n        description: `已删除: ${interactionTitle}`,\n        duration: 4000,\n      });\n    },\n\n    followUpCreated: (interaction: PatientInteraction) => {\n      toast.info(`跟进任务已自动创建`, {\n        description: `基于互动: ${interaction.title}`,\n        duration: 5000,\n      });\n    },\n\n    statusChanged: (interaction: PatientInteraction, oldStatus: string, newStatus: string) => {\n      const statusLabels = {\n        'open': '开放',\n        'in-progress': '进行中',\n        'resolved': '已解决',\n        'closed': '已关闭',\n      };\n      \n      toast.success(`互动状态已更新`, {\n        description: `${interaction.title}: ${statusLabels[oldStatus as keyof typeof statusLabels]} → ${statusLabels[newStatus as keyof typeof statusLabels]}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // Patient Task notifications\n  task: {\n    created: (task: PatientTask) => {\n      const typeLabels = {\n        'follow-up-call': '跟进电话',\n        'appointment-scheduling': '预约安排',\n        'treatment-reminder': '治疗提醒',\n        'billing-follow-up': '账单跟进',\n        'medical-record-update': '病历更新',\n        'consultation-follow-up': '咨询跟进',\n      };\n      \n      const typeLabel = typeLabels[task.taskType] || '任务';\n      toast.success(`${typeLabel}任务创建成功！`, {\n        description: `标题: ${task.title}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (task: PatientTask) => {\n      toast.success(`任务更新成功！`, {\n        description: `标题: ${task.title}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (taskTitle: string) => {\n      toast.success(`任务删除成功！`, {\n        description: `已删除: ${taskTitle}`,\n        duration: 4000,\n      });\n    },\n\n    assigned: (task: PatientTask, assignedTo: User) => {\n      const assigneeName = `${assignedTo.firstName || ''} ${assignedTo.lastName || ''}`.trim() || assignedTo.email;\n      toast.info(`任务已分配`, {\n        description: `${task.title} → ${assigneeName}`,\n        duration: 4000,\n      });\n    },\n\n    statusChanged: (task: PatientTask, oldStatus: string, newStatus: string) => {\n      const statusLabels = {\n        'pending': '待处理',\n        'in-progress': '进行中',\n        'completed': '已完成',\n        'cancelled': '已取消',\n      };\n      \n      const statusEmojis = {\n        'pending': '⏳',\n        'in-progress': '🔄',\n        'completed': '✅',\n        'cancelled': '❌',\n      };\n      \n      toast.success(`任务状态已更新`, {\n        description: `${statusEmojis[newStatus as keyof typeof statusEmojis]} ${task.title}: ${statusLabels[oldStatus as keyof typeof statusLabels]} → ${statusLabels[newStatus as keyof typeof statusLabels]}`,\n        duration: 4000,\n      });\n    },\n\n    completed: (task: PatientTask) => {\n      toast.success(`🎉 任务完成！`, {\n        description: `${task.title} 已标记为完成`,\n        duration: 5000,\n      });\n    },\n\n    overdue: (task: PatientTask) => {\n      toast.warning(`⚠️ 任务已逾期`, {\n        description: `${task.title} - 截止时间: ${formatDateTime(new Date(task.dueDate))}`,\n        duration: 8000,\n      });\n    },\n\n    reminder: (task: PatientTask, minutesUntil: number) => {\n      const timeText = minutesUntil < 60 \n        ? `${minutesUntil} 分钟后`\n        : `${Math.floor(minutesUntil / 60)} 小时后`;\n        \n      toast.info(`📅 任务提醒`, {\n        description: `${task.title} 将在 ${timeText} 到期`,\n        duration: 6000,\n      });\n    },\n  },\n\n  // Bulk operations\n  bulk: {\n    interactionsCreated: (count: number) => {\n      toast.success(`批量创建互动记录成功`, {\n        description: `已创建 ${count} 条互动记录`,\n        duration: 4000,\n      });\n    },\n\n    tasksCreated: (count: number) => {\n      toast.success(`批量创建任务成功`, {\n        description: `已创建 ${count} 个任务`,\n        duration: 4000,\n      });\n    },\n\n    tasksAssigned: (count: number, assignee: User) => {\n      const assigneeName = `${assignee.firstName || ''} ${assignee.lastName || ''}`.trim() || assignee.email;\n      toast.success(`批量分配任务成功`, {\n        description: `已将 ${count} 个任务分配给 ${assigneeName}`,\n        duration: 4000,\n      });\n    },\n\n    statusUpdated: (count: number, status: string) => {\n      const statusLabels = {\n        'pending': '待处理',\n        'in-progress': '进行中',\n        'completed': '已完成',\n        'cancelled': '已取消',\n        'open': '开放',\n        'resolved': '已解决',\n        'closed': '已关闭',\n      };\n      \n      toast.success(`批量状态更新成功`, {\n        description: `${count} 个项目已更新为 ${statusLabels[status as keyof typeof statusLabels]}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // Timeline and communication log\n  timeline: {\n    loaded: (count: number) => {\n      toast.success(`时间线加载完成`, {\n        description: `显示 ${count} 条记录`,\n        duration: 2000,\n      });\n    },\n\n    filtered: (totalCount: number, filteredCount: number) => {\n      toast.info(`筛选结果`, {\n        description: `从 ${totalCount} 条记录中筛选出 ${filteredCount} 条`,\n        duration: 3000,\n      });\n    },\n\n    exported: (format: string, count: number) => {\n      toast.success(`导出成功`, {\n        description: `已导出 ${count} 条记录为 ${format} 格式`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // Quick actions\n  quickAction: {\n    callInitiated: (patientName: string) => {\n      toast.info(`📞 准备联系患者`, {\n        description: `正在为 ${patientName} 创建通话记录`,\n        duration: 3000,\n      });\n    },\n\n    appointmentScheduled: (patientName: string) => {\n      toast.success(`📅 预约创建成功`, {\n        description: `已为 ${patientName} 创建预约`,\n        duration: 4000,\n      });\n    },\n\n    followUpScheduled: (patientName: string, dueDate: string) => {\n      toast.success(`⏰ 跟进任务已安排`, {\n        description: `${patientName} - 跟进时间: ${formatDateTime(new Date(dueDate))}`,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Error notifications\n  error: {\n    interactionCreateFailed: (error?: string) => {\n      toast.error(`互动记录创建失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    taskCreateFailed: (error?: string) => {\n      toast.error(`任务创建失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    updateFailed: (itemType: string, error?: string) => {\n      const typeLabels = {\n        'interaction': '互动记录',\n        'task': '任务',\n      };\n      \n      toast.error(`${typeLabels[itemType as keyof typeof typeLabels] || '项目'}更新失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    deleteFailed: (itemType: string, error?: string) => {\n      const typeLabels = {\n        'interaction': '互动记录',\n        'task': '任务',\n      };\n      \n      toast.error(`${typeLabels[itemType as keyof typeof typeLabels] || '项目'}删除失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    loadFailed: (dataType: string, error?: string) => {\n      const typeLabels = {\n        'interactions': '互动记录',\n        'tasks': '任务',\n        'timeline': '时间线',\n      };\n      \n      toast.error(`${typeLabels[dataType as keyof typeof typeLabels] || '数据'}加载失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    permissionDenied: (action: string) => {\n      toast.error(`权限不足`, {\n        description: `您没有权限执行: ${action}`,\n        duration: 5000,\n      });\n    },\n\n    networkError: () => {\n      toast.error(`网络连接失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n  },\n\n  // Success notifications\n  success: {\n    dataRefreshed: (dataType: string) => {\n      const typeLabels = {\n        'interactions': '互动记录',\n        'tasks': '任务',\n        'timeline': '时间线',\n      };\n      \n      toast.success(`${typeLabels[dataType as keyof typeof typeLabels] || '数据'}刷新成功`, {\n        duration: 2000,\n      });\n    },\n\n    syncCompleted: () => {\n      toast.success(`数据同步完成`, {\n        description: '所有CRM数据已同步到最新状态',\n        duration: 3000,\n      });\n    },\n  },\n};\n\n// Utility function to dismiss all CRM-related toasts\nexport const dismissCrmToasts = () => {\n  toast.dismiss();\n};\n\n// Helper function to show loading toast\nexport const showCrmLoadingToast = (message: string) => {\n  return toast.loading(message, {\n    duration: Infinity, // Will be dismissed manually\n  });\n};\n", "'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Separator } from '@/components/ui/separator';\nimport { IconArrowLeft, IconUser, IconPhone, IconMail, IconCalendar, IconClock, IconEdit, IconMessageCircle, IconFileText, IconActivity, IconTrendingUp } from '@/components/icons';\nimport { InteractionTimeline } from '@/components/crm/interaction-timeline';\nimport { TaskManager } from '@/components/crm/task-manager';\nimport { QuickActions } from '@/components/crm/quick-actions';\nimport { CommunicationLog } from '@/components/crm/communication-log';\nimport { InteractionFormDialog } from '@/components/crm/interaction-form-dialog';\nimport { TaskFormDialog } from '@/components/crm/task-form-dialog';\nimport { Patient, PatientInteraction, PatientTask, TimelineItem, User, PatientInteractionFormData, PatientTaskFormData } from '@/types/clinic';\nimport { createPayloadClient } from '@/lib/payload-client';\nimport { useUser } from '@clerk/nextjs';\nimport { crmNotifications, showCrmLoadingToast } from '@/lib/crm-notifications';\nimport { toast } from 'sonner';\nimport { formatDateTime, formatRelativeTime } from '@/lib/utils';\nimport { cn } from '@/lib/utils';\ninterface PatientDetailViewProps {\n  patientId: string;\n}\nexport default function PatientDetailView({\n  patientId\n}: PatientDetailViewProps) {\n  const router = useRouter();\n  const {\n    user\n  } = useUser();\n  const [patient, setPatient] = useState<Patient | null>(null);\n  const [interactions, setInteractions] = useState<PatientInteraction[]>([]);\n  const [tasks, setTasks] = useState<PatientTask[]>([]);\n  const [timeline, setTimeline] = useState<TimelineItem[]>([]);\n  const [availableStaff, setAvailableStaff] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Dialog states\n  const [interactionDialogOpen, setInteractionDialogOpen] = useState(false);\n  const [taskDialogOpen, setTaskDialogOpen] = useState(false);\n  const [defaultInteractionType, setDefaultInteractionType] = useState<string>('');\n  const [defaultTaskType, setDefaultTaskType] = useState<string>('');\n  const [editingInteraction, setEditingInteraction] = useState<PatientInteraction | undefined>();\n  const [editingTask, setEditingTask] = useState<PatientTask | undefined>();\n  const [formLoading, setFormLoading] = useState(false);\n\n  // Load patient data and CRM information\n  useEffect(() => {\n    const loadPatientData = async () => {\n      if (!user) return;\n      const loadingToast = showCrmLoadingToast('加载患者信息...');\n      try {\n        const payloadClient = createPayloadClient({\n          clerkId: user.id,\n          email: user.emailAddresses[0]?.emailAddress || '',\n          firstName: user.firstName || '',\n          lastName: user.lastName || ''\n        });\n\n        // Load patient basic info\n        const patientData = await payloadClient.getPatient(patientId);\n        setPatient(patientData as Patient);\n\n        // Load interactions\n        const interactionsData = await payloadClient.getPatientInteractionsByPatient(patientId, {\n          limit: 50\n        });\n        setInteractions((interactionsData as any).docs || []);\n\n        // Load tasks\n        const tasksData = await payloadClient.getPatientTasksByPatient(patientId, {\n          limit: 50\n        });\n        setTasks((tasksData as any).docs || []);\n\n        // Load combined timeline\n        const timelineData = await payloadClient.getPatientTimeline(patientId, {\n          limit: 100\n        });\n        setTimeline((timelineData as any).docs || []);\n\n        // Load available staff for task assignment\n        const staffData = await payloadClient.getUsers({\n          limit: 50\n        });\n        setAvailableStaff((staffData as any).docs || []);\n        crmNotifications.success.dataRefreshed('患者信息');\n      } catch (error) {\n        console.error('Error loading patient data:', error);\n        crmNotifications.error.loadFailed('患者信息', error instanceof Error ? error.message : undefined);\n      } finally {\n        setLoading(false);\n        toast.dismiss(loadingToast);\n      }\n    };\n    loadPatientData();\n  }, [patientId, user]);\n  const handleCreateInteraction = (type?: string) => {\n    setDefaultInteractionType(type || '');\n    setEditingInteraction(undefined);\n    setInteractionDialogOpen(true);\n  };\n  const handleCreateTask = (type?: string) => {\n    setDefaultTaskType(type || '');\n    setEditingTask(undefined);\n    setTaskDialogOpen(true);\n  };\n  const handleSubmitInteraction = async (data: PatientInteractionFormData) => {\n    setFormLoading(true);\n    try {\n      const payloadClient = createPayloadClient({\n        clerkId: user!.id,\n        email: user!.emailAddresses[0]?.emailAddress || '',\n        firstName: user!.firstName || '',\n        lastName: user!.lastName || ''\n      });\n      if (editingInteraction) {\n        const updated = await payloadClient.updatePatientInteraction(editingInteraction.id, data);\n        setInteractions(prev => prev.map(item => item.id === editingInteraction.id ? updated as PatientInteraction : item));\n        crmNotifications.interaction.updated(updated as PatientInteraction);\n      } else {\n        const created = await payloadClient.createPatientInteraction(data);\n        setInteractions(prev => [created as PatientInteraction, ...prev]);\n        crmNotifications.interaction.created(created as PatientInteraction);\n      }\n\n      // Refresh timeline\n      const timelineData = await payloadClient.getPatientTimeline(patientId, {\n        limit: 100\n      });\n      setTimeline(timelineData.docs || []);\n    } catch (error) {\n      console.error('Error submitting interaction:', error);\n      crmNotifications.error.interactionCreateFailed(error instanceof Error ? error.message : undefined);\n    } finally {\n      setFormLoading(false);\n    }\n  };\n  const handleSubmitTask = async (data: PatientTaskFormData) => {\n    setFormLoading(true);\n    try {\n      const payloadClient = createPayloadClient({\n        clerkId: user!.id,\n        email: user!.emailAddresses[0]?.emailAddress || '',\n        firstName: user!.firstName || '',\n        lastName: user!.lastName || ''\n      });\n      if (editingTask) {\n        const updated = await payloadClient.updatePatientTask(editingTask.id, data);\n        setTasks(prev => prev.map(item => item.id === editingTask.id ? updated : item));\n        crmNotifications.task.updated(updated);\n      } else {\n        const created = await payloadClient.createPatientTask(data);\n        setTasks(prev => [created, ...prev]);\n        crmNotifications.task.created(created);\n      }\n\n      // Refresh timeline\n      const timelineData = await payloadClient.getPatientTimeline(patientId, {\n        limit: 100\n      });\n      setTimeline(timelineData.docs || []);\n    } catch (error) {\n      console.error('Error submitting task:', error);\n      crmNotifications.error.taskCreateFailed(error instanceof Error ? error.message : undefined);\n    } finally {\n      setFormLoading(false);\n    }\n  };\n  const handleScheduleAppointment = () => {\n    // TODO: Navigate to appointment creation\n    router.push(`/dashboard/appointments/new?patientId=${patientId}`);\n  };\n  const handleViewBilling = () => {\n    // TODO: Navigate to billing page\n    router.push(`/dashboard/billing?patientId=${patientId}`);\n  };\n  const handleTaskStatusChange = async (taskId: string, status: PatientTask['status']) => {\n    try {\n      const payloadClient = createPayloadClient({\n        clerkId: user!.id,\n        email: user!.emailAddresses[0]?.emailAddress || '',\n        firstName: user!.firstName || '',\n        lastName: user!.lastName || ''\n      });\n      await payloadClient.updatePatientTask(taskId, {\n        status\n      });\n\n      // Update local state\n      setTasks(prev => prev.map(task => task.id === taskId ? {\n        ...task,\n        status\n      } : task));\n      const task = tasks.find(t => t.id === taskId);\n      if (task) {\n        crmNotifications.task.statusChanged(task, task.status, status);\n      }\n    } catch (error) {\n      console.error('Error updating task status:', error);\n      crmNotifications.error.updateFailed('task', error instanceof Error ? error.message : undefined);\n    }\n  };\n  if (loading) {\n    return <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-gray-200 rounded w-1/4 mb-4\" />\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n            <div className=\"lg:col-span-2 space-y-4\">\n              <div className=\"h-64 bg-gray-200 rounded\" />\n              <div className=\"h-96 bg-gray-200 rounded\" />\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"h-48 bg-gray-200 rounded\" />\n              <div className=\"h-64 bg-gray-200 rounded\" />\n            </div>\n          </div>\n        </div>\n      </div>;\n  }\n  if (!patient) {\n    return <div className=\"text-center py-12\">\n        <IconUser className=\"size-12 mx-auto mb-4 text-muted-foreground\" />\n        <h3 className=\"text-lg font-medium mb-2\">患者未找到</h3>\n        <p className=\"text-muted-foreground mb-4\">请检查患者ID是否正确</p>\n        <Button onClick={() => router.back()}>\n          <IconArrowLeft className=\"size-4 mr-2\" />\n          返回\n        </Button>\n      </div>;\n  }\n\n  // Calculate summary statistics\n  const totalInteractions = interactions.length;\n  const pendingTasks = tasks.filter(task => task.status === 'pending').length;\n  const overdueTasks = tasks.filter(task => task.status !== 'completed' && new Date(task.dueDate) < new Date()).length;\n  const lastInteraction = interactions[0]; // Assuming sorted by timestamp desc\n\n  return <div className=\"space-y-6\" data-sentry-component=\"PatientDetailView\" data-sentry-source-file=\"patient-detail-view.tsx\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-4\">\n          <Button variant=\"ghost\" size=\"sm\" onClick={() => router.back()} data-sentry-element=\"Button\" data-sentry-source-file=\"patient-detail-view.tsx\">\n            <IconArrowLeft className=\"size-4 mr-2\" data-sentry-element=\"IconArrowLeft\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n            返回\n          </Button>\n          <div>\n            <h1 className=\"text-2xl font-bold\">{patient.fullName}</h1>\n            <p className=\"text-muted-foreground\">患者详细信息</p>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <QuickActions patient={patient} onCreateInteraction={handleCreateInteraction} onCreateTask={handleCreateTask} onScheduleAppointment={handleScheduleAppointment} onViewBilling={handleViewBilling} compact data-sentry-element=\"QuickActions\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n          <Button variant=\"outline\" size=\"sm\" data-sentry-element=\"Button\" data-sentry-source-file=\"patient-detail-view.tsx\">\n            <IconEdit className=\"size-4 mr-2\" data-sentry-element=\"IconEdit\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n            编辑\n          </Button>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Left Column - Main Content */}\n        <div className=\"lg:col-span-2\">\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\" data-sentry-element=\"Tabs\" data-sentry-source-file=\"patient-detail-view.tsx\">\n            <TabsList className=\"grid w-full grid-cols-4\" data-sentry-element=\"TabsList\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              <TabsTrigger value=\"overview\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"patient-detail-view.tsx\">概览</TabsTrigger>\n              <TabsTrigger value=\"interactions\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                互动记录 ({totalInteractions})\n              </TabsTrigger>\n              <TabsTrigger value=\"tasks\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                任务管理 ({tasks.length})\n              </TabsTrigger>\n              <TabsTrigger value=\"timeline\" data-sentry-element=\"TabsTrigger\" data-sentry-source-file=\"patient-detail-view.tsx\">时间线</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"overview\" className=\"space-y-6\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              {/* Patient Basic Info */}\n              <Card data-sentry-element=\"Card\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                  <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                    <IconUser className=\"size-5\" data-sentry-element=\"IconUser\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n                    基本信息\n                  </CardTitle>\n                </CardHeader>\n                <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                  <div className=\"flex items-start gap-4\">\n                    <Avatar className=\"size-16\" data-sentry-element=\"Avatar\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                      <AvatarImage src={typeof patient.photo === 'object' ? patient.photo?.url : undefined} alt={patient.fullName} data-sentry-element=\"AvatarImage\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n                      <AvatarFallback className=\"text-lg\" data-sentry-element=\"AvatarFallback\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                        {patient.fullName.slice(0, 2).toUpperCase()}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div className=\"flex-1 space-y-3\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div className=\"flex items-center gap-2\">\n                          <IconPhone className=\"size-4 text-muted-foreground\" data-sentry-element=\"IconPhone\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n                          <span className=\"font-medium\">{patient.phone}</span>\n                        </div>\n                        {patient.email && <div className=\"flex items-center gap-2\">\n                            <IconMail className=\"size-4 text-muted-foreground\" />\n                            <span>{patient.email}</span>\n                          </div>}\n                        <div className=\"flex items-center gap-2\">\n                          <IconCalendar className=\"size-4 text-muted-foreground\" data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n                          <span>创建时间: {formatDateTime(new Date(patient.createdAt))}</span>\n                        </div>\n                        {patient.lastVisit && <div className=\"flex items-center gap-2\">\n                            <IconClock className=\"size-4 text-muted-foreground\" />\n                            <span>最后就诊: {formatDateTime(new Date(patient.lastVisit))}</span>\n                          </div>}\n                      </div>\n                      \n                      {patient.userType && <div className=\"flex items-center gap-2\">\n                          <Badge variant={patient.userType === 'patient' ? 'default' : 'secondary'}>\n                            {patient.userType === 'patient' ? '正式患者' : '咨询用户'}\n                          </Badge>\n                          {patient.status && <Badge variant=\"outline\">\n                              {patient.status === 'active' && '活跃'}\n                              {patient.status === 'inactive' && '非活跃'}\n                              {patient.status === 'converted' && '已转换'}\n                            </Badge>}\n                        </div>}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Activity Summary */}\n              <Card data-sentry-element=\"Card\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                  <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                    <IconActivity className=\"size-5\" data-sentry-element=\"IconActivity\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n                    活动概要\n                  </CardTitle>\n                </CardHeader>\n                <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                    <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-blue-600\">{totalInteractions}</div>\n                      <div className=\"text-sm text-muted-foreground\">总互动次数</div>\n                    </div>\n                    <div className=\"text-center p-4 bg-yellow-50 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-yellow-600\">{pendingTasks}</div>\n                      <div className=\"text-sm text-muted-foreground\">待处理任务</div>\n                    </div>\n                    <div className=\"text-center p-4 bg-red-50 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-red-600\">{overdueTasks}</div>\n                      <div className=\"text-sm text-muted-foreground\">逾期任务</div>\n                    </div>\n                    <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {lastInteraction ? formatRelativeTime(new Date(lastInteraction.timestamp)) : '无'}\n                      </div>\n                      <div className=\"text-sm text-muted-foreground\">最后联系</div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"interactions\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              <InteractionTimeline patientId={patientId} interactions={interactions} onCreateInteraction={handleCreateInteraction} onInteractionClick={interaction => console.log('View interaction:', interaction)} data-sentry-element=\"InteractionTimeline\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n            </TabsContent>\n\n            <TabsContent value=\"tasks\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              <TaskManager patientId={patientId} tasks={tasks} onCreateTask={handleCreateTask} onTaskClick={task => console.log('View task:', task)} onTaskStatusChange={handleTaskStatusChange} viewMode=\"list\" data-sentry-element=\"TaskManager\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n            </TabsContent>\n\n            <TabsContent value=\"timeline\" data-sentry-element=\"TabsContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              <CommunicationLog patientId={patientId} timeline={timeline} onItemClick={item => console.log('View timeline item:', item)} onEditItem={item => console.log('Edit timeline item:', item)} data-sentry-element=\"CommunicationLog\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n            </TabsContent>\n          </Tabs>\n        </div>\n\n        {/* Right Column - Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Quick Actions */}\n          <QuickActions patient={patient} onCreateInteraction={handleCreateInteraction} onCreateTask={handleCreateTask} onScheduleAppointment={handleScheduleAppointment} onViewBilling={handleViewBilling} data-sentry-element=\"QuickActions\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n\n          {/* Recent Activity */}\n          <Card data-sentry-element=\"Card\" data-sentry-source-file=\"patient-detail-view.tsx\">\n            <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              <CardTitle className=\"flex items-center gap-2\" data-sentry-element=\"CardTitle\" data-sentry-source-file=\"patient-detail-view.tsx\">\n                <IconTrendingUp className=\"size-5\" data-sentry-element=\"IconTrendingUp\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n                最近活动\n              </CardTitle>\n            </CardHeader>\n            <CardContent data-sentry-element=\"CardContent\" data-sentry-source-file=\"patient-detail-view.tsx\">\n              <div className=\"space-y-3\">\n                {timeline.slice(0, 5).map(item => <div key={item.id} className=\"flex items-start gap-3 text-sm\">\n                    <div className=\"size-2 bg-blue-500 rounded-full mt-2 flex-shrink-0\" />\n                    <div className=\"flex-1\">\n                      <p className=\"font-medium\">{item.title}</p>\n                      <p className=\"text-muted-foreground text-xs\">\n                        {formatRelativeTime(new Date(item.timestamp))}\n                      </p>\n                    </div>\n                  </div>)}\n                {timeline.length === 0 && <p className=\"text-muted-foreground text-sm text-center py-4\">\n                    暂无活动记录\n                  </p>}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Dialogs */}\n      <InteractionFormDialog open={interactionDialogOpen} onOpenChange={setInteractionDialogOpen} patientId={patientId} patientName={patient.fullName} interaction={editingInteraction} defaultType={defaultInteractionType} onSubmit={handleSubmitInteraction} loading={formLoading} data-sentry-element=\"InteractionFormDialog\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n\n      <TaskFormDialog open={taskDialogOpen} onOpenChange={setTaskDialogOpen} patientId={patientId} patientName={patient.fullName} task={editingTask} defaultType={defaultTaskType} availableStaff={availableStaff} onSubmit={handleSubmitTask} loading={formLoading} data-sentry-element=\"TaskFormDialog\" data-sentry-source-file=\"patient-detail-view.tsx\" />\n    </div>;\n}", "module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot='dialog' {...props} data-sentry-element=\"DialogPrimitive.Root\" data-sentry-component=\"Dialog\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} data-sentry-element=\"DialogPrimitive.Trigger\" data-sentry-component=\"DialogTrigger\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} data-sentry-element=\"DialogPrimitive.Portal\" data-sentry-component=\"DialogPortal\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} data-sentry-element=\"DialogPrimitive.Close\" data-sentry-component=\"DialogClose\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return <DialogPrimitive.Overlay data-slot='dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"DialogPrimitive.Overlay\" data-sentry-component=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return <DialogPortal data-slot='dialog-portal' data-sentry-element=\"DialogPortal\" data-sentry-component=\"DialogContent\" data-sentry-source-file=\"dialog.tsx\">\r\n      <DialogOverlay data-sentry-element=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />\r\n      <DialogPrimitive.Content data-slot='dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"DialogPrimitive.Content\" data-sentry-source-file=\"dialog.tsx\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\" data-sentry-element=\"DialogPrimitive.Close\" data-sentry-source-file=\"dialog.tsx\">\r\n          <XIcon data-sentry-element=\"XIcon\" data-sentry-source-file=\"dialog.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>;\n}\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"DialogHeader\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"DialogFooter\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return <DialogPrimitive.Title data-slot='dialog-title' className={cn('text-lg leading-none font-semibold', className)} {...props} data-sentry-element=\"DialogPrimitive.Title\" data-sentry-component=\"DialogTitle\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return <DialogPrimitive.Description data-slot='dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"DialogPrimitive.Description\" data-sentry-component=\"DialogDescription\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nexport { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger };", "import { Suspense } from 'react';\nimport { notFound } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport FormCardSkeleton from '@/components/form-card-skeleton';\nimport PatientDetailView from '@/components/patients/patient-detail-view';\nexport const metadata = {\n  title: 'Dashboard : Patient Detail'\n};\ntype PageProps = {\n  params: Promise<{\n    id: string;\n  }>;\n};\nexport default async function PatientDetailPage(props: PageProps) {\n  const params = await props.params;\n\n  // Validate patient ID\n  if (!params.id || params.id === 'new') {\n    notFound();\n  }\n  return <PageContainer scrollable data-sentry-element=\"PageContainer\" data-sentry-component=\"PatientDetailPage\" data-sentry-source-file=\"page.tsx\">\n      <div className='flex-1 space-y-4'>\n        <Suspense fallback={<FormCardSkeleton />} data-sentry-element=\"Suspense\" data-sentry-source-file=\"page.tsx\">\n          <PatientDetailView patientId={params.id} data-sentry-element=\"PatientDetailView\" data-sentry-source-file=\"page.tsx\" />\n        </Suspense>\n      </div>\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/patients/[id]',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/patients/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/patients/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/patients/[id]',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue = {\n  state: CheckedState;\n  disabled?: boolean;\n};\n\nconst [CheckboxProvider, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\ntype CheckboxElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked = false, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked,\n      onChange: onCheckedChange,\n    });\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = button?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [button, setChecked]);\n\n    return (\n      <CheckboxProvider scope={__scopeCheckbox} state={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"checkbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...checkboxProps}\n          ref={composedRefs}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            // According to WAI ARIA, Checkboxes don't activate on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if checkbox is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect checkbox updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <BubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n            defaultChecked={isIndeterminate(defaultChecked) ? false : defaultChecked}\n          />\n        )}\n      </CheckboxProvider>\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence present={forceMount || isIndeterminate(context.state) || context.state === true}>\n        <Primitive.span\n          data-state={getState(context.state)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype InputProps = React.ComponentPropsWithoutRef<'input'>;\ninterface BubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: CheckedState;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst BubbleInput = (props: BubbleInputProps) => {\n  const { control, checked, bubbles = true, defaultChecked, ...inputProps } = props;\n  const ref = React.useRef<HTMLInputElement>(null);\n  const prevChecked = usePrevious(checked);\n  const controlSize = useSize(control);\n\n  // Bubble checked change to parents (e.g form change event)\n  React.useEffect(() => {\n    const input = ref.current!;\n    const inputProto = window.HTMLInputElement.prototype;\n    const descriptor = Object.getOwnPropertyDescriptor(inputProto, 'checked') as PropertyDescriptor;\n    const setChecked = descriptor.set;\n\n    if (prevChecked !== checked && setChecked) {\n      const event = new Event('click', { bubbles });\n      input.indeterminate = isIndeterminate(checked);\n      setChecked.call(input, isIndeterminate(checked) ? false : checked);\n      input.dispatchEvent(event);\n    }\n  }, [prevChecked, checked, bubbles]);\n\n  const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n  return (\n    <input\n      type=\"checkbox\"\n      aria-hidden\n      defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n      {...inputProps}\n      tabIndex={-1}\n      ref={ref}\n      style={{\n        ...props.style,\n        ...controlSize,\n        position: 'absolute',\n        pointerEvents: 'none',\n        opacity: 0,\n        margin: 0,\n      }}\n    />\n  );\n};\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Checkbox;\nconst Indicator = CheckboxIndicator;\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { CheckboxProps, CheckboxIndicatorProps, CheckedState };\n", "'use client';\n\nimport * as React from 'react';\nimport * as CheckboxPrimitive from '@radix-ui/react-checkbox';\nimport { CheckIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return <CheckboxPrimitive.Root data-slot='checkbox' className={cn('peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50', className)} {...props} data-sentry-element=\"CheckboxPrimitive.Root\" data-sentry-component=\"Checkbox\" data-sentry-source-file=\"checkbox.tsx\">\r\n      <CheckboxPrimitive.Indicator data-slot='checkbox-indicator' className='flex items-center justify-center text-current transition-none' data-sentry-element=\"CheckboxPrimitive.Indicator\" data-sentry-source-file=\"checkbox.tsx\">\r\n        <CheckIcon className='size-3.5' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"checkbox.tsx\" />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>;\n}\nexport { Checkbox };", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\patients\\\\patient-detail-view.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\[id]\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'patients',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\[id]\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/patients/[id]/page\",\n        pathname: \"/dashboard/patients/[id]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\patients\\\\patient-detail-view.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"events\");", "// Payload CMS client with authentication\nimport { AuthenticatedUser } from './auth-middleware';\n\nexport interface PayloadRequestOptions {\n  method?: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE';\n  body?: any;\n  params?: Record<string, string | number>;\n}\n\n/**\n * Enhanced Payload CMS client with proper authentication\n * Routes through frontend API routes which proxy to backend\n */\nexport class PayloadClient {\n  private user: AuthenticatedUser;\n\n  constructor(user: AuthenticatedUser) {\n    this.user = user;\n  }\n\n  /**\n   * Make authenticated request to backend API\n   * When called from server-side (API routes), goes directly to backend\n   * When called from client-side, goes through frontend API routes\n   */\n  private async makeRequest<T>(\n    endpoint: string,\n    options: PayloadRequestOptions = {}\n  ): Promise<T> {\n    const { method = 'GET', body, params } = options;\n\n    // Determine if we're running on server or client\n    const isServer = typeof window === 'undefined';\n\n    let url: string;\n    let requestOptions: RequestInit;\n\n    if (isServer) {\n      // Server-side: make direct request to backend\n      const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n      url = `${backendUrl}/api${endpoint}`;\n\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      // Include Clerk user headers for backend authentication\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'X-Clerk-User-Id': this.user.clerkId,\n          'X-User-Email': this.user.email,\n        },\n      };\n    } else {\n      // Client-side: use frontend API routes\n      url = `/api${endpoint}`;\n      if (params) {\n        const searchParams = new URLSearchParams();\n        Object.entries(params).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            searchParams.append(key, value.toString());\n          }\n        });\n        if (searchParams.toString()) {\n          url += `?${searchParams.toString()}`;\n        }\n      }\n\n      requestOptions = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      };\n    }\n\n    if (body && method !== 'GET') {\n      requestOptions.body = JSON.stringify(body);\n    }\n\n    try {\n      const response = await fetch(url, requestOptions);\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(\n          `API request failed: ${response.status} ${response.statusText} - ${errorText}`\n        );\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed for ${endpoint}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Appointments API methods\n   */\n  async getAppointments(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n  }) {\n    return this.makeRequest('/appointments', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createAppointment(data: any) {\n    return this.makeRequest('/appointments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateAppointment(id: string, data: any) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteAppointment(id: string) {\n    return this.makeRequest(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patients API methods\n   */\n  async getPatients(params?: {\n    limit?: number;\n    page?: number;\n    search?: string;\n  }) {\n    const queryParams: any = { depth: '1', ...params };\n    \n    // Add search functionality\n    if (params?.search) {\n      queryParams['where[or][0][fullName][contains]'] = params.search;\n      queryParams['where[or][1][phone][contains]'] = params.search;\n      queryParams['where[or][2][email][contains]'] = params.search;\n      delete queryParams.search; // Remove search from params\n    }\n\n    return this.makeRequest('/patients', { params: queryParams });\n  }\n\n  async getPatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      params: { depth: '1' },\n    });\n  }\n\n  async createPatient(data: any) {\n    return this.makeRequest('/patients', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatient(id: string, data: any) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatient(id: string) {\n    return this.makeRequest(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Treatments API methods\n   */\n  async getTreatments(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/treatments', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async getTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`);\n  }\n\n  async createTreatment(data: any) {\n    return this.makeRequest('/treatments', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updateTreatment(id: string, data: any) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deleteTreatment(id: string) {\n    return this.makeRequest(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Users API methods (for user management)\n   */\n  async getUsers(params?: {\n    limit?: number;\n    page?: number;\n  }) {\n    return this.makeRequest('/users', {\n      params: { depth: '1', ...params },\n    });\n  }\n\n  async updateUser(userId: string, data: any) {\n    return this.makeRequest(`/users/${userId}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async syncCurrentUser() {\n    // This would sync the current Clerk user with Payload CMS\n    return this.makeRequest('/users/sync', {\n      method: 'POST',\n      body: {\n        clerkId: this.user.clerkId,\n        email: this.user.email,\n        firstName: this.user.firstName,\n        lastName: this.user.lastName,\n      },\n    });\n  }\n\n  async syncUser(userData: {\n    clerkId: string;\n    email: string;\n    firstName?: string;\n    lastName?: string;\n  }) {\n    // Sync a specific user with Payload CMS and return user with role\n    try {\n      // First, try to find existing user\n      const existingUsers = await this.makeRequest<any>('/users', {\n        params: {\n          where: JSON.stringify({\n            clerkId: { equals: userData.clerkId }\n          }),\n          limit: 1,\n        },\n      });\n\n      if (existingUsers.docs && existingUsers.docs.length > 0) {\n        // Update existing user\n        const existingUser = existingUsers.docs[0];\n        const updatedUser = await this.makeRequest<any>(`/users/${existingUser.id}`, {\n          method: 'PATCH',\n          body: {\n            email: userData.email,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return updatedUser;\n      } else {\n        // Create new user with default role\n        const newUser = await this.makeRequest<any>('/users', {\n          method: 'POST',\n          body: {\n            email: userData.email,\n            clerkId: userData.clerkId,\n            firstName: userData.firstName,\n            lastName: userData.lastName,\n            role: 'front-desk', // Default role for new users\n            lastLogin: new Date().toISOString(),\n          },\n        });\n        return newUser;\n      }\n    } catch (error) {\n      console.error('Error syncing user with Payload:', error);\n      // Return a default user object if sync fails\n      return {\n        id: 'temp-id',\n        email: userData.email,\n        clerkId: userData.clerkId,\n        role: 'front-desk',\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n      };\n    }\n  }\n\n  /**\n   * Patient Interactions API methods\n   */\n  async getPatientInteractions(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-interactions', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientInteraction(data: any) {\n    return this.makeRequest('/patient-interactions', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientInteraction(id: string, data: any) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientInteraction(id: string) {\n    return this.makeRequest(`/patient-interactions/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient Tasks API methods\n   */\n  async getPatientTasks(params?: {\n    limit?: number;\n    page?: number;\n    where?: any;\n    sort?: string;\n  }) {\n    return this.makeRequest('/patient-tasks', {\n      params: {\n        depth: '2', // Include relationships\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      params: { depth: '2' },\n    });\n  }\n\n  async createPatientTask(data: any) {\n    return this.makeRequest('/patient-tasks', {\n      method: 'POST',\n      body: data,\n    });\n  }\n\n  async updatePatientTask(id: string, data: any) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'PATCH',\n      body: data,\n    });\n  }\n\n  async deletePatientTask(id: string) {\n    return this.makeRequest(`/patient-tasks/${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  /**\n   * Patient-specific CRM methods\n   */\n  async getPatientInteractionsByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    interactionType?: string;\n    status?: string;\n    priority?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/interactions`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTasksByPatient(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    taskType?: string;\n    status?: string;\n    priority?: string;\n    assignedTo?: string;\n  }) {\n    return this.makeRequest(`/patients/${patientId}/tasks`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n\n  async getPatientTimeline(patientId: string, params?: {\n    limit?: number;\n    page?: number;\n    type?: 'interaction' | 'task';\n  }) {\n    return this.makeRequest(`/patients/${patientId}/timeline`, {\n      params: {\n        depth: '2',\n        ...params,\n      },\n    });\n  }\n}\n\n/**\n * Factory function to create PayloadClient instance\n */\nexport function createPayloadClient(user: AuthenticatedUser): PayloadClient {\n  return new PayloadClient(user);\n}\n"], "names": ["LeftIcon", "ChevronLeftIcon", "className", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "RightIcon", "ChevronRightIcon", "Calendar", "classNames", "showOutsideDays", "props", "DayPicker", "cn", "months", "month", "caption", "caption_label", "nav", "nav_button", "buttonVariants", "variant", "nav_button_previous", "nav_button_next", "table", "head_row", "head_cell", "row", "cell", "mode", "day", "day_range_start", "day_range_end", "day_selected", "day_today", "day_outside", "day_disabled", "day_range_middle", "day_hidden", "components", "IconLeft", "IconRight", "interactionTypeConfig", "label", "icon", "IconPhone", "color", "IconMail", "IconStethoscope", "IconUser", "IconMessageCircle", "IconCreditCard", "statusConfig", "priorityConfig", "InteractionTimeline", "patientId", "interactions", "loading", "onCreateInteraction", "onInteractionClick", "searchTerm", "setSearchTerm", "useState", "filterType", "setFilterType", "filterStatus", "setFilterStatus", "filteredInteractions", "setFilteredInteractions", "getInteractionIcon", "type", "IconComponent", "getStaffMemberName", "staffMember", "firstName", "lastName", "trim", "email", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "IconClock", "<PERSON><PERSON><PERSON><PERSON>", "div", "map", "_", "i", "Badge", "length", "<PERSON><PERSON>", "onClick", "size", "IconPlus", "IconSearch", "Input", "placeholder", "value", "onChange", "e", "target", "Select", "onValueChange", "SelectTrigger", "IconFilter", "SelectValue", "SelectContent", "SelectItem", "Object", "entries", "config", "ScrollArea", "p", "interaction", "index", "typeConfig", "interactionType", "statusBadge", "status", "priorityBadge", "priority", "h4", "title", "span", "formatDateTime", "Date", "timestamp", "outcome", "followUpRequired", "IconArrowRight", "followUpDate", "id", "taskTypeConfig", "IconCalendar", "IconFileText", "IconCheck", "IconX", "TaskManager", "tasks", "onCreateTask", "onTaskClick", "onTaskStatusChange", "viewMode", "filterPriority", "setFilterPriority", "filteredTasks", "setFilteredTasks", "getTaskIcon", "getAssignedToName", "assignedTo", "getTasksByStatus", "filter", "task", "handleStatusChange", "newStatus", "renderTaskCard", "taskType", "overdue", "isOverdue", "dueDate", "IconAlertTriangle", "description", "stopPropagation", "Tabs", "defaultValue", "TabsList", "TabsTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interactionActions", "bgColor", "taskActions", "QuickActions", "patient", "onScheduleAppointment", "onViewBilling", "compact", "isInteractionMenuOpen", "setIsInteractionMenuOpen", "isTaskMenuOpen", "setIsTaskMenuOpen", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "DropdownMenuContent", "align", "DropdownMenuLabel", "DropdownMenuSeparator", "slice", "action", "DropdownMenuItem", "fullName", "open", "onOpenChange", "IconChevronDown", "CommunicationLog", "timeline", "onItemClick", "onEditItem", "activeTab", "setActiveTab", "filteredTimeline", "setFilteredTimeline", "getItemIcon", "item", "data", "getItemColor", "getStaffName", "staff", "interactionCount", "taskCount", "Separator", "isInteraction", "itemColor", "completedAt", "formatRelativeTime", "IconEye", "IconEdit", "interactionSchema", "z", "object", "enum", "string", "min", "max", "notes", "optional", "boolean", "default", "relatedAppointment", "relatedBill", "interactionTypeOptions", "priorityOptions", "statusOptions", "InteractionFormDialog", "patientName", "defaultType", "onSubmit", "setFollowUpDate", "followUpTime", "setFollowUpTime", "isEditing", "form", "useForm", "resolver", "zodResolver", "defaultValues", "handleSubmit", "hours", "minutes", "split", "combinedDate", "setHours", "parseInt", "toISOString", "formData", "error", "console", "watchFollowUpRequired", "watch", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "strong", "Form", "FormField", "control", "name", "render", "field", "FormItem", "FormLabel", "FormControl", "option", "FormMessage", "FormDescription", "Textarea", "Checkbox", "checked", "onCheckedChange", "Popover", "PopoverTrigger", "PopoverC<PERSON>nt", "selected", "onSelect", "disabled", "date", "initialFocus", "<PERSON><PERSON><PERSON><PERSON>er", "taskSchema", "relatedInteraction", "completionNotes", "taskTypeOptions", "TaskFormDialog", "relatedInteractionId", "availableStaff", "setDueDate", "dueTime", "setDueTime", "watchStatus", "role", "crmNotifications", "created", "typeLabel", "toast", "success", "duration", "updated", "deleted", "interactionTitle", "followUpCreated", "info", "statusChanged", "oldStatus", "statusLabels", "taskTitle", "assigned", "assignee<PERSON>ame", "statusEmojis", "completed", "warning", "reminder", "minutesUntil", "timeText", "Math", "floor", "interactionCreateFailed", "taskCreateFailed", "updateFailed", "itemType", "typeLabels", "deleteFailed", "loadFailed", "dataType", "permissionDenied", "networkError", "PatientDetailView", "router", "useRouter", "user", "useUser", "setPatient", "setInteractions", "setTasks", "setTimeline", "setAvailableStaff", "setLoading", "interactionDialogOpen", "setInteractionDialogOpen", "taskDialogOpen", "setTaskDialogOpen", "defaultInteractionType", "setDefaultInteractionType", "defaultTaskType", "setDefaultTaskType", "editingInteraction", "setEditingInteraction", "editingTask", "setEditingTask", "formLoading", "setFormLoading", "handleCreateInteraction", "undefined", "handleCreateTask", "handleSubmitInteraction", "payloadClient", "createPayloadClient", "clerkId", "emailAddresses", "emailAddress", "updatePatientInteraction", "prev", "createPatientInteraction", "timelineData", "getPatientTimeline", "limit", "docs", "Error", "message", "handleSubmitTask", "updatePatientTask", "createPatientTask", "handleScheduleAppointment", "push", "handleViewBilling", "handleTaskStatusChange", "taskId", "find", "t", "h3", "back", "IconArrowLeft", "totalInteractions", "pendingTasks", "overdueTasks", "lastInteraction", "h1", "Avatar", "AvatarImage", "src", "photo", "url", "alt", "AvatarFallback", "toUpperCase", "phone", "createdAt", "lastVisit", "userType", "IconActivity", "log", "IconTrendingUp", "DialogPrimitive", "data-slot", "DialogTrigger", "DialogPortal", "DialogOverlay", "children", "XIcon", "metadata", "PatientDetailPage", "params", "notFound", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "scrollable", "Suspense", "fallback", "FormCardSkeleton", "serverComponentModule.default", "CheckboxPrimitive", "CheckIcon", "PayloadClient", "constructor", "makeRequest", "endpoint", "options", "requestOptions", "method", "body", "backendUrl", "searchParams", "URLSearchParams", "for<PERSON>ach", "key", "append", "toString", "headers", "JSON", "stringify", "response", "fetch", "ok", "errorText", "text", "statusText", "json", "getAppointments", "depth", "getAppointment", "createAppointment", "updateAppointment", "deleteAppointment", "getPatients", "queryParams", "search", "getPatient", "createPatient", "updatePatient", "deletePatient", "getTreatments", "getTreatment", "createTreatment", "updateTreatment", "deleteTreatment", "getUsers", "updateUser", "userId", "syncCurrentUser", "syncUser", "userData", "existingUsers", "where", "equals", "lastLogin", "existingUser", "updatedUser", "getPatientInteractions", "getPatientInteraction", "deletePatientInteraction", "getPatientTasks", "getPatientTask", "deletePatientTask", "getPatientInteractionsByPatient", "getPatientTasksByPatient"], "sourceRoot": ""}