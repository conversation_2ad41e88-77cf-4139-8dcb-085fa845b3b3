try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="be7af661-24e6-4bb0-984b-85aefa456387",e._sentryDebugIdIdentifier="sentry-dbid-be7af661-24e6-4bb0-984b-85aefa456387")}catch(e){}"use strict";(()=>{var e={};e.id=5020,e.ids=[5020],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63504:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>P,serverHooks:()=>_,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>g,HEAD:()=>T,OPTIONS:()=>w,PATCH:()=>k,POST:()=>h,PUT:()=>f});var a=t(86047),o=t(85544),n=t(36135),i=t(63033),p=t(53547),d=t(54360),u=t(19761);let l=(0,p.ZA)(async(e,r)=>{try{let t=(0,d.o)(e),s=new URL(r.url),a=parseInt(s.searchParams.get("limit")||"10"),o=parseInt(s.searchParams.get("page")||"1"),n=s.searchParams.get("patientId"),i=s.searchParams.get("taskType"),u=s.searchParams.get("status"),l=s.searchParams.get("priority"),c=s.searchParams.get("assignedTo"),y=s.searchParams.get("createdBy"),x=s.searchParams.get("overdue"),q=s.searchParams.get("search"),g={};n&&(g.patient={equals:n}),i&&(g.taskType={equals:i}),u&&(g.status={equals:u}),l&&(g.priority={equals:l}),c&&(g.assignedTo={equals:c}),y&&(g.createdBy={equals:y}),"true"===x&&(g.and=[g,{dueDate:{less_than:new Date}},{status:{not_equals:"completed"}}]),q&&(g.or=[{title:{contains:q}},{description:{contains:q}}]),"doctor"===e.role?g.and=[g,{or:[{assignedTo:{equals:e.payloadUserId}},{createdBy:{equals:e.payloadUserId}},{taskType:{in:["treatment-reminder","medical-record-update","consultation-follow-up"]}}]}]:"front-desk"===e.role&&(g.and=[g,{or:[{assignedTo:{equals:e.payloadUserId}},{taskType:{in:["follow-up-call","appointment-scheduling","billing-follow-up"]}}]}]);let h=await t.getPatientTasks({limit:a,page:o,where:g,sort:"-dueDate"});return(0,p.$y)(h)}catch(e){return console.error("Error fetching patient tasks:",e),(0,p.WX)("Failed to fetch patient tasks")}}),c=(0,p.ZA)(async(e,r)=>{try{let t=(0,d.o)(e),s=await r.json();if(s.createdBy=e.payloadUserId,s.assignedTo||(s.assignedTo=e.payloadUserId),!s.patient||!s.title||!s.taskType||!s.dueDate)return(0,p.WX)("Missing required fields: patient, title, taskType, dueDate",400);if("front-desk"===e.role&&!["follow-up-call","appointment-scheduling","billing-follow-up"].includes(s.taskType))return(0,p.WX)("Front-desk staff can only create follow-up-call, appointment-scheduling, or billing-follow-up tasks",403);let a=await t.createPatientTask(s);return(0,p.$y)(a)}catch(e){return console.error("Error creating patient task:",e),(0,p.WX)("Failed to create patient task")}}),y={...i},x="workUnitAsyncStorage"in y?y.workUnitAsyncStorage:"requestAsyncStorage"in y?y.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let a;try{let e=x?.getStore();a=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/patient-tasks",headers:a}).apply(t,s)}})}let g=q(l,"GET"),h=q(c,"POST"),f=q(void 0,"PUT"),k=q(void 0,"PATCH"),m=q(void 0,"DELETE"),T=q(void 0,"HEAD"),w=q(void 0,"OPTIONS"),P=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/patient-tasks/route",pathname:"/api/patient-tasks",filename:"route",bundlePath:"app/api/patient-tasks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-tasks\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:v,serverHooks:_}=P;function E(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:v})}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615,125],()=>t(63504));module.exports=s})();
//# sourceMappingURL=route.js.map