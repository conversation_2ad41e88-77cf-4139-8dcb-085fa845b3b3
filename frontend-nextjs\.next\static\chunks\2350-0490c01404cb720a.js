try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="be8e522d-c7cc-4247-b4a9-314a7d9fa089",e._sentryDebugIdIdentifier="sentry-dbid-be8e522d-c7cc-4247-b4a9-314a7d9fa089")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2350],{48086:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(23278).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59949:(e,t,r)=>{r.d(t,{RG:()=>D,bL:()=>_,q7:()=>k});var n=r(99004),o=r(84732),a=r(59694),l=r(39552),s=r(38774),i=r(29548),u=r(51452),c=r(36962),d=r(18608),f=r(51825),p=r(52880),g="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[b,h,y]=(0,a.N)(v),[w,D]=(0,s.A)(v,[y]),[x,R]=w(v),I=n.forwardRef((e,t)=>(0,p.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:t})})}));I.displayName=v;var j=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:s=!1,dir:i,currentTabStopId:v,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:y,onEntryFocus:w,preventScrollOnEntryFocus:D=!1,...R}=e,I=n.useRef(null),j=(0,l.s)(t,I),C=(0,f.jH)(i),[F=null,E]=(0,d.i)({prop:v,defaultProp:b,onChange:y}),[_,k]=n.useState(!1),N=(0,c.c)(w),T=h(r),O=n.useRef(!1),[G,P]=n.useState(0);return n.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(g,N),()=>e.removeEventListener(g,N)},[N]),(0,p.jsx)(x,{scope:r,orientation:a,dir:C,loop:s,currentTabStopId:F,onItemFocus:n.useCallback(e=>E(e),[E]),onItemShiftTab:n.useCallback(()=>k(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:_||0===G?-1:0,"data-orientation":a,...R,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{O.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!O.current;if(e.target===e.currentTarget&&t&&!_){let t=new CustomEvent(g,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===F),...e].filter(Boolean).map(e=>e.ref.current),D)}}O.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>k(!1))})})}),C="RovingFocusGroupItem",F=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:l=!1,tabStopId:s,...c}=e,d=(0,i.B)(),f=s||d,g=R(C,r),m=g.currentTabStopId===f,v=h(r),{onFocusableItemAdd:y,onFocusableItemRemove:w}=g;return n.useEffect(()=>{if(a)return y(),()=>w()},[a,y,w]),(0,p.jsx)(b.ItemSlot,{scope:r,id:f,focusable:a,active:l,children:(0,p.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":g.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?g.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>g.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void g.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,g.orientation,g.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=g.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>A(r))}})})})});F.displayName=C;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var _=I,k=F},88749:(e,t,r)=>{r.d(t,{G$:()=>V,Hs:()=>D,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>Y,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>$});var n=r(99004),o=r(84732),a=r(39552),l=r(38774),s=r(29548),i=r(18608),u=r(17430),c=r(40201),d=r(55173),f=r(22474),p=r(51452),g=r(6280),m=r(92350),v=r(10144),b=r(50516),h=r(52880),y="Dialog",[w,D]=(0,l.A)(y),[x,R]=w(y),I=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[f=!1,p]=(0,i.i)({prop:o,defaultProp:a,onChange:l});return(0,h.jsx)(x,{scope:t,triggerRef:c,contentRef:d,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:r})};I.displayName=y;var j="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=R(j,r),s=(0,a.s)(t,l.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:s,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});C.displayName=j;var F="DialogPortal",[E,A]=w(F,{forceMount:void 0}),_=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=R(F,t);return(0,h.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,h.jsx)(f.C,{present:r||l.open,children:(0,h.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};_.displayName=F;var k="DialogOverlay",N=n.forwardRef((e,t)=>{let r=A(k,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(k,e.__scopeDialog);return a.modal?(0,h.jsx)(f.C,{present:n||a.open,children:(0,h.jsx)(T,{...o,ref:t})}):null});N.displayName=k;var T=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(k,r);return(0,h.jsx)(m.A,{as:b.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":W(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",G=n.forwardRef((e,t)=>{let r=A(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=R(O,e.__scopeDialog);return(0,h.jsx)(f.C,{present:n||a.open,children:a.modal?(0,h.jsx)(P,{...o,ref:t}):(0,h.jsx)(M,{...o,ref:t})})});G.displayName=O;var P=n.forwardRef((e,t)=>{let r=R(O,e.__scopeDialog),l=n.useRef(null),s=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(S,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=n.forwardRef((e,t)=>{let r=R(O,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,h.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let s=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(s))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),S=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:s,...i}=e,d=R(O,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,g.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:s,children:(0,h.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":W(d.open),...i,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(z,{titleId:d.titleId}),(0,h.jsx)(J,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(B,r);return(0,h.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});L.displayName=B;var K="DialogDescription",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(K,r);return(0,h.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});q.displayName=K;var U="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=R(U,r);return(0,h.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function W(e){return e?"open":"closed"}H.displayName=U;var Z="DialogTitleWarning",[V,X]=(0,l.q)(Z,{contentName:O,titleName:B,docsSlug:"dialog"}),z=e=>{let{titleId:t}=e,r=X(Z),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},Y=I,$=C,Q=_,ee=N,et=G,er=L,en=q,eo=H}}]);