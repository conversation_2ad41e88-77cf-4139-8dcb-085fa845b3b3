'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  IconCash,
  IconCreditCard,
  IconDeviceMobile,
  IconBuildingBank,
  IconPlus,
  IconTrash,
  IconReceipt,
  IconPigMoney,
  IconAlertCircle,
  IconCheck,
  IconLoader2
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { Bill, Deposit } from '@/types/clinic';
import { billsAPI, depositsAPI } from '@/lib/api/billing';

interface PaymentMethod {
  id: string;
  method: 'cash' | 'card' | 'wechat' | 'alipay' | 'bank-transfer' | 'installment';
  amount: number;
  transactionId?: string;
  notes?: string;
}

interface DepositApplication {
  depositId: string;
  amount: number;
  deposit?: Deposit;
}

interface AdvancedPaymentProcessorProps {
  bill: Bill;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPaymentProcessed: (result: any) => void;
}

const paymentMethodOptions = [
  { value: 'cash', label: '现金', icon: IconCash },
  { value: 'card', label: '银行卡', icon: IconCreditCard },
  { value: 'wechat', label: '微信支付', icon: IconDeviceMobile },
  { value: 'alipay', label: '支付宝', icon: IconDeviceMobile },
  { value: 'bank-transfer', label: '银行转账', icon: IconBuildingBank },
  { value: 'installment', label: '分期付款', icon: IconCreditCard },
];

export function AdvancedPaymentProcessor({ 
  bill, 
  open, 
  onOpenChange, 
  onPaymentProcessed 
}: AdvancedPaymentProcessorProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    { id: '1', method: 'cash', amount: 0 }
  ]);
  const [depositApplications, setDepositApplications] = useState<DepositApplication[]>([]);
  const [availableDeposits, setAvailableDeposits] = useState<Deposit[]>([]);
  const [notes, setNotes] = useState('');
  const [generateReceipt, setGenerateReceipt] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [loadingDeposits, setLoadingDeposits] = useState(false);

  // Load available deposits for the patient
  useEffect(() => {
    if (open && bill.patient) {
      fetchAvailableDeposits();
    }
  }, [open, bill.patient]);

  const fetchAvailableDeposits = async () => {
    try {
      setLoadingDeposits(true);
      const patientId = typeof bill.patient === 'object' ? bill.patient.id : bill.patient;
      const deposits = await depositsAPI.getDeposits({
        patient: patientId,
        status: 'active',
        limit: 100,
      });
      setAvailableDeposits(deposits.docs.filter(d => d.remainingAmount > 0));
    } catch (error) {
      console.error('Error fetching deposits:', error);
      toast.error('获取押金信息失败');
    } finally {
      setLoadingDeposits(false);
    }
  };

  const addPaymentMethod = () => {
    const newId = (paymentMethods.length + 1).toString();
    setPaymentMethods([...paymentMethods, { id: newId, method: 'cash', amount: 0 }]);
  };

  const removePaymentMethod = (id: string) => {
    if (paymentMethods.length > 1) {
      setPaymentMethods(paymentMethods.filter(pm => pm.id !== id));
    }
  };

  const updatePaymentMethod = (id: string, updates: Partial<PaymentMethod>) => {
    setPaymentMethods(paymentMethods.map(pm => 
      pm.id === id ? { ...pm, ...updates } : pm
    ));
  };

  const addDepositApplication = () => {
    if (availableDeposits.length === 0) {
      toast.error('没有可用的押金');
      return;
    }
    
    const availableDeposit = availableDeposits.find(d => 
      !depositApplications.some(da => da.depositId === d.id)
    );
    
    if (!availableDeposit) {
      toast.error('所有押金都已添加');
      return;
    }

    setDepositApplications([...depositApplications, {
      depositId: availableDeposit.id,
      amount: Math.min(availableDeposit.remainingAmount, bill.remainingAmount),
      deposit: availableDeposit,
    }]);
  };

  const removeDepositApplication = (depositId: string) => {
    setDepositApplications(depositApplications.filter(da => da.depositId !== depositId));
  };

  const updateDepositApplication = (depositId: string, amount: number) => {
    setDepositApplications(depositApplications.map(da =>
      da.depositId === depositId ? { ...da, amount } : da
    ));
  };

  const calculateTotals = () => {
    const paymentTotal = paymentMethods.reduce((sum, pm) => sum + pm.amount, 0);
    const depositTotal = depositApplications.reduce((sum, da) => sum + da.amount, 0);
    const grandTotal = paymentTotal + depositTotal;
    
    return {
      paymentTotal,
      depositTotal,
      grandTotal,
      remaining: bill.remainingAmount - grandTotal,
    };
  };

  const validatePayment = () => {
    const totals = calculateTotals();
    
    if (totals.grandTotal <= 0) {
      toast.error('支付金额必须大于0');
      return false;
    }
    
    if (totals.grandTotal > bill.remainingAmount) {
      toast.error('支付金额不能超过账单余额');
      return false;
    }
    
    // Validate payment methods
    for (const pm of paymentMethods) {
      if (pm.amount <= 0) {
        toast.error('所有支付方式的金额必须大于0');
        return false;
      }
    }
    
    // Validate deposit applications
    for (const da of depositApplications) {
      if (da.amount <= 0) {
        toast.error('押金抵扣金额必须大于0');
        return false;
      }
      if (da.deposit && da.amount > da.deposit.remainingAmount) {
        toast.error(`押金抵扣金额不能超过押金余额 (${da.deposit.remainingAmount})`);
        return false;
      }
    }
    
    return true;
  };

  const processPayment = async () => {
    if (!validatePayment()) {
      return;
    }
    
    try {
      setProcessing(true);
      
      const response = await fetch('/api/payments/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          billId: bill.id,
          paymentMethods: paymentMethods.filter(pm => pm.amount > 0),
          applyDeposits: depositApplications.filter(da => da.amount > 0),
          notes,
          generateReceipt,
        }),
      });
      
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Payment processing failed');
      }
      
      const result = await response.json();
      
      toast.success(`支付处理成功！总金额: $${calculateTotals().grandTotal}`);
      onPaymentProcessed(result);
      onOpenChange(false);
      
      // Reset form
      setPaymentMethods([{ id: '1', method: 'cash', amount: 0 }]);
      setDepositApplications([]);
      setNotes('');
      
    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error(error instanceof Error ? error.message : '支付处理失败');
    } finally {
      setProcessing(false);
    }
  };

  const totals = calculateTotals();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconCreditCard className="h-5 w-5" />
            高级支付处理 - {bill.billNumber}
          </DialogTitle>
          <DialogDescription>
            账单余额: {new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'USD' }).format(bill.remainingAmount)}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Payment Methods Section */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">支付方式</h3>
              <Button onClick={addPaymentMethod} size="sm" variant="outline">
                <IconPlus className="h-4 w-4 mr-2" />
                添加支付方式
              </Button>
            </div>
            
            <div className="space-y-3">
              {paymentMethods.map((pm) => (
                <Card key={pm.id}>
                  <CardContent className="pt-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div>
                        <Label>支付方式</Label>
                        <Select
                          value={pm.method}
                          onValueChange={(value: any) => updatePaymentMethod(pm.id, { method: value })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {paymentMethodOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                <div className="flex items-center gap-2">
                                  <option.icon className="h-4 w-4" />
                                  {option.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label>金额</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={pm.amount}
                          onChange={(e) => updatePaymentMethod(pm.id, { amount: parseFloat(e.target.value) || 0 })}
                          placeholder="0.00"
                        />
                      </div>
                      
                      <div>
                        <Label>交易ID (可选)</Label>
                        <Input
                          value={pm.transactionId || ''}
                          onChange={(e) => updatePaymentMethod(pm.id, { transactionId: e.target.value })}
                          placeholder="交易编号"
                        />
                      </div>
                      
                      <div className="flex items-end">
                        <Button
                          onClick={() => removePaymentMethod(pm.id)}
                          size="sm"
                          variant="outline"
                          disabled={paymentMethods.length === 1}
                        >
                          <IconTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Deposit Applications Section */}
          {availableDeposits.length > 0 && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">押金抵扣</h3>
                <Button onClick={addDepositApplication} size="sm" variant="outline" disabled={loadingDeposits}>
                  <IconPigMoney className="h-4 w-4 mr-2" />
                  添加押金抵扣
                </Button>
              </div>
              
              {depositApplications.length > 0 && (
                <div className="space-y-3">
                  {depositApplications.map((da) => (
                    <Card key={da.depositId}>
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <Label>押金编号</Label>
                              <div className="text-sm font-medium">{da.deposit?.depositNumber}</div>
                              <div className="text-xs text-muted-foreground">
                                余额: {new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'USD' }).format(da.deposit?.remainingAmount || 0)}
                              </div>
                            </div>
                            
                            <div>
                              <Label>抵扣金额</Label>
                              <Input
                                type="number"
                                min="0"
                                max={da.deposit?.remainingAmount || 0}
                                step="0.01"
                                value={da.amount}
                                onChange={(e) => updateDepositApplication(da.depositId, parseFloat(e.target.value) || 0)}
                              />
                            </div>
                            
                            <div className="flex items-end">
                              <Button
                                onClick={() => removeDepositApplication(da.depositId)}
                                size="sm"
                                variant="outline"
                              >
                                <IconTrash className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Notes Section */}
          <div>
            <Label htmlFor="notes">备注</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="支付相关备注..."
              rows={3}
            />
          </div>

          {/* Options */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="generate-receipt"
              checked={generateReceipt}
              onCheckedChange={(checked) => setGenerateReceipt(checked as boolean)}
            />
            <Label htmlFor="generate-receipt">自动生成收据</Label>
          </div>

          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">支付汇总</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>支付方式总计:</span>
                <span>{new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'USD' }).format(totals.paymentTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>押金抵扣总计:</span>
                <span>{new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'USD' }).format(totals.depositTotal)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>支付总计:</span>
                <span>{new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'USD' }).format(totals.grandTotal)}</span>
              </div>
              <div className="flex justify-between">
                <span>剩余余额:</span>
                <span className={totals.remaining < 0 ? 'text-red-600' : 'text-muted-foreground'}>
                  {new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'USD' }).format(totals.remaining)}
                </span>
              </div>
              {totals.remaining < 0 && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <IconAlertCircle className="h-4 w-4" />
                  支付金额超过账单余额
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={processPayment} 
            disabled={processing || totals.grandTotal <= 0 || totals.remaining < 0}
          >
            {processing ? (
              <>
                <IconLoader2 className="mr-2 h-4 w-4 animate-spin" />
                处理中...
              </>
            ) : (
              <>
                <IconCheck className="mr-2 h-4 w-4" />
                处理支付
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
