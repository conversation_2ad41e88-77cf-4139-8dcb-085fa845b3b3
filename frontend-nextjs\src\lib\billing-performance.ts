// Performance optimization utilities for billing system
// Includes caching, memoization, lazy loading, and performance monitoring

import { LRUCache } from 'lru-cache';

// Performance configuration
const PERFORMANCE_CONFIG = {
  cache: {
    maxSize: 1000, // Maximum number of cached items
    ttl: 5 * 60 * 1000, // 5 minutes TTL
    staleWhileRevalidate: 2 * 60 * 1000, // 2 minutes stale-while-revalidate
  },
  pagination: {
    defaultPageSize: 20,
    maxPageSize: 100,
  },
  debounce: {
    searchDelay: 300, // ms
    validationDelay: 500, // ms
  },
  monitoring: {
    slowQueryThreshold: 1000, // ms
    memoryWarningThreshold: 100 * 1024 * 1024, // 100MB
    performanceMetricsRetention: 1000, // Keep last 1000 measurements
    alertThresholds: {
      apiResponseTime: 2000, // ms
      renderTime: 100, // ms
      memoryUsage: 200 * 1024 * 1024, // 200MB
      cacheHitRate: 0.7, // 70%
    },
  },
  optimization: {
    enableVirtualScrolling: true,
    virtualScrollItemHeight: 60, // px
    enableBatchRequests: true,
    batchRequestDelay: 100, // ms
    enablePreloading: true,
    preloadDistance: 5, // items
  },
};

// Cache implementation with LRU eviction
export class BillingCache {
  private static instance: BillingCache;
  private cache: LRUCache<string, any>;
  private hitCount = 0;
  private missCount = 0;

  private constructor() {
    this.cache = new LRUCache({
      max: PERFORMANCE_CONFIG.cache.maxSize,
      ttl: PERFORMANCE_CONFIG.cache.ttl,
      allowStale: true,
      updateAgeOnGet: true,
    });
  }

  static getInstance(): BillingCache {
    if (!BillingCache.instance) {
      BillingCache.instance = new BillingCache();
    }
    return BillingCache.instance;
  }

  /**
   * Get item from cache
   */
  get<T>(key: string): T | undefined {
    const value = this.cache.get(key);
    if (value !== undefined) {
      this.hitCount++;
      return value as T;
    }
    this.missCount++;
    return undefined;
  }

  /**
   * Set item in cache
   */
  set<T>(key: string, value: T, ttl?: number): void {
    this.cache.set(key, value, { ttl });
  }

  /**
   * Delete item from cache
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear entire cache
   */
  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    hitRate: number;
    hitCount: number;
    missCount: number;
  } {
    const total = this.hitCount + this.missCount;
    return {
      size: this.cache.size,
      hitRate: total > 0 ? this.hitCount / total : 0,
      hitCount: this.hitCount,
      missCount: this.missCount,
    };
  }

  /**
   * Invalidate cache entries by pattern
   */
  invalidatePattern(pattern: string): void {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.cache.delete(key);
      }
    }
  }
}

// Memoization utility for expensive computations
export class Memoizer {
  private static cache = new Map<string, { value: any; timestamp: number; ttl: number }>();

  /**
   * Memoize function with TTL
   */
  static memoize<T extends (...args: any[]) => any>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string,
    ttl: number = 60000 // 1 minute default
  ): T {
    return ((...args: Parameters<T>) => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
      const cached = this.cache.get(key);
      const now = Date.now();

      if (cached && now - cached.timestamp < cached.ttl) {
        return cached.value;
      }

      const result = fn(...args);
      this.cache.set(key, { value: result, timestamp: now, ttl });

      // Clean up expired entries periodically
      if (this.cache.size > 1000) {
        this.cleanup();
      }

      return result;
    }) as T;
  }

  /**
   * Clear memoization cache
   */
  static clear(): void {
    this.cache.clear();
  }

  /**
   * Clean up expired entries
   */
  private static cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp >= entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Debounce utility for user input
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

// Throttle utility for frequent events
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Performance monitoring
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private memoryUsage: number[] = [];

  private constructor() {
    // Monitor memory usage periodically
    setInterval(() => {
      if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
        const memory = (performance as any).memory;
        this.memoryUsage.push(memory.usedJSHeapSize);
        
        // Keep only last 100 measurements
        if (this.memoryUsage.length > 100) {
          this.memoryUsage.shift();
        }

        // Warn if memory usage is high
        if (memory.usedJSHeapSize > PERFORMANCE_CONFIG.monitoring.memoryWarningThreshold) {
          console.warn('High memory usage detected:', memory.usedJSHeapSize / 1024 / 1024, 'MB');
        }
      }
    }, 10000); // Every 10 seconds
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start timing an operation
   */
  startTiming(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration);
      
      if (duration > PERFORMANCE_CONFIG.monitoring.slowQueryThreshold) {
        console.warn(`Slow operation detected: ${operation} took ${duration.toFixed(2)}ms`);
      }
    };
  }

  /**
   * Record a performance metric
   */
  recordMetric(operation: string, duration: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }
    
    const metrics = this.metrics.get(operation)!;
    metrics.push(duration);
    
    // Keep only last 100 measurements
    if (metrics.length > 100) {
      metrics.shift();
    }
  }

  /**
   * Get performance statistics
   */
  getStats(): Record<string, {
    count: number;
    average: number;
    min: number;
    max: number;
    p95: number;
  }> {
    const stats: Record<string, any> = {};
    
    for (const [operation, durations] of this.metrics.entries()) {
      if (durations.length === 0) continue;
      
      const sorted = [...durations].sort((a, b) => a - b);
      const sum = durations.reduce((a, b) => a + b, 0);
      
      stats[operation] = {
        count: durations.length,
        average: sum / durations.length,
        min: sorted[0],
        max: sorted[sorted.length - 1],
        p95: sorted[Math.floor(sorted.length * 0.95)],
      };
    }
    
    return stats;
  }

  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    current: number;
    average: number;
    peak: number;
  } | null {
    if (this.memoryUsage.length === 0) return null;
    
    const current = this.memoryUsage[this.memoryUsage.length - 1];
    const sum = this.memoryUsage.reduce((a, b) => a + b, 0);
    const average = sum / this.memoryUsage.length;
    const peak = Math.max(...this.memoryUsage);
    
    return { current, average, peak };
  }
}

// Lazy loading utility for components
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
): React.LazyExoticComponent<T> {
  if (typeof React === 'undefined') {
    // Handle server-side rendering
    return null as any;
  }

  const LazyComponent = React.lazy(importFn);

  if (fallback) {
    return React.lazy(async () => {
      try {
        return await importFn();
      } catch (error) {
        console.error('Failed to load component:', error);
        return { default: fallback as T };
      }
    });
  }

  return LazyComponent;
}

// Pagination utility
export interface PaginationOptions {
  page: number;
  pageSize: number;
  total?: number;
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export function paginateArray<T>(
  array: T[],
  options: PaginationOptions
): PaginatedResult<T> {
  const { page, pageSize } = options;
  const total = array.length;
  const totalPages = Math.ceil(total / pageSize);
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  
  return {
    data: array.slice(startIndex, endIndex),
    pagination: {
      page,
      pageSize,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// Virtual scrolling utility for large lists
export function useVirtualScrolling<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
): {
  visibleItems: T[];
  startIndex: number;
  endIndex: number;
  totalHeight: number;
  offsetY: number;
} {
  const [scrollTop, setScrollTop] = React.useState(0);

  if (typeof React === 'undefined') {
    // Handle server-side rendering
    return {
      visibleItems: items,
      startIndex: 0,
      endIndex: items.length,
      totalHeight: items.length * itemHeight,
      offsetY: 0,
    };
  }

  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(
    startIndex + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );

  const visibleItems = items.slice(startIndex, endIndex);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY,
  };
}

// Export singleton instances
export const billingCache = BillingCache.getInstance();
export const performanceMonitor = PerformanceMonitor.getInstance();

// Export performance utilities
export const debouncedSearch = debounce((query: string, callback: (query: string) => void) => {
  callback(query);
}, PERFORMANCE_CONFIG.debounce.searchDelay);

export const debouncedValidation = debounce((data: any, callback: (data: any) => void) => {
  callback(data);
}, PERFORMANCE_CONFIG.debounce.validationDelay);

// Memoized calculation functions
export const calculateBillTotal = Memoizer.memoize(
  (items: any[]) => {
    return items.reduce((total, item) => {
      const itemTotal = item.quantity * item.unitPrice;
      const discount = itemTotal * (item.discountRate || 0) / 100;
      return total + (itemTotal - discount);
    }, 0);
  },
  (items) => JSON.stringify(items.map(item => ({ 
    quantity: item.quantity, 
    unitPrice: item.unitPrice, 
    discountRate: item.discountRate 
  }))),
  30000 // 30 seconds TTL
);

export const calculatePaymentSummary = Memoizer.memoize(
  (payments: any[]) => {
    return payments.reduce((summary, payment) => {
      summary.total += payment.amount;
      summary.byMethod[payment.paymentMethod] = 
        (summary.byMethod[payment.paymentMethod] || 0) + payment.amount;
      return summary;
    }, { total: 0, byMethod: {} });
  },
  (payments) => JSON.stringify(payments.map(p => ({ 
    amount: p.amount, 
    paymentMethod: p.paymentMethod 
  }))),
  60000 // 1 minute TTL
);
