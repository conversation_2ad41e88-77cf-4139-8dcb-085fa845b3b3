{"version": 3, "file": "../app/api/payments/route.js", "mappings": "ubAAA,4GCAA,qDCAA,sjDCAA,uCAAiN,yBCAjN,mECAA,0GCAA,8CCAA,uCAAiN,yBCAjN,gDCAA,kDCAA,gDCAA,uGCAA,iECAA,qXCwBA,IAAMA,EAA8F,CAElGC,cAAe,CAAEC,QAAS,YAAaC,SAAU,OAAQ,EACzDC,mBAAoB,CAAEF,QAAS,kBAAmBC,SAAU,OAAQ,EACpEE,uBAAwB,CAAEH,QAAS,iBAAkBC,SAAU,OAAQ,EACvEG,mBAAoB,CAAEJ,QAAS,kBAAmBC,SAAU,OAAQ,EAGpEI,iBAAkB,CAAEL,QAAS,mBAAoBC,SAAU,OAAQ,EACnEK,aAAc,CAAEN,QAAS,iBAAkBC,SAAU,OAAQ,EAC7DM,cAAe,CAAEP,QAAS,SAAUC,SAAU,SAAU,EACxDO,aAAc,CAAER,QAAS,SAAUC,SAAU,SAAU,EACvDQ,kBAAmB,CAAET,QAAS,iBAAkBC,SAAU,OAAQ,EAGlES,uBAAwB,CAAEV,QAAS,SAAUC,SAAU,OAAQ,EAC/DU,eAAgB,CAAEX,QAAS,aAAcC,SAAU,OAAQ,EAC3DW,qBAAsB,CAAEZ,QAAS,WAAYC,SAAU,OAAQ,EAC/DY,yBAA0B,CAAEb,QAAS,eAAgBC,SAAU,OAAQ,EAGvEa,cAAe,CAAEd,QAAS,SAAUC,SAAU,OAAQ,EACtDc,sBAAuB,CAAEf,QAAS,kBAAmBC,SAAU,OAAQ,EACvEe,eAAgB,CAAEhB,QAAS,gBAAiBC,SAAU,OAAQ,EAC9DgB,cAAe,CAAEjB,QAAS,iBAAkBC,SAAU,OAAQ,EAG9DiB,oBAAqB,CAAElB,QAAS,eAAgBC,SAAU,OAAQ,EAClEkB,oBAAqB,CAAEnB,QAAS,iBAAkBC,SAAU,OAAQ,EACpEmB,gBAAiB,CAAEpB,QAAS,cAAeC,SAAU,OAAQ,EAC7DoB,uBAAwB,CAAErB,QAAS,SAAUC,SAAU,OAAQ,EAC/DqB,oBAAqB,CAAEtB,QAAS,aAAcC,SAAU,OAAQ,EAGhEsB,sBAAuB,CAAEvB,QAAS,eAAgBC,SAAU,OAAQ,EACpEuB,cAAe,CAAExB,QAAS,iBAAkBC,SAAU,OAAQ,EAC9DwB,aAAc,CAAEzB,QAAS,iBAAkBC,SAAU,OAAQ,EAC7DyB,oBAAqB,CAAE1B,QAAS,SAAUC,SAAU,OAAQ,EAC5D0B,oBAAqB,CAAE3B,QAAS,aAAcC,SAAU,OAAQ,EAChE2B,sBAAuB,CAAE5B,QAAS,cAAeC,SAAU,OAAQ,EACnE4B,mBAAoB,CAAE7B,QAAS,cAAeC,SAAU,OAAQ,EAChE6B,0BAA2B,CAAE9B,QAAS,eAAgBC,SAAU,OAAQ,EAGxE8B,gBAAiB,CAAE/B,QAAS,QAASC,SAAU,OAAQ,EACvD+B,6BAA8B,CAAEhC,QAAS,SAAUC,SAAU,OAAQ,EACrEgC,8BAA+B,CAAEjC,QAAS,aAAcC,SAAU,OAAQ,EAC1EiC,kBAAmB,CAAElC,QAAS,YAAaC,SAAU,OAAQ,EAG7DkC,oBAAqB,CAAEnC,QAAS,gBAAiBC,SAAU,OAAQ,EACnEmC,uBAAwB,CAAEpC,QAAS,eAAgBC,SAAU,OAAQ,EAGrEoC,eAAgB,CAAErC,QAAS,eAAgBC,SAAU,OAAQ,EAC7DqC,cAAe,CAAEtC,QAAS,iBAAkBC,SAAU,OAAQ,CAChE,CAGO,OAAMsC,EAIX,aAAsB,MAFdC,QAAAA,CAA2B,EAAE,CAIrC,OAAOC,aAAmC,CAIxC,OAHI,EAAqBC,QAAQ,EAAE,CACjCH,EAAoBG,QAAQ,CAAG,IAAIH,CAAAA,EAE9BA,EAAoBG,QAAQ,CAMrCC,eAAeC,CAAU,CAAEC,EAA+B,CAAC,CAAC,CAAgB,CAC1E,IAOIC,EAPE,CACJC,aAAY,CAAI,UAChBC,GAAW,CAAI,SACfC,EAAU,KAAK,iBACfC,EAAkB,YAAY,CAC/B,CAAGL,EAIJ,GAAID,GAAOO,MAAQrD,CAAc,CAAC8C,EAAMO,IAAI,CAAC,CAAE,CAC7C,IAAMC,EAAYtD,CAAc,CAAC8C,EAAMO,IAAI,CAAC,CAC5CL,EAAe,CACbK,KAAMP,EAAMO,IAAI,CAChBnD,QAAS4C,EAAM5C,OAAO,EAAIoD,EAAUpD,OAAO,CAC3CqD,YAAaT,EAAM5C,OAAO,EAAIoD,EAAUpD,OAAO,CAC/CC,SAAUmD,EAAUnD,QAAQ,CAC5BqD,QAASV,EAAMU,OAAO,CACtBC,UAAW,IAAIC,aACfP,CACF,CACF,MAEEH,CAFK,CAEU,CACbK,KAAM,gBACNnD,QAAS4C,GAAO5C,SAAW,yBAC3BqD,YAAaH,EACbjD,SAAU,QACVqD,QAASV,EACTW,UAAW,IAAIC,aACfP,CACF,EAWF,OARID,GACF,IAAI,CAACA,EADO,MACC,CAACF,GAGZC,GACF,IAAI,CAACU,GADQ,WACM,CAACX,GAGfA,CACT,CAKAY,mBAAmBd,CAAU,CAAEC,EAA+B,CAAC,CAAC,CAAgB,CAC9E,IAAMc,EAA6B,CACjCR,KAAM,gBACNnD,QAAS4C,GAAO5C,SAAW,yBAC3BqD,YAAa,oBACbpD,SAAU,QACVqD,QAASV,EACTW,UAAW,IAAIC,KACfP,QAASJ,EAAQI,OAAO,EAAI,SAC9B,EAUA,OARyB,IAArBJ,EAAQG,CAAoB,OAAZ,EAClB,IAAI,CAACA,QAAQ,CAACW,IAGU,IAAtBd,EAAQE,CAAqB,QAAZ,EACnB,IAAI,CAACU,cAAc,CAACE,GAGfA,CACT,CAKAC,sBAAsBC,CAAuB,CAAEhB,EAA+B,CAAC,CAAC,CAAgB,CAC9F,IAAMiB,EAAaD,CAAgB,CAAC,EAAE,CAChCE,EAAgC,CACpCZ,KAAM,mBACNnD,QAAS,oBACTqD,YAAaS,GAAY9D,SAAW,iBACpCC,SAAU,QACVqD,QAASO,EACTN,UAAW,IAAIC,KACfP,QAASJ,EAAQI,OAAO,EAAI,YAC9B,EAUA,OARyB,IAArBJ,EAAQG,CAAoB,OAAZ,EAClB,IAAI,CAACA,QAAQ,CAACe,IAGU,IAAtBlB,EAAQE,CAAqB,QAAZ,EACnB,IAAI,CAACU,cAAc,CAACM,GAGfA,CACT,CAKAC,YAAYhE,CAAe,CAAEiE,CAAoB,CAAQ,CACvDC,EAAAA,KAAKA,CAACC,OAAO,CAACnE,EAAS,aACrBiE,EACAG,SAAU,GACZ,EACF,CAKAC,YAAYrE,CAAe,CAAEiE,CAAoB,CAAQ,CACvDC,EAAAA,KAAKA,CAACI,OAAO,CAACtE,EAAS,aACrBiE,EACAG,SAAU,GACZ,EACF,CAKAG,SAASvE,CAAe,CAAEiE,CAAoB,CAAQ,CACpDC,EAAAA,KAAKA,CAACM,IAAI,CAACxE,EAAS,aAClBiE,EACAG,SAAU,GACZ,EACF,CAKA,SAAiBxB,CAAmB,CAAQ,CAC1C6B,QAAQ7B,KAAK,CAAC,CAAC,CAAC,EAAEA,EAAMK,OAAO,CAAC,EAAE,EAAEL,EAAMO,IAAI,CAAC,EAAE,EAAEP,EAAM5C,OAAO,EAAE,CAAE,CAClEqD,YAAaT,EAAMS,WAAW,CAC9BC,QAASV,EAAMU,OAAO,CACtBC,UAAWX,EAAMW,SAAS,GAI5B,IAAI,CAACf,QAAQ,CAACkC,IAAI,CAAC9B,GACf,IAAI,CAACJ,QAAQ,CAACmC,MAAM,CAAG,KAAK,IAC1B,CAACnC,QAAQ,CAACoC,KAAK,EAEvB,CAKA,eAAuBhC,CAAmB,CAAQ,CAChD,IAAMiC,EAAe,CACnBT,SAA6B,UAAnBxB,EAAM3C,QAAQ,CAAe,IAAO,GAChD,EAEA,OAAQ2C,EAAM3C,QAAQ,EACpB,IAAK,QACHiE,EAAAA,KAAKA,CAACtB,KAAK,CAACA,EAAMS,WAAW,CAAEwB,GAC/B,KACF,KAAK,UACHX,EAAAA,KAAKA,CAACI,OAAO,CAAC1B,EAAMS,WAAW,CAAEwB,GACjC,KACF,KAAK,OACHX,EAAAA,KAAKA,CAACM,IAAI,CAAC5B,EAAMS,WAAW,CAAEwB,EAElC,CACF,CAKAC,aAA8B,CAC5B,MAAO,IAAI,IAAI,CAACtC,QAAQ,CAAC,CAM3BuC,eAAsB,CACpB,IAAI,CAACvC,QAAQ,CAAG,EAAE,CAEtB,CAGO,IAAMwC,EAAsBzC,EAAoBE,WAAW,GAAG,EAEvC,CAACG,EAAYC,IACzCmC,EAAoBrC,cAAc,CAACC,EAAOC,GAE/Ba,EAAqB,CAACd,EAAYC,CAFM,GAGnDmC,EAAoBtB,kBAAkB,CAACd,EAAOC,GA8BnCoC,EAAmB,IA9ByB,EA+BvDC,EACAC,EAAqB,CAAC,CACtBC,EAAoB,GAAI,CACxBnC,KAEA,IAAIoC,EAEJ,IAAK,IAAIC,EAAU,EAAGA,GAAWH,EAAYG,IAC3C,GAAI,CACF,EAFoD,KAE7C,MAAMJ,GACf,CAAE,MAAOtC,EAAO,CAGd,GAFAyC,EAAYzC,EAER0C,IAAYH,EAKd,MAJAxC,EAAeC,EAAO,CACpBK,QAASA,GAAW,QACpBC,gBAAiB,CAAC,QAAQ,EAAEiC,EAAW,CAAC,CAAC,GAErCvC,EAIR,IAAM2C,EAAQH,EAAYI,KAAKC,GAAG,CAAC,EAAGH,EAAU,EAChD,OAAM,IAAII,QAAQC,GAAWC,WAAWD,EAASJ,GACnD,CAGF,MAAMF,CACR,CCzUO,CDyUL,MCzUWQ,UAAwBC,MACnCC,YACE/F,CAAe,CACf,CAAsB,CACtB,CAAoB,CACpB,CAAoB,CACpB,CACA,KAAK,CAACA,GAAAA,IAAAA,CAJCgG,MAAAA,CAAAA,EAAAA,IAAAA,CACA7C,IAAAA,CAAAA,EAAAA,IAAAA,CACAG,OAAAA,CAAAA,EAGP,IAAI,CAAC2C,IAAI,CAAG,iBACd,CACF,CE2TO,IAAMC,EAAwB,CAanCC,qBAAsB,CAACC,EAAgBC,KACrC,IAAMC,EAAkBD,EAAKC,eAAe,EAAI,SAEhD,EAAaA,EACJ,CACLC,OAAO,EACPC,KAH0B,EAGlB,CAAC,cAAc,EAAEF,EAAgBG,OAAO,CAAC,IAAI,EAIrDL,GAAU,EACL,CACLG,OAAO,EACPC,OAAQ,WACV,EAGK,CAAED,OAAO,CAAK,CACvB,CA0CF,EAAE,0BCtZF,IAAMG,EAAcC,SAAAA,cAA+B,IAAI,GAGjDC,QAAiBd,EAAAA,KAAAA,CAAAA,WAEnB9F,CAAAA,CAAe,CACRgG,EAAiB,GAAG,CAAH,CACJ,CACpB,CAAoB,CACpB,CACA,KAAK,CAAChG,GAJCgG,IAAAA,CAAAA,MAAAA,CAAAA,EACA7C,IAAAA,CAAAA,IAAAA,CAAAA,EAAAA,IAAAA,CACAG,OAAAA,CAAAA,EAGP,IAAI,CAHGA,IAGE,CAAG,UACd,CACF,CAGA,eAAeuD,EAAiBC,CAAc,EAC5C,GAAI,CACF,IAAMC,EAAW,MAAMC,KAAAA,CAAM,CAAC,+BAA+B,EAAEF,EAAAA,CAAQ,CAAE,CACvEG,OAAS,EACPC,aAAAA,CAAe,CAAC,OAAO,EAAEP,QAAQQ,GAAG,CAACC,gBAAgB,CAAE,CACzD,CACF,GAEA,GAAI,CAACL,EAASM,EAAE,CACd,CADgB,EAAJA,GACN,IAAIT,EAAS,mCAAoC,GAAK,2BAG9D,OAAO,MAAMG,EAASO,IAAI,EAC5B,CAAE,MAAO1E,EAAO,CAEd,EAFc,IACd6B,OAAQ7B,CAAAA,KAAK,CAAC,4BAA8BA,CAAAA,GACtC,EADsCA,CAAAA,CAClCgE,EAAS,qCAAsC,GAAK,sBAChE,CACF,CAGA,eAAeW,EAAmBC,CAAW,CAAE3E,CAAoB,CAAEiE,CAAc,CAAEW,CAAiB,EACpG,GAAI,CACF,IAAMV,EAAW,MAAXA,KAAiBC,CAAMQ,EAAK,IAC7B3E,CAAO,CACVoE,OAAS,EACP,cAAgB,oBAChB,iBAAmBH,CAAAA,EACnB,IADmBA,UACHW,CAAAA,EAChB,GAAG5E,EAAQoE,EADKQ,KACLR,CAEf,GAEMS,EAAO,EAAPA,IAAaX,EAASO,IAAI,EAAbP,CAEnB,GAAI,CAACA,EAASM,EAAE,CACd,CADgB,EAAJA,GACN,IAAIT,EACRc,EAAK9E,EAAL8E,EAAAA,CAAU,EAAI,CAAC,wBAAwB,EAAEX,EAASf,MAATe,CAAe,CAAE,CAC1DA,EAASf,MAAAA,CACT0B,EAAKvE,EAAAA,EAAI,EAAI,eACbuE,CAAAA,GAIJ,CAJIA,CAAAA,KAIGA,CACT,CAAE,EADOA,IACA9E,EAAO,CACd,EADc,CACVA,aAAiBgE,EACnB,MAD6B,CAK/B,IAJQhE,GAGR6B,OAAQ7B,CAAAA,KAAK,CAAC,wBAA0BA,CAAAA,GAClC,EADkCA,CAAAA,CAC9BgE,EAAS,8BAA+B,GAAK,yBACzD,CACF,CAKO,eAAee,EAAIC,CAAoB,EAAxBD,GAChB,CACF,GAAM,CAAEb,QAAM,CAAE,CAAG,MAAMe,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAACf,EACH,IADW,GACJgB,EAAAA,YAAAA,CAAaR,IAAI,CACtB,CAAE1E,KAAO,2BACT,EAAEoD,MAAQ,IAAI,GAKlB,IAAM+B,EAAO,MAAMf,KAAAA,CAAM,CAAC,+BAA+B,EAAEF,EAAAA,CAAQ,CAAE,CACnEG,OAAS,EACPC,aAAAA,CAAe,CAAC,OAAO,EAAEP,QAAQQ,GAAG,CAACC,gBAAgB,CAAE,EAE3D,GAAGY,IAAI,CAACC,GAAAA,EAAWX,IAAI,IAGjBE,EAAM,IAAIU,GAAIN,CAAAA,EAAQJ,GAAG,EAAXI,EACD,CAAGlB,EAAAA,EAAY,SAAZA,IAAyB,EAAEc,EAAIW,CAAJX,KAAU,CAAE,EAEvDT,EAAW,MAAXA,KAAiBC,CAAMoB,EAAY,CACvCC,MAAQ,CAD+B,MAEvCpB,OAAS,EACP,cAAgB,oBAChB,iBAAmBH,CAAAA,EACnB,IADmBA,WACHiB,EAAKO,EAAAA,aAAe,CAAC,EAAE,EAAEC,aAAiB,IAC5D,CACF,GAEMb,EAAO,EAAPA,IAAaX,EAASO,IAAI,EAAbP,CAEnB,GAAI,CAACA,EAASM,EAAE,CACd,CADgB,EAAJA,IACLS,EAAAA,YAAAA,CAAaR,IAAI,CAACI,EAAM,CAAE1B,CAAF,KAAEA,CAAQe,EAASf,MAAAA,GAGpD,OAAO8B,EAAAA,YAAAA,CAAaR,IAAI,CAACI,EAC3B,CAAE,CADyBA,CAAAA,IAClB9E,EAAO,CAEd,EAFc,KACd6B,OAAQ7B,CAAAA,KAAK,CAAC,kCAAoCA,CAAAA,GAC3CkF,EAD2ClF,CAAAA,WAC3CkF,CAAaR,IAAI,CACtB,CAAE1E,KAAO,yBACT,EAAEoD,MAAQ,IAAI,EAElB,CACF,CAKO,eAAewC,EAAKZ,CAAoB,EAC7C,CADoBY,EAChB,CACF,IAiEIC,EAjEE,EAiEFA,MAjEI3B,CAAM,CAAE,CAAG,MAAMe,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAACf,EAYH,IAZW,GACX4B,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/B,WACA,aACA,8BACA,UACA,EAAEC,QAAU,gBAAgB,GAC5B,EACA,yBACAhB,CAAAA,GAGKE,EAAAA,EAHLF,CAAAA,SAGKE,CAAaR,IAAI,CACtB,CACE1E,KAAO,2BACPO,IAAM,iBACNnD,OAAS,aAEX,EAAEgG,MAAQ,IAAI,GAKlB,IAAM6C,EAAkBC,EAAAA,EAAAA,CAAYC,QAAZD,MAA0B,CAAChC,EAAQ,OACvD,CAAC+B,EAAgBG,OAAO,CAY1B,CAZ4B,IAATA,EACnBN,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/B7B,EACA,UACA,8BACA,UACA,EAAE8B,QAAU,iBAAiBK,SAAAA,CAAWJ,EAAgBI,SAAAA,GACxD,EACA,6BACArB,CAAAA,GAGKE,EAAAA,EAHLF,CAAAA,SAGKE,CAAaR,IAAI,CACtB,CACE1E,KAAO,+BACPO,IAAM,+BACNnD,OAAS,kBACTiJ,SAAAA,CAAWJ,EAAgBI,SAAAA,CAE7B,EAAEjD,MAAQ,IAAI,GAKlB,IAAM+B,EAAO,EAAPA,IAAalB,EAAiBC,GAC9BW,EAAYM,CADkBjB,CAAAA,EACbwB,KADaxB,QACE,CAAC,EAAE,EAAEyB,aAAiB,KAE5D,GAAI,CAACd,EACH,OADc,EACPK,YAAAA,CAAaR,IAAI,CACtB,CACE1E,KAAO,wBACPO,IAAM,sBACNnD,OAAS,mBAEX,EAAEgG,MAAQ,IAAI,GAMlB,GAAI,CACFyC,EAAO,MAAMb,EAAQN,IAAI,EAC3B,CAAE,MAAO1E,EAAO,CAYd,EAZc,KACd8F,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/B7B,EACAW,EACA,EADAA,KAAAA,4BAEA,UACA,EAAE7E,OAAM,EACR,GACA,mCACAgF,CAAAA,GAGKE,EAAAA,EAHLF,CAAAA,SAGKE,CAAaR,IAAI,CACtB,CACE1E,KAAO,gCACPO,IAAM,gBACNnD,OAAS,YAEX,EAAEgG,MAAQ,IAAI,EAElB,CAGA,GAAI,CACFyC,EAAKrC,EAALqC,IAAW,CAAGS,EAAAA,EAAAA,CAAeC,cAAc,CAACV,EAAKrC,MAAM,EACvDqC,EAAKW,EAALX,WAAkB,CAAGS,EAAAA,EAAAA,CAAeG,qBAAqB,CAACZ,EAAKW,aAAa,EAExEX,EAAKa,EAALb,WAAkB,EAAE,CACtBA,EAAKa,EAALb,WAAkB,CAAGS,EAAAA,EAAAA,CAAeK,YAAY,CAACd,EAAKa,EAALb,WAAkB,CAAE,MAEnEA,EAAKe,EAALf,GAAU,EAAE,CACdA,EAAKe,EAALf,GAAU,CAAGS,EAAAA,EAAAA,CAAeK,YAAY,CAACd,EAAKe,EAALf,GAAU,CAAE,KAEzD,CAAE,MAAOgB,EAAmB,CAY1B,OAXAf,EAAAA,EAAAA,CAAYC,EADc,mBACO,CAC/B7B,EACAW,EACA,EADAA,KAAAA,8BAEA,UACA,EAAE7E,KAAO6G,CAAAA,CAAkB,GAC3B,EACAA,aAA6B3D,IAAAA,CAAAA,CAAQ2D,EAAkBzJ,OAAO,CAAG,OAAVA,sBACvD4H,CAAAA,GAGKE,EAAAA,EAHLF,CAAAA,SAGKE,CAAaR,IAAI,CACtB,CACE1E,KAAO,6BACPO,IAAM,sBACNnD,OAAS,aAEX,EAAEgG,MAAQ,IAAI,EAElB,CAGA,IAAM0D,EAAmBC,EAAAA,EAAkBC,CAAAA,SAArCF,CAA+CjB,GACrD,CADqDA,CAAAA,CACjD,CAACiB,EAAiBvF,OAAO,CAAE,CAC7B,IAAM0F,CADc1F,CACI2F,CAAAA,EAAAA,EAAAA,EAAAA,CAAuBJ,CAAAA,EAAiB9G,EAA1DiH,GAA+D,EAarE,OAb+CH,EAE/ChB,EAAAA,CAAYC,qBAAqB,CAC/B7B,EACAW,EACA,EADAA,KAAAA,4BAEA,UACA,EAAE5D,gBAAkBgG,CAAAA,CAAgB,GACpC,EACA,2BACAjC,CAAAA,GAGKE,EAAAA,EAHLF,CAAAA,SAGKE,CAAaR,IAAI,CACtB,CACE1E,KAAO,qBACPO,IAAM,oBACNnD,OAAS,YACTsD,OAASuG,CAAAA,EAEX,EAAE7D,MAAQ,IAAI,EAElB,CAEA,IAAM+D,EAAgBL,EAAiBhC,IAAI,CAG3C,GAAIe,CAHEsB,CAGGC,EAALvB,IAAW,CACb,CADe,EACX,CAEF,IAAMwB,EAAU,CAAGvD,EAAAA,EAAY,SAAZA,EAAuB,EAAE+B,EAAKuB,EAALvB,IAAW,CAAE,EACnDpC,EAAO,EAAPA,IAAakB,EAAmB0C,EAAS,CAAE5B,IAAF,EAAU,MAAM,CAA5Cd,CAA+CT,EAAQW,GAGpEyC,CAHoEzC,CAGhDvB,EAAsBC,EAH0BsB,CAAAA,gBAG1BtB,CAAoB,CAAC4D,EAAc3D,MAAM,CAAEC,GAC3F,CADqE0D,CAAsB1D,CACvF,CAAC6D,EAAkB3D,KAAK,CAC1B,CAD4B,MACrBuB,EADcvB,YACduB,CAAaR,IAAI,CACtB,CACE1E,KAAO,0BACPO,IAAM,0BACNnD,OAAAA,CAASkK,EAAkB1D,MAAAA,CAE7B,EAAER,MAAQ,IAAI,EAGpB,CAAE,MAAOpD,EAAO,CACd,EADc,CACVA,KAAiBgE,QAAAA,GAA6B,GAAK,EAAlCA,CAAYhE,EAAMoD,GAAAA,GAAM,CAC3C,OAAO8B,EAAAA,YAAAA,CAAaR,IAAI,CACtB,CACE1E,KAAO,kBACPO,IAAM,kBACNnD,OAAS,YAEX,EAAEgG,MAAQ,IAAI,EAGlB,OAAMpD,CACR,CAIF,IAAMuH,EAA0BC,SAsE3BA,CAAsC,QAC7C,GAvEwDL,eAuEhDX,CAAa,eAAEE,CAAa,QAAElD,CAAM,CAAE,CAAGiE,QAGX,CAAC,EAHUA,KAGF,SAAU,SAAU,GAC/DC,QAD0E,CAC5CC,QAAQ,CAACnB,KACrC,CAACE,OADoD,EACrBkB,IAAdlB,EAAqB3E,CAAAA,IAArB2E,EAA2B,CAAQ,CAChD,CACL/C,KAAO,IACPC,MAAAA,CAAQ,CAAGiE,EAAAA,CAgCuB,CACtCC,IAAM,MACNC,IAAM,IAlCSF,GAmCfG,MAAQ,QACRC,MAAQ,OACRC,QAAU,QACVC,WAAa,OACf,EACc,CATc1C,EA/BUe,EAwChB,EAToB,EA/BW,IAwC3Bf,GAxC2B,CAAQ,CACzD,EAKkB,MAAUjC,GAA5BgD,GAA4BhD,EAAS,IAChC,CADuC,GAA5CgD,EAEO,IACP5C,MAAQ,sBACV,EAGoB,QAAYJ,GAA9BgD,GAA8BhD,EAAS,IAATA,CAE9BG,CAF+C,EAA/C6C,EAEO,IACP5C,MAAQ,uBACV,EAGoB,QAAYJ,GAA9BgD,GAA8BhD,EAAS,IAATA,CAE9BG,CAF+C,EAA/C6C,EAEO,IACP5C,MAAQ,sBACV,EAGK,CAAED,KAAO,GAAK,CACvB,EA3G0DwD,GACtD,GAAI,CAACI,EAAwB5D,IADyBwD,CACpB,CAChC,CADkC,MAC3BjC,EAAAA,MADoBvB,MACpBuB,CAAaR,IAAI,CACtB,CACE1E,KAAO,oCACPO,IAAM,wBACNnD,OAAAA,CAASmK,EAAwB3D,MAAAA,CAEnC,EAAER,MAAQ,IAAI,GAKlB,IAAMoC,EAAa,GAAG1B,EAAY,GAAf,MAAe,IAAa,CAAC,CAC1CgB,EAAO,EAAPA,IAAaH,EACjBa,EACA,CACEC,MAAQ,CADV,MAFiBd,CAIfkB,IAAMuC,CAAAA,IAAAA,CAAKC,SAAS,CAAClB,EACvB,EACAjD,EACAW,GAsBF,CAtBEA,GAHuBsC,EAGvBtC,CAAAA,EAIFiB,EAAAA,CAAYC,qBAAqB,CAC/B7B,EACAW,EACA,EADAA,KAAAA,UAEA,UACA,EACEyD,SAAAA,CAAWxD,EAAKyD,EAAE,CAClBnB,MAAAA,CAAQvB,EAAKuB,MAAM,CACnB5D,MAAAA,CAAQ2D,EAAc3D,MAAM,CAC5BgD,aAAAA,CAAeW,EAAcX,aAAa,CAC1CE,aAAAA,CAAeS,EAAcT,aAAa,CAC1C8B,SAAAA,CAAW3C,EAAK2C,SAAAA,GAElB,OACAC,EACAzD,GAGKE,EAAAA,EAHLF,CAAAA,SAGKE,CAAaR,IAAI,CAACI,EAAM,CAAE1B,CAAF,KAAU,IAAI,EAC/C,CAAE,MAAOpD,EAAO,CACd,EADc,CACVA,aAAiBgE,EACnB,MAD6B,CACtBkB,EAAAA,YAAAA,CAAaR,IAAI,CACtB,CACE1E,KAAAA,CAAOA,EAAM5C,OAAO,CACpBmD,IAAAA,CAAMP,EAAMO,IAAI,CAChBnD,OAAAA,CAAS4C,EAAM5C,OAAO,CACtBsD,OAAAA,CAASV,EAAMU,OAAAA,CAEjB,EAAE0C,MAAAA,CAAQpD,EAAMoD,MAAO,GAK3B,OADAvB,OAAQ7B,CAAAA,KAAK,CAAC,yCAA2CA,CAAAA,GAClDkF,EADkDlF,CAAAA,WAClDkF,CAAaR,IAAI,CACtB,CACE1E,KAAO,yBACPO,IAAM,kBACNnD,OAAS,sBAEX,EAAEgG,MAAQ,IAAI,EAElB,CACF,CCzXA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IADM,cACY,CAAE,eAAe,SACnC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAExB,CAIC,IAAC,EAAM,CAAH,CAAesF,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,2BACA,yBACA,iBACA,mCACA,CAAK,CACL,6GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,0BC5BA,iECAA,uDCAA,uDCAA,qDCAA,yDCAA,sGCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,sDCAA,8GCAA,qDCAA,4DCAA,wDCAA,gECAA,wDCAA,sDCAA,yDCAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/?7be5", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/./src/lib/billing-error-handler.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/api/billing.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/billing-notifications.ts", "webpack://next-shadcn-dashboard-starter/./src/lib/validation/validation-utils.ts", "webpack://next-shadcn-dashboard-starter/src/app/api/payments/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/?1d17", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0\\\\node_modules\\\\sonner\\\\dist\\\\index.mjs\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\sonner@1.7.4_react-dom@19.0.0_react@19.0.0__react@19.0.0\\\\node_modules\\\\sonner\\\\dist\\\\index.mjs\");\n", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "// Comprehensive error handling system for billing operations\n// Provides consistent error handling, logging, and user feedback\n\nimport { toast } from 'sonner';\n\n// Error types and interfaces\nexport interface BillingError {\n  code: string;\n  message: string;\n  userMessage: string;\n  severity: 'error' | 'warning' | 'info';\n  details?: any;\n  timestamp: Date;\n  context?: string;\n}\n\nexport interface ErrorHandlerOptions {\n  showToast?: boolean;\n  logError?: boolean;\n  context?: string;\n  fallbackMessage?: string;\n}\n\n// Error code mappings with user-friendly messages\nconst ERROR_MESSAGES: Record<string, { message: string; severity: 'error' | 'warning' | 'info' }> = {\n  // Authentication errors\n  AUTH_REQUIRED: { message: '请先登录以继续操作', severity: 'error' },\n  AUTH_SERVICE_ERROR: { message: '认证服务暂时不可用，请稍后重试', severity: 'error' },\n  CLERK_USER_FETCH_ERROR: { message: '获取用户信息失败，请重新登录', severity: 'error' },\n  USER_EMAIL_MISSING: { message: '用户邮箱信息缺失，请联系管理员', severity: 'error' },\n\n  // Validation errors\n  VALIDATION_ERROR: { message: '输入数据验证失败，请检查表单内容', severity: 'error' },\n  INVALID_JSON: { message: '数据格式错误，请刷新页面重试', severity: 'error' },\n  INVALID_LIMIT: { message: '分页参数错误', severity: 'warning' },\n  INVALID_PAGE: { message: '页码参数错误', severity: 'warning' },\n  SUBTOTAL_MISMATCH: { message: '账单金额计算错误，请重新检查', severity: 'error' },\n\n  // Business logic errors\n  INVALID_PAYMENT_AMOUNT: { message: '支付金额无效', severity: 'error' },\n  BILL_NOT_FOUND: { message: '账单不存在或已被删除', severity: 'error' },\n  PAYMENT_METHOD_ERROR: { message: '支付方式验证失败', severity: 'error' },\n  INSUFFICIENT_PERMISSIONS: { message: '权限不足，无法执行此操作', severity: 'error' },\n\n  // Backend/Service errors\n  BACKEND_ERROR: { message: '后端服务错误', severity: 'error' },\n  BACKEND_SERVICE_ERROR: { message: '后端服务暂时不可用，请稍后重试', severity: 'error' },\n  DATABASE_ERROR: { message: '数据库操作失败，请稍后重试', severity: 'error' },\n  NETWORK_ERROR: { message: '网络连接错误，请检查网络连接', severity: 'error' },\n\n  // Security errors\n  RATE_LIMIT_EXCEEDED: { message: '操作过于频繁，请稍后再试', severity: 'error' },\n  SUSPICIOUS_ACTIVITY: { message: '检测到可疑活动，操作已被阻止', severity: 'error' },\n  SESSION_EXPIRED: { message: '会话已过期，请重新登录', severity: 'error' },\n  INVALID_AUTHENTICATION: { message: '身份验证失败', severity: 'error' },\n  UNAUTHORIZED_ACCESS: { message: '未授权访问，请先登录', severity: 'error' },\n\n  // Payment processing errors\n  PAYMENT_GATEWAY_ERROR: { message: '支付网关错误，请稍后重试', severity: 'error' },\n  CARD_DECLINED: { message: '银行卡被拒绝，请检查卡片状态', severity: 'error' },\n  CARD_EXPIRED: { message: '银行卡已过期，请使用其他卡片', severity: 'error' },\n  INVALID_CARD_NUMBER: { message: '银行卡号无效', severity: 'error' },\n  TRANSACTION_TIMEOUT: { message: '交易超时，请重新尝试', severity: 'error' },\n  DUPLICATE_TRANSACTION: { message: '重复交易，请勿重复提交', severity: 'error' },\n  INSUFFICIENT_FUNDS: { message: '余额不足，无法完成支付', severity: 'error' },\n  PAYMENT_ALREADY_PROCESSED: { message: '支付已处理，请勿重复操作', severity: 'error' },\n\n  // Deposit and refund errors\n  DEPOSIT_EXPIRED: { message: '押金已过期', severity: 'error' },\n  DEPOSIT_INSUFFICIENT_BALANCE: { message: '押金余额不足', severity: 'error' },\n  REFUND_AMOUNT_EXCEEDS_PAYMENT: { message: '退款金额超过支付金额', severity: 'error' },\n  DEPOSIT_NOT_FOUND: { message: '未找到指定押金记录', severity: 'error' },\n\n  // System configuration errors\n  CONFIGURATION_ERROR: { message: '系统配置错误，请联系管理员', severity: 'error' },\n  EXTERNAL_SERVICE_ERROR: { message: '外部服务错误，请稍后重试', severity: 'error' },\n\n  // Generic errors\n  INTERNAL_ERROR: { message: '系统内部错误，请稍后重试', severity: 'error' },\n  UNKNOWN_ERROR: { message: '发生未知错误，请联系技术支持', severity: 'error' },\n};\n\n// Error handler class\nexport class BillingErrorHandler {\n  private static instance: BillingErrorHandler;\n  private errorLog: BillingError[] = [];\n\n  private constructor() {}\n\n  static getInstance(): BillingErrorHandler {\n    if (!BillingErrorHandler.instance) {\n      BillingErrorHandler.instance = new BillingErrorHandler();\n    }\n    return BillingErrorHandler.instance;\n  }\n\n  /**\n   * Handle API response errors\n   */\n  handleAPIError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const {\n      showToast = true,\n      logError = true,\n      context = 'API',\n      fallbackMessage = '操作失败，请稍后重试'\n    } = options;\n\n    let billingError: BillingError;\n\n    if (error?.code && ERROR_MESSAGES[error.code]) {\n      const errorInfo = ERROR_MESSAGES[error.code];\n      billingError = {\n        code: error.code,\n        message: error.message || errorInfo.message,\n        userMessage: error.message || errorInfo.message,\n        severity: errorInfo.severity,\n        details: error.details,\n        timestamp: new Date(),\n        context\n      };\n    } else {\n      // Handle unknown errors\n      billingError = {\n        code: 'UNKNOWN_ERROR',\n        message: error?.message || 'Unknown error occurred',\n        userMessage: fallbackMessage,\n        severity: 'error',\n        details: error,\n        timestamp: new Date(),\n        context\n      };\n    }\n\n    if (logError) {\n      this.logError(billingError);\n    }\n\n    if (showToast) {\n      this.showErrorToast(billingError);\n    }\n\n    return billingError;\n  }\n\n  /**\n   * Handle network/fetch errors\n   */\n  handleNetworkError(error: any, options: ErrorHandlerOptions = {}): BillingError {\n    const networkError: BillingError = {\n      code: 'NETWORK_ERROR',\n      message: error?.message || 'Network request failed',\n      userMessage: '网络连接错误，请检查网络连接后重试',\n      severity: 'error',\n      details: error,\n      timestamp: new Date(),\n      context: options.context || 'Network'\n    };\n\n    if (options.logError !== false) {\n      this.logError(networkError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(networkError);\n    }\n\n    return networkError;\n  }\n\n  /**\n   * Handle validation errors\n   */\n  handleValidationError(validationErrors: any[], options: ErrorHandlerOptions = {}): BillingError {\n    const firstError = validationErrors[0];\n    const validationError: BillingError = {\n      code: 'VALIDATION_ERROR',\n      message: 'Validation failed',\n      userMessage: firstError?.message || '表单验证失败，请检查输入内容',\n      severity: 'error',\n      details: validationErrors,\n      timestamp: new Date(),\n      context: options.context || 'Validation'\n    };\n\n    if (options.logError !== false) {\n      this.logError(validationError);\n    }\n\n    if (options.showToast !== false) {\n      this.showErrorToast(validationError);\n    }\n\n    return validationError;\n  }\n\n  /**\n   * Show success message\n   */\n  showSuccess(message: string, description?: string): void {\n    toast.success(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Show warning message\n   */\n  showWarning(message: string, description?: string): void {\n    toast.warning(message, {\n      description,\n      duration: 4000,\n    });\n  }\n\n  /**\n   * Show info message\n   */\n  showInfo(message: string, description?: string): void {\n    toast.info(message, {\n      description,\n      duration: 3000,\n    });\n  }\n\n  /**\n   * Log error to console and internal log\n   */\n  private logError(error: BillingError): void {\n    console.error(`[${error.context}] ${error.code}: ${error.message}`, {\n      userMessage: error.userMessage,\n      details: error.details,\n      timestamp: error.timestamp\n    });\n\n    // Add to internal error log (keep last 100 errors)\n    this.errorLog.push(error);\n    if (this.errorLog.length > 100) {\n      this.errorLog.shift();\n    }\n  }\n\n  /**\n   * Show error toast notification\n   */\n  private showErrorToast(error: BillingError): void {\n    const toastOptions = {\n      duration: error.severity === 'error' ? 5000 : 4000,\n    };\n\n    switch (error.severity) {\n      case 'error':\n        toast.error(error.userMessage, toastOptions);\n        break;\n      case 'warning':\n        toast.warning(error.userMessage, toastOptions);\n        break;\n      case 'info':\n        toast.info(error.userMessage, toastOptions);\n        break;\n    }\n  }\n\n  /**\n   * Get error log for debugging\n   */\n  getErrorLog(): BillingError[] {\n    return [...this.errorLog];\n  }\n\n  /**\n   * Clear error log\n   */\n  clearErrorLog(): void {\n    this.errorLog = [];\n  }\n}\n\n// Convenience functions for common error handling patterns\nexport const billingErrorHandler = BillingErrorHandler.getInstance();\n\nexport const handleAPIError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleAPIError(error, options);\n\nexport const handleNetworkError = (error: any, options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleNetworkError(error, options);\n\nexport const handleValidationError = (errors: any[], options?: ErrorHandlerOptions) => \n  billingErrorHandler.handleValidationError(errors, options);\n\nexport const showSuccess = (message: string, description?: string) => \n  billingErrorHandler.showSuccess(message, description);\n\nexport const showWarning = (message: string, description?: string) => \n  billingErrorHandler.showWarning(message, description);\n\nexport const showInfo = (message: string, description?: string) => \n  billingErrorHandler.showInfo(message, description);\n\n// Error boundary helper for React components\nexport const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(\n  fn: T,\n  context?: string\n): T => {\n  return (async (...args: any[]) => {\n    try {\n      return await fn(...args);\n    } catch (error) {\n      handleAPIError(error, { context });\n      throw error;\n    }\n  }) as T;\n};\n\n// Retry utility with exponential backoff\nexport const retryWithBackoff = async <T>(\n  fn: () => Promise<T>,\n  maxRetries: number = 3,\n  baseDelay: number = 1000,\n  context?: string\n): Promise<T> => {\n  let lastError: any;\n\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\n    try {\n      return await fn();\n    } catch (error) {\n      lastError = error;\n      \n      if (attempt === maxRetries) {\n        handleAPIError(error, { \n          context: context || 'Retry',\n          fallbackMessage: `操作失败，已重试${maxRetries}次`\n        });\n        throw error;\n      }\n\n      // Exponential backoff delay\n      const delay = baseDelay * Math.pow(2, attempt - 1);\n      await new Promise(resolve => setTimeout(resolve, delay));\n    }\n  }\n\n  throw lastError;\n};\n", "// Billing API client functions for medical clinic system\n// Handles bills, payments, and financial operations with comprehensive error handling\n\nimport { Bill, Payment, BillItem, PayloadResponse } from '@/types/clinic';\nimport {\n  handleAPIError,\n  handleNetworkError,\n  retryWithBackoff,\n  BillingError\n} from '@/lib/billing-error-handler';\n\n// API base URL - using relative paths for Next.js API routes\nconst API_BASE = '/api';\n\n// Enhanced error handling utility\nexport class BillingAPIError extends Error {\n  constructor(\n    message: string,\n    public status?: number,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'BillingAPIError';\n  }\n}\n\n// Enhanced API request handler with comprehensive error handling and retry logic\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {},\n  retryOptions?: { maxRetries?: number; context?: string }\n): Promise<T> {\n  const url = `${API_BASE}${endpoint}`;\n\n  const defaultHeaders = {\n    'Content-Type': 'application/json',\n  };\n\n  const config: RequestInit = {\n    ...options,\n    headers: {\n      ...defaultHeaders,\n      ...options.headers,\n    },\n  };\n\n  const makeRequest = async (): Promise<T> => {\n    try {\n      const response = await fetch(url, config);\n\n      if (!response.ok) {\n        let errorData;\n        try {\n          errorData = await response.json();\n        } catch {\n          errorData = {\n            error: `HTTP ${response.status}: ${response.statusText}`,\n            code: `HTTP_${response.status}`,\n            message: response.statusText\n          };\n        }\n\n        const apiError = new BillingAPIError(\n          errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`,\n          response.status,\n          errorData.code || `HTTP_${response.status}`,\n          errorData.details\n        );\n\n        // Handle the error through the error handler\n        handleAPIError(errorData, {\n          context: retryOptions?.context || 'API Request',\n          showToast: false // Don't show toast here, let the calling function decide\n        });\n\n        throw apiError;\n      }\n\n      return await response.json();\n    } catch (error) {\n      if (error instanceof BillingAPIError) {\n        throw error;\n      }\n\n      // Handle network errors\n      const networkError = new BillingAPIError(\n        error instanceof Error ? error.message : 'Network error occurred',\n        0,\n        'NETWORK_ERROR',\n        error\n      );\n\n      handleNetworkError(error, {\n        context: retryOptions?.context || 'API Request',\n        showToast: false\n      });\n\n      throw networkError;\n    }\n  };\n\n  // Use retry logic for GET requests and other idempotent operations\n  const isIdempotent = !options.method || ['GET', 'HEAD', 'OPTIONS'].includes(options.method.toUpperCase());\n\n  if (isIdempotent && retryOptions?.maxRetries) {\n    return retryWithBackoff(\n      makeRequest,\n      retryOptions.maxRetries,\n      1000,\n      retryOptions.context\n    );\n  }\n\n  return makeRequest();\n}\n\n// Bill API Functions\nexport const billsAPI = {\n  /**\n   * Fetch all bills with optional filtering and pagination\n   */\n  async fetchBills(params?: {\n    page?: number;\n    limit?: number;\n    search?: string;\n    status?: string;\n    patientId?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Bill>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.search) searchParams.append('search', params.search);\n    if (params?.status) searchParams.append('status', params.status);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/bills${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Bill>>(\n      endpoint,\n      {},\n      { maxRetries: 3, context: 'Fetch Bills' }\n    );\n  },\n\n  /**\n   * Fetch a specific bill by ID\n   */\n  async fetchBill(id: string): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`);\n  },\n\n  /**\n   * Create a new bill\n   */\n  async createBill(billData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    billType: 'treatment' | 'consultation' | 'deposit' | 'additional';\n    subtotal: number;\n    discountAmount?: number;\n    taxAmount?: number;\n    totalAmount: number;\n    description: string;\n    notes?: string;\n    dueDate: string;\n    items?: Array<{\n      itemType: 'treatment' | 'consultation' | 'material' | 'service';\n      itemName: string;\n      description?: string;\n      quantity: number;\n      unitPrice: number;\n      discountRate?: number;\n    }>;\n  }): Promise<Bill> {\n    return apiRequest<Bill>('/bills', {\n      method: 'POST',\n      body: JSON.stringify(billData),\n    });\n  },\n\n  /**\n   * Update an existing bill\n   */\n  async updateBill(id: string, updateData: Partial<Bill>): Promise<Bill> {\n    return apiRequest<Bill>(`/bills/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Delete a bill\n   */\n  async deleteBill(id: string): Promise<void> {\n    return apiRequest<void>(`/bills/${id}`, {\n      method: 'DELETE',\n    });\n  },\n\n  /**\n   * Generate bill from appointment\n   */\n  async generateFromAppointment(appointmentId: string, billType: string = 'treatment'): Promise<Bill> {\n    return apiRequest<Bill>('/bills/generate-from-appointment', {\n      method: 'POST',\n      body: JSON.stringify({ appointmentId, billType }),\n    });\n  },\n\n  /**\n   * Check if appointment already has a bill\n   */\n  async checkAppointmentBill(appointmentId: string): Promise<{ hasBill: boolean; bill?: Bill }> {\n    try {\n      const response = await billsAPI.fetchBills({\n        limit: 1,\n        // Note: This would need backend support for filtering by appointment\n        // For now, we'll fetch and filter client-side in components\n      });\n\n      const bill = response.docs.find(bill =>\n        typeof bill.appointment === 'object' && bill.appointment?.id === appointmentId\n      );\n\n      return {\n        hasBill: !!bill,\n        bill: bill || undefined,\n      };\n    } catch (error) {\n      console.error('Failed to check appointment bill:', error);\n      return { hasBill: false };\n    }\n  },\n};\n\n// Payment API Functions\nexport const paymentsAPI = {\n  /**\n   * Fetch all payments with optional filtering\n   */\n  async fetchPayments(params?: {\n    page?: number;\n    limit?: number;\n    billId?: string;\n    patientId?: string;\n    paymentMethod?: string;\n    status?: string;\n    dateFrom?: string;\n    dateTo?: string;\n  }): Promise<PayloadResponse<Payment>> {\n    const searchParams = new URLSearchParams();\n    \n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.billId) searchParams.append('bill', params.billId);\n    if (params?.patientId) searchParams.append('patient', params.patientId);\n    if (params?.paymentMethod) searchParams.append('paymentMethod', params.paymentMethod);\n    if (params?.status) searchParams.append('paymentStatus', params.status);\n    if (params?.dateFrom) searchParams.append('dateFrom', params.dateFrom);\n    if (params?.dateTo) searchParams.append('dateTo', params.dateTo);\n\n    const queryString = searchParams.toString();\n    const endpoint = `/payments${queryString ? `?${queryString}` : ''}`;\n    \n    return apiRequest<PayloadResponse<Payment>>(endpoint);\n  },\n\n  /**\n   * Fetch a specific payment by ID\n   */\n  async fetchPayment(id: string): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`);\n  },\n\n  /**\n   * Process a new payment\n   */\n  async processPayment(paymentData: {\n    bill: string;\n    patient: string;\n    amount: number;\n    paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';\n    transactionId?: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>('/payments', {\n      method: 'POST',\n      body: JSON.stringify(paymentData),\n    });\n  },\n\n  /**\n   * Update payment status\n   */\n  async updatePayment(id: string, updateData: Partial<Payment>): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Process refund\n   */\n  async processRefund(paymentId: string, refundData: {\n    amount: number;\n    reason: string;\n    notes?: string;\n  }): Promise<Payment> {\n    return apiRequest<Payment>(`/payments/${paymentId}/refund`, {\n      method: 'POST',\n      body: JSON.stringify(refundData),\n    });\n  },\n};\n\n// Financial reporting API functions\nexport const reportsAPI = {\n  /**\n   * Get daily revenue report\n   */\n  async getDailyRevenue(date: string): Promise<{\n    date: string;\n    totalRevenue: number;\n    paymentCount: number;\n    paymentMethods: Record<string, { amount: number; count: number }>;\n  }> {\n    return apiRequest(`/reports/daily-revenue?date=${date}`);\n  },\n\n  /**\n   * Get monthly revenue report\n   */\n  async getMonthlyRevenue(year: number, month: number): Promise<{\n    year: number;\n    month: number;\n    totalRevenue: number;\n    dailyBreakdown: Array<{ date: string; revenue: number }>;\n  }> {\n    return apiRequest(`/reports/monthly-revenue?year=${year}&month=${month}`);\n  },\n\n  /**\n   * Get outstanding balances report\n   */\n  async getOutstandingBalances(): Promise<{\n    totalOutstanding: number;\n    overdueAmount: number;\n    billsCount: number;\n    overdueBillsCount: number;\n    bills: Array<{\n      id: string;\n      billNumber: string;\n      patient: string;\n      amount: number;\n      dueDate: string;\n      daysOverdue: number;\n    }>;\n  }> {\n    return apiRequest('/reports/outstanding-balances');\n  },\n\n  /**\n   * Generate financial report\n   */\n  async getFinancialReport(params?: {\n    startDate?: string;\n    endDate?: string;\n    type?: 'summary' | 'detailed';\n  }): Promise<any> {\n    const searchParams = new URLSearchParams();\n    if (params?.startDate) searchParams.set('startDate', params.startDate);\n    if (params?.endDate) searchParams.set('endDate', params.endDate);\n    if (params?.type) searchParams.set('type', params.type);\n\n    return apiRequest(`/reports/financial?${searchParams.toString()}`);\n  },\n};\n\n// Deposit API functions\nexport const depositsAPI = {\n  /**\n   * Create a new deposit\n   */\n  async createDeposit(depositData: {\n    patient: string;\n    appointment?: string;\n    treatment?: string;\n    depositType: 'treatment' | 'appointment' | 'material';\n    amount: number;\n    purpose: string;\n    notes?: string;\n    expiryDate?: string;\n  }): Promise<any> {\n    return apiRequest('/deposits', {\n      method: 'POST',\n      body: JSON.stringify(depositData),\n    });\n  },\n\n  /**\n   * Get deposits with filtering and pagination\n   */\n  async getDeposits(params?: {\n    page?: number;\n    limit?: number;\n    patient?: string;\n    status?: string;\n    depositType?: string;\n  }): Promise<PayloadResponse<any>> {\n    const searchParams = new URLSearchParams();\n    if (params?.page) searchParams.set('page', params.page.toString());\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.patient) searchParams.set('where[patient][equals]', params.patient);\n    if (params?.status) searchParams.set('where[status][equals]', params.status);\n    if (params?.depositType) searchParams.set('where[depositType][equals]', params.depositType);\n\n    return apiRequest(`/deposits?${searchParams.toString()}`);\n  },\n\n  /**\n   * Get deposit by ID\n   */\n  async getDepositById(id: string): Promise<any> {\n    return apiRequest(`/deposits/${id}`);\n  },\n\n  /**\n   * Update deposit\n   */\n  async updateDeposit(id: string, updateData: {\n    status?: string;\n    usedAmount?: number;\n    notes?: string;\n  }): Promise<any> {\n    return apiRequest(`/deposits/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updateData),\n    });\n  },\n\n  /**\n   * Apply deposit to bill\n   */\n  async applyToBill(depositId: string, billId: string, amount: number): Promise<any> {\n    return apiRequest('/deposits/apply-to-bill', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        billId,\n        amount,\n      }),\n    });\n  },\n\n  /**\n   * Process deposit refund\n   */\n  async processRefund(depositId: string, refundAmount: number, refundReason: string, refundMethod: string = 'cash'): Promise<any> {\n    return apiRequest('/deposits/refund', {\n      method: 'POST',\n      body: JSON.stringify({\n        depositId,\n        refundAmount,\n        refundReason,\n        refundMethod,\n      }),\n    });\n  },\n};\n\n// Receipt API functions\nexport const receiptsAPI = {\n  /**\n   * Generate receipt for payment\n   */\n  async generateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`);\n  },\n\n  /**\n   * Regenerate receipt number (admin only)\n   */\n  async regenerateReceipt(paymentId: string): Promise<any> {\n    return apiRequest(`/payments/${paymentId}/receipt`, {\n      method: 'POST',\n    });\n  },\n};\n\n\n\n// BillingAPIError is already exported above at line 16\n\n// Export utility functions\nexport const billingUtils = {\n  /**\n   * Format currency amount for display\n   */\n  formatCurrency(amount: number): string {\n    return new Intl.NumberFormat('zh-CN', {\n      style: 'currency',\n      currency: 'USD',\n    }).format(amount);\n  },\n\n  /**\n   * Calculate bill total with discounts and taxes\n   */\n  calculateBillTotal(subtotal: number, discountAmount: number = 0, taxAmount: number = 0): number {\n    return subtotal + taxAmount - discountAmount;\n  },\n\n  /**\n   * Get payment method display name\n   */\n  getPaymentMethodName(method: string): string {\n    const methods: Record<string, string> = {\n      cash: '现金',\n      card: '银行卡',\n      wechat: '微信支付',\n      alipay: '支付宝',\n      transfer: '银行转账',\n      deposit: '押金抵扣',\n      installment: '分期付款',\n    };\n    return methods[method] || method;\n  },\n\n  /**\n   * Get bill status display name\n   */\n  getBillStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      draft: '草稿',\n      sent: '已发送',\n      confirmed: '已确认',\n      paid: '已支付',\n      cancelled: '已取消',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get payment status display name\n   */\n  getPaymentStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      pending: '待处理',\n      completed: '已完成',\n      failed: '失败',\n      refunded: '已退款',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Get deposit status display name\n   */\n  getDepositStatusName(status: string): string {\n    const statuses: Record<string, string> = {\n      active: '有效',\n      used: '已使用',\n      refunded: '已退还',\n      expired: '已过期',\n    };\n    return statuses[status] || status;\n  },\n\n  /**\n   * Validate payment amount against bill balance\n   */\n  validatePaymentAmount(amount: number, billBalance: number): boolean {\n    return amount > 0 && amount <= billBalance;\n  },\n};\n", "// Comprehensive toast notification utilities for billing actions\n// Provides consistent messaging in Chinese for all billing operations\n\nimport { toast } from 'sonner';\nimport { Bill, Payment, BillItem } from '@/types/clinic';\nimport { billingUtils } from './api/billing';\n\nexport const billingNotifications = {\n  // Bill-related notifications\n  bill: {\n    created: (bill: Bill) => {\n      toast.success(`账单创建成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (bill: Bill) => {\n      toast.success(`账单更新成功！`, {\n        description: `账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (billNumber: string) => {\n      toast.success(`账单删除成功！`, {\n        description: `账单编号: ${billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    statusUpdated: (bill: Bill, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        draft: '草稿',\n        sent: '已发送',\n        confirmed: '已确认',\n        paid: '已支付',\n        cancelled: '已取消',\n      };\n      \n      toast.success(`账单状态已更新！`, {\n        description: `${bill.billNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 5000,\n      });\n    },\n\n    generateFromAppointment: (bill: Bill, appointmentDate: string) => {\n      toast.success(`从预约生成账单成功！`, {\n        description: `预约日期: ${appointmentDate}，账单编号: ${bill.billNumber}`,\n        duration: 4000,\n      });\n    },\n\n    loadError: (error?: string) => {\n      toast.error(`加载账单失败`, {\n        description: error || '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    createError: (error?: string) => {\n      toast.error(`创建账单失败`, {\n        description: error || '请检查输入信息后重试',\n        duration: 5000,\n      });\n    },\n\n    updateError: (error?: string) => {\n      toast.error(`更新账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    deleteError: (error?: string) => {\n      toast.error(`删除账单失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`账单验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Payment-related notifications\n  payment: {\n    processed: (payment: Payment) => {\n      toast.success(`支付处理成功！`, {\n        description: `支付金额: ${billingUtils.formatCurrency(payment.amount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    receiptGenerated: (payment: Payment) => {\n      toast.success(`收据生成成功！`, {\n        description: `收据编号: ${payment.receiptNumber || '待生成'}`,\n        duration: 4000,\n      });\n    },\n\n    refunded: (payment: Payment, refundAmount: number) => {\n      toast.success(`退款处理成功！`, {\n        description: `退款金额: ${billingUtils.formatCurrency(refundAmount)}，支付编号: ${payment.paymentNumber}`,\n        duration: 5000,\n      });\n    },\n\n    statusUpdated: (payment: Payment, oldStatus: string, newStatus: string) => {\n      const statusNames = {\n        pending: '待处理',\n        completed: '已完成',\n        failed: '失败',\n        refunded: '已退款',\n      };\n\n      toast.success(`支付状态已更新！`, {\n        description: `${payment.paymentNumber}: ${statusNames[oldStatus as keyof typeof statusNames]} → ${statusNames[newStatus as keyof typeof statusNames]}`,\n        duration: 4000,\n      });\n    },\n\n    processError: (error?: string) => {\n      toast.error(`支付处理失败`, {\n        description: error || '请检查支付信息后重试',\n        duration: 5000,\n      });\n    },\n\n    refundError: (error?: string) => {\n      toast.error(`退款处理失败`, {\n        description: error || '请联系管理员处理',\n        duration: 5000,\n      });\n    },\n\n    validationError: (message: string) => {\n      toast.error(`支付验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n\n    amountExceeded: (maxAmount: number) => {\n      toast.error(`支付金额超限`, {\n        description: `最大支付金额: ${billingUtils.formatCurrency(maxAmount)}`,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Receipt-related notifications\n  receipt: {\n    printed: (receiptNumber: string) => {\n      toast.success(`收据打印成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    downloaded: (receiptNumber: string) => {\n      toast.success(`收据下载成功！`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 3000,\n      });\n    },\n\n    printError: () => {\n      toast.error(`收据打印失败`, {\n        description: '请检查打印机设置',\n        duration: 4000,\n      });\n    },\n\n    downloadError: () => {\n      toast.error(`收据下载失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    notFound: (receiptNumber: string) => {\n      toast.error(`收据未找到`, {\n        description: `收据编号: ${receiptNumber}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // General system notifications\n  system: {\n    loading: (action: string) => {\n      toast.loading(`${action}中...`, {\n        duration: Infinity, // Will be dismissed manually\n      });\n    },\n\n    networkError: () => {\n      toast.error(`网络连接失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    permissionDenied: (action: string) => {\n      toast.error(`权限不足`, {\n        description: `您没有权限执行: ${action}`,\n        duration: 5000,\n      });\n    },\n\n    dataRefreshed: () => {\n      toast.success(`数据刷新成功`, {\n        duration: 2000,\n      });\n    },\n\n    dataRefreshError: () => {\n      toast.error(`数据刷新失败`, {\n        description: '请稍后重试',\n        duration: 4000,\n      });\n    },\n\n    operationCancelled: (operation: string) => {\n      toast.info(`${operation}已取消`, {\n        duration: 2000,\n      });\n    },\n\n    featureNotImplemented: (feature: string) => {\n      toast.info(`${feature}功能开发中...`, {\n        description: '敬请期待',\n        duration: 3000,\n      });\n    },\n  },\n\n  // Financial reporting notifications\n  financial: {\n    reportGenerated: (reportType: string, period: string) => {\n      toast.success(`${reportType}生成成功！`, {\n        description: `报表期间: ${period}`,\n        duration: 4000,\n      });\n    },\n\n    reportError: (reportType: string, error?: string) => {\n      toast.error(`${reportType}生成失败`, {\n        description: error || '请稍后重试',\n        duration: 5000,\n      });\n    },\n\n    dataExported: (format: string) => {\n      toast.success(`数据导出成功！`, {\n        description: `格式: ${format}`,\n        duration: 3000,\n      });\n    },\n\n    exportError: (error?: string) => {\n      toast.error(`数据导出失败`, {\n        description: error || '请稍后重试',\n        duration: 4000,\n      });\n    },\n  },\n\n  // Validation and warning notifications\n  validation: {\n    requiredField: (fieldName: string) => {\n      toast.error(`字段验证失败`, {\n        description: `${fieldName}为必填项`,\n        duration: 4000,\n      });\n    },\n\n    invalidFormat: (fieldName: string, expectedFormat: string) => {\n      toast.error(`格式验证失败`, {\n        description: `${fieldName}格式应为: ${expectedFormat}`,\n        duration: 4000,\n      });\n    },\n\n    duplicateEntry: (itemType: string, identifier: string) => {\n      toast.error(`重复条目`, {\n        description: `${itemType} \"${identifier}\" 已存在`,\n        duration: 4000,\n      });\n    },\n\n    unsavedChanges: () => {\n      toast.warning(`有未保存的更改`, {\n        description: '请保存后再继续',\n        duration: 4000,\n      });\n    },\n\n    confirmAction: (action: string) => {\n      toast.warning(`请确认操作`, {\n        description: `即将执行: ${action}`,\n        duration: 5000,\n      });\n    },\n  },\n};\n\n// Utility function to dismiss all toasts\nexport const dismissAllToasts = () => {\n  toast.dismiss();\n};\n\n// Utility function to show custom toast with consistent styling\nexport const showCustomToast = (\n  type: 'success' | 'error' | 'warning' | 'info',\n  title: string,\n  description?: string,\n  duration: number = 4000\n) => {\n  const toastFunction = toast[type];\n  toastFunction(title, {\n    description,\n    duration,\n  });\n};\n", "// Validation utilities for real-time form validation and error handling\n// Provides consistent validation feedback across all billing forms\n\nimport { z } from 'zod';\nimport { billingNotifications } from '@/lib/billing-notifications';\n\n// Validation result interface\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: ValidationError[];\n  warnings: ValidationWarning[];\n}\n\nexport interface ValidationError {\n  field: string;\n  message: string;\n  code: string;\n  severity: 'error' | 'warning';\n}\n\nexport interface ValidationWarning {\n  field: string;\n  message: string;\n  suggestion?: string;\n}\n\n// Real-time validation debounce utility\nexport class ValidationDebouncer {\n  private timeouts: Map<string, NodeJS.Timeout> = new Map();\n  private readonly delay: number;\n\n  constructor(delay: number = 500) {\n    this.delay = delay;\n  }\n\n  debounce<T extends any[]>(\n    key: string,\n    callback: (...args: T) => void,\n    ...args: T\n  ): void {\n    // Clear existing timeout for this key\n    const existingTimeout = this.timeouts.get(key);\n    if (existingTimeout) {\n      clearTimeout(existingTimeout);\n    }\n\n    // Set new timeout\n    const timeout = setTimeout(() => {\n      callback(...args);\n      this.timeouts.delete(key);\n    }, this.delay);\n\n    this.timeouts.set(key, timeout);\n  }\n\n  clear(key?: string): void {\n    if (key) {\n      const timeout = this.timeouts.get(key);\n      if (timeout) {\n        clearTimeout(timeout);\n        this.timeouts.delete(key);\n      }\n    } else {\n      // Clear all timeouts\n      this.timeouts.forEach(timeout => clearTimeout(timeout));\n      this.timeouts.clear();\n    }\n  }\n}\n\n// Field-level validation utility\nexport class FieldValidator {\n  private schema: z.ZodSchema;\n  private debouncer: ValidationDebouncer;\n\n  constructor(schema: z.ZodSchema, debounceDelay: number = 300) {\n    this.schema = schema;\n    this.debouncer = new ValidationDebouncer(debounceDelay);\n  }\n\n  validateField(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    this.debouncer.debounce(\n      fieldPath,\n      this.performFieldValidation.bind(this),\n      fieldPath,\n      value,\n      fullData,\n      onValidation\n    );\n  }\n\n  private performFieldValidation(\n    fieldPath: string,\n    value: any,\n    fullData: any,\n    onValidation?: (result: ValidationResult) => void\n  ): void {\n    try {\n      // Create a partial object with just this field\n      const fieldData = this.setNestedValue({}, fieldPath, value);\n      \n      // Merge with existing data\n      const testData = { ...fullData, ...fieldData };\n      \n      // Validate the full object but focus on this field\n      const result = this.schema.safeParse(testData);\n      \n      const fieldErrors = result.success \n        ? []\n        : result.error.errors\n            .filter(error => error.path.join('.') === fieldPath)\n            .map(error => ({\n              field: fieldPath,\n              message: error.message,\n              code: error.code,\n              severity: 'error' as const,\n            }));\n\n      const warnings = this.generateWarnings(fieldPath, value, fullData);\n\n      const validationResult: ValidationResult = {\n        isValid: fieldErrors.length === 0,\n        errors: fieldErrors,\n        warnings,\n      };\n\n      if (onValidation) {\n        onValidation(validationResult);\n      }\n    } catch (error) {\n      console.error('Field validation error:', error);\n      if (onValidation) {\n        onValidation({\n          isValid: false,\n          errors: [{\n            field: fieldPath,\n            message: '验证过程中发生错误',\n            code: 'VALIDATION_ERROR',\n            severity: 'error',\n          }],\n          warnings: [],\n        });\n      }\n    }\n  }\n\n  private setNestedValue(obj: any, path: string, value: any): any {\n    const keys = path.split('.');\n    let current = obj;\n    \n    for (let i = 0; i < keys.length - 1; i++) {\n      const key = keys[i];\n      if (!(key in current)) {\n        current[key] = {};\n      }\n      current = current[key];\n    }\n    \n    current[keys[keys.length - 1]] = value;\n    return obj;\n  }\n\n  private generateWarnings(fieldPath: string, value: any, fullData: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate context-specific warnings\n    switch (fieldPath) {\n      case 'amount':\n        if (typeof value === 'number' && value > 10000) {\n          warnings.push({\n            field: fieldPath,\n            message: '金额较大，请确认是否正确',\n            suggestion: '检查金额是否输入正确',\n          });\n        }\n        break;\n\n      case 'dueDate':\n        if (value) {\n          const dueDate = new Date(value);\n          const today = new Date();\n          const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n          \n          if (daysDiff > 365) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期距离现在超过一年',\n              suggestion: '考虑设置更近的到期日期',\n            });\n          } else if (daysDiff < 7) {\n            warnings.push({\n              field: fieldPath,\n              message: '到期日期较近',\n              suggestion: '确保有足够时间处理账单',\n            });\n          }\n        }\n        break;\n\n      case 'discountAmount':\n        if (typeof value === 'number' && value > 0 && fullData.items) {\n          const subtotal = fullData.items.reduce((sum: number, item: any) => {\n            return sum + (item.quantity || 0) * (item.unitPrice || 0);\n          }, 0);\n          \n          if (value > subtotal * 0.5) {\n            warnings.push({\n              field: fieldPath,\n              message: '折扣金额超过小计的50%',\n              suggestion: '确认折扣金额是否正确',\n            });\n          }\n        }\n        break;\n\n      case 'unitPrice':\n        if (typeof value === 'number' && value === 0) {\n          warnings.push({\n            field: fieldPath,\n            message: '单价为0，确认是否为免费项目',\n            suggestion: '如果不是免费项目，请输入正确单价',\n          });\n        }\n        break;\n    }\n\n    return warnings;\n  }\n\n  cleanup(): void {\n    this.debouncer.clear();\n  }\n}\n\n// Form-level validation utility\nexport class FormValidator {\n  private schema: z.ZodSchema;\n  private fieldValidators: Map<string, FieldValidator> = new Map();\n\n  constructor(schema: z.ZodSchema) {\n    this.schema = schema;\n  }\n\n  validateForm(data: any): ValidationResult {\n    try {\n      const result = this.schema.safeParse(data);\n      \n      if (result.success) {\n        return {\n          isValid: true,\n          errors: [],\n          warnings: this.generateFormWarnings(data),\n        };\n      }\n\n      const errors = result.error.errors.map(error => ({\n        field: error.path.join('.'),\n        message: error.message,\n        code: error.code,\n        severity: 'error' as const,\n      }));\n\n      return {\n        isValid: false,\n        errors,\n        warnings: this.generateFormWarnings(data),\n      };\n    } catch (error) {\n      console.error('Form validation error:', error);\n      return {\n        isValid: false,\n        errors: [{\n          field: 'form',\n          message: '表单验证过程中发生错误',\n          code: 'FORM_VALIDATION_ERROR',\n          severity: 'error',\n        }],\n        warnings: [],\n      };\n    }\n  }\n\n  private generateFormWarnings(data: any): ValidationWarning[] {\n    const warnings: ValidationWarning[] = [];\n\n    // Generate form-level warnings\n    if (data.items && Array.isArray(data.items)) {\n      const totalItems = data.items.length;\n      if (totalItems > 20) {\n        warnings.push({\n          field: 'items',\n          message: `账单包含${totalItems}个项目，较多`,\n          suggestion: '考虑合并相似项目或分拆为多个账单',\n        });\n      }\n\n      const totalAmount = data.items.reduce((sum: number, item: any) => {\n        return sum + (item.quantity || 0) * (item.unitPrice || 0);\n      }, 0);\n\n      if (totalAmount > 50000) {\n        warnings.push({\n          field: 'form',\n          message: '账单总金额较大',\n          suggestion: '确认金额计算是否正确',\n        });\n      }\n    }\n\n    return warnings;\n  }\n\n  getFieldValidator(fieldPath: string): FieldValidator {\n    if (!this.fieldValidators.has(fieldPath)) {\n      this.fieldValidators.set(fieldPath, new FieldValidator(this.schema));\n    }\n    return this.fieldValidators.get(fieldPath)!;\n  }\n\n  cleanup(): void {\n    this.fieldValidators.forEach(validator => validator.cleanup());\n    this.fieldValidators.clear();\n  }\n}\n\n// Validation error display utility\nexport const displayValidationErrors = (errors: ValidationError[]): void => {\n  errors.forEach(error => {\n    if (error.severity === 'error') {\n      billingNotifications.validation.requiredField(error.field);\n    }\n  });\n};\n\n// Business logic validation\nexport const validateBusinessRules = {\n  // Check if bill can be marked as paid\n  canMarkAsPaid: (bill: any): { valid: boolean; reason?: string } => {\n    if ((bill.remainingAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '账单还有未支付金额，无法标记为已支付',\n      };\n    }\n    return { valid: true };\n  },\n\n  // Check if payment amount is valid for bill\n  isValidPaymentAmount: (amount: number, bill: any): { valid: boolean; reason?: string } => {\n    const remainingAmount = bill.remainingAmount || 0;\n    \n    if (amount > remainingAmount) {\n      return {\n        valid: false,\n        reason: `支付金额不能超过待付金额 $${remainingAmount.toFixed(2)}`,\n      };\n    }\n    \n    if (amount <= 0) {\n      return {\n        valid: false,\n        reason: '支付金额必须大于0',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill can be deleted\n  canDeleteBill: (bill: any): { valid: boolean; reason?: string } => {\n    if (bill.status === 'paid') {\n      return {\n        valid: false,\n        reason: '已支付的账单不能删除',\n      };\n    }\n    \n    if ((bill.paidAmount || 0) > 0) {\n      return {\n        valid: false,\n        reason: '已有支付记录的账单不能删除',\n      };\n    }\n    \n    return { valid: true };\n  },\n\n  // Check if bill status transition is valid\n  isValidStatusTransition: (fromStatus: string, toStatus: string): { valid: boolean; reason?: string } => {\n    const validTransitions: Record<string, string[]> = {\n      draft: ['sent', 'cancelled'],\n      sent: ['confirmed', 'cancelled'],\n      confirmed: ['paid', 'cancelled'],\n      paid: [], // No transitions from paid\n      cancelled: [], // No transitions from cancelled\n    };\n\n    const allowedTransitions = validTransitions[fromStatus] || [];\n    \n    if (!allowedTransitions.includes(toStatus)) {\n      return {\n        valid: false,\n        reason: `不能从\"${fromStatus}\"状态转换到\"${toStatus}\"状态`,\n      };\n    }\n    \n    return { valid: true };\n  },\n};\n\n// Export validation constants\nexport const VALIDATION_CONSTANTS = {\n  MAX_BILL_AMOUNT: 999999.99,\n  MAX_ITEMS_PER_BILL: 50,\n  MAX_DESCRIPTION_LENGTH: 200,\n  MAX_NOTES_LENGTH: 1000,\n  MIN_PAYMENT_AMOUNT: 0.01,\n  MAX_DISCOUNT_RATE: 100,\n  DEBOUNCE_DELAY: 300,\n} as const;\n", "import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\nimport { paymentFormSchema } from '@/lib/validation/billing-schemas';\nimport { formatValidationErrors } from '@/lib/validation/billing-schemas';\nimport { validateBusinessRules } from '@/lib/validation/validation-utils';\nimport { auditLogger, rateLimiter, InputSanitizer } from '@/lib/billing-security';\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n// Enhanced error handling utility\nclass APIError extends Error {\n  constructor(\n    message: string,\n    public status: number = 500,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'APIError';\n  }\n}\n\n// Utility to get user info from Clerk with error handling\nasync function getClerkUserInfo(userId: string) {\n  try {\n    const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    });\n\n    if (!response.ok) {\n      throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching Clerk user:', error);\n    throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');\n  }\n}\n\n// Utility to make backend requests with comprehensive error handling\nasync function makeBackendRequest(url: string, options: RequestInit, userId: string, userEmail: string) {\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': userEmail,\n        ...options.headers,\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new APIError(\n        data.error || `Backend request failed: ${response.status}`,\n        response.status,\n        data.code || 'BACKEND_ERROR',\n        data\n      );\n    }\n\n    return data;\n  } catch (error) {\n    if (error instanceof APIError) {\n      throw error;\n    }\n\n    console.error('Backend request error:', error);\n    throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');\n  }\n}\n\n/**\n * GET /api/payments - Proxy to backend payments API\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n    \n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Authentication required' },\n        { status: 401 }\n      );\n    }\n\n    // Get user info from Clerk\n    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    }).then(res => res.json());\n\n    // Forward request to backend with authentication headers\n    const url = new URL(request.url);\n    const backendUrl = `${BACKEND_URL}/api/payments${url.search}`;\n    \n    const response = await fetch(backendUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': user.email_addresses[0]?.email_address || '',\n      },\n    });\n\n    const data = await response.json();\n    \n    if (!response.ok) {\n      return NextResponse.json(data, { status: response.status });\n    }\n\n    return NextResponse.json(data);\n  } catch (error) {\n    console.error('Error proxying payments request:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST /api/payments - Process payment with comprehensive validation and business logic\n */\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      auditLogger.logFinancialOperation(\n        'anonymous',\n        'anonymous',\n        'CREATE_PAYMENT_UNAUTHORIZED',\n        'payments',\n        { endpoint: '/api/payments' },\n        false,\n        'Authentication required',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Authentication required',\n          code: 'AUTH_REQUIRED',\n          message: '请先登录以处理支付'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Check rate limiting for payment requests (more restrictive)\n    const rateLimitResult = rateLimiter.checkRateLimit(userId, true);\n    if (!rateLimitResult.allowed) {\n      auditLogger.logFinancialOperation(\n        userId,\n        'unknown',\n        'CREATE_PAYMENT_RATE_LIMITED',\n        'payments',\n        { endpoint: '/api/payments', resetTime: rateLimitResult.resetTime },\n        false,\n        'Payment rate limit exceeded',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Payment rate limit exceeded',\n          code: 'PAYMENT_RATE_LIMIT_EXCEEDED',\n          message: '支付请求过于频繁，请稍后重试',\n          resetTime: rateLimitResult.resetTime\n        },\n        { status: 429 }\n      );\n    }\n\n    // Get user info from Clerk with error handling early\n    const user = await getClerkUserInfo(userId);\n    const userEmail = user.email_addresses[0]?.email_address || '';\n\n    if (!userEmail) {\n      return NextResponse.json(\n        {\n          error: 'User email not found',\n          code: 'USER_EMAIL_MISSING',\n          message: '用户邮箱信息缺失，请联系管理员'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Parse and validate request body\n    let body;\n    try {\n      body = await request.json();\n    } catch (error) {\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_PAYMENT_JSON_PARSE_FAILED',\n        'payments',\n        { error },\n        false,\n        'Failed to parse JSON request body',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Invalid JSON in request body',\n          code: 'INVALID_JSON',\n          message: '请求数据格式错误'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Sanitize payment input data\n    try {\n      body.amount = InputSanitizer.sanitizeAmount(body.amount);\n      body.paymentMethod = InputSanitizer.sanitizePaymentMethod(body.paymentMethod);\n\n      if (body.transactionId) {\n        body.transactionId = InputSanitizer.sanitizeText(body.transactionId, 100);\n      }\n      if (body.notes) {\n        body.notes = InputSanitizer.sanitizeText(body.notes, 500);\n      }\n    } catch (sanitizationError) {\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_PAYMENT_SANITIZATION_FAILED',\n        'payments',\n        { error: sanitizationError },\n        false,\n        sanitizationError instanceof Error ? sanitizationError.message : 'Payment sanitization failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Input sanitization failed',\n          code: 'SANITIZATION_ERROR',\n          message: '支付数据格式不正确'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate payment data using Zod schema\n    const validationResult = paymentFormSchema.safeParse(body);\n    if (!validationResult.success) {\n      const formattedErrors = formatValidationErrors(validationResult.error);\n\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_PAYMENT_VALIDATION_FAILED',\n        'payments',\n        { validationErrors: formattedErrors },\n        false,\n        'Payment validation failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Validation failed',\n          code: 'VALIDATION_ERROR',\n          message: '支付数据验证失败',\n          details: formattedErrors\n        },\n        { status: 400 }\n      );\n    }\n\n    const validatedData = validationResult.data;\n\n    // If bill ID is provided, validate payment amount against bill\n    if (body.billId) {\n      try {\n        // Fetch bill details to validate payment amount\n        const billUrl = `${BACKEND_URL}/api/bills/${body.billId}`;\n        const bill = await makeBackendRequest(billUrl, { method: 'GET' }, userId, userEmail);\n\n        // Validate payment amount against remaining bill amount\n        const paymentValidation = validateBusinessRules.isValidPaymentAmount(validatedData.amount, bill);\n        if (!paymentValidation.valid) {\n          return NextResponse.json(\n            {\n              error: 'Invalid payment amount',\n              code: 'INVALID_PAYMENT_AMOUNT',\n              message: paymentValidation.reason\n            },\n            { status: 400 }\n          );\n        }\n      } catch (error) {\n        if (error instanceof APIError && error.status === 404) {\n          return NextResponse.json(\n            {\n              error: 'Bill not found',\n              code: 'BILL_NOT_FOUND',\n              message: '指定的账单不存在'\n            },\n            { status: 404 }\n          );\n        }\n        throw error; // Re-throw other errors\n      }\n    }\n\n    // Additional business logic validation for payment methods\n    const paymentMethodValidation = validatePaymentMethod(validatedData);\n    if (!paymentMethodValidation.valid) {\n      return NextResponse.json(\n        {\n          error: 'Payment method validation failed',\n          code: 'PAYMENT_METHOD_ERROR',\n          message: paymentMethodValidation.reason\n        },\n        { status: 400 }\n      );\n    }\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/payments`;\n    const data = await makeBackendRequest(\n      backendUrl,\n      {\n        method: 'POST',\n        body: JSON.stringify(validatedData)\n      },\n      userId,\n      userEmail\n    );\n\n    // Log successful payment creation (mask sensitive data)\n    auditLogger.logFinancialOperation(\n      userId,\n      userEmail,\n      'CREATE_PAYMENT',\n      'payments',\n      {\n        paymentId: data.id,\n        billId: body.billId,\n        amount: validatedData.amount,\n        paymentMethod: validatedData.paymentMethod,\n        transactionId: validatedData.transactionId, // Will be masked by audit logger\n        patientId: body.patientId\n      },\n      true,\n      undefined,\n      request\n    );\n\n    return NextResponse.json(data, { status: 201 });\n  } catch (error) {\n    if (error instanceof APIError) {\n      return NextResponse.json(\n        {\n          error: error.message,\n          code: error.code,\n          message: error.message,\n          details: error.details\n        },\n        { status: error.status }\n      );\n    }\n\n    console.error('Unexpected error in POST /api/payments:', error);\n    return NextResponse.json(\n      {\n        error: 'Internal server error',\n        code: 'INTERNAL_ERROR',\n        message: '处理支付时发生服务器错误，请稍后重试'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// Additional payment method validation\nfunction validatePaymentMethod(paymentData: any): { valid: boolean; reason?: string } {\n  const { paymentMethod, transactionId, amount } = paymentData;\n\n  // Validate transaction ID requirements for different payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(paymentMethod)) {\n    if (!transactionId || transactionId.trim().length === 0) {\n      return {\n        valid: false,\n        reason: `${getPaymentMethodName(paymentMethod)}需要提供交易ID`\n      };\n    }\n  }\n\n  // Validate amount limits for different payment methods\n  if (paymentMethod === 'cash' && amount > 50000) {\n    return {\n      valid: false,\n      reason: '现金支付单笔金额不能超过50,000元'\n    };\n  }\n\n  if (paymentMethod === 'wechat' && amount > 200000) {\n    return {\n      valid: false,\n      reason: '微信支付单笔金额不能超过200,000元'\n    };\n  }\n\n  if (paymentMethod === 'alipay' && amount > 200000) {\n    return {\n      valid: false,\n      reason: '支付宝单笔金额不能超过200,000元'\n    };\n  }\n\n  return { valid: true };\n}\n\n// Helper function to get payment method display name\nfunction getPaymentMethodName(method: string): string {\n  const methods: Record<string, string> = {\n    cash: '现金',\n    card: '银行卡',\n    wechat: '微信支付',\n    alipay: '支付宝',\n    transfer: '银行转账',\n    installment: '分期付款',\n  };\n  return methods[method] || method;\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/payments',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\payments\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/payments/route\",\n        pathname: \"/api/payments\",\n        filename: \"route\",\n        bundlePath: \"app/api/payments/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\payments\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["ERROR_MESSAGES", "AUTH_REQUIRED", "message", "severity", "AUTH_SERVICE_ERROR", "CLERK_USER_FETCH_ERROR", "USER_EMAIL_MISSING", "VALIDATION_ERROR", "INVALID_JSON", "INVALID_LIMIT", "INVALID_PAGE", "SUBTOTAL_MISMATCH", "INVALID_PAYMENT_AMOUNT", "BILL_NOT_FOUND", "PAYMENT_METHOD_ERROR", "INSUFFICIENT_PERMISSIONS", "BACKEND_ERROR", "BACKEND_SERVICE_ERROR", "DATABASE_ERROR", "NETWORK_ERROR", "RATE_LIMIT_EXCEEDED", "SUSPICIOUS_ACTIVITY", "SESSION_EXPIRED", "INVALID_AUTHENTICATION", "UNAUTHORIZED_ACCESS", "PAYMENT_GATEWAY_ERROR", "CARD_DECLINED", "CARD_EXPIRED", "INVALID_CARD_NUMBER", "TRANSACTION_TIMEOUT", "DUPLICATE_TRANSACTION", "INSUFFICIENT_FUNDS", "PAYMENT_ALREADY_PROCESSED", "DEPOSIT_EXPIRED", "DEPOSIT_INSUFFICIENT_BALANCE", "REFUND_AMOUNT_EXCEEDS_PAYMENT", "DEPOSIT_NOT_FOUND", "CONFIGURATION_ERROR", "EXTERNAL_SERVICE_ERROR", "INTERNAL_ERROR", "UNKNOWN_ERROR", "Billing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "errorLog", "getInstance", "instance", "handleAPIError", "error", "options", "billingError", "showToast", "logError", "context", "fallbackMessage", "code", "errorInfo", "userMessage", "details", "timestamp", "Date", "showErrorToast", "handleNetworkError", "networkError", "handleValidationError", "validationErrors", "firstError", "validationError", "showSuccess", "description", "toast", "success", "duration", "showWarning", "warning", "showInfo", "info", "console", "push", "length", "shift", "toastOptions", "getErrorLog", "clearErrorLog", "billingError<PERSON>andler", "retry<PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "maxRetries", "baseDelay", "lastError", "attempt", "delay", "Math", "pow", "Promise", "resolve", "setTimeout", "BillingAPIError", "Error", "constructor", "status", "name", "validateBusinessRules", "isValidPaymentAmount", "amount", "bill", "remainingAmount", "valid", "reason", "toFixed", "BACKEND_URL", "process", "APIError", "getClerkUserInfo", "userId", "response", "fetch", "headers", "Authorization", "env", "CLERK_SECRET_KEY", "ok", "json", "makeBackendRequest", "url", "userEmail", "data", "GET", "request", "auth", "NextResponse", "user", "then", "res", "URL", "search", "backendUrl", "method", "email_addresses", "email_address", "POST", "body", "auditLogger", "logFinancialOperation", "endpoint", "rateLimitResult", "rateLimiter", "checkRateLimit", "allowed", "resetTime", "InputSanitizer", "sanitizeAmount", "paymentMethod", "sanitizePaymentMethod", "transactionId", "sanitizeText", "notes", "sanitizationError", "validationResult", "paymentFormSchema", "safeParse", "formattedErrors", "formatValidationErrors", "validatedData", "billId", "billUrl", "paymentValidation", "paymentMethodValidation", "validatePaymentMethod", "paymentData", "methodsRequiringTransactionId", "includes", "trim", "getPaymentMethodName", "cash", "card", "wechat", "alipay", "transfer", "installment", "JSON", "stringify", "paymentId", "id", "patientId", "undefined", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}