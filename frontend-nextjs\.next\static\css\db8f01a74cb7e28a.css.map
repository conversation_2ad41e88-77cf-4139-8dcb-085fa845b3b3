{"version": 3, "sources": ["webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/agenda.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/css/react-big-calendar.css", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/reset.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/styles.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/toolbar.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/variables.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/event.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/month.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/time-column.scss", "webpack://_N_E/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/sass/time-grid.scss", "webpack://_N_E/src/styles/calendar.css"], "names": [], "mappings": "AAuDE,gBCwXF,CC/aA,SACE,aAAA,CACA,YAAA,CACA,QDEF,CCCA,eACE,gBAAA,CACA,mBAAA,CACA,yBAAA,CAAA,sBAAA,CAAA,iBAAA,CACA,cDEF,CCCA,yBACE,kBDEF,CCCA,mCACE,QAAA,CACA,SDEF,CElBA,cACE,6BAAA,CAAA,qBAAA,CACA,WAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,yBAAA,CAAA,sBAAA,CAAA,mBFqBF,CElBA,oBACE,kBFqBF,CElBA,YACE,WFqBF,CElBA,2DAGE,0BAAA,CAAA,kBFqBF,CElBA,0BACE,eAAA,CACA,iBAAA,CACA,KAAA,CACA,MAAA,CACA,OAAA,CACA,QFqBF,CElBA,kFACE,aAAA,CACA,eAAA,CACA,sBAAA,CACA,kBFqBF,CElBA,SACE,aFqBF,CElBA,eACE,aFqBF,CElBA,kBACE,kBFqBF,CElBA,YACE,eAAA,CACA,kBAAA,CAAA,eAAA,CAAA,WAAA,CACA,sBAAA,CACA,kBAAA,CACA,aAAA,CACA,iBAAA,CACA,qBAAA,CACA,eAAA,CACA,aAAA,CACA,YAAA,CACA,4BFqBF,CEnBE,wBACE,0BFqBJ,CElBE,iCACE,mBAAA,CACA,2BFoBJ,CEhBI,yDAGE,aAAA,CACA,oBFgBN,CEXA,iBACE,aAAA,CACA,eAAA,CACA,QAAA,CACA,SAAA,CACA,WAAA,CACA,cAAA,CACA,wBAAA,CAAA,qBAAA,CAAA,oBAAA,CAAA,gBFcF,CEXA,iBACE,iBAAA,CACA,qBAAA,CAAA,oBAAA,CAAA,gBAAA,CACA,wBAAA,CACA,SFcF,CEXA,4BACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,WFcF,CEZE,8DACE,WAAA,CACA,iBAAA,CAIA,uBAAA,CACA,oBFcJ,CEXI,iFACE,YFaN,CERA,WACE,wBFWF,CGlIA,aACE,mBAAA,CAAA,mBAAA,CACA,kBAAA,CAAA,cAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,sBAAA,CACA,wBAAA,CAAA,qBAAA,CACA,kBAAA,CACA,cHqIF,CGnIE,gCACE,kBAAA,CAAA,mBAAA,CAAA,WAAA,CACA,cAAA,CACA,iBHqIJ,CGlIE,oBACE,aCGQ,CDFR,oBAAA,CACA,QAAA,CACA,iBAAA,CACA,qBAAA,CACA,eAAA,CACA,qBAAA,CACA,qBAAA,CACA,oBAAA,CACA,iBAAA,CACA,kBAAA,CACA,kBHoIJ,CGlII,0DAEE,qBAAA,CACA,mDAAA,CAAA,2CAAA,CACA,wBAnCc,CAoCd,oBHmIN,CGjIM,4IAEE,aCnBI,CDoBJ,wBAAA,CACA,oBHkIR,CGxHI,oDALE,aC1BM,CD2BN,wBAhDc,CAiDd,oBHsIN,CGnII,0BAEE,cHiIN,CG1HA,eACE,oBAAA,CACA,kBH6HF,CG3HE,mDACE,yBAAA,CACA,4BH6HJ,CG1HE,mDACE,wBAAA,CACA,2BH4HJ,CGzHE,4DACE,iBAAA,CACA,wBAAA,CACA,2BH2HJ,CGxHE,4DACE,iBAAA,CACA,yBAAA,CACA,4BH0HJ,CGvHE,yDACE,eHyHJ,CGtHE,6BACE,gBHwHJ,CGrHE,sCACE,aAAA,CACA,iBHuHJ,CGpHE,oDAEE,gBHqHJ,CGjHA,yBACE,aACE,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBHoHF,CACF,CKjOA,+CACE,WAAA,CACA,6BAAA,CAAA,qBAAA,CACA,uBAAA,CAAA,eAAA,CACA,QAAA,CACA,eDac,CCZd,wBDOS,CCNT,iBDUoB,CCTpB,UDQY,CCPZ,cAAA,CACA,UAAA,CACA,eLmOF,CKjOE,+IACE,cAAA,CACA,mBLmOJ,CKhOE,yEACE,wBLkOJ,CK/NE,2DACE,wBLiOJ,CK7NA,iBAEE,aL+NF,CK5NA,oBACE,mDAAA,CAAA,2CL+NF,CK5NA,2BACE,wBAAA,CACA,2BL+NF,CK7NA,2BACE,yBAAA,CACA,4BLgOF,CK5NA,6BACE,wBAAA,CACA,yBL+NF,CK7NA,2BACE,2BAAA,CACA,4BLgOF,CMpRA,SACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,kBNuRF,CMpRA,iBACE,iBNuRF,CMhRA,mBACE,+BNkRF,CM/QA,eAEE,qCAAA,CACA,SFDa,CEEb,eAAA,CACA,aAAA,CACA,WAAA,CACA,kBAAA,CACA,aNiRF,CMhRE,0CAEE,aNiRJ,CM7QA,gBACE,iBAAA,CACA,qBAAA,CAEA,2BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,UAAA,CACA,qBAAA,CAAA,oBAAA,CAAA,gBAAA,CACA,wBAAA,CAEA,WN+QF,CM5QA,kCAVE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,4BN0RF,CMjRA,kBAEE,6BAAA,CAAA,sBAAA,CAAA,kBN+QF,CM5QA,eACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,iBAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,2BAAA,CAAA,cAAA,CACA,eAAA,CAEA,WN8QF,CM5QE,8BACE,yBN8QJ,CM1QA,eACE,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,WAAA,CACA,iBN8QF,CM3QE,uBACE,eN6QJ,CMzQI,kEAGE,aAAA,CACA,oBNyQN,CMpQA,YAEE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,6BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,kBAAA,CACA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,eAAA,CACA,SNsQF,CMnQA,YACE,kBAAA,CAAA,eAAA,CAAA,WNsQF,CMpQE,wBACE,0BNsQJ,CMnQE,iCACE,mBAAA,CACA,2BNqQJ,CMjQA,aACE,iBAAA,CACA,SAAA,CACA,wBAAA,CACA,qBAAA,CACA,6CAAA,CAAA,qCAAA,CACA,YNoQF,CMlQE,iBACE,cNoQJ,CMhQA,oBACE,+BAAA,CACA,sBAAA,CACA,gBNmQF,CD9XA,iBACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,aCiYF,CD/XE,wCACE,UAAA,CACA,qBAAA,CACA,gBAAA,CACA,wBCiYJ,CD/XI,oDACE,gBAAA,CACA,kBCiYN,CD9XI,8DACE,iBAAA,CACA,kBAAA,CACA,wBCgYN,CD7XI,uDACE,0BC+XN,CD3XM,gEACE,mBAAA,CACA,2BC6XR,CDzXI,oDACE,yBC2XN,CDxXI,oDACE,eAAA,CACA,eAAA,CACA,4BC0XN,CDxXM,6DACE,gBC0XR,CDpXA,sBACE,wBCuXF,CDrXE,iDACE,YCuXJ,CDrXE,kDACE,YCuXJ,CDnXA,4CAEE,kBCsXF,CDjXA,uBACE,UCoXF,COzbA,iBACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,eP4bF,CO1bE,qCACE,kBAAA,CAAA,UAAA,CAAA,MP4bJ,COvbA,oBACE,4BAAA,CACA,eAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,2BAAA,CAAA,uBP0bF,COvbA,oCAEE,kBAAA,CAAA,aAAA,CAAA,SP0bF,COvbA,WACE,aP0bF,COvbA,cACE,iBP0bF,COxbE,oCACE,QAAA,CACA,MAAA,CACA,iBAAA,CACA,OAAA,CACA,iBAAA,CACA,KP0bJ,COxbI,4CACE,SAAA,CACA,OP0bN,COtbE,6DACE,wBAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,eAAA,CACA,eAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,sBAAA,CACA,eAAA,CACA,iBPwbJ,COrbE,oCAEE,WPsbJ,COnbE,+BACE,kBAAA,CAAA,aAAA,CAAA,SAAA,CACA,iBAAA,CACA,UPqbJ,COlbE,iCACE,UAAA,CACA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,oBAAA,CACA,aAAA,CACA,WAAA,CACA,cPobJ,COjbE,6BACE,4BPmbJ,CO9aE,2FAEE,eAAA,CACA,MAAA,CACA,sBAAA,CACA,2BAAA,CACA,UAAA,CACA,iBPibJ,CO9aE,0CACE,ePgbJ,CO7aE,kDACE,cAAA,CACA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,2BAAA,CAAA,cP+aJ,CO5aE,0DACE,YP8aJ,CO3aE,uCACE,eP6aJ,CO1aE,0EAEE,WAAA,CAEA,kBAAA,CAAA,gBAAA,CAAA,UAAA,CACA,4BAAA,CAAA,eP2aJ,COvaA,kDACE,gBP0aF,COvaA,eACE,kBAAA,CAAA,gBAAA,CAAA,UP0aF,COxaE,uBACE,eP0aJ,COtaA,gBACE,iBPyaF,CQ3iBA,oBACE,UAAA,CACA,iBAAA,CACA,+BJMwB,CILxB,WJIqB,CIHrB,aAAA,CACA,UAAA,CACA,WR8iBF,CQ3iBA,oBACE,WR8iBF,CQ3iBA,eACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,kBAAA,CAAA,UAAA,CAAA,MAAA,CACA,UAAA,CACA,qBAAA,CACA,YR8iBF,CQ5iBE,gCACE,kBAAA,CACA,gBR8iBJ,CQ3iBE,gCACE,8BAAA,CAAA,sBAAA,CACA,UAAA,CACA,WAAA,CACA,iBR6iBJ,CQ3iBE,iDACE,0BR6iBJ,CQ1iBE,kCACE,iBAAA,CACA,SR4iBJ,CQziBE,wBACE,6BAAA,CAAA,qBAAA,CACA,eR2iBJ,CQviBA,iBACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,kBAAA,CAAA,iBAAA,CAAA,aAAA,CACA,6BAAA,CAAA,4BAAA,CAAA,sBAAA,CAAA,kBR0iBF,CQxiBE,iCACE,2BR0iBJ,CQviBE,0CACE,oBAAA,CACA,0BRyiBJ,CQliBE,iFACE,4BRuiBJ,CQ3hBA,iCACE,YR8hBF,CQ3hBA,yBACE,kBAAA,CAAA,UAAA,CAAA,MAAA,CACA,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,WAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBAAA,CACA,0BR8hBF,CQ5hBE,kCACE,mBAAA,CACA,2BR8hBJ,CQ3hBE,mDACE,4BAAA,CACA,mBAAA,CAAA,aR6hBJ,CQzhBA,kBACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,kBAAA,CAAA,eAAA,CAAA,WAAA,CACA,uBAAA,CAAA,oBAAA,CAAA,sBAAA,CACA,UAAA,CACA,yBAAA,CACA,eAAA,CACA,iBR4hBF,CQ1hBE,mCACE,kBAAA,CAAA,aAAA,CAAA,SR4hBJ,CQzhBE,wBACE,0BR2hBJ,CQxhBE,iCACE,mBAAA,CACA,2BR0hBJ,CQvhBE,gCACE,UAAA,CACA,qBAAA,CAAA,oBAAA,CAAA,gBAAA,CACA,wBRyhBJ,CQrhBA,4BACE,iBAAA,CACA,SAAA,CACA,MAAA,CACA,OAAA,CACA,UAAA,CAEA,wBJjHmB,CIkHnB,mBRuhBF,CQnhBE,+CACE,mBAAA,CAAA,mBAAA,CAAA,YAAA,CACA,2BAAA,CAAA,4BAAA,CAAA,yBAAA,CAAA,qBRshBJ,CQnhBE,4CACE,WRqhBJ,CSzqBA,cACE,uCAAwC,CACxC,4BAA6B,CAC7B,mBACF,CAGA,YACE,qCAAyC,CACzC,kCAAmC,CACnC,eAAgB,CAChB,iBAAmB,CACnB,0CAA2C,CAC3C,eACF,CAEA,wBACE,wCACF,CAGA,aACE,YAAa,CACb,kBAAmB,CACnB,6BAA8B,CAC9B,kBAAmB,CACnB,aAAe,CACf,qCAAyC,CACzC,mBACF,CAEA,oBACE,sBAAyB,CACzB,iBAAmB,CACnB,eAAgB,CAChB,qBAAuB,CACvB,qBAAuB,CACvB,4BAA6B,CAC7B,4BAA6B,CAC7B,4BACF,CAEA,0BACE,mCAAoC,CACpC,mCACF,CAEA,0BACE,YAAa,CACb,qCACF,CAEA,+BACE,oCAAqC,CACrC,oCACF,CAEA,mBACE,kBAAmB,CACnB,eAAgB,CAChB,4BACF,CAGA,gBACE,mCAAoC,CACpC,mBAAqB,CACrB,eACF,CAEA,eACE,0CACF,CAEA,0BACE,kBACF,CAEA,eACE,gBAAiB,CACjB,cACF,CAEA,iBACE,iBAAmB,CACnB,kCAAmC,CACnC,oBACF,CAEA,uBACE,4BACF,CAEA,6BACE,qCACF,CAEA,uBACE,eACF,CAEA,yBACE,yBACF,CAGA,YACE,uCAAwC,CACxC,+BACF,CAEA,kBACE,qCACF,CAEA,6BACE,qCACF,CAEA,sBACE,uCACF,CAEA,wBACE,wCACF,CAGA,WACE,qBAAuB,CACvB,gBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAChB,cAAe,CACf,iBAAkB,CAClB,cACF,CAEA,iBACE,UACF,CAEA,wBACE,2DACF,CAEA,iBACE,gBAAkB,CAClB,eACF,CAEA,oBACE,qCACF,CAGA,eACE,mCAAoC,CACpC,mBAAqB,CACrB,eACF,CAEA,iBACE,0CAA2C,CAC3C,qCACF,CAEA,kBACE,uCACF,CAEA,iBACE,qCAAyC,CACzC,yCACF,CAEA,qCACE,6CACF,CAEA,eACE,6CACF,CAEA,uBACE,wCACF,CAEA,4BACE,oCAAqC,CACrC,UAAW,CACX,UACF,CAGA,iBACE,mCAAoC,CACpC,mBAAqB,CACrB,eACF,CAEA,uBACE,UACF,CAEA,uCACE,qCAAyC,CACzC,cAAgB,CAChB,iBAAmB,CACnB,eAAgB,CAChB,0CACF,CAEA,uCAEE,iBAAmB,CACnB,kCAEF,CAEA,+EANE,cAAgB,CAGhB,0CAMF,CAGA,aACE,oCAAqC,CACrC,oCAAqC,CACrC,mCAAoC,CACpC,mBAAqB,CACrB,sEAA8E,CAC9E,UACF,CAEA,oBACE,cAAgB,CAChB,0CAA2C,CAC3C,eACF,CAGA,eACE,yBAA0B,CAC1B,gBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,oBACF,CAEA,qBACE,4BACF,CAGA,yBACE,aACE,qBAAsB,CACtB,SACF,CAEA,oBACE,gBAAkB,CAClB,oBACF,CAEA,mBACE,cACF,CAEA,WACE,gBAAkB,CAClB,eACF,CAEA,YACE,gBAAkB,CAClB,eACF,CACF,CAGA,4BACE,wBAAyB,CACzB,WACF,CAEA,4BACE,wBAAyB,CACzB,WACF,CAEA,4BACE,wBAAyB,CACzB,WACF,CAEA,4BACE,wBAAyB,CACzB,WACF,CAEA,0BACE,wBAAyB,CACzB,WACF,CAGA,6BACE,6BACF,CAEA,0BACE,6BACF,CAGA,kBACE,mDACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAGA,wBACE,kDACF,CAEA,8BACE,YAAa,CACb,iBAAkB,CAClB,WAAa,CACb,aAAe,CACf,gBACF", "file": "static/css/db8f01a74cb7e28a.css", "sourcesContent": [null, null, null, null, null, null, null, null, null, null, "/* Custom styles for react-big-calendar to match Shadcn/ui theme */\n\n/* Calendar container */\n.rbc-calendar {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n  font-family: inherit;\n}\n\n/* Header styles */\n.rbc-header {\n  background-color: hsl(var(--muted) / 0.5);\n  color: hsl(var(--muted-foreground));\n  font-weight: 500;\n  font-size: 0.875rem;\n  border-bottom: 1px solid hsl(var(--border));\n  padding: 8px 4px;\n}\n\n.rbc-header + .rbc-header {\n  border-left: 1px solid hsl(var(--border));\n}\n\n/* Toolbar styles */\n.rbc-toolbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  padding: 0.5rem;\n  background-color: hsl(var(--muted) / 0.3);\n  border-radius: 0.5rem;\n}\n\n.rbc-toolbar button {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-radius: 0.375rem;\n  transition: colors 0.2s;\n  border: 1px solid transparent;\n  background-color: transparent;\n  color: hsl(var(--foreground));\n}\n\n.rbc-toolbar button:hover {\n  background-color: hsl(var(--accent));\n  color: hsl(var(--accent-foreground));\n}\n\n.rbc-toolbar button:focus {\n  outline: none;\n  box-shadow: 0 0 0 2px hsl(var(--ring));\n}\n\n.rbc-toolbar button.rbc-active {\n  background-color: hsl(var(--primary));\n  color: hsl(var(--primary-foreground));\n}\n\n.rbc-toolbar-label {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: hsl(var(--foreground));\n}\n\n/* Month view styles */\n.rbc-month-view {\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.rbc-month-row {\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n.rbc-month-row:last-child {\n  border-bottom: none;\n}\n\n.rbc-date-cell {\n  text-align: right;\n  padding: 0.25rem;\n}\n\n.rbc-date-cell > a {\n  font-size: 0.875rem;\n  color: hsl(var(--muted-foreground));\n  transition: color 0.2s;\n}\n\n.rbc-date-cell > a:hover {\n  color: hsl(var(--foreground));\n}\n\n.rbc-date-cell.rbc-off-range {\n  color: hsl(var(--muted-foreground) / 0.5);\n}\n\n.rbc-date-cell.rbc-now {\n  font-weight: 600;\n}\n\n.rbc-date-cell.rbc-now > a {\n  color: hsl(var(--primary));\n}\n\n/* Day cell styles */\n.rbc-day-bg {\n  background-color: hsl(var(--background));\n  transition: background-color 0.2s;\n}\n\n.rbc-day-bg:hover {\n  background-color: hsl(var(--muted) / 0.3);\n}\n\n.rbc-day-bg.rbc-off-range-bg {\n  background-color: hsl(var(--muted) / 0.2);\n}\n\n.rbc-day-bg.rbc-today {\n  background-color: hsl(var(--primary) / 0.1);\n}\n\n.rbc-day-bg + .rbc-day-bg {\n  border-left: 1px solid hsl(var(--border));\n}\n\n/* Event styles */\n.rbc-event {\n  border-radius: 0.125rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  padding: 2px 4px;\n  margin: 1px 2px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.rbc-event:hover {\n  opacity: 0.8;\n}\n\n.rbc-event.rbc-selected {\n  box-shadow: 0 0 0 2px hsl(var(--ring)), 0 0 0 4px transparent;\n}\n\n.rbc-event-label {\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.rbc-event-overlaps {\n  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n}\n\n/* Week and day view styles */\n.rbc-time-view {\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.rbc-time-header {\n  border-bottom: 1px solid hsl(var(--border));\n  background-color: hsl(var(--muted) / 0.3);\n}\n\n.rbc-time-content {\n  background-color: hsl(var(--background));\n}\n\n.rbc-time-gutter {\n  background-color: hsl(var(--muted) / 0.2);\n  border-right: 1px solid hsl(var(--border));\n}\n\n.rbc-time-gutter .rbc-timeslot-group {\n  border-bottom: 1px solid hsl(var(--border) / 0.5);\n}\n\n.rbc-time-slot {\n  border-bottom: 1px solid hsl(var(--border) / 0.3);\n}\n\n.rbc-time-slot.rbc-now {\n  background-color: hsl(var(--primary) / 0.05);\n}\n\n.rbc-current-time-indicator {\n  background-color: hsl(var(--primary));\n  height: 2px;\n  z-index: 10;\n}\n\n/* Agenda view styles */\n.rbc-agenda-view {\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.rbc-agenda-view table {\n  width: 100%;\n}\n\n.rbc-agenda-view .rbc-agenda-date-cell {\n  background-color: hsl(var(--muted) / 0.3);\n  padding: 0.75rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n.rbc-agenda-view .rbc-agenda-time-cell {\n  padding: 0.75rem;\n  font-size: 0.875rem;\n  color: hsl(var(--muted-foreground));\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n.rbc-agenda-view .rbc-agenda-event-cell {\n  padding: 0.75rem;\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n/* Popup styles */\n.rbc-overlay {\n  background-color: hsl(var(--popover));\n  color: hsl(var(--popover-foreground));\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  z-index: 50;\n}\n\n.rbc-overlay-header {\n  padding: 0.75rem;\n  border-bottom: 1px solid hsl(var(--border));\n  font-weight: 500;\n}\n\n/* Show more link */\n.rbc-show-more {\n  color: hsl(var(--primary));\n  font-size: 0.75rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: color 0.2s;\n}\n\n.rbc-show-more:hover {\n  color: hsl(var(--primary) / 0.8);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .rbc-toolbar {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .rbc-toolbar button {\n    font-size: 0.75rem;\n    padding: 0.25rem 0.5rem;\n  }\n\n  .rbc-toolbar-label {\n    font-size: 1rem;\n  }\n\n  .rbc-event {\n    font-size: 0.75rem;\n    padding: 1px 2px;\n  }\n\n  .rbc-header {\n    font-size: 0.75rem;\n    padding: 4px 2px;\n  }\n}\n\n/* Custom status colors - these will be overridden by eventStyleGetter */\n.rbc-event.status-scheduled {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.rbc-event.status-confirmed {\n  background-color: #10b981;\n  color: white;\n}\n\n.rbc-event.status-completed {\n  background-color: #6b7280;\n  color: white;\n}\n\n.rbc-event.status-cancelled {\n  background-color: #ef4444;\n  color: white;\n}\n\n.rbc-event.status-no-show {\n  background-color: #f59e0b;\n  color: white;\n}\n\n/* Appointment type indicators */\n.rbc-event.type-consultation {\n  border-left: 4px solid #8b5cf6;\n}\n\n.rbc-event.type-treatment {\n  border-left: 4px solid #06b6d4;\n}\n\n/* Loading state */\n.calendar-loading {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: .5;\n  }\n}\n\n/* Conflict warning styles */\n.rbc-event.has-conflict {\n  box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px transparent;\n}\n\n.rbc-event.has-conflict::after {\n  content: '⚠️';\n  position: absolute;\n  top: -0.25rem;\n  right: -0.25rem;\n  font-size: 0.75rem;\n}\n"], "sourceRoot": ""}