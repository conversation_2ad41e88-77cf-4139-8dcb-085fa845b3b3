try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="d25f5690-f521-45e2-abde-50b88c4d99ed",e._sentryDebugIdIdentifier="sentry-dbid-d25f5690-f521-45e2-abde-50b88c4d99ed")}catch(e){}"use strict";(()=>{var e={};e.id=9153,e.ids=[9153],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8029:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>k,serverHooks:()=>A,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>E});var i={};t.r(i),t.d(i,{DELETE:()=>w,GET:()=>h,HEAD:()=>P,OPTIONS:()=>v,PATCH:()=>b,POST:()=>g,PUT:()=>m});var n=t(86047),o=t(85544),s=t(36135),a=t(63033),d=t(53547),p=t(54360),u=t(19761);let c=(0,d.ZA)(async(e,r,{params:t})=>{try{let r=(0,p.o)(e),i=await r.getPatientInteraction(t.id);if("doctor"===e.role){if(("object"==typeof i.staffMember?i.staffMember.id:i.staffMember)!==e.payloadUserId&&!["consultation-note","treatment-discussion","in-person-visit"].includes(i.interactionType))return(0,d.WX)("Permission denied",403)}else if("front-desk"===e.role&&!["phone-call","email","billing-inquiry"].includes(i.interactionType))return(0,d.WX)("Permission denied",403);return(0,d.$y)(i)}catch(e){return console.error("Error fetching patient interaction:",e),(0,d.WX)("Failed to fetch patient interaction")}}),l=(0,d.ZA)(async(e,r,{params:t})=>{try{let i=(0,p.o)(e),n=await r.json(),o=await i.getPatientInteraction(t.id),s="object"==typeof o.staffMember?o.staffMember.id:o.staffMember;if("admin"!==e.role&&s!==e.payloadUserId)return(0,d.WX)("You can only update your own interactions",403);delete n.staffMember;let a=await i.updatePatientInteraction(t.id,n);return(0,d.$y)(a)}catch(e){return console.error("Error updating patient interaction:",e),(0,d.WX)("Failed to update patient interaction")}}),x=(0,d.ZA)(async(e,r,{params:t})=>{try{if("admin"!==e.role)return(0,d.WX)("Only administrators can delete interactions",403);let r=(0,p.o)(e);return await r.deletePatientInteraction(t.id),(0,d.$y)({message:"Patient interaction deleted successfully"})}catch(e){return console.error("Error deleting patient interaction:",e),(0,d.WX)("Failed to delete patient interaction")}}),f={...a},y="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,i)=>{let n;try{let e=y?.getStore();n=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/patient-interactions/[id]",headers:n}).apply(t,i)}})}let h=q(c,"GET"),g=q(void 0,"POST"),m=q(void 0,"PUT"),b=q(l,"PATCH"),w=q(x,"DELETE"),P=q(void 0,"HEAD"),v=q(void 0,"OPTIONS"),k=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/patient-interactions/[id]/route",pathname:"/api/patient-interactions/[id]",filename:"route",bundlePath:"app/api/patient-interactions/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-interactions\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:T,workUnitAsyncStorage:E,serverHooks:A}=k;function _(){return(0,s.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:E})}},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[55,3738,1950,5886,9615,125],()=>t(8029));module.exports=i})();
//# sourceMappingURL=route.js.map