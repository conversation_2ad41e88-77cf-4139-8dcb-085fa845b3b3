try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="5a1d65f3-406d-44b4-9583-8e17fdee264e",e._sentryDebugIdIdentifier="sentry-dbid-5a1d65f3-406d-44b4-9583-8e17fdee264e")}catch(e){}exports.id=7494,exports.ids=[7494],exports.modules={9343:(e,t,n)=>{"use strict";n.d(t,{Avatar:()=>o,AvatarFallback:()=>d,AvatarImage:()=>i});var a=n(24443);n(60222);var r=n(48199),s=n(72595);function o({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function i({className:e,...t}){return(0,a.jsx)(r._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",e),...t,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function d({className:e,...t}){return(0,a.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}},9597:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\separator.tsx","Separator")},11035:(e,t,n)=>{"use strict";n.d(t,{I:()=>c,SQ:()=>l,_2:()=>u,hO:()=>m,lp:()=>p,mB:()=>f,rI:()=>i,ty:()=>d});var a=n(24443);n(60222);var r=n(60642),s=n(84338),o=n(72595);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dropdown-menu",...e,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function d({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...e,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function l({className:e,sideOffset:t=4,...n}){return(0,a.jsx)(r.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...n,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c({...e}){return(0,a.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...e,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u({className:e,inset:t,variant:n="default",...s}){return(0,a.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m({className:e,children:t,checked:n,...i}){return(0,a.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),checked:n,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,a.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,a.jsx)(s.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),t]})}function p({className:e,inset:t,...n}){return(0,a.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function f({className:e,...t}){return(0,a.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",e),...t,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},12099:(e,t,n)=>{"use strict";n.d(t,{$7:()=>q.A,Bl:()=>v.A,C0:()=>O.A,FI:()=>Y,GI:()=>$.A,Gk:()=>U.A,Hv:()=>K.A,MR:()=>o.A,QO:()=>S.A,VS:()=>z.A,_L:()=>F.A,_v:()=>T.A,as:()=>J.A,bY:()=>_.A,hI:()=>A.A,iW:()=>B.A,jQ:()=>G.A,nR:()=>f.A,rI:()=>V.A,st:()=>L.A,uI:()=>w.A,uJ:()=>H.A,vg:()=>C.A});var a=n(50628),r=n(16701),s=n(91537),o=n(4826),i=n(25816),d=n(42363),l=n(80409),c=n(71672),u=n(15431),m=n(9752),p=n(79397),f=n(44537),b=n(59844),x=n(43839),h=n(1675),y=n(20742),g=n(12305),v=n(53503),j=n(9556),w=n(20866),S=n(92700),C=n(46643),A=n(12741),k=n(15355),D=n(93596),N=n(25804),M=n(277),P=n(14736),R=n(6776),I=n(98862),B=n(69322),T=n(95748),E=n(24658),z=n(92113),U=n(33078),V=n(22031),L=n(49230),G=n(68343),_=n(87615),F=n(2212),O=n(17838),H=n(33520),K=n(47145),$=n(49958),q=n(97185),J=n(58246);let Y={dashboard:a.A,logo:r.A,login:s.A,close:o.A,product:i.A,spinner:d.A,kanban:l.A,chevronLeft:c.A,chevronRight:u.A,trash:m.A,employee:p.A,post:f.A,page:b.A,userPen:x.A,user2:h.A,media:y.A,settings:g.A,billing:v.A,ellipsis:j.A,add:w.A,warning:S.A,user:C.A,arrowRight:A.A,help:k.A,pizza:D.A,sun:N.A,moon:M.A,laptop:P.A,github:R.A,twitter:I.A,check:B.A,calendar:T.A,users:E.A,medical:z.A}},14866:(e,t,n)=>{"use strict";n.d(t,{default:()=>i});var a=n(24443),r=n(32259),s=n(17838),o=n(33284);function i(){let{query:e}=(0,r.useKBar)();return(0,a.jsx)("div",{className:"w-full space-y-2","data-sentry-component":"SearchInput","data-sentry-source-file":"search-input.tsx",children:(0,a.jsxs)(o.$,{variant:"outline",className:"bg-background text-muted-foreground relative h-9 w-full justify-start rounded-[0.5rem] text-sm font-normal shadow-none sm:pr-12 md:w-40 lg:w-64",onClick:e.toggle,"data-sentry-element":"Button","data-sentry-source-file":"search-input.tsx",children:[(0,a.jsx)(s.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconSearch","data-sentry-source-file":"search-input.tsx"}),"Search...",(0,a.jsxs)("kbd",{className:"bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-6 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex",children:[(0,a.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]})})}},17440:(e,t,n)=>{"use strict";n.d(t,{M:()=>s});var a=n(24443),r=n(9343);function s({className:e,showInfo:t=!1,user:n}){return(0,a.jsxs)("div",{className:"flex items-center gap-2","data-sentry-component":"UserAvatarProfile","data-sentry-source-file":"user-avatar-profile.tsx",children:[(0,a.jsxs)(r.Avatar,{className:e,"data-sentry-element":"Avatar","data-sentry-source-file":"user-avatar-profile.tsx",children:[(0,a.jsx)(r.AvatarImage,{src:n?.imageUrl||"",alt:n?.fullName||"","data-sentry-element":"AvatarImage","data-sentry-source-file":"user-avatar-profile.tsx"}),(0,a.jsx)(r.AvatarFallback,{className:"rounded-lg","data-sentry-element":"AvatarFallback","data-sentry-source-file":"user-avatar-profile.tsx",children:n?.fullName?.slice(0,2)?.toUpperCase()||"CN"})]}),t&&(0,a.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,a.jsx)("span",{className:"truncate font-semibold",children:n?.fullName||""}),(0,a.jsx)("span",{className:"truncate text-xs",children:n?.emailAddresses[0].emailAddress||""})]})]})}},18984:(e,t,n)=>{"use strict";n.d(t,{J:()=>o});var a=n(24443);n(60222);var r=n(11348),s=n(72595);function o({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t,"data-sentry-element":"LabelPrimitive.Root","data-sentry-component":"Label","data-sentry-source-file":"label.tsx"})}},19342:(e,t,n)=>{"use strict";n.d(t,{p:()=>s});var a=n(24443);n(60222);var r=n(72595);function s({className:e,type:t,...n}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...n,"data-sentry-component":"Input","data-sentry-source-file":"input.tsx"})}},21660:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\kbar\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\index.tsx","default")},22762:(e,t,n)=>{"use strict";n.d(t,{$:()=>d,r:()=>i});var a=n(78869);n(22576);var r=n(41488),s=n(31963),o=n(19557);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:n,asChild:s=!1,...d}){let l=s?r.DX:"button";return(0,a.jsx)(l,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:n,className:e})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},23032:(e,t,n)=>{"use strict";n.d(t,{TR:()=>f,bq:()=>m,eb:()=>b,gC:()=>p,l6:()=>l,mi:()=>x,s3:()=>c,yv:()=>u});var a=n(24443);n(60222);var r=n(63299),s=n(75838),o=n(84338),i=n(86127),d=n(72595);function l({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e,"data-sentry-element":"SelectPrimitive.Root","data-sentry-component":"Select","data-sentry-source-file":"select.tsx"})}function c({...e}){return(0,a.jsx)(r.YJ,{"data-slot":"select-group",...e,"data-sentry-element":"SelectPrimitive.Group","data-sentry-component":"SelectGroup","data-sentry-source-file":"select.tsx"})}function u({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e,"data-sentry-element":"SelectPrimitive.Value","data-sentry-component":"SelectValue","data-sentry-source-file":"select.tsx"})}function m({className:e,size:t="default",children:n,...o}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...o,"data-sentry-element":"SelectPrimitive.Trigger","data-sentry-component":"SelectTrigger","data-sentry-source-file":"select.tsx",children:[n,(0,a.jsx)(r.In,{asChild:!0,"data-sentry-element":"SelectPrimitive.Icon","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(s.A,{className:"size-4 opacity-50","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})]})}function p({className:e,children:t,position:n="popper",...s}){return(0,a.jsx)(r.ZL,{"data-sentry-element":"SelectPrimitive.Portal","data-sentry-component":"SelectContent","data-sentry-source-file":"select.tsx",children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...s,"data-sentry-element":"SelectPrimitive.Content","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)(h,{"data-sentry-element":"SelectScrollUpButton","data-sentry-source-file":"select.tsx"}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),"data-sentry-element":"SelectPrimitive.Viewport","data-sentry-source-file":"select.tsx",children:t}),(0,a.jsx)(y,{"data-sentry-element":"SelectScrollDownButton","data-sentry-source-file":"select.tsx"})]})})}function f({className:e,...t}){return(0,a.jsx)(r.JU,{"data-slot":"select-label",className:(0,d.cn)("text-muted-foreground px-2 py-1.5 text-xs",e),...t,"data-sentry-element":"SelectPrimitive.Label","data-sentry-component":"SelectLabel","data-sentry-source-file":"select.tsx"})}function b({className:e,children:t,...n}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,"data-sentry-element":"SelectPrimitive.Item","data-sentry-component":"SelectItem","data-sentry-source-file":"select.tsx",children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{"data-sentry-element":"SelectPrimitive.ItemIndicator","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(o.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"select.tsx"})})}),(0,a.jsx)(r.p4,{"data-sentry-element":"SelectPrimitive.ItemText","data-sentry-source-file":"select.tsx",children:t})]})}function x({className:e,...t}){return(0,a.jsx)(r.wv,{"data-slot":"select-separator",className:(0,d.cn)("bg-border pointer-events-none -mx-1 my-1 h-px",e),...t,"data-sentry-element":"SelectPrimitive.Separator","data-sentry-component":"SelectSeparator","data-sentry-source-file":"select.tsx"})}function h({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollUpButton","data-sentry-component":"SelectScrollUpButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(i.A,{className:"size-4","data-sentry-element":"ChevronUpIcon","data-sentry-source-file":"select.tsx"})})}function y({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,"data-sentry-element":"SelectPrimitive.ScrollDownButton","data-sentry-component":"SelectScrollDownButton","data-sentry-source-file":"select.tsx",children:(0,a.jsx)(s.A,{className:"size-4","data-sentry-element":"ChevronDownIcon","data-sentry-source-file":"select.tsx"})})}},30848:(e,t,n)=>{"use strict";n.d(t,{ThemeSelector:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call ThemeSelector() from the server but ThemeSelector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\theme-selector.tsx","ThemeSelector")},31232:(e,t,n)=>{"use strict";function a(e){switch(e){case"admin":return{canCreatePatients:!0,canEditPatients:!0,canDeletePatients:!0,canViewMedicalNotes:!0,canEditMedicalNotes:!0,canCreateAppointments:!0,canEditAllAppointments:!0,canEditOwnAppointments:!0,canDeleteAppointments:!0,canCreateTreatments:!0,canEditTreatments:!0,canDeleteTreatments:!0,canManageUsers:!0,canViewAnalytics:!0,canViewFinancialData:!0,canCreateBills:!0,canEditBills:!0,canDeleteBills:!0,canViewAllBills:!0,canViewOwnBills:!0,canProcessPayments:!0,canViewPayments:!0,canGenerateReceipts:!0,canHandleRefunds:!0,canGenerateReports:!0,canViewDetailedFinancials:!0,canManagePaymentMethods:!0,canApplyDiscounts:!0,canApproveRefunds:!0,canViewSensitiveFinancials:!0,canExportFinancialData:!0,canBulkUpdateBills:!0,canOverridePaymentLimits:!0,canAccessAuditLogs:!0,canManageDeposits:!0,canProcessDepositRefunds:!0,canViewPatientFinancialHistory:!0,canModifyBillDueDates:!0,canWaiveFees:!0,canAccessAdvancedReports:!0,canManageBillingSettings:!0,canCreateDeposits:!0,canEditDeposits:!0,canDeleteDeposits:!0,canApplyDeposits:!0,canRefundDeposits:!0,canViewDepositHistory:!0,canViewDailyReports:!0,canViewMonthlyReports:!0,canViewYearlyReports:!0,canViewOutstandingReports:!0,canViewPaymentMethodReports:!0,canViewTreatmentRevenueReports:!0,canExportReports:!0,canScheduleReports:!0};case"doctor":return{canCreatePatients:!1,canEditPatients:!1,canDeletePatients:!1,canViewMedicalNotes:!0,canEditMedicalNotes:!0,canCreateAppointments:!1,canEditAllAppointments:!1,canEditOwnAppointments:!0,canDeleteAppointments:!1,canCreateTreatments:!1,canEditTreatments:!1,canDeleteTreatments:!1,canManageUsers:!1,canViewAnalytics:!1,canViewFinancialData:!1,canCreateBills:!1,canEditBills:!1,canDeleteBills:!1,canViewAllBills:!1,canViewOwnBills:!0,canProcessPayments:!1,canViewPayments:!0,canGenerateReceipts:!1,canHandleRefunds:!1,canGenerateReports:!1,canViewDetailedFinancials:!1,canManagePaymentMethods:!1,canApplyDiscounts:!1,canApproveRefunds:!1,canViewSensitiveFinancials:!1,canExportFinancialData:!1,canBulkUpdateBills:!1,canOverridePaymentLimits:!1,canAccessAuditLogs:!1,canManageDeposits:!1,canProcessDepositRefunds:!1,canViewPatientFinancialHistory:!0,canModifyBillDueDates:!1,canWaiveFees:!1,canAccessAdvancedReports:!1,canManageBillingSettings:!1,canCreateDeposits:!1,canEditDeposits:!1,canDeleteDeposits:!1,canApplyDeposits:!1,canRefundDeposits:!1,canViewDepositHistory:!1,canViewDailyReports:!1,canViewMonthlyReports:!1,canViewYearlyReports:!1,canViewOutstandingReports:!1,canViewPaymentMethodReports:!1,canViewTreatmentRevenueReports:!1,canExportReports:!1,canScheduleReports:!1};case"front-desk":return{canCreatePatients:!0,canEditPatients:!0,canDeletePatients:!0,canViewMedicalNotes:!1,canEditMedicalNotes:!1,canCreateAppointments:!0,canEditAllAppointments:!0,canEditOwnAppointments:!0,canDeleteAppointments:!0,canCreateTreatments:!1,canEditTreatments:!1,canDeleteTreatments:!1,canManageUsers:!1,canViewAnalytics:!1,canViewFinancialData:!1,canCreateBills:!0,canEditBills:!0,canDeleteBills:!1,canViewAllBills:!0,canViewOwnBills:!0,canProcessPayments:!0,canViewPayments:!0,canGenerateReceipts:!0,canHandleRefunds:!1,canGenerateReports:!1,canViewDetailedFinancials:!1,canManagePaymentMethods:!0,canApplyDiscounts:!0,canApproveRefunds:!1,canViewSensitiveFinancials:!1,canExportFinancialData:!1,canBulkUpdateBills:!0,canOverridePaymentLimits:!1,canAccessAuditLogs:!1,canManageDeposits:!0,canProcessDepositRefunds:!1,canViewPatientFinancialHistory:!0,canModifyBillDueDates:!0,canWaiveFees:!1,canAccessAdvancedReports:!1,canManageBillingSettings:!1,canCreateDeposits:!0,canEditDeposits:!0,canDeleteDeposits:!1,canApplyDeposits:!0,canRefundDeposits:!1,canViewDepositHistory:!0,canViewDailyReports:!0,canViewMonthlyReports:!0,canViewYearlyReports:!1,canViewOutstandingReports:!0,canViewPaymentMethodReports:!0,canViewTreatmentRevenueReports:!1,canExportReports:!1,canScheduleReports:!1};default:return{canCreatePatients:!1,canEditPatients:!1,canDeletePatients:!1,canViewMedicalNotes:!1,canEditMedicalNotes:!1,canCreateAppointments:!1,canEditAllAppointments:!1,canEditOwnAppointments:!1,canDeleteAppointments:!1,canCreateTreatments:!1,canEditTreatments:!1,canDeleteTreatments:!1,canManageUsers:!1,canViewAnalytics:!1,canViewFinancialData:!1,canCreateBills:!1,canEditBills:!1,canDeleteBills:!1,canViewAllBills:!1,canViewOwnBills:!1,canProcessPayments:!1,canViewPayments:!1,canGenerateReceipts:!1,canHandleRefunds:!1,canGenerateReports:!1,canViewDetailedFinancials:!1,canManagePaymentMethods:!1,canApplyDiscounts:!1,canApproveRefunds:!1,canViewSensitiveFinancials:!1,canExportFinancialData:!1,canBulkUpdateBills:!1,canOverridePaymentLimits:!1,canAccessAuditLogs:!1,canManageDeposits:!1,canProcessDepositRefunds:!1,canViewPatientFinancialHistory:!1,canModifyBillDueDates:!1,canWaiveFees:!1,canAccessAdvancedReports:!1,canManageBillingSettings:!1,canCreateDeposits:!1,canEditDeposits:!1,canDeleteDeposits:!1,canApplyDeposits:!1,canRefundDeposits:!1,canViewDepositHistory:!1,canViewDailyReports:!1,canViewMonthlyReports:!1,canViewYearlyReports:!1,canViewOutstandingReports:!1,canViewPaymentMethodReports:!1,canViewTreatmentRevenueReports:!1,canExportReports:!1,canScheduleReports:!1}}}function r(e,t){return!!e&&!!e.role&&a(e.role)[t]}function s(e,t){return!!e&&!!e.role&&(Array.isArray(t)?t:[t]).includes(e.role)}function o(e){switch(e){case"admin":return"管理员";case"front-desk":return"前台";case"doctor":return"医生";default:return"未知角色"}}function i(e){switch(e){case"admin":return"bg-red-100 text-red-800";case"doctor":return"bg-blue-100 text-blue-800";case"front-desk":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}}n.d(t,{Pj:()=>i,_m:()=>r,bn:()=>a,cb:()=>o,hf:()=>s})},31870:(e,t,n)=>{"use strict";n.d(t,{C:()=>a});let a=[{title:"仪表板",url:"/dashboard",icon:"dashboard",isActive:!1,shortcut:["d","d"],items:[],roles:["admin","front-desk","doctor"]},{title:"预约管理",url:"/dashboard/appointments",icon:"calendar",shortcut:["a","a"],isActive:!1,items:[],roles:["admin","front-desk","doctor"]},{title:"患者管理",url:"/dashboard/patients",icon:"users",shortcut:["p","p"],isActive:!1,items:[],roles:["admin","front-desk","doctor"]},{title:"治疗项目",url:"/dashboard/treatments",icon:"medical",shortcut:["t","t"],isActive:!1,items:[],roles:["admin"]},{title:"账单管理",url:"/dashboard/billing",icon:"billing",shortcut:["b","b"],isActive:!1,items:[],roles:["admin","front-desk"]},{title:"系统管理",url:"/dashboard/admin",icon:"userPen",shortcut:["u","u"],isActive:!1,items:[],roles:["admin"]},{title:"账户",url:"#",icon:"billing",isActive:!1,items:[{title:"个人资料",url:"/dashboard/profile",icon:"userPen",shortcut:["m","m"]},{title:"登录",shortcut:["l","l"],url:"/",icon:"login"}]}]},32083:(e,t,n)=>{"use strict";n.d(t,{UserNav:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call UserNav() from the server but UserNav is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\user-nav.tsx","UserNav")},38726:(e,t,n)=>{"use strict";n.d(t,{default:()=>r});var a=n(91611);(0,a.registerClientReference)(function(){throw Error("Attempted to call company() from the server but company is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\app-sidebar.tsx","company");let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\app-sidebar.tsx","default")},45365:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Sidebar:()=>P,SidebarContent:()=>V,SidebarFooter:()=>z,SidebarGroup:()=>L,SidebarGroupAction:()=>_,SidebarGroupContent:()=>F,SidebarGroupLabel:()=>G,SidebarHeader:()=>E,SidebarInput:()=>T,SidebarInset:()=>B,SidebarMenu:()=>O,SidebarMenuAction:()=>q,SidebarMenuBadge:()=>J,SidebarMenuButton:()=>$,SidebarMenuItem:()=>H,SidebarMenuSkeleton:()=>Y,SidebarMenuSub:()=>W,SidebarMenuSubButton:()=>Q,SidebarMenuSubItem:()=>X,SidebarProvider:()=>M,SidebarRail:()=>I,SidebarSeparator:()=>U,SidebarTrigger:()=>R,useSidebar:()=>N});var a=n(24443),r=n(60222),s=n(16586),o=n(29693),i=n(748),d=n(72595),l=n(33284),c=n(19342),u=n(75895),m=n(99873),p=n(20422);function f({...e}){return(0,a.jsx)(m.bL,{"data-slot":"sheet",...e,"data-sentry-element":"SheetPrimitive.Root","data-sentry-component":"Sheet","data-sentry-source-file":"sheet.tsx"})}function b({...e}){return(0,a.jsx)(m.ZL,{"data-slot":"sheet-portal",...e,"data-sentry-element":"SheetPrimitive.Portal","data-sentry-component":"SheetPortal","data-sentry-source-file":"sheet.tsx"})}function x({className:e,...t}){return(0,a.jsx)(m.hJ,{"data-slot":"sheet-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"SheetPrimitive.Overlay","data-sentry-component":"SheetOverlay","data-sentry-source-file":"sheet.tsx"})}function h({className:e,children:t,side:n="right",...r}){return(0,a.jsxs)(b,{"data-sentry-element":"SheetPortal","data-sentry-component":"SheetContent","data-sentry-source-file":"sheet.tsx",children:[(0,a.jsx)(x,{"data-sentry-element":"SheetOverlay","data-sentry-source-file":"sheet.tsx"}),(0,a.jsxs)(m.UC,{"data-slot":"sheet-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,"data-sentry-element":"SheetPrimitive.Content","data-sentry-source-file":"sheet.tsx",children:[t,(0,a.jsxs)(m.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none","data-sentry-element":"SheetPrimitive.Close","data-sentry-source-file":"sheet.tsx",children:[(0,a.jsx)(p.A,{className:"size-4","data-sentry-element":"XIcon","data-sentry-source-file":"sheet.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function y({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,d.cn)("flex flex-col gap-1.5 p-4",e),...t,"data-sentry-component":"SheetHeader","data-sentry-source-file":"sheet.tsx"})}function g({className:e,...t}){return(0,a.jsx)(m.hE,{"data-slot":"sheet-title",className:(0,d.cn)("text-foreground font-semibold",e),...t,"data-sentry-element":"SheetPrimitive.Title","data-sentry-component":"SheetTitle","data-sentry-source-file":"sheet.tsx"})}function v({className:e,...t}){return(0,a.jsx)(m.VY,{"data-slot":"sheet-description",className:(0,d.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"SheetPrimitive.Description","data-sentry-component":"SheetDescription","data-sentry-source-file":"sheet.tsx"})}function j({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,d.cn)("bg-accent animate-pulse rounded-md",e),...t,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}var w=n(73656);function S({delayDuration:e=0,...t}){return(0,a.jsx)(w.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t,"data-sentry-element":"TooltipPrimitive.Provider","data-sentry-component":"TooltipProvider","data-sentry-source-file":"tooltip.tsx"})}function C({...e}){return(0,a.jsx)(S,{"data-sentry-element":"TooltipProvider","data-sentry-component":"Tooltip","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsx)(w.bL,{"data-slot":"tooltip",...e,"data-sentry-element":"TooltipPrimitive.Root","data-sentry-source-file":"tooltip.tsx"})})}function A({...e}){return(0,a.jsx)(w.l9,{"data-slot":"tooltip-trigger",...e,"data-sentry-element":"TooltipPrimitive.Trigger","data-sentry-component":"TooltipTrigger","data-sentry-source-file":"tooltip.tsx"})}function k({className:e,sideOffset:t=0,children:n,...r}){return(0,a.jsx)(w.ZL,{"data-sentry-element":"TooltipPrimitive.Portal","data-sentry-component":"TooltipContent","data-sentry-source-file":"tooltip.tsx",children:(0,a.jsxs)(w.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,d.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...r,"data-sentry-element":"TooltipPrimitive.Content","data-sentry-source-file":"tooltip.tsx",children:[n,(0,a.jsx)(w.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]","data-sentry-element":"TooltipPrimitive.Arrow","data-sentry-source-file":"tooltip.tsx"})]})})}let D=r.createContext(null);function N(){let e=r.useContext(D);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function M({defaultOpen:e=!0,open:t,onOpenChange:n,className:s,style:o,children:i,...l}){let c=function(){let[e,t]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),n=()=>{t(window.innerWidth<768)};return e.addEventListener("change",n),t(window.innerWidth<768),()=>e.removeEventListener("change",n)},[]),!!e}(),[u,m]=r.useState(!1),[p,f]=r.useState(e),b=t??p,x=r.useCallback(e=>{let t="function"==typeof e?e(b):e;n?n(t):f(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[n,b]),h=r.useCallback(()=>c?m(e=>!e):x(e=>!e),[c,x,m]);r.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),h())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[h]);let y=b?"expanded":"collapsed",g=r.useMemo(()=>({state:y,open:b,setOpen:x,isMobile:c,openMobile:u,setOpenMobile:m,toggleSidebar:h}),[y,b,x,c,u,m,h]);return(0,a.jsx)(D.Provider,{value:g,"data-sentry-element":"SidebarContext.Provider","data-sentry-component":"SidebarProvider","data-sentry-source-file":"sidebar.tsx",children:(0,a.jsx)(S,{delayDuration:0,"data-sentry-element":"TooltipProvider","data-sentry-source-file":"sidebar.tsx",children:(0,a.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...o},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...l,children:i})})})}function P({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:s,...o}){let{isMobile:i,state:l,openMobile:c,setOpenMobile:u}=N();return"none"===n?(0,a.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...o,children:s}):i?(0,a.jsx)(f,{open:c,onOpenChange:u,...o,children:(0,a.jsxs)(h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,a.jsxs)(y,{className:"sr-only",children:[(0,a.jsx)(g,{children:"Sidebar"}),(0,a.jsx)(v,{children:"Displays the mobile sidebar."})]}),(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,a.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar","data-sentry-component":"Sidebar","data-sentry-source-file":"sidebar.tsx",children:[(0,a.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,a.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...o,children:(0,a.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function R({className:e,onClick:t,...n}){let{toggleSidebar:r}=N();return(0,a.jsxs)(l.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",e),onClick:e=>{t?.(e),r()},...n,"data-sentry-element":"Button","data-sentry-component":"SidebarTrigger","data-sentry-source-file":"sidebar.tsx",children:[(0,a.jsx)(i.A,{"data-sentry-element":"PanelLeftIcon","data-sentry-source-file":"sidebar.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function I({className:e,...t}){let{toggleSidebar:n}=N();return(0,a.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:(0,d.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t,"data-sentry-component":"SidebarRail","data-sentry-source-file":"sidebar.tsx"})}function B({className:e,...t}){return(0,a.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...t,"data-sentry-component":"SidebarInset","data-sentry-source-file":"sidebar.tsx"})}function T({className:e,...t}){return(0,a.jsx)(c.p,{"data-slot":"sidebar-input","data-sidebar":"input",className:(0,d.cn)("bg-background h-8 w-full shadow-none",e),...t,"data-sentry-element":"Input","data-sentry-component":"SidebarInput","data-sentry-source-file":"sidebar.tsx"})}function E({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t,"data-sentry-component":"SidebarHeader","data-sentry-source-file":"sidebar.tsx"})}function z({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",e),...t,"data-sentry-component":"SidebarFooter","data-sentry-source-file":"sidebar.tsx"})}function U({className:e,...t}){return(0,a.jsx)(u.Separator,{"data-slot":"sidebar-separator","data-sidebar":"separator",className:(0,d.cn)("bg-sidebar-border mx-2 w-auto",e),...t,"data-sentry-element":"Separator","data-sentry-component":"SidebarSeparator","data-sentry-source-file":"sidebar.tsx"})}function V({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t,"data-sentry-component":"SidebarContent","data-sentry-source-file":"sidebar.tsx"})}function L({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t,"data-sentry-component":"SidebarGroup","data-sentry-source-file":"sidebar.tsx"})}function G({className:e,asChild:t=!1,...n}){let r=t?s.DX:"div";return(0,a.jsx)(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n,"data-sentry-element":"Comp","data-sentry-component":"SidebarGroupLabel","data-sentry-source-file":"sidebar.tsx"})}function _({className:e,asChild:t=!1,...n}){let r=t?s.DX:"button";return(0,a.jsx)(r,{"data-slot":"sidebar-group-action","data-sidebar":"group-action",className:(0,d.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","group-data-[collapsible=icon]:hidden",e),...n,"data-sentry-element":"Comp","data-sentry-component":"SidebarGroupAction","data-sentry-source-file":"sidebar.tsx"})}function F({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,d.cn)("w-full text-sm",e),...t,"data-sentry-component":"SidebarGroupContent","data-sentry-source-file":"sidebar.tsx"})}function O({className:e,...t}){return(0,a.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",e),...t,"data-sentry-component":"SidebarMenu","data-sentry-source-file":"sidebar.tsx"})}function H({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",e),...t,"data-sentry-component":"SidebarMenuItem","data-sentry-source-file":"sidebar.tsx"})}let K=(0,o.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function $({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:i,...l}){let c=e?s.DX:"button",{isMobile:u,state:m}=N(),p=(0,a.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:(0,d.cn)(K({variant:n,size:r}),i),...l});return o?("string"==typeof o&&(o={children:o}),(0,a.jsxs)(C,{"data-sentry-element":"Tooltip","data-sentry-component":"SidebarMenuButton","data-sentry-source-file":"sidebar.tsx",children:[(0,a.jsx)(A,{asChild:!0,"data-sentry-element":"TooltipTrigger","data-sentry-source-file":"sidebar.tsx",children:p}),(0,a.jsx)(k,{side:"right",align:"center",hidden:"collapsed"!==m||u,...o,"data-sentry-element":"TooltipContent","data-sentry-source-file":"sidebar.tsx"})]})):p}function q({className:e,asChild:t=!1,showOnHover:n=!1,...r}){let o=t?s.DX:"button";return(0,a.jsx)(o,{"data-slot":"sidebar-menu-action","data-sidebar":"menu-action",className:(0,d.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 md:after:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0",e),...r,"data-sentry-element":"Comp","data-sentry-component":"SidebarMenuAction","data-sentry-source-file":"sidebar.tsx"})}function J({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sidebar-menu-badge","data-sidebar":"menu-badge",className:(0,d.cn)("text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t,"data-sentry-component":"SidebarMenuBadge","data-sentry-source-file":"sidebar.tsx"})}function Y({className:e,showIcon:t=!1,...n}){let s=r.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,a.jsxs)("div",{"data-slot":"sidebar-menu-skeleton","data-sidebar":"menu-skeleton",className:(0,d.cn)("flex h-8 items-center gap-2 rounded-md px-2",e),...n,"data-sentry-component":"SidebarMenuSkeleton","data-sentry-source-file":"sidebar.tsx",children:[t&&(0,a.jsx)(j,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,a.jsx)(j,{className:"h-4 max-w-(--skeleton-width) flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":s},"data-sentry-element":"Skeleton","data-sentry-source-file":"sidebar.tsx"})]})}function W({className:e,...t}){return(0,a.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,d.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t,"data-sentry-component":"SidebarMenuSub","data-sentry-source-file":"sidebar.tsx"})}function X({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,d.cn)("group/menu-sub-item relative",e),...t,"data-sentry-component":"SidebarMenuSubItem","data-sentry-source-file":"sidebar.tsx"})}function Q({asChild:e=!1,size:t="md",isActive:n=!1,className:r,...o}){let i=e?s.DX:"a";return(0,a.jsx)(i,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:(0,d.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===t&&"text-xs","md"===t&&"text-sm","group-data-[collapsible=icon]:hidden",r),...o,"data-sentry-element":"Comp","data-sentry-component":"SidebarMenuSubButton","data-sentry-source-file":"sidebar.tsx"})}},50006:(e,t,n)=>{Promise.resolve().then(n.bind(n,97392)),Promise.resolve().then(n.bind(n,50859)),Promise.resolve().then(n.bind(n,56040)),Promise.resolve().then(n.bind(n,92454)),Promise.resolve().then(n.bind(n,50981)),Promise.resolve().then(n.bind(n,14866)),Promise.resolve().then(n.bind(n,58326)),Promise.resolve().then(n.bind(n,75895)),Promise.resolve().then(n.bind(n,45365)),Promise.resolve().then(n.bind(n,74482))},50859:(e,t,n)=>{"use strict";n.d(t,{default:()=>m});var a=n(24443),r=n(31870),s=n(32259),o=n(34769),i=n(60222);let d=i.forwardRef(({action:e,active:t,currentRootActionId:n},r)=>{let s=i.useMemo(()=>{if(!n)return e.ancestors;let t=e.ancestors.findIndex(e=>e.id===n);return e.ancestors.slice(t+1)},[e.ancestors,n]);return(0,a.jsxs)("div",{ref:r,className:"relative z-10 flex cursor-pointer items-center justify-between px-4 py-3",children:[t&&(0,a.jsx)("div",{id:"kbar-result-item",className:"border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4"}),(0,a.jsxs)("div",{className:"relative z-10 flex items-center gap-2",children:[e.icon&&e.icon,(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{children:[s.length>0&&s.map(e=>(0,a.jsxs)(i.Fragment,{children:[(0,a.jsx)("span",{className:"text-muted-foreground mr-2",children:e.name}),(0,a.jsx)("span",{className:"mr-2",children:"›"})]},e.id)),(0,a.jsx)("span",{children:e.name})]}),e.subtitle&&(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:e.subtitle})]})]}),e.shortcut?.length?(0,a.jsx)("div",{className:"relative z-10 grid grid-flow-col gap-1",children:e.shortcut.map((e,t)=>(0,a.jsx)("kbd",{className:"bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium",children:e},e+t))}):null]})});function l(){let{results:e,rootActionId:t}=(0,s.useMatches)();return(0,a.jsx)(s.KBarResults,{items:e,onRender:({item:e,active:n})=>"string"==typeof e?(0,a.jsx)("div",{className:"text-primary-foreground px-4 py-2 text-sm uppercase opacity-50",children:e}):(0,a.jsx)(d,{action:e,active:n,currentRootActionId:t??""}),"data-sentry-element":"KBarResults","data-sentry-component":"RenderResults","data-sentry-source-file":"render-result.tsx"})}d.displayName="KBarResultItem";var c=n(55294);let u=()=>{let{theme:e,setTheme:t}=(0,c.D)();(0,s.useRegisterActions)([{id:"toggleTheme",name:"Toggle Theme",shortcut:["t","t"],section:"Theme",perform:()=>{t("light"===e?"dark":"light")}},{id:"setLightTheme",name:"Set Light Theme",section:"Theme",perform:()=>t("light")},{id:"setDarkTheme",name:"Set Dark Theme",section:"Theme",perform:()=>t("dark")}],[e])};function m({children:e}){let t=(0,o.useRouter)(),n=(0,i.useMemo)(()=>{let e=e=>{t.push(e)};return r.C.flatMap(t=>{let n="#"!==t.url?{id:`${t.title.toLowerCase()}Action`,name:t.title,shortcut:t.shortcut,keywords:t.title.toLowerCase(),section:"Navigation",subtitle:`Go to ${t.title}`,perform:()=>e(t.url)}:null,a=t.items?.map(n=>({id:`${n.title.toLowerCase()}Action`,name:n.title,shortcut:n.shortcut,keywords:n.title.toLowerCase(),section:t.title,subtitle:`Go to ${n.title}`,perform:()=>e(n.url)}))??[];return n?[n,...a]:a})},[t]);return(0,a.jsx)(s.KBarProvider,{actions:n,"data-sentry-element":"KBarProvider","data-sentry-component":"KBar","data-sentry-source-file":"index.tsx",children:(0,a.jsx)(p,{"data-sentry-element":"KBarComponent","data-sentry-source-file":"index.tsx",children:e})})}let p=({children:e})=>(u(),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.KBarPortal,{"data-sentry-element":"KBarPortal","data-sentry-source-file":"index.tsx",children:(0,a.jsx)(s.KBarPositioner,{className:"bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm","data-sentry-element":"KBarPositioner","data-sentry-source-file":"index.tsx",children:(0,a.jsxs)(s.KBarAnimator,{className:"bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg","data-sentry-element":"KBarAnimator","data-sentry-source-file":"index.tsx",children:[(0,a.jsx)("div",{className:"bg-card border-border sticky top-0 z-10 border-b",children:(0,a.jsx)(s.KBarSearch,{className:"bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden","data-sentry-element":"KBarSearch","data-sentry-source-file":"index.tsx"})}),(0,a.jsx)("div",{className:"max-h-[400px]",children:(0,a.jsx)(l,{"data-sentry-element":"RenderResults","data-sentry-source-file":"index.tsx"})})]})})}),e]}))},50981:(e,t,n)=>{"use strict";n.d(t,{UserNav:()=>l});var a=n(24443),r=n(33284),s=n(11035),o=n(17440),i=n(22371),d=n(34769);function l(){let{user:e}=(0,i.Jd)(),t=(0,d.useRouter)();if(e)return(0,a.jsxs)(s.rI,{children:[(0,a.jsx)(s.ty,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsx)(o.M,{user:e})})}),(0,a.jsxs)(s.SQ,{className:"w-56",align:"end",sideOffset:10,forceMount:!0,children:[(0,a.jsx)(s.lp,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm leading-none font-medium",children:e.fullName}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs leading-none",children:e.emailAddresses[0].emailAddress})]})}),(0,a.jsx)(s.mB,{}),(0,a.jsxs)(s.I,{children:[(0,a.jsx)(s._2,{onClick:()=>t.push("/dashboard/profile"),children:"Profile"}),(0,a.jsx)(s._2,{children:"Billing"}),(0,a.jsx)(s._2,{children:"Settings"}),(0,a.jsx)(s._2,{children:"New Team"})]}),(0,a.jsx)(s.mB,{}),(0,a.jsx)(s._2,{children:(0,a.jsx)(i.ct,{redirectUrl:"/auth/sign-in"})})]})]})}},56040:(e,t,n)=>{"use strict";n.d(t,{default:()=>R});var a=n(24443),r=n(14178);function s({...e}){return(0,a.jsx)(r.bL,{"data-slot":"collapsible",...e,"data-sentry-element":"CollapsiblePrimitive.Root","data-sentry-component":"Collapsible","data-sentry-source-file":"collapsible.tsx"})}function o({...e}){return(0,a.jsx)(r.R6,{"data-slot":"collapsible-trigger",...e,"data-sentry-element":"CollapsiblePrimitive.CollapsibleTrigger","data-sentry-component":"CollapsibleTrigger","data-sentry-source-file":"collapsible.tsx"})}function i({...e}){return(0,a.jsx)(r.Ke,{"data-slot":"collapsible-content",...e,"data-sentry-element":"CollapsiblePrimitive.CollapsibleContent","data-sentry-component":"CollapsibleContent","data-sentry-source-file":"collapsible.tsx"})}var d=n(11035),l=n(45365),c=n(17440),u=n(31870),m=n(60222),p=n(74482),f=n(22371),b=n(41245),x=n(15431),h=n(97342),y=n(1675),g=n(53503),v=n(33005),j=n(51524),w=n(38250),S=n.n(w),C=n(34769),A=n(12099),k=n(89915),D=n(88629),N=n(84338);function M({tenants:e,defaultTenant:t,onTenantSwitch:n}){let[r,s]=m.useState(t||(e.length>0?e[0]:void 0)),o=e=>{s(e),n&&n(e.id)};return r?(0,a.jsx)(l.SidebarMenu,{"data-sentry-element":"SidebarMenu","data-sentry-component":"OrgSwitcher","data-sentry-source-file":"org-switcher.tsx",children:(0,a.jsx)(l.SidebarMenuItem,{"data-sentry-element":"SidebarMenuItem","data-sentry-source-file":"org-switcher.tsx",children:(0,a.jsxs)(d.rI,{"data-sentry-element":"DropdownMenu","data-sentry-source-file":"org-switcher.tsx",children:[(0,a.jsx)(d.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"org-switcher.tsx",children:(0,a.jsxs)(l.SidebarMenuButton,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground","data-sentry-element":"SidebarMenuButton","data-sentry-source-file":"org-switcher.tsx",children:[(0,a.jsx)("div",{className:"bg-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,a.jsx)(k.A,{className:"size-4","data-sentry-element":"GalleryVerticalEnd","data-sentry-source-file":"org-switcher.tsx"})}),(0,a.jsxs)("div",{className:"flex flex-col gap-0.5 leading-none",children:[(0,a.jsx)("span",{className:"font-semibold",children:"Next Starter"}),(0,a.jsx)("span",{className:"",children:r.name})]}),(0,a.jsx)(D.A,{className:"ml-auto","data-sentry-element":"ChevronsUpDown","data-sentry-source-file":"org-switcher.tsx"})]})}),(0,a.jsx)(d.SQ,{className:"w-[--radix-dropdown-menu-trigger-width]",align:"start","data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"org-switcher.tsx",children:e.map(e=>(0,a.jsxs)(d._2,{onSelect:()=>o(e),children:[e.name," ",e.id===r.id&&(0,a.jsx)(N.A,{className:"ml-auto"})]},e.id))})]})})}):null}b.A;let P=[{id:"1",name:"Acme Inc"},{id:"2",name:"Beta Corp"},{id:"3",name:"Gamma Ltd"}];function R(){let e=(0,C.usePathname)(),{isOpen:t}=function(){let[e,t]=(0,m.useState)(!1);return{isOpen:e}}(),{user:n}=(0,f.Jd)(),{user:r}=(0,p.It)(),b=(0,C.useRouter)(),w=P[0],k=m.useMemo(()=>{if(!r?.role)return u.C;let e=r.role;return u.C.filter(t=>!t.roles||0===t.roles.length||t.roles.includes(e))},[r?.role]);return m.useEffect(()=>{},[t]),(0,a.jsxs)(l.Sidebar,{collapsible:"icon","data-sentry-element":"Sidebar","data-sentry-component":"AppSidebar","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(l.SidebarHeader,{"data-sentry-element":"SidebarHeader","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)(M,{tenants:P,defaultTenant:w,onTenantSwitch:e=>{},"data-sentry-element":"OrgSwitcher","data-sentry-source-file":"app-sidebar.tsx"})}),(0,a.jsx)(l.SidebarContent,{className:"overflow-x-hidden","data-sentry-element":"SidebarContent","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsxs)(l.SidebarGroup,{"data-sentry-element":"SidebarGroup","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(l.SidebarGroupLabel,{"data-sentry-element":"SidebarGroupLabel","data-sentry-source-file":"app-sidebar.tsx",children:"概览"}),(0,a.jsx)(l.SidebarMenu,{"data-sentry-element":"SidebarMenu","data-sentry-source-file":"app-sidebar.tsx",children:k.map(t=>{let n=t.icon?A.FI[t.icon]:A.FI.logo;return t?.items&&t?.items?.length>0?(0,a.jsx)(s,{asChild:!0,defaultOpen:t.isActive,className:"group/collapsible",children:(0,a.jsxs)(l.SidebarMenuItem,{children:[(0,a.jsx)(o,{asChild:!0,children:(0,a.jsxs)(l.SidebarMenuButton,{tooltip:t.title,isActive:e===t.url,children:[t.icon&&(0,a.jsx)(n,{}),(0,a.jsx)("span",{children:t.title}),(0,a.jsx)(x.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,a.jsx)(i,{children:(0,a.jsx)(l.SidebarMenuSub,{children:t.items?.map(t=>(0,a.jsx)(l.SidebarMenuSubItem,{children:(0,a.jsx)(l.SidebarMenuSubButton,{asChild:!0,isActive:e===t.url,children:(0,a.jsx)(S(),{href:t.url,children:(0,a.jsx)("span",{children:t.title})})})},t.title))})})]})},t.title):(0,a.jsx)(l.SidebarMenuItem,{children:(0,a.jsx)(l.SidebarMenuButton,{asChild:!0,tooltip:t.title,isActive:e===t.url,children:(0,a.jsxs)(S(),{href:t.url,children:[(0,a.jsx)(n,{}),(0,a.jsx)("span",{children:t.title})]})})},t.title)})})]})}),(0,a.jsx)(l.SidebarFooter,{"data-sentry-element":"SidebarFooter","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)(l.SidebarMenu,{"data-sentry-element":"SidebarMenu","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)(l.SidebarMenuItem,{"data-sentry-element":"SidebarMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsxs)(d.rI,{"data-sentry-element":"DropdownMenu","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(d.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsxs)(l.SidebarMenuButton,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground","data-sentry-element":"SidebarMenuButton","data-sentry-source-file":"app-sidebar.tsx",children:[n&&(0,a.jsx)(c.M,{className:"h-8 w-8 rounded-lg",showInfo:!0,user:n}),(0,a.jsx)(h.A,{className:"ml-auto size-4","data-sentry-element":"IconChevronsDown","data-sentry-source-file":"app-sidebar.tsx"})]})}),(0,a.jsxs)(d.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:"bottom",align:"end",sideOffset:4,"data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(d.lp,{className:"p-0 font-normal","data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"app-sidebar.tsx",children:(0,a.jsx)("div",{className:"px-1 py-1.5",children:n&&(0,a.jsx)(c.M,{className:"h-8 w-8 rounded-lg",showInfo:!0,user:n})})}),(0,a.jsx)(d.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsxs)(d.I,{"data-sentry-element":"DropdownMenuGroup","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsxs)(d._2,{onClick:()=>b.push("/dashboard/profile"),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconUserCircle","data-sentry-source-file":"app-sidebar.tsx"}),"Profile"]}),(0,a.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconCreditCard","data-sentry-source-file":"app-sidebar.tsx"}),"Billing"]}),(0,a.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconBell","data-sentry-source-file":"app-sidebar.tsx"}),"Notifications"]})]}),(0,a.jsx)(d.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsxs)(d._2,{"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"app-sidebar.tsx",children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconLogout","data-sentry-source-file":"app-sidebar.tsx"}),(0,a.jsx)(f.ct,{redirectUrl:"/auth/sign-in","data-sentry-element":"SignOutButton","data-sentry-source-file":"app-sidebar.tsx"})]})]})]})})})}),(0,a.jsx)(l.SidebarRail,{"data-sentry-element":"SidebarRail","data-sentry-source-file":"app-sidebar.tsx"})]})}},56164:(e,t,n)=>{"use strict";let a;n.r(t),n.d(t,{default:()=>M,generateImageMetadata:()=>D,generateMetadata:()=>k,generateViewport:()=>N,metadata:()=>w});var r=n(63033),s=n(78869),o=n(21660),i=n(38726);n(22576);var d=n(94940),l=n(9597),c=n(62001),u=n(67343),m=n(32083),p=n(30848),f=n(72680),b=n(22762),x=n(9286);function h(){return(0,s.jsx)(b.$,{variant:"ghost",asChild:!0,size:"sm",className:"hidden sm:flex","data-sentry-element":"Button","data-sentry-component":"CtaGithub","data-sentry-source-file":"cta-github.tsx",children:(0,s.jsx)("a",{href:"https://github.com/Kiranism/next-shadcn-dashboard-starter",rel:"noopener noreferrer",target:"_blank",className:"dark:text-foreground",children:(0,s.jsx)(x.A,{"data-sentry-element":"IconBrandGithub","data-sentry-source-file":"cta-github.tsx"})})})}function y(){return(0,s.jsxs)("header",{className:"flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12","data-sentry-component":"Header","data-sentry-source-file":"header.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,s.jsx)(d.SidebarTrigger,{className:"-ml-1","data-sentry-element":"SidebarTrigger","data-sentry-source-file":"header.tsx"}),(0,s.jsx)(l.Separator,{orientation:"vertical",className:"mr-2 h-4","data-sentry-element":"Separator","data-sentry-source-file":"header.tsx"}),(0,s.jsx)(c.Breadcrumbs,{"data-sentry-element":"Breadcrumbs","data-sentry-source-file":"header.tsx"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,s.jsx)(h,{"data-sentry-element":"CtaGithub","data-sentry-source-file":"header.tsx"}),(0,s.jsx)("div",{className:"hidden md:flex",children:(0,s.jsx)(u.default,{"data-sentry-element":"SearchInput","data-sentry-source-file":"header.tsx"})}),(0,s.jsx)(m.UserNav,{"data-sentry-element":"UserNav","data-sentry-source-file":"header.tsx"}),(0,s.jsx)(f.ModeToggle,{"data-sentry-element":"ModeToggle","data-sentry-source-file":"header.tsx"}),(0,s.jsx)(p.ThemeSelector,{"data-sentry-element":"ThemeSelector","data-sentry-source-file":"header.tsx"})]})]})}var g=n(92696),v=n(87927),j=n(19761);let w={title:"Next Shadcn Dashboard Starter",description:"Basic dashboard with Next.js and Shadcn"};async function S({children:e}){let t=await (0,v.UL)(),n=t.get("sidebar_state")?.value==="true";return(0,s.jsx)(g.RoleProvider,{"data-sentry-element":"RoleProvider","data-sentry-component":"DashboardLayout","data-sentry-source-file":"layout.tsx",children:(0,s.jsx)(o.default,{"data-sentry-element":"KBar","data-sentry-source-file":"layout.tsx",children:(0,s.jsxs)(d.SidebarProvider,{defaultOpen:n,"data-sentry-element":"SidebarProvider","data-sentry-source-file":"layout.tsx",children:[(0,s.jsx)(i.default,{"data-sentry-element":"AppSidebar","data-sentry-source-file":"layout.tsx"}),(0,s.jsxs)(d.SidebarInset,{"data-sentry-element":"SidebarInset","data-sentry-source-file":"layout.tsx",children:[(0,s.jsx)(y,{"data-sentry-element":"Header","data-sentry-source-file":"layout.tsx"}),e]})]})})})}let C={...r},A="workUnitAsyncStorage"in C?C.workUnitAsyncStorage:"requestAsyncStorage"in C?C.requestAsyncStorage:void 0;a=new Proxy(S,{apply:(e,t,n)=>{let a,r,s;try{let e=A?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,s=e?.headers}catch(e){}return j.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard",componentType:"Layout",sentryTraceHeader:a,baggageHeader:r,headers:s}).apply(t,n)}});let k=void 0,D=void 0,N=void 0,M=a},58326:(e,t,n)=>{"use strict";n.d(t,{ThemeSelector:()=>c});var a=n(24443),r=n(99971),s=n(18984),o=n(23032);let i=[{name:"Default",value:"default"},{name:"Blue",value:"blue"},{name:"Green",value:"green"},{name:"Amber",value:"amber"}],d=[{name:"Default",value:"default-scaled"},{name:"Blue",value:"blue-scaled"}],l=[{name:"Mono",value:"mono-scaled"}];function c(){let{activeTheme:e,setActiveTheme:t}=(0,r.p)();return(0,a.jsxs)("div",{className:"flex items-center gap-2","data-sentry-component":"ThemeSelector","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsx)(s.J,{htmlFor:"theme-selector",className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"theme-selector.tsx",children:"Theme"}),(0,a.jsxs)(o.l6,{value:e,onValueChange:t,"data-sentry-element":"Select","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsxs)(o.bq,{id:"theme-selector",className:"justify-start *:data-[slot=select-value]:w-12","data-sentry-element":"SelectTrigger","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsx)("span",{className:"text-muted-foreground hidden sm:block",children:"Select a theme:"}),(0,a.jsx)("span",{className:"text-muted-foreground block sm:hidden",children:"Theme"}),(0,a.jsx)(o.yv,{placeholder:"Select a theme","data-sentry-element":"SelectValue","data-sentry-source-file":"theme-selector.tsx"})]}),(0,a.jsxs)(o.gC,{align:"end","data-sentry-element":"SelectContent","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsxs)(o.s3,{"data-sentry-element":"SelectGroup","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsx)(o.TR,{"data-sentry-element":"SelectLabel","data-sentry-source-file":"theme-selector.tsx",children:"Default"}),i.map(e=>(0,a.jsx)(o.eb,{value:e.value,children:e.name},e.name))]}),(0,a.jsx)(o.mi,{"data-sentry-element":"SelectSeparator","data-sentry-source-file":"theme-selector.tsx"}),(0,a.jsxs)(o.s3,{"data-sentry-element":"SelectGroup","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsx)(o.TR,{"data-sentry-element":"SelectLabel","data-sentry-source-file":"theme-selector.tsx",children:"Scaled"}),d.map(e=>(0,a.jsx)(o.eb,{value:e.value,children:e.name},e.name))]}),(0,a.jsxs)(o.s3,{"data-sentry-element":"SelectGroup","data-sentry-source-file":"theme-selector.tsx",children:[(0,a.jsx)(o.TR,{"data-sentry-element":"SelectLabel","data-sentry-source-file":"theme-selector.tsx",children:"Monospaced"}),l.map(e=>(0,a.jsx)(o.eb,{value:e.value,children:e.name},e.name))]})]})]})]})}},62001:(e,t,n)=>{"use strict";n.d(t,{Breadcrumbs:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call Breadcrumbs() from the server but Breadcrumbs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\breadcrumbs.tsx","Breadcrumbs")},67343:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\search-input.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\search-input.tsx","default")},72680:(e,t,n)=>{"use strict";n.d(t,{ModeToggle:()=>a});let a=(0,n(91611).registerClientReference)(function(){throw Error("Attempted to call ModeToggle() from the server but ModeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\ThemeToggle\\theme-toggle.tsx","ModeToggle")},74482:(e,t,n)=>{"use strict";n.d(t,{Bk:()=>c,It:()=>l,RoleProvider:()=>d,Y0:()=>u});var a=n(24443),r=n(60222),s=n(22371),o=n(31232);let i=(0,r.createContext)(void 0);function d({children:e}){let{user:t,isLoaded:n}=(0,s.Jd)(),[d,l]=(0,r.useState)(null),[c,u]=(0,r.useState)(!0),[m,p]=(0,r.useState)(null),f=async()=>{if(!t){l(null),u(!1);return}try{u(!0),p(null);let e=await fetch("/api/auth/sync",{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("Failed to sync user data");let n=await e.json(),a={clerkId:t.id,email:t.emailAddresses[0]?.emailAddress||"",firstName:t.firstName||void 0,lastName:t.lastName||void 0,role:n.user?.role||"front-desk",payloadUserId:n.user?.payloadUserId};l(a)}catch(e){console.error("Error fetching user role:",e),p(e instanceof Error?e.message:"Failed to fetch user role"),t&&l({clerkId:t.id,email:t.emailAddresses[0]?.emailAddress||"",firstName:t.firstName||void 0,lastName:t.lastName||void 0,role:"front-desk",payloadUserId:void 0})}finally{u(!1)}},b=d?.role?(0,o.bn)(d.role):null;return(0,a.jsx)(i.Provider,{value:{user:d,loading:c,error:m,permissions:b,hasPermission:e=>(0,o._m)(d,e),hasRole:e=>(0,o.hf)(d,e),refreshUser:f},"data-sentry-element":"RoleContext.Provider","data-sentry-component":"RoleProvider","data-sentry-source-file":"role-context.tsx",children:e})}function l(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useRole must be used within a RoleProvider");return e}function c({permission:e,children:t,fallback:n=null}){return!function(e){let{hasPermission:t}=l();return t(e)}(e)?(0,a.jsx)(a.Fragment,{children:n}):(0,a.jsx)(a.Fragment,{children:t})}function u({roles:e,children:t,fallback:n=null}){return!function(e){let{hasRole:t}=l();return t(e)}(e)?(0,a.jsx)(a.Fragment,{children:n}):(0,a.jsx)(a.Fragment,{children:t})}},75895:(e,t,n)=>{"use strict";n.d(t,{Separator:()=>o});var a=n(24443);n(60222);var r=n(48353),s=n(72595);function o({className:e,orientation:t="horizontal",decorative:n=!0,...o}){return(0,a.jsx)(r.b,{"data-slot":"separator-root",decorative:n,orientation:t,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...o,"data-sentry-element":"SeparatorPrimitive.Root","data-sentry-component":"Separator","data-sentry-source-file":"separator.tsx"})}},86958:(e,t,n)=>{Promise.resolve().then(n.bind(n,62001)),Promise.resolve().then(n.bind(n,21660)),Promise.resolve().then(n.bind(n,38726)),Promise.resolve().then(n.bind(n,72680)),Promise.resolve().then(n.bind(n,32083)),Promise.resolve().then(n.bind(n,67343)),Promise.resolve().then(n.bind(n,30848)),Promise.resolve().then(n.bind(n,9597)),Promise.resolve().then(n.bind(n,94940)),Promise.resolve().then(n.bind(n,92696))},92454:(e,t,n)=>{"use strict";n.d(t,{ModeToggle:()=>d});var a=n(24443),r=n(3431),s=n(55294),o=n(60222),i=n(33284);function d(){let{setTheme:e,resolvedTheme:t}=(0,s.D)(),n=o.useCallback(n=>{let a="dark"===t?"light":"dark",r=document.documentElement;if(!document.startViewTransition)return void e(a);n&&(r.style.setProperty("--x",`${n.clientX}px`),r.style.setProperty("--y",`${n.clientY}px`)),document.startViewTransition(()=>{e(a)})},[t,e]);return(0,a.jsxs)(i.$,{variant:"secondary",size:"icon",className:"group/toggle size-8",onClick:n,"data-sentry-element":"Button","data-sentry-component":"ModeToggle","data-sentry-source-file":"theme-toggle.tsx",children:[(0,a.jsx)(r.A,{"data-sentry-element":"IconBrightness","data-sentry-source-file":"theme-toggle.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}},92696:(e,t,n)=>{"use strict";n.d(t,{RoleProvider:()=>r});var a=n(91611);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call RoleProvider() from the server but RoleProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","RoleProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useRole() from the server but useRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","useRole"),(0,a.registerClientReference)(function(){throw Error("Attempted to call usePermission() from the server but usePermission is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","usePermission"),(0,a.registerClientReference)(function(){throw Error("Attempted to call useHasRole() from the server but useHasRole is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","useHasRole"),(0,a.registerClientReference)(function(){throw Error("Attempted to call PermissionGate() from the server but PermissionGate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","PermissionGate"),(0,a.registerClientReference)(function(){throw Error("Attempted to call RoleGate() from the server but RoleGate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","RoleGate"),(0,a.registerClientReference)(function(){throw Error("Attempted to call withRoleAccess() from the server but withRoleAccess is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","withRoleAccess"),(0,a.registerClientReference)(function(){throw Error("Attempted to call RoleLoading() from the server but RoleLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx","RoleLoading")},94940:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Sidebar:()=>r,SidebarContent:()=>s,SidebarFooter:()=>o,SidebarGroup:()=>i,SidebarGroupAction:()=>d,SidebarGroupContent:()=>l,SidebarGroupLabel:()=>c,SidebarHeader:()=>u,SidebarInput:()=>m,SidebarInset:()=>p,SidebarMenu:()=>f,SidebarMenuAction:()=>b,SidebarMenuBadge:()=>x,SidebarMenuButton:()=>h,SidebarMenuItem:()=>y,SidebarMenuSkeleton:()=>g,SidebarMenuSub:()=>v,SidebarMenuSubButton:()=>j,SidebarMenuSubItem:()=>w,SidebarProvider:()=>S,SidebarRail:()=>C,SidebarSeparator:()=>A,SidebarTrigger:()=>k,useSidebar:()=>D});var a=n(91611);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","Sidebar"),s=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarContent"),o=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarFooter() from the server but SidebarFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarFooter"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarGroup"),d=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarGroupAction"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarGroupContent"),c=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarGroupLabel"),u=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarHeader"),m=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarInput"),p=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarInset"),f=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenu"),b=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuAction"),x=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuBadge"),h=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuButton"),y=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuItem"),g=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuSkeleton"),v=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuSub"),j=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubButton"),w=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarMenuSubItem"),S=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarProvider"),C=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarRail"),A=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarSeparator"),k=(0,a.registerClientReference)(function(){throw Error("Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","SidebarTrigger"),D=(0,a.registerClientReference)(function(){throw Error("Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx","useSidebar")},97392:(e,t,n)=>{"use strict";n.d(t,{Breadcrumbs:()=>h});var a=n(24443),r=n(60222),s=n(16586),o=n(80910),i=n(72595);function d({...e}){return(0,a.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e,"data-sentry-component":"Breadcrumb","data-sentry-source-file":"breadcrumb.tsx"})}function l({className:e,...t}){return(0,a.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,i.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t,"data-sentry-component":"BreadcrumbList","data-sentry-source-file":"breadcrumb.tsx"})}function c({className:e,...t}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,i.cn)("inline-flex items-center gap-1.5",e),...t,"data-sentry-component":"BreadcrumbItem","data-sentry-source-file":"breadcrumb.tsx"})}function u({asChild:e,className:t,...n}){let r=e?s.DX:"a";return(0,a.jsx)(r,{"data-slot":"breadcrumb-link",className:(0,i.cn)("hover:text-foreground transition-colors",t),...n,"data-sentry-element":"Comp","data-sentry-component":"BreadcrumbLink","data-sentry-source-file":"breadcrumb.tsx"})}function m({className:e,...t}){return(0,a.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("text-foreground font-normal",e),...t,"data-sentry-component":"BreadcrumbPage","data-sentry-source-file":"breadcrumb.tsx"})}function p({children:e,className:t,...n}){return(0,a.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",t),...n,"data-sentry-component":"BreadcrumbSeparator","data-sentry-source-file":"breadcrumb.tsx",children:e??(0,a.jsx)(o.A,{})})}var f=n(34769);let b={"/dashboard":[{title:"Dashboard",link:"/dashboard"}],"/dashboard/employee":[{title:"Dashboard",link:"/dashboard"},{title:"Employee",link:"/dashboard/employee"}],"/dashboard/product":[{title:"Dashboard",link:"/dashboard"},{title:"Product",link:"/dashboard/product"}]};var x=n(66125);function h(){let e=function(){let e=(0,f.usePathname)();return(0,r.useMemo)(()=>{if(b[e])return b[e];let t=e.split("/").filter(Boolean);return t.map((e,n)=>{let a=`/${t.slice(0,n+1).join("/")}`;return{title:e.charAt(0).toUpperCase()+e.slice(1),link:a}})},[e])}();return 0===e.length?null:(0,a.jsx)(d,{"data-sentry-element":"Breadcrumb","data-sentry-component":"Breadcrumbs","data-sentry-source-file":"breadcrumbs.tsx",children:(0,a.jsx)(l,{"data-sentry-element":"BreadcrumbList","data-sentry-source-file":"breadcrumbs.tsx",children:e.map((t,n)=>(0,a.jsxs)(r.Fragment,{children:[n!==e.length-1&&(0,a.jsx)(c,{className:"hidden md:block",children:(0,a.jsx)(u,{href:t.link,children:t.title})}),n<e.length-1&&(0,a.jsx)(p,{className:"hidden md:block",children:(0,a.jsx)(x.A,{})}),n===e.length-1&&(0,a.jsx)(m,{children:t.title})]},t.title))})})}}};
//# sourceMappingURL=7494.js.map