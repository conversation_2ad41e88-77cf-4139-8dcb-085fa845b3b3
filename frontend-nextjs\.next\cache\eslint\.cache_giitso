[{"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\appointments\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\auth\\sync\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\treatments\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\treatments\\[id]\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\auth\\sign-in\\[[...sign-in]]\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\auth\\sign-up\\[[...sign-up]]\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\admin\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\appointments\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\kanban\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx": "13", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\error.tsx": "14", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\loading.tsx": "15", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\error.tsx": "17", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\loading.tsx": "18", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\error.tsx": "20", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\loading.tsx": "21", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\error.tsx": "23", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\loading.tsx": "24", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\error.tsx": "26", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\layout.tsx": "27", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\__tests__\\page.test.tsx": "30", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\page.tsx": "31", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\[productId]\\page.tsx": "32", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx": "33", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\treatments\\page.tsx": "34", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx": "35", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx": "36", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx": "37", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\page.tsx": "38", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\active-theme.tsx": "39", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-calendar.tsx": "40", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-filters.tsx": "41", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-form-dialog.tsx": "42", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointments-list.tsx": "43", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\multi-step-appointment-form.tsx": "44", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\__tests__\\appointment-filters.test.tsx": "45", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\__tests__\\appointment-form-dialog.test.tsx": "46", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\__tests__\\appointments-list.test.tsx": "47", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\breadcrumbs.tsx": "48", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\dashboard\\dashboard-metrics.tsx": "49", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\file-uploader.tsx": "50", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\form-card-skeleton.tsx": "51", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\icons.tsx": "52", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\index.tsx": "53", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\render-result.tsx": "54", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\result-item.tsx": "55", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\use-theme-switching.tsx": "56", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\app-sidebar.tsx": "57", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\cta-github.tsx": "58", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\header.tsx": "59", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\page-container.tsx": "60", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\providers.tsx": "61", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\ThemeToggle\\theme-provider.tsx": "62", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\ThemeToggle\\theme-toggle.tsx": "63", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\user-nav.tsx": "64", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\modal\\alert-modal.tsx": "65", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\nav-main.tsx": "66", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\nav-projects.tsx": "67", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\nav-user.tsx": "68", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\org-switcher.tsx": "69", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\patient-form-dialog.tsx": "70", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\__tests__\\patient-form-dialog.test.tsx": "71", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\search-input.tsx": "72", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\theme-selector.tsx": "73", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\treatment-form-dialog.tsx": "74", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\treatments-list.tsx": "75", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\__tests__\\treatment-form-dialog.test.tsx": "76", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\__tests__\\treatments-list.test.tsx": "77", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\accordion.tsx": "78", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\alert-dialog.tsx": "79", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\alert.tsx": "80", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\aspect-ratio.tsx": "81", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\avatar.tsx": "82", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\badge.tsx": "83", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\breadcrumb.tsx": "84", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\button.tsx": "85", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\calendar.tsx": "86", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\card.tsx": "87", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\chart.tsx": "88", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\checkbox.tsx": "89", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\collapsible.tsx": "90", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\command.tsx": "91", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\confirmation-dialog.tsx": "92", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\context-menu.tsx": "93", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\dialog.tsx": "94", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\drawer.tsx": "95", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\dropdown-menu.tsx": "96", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\form.tsx": "97", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\heading.tsx": "98", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\hover-card.tsx": "99", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\input-otp.tsx": "100", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\input.tsx": "101", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\label.tsx": "102", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\menubar.tsx": "103", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\modal.tsx": "104", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\navigation-menu.tsx": "105", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\pagination.tsx": "106", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\popover.tsx": "107", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\progress.tsx": "108", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\radio-group.tsx": "109", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\resizable.tsx": "110", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx": "111", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\select.tsx": "112", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\separator.tsx": "113", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sheet.tsx": "114", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx": "115", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\skeleton.tsx": "116", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\slider.tsx": "117", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sonner.tsx": "118", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\switch.tsx": "119", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-column-header.tsx": "120", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-date-filter.tsx": "121", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-faceted-filter.tsx": "122", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-pagination.tsx": "123", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-skeleton.tsx": "124", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-slider-filter.tsx": "125", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-toolbar.tsx": "126", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-view-options.tsx": "127", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table.tsx": "128", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx": "129", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\tabs.tsx": "130", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\textarea.tsx": "131", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\toggle-group.tsx": "132", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\toggle.tsx": "133", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\tooltip.tsx": "134", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\user-avatar-profile.tsx": "135", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\config\\data-table.ts": "136", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\constants\\data.ts": "137", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\constants\\mock-api.ts": "138", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\github-auth-button.tsx": "139", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\sign-in-view.tsx": "140", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\sign-up-view.tsx": "141", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\user-auth-form.tsx": "142", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\board-column.tsx": "143", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\column-action.tsx": "144", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\kanban-board.tsx": "145", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\kanban-view-page.tsx": "146", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\new-section-dialog.tsx": "147", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\new-task-dialog.tsx": "148", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\task-card.tsx": "149", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\utils\\index.ts": "150", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\utils\\store.ts": "151", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\area-graph-skeleton.tsx": "152", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\area-graph.tsx": "153", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\bar-graph-skeleton.tsx": "154", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\bar-graph.tsx": "155", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\overview.tsx": "156", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\pie-graph-skeleton.tsx": "157", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\pie-graph.tsx": "158", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\recent-sales-skeleton.tsx": "159", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\recent-sales.tsx": "160", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-form.tsx": "161", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-listing.tsx": "162", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\cell-action.tsx": "163", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\columns.tsx": "164", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\index.tsx": "165", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\options.tsx": "166", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-view-page.tsx": "167", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\profile\\components\\profile-create-form.tsx": "168", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\profile\\components\\profile-view-page.tsx": "169", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\profile\\utils\\form-schema.ts": "170", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-breadcrumbs.tsx": "171", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-callback-ref.ts": "172", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-callback-ref.tsx": "173", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-controllable-state.tsx": "174", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-data-table.ts": "175", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-debounce.tsx": "176", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-debounced-callback.ts": "177", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-media-query.ts": "178", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-mobile.tsx": "179", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-multistep-form.tsx": "180", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\instrumentation-client.ts": "181", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\instrumentation.ts": "182", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\api.ts": "183", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\appointment-utils.ts": "184", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\auth-middleware.ts": "185", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\data-table.ts": "186", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\error-utils.ts": "187", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\font.ts": "188", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\format.ts": "189", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\parsers.ts": "190", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\payload-client.ts": "191", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx": "192", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-utils.ts": "193", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\searchparams.ts": "194", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\utils.ts": "195", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\middleware.ts": "196", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\integration\\api-endpoints.test.tsx": "197", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\integration\\clinic-workflows.test.tsx": "198", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\mocks\\data.ts": "199", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\mocks\\handlers.ts": "200", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\mocks\\server.ts": "201", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\setup.ts": "202", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\utils.tsx": "203", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\types\\clinic.ts": "204", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\types\\data-table.ts": "205", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\types\\index.ts": "206", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bill-items\\route.ts": "207", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bill-items\\[id]\\route.ts": "208", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\generate-from-appointment\\route.ts": "209", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\route.ts": "210", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\[id]\\route.ts": "211", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-interactions\\route.ts": "212", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-interactions\\[id]\\route.ts": "213", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-tasks\\route.ts": "214", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-tasks\\[id]\\route.ts": "215", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\interactions\\route.ts": "216", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\tasks\\route.ts": "217", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\timeline\\route.ts": "218", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\payments\\route.ts": "219", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\payments\\[id]\\route.ts": "220", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\billing\\page.tsx": "221", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\[id]\\page.tsx": "222", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-reminder-settings.tsx": "223", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-status-manager.tsx": "224", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\advanced-payment-processor.tsx": "225", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\advanced-receipt-system.tsx": "226", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\appointment-to-bill.tsx": "227", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-dialog.tsx": "228", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-filters.tsx": "229", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-form.tsx": "230", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-status-manager.tsx": "231", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\billing-list.tsx": "232", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\billing-tabs.tsx": "233", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\comprehensive-financial-reports.tsx": "234", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\deposit-application-dialog.tsx": "235", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\deposit-form.tsx": "236", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\enhanced-financial-dashboard.tsx": "237", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\enhanced-form-validation.tsx": "238", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\financial-dashboard.tsx": "239", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\financial-summary.tsx": "240", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\generate-bill-button.tsx": "241", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\optimized-bill-list.tsx": "242", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\optimized-billing-list.tsx": "243", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\payment-dialog.tsx": "244", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\payment-form.tsx": "245", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\performance-dashboard.tsx": "246", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\receipt-dialog.tsx": "247", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\receipt-manager.tsx": "248", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\receipt.tsx": "249", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\security-audit-dashboard.tsx": "250", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\security-monitor.tsx": "251", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\communication-log.tsx": "252", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\crm-dashboard.tsx": "253", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\interaction-form-dialog.tsx": "254", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\interaction-timeline.tsx": "255", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\quick-actions.tsx": "256", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\task-form-dialog.tsx": "257", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\task-manager.tsx": "258", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\patient-detail-view.tsx": "259", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\validation-feedback.tsx": "260", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\api\\billing.ts": "261", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\appointment-notifications.ts": "262", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\appointment-reminders.ts": "263", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-error-handler.ts": "264", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-notifications.ts": "265", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-performance.ts": "266", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-security.ts": "267", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-validation.ts": "268", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\calendar-utils.ts": "269", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\crm-notifications.ts": "270", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\translations.ts": "271", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\validation\\billing-schemas.ts": "272", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\validation\\validation-utils.ts": "273", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\billing-test-utils.tsx": "274", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\appointment-system.test.ts": "275", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-api.test.ts": "276", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-integration.test.ts": "277", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-performance.test.ts": "278", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-security.test.ts": "279", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-validation.test.ts": "280", "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\e2e\\billing-e2e.spec.ts": "281"}, {"size": 1525, "mtime": 1752035018714, "results": "282", "hashOfConfig": "283"}, {"size": 1649, "mtime": 1752034710462, "results": "284", "hashOfConfig": "283"}, {"size": 1286, "mtime": 1752026182075, "results": "285", "hashOfConfig": "283"}, {"size": 1572, "mtime": 1752035043723, "results": "286", "hashOfConfig": "283"}, {"size": 1613, "mtime": 1752034749552, "results": "287", "hashOfConfig": "283"}, {"size": 1509, "mtime": 1752035067971, "results": "288", "hashOfConfig": "283"}, {"size": 1631, "mtime": 1752034984228, "results": "289", "hashOfConfig": "283"}, {"size": 798, "mtime": 1752013876284, "results": "290", "hashOfConfig": "283"}, {"size": 798, "mtime": 1752013876285, "results": "291", "hashOfConfig": "283"}, {"size": 8697, "mtime": 1752039200482, "results": "292", "hashOfConfig": "283"}, {"size": 1119, "mtime": 1752038411255, "results": "293", "hashOfConfig": "283"}, {"size": 215, "mtime": 1752013876285, "results": "294", "hashOfConfig": "283"}, {"size": 1149, "mtime": 1752025570229, "results": "295", "hashOfConfig": "283"}, {"size": 489, "mtime": 1752013876286, "results": "296", "hashOfConfig": "283"}, {"size": 164, "mtime": 1752013876286, "results": "297", "hashOfConfig": "283"}, {"size": 222, "mtime": 1752013876286, "results": "298", "hashOfConfig": "283"}, {"size": 2142, "mtime": 1752013876286, "results": "299", "hashOfConfig": "283"}, {"size": 161, "mtime": 1752013876287, "results": "300", "hashOfConfig": "283"}, {"size": 220, "mtime": 1752013876287, "results": "301", "hashOfConfig": "283"}, {"size": 487, "mtime": 1752013876287, "results": "302", "hashOfConfig": "283"}, {"size": 161, "mtime": 1752013876287, "results": "303", "hashOfConfig": "283"}, {"size": 209, "mtime": 1752013876288, "results": "304", "hashOfConfig": "283"}, {"size": 480, "mtime": 1752013876288, "results": "305", "hashOfConfig": "283"}, {"size": 198, "mtime": 1752013876288, "results": "306", "hashOfConfig": "283"}, {"size": 218, "mtime": 1752013876288, "results": "307", "hashOfConfig": "283"}, {"size": 483, "mtime": 1752013876288, "results": "308", "hashOfConfig": "283"}, {"size": 5263, "mtime": 1752013876289, "results": "309", "hashOfConfig": "283"}, {"size": 892, "mtime": 1752038298815, "results": "310", "hashOfConfig": "283"}, {"size": 12425, "mtime": 1752038744893, "results": "311", "hashOfConfig": "283"}, {"size": 5157, "mtime": 1752084738018, "results": "312", "hashOfConfig": "283"}, {"size": 2002, "mtime": 1752013876290, "results": "313", "hashOfConfig": "283"}, {"size": 740, "mtime": 1752013876289, "results": "314", "hashOfConfig": "283"}, {"size": 221, "mtime": 1752013876290, "results": "315", "hashOfConfig": "283"}, {"size": 1168, "mtime": 1752039130744, "results": "316", "hashOfConfig": "283"}, {"size": 697, "mtime": 1752013876291, "results": "317", "hashOfConfig": "283"}, {"size": 2446, "mtime": 1752039853810, "results": "318", "hashOfConfig": "283"}, {"size": 1100, "mtime": 1752013876291, "results": "319", "hashOfConfig": "283"}, {"size": 286, "mtime": 1752013876292, "results": "320", "hashOfConfig": "283"}, {"size": 1671, "mtime": 1752013876292, "results": "321", "hashOfConfig": "283"}, {"size": 6671, "mtime": 1752105318179, "results": "322", "hashOfConfig": "283"}, {"size": 5461, "mtime": 1752039730089, "results": "323", "hashOfConfig": "283"}, {"size": 17922, "mtime": 1752111751620, "results": "324", "hashOfConfig": "283"}, {"size": 15571, "mtime": 1752112103686, "results": "325", "hashOfConfig": "283"}, {"size": 27789, "mtime": 1752112341811, "results": "326", "hashOfConfig": "283"}, {"size": 5515, "mtime": 1752084665559, "results": "327", "hashOfConfig": "283"}, {"size": 5459, "mtime": 1752022794258, "results": "328", "hashOfConfig": "283"}, {"size": 4310, "mtime": 1752023234114, "results": "329", "hashOfConfig": "283"}, {"size": 1206, "mtime": 1752013876292, "results": "330", "hashOfConfig": "283"}, {"size": 6740, "mtime": 1752038393714, "results": "331", "hashOfConfig": "283"}, {"size": 9617, "mtime": 1752013876293, "results": "332", "hashOfConfig": "283"}, {"size": 1814, "mtime": 1752013876293, "results": "333", "hashOfConfig": "283"}, {"size": 2352, "mtime": 1752110374860, "results": "334", "hashOfConfig": "283"}, {"size": 2918, "mtime": 1752013876294, "results": "335", "hashOfConfig": "283"}, {"size": 647, "mtime": 1752013876294, "results": "336", "hashOfConfig": "283"}, {"size": 2381, "mtime": 1752013876294, "results": "337", "hashOfConfig": "283"}, {"size": 806, "mtime": 1752013876294, "results": "338", "hashOfConfig": "283"}, {"size": 7851, "mtime": 1752038760330, "results": "339", "hashOfConfig": "283"}, {"size": 522, "mtime": 1752013876296, "results": "340", "hashOfConfig": "283"}, {"size": 1105, "mtime": 1752013876296, "results": "341", "hashOfConfig": "283"}, {"size": 530, "mtime": 1752013876296, "results": "342", "hashOfConfig": "283"}, {"size": 834, "mtime": 1752013876297, "results": "343", "hashOfConfig": "283"}, {"size": 283, "mtime": 1752013876295, "results": "344", "hashOfConfig": "283"}, {"size": 1157, "mtime": 1752013876295, "results": "345", "hashOfConfig": "283"}, {"size": 2015, "mtime": 1752013876297, "results": "346", "hashOfConfig": "283"}, {"size": 1063, "mtime": 1752013876297, "results": "347", "hashOfConfig": "283"}, {"size": 2513, "mtime": 1752013876297, "results": "348", "hashOfConfig": "283"}, {"size": 2660, "mtime": 1752013876298, "results": "349", "hashOfConfig": "283"}, {"size": 3697, "mtime": 1752013876298, "results": "350", "hashOfConfig": "283"}, {"size": 2499, "mtime": 1752013876298, "results": "351", "hashOfConfig": "283"}, {"size": 8030, "mtime": 1752039092289, "results": "352", "hashOfConfig": "283"}, {"size": 6141, "mtime": 1752084749943, "results": "353", "hashOfConfig": "283"}, {"size": 895, "mtime": 1752013876298, "results": "354", "hashOfConfig": "283"}, {"size": 2486, "mtime": 1752013876298, "results": "355", "hashOfConfig": "283"}, {"size": 7380, "mtime": 1752040570380, "results": "356", "hashOfConfig": "283"}, {"size": 9130, "mtime": 1752041230086, "results": "357", "hashOfConfig": "283"}, {"size": 6884, "mtime": 1752023844355, "results": "358", "hashOfConfig": "283"}, {"size": 5641, "mtime": 1752028964964, "results": "359", "hashOfConfig": "283"}, {"size": 2129, "mtime": 1752013876299, "results": "360", "hashOfConfig": "283"}, {"size": 4037, "mtime": 1752013876299, "results": "361", "hashOfConfig": "283"}, {"size": 1684, "mtime": 1752013876299, "results": "362", "hashOfConfig": "283"}, {"size": 295, "mtime": 1752013876300, "results": "363", "hashOfConfig": "283"}, {"size": 1158, "mtime": 1752013876300, "results": "364", "hashOfConfig": "283"}, {"size": 1681, "mtime": 1752013876300, "results": "365", "hashOfConfig": "283"}, {"size": 2479, "mtime": 1752013876300, "results": "366", "hashOfConfig": "283"}, {"size": 2186, "mtime": 1752013876301, "results": "367", "hashOfConfig": "283"}, {"size": 3030, "mtime": 1752013876301, "results": "368", "hashOfConfig": "283"}, {"size": 2090, "mtime": 1752013876301, "results": "369", "hashOfConfig": "283"}, {"size": 10300, "mtime": 1752013876301, "results": "370", "hashOfConfig": "283"}, {"size": 1265, "mtime": 1752013876302, "results": "371", "hashOfConfig": "283"}, {"size": 839, "mtime": 1752013876302, "results": "372", "hashOfConfig": "283"}, {"size": 4849, "mtime": 1752013876302, "results": "373", "hashOfConfig": "283"}, {"size": 1496, "mtime": 1752039820990, "results": "374", "hashOfConfig": "283"}, {"size": 8498, "mtime": 1752013876302, "results": "375", "hashOfConfig": "283"}, {"size": 3963, "mtime": 1752013876303, "results": "376", "hashOfConfig": "283"}, {"size": 4218, "mtime": 1752013876303, "results": "377", "hashOfConfig": "283"}, {"size": 8565, "mtime": 1752013876303, "results": "378", "hashOfConfig": "283"}, {"size": 3964, "mtime": 1752013876304, "results": "379", "hashOfConfig": "283"}, {"size": 336, "mtime": 1752013876304, "results": "380", "hashOfConfig": "283"}, {"size": 1584, "mtime": 1752013876304, "results": "381", "hashOfConfig": "283"}, {"size": 2345, "mtime": 1752013876304, "results": "382", "hashOfConfig": "283"}, {"size": 992, "mtime": 1752013876304, "results": "383", "hashOfConfig": "283"}, {"size": 641, "mtime": 1752013876305, "results": "384", "hashOfConfig": "283"}, {"size": 8695, "mtime": 1752013876305, "results": "385", "hashOfConfig": "283"}, {"size": 825, "mtime": 1752013876305, "results": "386", "hashOfConfig": "283"}, {"size": 6847, "mtime": 1752013876305, "results": "387", "hashOfConfig": "283"}, {"size": 2850, "mtime": 1752013876306, "results": "388", "hashOfConfig": "283"}, {"size": 1692, "mtime": 1752013876306, "results": "389", "hashOfConfig": "283"}, {"size": 777, "mtime": 1752013876306, "results": "390", "hashOfConfig": "283"}, {"size": 1519, "mtime": 1752013876306, "results": "391", "hashOfConfig": "283"}, {"size": 2094, "mtime": 1752013876306, "results": "392", "hashOfConfig": "283"}, {"size": 1710, "mtime": 1752013876306, "results": "393", "hashOfConfig": "283"}, {"size": 6454, "mtime": 1752013876307, "results": "394", "hashOfConfig": "283"}, {"size": 738, "mtime": 1752013876307, "results": "395", "hashOfConfig": "283"}, {"size": 4245, "mtime": 1752013876307, "results": "396", "hashOfConfig": "283"}, {"size": 22452, "mtime": 1752013876308, "results": "397", "hashOfConfig": "283"}, {"size": 292, "mtime": 1752013876308, "results": "398", "hashOfConfig": "283"}, {"size": 2071, "mtime": 1752013876308, "results": "399", "hashOfConfig": "283"}, {"size": 595, "mtime": 1752013876308, "results": "400", "hashOfConfig": "283"}, {"size": 1214, "mtime": 1752013876309, "results": "401", "hashOfConfig": "283"}, {"size": 3280, "mtime": 1752013876309, "results": "402", "hashOfConfig": "283"}, {"size": 6331, "mtime": 1752013876310, "results": "403", "hashOfConfig": "283"}, {"size": 6155, "mtime": 1752013876310, "results": "404", "hashOfConfig": "283"}, {"size": 3896, "mtime": 1752013876310, "results": "405", "hashOfConfig": "283"}, {"size": 3836, "mtime": 1752013876311, "results": "406", "hashOfConfig": "283"}, {"size": 7498, "mtime": 1752013876311, "results": "407", "hashOfConfig": "283"}, {"size": 4497, "mtime": 1752013876311, "results": "408", "hashOfConfig": "283"}, {"size": 2391, "mtime": 1752013876311, "results": "409", "hashOfConfig": "283"}, {"size": 3579, "mtime": 1752013876312, "results": "410", "hashOfConfig": "283"}, {"size": 2575, "mtime": 1752013876309, "results": "411", "hashOfConfig": "283"}, {"size": 2044, "mtime": 1752013876312, "results": "412", "hashOfConfig": "283"}, {"size": 781, "mtime": 1752013876312, "results": "413", "hashOfConfig": "283"}, {"size": 2007, "mtime": 1752013876312, "results": "414", "hashOfConfig": "283"}, {"size": 1620, "mtime": 1752013876312, "results": "415", "hashOfConfig": "283"}, {"size": 1961, "mtime": 1752013876313, "results": "416", "hashOfConfig": "283"}, {"size": 1092, "mtime": 1752013876313, "results": "417", "hashOfConfig": "283"}, {"size": 2758, "mtime": 1752013876313, "results": "418", "hashOfConfig": "283"}, {"size": 3517, "mtime": 1752041486259, "results": "419", "hashOfConfig": "283"}, {"size": 4212, "mtime": 1752013876314, "results": "420", "hashOfConfig": "283"}, {"size": 587, "mtime": 1752013876315, "results": "421", "hashOfConfig": "283"}, {"size": 3821, "mtime": 1752013876315, "results": "422", "hashOfConfig": "283"}, {"size": 3821, "mtime": 1752013876315, "results": "423", "hashOfConfig": "283"}, {"size": 2645, "mtime": 1752013876315, "results": "424", "hashOfConfig": "283"}, {"size": 3838, "mtime": 1752013876316, "results": "425", "hashOfConfig": "283"}, {"size": 3726, "mtime": 1752013876316, "results": "426", "hashOfConfig": "283"}, {"size": 10137, "mtime": 1752013876316, "results": "427", "hashOfConfig": "283"}, {"size": 575, "mtime": 1752013876317, "results": "428", "hashOfConfig": "283"}, {"size": 1868, "mtime": 1752013876317, "results": "429", "hashOfConfig": "283"}, {"size": 2224, "mtime": 1752013876317, "results": "430", "hashOfConfig": "283"}, {"size": 2184, "mtime": 1752013876318, "results": "431", "hashOfConfig": "283"}, {"size": 565, "mtime": 1752013876318, "results": "432", "hashOfConfig": "283"}, {"size": 2675, "mtime": 1752013876318, "results": "433", "hashOfConfig": "283"}, {"size": 1029, "mtime": 1752013876319, "results": "434", "hashOfConfig": "283"}, {"size": 3966, "mtime": 1752013876319, "results": "435", "hashOfConfig": "283"}, {"size": 1465, "mtime": 1752013876319, "results": "436", "hashOfConfig": "283"}, {"size": 10034, "mtime": 1752013876320, "results": "437", "hashOfConfig": "283"}, {"size": 6358, "mtime": 1752013876320, "results": "438", "hashOfConfig": "283"}, {"size": 783, "mtime": 1752013876320, "results": "439", "hashOfConfig": "283"}, {"size": 5040, "mtime": 1752013876320, "results": "440", "hashOfConfig": "283"}, {"size": 1015, "mtime": 1752013876321, "results": "441", "hashOfConfig": "283"}, {"size": 2173, "mtime": 1752013876321, "results": "442", "hashOfConfig": "283"}, {"size": 6627, "mtime": 1752013876321, "results": "443", "hashOfConfig": "283"}, {"size": 1070, "mtime": 1752013876321, "results": "444", "hashOfConfig": "283"}, {"size": 1767, "mtime": 1752013876322, "results": "445", "hashOfConfig": "283"}, {"size": 2153, "mtime": 1752013876322, "results": "446", "hashOfConfig": "283"}, {"size": 1080, "mtime": 1752013876322, "results": "447", "hashOfConfig": "283"}, {"size": 406, "mtime": 1752013876323, "results": "448", "hashOfConfig": "283"}, {"size": 673, "mtime": 1752013876323, "results": "449", "hashOfConfig": "283"}, {"size": 23000, "mtime": 1752013876323, "results": "450", "hashOfConfig": "283"}, {"size": 197, "mtime": 1752013876324, "results": "451", "hashOfConfig": "283"}, {"size": 1437, "mtime": 1752013876324, "results": "452", "hashOfConfig": "283"}, {"size": 1328, "mtime": 1752013876324, "results": "453", "hashOfConfig": "283"}, {"size": 749, "mtime": 1752013876325, "results": "454", "hashOfConfig": "283"}, {"size": 749, "mtime": 1752013876325, "results": "455", "hashOfConfig": "283"}, {"size": 1945, "mtime": 1752013876325, "results": "456", "hashOfConfig": "283"}, {"size": 8379, "mtime": 1752013876326, "results": "457", "hashOfConfig": "283"}, {"size": 420, "mtime": 1752013876326, "results": "458", "hashOfConfig": "283"}, {"size": 726, "mtime": 1752013876326, "results": "459", "hashOfConfig": "283"}, {"size": 507, "mtime": 1752013876326, "results": "460", "hashOfConfig": "283"}, {"size": 605, "mtime": 1752013876326, "results": "461", "hashOfConfig": "283"}, {"size": 750, "mtime": 1752013876326, "results": "462", "hashOfConfig": "283"}, {"size": 1299, "mtime": 1752013876328, "results": "463", "hashOfConfig": "283"}, {"size": 1014, "mtime": 1752013876328, "results": "464", "hashOfConfig": "283"}, {"size": 6601, "mtime": 1752035907940, "results": "465", "hashOfConfig": "283"}, {"size": 8360, "mtime": 1752027194239, "results": "466", "hashOfConfig": "283"}, {"size": 4461, "mtime": 1752034643812, "results": "467", "hashOfConfig": "283"}, {"size": 2556, "mtime": 1752013876328, "results": "468", "hashOfConfig": "283"}, {"size": 8567, "mtime": 1752039503783, "results": "469", "hashOfConfig": "283"}, {"size": 896, "mtime": 1752013876329, "results": "470", "hashOfConfig": "283"}, {"size": 402, "mtime": 1752013876329, "results": "471", "hashOfConfig": "283"}, {"size": 2557, "mtime": 1752013876329, "results": "472", "hashOfConfig": "283"}, {"size": 11282, "mtime": 1752107117523, "results": "473", "hashOfConfig": "283"}, {"size": 6011, "mtime": 1752033767665, "results": "474", "hashOfConfig": "283"}, {"size": 15050, "mtime": 1752101099438, "results": "475", "hashOfConfig": "283"}, {"size": 599, "mtime": 1752013876329, "results": "476", "hashOfConfig": "283"}, {"size": 4830, "mtime": 1752107690743, "results": "477", "hashOfConfig": "283"}, {"size": 628, "mtime": 1752013876330, "results": "478", "hashOfConfig": "283"}, {"size": 5967, "mtime": 1752084895005, "results": "479", "hashOfConfig": "283"}, {"size": 9059, "mtime": 1752024034909, "results": "480", "hashOfConfig": "283"}, {"size": 9381, "mtime": 1752080814897, "results": "481", "hashOfConfig": "283"}, {"size": 16199, "mtime": 1752080891540, "results": "482", "hashOfConfig": "283"}, {"size": 181, "mtime": 1752021355026, "results": "483", "hashOfConfig": "283"}, {"size": 4172, "mtime": 1752028855326, "results": "484", "hashOfConfig": "283"}, {"size": 5488, "mtime": 1752028382913, "results": "485", "hashOfConfig": "283"}, {"size": 7740, "mtime": 1752107168806, "results": "486", "hashOfConfig": "283"}, {"size": 1308, "mtime": 1752013876330, "results": "487", "hashOfConfig": "283"}, {"size": 863, "mtime": 1752025615436, "results": "488", "hashOfConfig": "283"}, {"size": 2893, "mtime": 1752078762026, "results": "489", "hashOfConfig": "283"}, {"size": 4433, "mtime": 1752078780088, "results": "490", "hashOfConfig": "283"}, {"size": 1621, "mtime": 1752078720185, "results": "491", "hashOfConfig": "283"}, {"size": 11566, "mtime": 1752110527559, "results": "492", "hashOfConfig": "283"}, {"size": 4373, "mtime": 1752078711457, "results": "493", "hashOfConfig": "283"}, {"size": 4028, "mtime": 1752110832367, "results": "494", "hashOfConfig": "283"}, {"size": 3351, "mtime": 1752110744095, "results": "495", "hashOfConfig": "283"}, {"size": 4827, "mtime": 1752111130348, "results": "496", "hashOfConfig": "283"}, {"size": 4116, "mtime": 1752110969414, "results": "497", "hashOfConfig": "283"}, {"size": 2301, "mtime": 1752111187827, "results": "498", "hashOfConfig": "283"}, {"size": 2696, "mtime": 1752111258676, "results": "499", "hashOfConfig": "283"}, {"size": 5355, "mtime": 1752111464402, "results": "500", "hashOfConfig": "283"}, {"size": 12440, "mtime": 1752111692825, "results": "501", "hashOfConfig": "283"}, {"size": 3029, "mtime": 1752078747371, "results": "502", "hashOfConfig": "283"}, {"size": 1097, "mtime": 1752047173760, "results": "503", "hashOfConfig": "283"}, {"size": 855, "mtime": 1752107729337, "results": "504", "hashOfConfig": "283"}, {"size": 10505, "mtime": 1752105490057, "results": "505", "hashOfConfig": "283"}, {"size": 8952, "mtime": 1752111816938, "results": "506", "hashOfConfig": "283"}, {"size": 17214, "mtime": 1752109211981, "results": "507", "hashOfConfig": "283"}, {"size": 21577, "mtime": 1752109530544, "results": "508", "hashOfConfig": "283"}, {"size": 13124, "mtime": 1752109567397, "results": "509", "hashOfConfig": "283"}, {"size": 2801, "mtime": 1752045921409, "results": "510", "hashOfConfig": "283"}, {"size": 13389, "mtime": 1752046744980, "results": "511", "hashOfConfig": "283"}, {"size": 24950, "mtime": 1752112443705, "results": "512", "hashOfConfig": "283"}, {"size": 9282, "mtime": 1752112569974, "results": "513", "hashOfConfig": "283"}, {"size": 15655, "mtime": 1752046897241, "results": "514", "hashOfConfig": "283"}, {"size": 3913, "mtime": 1752047125413, "results": "515", "hashOfConfig": "283"}, {"size": 22711, "mtime": 1752109622770, "results": "516", "hashOfConfig": "283"}, {"size": 12537, "mtime": 1752112630379, "results": "517", "hashOfConfig": "283"}, {"size": 9649, "mtime": 1752112702460, "results": "518", "hashOfConfig": "283"}, {"size": 10258, "mtime": 1752109665685, "results": "519", "hashOfConfig": "283"}, {"size": 10932, "mtime": 1752079315096, "results": "520", "hashOfConfig": "283"}, {"size": 15416, "mtime": 1752109708673, "results": "521", "hashOfConfig": "283"}, {"size": 5561, "mtime": 1752046334797, "results": "522", "hashOfConfig": "283"}, {"size": 7903, "mtime": 1752047085021, "results": "523", "hashOfConfig": "283"}, {"size": 12488, "mtime": 1752112781550, "results": "524", "hashOfConfig": "283"}, {"size": 15153, "mtime": 1752110274973, "results": "525", "hashOfConfig": "283"}, {"size": 1027, "mtime": 1752045700082, "results": "526", "hashOfConfig": "283"}, {"size": 12303, "mtime": 1752096534832, "results": "527", "hashOfConfig": "283"}, {"size": 18317, "mtime": 1752079930982, "results": "528", "hashOfConfig": "283"}, {"size": 7120, "mtime": 1752046677073, "results": "529", "hashOfConfig": "283"}, {"size": 10003, "mtime": 1752046219744, "results": "530", "hashOfConfig": "283"}, {"size": 8198, "mtime": 1752046071832, "results": "531", "hashOfConfig": "283"}, {"size": 17287, "mtime": 1752109752574, "results": "532", "hashOfConfig": "283"}, {"size": 15050, "mtime": 1752079660828, "results": "533", "hashOfConfig": "283"}, {"size": 17086, "mtime": 1752107610745, "results": "534", "hashOfConfig": "283"}, {"size": 23024, "mtime": 1752109171793, "results": "535", "hashOfConfig": "283"}, {"size": 16077, "mtime": 1752107845273, "results": "536", "hashOfConfig": "283"}, {"size": 11880, "mtime": 1752107433721, "results": "537", "hashOfConfig": "283"}, {"size": 13734, "mtime": 1752107541200, "results": "538", "hashOfConfig": "283"}, {"size": 17421, "mtime": 1752107916596, "results": "539", "hashOfConfig": "283"}, {"size": 13344, "mtime": 1752109133982, "results": "540", "hashOfConfig": "283"}, {"size": 20231, "mtime": 1752107991717, "results": "541", "hashOfConfig": "283"}, {"size": 8697, "mtime": 1752047432238, "results": "542", "hashOfConfig": "283"}, {"size": 15459, "mtime": 1752108703579, "results": "543", "hashOfConfig": "283"}, {"size": 8494, "mtime": 1752105275472, "results": "544", "hashOfConfig": "283"}, {"size": 9407, "mtime": 1752105449675, "results": "545", "hashOfConfig": "283"}, {"size": 11095, "mtime": 1752101548710, "results": "546", "hashOfConfig": "283"}, {"size": 8850, "mtime": 1752046490235, "results": "547", "hashOfConfig": "283"}, {"size": 12096, "mtime": 1752110207223, "results": "548", "hashOfConfig": "283"}, {"size": 11196, "mtime": 1752101443496, "results": "549", "hashOfConfig": "283"}, {"size": 11871, "mtime": 1752101623712, "results": "550", "hashOfConfig": "283"}, {"size": 6447, "mtime": 1752104875572, "results": "551", "hashOfConfig": "283"}, {"size": 10265, "mtime": 1752107661403, "results": "552", "hashOfConfig": "283"}, {"size": 7655, "mtime": 1752038262141, "results": "553", "hashOfConfig": "283"}, {"size": 10181, "mtime": 1752078176141, "results": "554", "hashOfConfig": "283"}, {"size": 11372, "mtime": 1752096405496, "results": "555", "hashOfConfig": "283"}, {"size": 7060, "mtime": 1752110302334, "results": "556", "hashOfConfig": "283"}, {"size": 9488, "mtime": 1752105582226, "results": "557", "hashOfConfig": "283"}, {"size": 10730, "mtime": 1752080015184, "results": "558", "hashOfConfig": "283"}, {"size": 17203, "mtime": 1752080084653, "results": "559", "hashOfConfig": "283"}, {"size": 12541, "mtime": 1752102014566, "results": "560", "hashOfConfig": "283"}, {"size": 15089, "mtime": 1752102079980, "results": "561", "hashOfConfig": "283"}, {"size": 13511, "mtime": 1752101885020, "results": "562", "hashOfConfig": "283"}, {"size": 17378, "mtime": 1752080165578, "results": "563", "hashOfConfig": "283"}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1en2yoy", {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\appointments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\auth\\sync\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\treatments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\treatments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\auth\\sign-in\\[[...sign-in]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\auth\\sign-up\\[[...sign-up]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\appointments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\kanban\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\__tests__\\page.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\[productId]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\treatments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\active-theme.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-calendar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-filters.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-form-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointments-list.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\multi-step-appointment-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\__tests__\\appointment-filters.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\__tests__\\appointment-form-dialog.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\__tests__\\appointments-list.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\breadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\dashboard\\dashboard-metrics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\file-uploader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\form-card-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\icons.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\render-result.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\result-item.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\kbar\\use-theme-switching.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\app-sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\cta-github.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\page-container.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\providers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\ThemeToggle\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\ThemeToggle\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\layout\\user-nav.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\modal\\alert-modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\nav-main.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\nav-projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\nav-user.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\org-switcher.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\patient-form-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\__tests__\\patient-form-dialog.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\search-input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\theme-selector.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\treatment-form-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\treatments-list.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\__tests__\\treatment-form-dialog.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\treatments\\__tests__\\treatments-list.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\confirmation-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\heading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\modal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-column-header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-date-filter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-faceted-filter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-slider-filter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-toolbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table-view-options.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table\\data-table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\user-avatar-profile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\config\\data-table.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\constants\\data.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\constants\\mock-api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\github-auth-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\sign-in-view.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\sign-up-view.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\auth\\components\\user-auth-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\board-column.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\column-action.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\kanban-board.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\kanban-view-page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\new-section-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\new-task-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\task-card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\utils\\store.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\area-graph-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\area-graph.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\bar-graph-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\bar-graph.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\overview.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\pie-graph-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\pie-graph.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\recent-sales-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\overview\\components\\recent-sales.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-listing.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\cell-action.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\columns.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\options.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-view-page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\profile\\components\\profile-create-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\profile\\components\\profile-view-page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\profile\\utils\\form-schema.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-breadcrumbs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-callback-ref.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-callback-ref.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-controllable-state.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-data-table.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-debounce.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-debounced-callback.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-media-query.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-mobile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\hooks\\use-multistep-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\instrumentation-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\instrumentation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\appointment-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\auth-middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\data-table.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\error-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\font.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\format.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\parsers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\payload-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-context.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\role-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\searchparams.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\integration\\api-endpoints.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\integration\\clinic-workflows.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\mocks\\data.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\mocks\\handlers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\mocks\\server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\setup.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\utils.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\types\\clinic.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\types\\data-table.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bill-items\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bill-items\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\generate-from-appointment\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\bills\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-interactions\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-interactions\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-tasks\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-tasks\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\interactions\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\tasks\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\timeline\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\payments\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\payments\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\billing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-reminder-settings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\appointments\\appointment-status-manager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\advanced-payment-processor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\advanced-receipt-system.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\appointment-to-bill.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-filters.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\bill-status-manager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\billing-list.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\billing-tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\comprehensive-financial-reports.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\deposit-application-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\deposit-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\enhanced-financial-dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\enhanced-form-validation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\financial-dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\financial-summary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\generate-bill-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\optimized-bill-list.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\optimized-billing-list.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\payment-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\payment-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\performance-dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\receipt-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\receipt-manager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\receipt.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\security-audit-dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\billing\\security-monitor.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\communication-log.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\crm-dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\interaction-form-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\interaction-timeline.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\quick-actions.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\task-form-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\crm\\task-manager.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\patient-detail-view.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\validation-feedback.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\api\\billing.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\appointment-notifications.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\appointment-reminders.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-error-handler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-notifications.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-performance.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-security.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\billing-validation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\calendar-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\crm-notifications.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\translations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\validation\\billing-schemas.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\lib\\validation\\validation-utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\test\\billing-test-utils.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\appointment-system.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-api.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-integration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-performance.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-security.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\billing\\billing-validation.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\__tests__\\e2e\\billing-e2e.spec.ts", [], []]