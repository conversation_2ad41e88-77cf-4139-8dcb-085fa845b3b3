'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  IconCash, 
  IconReceipt,
  IconArrowRight,
  IconX,
  IconCheck
} from '@tabler/icons-react';
import { Bill } from '@/types/clinic';
import { depositsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { toast } from 'sonner';
import { billingNotifications } from '@/lib/billing-notifications';

// Deposit application validation schema
const depositApplicationSchema = z.object({
  depositId: z.string().min(1, '请选择押金'),
  amount: z.number().min(0.01, '应用金额必须大于0'),
});

type DepositApplicationData = z.infer<typeof depositApplicationSchema>;

interface DepositApplicationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bill: Bill;
  onSuccess?: () => void;
}

interface Deposit {
  id: string;
  depositNumber: string;
  depositType: string;
  amount: number;
  usedAmount: number;
  remainingAmount: number;
  status: string;
  purpose: string;
  expiryDate?: string;
}

export function DepositApplicationDialog({
  open,
  onOpenChange,
  bill,
  onSuccess,
}: DepositApplicationDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [availableDeposits, setAvailableDeposits] = useState<Deposit[]>([]);
  const [selectedDeposit, setSelectedDeposit] = useState<Deposit | null>(null);
  const [isLoadingDeposits, setIsLoadingDeposits] = useState(false);

  const form = useForm<DepositApplicationData>({
    resolver: zodResolver(depositApplicationSchema),
    defaultValues: {
      depositId: '',
      amount: 0,
    },
  });

  // Load available deposits for the patient
  useEffect(() => {
    if (open && bill.patient) {
      loadAvailableDeposits();
    }
  }, [open, bill.patient]);

  const loadAvailableDeposits = async () => {
    setIsLoadingDeposits(true);
    try {
      const response = await depositsAPI.getDeposits({
        patient: typeof bill.patient === 'string' ? bill.patient : bill.patient?.id || bill.patientId,
        status: 'active',
      });
      
      // Filter deposits with remaining balance
      const activeDeposits = response.docs.filter(deposit => 
        deposit.remainingAmount > 0 && 
        (!deposit.expiryDate || new Date(deposit.expiryDate) > new Date())
      );
      
      setAvailableDeposits(activeDeposits);
    } catch (error) {
      console.error('Error loading deposits:', error);
      toast.error('加载押金信息失败');
    } finally {
      setIsLoadingDeposits(false);
    }
  };

  const handleDepositSelect = (depositId: string) => {
    const deposit = availableDeposits.find(d => d.id === depositId);
    setSelectedDeposit(deposit || null);
    
    if (deposit) {
      // Set default amount to minimum of deposit balance and bill balance
      const maxAmount = Math.min(deposit.remainingAmount, bill.remainingAmount || 0);
      form.setValue('amount', maxAmount);
    }
  };

  const handleSubmit = async (data: DepositApplicationData) => {
    if (!selectedDeposit) return;

    setIsSubmitting(true);
    try {
      await depositsAPI.applyToBill(data.depositId, bill.id, data.amount);

      toast.success('押金抵扣成功');
      form.reset();
      onOpenChange(false);
      onSuccess?.();
    } catch (error) {
      console.error('Error applying deposit:', error);
      if (error instanceof BillingAPIError) {
        toast.error(error.message);
      } else {
        toast.error('押金抵扣失败');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const maxApplicationAmount = selectedDeposit 
    ? Math.min(selectedDeposit.remainingAmount, bill.remainingAmount || 0)
    : 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconCash className="h-5 w-5" />
            押金抵扣
          </DialogTitle>
          <DialogDescription>
            将患者的押金余额应用到当前账单支付
          </DialogDescription>
        </DialogHeader>

        {/* Bill Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">账单信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>账单编号:</span>
              <span className="font-mono">{bill.billNumber}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>账单总额:</span>
              <span>{billingUtils.formatCurrency(bill.totalAmount)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>剩余金额:</span>
              <span className="font-semibold text-orange-600">
                {billingUtils.formatCurrency(bill.remainingAmount || 0)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Deposit Selection */}
            <FormField
              control={form.control}
              name="depositId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>选择押金</FormLabel>
                  <Select 
                    onValueChange={(value) => {
                      field.onChange(value);
                      handleDepositSelect(value);
                    }} 
                    value={field.value}
                    disabled={isLoadingDeposits}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={
                          isLoadingDeposits ? "加载中..." : 
                          availableDeposits.length === 0 ? "无可用押金" : "选择押金"
                        } />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableDeposits.map((deposit) => (
                        <SelectItem key={deposit.id} value={deposit.id}>
                          <div className="flex flex-col">
                            <span>{deposit.depositNumber} - {deposit.purpose}</span>
                            <span className="text-xs text-muted-foreground">
                              余额: {billingUtils.formatCurrency(deposit.remainingAmount)}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Selected Deposit Details */}
            {selectedDeposit && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm">押金详情</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>押金编号:</span>
                    <span className="font-mono">{selectedDeposit.depositNumber}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>押金类型:</span>
                    <Badge variant="outline">
                      {selectedDeposit.depositType === 'treatment' ? '治疗押金' :
                       selectedDeposit.depositType === 'appointment' ? '预约押金' : '材料押金'}
                    </Badge>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>押金总额:</span>
                    <span>{billingUtils.formatCurrency(selectedDeposit.amount)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>可用余额:</span>
                    <span className="font-semibold text-green-600">
                      {billingUtils.formatCurrency(selectedDeposit.remainingAmount)}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Application Amount */}
            {selectedDeposit && (
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>抵扣金额</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        max={maxApplicationAmount}
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      最大可抵扣金额: {billingUtils.formatCurrency(maxApplicationAmount)}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Application Summary */}
            {selectedDeposit && form.watch('amount') > 0 && (
              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">押金余额</div>
                      <div className="font-semibold">
                        {billingUtils.formatCurrency(selectedDeposit.remainingAmount)}
                      </div>
                    </div>
                    <IconArrowRight className="h-4 w-4 text-muted-foreground" />
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">抵扣金额</div>
                      <div className="font-semibold text-blue-600">
                        {billingUtils.formatCurrency(form.watch('amount'))}
                      </div>
                    </div>
                    <IconArrowRight className="h-4 w-4 text-muted-foreground" />
                    <div className="text-center">
                      <div className="text-sm text-muted-foreground">账单余额</div>
                      <div className="font-semibold text-green-600">
                        {billingUtils.formatCurrency((bill.remainingAmount || 0) - form.watch('amount'))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Separator />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                <IconX className="h-4 w-4 mr-2" />
                取消
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting || !selectedDeposit || form.watch('amount') <= 0}
              >
                <IconCheck className="h-4 w-4 mr-2" />
                {isSubmitting ? '处理中...' : '确认抵扣'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
