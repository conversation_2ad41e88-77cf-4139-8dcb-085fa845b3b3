try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="4fad934b-fd04-4bd6-97f4-cc4168afa794",e._sentryDebugIdIdentifier="sentry-dbid-4fad934b-fd04-4bd6-97f4-cc4168afa794")}catch(e){}"use strict";(()=>{var e={};e.id=4929,e.ids=[4929],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},17331:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>w,serverHooks:()=>b,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>P});var s={};t.r(s),t.d(s,{DELETE:()=>k,GET:()=>y,HEAD:()=>m,OPTIONS:()=>T,PATCH:()=>f,POST:()=>g,PUT:()=>h});var o=t(86047),a=t(85544),i=t(36135),n=t(63033),p=t(53547),d=t(54360),u=t(19761);let l=(0,p.ZA)(async(e,r,{params:t})=>{try{let s=(0,d.o)(e),o=new URL(r.url),a=parseInt(o.searchParams.get("limit")||"20"),i=parseInt(o.searchParams.get("page")||"1"),n=o.searchParams.get("taskType"),u=o.searchParams.get("status"),l=o.searchParams.get("priority"),c=o.searchParams.get("assignedTo"),x={patient:{equals:t.id}};n&&(x.taskType={equals:n}),u&&(x.status={equals:u}),l&&(x.priority={equals:l}),c&&(x.assignedTo={equals:c}),"doctor"===e.role?x.and=[x,{or:[{assignedTo:{equals:e.payloadUserId}},{createdBy:{equals:e.payloadUserId}},{taskType:{in:["treatment-reminder","medical-record-update","consultation-follow-up"]}}]}]:"front-desk"===e.role&&(x.and=[x,{or:[{assignedTo:{equals:e.payloadUserId}},{taskType:{in:["follow-up-call","appointment-scheduling","billing-follow-up"]}}]}]);let q=await s.getPatientTasks({limit:a,page:i,where:x,sort:"-dueDate"});return(0,p.$y)(q)}catch(e){return console.error("Error fetching patient tasks:",e),(0,p.WX)("Failed to fetch patient tasks")}}),c={...n},x="workUnitAsyncStorage"in c?c.workUnitAsyncStorage:"requestAsyncStorage"in c?c.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,s)=>{let o;try{let e=x?.getStore();o=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/patients/[id]/tasks",headers:o}).apply(t,s)}})}let y=q(l,"GET"),g=q(void 0,"POST"),h=q(void 0,"PUT"),f=q(void 0,"PATCH"),k=q(void 0,"DELETE"),m=q(void 0,"HEAD"),T=q(void 0,"OPTIONS"),w=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/patients/[id]/tasks/route",pathname:"/api/patients/[id]/tasks",filename:"route",bundlePath:"app/api/patients/[id]/tasks/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\tasks\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:P,serverHooks:b}=w;function E(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:P})}},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,5886,9615,125],()=>t(17331));module.exports=s})();
//# sourceMappingURL=route.js.map