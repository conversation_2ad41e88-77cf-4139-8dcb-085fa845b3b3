{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "D3QgCOQhp7Pm6Xjaarvqo", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "ce4381f4b93099b7e16d3c8d87282ada", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "086fed829a86d8fede85ed401f260990a68c9c8cac59c379dec68aefecfe5315", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "dac356dfbebcef5ce428616824be868dbb0c156e75ff27c761a0e200210bb55f"}}}, "functions": {}, "sortedMiddleware": ["/"]}