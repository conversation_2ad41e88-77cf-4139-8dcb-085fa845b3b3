{"version": 3, "middleware": {"/": {"files": ["server/edge-instrumentation.js", "server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "pCDwel5B6Cz3pIJIBy48K", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "c98d3a6a9b5ecc80564fc608c94a0bae", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b2c91712798c3f89856f97a95f29993fe24bca7c06b3199f6f7fa5374a8a5d54", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bc9e81bac0fcb0be2b5fa0013405e29134ef342f65c8b4ef357c590ab6a56638"}}}, "functions": {}, "sortedMiddleware": ["/"]}