'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  IconPlus,
  IconSearch,
  IconFilter,
  IconClock,
  IconUser,
  IconCalendar,
  IconPhone,
  IconStethoscope,
  IconCreditCard,
  IconFileText,
  IconMessageCircle,
  IconAlertTriangle,
  IconCheck,
  IconX,
  IconArrowRight
} from '@/components/icons';
import { PatientTask, User } from '@/types/clinic';
import { formatDateTime, isOverdue } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface TaskManagerProps {
  patientId?: string;
  tasks: PatientTask[];
  loading?: boolean;
  onCreateTask?: () => void;
  onTaskClick?: (task: PatientTask) => void;
  onTaskStatusChange?: (taskId: string, status: PatientTask['status']) => void;
  className?: string;
  viewMode?: 'kanban' | 'list';
}

const taskTypeConfig = {
  'follow-up-call': {
    label: '跟进电话',
    icon: IconPhone,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  'appointment-scheduling': {
    label: '预约安排',
    icon: IconCalendar,
    color: 'bg-green-100 text-green-800 border-green-200',
  },
  'treatment-reminder': {
    label: '治疗提醒',
    icon: IconStethoscope,
    color: 'bg-purple-100 text-purple-800 border-purple-200',
  },
  'billing-follow-up': {
    label: '账单跟进',
    icon: IconCreditCard,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
  'medical-record-update': {
    label: '病历更新',
    icon: IconFileText,
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  },
  'consultation-follow-up': {
    label: '咨询跟进',
    icon: IconMessageCircle,
    color: 'bg-orange-100 text-orange-800 border-orange-200',
  },
};

const statusConfig = {
  'pending': { 
    label: '待处理', 
    color: 'bg-gray-100 text-gray-800',
    icon: IconClock
  },
  'in-progress': {
    label: '进行中',
    color: 'bg-blue-100 text-blue-800',
    icon: IconArrowRight
  },
  'completed': { 
    label: '已完成', 
    color: 'bg-green-100 text-green-800',
    icon: IconCheck
  },
  'cancelled': { 
    label: '已取消', 
    color: 'bg-red-100 text-red-800',
    icon: IconX
  },
};

const priorityConfig = {
  'low': { label: '低', color: 'bg-gray-100 text-gray-600' },
  'medium': { label: '中', color: 'bg-yellow-100 text-yellow-800' },
  'high': { label: '高', color: 'bg-orange-100 text-orange-800' },
  'urgent': { label: '紧急', color: 'bg-red-100 text-red-800' },
};

export function TaskManager({
  patientId,
  tasks,
  loading = false,
  onCreateTask,
  onTaskClick,
  onTaskStatusChange,
  className,
  viewMode = 'list',
}: TaskManagerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filteredTasks, setFilteredTasks] = useState<PatientTask[]>(tasks);

  // Filter tasks based on search and filters
  useEffect(() => {
    let filtered = tasks;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(task => task.taskType === filterType);
    }

    // Status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(task => task.status === filterStatus);
    }

    // Priority filter
    if (filterPriority !== 'all') {
      filtered = filtered.filter(task => task.priority === filterPriority);
    }

    setFilteredTasks(filtered);
  }, [tasks, searchTerm, filterType, filterStatus, filterPriority]);

  const getTaskIcon = (type: PatientTask['taskType']) => {
    const IconComponent = taskTypeConfig[type]?.icon || IconFileText;
    return <IconComponent className="size-4" />;
  };

  const getAssignedToName = (assignedTo: User | string) => {
    if (typeof assignedTo === 'string') return '未分配';
    return `${assignedTo.firstName || ''} ${assignedTo.lastName || ''}`.trim() || assignedTo.email;
  };

  const getTasksByStatus = (status: PatientTask['status']) => {
    return filteredTasks.filter(task => task.status === status);
  };

  const handleStatusChange = (task: PatientTask, newStatus: PatientTask['status']) => {
    if (onTaskStatusChange) {
      onTaskStatusChange(task.id, newStatus);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconFileText className="size-5" />
            任务管理
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-start gap-3">
                  <div className="size-8 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const renderTaskCard = (task: PatientTask) => {
    const typeConfig = taskTypeConfig[task.taskType];
    const statusBadge = statusConfig[task.status];
    const priorityBadge = priorityConfig[task.priority];
    const overdue = isOverdue(task.dueDate) && task.status !== 'completed';

    return (
      <div
        key={task.id}
        className={cn(
          "p-4 rounded-lg border transition-colors",
          onTaskClick && "cursor-pointer hover:bg-muted/50",
          overdue && "border-red-200 bg-red-50"
        )}
        onClick={() => onTaskClick?.(task)}
      >
        <div className="flex items-start justify-between gap-2 mb-3">
          <div className="flex items-center gap-2">
            <div className={cn(
              "flex items-center justify-center size-6 rounded border",
              typeConfig?.color || 'bg-gray-100 text-gray-600 border-gray-200'
            )}>
              {getTaskIcon(task.taskType)}
            </div>
            <h4 className="font-medium text-sm leading-tight">
              {task.title}
            </h4>
          </div>
          {overdue && (
            <IconAlertTriangle className="size-4 text-red-500 flex-shrink-0" />
          )}
        </div>

        {task.description && (
          <p className="text-xs text-muted-foreground line-clamp-2 mb-3">
            {task.description}
          </p>
        )}

        <div className="flex items-center justify-between gap-2 mb-3">
          <div className="flex items-center gap-1">
            <Badge variant="outline" className={priorityBadge.color}>
              {priorityBadge.label}
            </Badge>
            <Badge variant="outline" className={statusBadge.color}>
              {statusBadge.label}
            </Badge>
          </div>
        </div>

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span className="flex items-center gap-1">
            <IconUser className="size-3" />
            {getAssignedToName(task.assignedTo)}
          </span>
          <span className={cn(
            "flex items-center gap-1",
            overdue && "text-red-600 font-medium"
          )}>
            <IconCalendar className="size-3" />
            {formatDateTime(new Date(task.dueDate))}
          </span>
        </div>

        {onTaskStatusChange && task.status !== 'completed' && (
          <div className="flex gap-1 mt-3">
            {task.status === 'pending' && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  handleStatusChange(task, 'in-progress');
                }}
                className="h-6 px-2 text-xs"
              >
                开始
              </Button>
            )}
            {task.status === 'in-progress' && (
              <Button
                size="sm"
                variant="outline"
                onClick={(e) => {
                  e.stopPropagation();
                  handleStatusChange(task, 'completed');
                }}
                className="h-6 px-2 text-xs"
              >
                完成
              </Button>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <IconFileText className="size-5" />
            任务管理
            <Badge variant="secondary" className="ml-2">
              {filteredTasks.length}
            </Badge>
          </CardTitle>
          {onCreateTask && (
            <Button onClick={onCreateTask} size="sm">
              <IconPlus className="size-4 mr-2" />
              创建任务
            </Button>
          )}
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-1">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
            <Input
              placeholder="搜索任务..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <IconFilter className="size-4 mr-2" />
              <SelectValue placeholder="类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              {Object.entries(taskTypeConfig).map(([value, config]) => (
                <SelectItem key={value} value={value}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={filterPriority} onValueChange={setFilterPriority}>
            <SelectTrigger className="w-full sm:w-[120px]">
              <SelectValue placeholder="优先级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有优先级</SelectItem>
              {Object.entries(priorityConfig).map(([value, config]) => (
                <SelectItem key={value} value={value}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {viewMode === 'kanban' ? (
          // Kanban View
          <Tabs defaultValue="pending" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              {Object.entries(statusConfig).map(([status, config]) => (
                <TabsTrigger key={status} value={status} className="text-xs">
                  {config.label} ({getTasksByStatus(status as PatientTask['status']).length})
                </TabsTrigger>
              ))}
            </TabsList>
            {Object.entries(statusConfig).map(([status, config]) => (
              <TabsContent key={status} value={status}>
                <ScrollArea className="h-[500px]">
                  <div className="space-y-3">
                    {getTasksByStatus(status as PatientTask['status']).map(renderTaskCard)}
                    {getTasksByStatus(status as PatientTask['status']).length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        <p>暂无{config.label}任务</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </TabsContent>
            ))}
          </Tabs>
        ) : (
          // List View
          <ScrollArea className="h-[600px]">
            {filteredTasks.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <IconFileText className="size-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">暂无任务</p>
                <p className="text-sm">创建任务来跟进患者事务</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredTasks.map(renderTaskCard)}
              </div>
            )}
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
