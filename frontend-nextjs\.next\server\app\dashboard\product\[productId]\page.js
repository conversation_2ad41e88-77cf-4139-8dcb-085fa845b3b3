try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="2a4ad7fe-90ff-471c-b14d-fa550462b84f",e._sentryDebugIdIdentifier="sentry-dbid-2a4ad7fe-90ff-471c-b14d-fa550462b84f")}catch(e){}(()=>{var e={};e.id=1674,e.ids=[1674],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3354:(e,a,t)=>{"use strict";t.d(a,{default:()=>i});let i=(0,t(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-form.tsx","default")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13788:(e,a,t)=>{"use strict";t.d(a,{default:()=>eT});var i=t(24443),n=(0,t(46244).A)("outline","upload","IconUpload",[["path",{d:"M4 17v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M7 9l5 -5l5 5",key:"svg-1"}],["path",{d:"M12 4l0 12",key:"svg-2"}]]),o=t(4826),p=t(31822),r=t(60222),l=t(71876),c=t(57707);let s=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function d(e,a,t){let i=function(e){let{name:a}=e;if(a&&-1!==a.lastIndexOf(".")&&!e.type){let t=a.split(".").pop().toLowerCase(),i=s.get(t);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,o="string"==typeof a?a:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&m(i,"path",o),void 0!==t&&Object.defineProperty(i,"handle",{value:t,writable:!1,configurable:!1,enumerable:!0}),m(i,"relativePath",o),i}function m(e,a,t){Object.defineProperty(e,a,{value:t,writable:!1,configurable:!1,enumerable:!0})}let u=[".DS_Store","Thumbs.db"];function x(e){return"object"==typeof e&&null!==e}function v(e){return e.filter(e=>-1===u.indexOf(e.name))}function f(e){if(null===e)return[];let a=[];for(let t=0;t<e.length;t++){let i=e[t];a.push(i)}return a}function g(e){if("function"!=typeof e.webkitGetAsEntry)return h(e);let a=e.webkitGetAsEntry();return a&&a.isDirectory?y(a):h(e,a)}function h(e,a){return(0,c.__awaiter)(this,void 0,void 0,function*(){var t;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let a=yield e.getAsFileSystemHandle();if(null===a)throw Error(`${e} is not a File`);if(void 0!==a){let e=yield a.getFile();return e.handle=a,d(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return d(i,null!=(t=null==a?void 0:a.fullPath)?t:void 0)})}function b(e){return(0,c.__awaiter)(this,void 0,void 0,function*(){return e.isDirectory?y(e):function(e){return(0,c.__awaiter)(this,void 0,void 0,function*(){return new Promise((a,t)=>{e.file(t=>{a(d(t,e.fullPath))},e=>{t(e)})})})}(e)})}function y(e){let a=e.createReader();return new Promise((e,t)=>{let i=[];!function n(){a.readEntries(a=>(0,c.__awaiter)(this,void 0,void 0,function*(){if(a.length){let e=Promise.all(a.map(b));i.push(e),n()}else try{let a=yield Promise.all(i);e(a)}catch(e){t(e)}}),e=>{t(e)})}()})}var w=t(20610);function j(e){return function(e){if(Array.isArray(e))return C(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||P(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function k(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,i)}return t}function z(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?k(Object(t),!0).forEach(function(a){D(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):k(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function D(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function q(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],p=!0,r=!1;try{for(n=n.call(e);!(p=(t=n.next()).done)&&(o.push(t.value),!a||o.length!==a);p=!0);}catch(e){r=!0,i=e}finally{try{p||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,a)||P(e,a)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(e,a){if(e){if("string"==typeof e)return C(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return C(e,a)}}function C(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,i=Array(a);t<a;t++)i[t]=e[t];return i}var F="function"==typeof w?w:w.default,O=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",a=e.split(","),t=a.length>1?"one of ".concat(a.join(", ")):a[0];return{code:"file-invalid-type",message:"File type must be ".concat(t)}},E=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},A=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},S={code:"too-many-files",message:"Too many files"};function I(e,a){var t="application/x-moz-file"===e.type||F(e,a);return[t,t?null:O(a)]}function _(e,a,t){if(N(e.size)){if(N(a)&&N(t)){if(e.size>t)return[!1,E(t)];if(e.size<a)return[!1,A(a)]}else if(N(a)&&e.size<a)return[!1,A(a)];else if(N(t)&&e.size>t)return[!1,E(t)]}return[!0,null]}function N(e){return null!=e}function R(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function M(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function T(e){e.preventDefault()}function $(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return a.some(function(a){return!R(e)&&a&&a.apply(void 0,[e].concat(i)),R(e)})}}function B(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function L(e){return/^.*\.[\w]+$/.test(e)}var U=["children"],G=["open"],K=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],H=["refKey","onChange","onClick"];function J(e,a){return function(e){if(Array.isArray(e))return e}(e)||function(e,a){var t,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],p=!0,r=!1;try{for(n=n.call(e);!(p=(t=n.next()).done)&&(o.push(t.value),!a||o.length!==a);p=!0);}catch(e){r=!0,i=e}finally{try{p||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,a)||V(e,a)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function V(e,a){if(e){if("string"==typeof e)return W(e,a);var t=Object.prototype.toString.call(e).slice(8,-1);if("Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t)return Array.from(e);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return W(e,a)}}function W(e,a){(null==a||a>e.length)&&(a=e.length);for(var t=0,i=Array(a);t<a;t++)i[t]=e[t];return i}function X(e,a){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);a&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,i)}return t}function Y(e){for(var a=1;a<arguments.length;a++){var t=null!=arguments[a]?arguments[a]:{};a%2?X(Object(t),!0).forEach(function(a){Z(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):X(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function Z(e,a,t){return a in e?Object.defineProperty(e,a,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[a]=t,e}function Q(e,a){if(null==e)return{};var t,i,n=function(e,a){if(null==e)return{};var t,i,n={},o=Object.keys(e);for(i=0;i<o.length;i++)t=o[i],a.indexOf(t)>=0||(n[t]=e[t]);return n}(e,a);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)t=o[i],!(a.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(n[t]=e[t])}return n}var ee=(0,r.forwardRef)(function(e,a){var t=e.children,i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=Y(Y({},ea),e),t=a.accept,i=a.disabled,n=a.getFilesFromEvent,o=a.maxSize,p=a.minSize,l=a.multiple,c=a.maxFiles,s=a.onDragEnter,d=a.onDragLeave,m=a.onDragOver,u=a.onDrop,x=a.onDropAccepted,v=a.onDropRejected,f=a.onFileDialogCancel,g=a.onFileDialogOpen,h=a.useFsAccessApi,b=a.autoFocus,y=a.preventDropOnDocument,w=a.noClick,k=a.noKeyboard,P=a.noDrag,C=a.noDragEventsBubbling,F=a.onError,O=a.validator,E=(0,r.useMemo)(function(){return N(t)?Object.entries(t).reduce(function(e,a){var t=q(a,2),i=t[0],n=t[1];return[].concat(j(e),[i],j(n))},[]).filter(function(e){return B(e)||L(e)}).join(","):void 0},[t]),A=(0,r.useMemo)(function(){return N(t)?[{description:"Files",accept:Object.entries(t).filter(function(e){var a=q(e,2),t=a[0],i=a[1],n=!0;return B(t)||(console.warn('Skipped "'.concat(t,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(L)||(console.warn('Skipped "'.concat(t,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,a){var t=q(a,2),i=t[0],n=t[1];return z(z({},e),{},D({},i,n))},{})}]:t},[t]),U=(0,r.useMemo)(function(){return"function"==typeof g?g:en},[g]),G=(0,r.useMemo)(function(){return"function"==typeof f?f:en},[f]),X=(0,r.useRef)(null),ee=(0,r.useRef)(null),eo=J((0,r.useReducer)(ei,et),2),ep=eo[0],er=eo[1],el=ep.isFocused,ec=ep.isFileDialogActive,es=(0,r.useRef)("undefined"!=typeof window&&window.isSecureContext&&h&&"showOpenFilePicker"in window),ed=function(){!es.current&&ec&&setTimeout(function(){ee.current&&(ee.current.files.length||(er({type:"closeDialog"}),G()))},300)};(0,r.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[ee,ec,G,es]);var em=(0,r.useRef)([]),eu=function(e){X.current&&X.current.contains(e.target)||(e.preventDefault(),em.current=[])};(0,r.useEffect)(function(){return y&&(document.addEventListener("dragover",T,!1),document.addEventListener("drop",eu,!1)),function(){y&&(document.removeEventListener("dragover",T),document.removeEventListener("drop",eu))}},[X,y]),(0,r.useEffect)(function(){return!i&&b&&X.current&&X.current.focus(),function(){}},[X,b,i]);var ex=(0,r.useCallback)(function(e){F?F(e):console.error(e)},[F]),ev=(0,r.useCallback)(function(e){var a;e.preventDefault(),e.persist(),eC(e),em.current=[].concat(function(e){if(Array.isArray(e))return W(e)}(a=em.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(a)||V(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),M(e)&&Promise.resolve(n(e)).then(function(a){if(!R(e)||C){var t,i,n,r,d,m,u,x,v=a.length,f=v>0&&(i=(t={files:a,accept:E,minSize:p,maxSize:o,multiple:l,maxFiles:c,validator:O}).files,n=t.accept,r=t.minSize,d=t.maxSize,m=t.multiple,u=t.maxFiles,x=t.validator,(!!m||!(i.length>1))&&(!m||!(u>=1)||!(i.length>u))&&i.every(function(e){var a=q(I(e,n),1)[0],t=q(_(e,r,d),1)[0],i=x?x(e):null;return a&&t&&!i}));er({isDragAccept:f,isDragReject:v>0&&!f,isDragActive:!0,type:"setDraggedFiles"}),s&&s(e)}}).catch(function(e){return ex(e)})},[n,s,ex,C,E,p,o,l,c,O]),ef=(0,r.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e);var a=M(e);if(a&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return a&&m&&m(e),!1},[m,C]),eg=(0,r.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e);var a=em.current.filter(function(e){return X.current&&X.current.contains(e)}),t=a.indexOf(e.target);-1!==t&&a.splice(t,1),em.current=a,!(a.length>0)&&(er({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),M(e)&&d&&d(e))},[X,d,C]),eh=(0,r.useCallback)(function(e,a){var t=[],i=[];e.forEach(function(e){var a=J(I(e,E),2),n=a[0],r=a[1],l=J(_(e,p,o),2),c=l[0],s=l[1],d=O?O(e):null;if(n&&c&&!d)t.push(e);else{var m=[r,s];d&&(m=m.concat(d)),i.push({file:e,errors:m.filter(function(e){return e})})}}),(!l&&t.length>1||l&&c>=1&&t.length>c)&&(t.forEach(function(e){i.push({file:e,errors:[S]})}),t.splice(0)),er({acceptedFiles:t,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),u&&u(t,i,a),i.length>0&&v&&v(i,a),t.length>0&&x&&x(t,a)},[er,l,E,p,o,c,u,x,v,O]),eb=(0,r.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e),em.current=[],M(e)&&Promise.resolve(n(e)).then(function(a){(!R(e)||C)&&eh(a,e)}).catch(function(e){return ex(e)}),er({type:"reset"})},[n,eh,ex,C]),ey=(0,r.useCallback)(function(){if(es.current){er({type:"openDialog"}),U(),window.showOpenFilePicker({multiple:l,types:A}).then(function(e){return n(e)}).then(function(e){eh(e,null),er({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(G(e),er({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(es.current=!1,ee.current?(ee.current.value=null,ee.current.click()):ex(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ex(e)});return}ee.current&&(er({type:"openDialog"}),U(),ee.current.value=null,ee.current.click())},[er,U,G,h,eh,ex,A,l]),ew=(0,r.useCallback)(function(e){X.current&&X.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ey())},[X,ey]),ej=(0,r.useCallback)(function(){er({type:"focus"})},[]),ek=(0,r.useCallback)(function(){er({type:"blur"})},[]),ez=(0,r.useCallback)(function(){w||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ey,0):ey())},[w,ey]),eD=function(e){return i?null:e},eq=function(e){return k?null:eD(e)},eP=function(e){return P?null:eD(e)},eC=function(e){C&&e.stopPropagation()},eF=(0,r.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.refKey,t=e.role,n=e.onKeyDown,o=e.onFocus,p=e.onBlur,r=e.onClick,l=e.onDragEnter,c=e.onDragOver,s=e.onDragLeave,d=e.onDrop,m=Q(e,K);return Y(Y(Z({onKeyDown:eq($(n,ew)),onFocus:eq($(o,ej)),onBlur:eq($(p,ek)),onClick:eD($(r,ez)),onDragEnter:eP($(l,ev)),onDragOver:eP($(c,ef)),onDragLeave:eP($(s,eg)),onDrop:eP($(d,eb)),role:"string"==typeof t&&""!==t?t:"presentation"},void 0===a?"ref":a,X),i||k?{}:{tabIndex:0}),m)}},[X,ew,ej,ek,ez,ev,ef,eg,eb,k,P,i]),eO=(0,r.useCallback)(function(e){e.stopPropagation()},[]),eE=(0,r.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=e.refKey,t=e.onChange,i=e.onClick,n=Q(e,H);return Y(Y({},Z({accept:E,multiple:l,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eD($(t,eb)),onClick:eD($(i,eO)),tabIndex:-1},void 0===a?"ref":a,ee)),n)}},[ee,t,l,eb,i]);return Y(Y({},ep),{},{isFocused:el&&!i,getRootProps:eF,getInputProps:eE,rootRef:X,inputRef:ee,open:eD(ey)})}(Q(e,U)),n=i.open,o=Q(i,G);return(0,r.useImperativeHandle)(a,function(){return{open:n}},[n]),r.createElement(r.Fragment,null,t(Y(Y({},o),{},{open:n})))});ee.displayName="Dropzone";var ea={disabled:!1,getFilesFromEvent:function(e){return(0,c.__awaiter)(this,void 0,void 0,function*(){var a;if(x(e)&&x(e.dataTransfer))return function(e,a){return(0,c.__awaiter)(this,void 0,void 0,function*(){if(e.items){let t=f(e.items).filter(e=>"file"===e.kind);return"drop"!==a?t:v(function e(a){return a.reduce((a,t)=>[...a,...Array.isArray(t)?e(t):[t]],[])}((yield Promise.all(t.map(g)))))}return v(f(e.files).map(e=>d(e)))})}(e.dataTransfer,e.type);if(x(a=e)&&x(a.target))return f(e.target.files).map(e=>d(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return(0,c.__awaiter)(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>d(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};ee.defaultProps=ea,ee.propTypes={children:l.func,accept:l.objectOf(l.arrayOf(l.string)),multiple:l.bool,preventDropOnDocument:l.bool,noClick:l.bool,noKeyboard:l.bool,noDrag:l.bool,noDragEventsBubbling:l.bool,minSize:l.number,maxSize:l.number,maxFiles:l.number,disabled:l.bool,getFilesFromEvent:l.func,onFileDialogCancel:l.func,onFileDialogOpen:l.func,useFsAccessApi:l.bool,autoFocus:l.bool,onDragEnter:l.func,onDragLeave:l.func,onDragOver:l.func,onDrop:l.func,onDropAccepted:l.func,onDropRejected:l.func,onError:l.func,validator:l.func};var et={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ei(e,a){switch(a.type){case"focus":return Y(Y({},e),{},{isFocused:!0});case"blur":return Y(Y({},e),{},{isFocused:!1});case"openDialog":return Y(Y({},et),{},{isFileDialogActive:!0});case"closeDialog":return Y(Y({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return Y(Y({},e),{},{isDragActive:a.isDragActive,isDragAccept:a.isDragAccept,isDragReject:a.isDragReject});case"setFiles":return Y(Y({},e),{},{acceptedFiles:a.acceptedFiles,fileRejections:a.fileRejections,isDragReject:a.isDragReject});case"reset":return Y({},et);default:return e}}function en(){}var eo=t(85001),ep=t(33284),er=t(4684),el=t(24582),ec="Progress",[es,ed]=(0,er.A)(ec),[em,eu]=es(ec),ex=r.forwardRef((e,a)=>{var t,n;let{__scopeProgress:o,value:p=null,max:r,getValueLabel:l=eg,...c}=e;(r||0===r)&&!ey(r)&&console.error((t=`${r}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let s=ey(r)?r:100;null===p||ew(p,s)||console.error((n=`${p}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let d=ew(p,s)?p:null,m=eb(d)?l(d,s):void 0;return(0,i.jsx)(em,{scope:o,value:d,max:s,children:(0,i.jsx)(el.sG.div,{"aria-valuemax":s,"aria-valuemin":0,"aria-valuenow":eb(d)?d:void 0,"aria-valuetext":m,role:"progressbar","data-state":eh(d,s),"data-value":d??void 0,"data-max":s,...c,ref:a})})});ex.displayName=ec;var ev="ProgressIndicator",ef=r.forwardRef((e,a)=>{let{__scopeProgress:t,...n}=e,o=eu(ev,t);return(0,i.jsx)(el.sG.div,{"data-state":eh(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...n,ref:a})});function eg(e,a){return`${Math.round(e/a*100)}%`}function eh(e,a){return null==e?"indeterminate":e===a?"complete":"loading"}function eb(e){return"number"==typeof e}function ey(e){return eb(e)&&!isNaN(e)&&e>0}function ew(e,a){return eb(e)&&!isNaN(e)&&e<=a&&e>=0}ef.displayName=ev;var ej=t(72595);function ek({className:e,value:a,...t}){return(0,i.jsx)(ex,{"data-slot":"progress",className:(0,ej.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...t,"data-sentry-element":"ProgressPrimitive.Root","data-sentry-component":"Progress","data-sentry-source-file":"progress.tsx",children:(0,i.jsx)(ef,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(a||0)}%)`},"data-sentry-element":"ProgressPrimitive.Indicator","data-sentry-source-file":"progress.tsx"})})}var ez=t(67529),eD=t(17880);function eq(e){let{value:a,onValueChange:t,onUpload:o,progresses:p,accept:l={"image/*":[]},maxSize:c=2097152,maxFiles:s=1,multiple:d=!1,disabled:m=!1,className:u,...x}=e,[v,f]=function({prop:e,defaultProp:a,onChange:t=()=>{}}){let[i,n]=function({defaultProp:e,onChange:a}){let t=r.useState(e),[i]=t,n=r.useRef(i),o=(0,eD.c)(a);return r.useEffect(()=>{n.current!==i&&(o(i),n.current=i)},[i,n,o]),t}({defaultProp:a,onChange:t}),o=void 0!==e,p=o?e:i,l=(0,eD.c)(t);return[p,r.useCallback(a=>{if(o){let t="function"==typeof a?a(e):a;t!==e&&l(t)}else n(a)},[o,e,n,l])]}({prop:a,onChange:t}),g=r.useCallback((e,a)=>{if(!d&&1===s&&e.length>1)return void eo.toast.error("Cannot upload more than 1 file at a time");if((v?.length??0)+e.length>s)return void eo.toast.error(`Cannot upload more than ${s} files`);let t=e.map(e=>Object.assign(e,{preview:URL.createObjectURL(e)})),i=v?[...v,...t]:t;if(f(i),a.length>0&&a.forEach(({file:e})=>{eo.toast.error(`File ${e.name} was rejected`)}),o&&i.length>0&&i.length<=s){let e=i.length>0?`${i.length} files`:"file";eo.toast.promise(o(i),{loading:`Uploading ${e}...`,success:()=>(f([]),`${e} uploaded`),error:`Failed to upload ${e}`})}},[v,s,d,o,f]);r.useEffect(()=>()=>{v&&v.forEach(e=>{eC(e)&&URL.revokeObjectURL(e.preview)})},[]);let h=m||(v?.length??0)>=s;return(0,i.jsxs)("div",{className:"relative flex flex-col gap-6 overflow-hidden","data-sentry-component":"FileUploader","data-sentry-source-file":"file-uploader.tsx",children:[(0,i.jsx)(ee,{onDrop:g,accept:l,maxSize:c,maxFiles:s,multiple:s>1||d,disabled:h,"data-sentry-element":"Dropzone","data-sentry-source-file":"file-uploader.tsx",children:({getRootProps:e,getInputProps:a,isDragActive:t})=>(0,i.jsxs)("div",{...e(),className:(0,ej.cn)("group border-muted-foreground/25 hover:bg-muted/25 relative grid h-52 w-full cursor-pointer place-items-center rounded-lg border-2 border-dashed px-5 py-2.5 text-center transition","ring-offset-background focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden",t&&"border-muted-foreground/50",h&&"pointer-events-none opacity-60",u),...x,children:[(0,i.jsx)("input",{...a()}),t?(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[(0,i.jsx)("div",{className:"rounded-full border border-dashed p-3",children:(0,i.jsx)(n,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),(0,i.jsx)("p",{className:"text-muted-foreground font-medium",children:"Drop the files here"})]}):(0,i.jsxs)("div",{className:"flex flex-col items-center justify-center gap-4 sm:px-5",children:[(0,i.jsx)("div",{className:"rounded-full border border-dashed p-3",children:(0,i.jsx)(n,{className:"text-muted-foreground size-7","aria-hidden":"true"})}),(0,i.jsxs)("div",{className:"space-y-px",children:[(0,i.jsxs)("p",{className:"text-muted-foreground font-medium",children:["Drag ","'n'"," drop files here, or click to select files"]}),(0,i.jsxs)("p",{className:"text-muted-foreground/70 text-sm",children:["You can upload",s>1?` ${s===1/0?"multiple":s}
                      files (up to ${(0,ej.z3)(c)} each)`:` a file with ${(0,ej.z3)(c)}`]})]})]})]})}),v?.length?(0,i.jsx)(ez.ScrollArea,{className:"h-fit w-full px-3",children:(0,i.jsx)("div",{className:"max-h-48 space-y-4",children:v?.map((e,a)=>(0,i.jsx)(eP,{file:e,onRemove:()=>(function(e){if(!v)return;let a=v.filter((a,t)=>t!==e);f(a),t?.(a)})(a),progress:p?.[e.name]},a))})}):null]})}function eP({file:e,progress:a,onRemove:t}){return(0,i.jsxs)("div",{className:"relative flex items-center space-x-4","data-sentry-component":"FileCard","data-sentry-source-file":"file-uploader.tsx",children:[(0,i.jsxs)("div",{className:"flex flex-1 space-x-4",children:[eC(e)?(0,i.jsx)(p.default,{src:e.preview,alt:e.name,width:48,height:48,loading:"lazy",className:"aspect-square shrink-0 rounded-md object-cover"}):null,(0,i.jsxs)("div",{className:"flex w-full flex-col gap-2",children:[(0,i.jsxs)("div",{className:"space-y-px",children:[(0,i.jsx)("p",{className:"text-foreground/80 line-clamp-1 text-sm font-medium",children:e.name}),(0,i.jsx)("p",{className:"text-muted-foreground text-xs",children:(0,ej.z3)(e.size)})]}),a?(0,i.jsx)(ek,{value:a}):null]})]}),(0,i.jsx)("div",{className:"flex items-center gap-2",children:(0,i.jsxs)(ep.$,{type:"button",variant:"ghost",size:"icon",onClick:t,disabled:void 0!==a&&a<100,className:"size-8 rounded-full","data-sentry-element":"Button","data-sentry-source-file":"file-uploader.tsx",children:[(0,i.jsx)(o.A,{className:"text-muted-foreground","data-sentry-element":"IconX","data-sentry-source-file":"file-uploader.tsx"}),(0,i.jsx)("span",{className:"sr-only",children:"Remove file"})]})})]})}function eC(e){return"preview"in e&&"string"==typeof e.preview}var eF=t(32218),eO=t(26882),eE=t(19342),eA=t(23032),eS=t(81646),eI=t(80395),e_=t(95550),eN=t(13875);let eR=["image/jpeg","image/jpg","image/png","image/webp"],eM=eN.Ik({image:eN.bz().refine(e=>e?.length==1,"Image is required.").refine(e=>e?.[0]?.size<=5e6,"Max file size is 5MB.").refine(e=>eR.includes(e?.[0]?.type),".jpg, .jpeg, .png and .webp files are accepted."),name:eN.Yj().min(2,{message:"Product name must be at least 2 characters."}),category:eN.Yj(),price:eN.ai(),description:eN.Yj().min(10,{message:"Description must be at least 10 characters."})});function eT({initialData:e,pageTitle:a}){let t={name:e?.name||"",category:e?.category||"",price:e?.price||0,description:e?.description||""},n=(0,e_.mN)({resolver:(0,eI.u)(eM),values:t});return(0,i.jsxs)(eF.Zp,{className:"mx-auto w-full","data-sentry-element":"Card","data-sentry-component":"ProductForm","data-sentry-source-file":"product-form.tsx",children:[(0,i.jsx)(eF.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"product-form.tsx",children:(0,i.jsx)(eF.ZB,{className:"text-left text-2xl font-bold","data-sentry-element":"CardTitle","data-sentry-source-file":"product-form.tsx",children:a})}),(0,i.jsx)(eF.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"product-form.tsx",children:(0,i.jsx)(eO.lV,{...n,"data-sentry-element":"Form","data-sentry-source-file":"product-form.tsx",children:(0,i.jsxs)("form",{onSubmit:n.handleSubmit(function(e){}),className:"space-y-8",children:[(0,i.jsx)(eO.zB,{control:n.control,name:"image",render:({field:e})=>(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)(eO.eI,{className:"w-full",children:[(0,i.jsx)(eO.lR,{children:"Images"}),(0,i.jsx)(eO.MJ,{children:(0,i.jsx)(eq,{value:e.value,onValueChange:e.onChange,maxFiles:4,maxSize:4194304})}),(0,i.jsx)(eO.C5,{})]})}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[(0,i.jsx)(eO.zB,{control:n.control,name:"name",render:({field:e})=>(0,i.jsxs)(eO.eI,{children:[(0,i.jsx)(eO.lR,{children:"Product Name"}),(0,i.jsx)(eO.MJ,{children:(0,i.jsx)(eE.p,{placeholder:"Enter product name",...e})}),(0,i.jsx)(eO.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsx)(eO.zB,{control:n.control,name:"category",render:({field:e})=>(0,i.jsxs)(eO.eI,{children:[(0,i.jsx)(eO.lR,{children:"Category"}),(0,i.jsxs)(eA.l6,{onValueChange:a=>e.onChange(a),value:e.value[e.value.length-1],children:[(0,i.jsx)(eO.MJ,{children:(0,i.jsx)(eA.bq,{children:(0,i.jsx)(eA.yv,{placeholder:"Select categories"})})}),(0,i.jsxs)(eA.gC,{children:[(0,i.jsx)(eA.eb,{value:"beauty",children:"Beauty Products"}),(0,i.jsx)(eA.eb,{value:"electronics",children:"Electronics"}),(0,i.jsx)(eA.eb,{value:"clothing",children:"Clothing"}),(0,i.jsx)(eA.eb,{value:"home",children:"Home & Garden"}),(0,i.jsx)(eA.eb,{value:"sports",children:"Sports & Outdoors"})]})]}),(0,i.jsx)(eO.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsx)(eO.zB,{control:n.control,name:"price",render:({field:e})=>(0,i.jsxs)(eO.eI,{children:[(0,i.jsx)(eO.lR,{children:"Price"}),(0,i.jsx)(eO.MJ,{children:(0,i.jsx)(eE.p,{type:"number",step:"0.01",placeholder:"Enter price",...e})}),(0,i.jsx)(eO.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"})]}),(0,i.jsx)(eO.zB,{control:n.control,name:"description",render:({field:e})=>(0,i.jsxs)(eO.eI,{children:[(0,i.jsx)(eO.lR,{children:"Description"}),(0,i.jsx)(eO.MJ,{children:(0,i.jsx)(eS.T,{placeholder:"Enter product description",className:"resize-none",...e})}),(0,i.jsx)(eO.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"product-form.tsx"}),(0,i.jsx)(ep.$,{type:"submit","data-sentry-element":"Button","data-sentry-source-file":"product-form.tsx",children:"Add Product"})]})})})]})}},17880:(e,a,t)=>{"use strict";t.d(a,{c:()=>n});var i=t(60222);function n(e){let a=i.useRef(e);return i.useEffect(()=>{a.current=e}),i.useMemo(()=>(...e)=>a.current?.(...e),[])}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20610:(e,a)=>{"use strict";a.__esModule=!0,a.default=function(e,a){if(e&&a){var t=Array.isArray(a)?a:a.split(",");if(0===t.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),o=n.replace(/\/.*$/,"");return t.some(function(e){var a=e.trim().toLowerCase();return"."===a.charAt(0)?i.toLowerCase().endsWith(a):a.endsWith("/*")?o===a.replace(/\/.*$/,""):n===a})}return!0}},21820:e=>{"use strict";e.exports=require("os")},23907:(e,a,t)=>{Promise.resolve().then(t.bind(t,67529)),Promise.resolve().then(t.bind(t,13788))},26882:(e,a,t)=>{"use strict";t.d(a,{C5:()=>h,MJ:()=>f,Rr:()=>g,eI:()=>x,lR:()=>v,lV:()=>c,zB:()=>d});var i=t(24443),n=t(60222),o=t(16586),p=t(95550),r=t(72595),l=t(18984);let c=p.Op,s=n.createContext({}),d=({...e})=>(0,i.jsx)(s.Provider,{value:{name:e.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,i.jsx)(p.xI,{...e,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})}),m=()=>{let e=n.useContext(s),a=n.useContext(u),{getFieldState:t}=(0,p.xW)(),i=(0,p.lN)({name:e.name}),o=t(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:r}=a;return{id:r,name:e.name,formItemId:`${r}-form-item`,formDescriptionId:`${r}-form-item-description`,formMessageId:`${r}-form-item-message`,...o}},u=n.createContext({});function x({className:e,...a}){let t=n.useId();return(0,i.jsx)(u.Provider,{value:{id:t},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,i.jsx)("div",{"data-slot":"form-item",className:(0,r.cn)("grid gap-2",e),...a})})}function v({className:e,...a}){let{error:t,formItemId:n}=m();return(0,i.jsx)(l.J,{"data-slot":"form-label","data-error":!!t,className:(0,r.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...a,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function f({...e}){let{error:a,formItemId:t,formDescriptionId:n,formMessageId:p}=m();return(0,i.jsx)(o.DX,{"data-slot":"form-control",id:t,"aria-describedby":a?`${n} ${p}`:`${n}`,"aria-invalid":!!a,...e,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function g({className:e,...a}){let{formDescriptionId:t}=m();return(0,i.jsx)("p",{"data-slot":"form-description",id:t,className:(0,r.cn)("text-muted-foreground text-sm",e),...a,"data-sentry-component":"FormDescription","data-sentry-source-file":"form.tsx"})}function h({className:e,...a}){let{error:t,formMessageId:n}=m(),o=t?String(t?.message??""):a.children;return o?(0,i.jsx)("p",{"data-slot":"form-message",id:n,className:(0,r.cn)("text-destructive text-sm",e),...a,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:o}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32218:(e,a,t)=>{"use strict";t.d(a,{BT:()=>l,Wu:()=>s,X9:()=>c,ZB:()=>r,Zp:()=>o,aR:()=>p,wL:()=>d});var i=t(24443);t(60222);var n=t(72595);function o({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function p({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function r({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function l({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function c({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...a,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function s({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function d({className:e,...a}){return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},47883:(e,a,t)=>{Promise.resolve().then(t.bind(t,89371)),Promise.resolve().then(t.bind(t,3354))},47923:(e,a,t)=>{"use strict";t.d(a,{c:()=>o,g:()=>p});var i=t(36637),n=t(3944);let o=e=>new Promise(a=>setTimeout(a,e)),p={records:[],initialize(){let e=[];for(let t=1;t<=20;t++){var a;e.push({id:a=t,name:i.a.commerce.productName(),description:i.a.commerce.productDescription(),created_at:i.a.date.between({from:"2022-01-01",to:"2023-12-31"}).toISOString(),price:parseFloat(i.a.commerce.price({min:5,max:500,dec:2})),photo_url:`https://api.slingacademy.com/public/sample-products/${a}.png`,category:i.a.helpers.arrayElement(["Electronics","Furniture","Clothing","Toys","Groceries","Books","Jewelry","Beauty Products"]),updated_at:i.a.date.recent().toISOString()})}this.records=e},async getAll({categories:e=[],search:a}){let t=[...this.records];return e.length>0&&(t=t.filter(a=>e.includes(a.category))),a&&(t=(0,n.Ht)(t,a,{keys:["name","description","category"]})),t},async getProducts({page:e=1,limit:a=10,categories:t,search:i}){await o(1e3);let n=t?t.split("."):[],p=await this.getAll({categories:n,search:i}),r=p.length,l=(e-1)*a,c=p.slice(l,l+a);return{success:!0,time:new Date().toISOString(),message:"Sample data for testing and learning purposes",total_products:r,offset:l,limit:a,products:c}},async getProductById(e){await o(1e3);let a=this.records.find(a=>a.id===e);return a?{success:!0,time:new Date().toISOString(),message:`Product with ID ${e} found`,product:a}:{success:!1,message:`Product with ID ${e} not found`}}};p.initialize()},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66881:(e,a,t)=>{"use strict";let i;t.r(a),t.d(a,{default:()=>w,generateImageMetadata:()=>b,generateMetadata:()=>h,generateViewport:()=>y,metadata:()=>x});var n=t(63033),o=t(78869),p=t(18124),r=t(83829),l=t(22576),c=t(47923),s=t(44508),d=t(3354);async function m({productId:e}){let a=null,t="Create New Product";return"new"!==e&&((a=(await c.g.getProductById(Number(e))).product)||(0,s.notFound)(),t="Edit Product"),(0,o.jsx)(d.default,{initialData:a,pageTitle:t,"data-sentry-element":"ProductForm","data-sentry-component":"ProductViewPage","data-sentry-source-file":"product-view-page.tsx"})}var u=t(19761);let x={title:"Dashboard : Product View"};async function v(e){let a=await e.params;return(0,o.jsx)(r.A,{scrollable:!0,"data-sentry-element":"PageContainer","data-sentry-component":"Page","data-sentry-source-file":"page.tsx",children:(0,o.jsx)("div",{className:"flex-1 space-y-4",children:(0,o.jsx)(l.Suspense,{fallback:(0,o.jsx)(p.A,{}),"data-sentry-element":"Suspense","data-sentry-source-file":"page.tsx",children:(0,o.jsx)(m,{productId:a.productId,"data-sentry-element":"ProductViewPage","data-sentry-source-file":"page.tsx"})})})})}let f={...n},g="workUnitAsyncStorage"in f?f.workUnitAsyncStorage:"requestAsyncStorage"in f?f.requestAsyncStorage:void 0;i=new Proxy(v,{apply:(e,a,t)=>{let i,n,o;try{let e=g?.getStore();i=e?.headers.get("sentry-trace")??void 0,n=e?.headers.get("baggage")??void 0,o=e?.headers}catch(e){}return u.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/product/[productId]",componentType:"Page",sentryTraceHeader:i,baggageHeader:n,headers:o}).apply(a,t)}});let h=void 0,b=void 0,y=void 0,w=i},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},86922:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>o.default,__next_app__:()=>s,pages:()=>c,routeModule:()=>d,tree:()=>l});var i=t(29703),n=t(85544),o=t(62458),p=t(77821),r={};for(let e in p)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(r[e]=()=>p[e]);t.d(a,r);let l={children:["",{children:["dashboard",{children:["product",{children:["[productId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,66881)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\[productId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\[productId]\\page.tsx"],s={require:t,loadChunk:()=>Promise.resolve()},d=new i.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/product/[productId]/page",pathname:"/dashboard/product/[productId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),i=a.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,3875,3408,395,1822,8774,7494,3566],()=>t(86922));module.exports=i})();
//# sourceMappingURL=page.js.map