import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { paymentFormSchema } from '@/lib/validation/billing-schemas';
import { formatValidationErrors } from '@/lib/validation/billing-schemas';
import { validateBusinessRules } from '@/lib/validation/validation-utils';
import { auditLogger, rateLimiter, InputSanitizer } from '@/lib/billing-security';

const BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';

// Enhanced error handling utility
class APIError extends Error {
  constructor(
    message: string,
    public status: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Utility to get user info from Clerk with error handling
async function getClerkUserInfo(userId: string) {
  try {
    const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
      },
    });

    if (!response.ok) {
      throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching Clerk user:', error);
    throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');
  }
}

// Utility to make backend requests with comprehensive error handling
async function makeBackendRequest(url: string, options: RequestInit, userId: string, userEmail: string) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'x-clerk-user-id': userId,
        'x-user-email': userEmail,
        ...options.headers,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      throw new APIError(
        data.error || `Backend request failed: ${response.status}`,
        response.status,
        data.code || 'BACKEND_ERROR',
        data
      );
    }

    return data;
  } catch (error) {
    if (error instanceof APIError) {
      throw error;
    }

    console.error('Backend request error:', error);
    throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');
  }
}

/**
 * GET /api/payments - Proxy to backend payments API
 */
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user info from Clerk
    const user = await fetch(`https://api.clerk.com/v1/users/${userId}`, {
      headers: {
        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,
      },
    }).then(res => res.json());

    // Forward request to backend with authentication headers
    const url = new URL(request.url);
    const backendUrl = `${BACKEND_URL}/api/payments${url.search}`;
    
    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-clerk-user-id': userId,
        'x-user-email': user.email_addresses[0]?.email_address || '',
      },
    });

    const data = await response.json();
    
    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error proxying payments request:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payments - Process payment with comprehensive validation and business logic
 */
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      auditLogger.logFinancialOperation(
        'anonymous',
        'anonymous',
        'CREATE_PAYMENT_UNAUTHORIZED',
        'payments',
        { endpoint: '/api/payments' },
        false,
        'Authentication required',
        request
      );

      return NextResponse.json(
        {
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          message: '请先登录以处理支付'
        },
        { status: 401 }
      );
    }

    // Check rate limiting for payment requests (more restrictive)
    const rateLimitResult = rateLimiter.checkRateLimit(userId, true);
    if (!rateLimitResult.allowed) {
      auditLogger.logFinancialOperation(
        userId,
        'unknown',
        'CREATE_PAYMENT_RATE_LIMITED',
        'payments',
        { endpoint: '/api/payments', resetTime: rateLimitResult.resetTime },
        false,
        'Payment rate limit exceeded',
        request
      );

      return NextResponse.json(
        {
          error: 'Payment rate limit exceeded',
          code: 'PAYMENT_RATE_LIMIT_EXCEEDED',
          message: '支付请求过于频繁，请稍后重试',
          resetTime: rateLimitResult.resetTime
        },
        { status: 429 }
      );
    }

    // Get user info from Clerk with error handling early
    const user = await getClerkUserInfo(userId);
    const userEmail = user.email_addresses[0]?.email_address || '';

    if (!userEmail) {
      return NextResponse.json(
        {
          error: 'User email not found',
          code: 'USER_EMAIL_MISSING',
          message: '用户邮箱信息缺失，请联系管理员'
        },
        { status: 400 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      auditLogger.logFinancialOperation(
        userId,
        userEmail,
        'CREATE_PAYMENT_JSON_PARSE_FAILED',
        'payments',
        { error },
        false,
        'Failed to parse JSON request body',
        request
      );

      return NextResponse.json(
        {
          error: 'Invalid JSON in request body',
          code: 'INVALID_JSON',
          message: '请求数据格式错误'
        },
        { status: 400 }
      );
    }

    // Sanitize payment input data
    try {
      body.amount = InputSanitizer.sanitizeAmount(body.amount);
      body.paymentMethod = InputSanitizer.sanitizePaymentMethod(body.paymentMethod);

      if (body.transactionId) {
        body.transactionId = InputSanitizer.sanitizeText(body.transactionId, 100);
      }
      if (body.notes) {
        body.notes = InputSanitizer.sanitizeText(body.notes, 500);
      }
    } catch (sanitizationError) {
      auditLogger.logFinancialOperation(
        userId,
        userEmail,
        'CREATE_PAYMENT_SANITIZATION_FAILED',
        'payments',
        { error: sanitizationError },
        false,
        sanitizationError instanceof Error ? sanitizationError.message : 'Payment sanitization failed',
        request
      );

      return NextResponse.json(
        {
          error: 'Input sanitization failed',
          code: 'SANITIZATION_ERROR',
          message: '支付数据格式不正确'
        },
        { status: 400 }
      );
    }

    // Validate payment data using Zod schema
    const validationResult = paymentFormSchema.safeParse(body);
    if (!validationResult.success) {
      const formattedErrors = formatValidationErrors(validationResult.error);

      auditLogger.logFinancialOperation(
        userId,
        userEmail,
        'CREATE_PAYMENT_VALIDATION_FAILED',
        'payments',
        { validationErrors: formattedErrors },
        false,
        'Payment validation failed',
        request
      );

      return NextResponse.json(
        {
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          message: '支付数据验证失败',
          details: formattedErrors
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // If bill ID is provided, validate payment amount against bill
    if (body.billId) {
      try {
        // Fetch bill details to validate payment amount
        const billUrl = `${BACKEND_URL}/api/bills/${body.billId}`;
        const bill = await makeBackendRequest(billUrl, { method: 'GET' }, userId, userEmail);

        // Validate payment amount against remaining bill amount
        const paymentValidation = validateBusinessRules.isValidPaymentAmount(validatedData.amount, bill);
        if (!paymentValidation.valid) {
          return NextResponse.json(
            {
              error: 'Invalid payment amount',
              code: 'INVALID_PAYMENT_AMOUNT',
              message: paymentValidation.reason
            },
            { status: 400 }
          );
        }
      } catch (error) {
        if (error instanceof APIError && error.status === 404) {
          return NextResponse.json(
            {
              error: 'Bill not found',
              code: 'BILL_NOT_FOUND',
              message: '指定的账单不存在'
            },
            { status: 404 }
          );
        }
        throw error; // Re-throw other errors
      }
    }

    // Additional business logic validation for payment methods
    const paymentMethodValidation = validatePaymentMethod(validatedData);
    if (!paymentMethodValidation.valid) {
      return NextResponse.json(
        {
          error: 'Payment method validation failed',
          code: 'PAYMENT_METHOD_ERROR',
          message: paymentMethodValidation.reason
        },
        { status: 400 }
      );
    }

    // Forward request to backend with authentication headers
    const backendUrl = `${BACKEND_URL}/api/payments`;
    const data = await makeBackendRequest(
      backendUrl,
      {
        method: 'POST',
        body: JSON.stringify(validatedData)
      },
      userId,
      userEmail
    );

    // Log successful payment creation (mask sensitive data)
    auditLogger.logFinancialOperation(
      userId,
      userEmail,
      'CREATE_PAYMENT',
      'payments',
      {
        paymentId: data.id,
        billId: body.billId,
        amount: validatedData.amount,
        paymentMethod: validatedData.paymentMethod,
        transactionId: validatedData.transactionId, // Will be masked by audit logger
        patientId: body.patientId
      },
      true,
      undefined,
      request
    );

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    if (error instanceof APIError) {
      return NextResponse.json(
        {
          error: error.message,
          code: error.code,
          message: error.message,
          details: error.details
        },
        { status: error.status }
      );
    }

    console.error('Unexpected error in POST /api/payments:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
        message: '处理支付时发生服务器错误，请稍后重试'
      },
      { status: 500 }
    );
  }
}

// Additional payment method validation
function validatePaymentMethod(paymentData: any): { valid: boolean; reason?: string } {
  const { paymentMethod, transactionId, amount } = paymentData;

  // Validate transaction ID requirements for different payment methods
  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];
  if (methodsRequiringTransactionId.includes(paymentMethod)) {
    if (!transactionId || transactionId.trim().length === 0) {
      return {
        valid: false,
        reason: `${getPaymentMethodName(paymentMethod)}需要提供交易ID`
      };
    }
  }

  // Validate amount limits for different payment methods
  if (paymentMethod === 'cash' && amount > 50000) {
    return {
      valid: false,
      reason: '现金支付单笔金额不能超过50,000元'
    };
  }

  if (paymentMethod === 'wechat' && amount > 200000) {
    return {
      valid: false,
      reason: '微信支付单笔金额不能超过200,000元'
    };
  }

  if (paymentMethod === 'alipay' && amount > 200000) {
    return {
      valid: false,
      reason: '支付宝单笔金额不能超过200,000元'
    };
  }

  return { valid: true };
}

// Helper function to get payment method display name
function getPaymentMethodName(method: string): string {
  const methods: Record<string, string> = {
    cash: '现金',
    card: '银行卡',
    wechat: '微信支付',
    alipay: '支付宝',
    transfer: '银行转账',
    installment: '分期付款',
  };
  return methods[method] || method;
}
