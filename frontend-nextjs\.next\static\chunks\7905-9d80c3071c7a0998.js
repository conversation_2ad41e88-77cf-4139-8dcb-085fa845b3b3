try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="70532fde-7488-48cf-8fd2-61e0df11e23b",e._sentryDebugIdIdentifier="sentry-dbid-70532fde-7488-48cf-8fd2-61e0df11e23b")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7905],{6281:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>l,Vc:()=>a,gE:()=>n,iM:()=>i,mG:()=>o,ub:()=>s});var i=[".lcl.dev",".lclstage.dev",".lclclerk.com"],n=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],o=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],s=[".accountsstage.dev"],a="https://api.lclclerk.com",l="https://api.clerkstage.dev",u="https://api.clerk.com"},8751:(e,t,r)=>{e.exports=r(44970)},15659:(e,t,r)=>{r.d(t,{Fj:()=>o,MC:()=>n,b_:()=>i});var i=()=>!1,n=()=>!1,o=()=>{try{return!0}catch{}return!1}},21730:(e,t,r)=>{function i(){return"undefined"!=typeof window}r.d(t,{M:()=>i}),RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i"),r(73967)},26980:(e,t,r)=>{r.d(t,{VK:()=>o,b_:()=>i.b_,Fj:()=>i.Fj,s2:()=>n});var i=r(15659),n=e=>{(0,i.b_)()&&console.error(`Clerk: ${e}`)};function o(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}r(73967)},27332:(e,t,r)=>{r.d(t,{zz:()=>n});var i=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let i={...r};for(let r of Object.keys(i)){let n=e(r.toString());n!==r&&(i[n]=i[r],delete i[r]),"object"==typeof i[n]&&(i[n]=t(i[n]))}return i};return t};function n(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}i(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),i(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},31257:(e,t,r)=>{r.d(t,{D:()=>c,M:()=>h});var i={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},n=new Set(["first_factor","second_factor","multi_factor"]),o=new Set(["strict_mfa","strict","moderate","lax"]),s=e=>"number"==typeof e&&e>0,a=e=>n.has(e),l=e=>o.has(e),u=(e,t)=>{let{orgId:r,orgRole:i,orgPermissions:n}=t;return(e.role||e.permission)&&r&&i&&n?e.permission?n.includes(e.permission):e.role?i===e.role:null:null},c=e=>{if(!e)return!1;let t="string"==typeof e&&l(e),r="object"==typeof e&&a(e.level)&&s(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?i[e]:e).bind(null,e)},d=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=c(e.reverification);if(!r)return null;let{level:i,afterMinutes:n}=r(),[o,s]=t,a=-1!==o?n>o:null,l=-1!==s?n>s:null;switch(i){case"first_factor":return a;case"second_factor":return -1!==s?l:a;case"multi_factor":return -1===s?a:a&&l}},h=e=>t=>{if(!e.userId)return!1;let r=u(t,e),i=d(t,e);return[r,i].some(e=>null===e)?[r,i].some(e=>!0===e):[r,i].every(e=>!0===e)}},33116:(e,t,r)=>{r.d(t,{cy:()=>p,B$:()=>er,z0:()=>G,A0:()=>Q,SW:()=>f,EH:()=>Z,rm:()=>et,m2:()=>ee,W5:()=>H,mO:()=>Y,eG:()=>X,iB:()=>J,Bl:()=>q,D:()=>C,wm:()=>M,sR:()=>O,n:()=>P,sb:()=>d,s7:()=>E,Wq:()=>k,yN:()=>v,kd:()=>j,kf:()=>_,vb:()=>S,wV:()=>h,Vo:()=>b,As:()=>N,ho:()=>s.ho,hP:()=>F,ui:()=>B,Z5:()=>s.Z5,D_:()=>s.D_,Wp:()=>s.Wp,dy:()=>s.wV,g7:()=>s.g7,go:()=>V,yC:()=>$,Jd:()=>s.Jd,P6:()=>U,aU:()=>I,ld:()=>W,Wv:()=>R,UX:()=>z,Uw:()=>L,_I:()=>w,$n:()=>y,Q:()=>K});var i=r(50323),n=r(31257);r(73967);var o=r(99004),s=r(76781),a=r(40940),l=r(15659),u=new Set,c=(e,t,r)=>{let i=(0,l.MC)()||(0,l.Fj)(),n=r??e;u.has(n)||i||(u.add(n),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))},d=(0,i._r)({packageName:"@clerk/clerk-react"});function h(e){d.setMessages(e).setPackageName(e)}var[p,g]=(0,s.e3)("AuthContext"),f=s.ED,m=s.hQ,v="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",k=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,b="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",y="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",w="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",S="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",_="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",P=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored.`,O=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,C=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,j=e=>`The <${e}/> component uses path-based routing by default unless a different routing strategy is provided using the \`routing\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \`path\` prop. Example: <${e} path={'/my-path'} />`,E=e=>`The \`path\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \`routing='path'\` to the <${e}/> component, or drop the \`path\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`,U="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored.",M="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored.",z="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",I="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",L="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",W="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",R="Missing props. <UserButton.Action /> component requires the following props: label.",T=e=>{(0,s.Kz)(()=>{d.throwMissingClerkProviderError({source:e})})},A=e=>new Promise(t=>{e.loaded&&t(),e.addOnLoaded(t)}),x=e=>async t=>(await A(e),e.session)?e.session.getToken(t):null,D=e=>async(...t)=>(await A(e),e.signOut(...t)),N=(e={})=>{T("useAuth");let t=g();void 0===t.sessionId&&void 0===t.userId&&(t=null!=e?e:{});let{sessionId:r,userId:i,actor:n,orgId:s,orgRole:a,orgSlug:l,orgPermissions:u,factorVerificationAge:c}=t,d=m();return F({sessionId:r,userId:i,actor:n,orgId:s,orgSlug:l,orgRole:a,getToken:(0,o.useCallback)(x(d),[d]),signOut:(0,o.useCallback)(D(d),[d]),orgPermissions:u,factorVerificationAge:c})};function F(e){let{sessionId:t,userId:r,actor:i,orgId:s,orgSlug:a,orgRole:l,has:u,signOut:c,getToken:h,orgPermissions:p,factorVerificationAge:g}=null!=e?e:{},f=(0,o.useCallback)(e=>u?u(e):(0,n.M)({userId:r,orgId:s,orgRole:l,orgPermissions:p,factorVerificationAge:g})(e),[r,g,s,l,p]);return void 0===t&&void 0===r?{isLoaded:!1,isSignedIn:void 0,sessionId:t,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:c,getToken:h}:null===t&&null===r?{isLoaded:!0,isSignedIn:!1,sessionId:t,userId:r,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:c,getToken:h}:t&&r&&s&&l?{isLoaded:!0,isSignedIn:!0,sessionId:t,userId:r,actor:i||null,orgId:s,orgRole:l,orgSlug:a||null,has:f,signOut:c,getToken:h}:t&&r&&!s?{isLoaded:!0,isSignedIn:!0,sessionId:t,userId:r,actor:i||null,orgId:null,orgRole:null,orgSlug:null,has:f,signOut:c,getToken:h}:d.throw("Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")}function B(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=o.useMemo(()=>e.createEmailLinkFlow(),[e]);return o.useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}var V=()=>{var e;T("useSignIn");let t=m(),r=(0,s.WD)();return(null==(e=t.telemetry)||e.record((0,a.FJ)("useSignIn")),r)?{isLoaded:!0,signIn:r.signIn,setActive:t.setActive}:{isLoaded:!1,signIn:void 0,setActive:void 0}},$=()=>{var e;T("useSignUp");let t=m(),r=(0,s.WD)();return(null==(e=t.telemetry)||e.record((0,a.FJ)("useSignUp")),r)?{isLoaded:!0,signUp:r.signUp,setActive:t.setActive}:{isLoaded:!1,signUp:void 0,setActive:void 0}},K=(e,t)=>{let r=("string"==typeof t?t:null==t?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=r;let i="string"==typeof t?void 0:t,n=t=>{T(r||"withClerk");let n=m();return n.loaded||(null==i?void 0:i.renderWhileLoading)?o.createElement(e,{...t,component:r,clerk:n}):null};return n.displayName=`withClerk(${r})`,n},J=({children:e})=>{T("SignedIn");let{userId:t}=g();return t?e:null},q=({children:e})=>{T("SignedOut");let{userId:t}=g();return null===t?e:null},G=({children:e})=>(T("ClerkLoaded"),m().loaded)?e:null,Q=({children:e})=>(T("ClerkLoading"),m().loaded)?null:e,Z=({children:e,fallback:t,...r})=>{T("Protect");let{isLoaded:i,has:n,userId:o}=N();if(!i)return null;let s=null!=t?t:null;return o?"function"==typeof r.condition?r.condition(n)?e:s:r.role||r.permission?n(r)?e:s:e:s},H=K(({clerk:e,...t})=>{let{client:r,session:i}=e,n=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return o.useEffect(()=>{null===i&&n?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),Y=K(({clerk:e,...t})=>(o.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),X=K(({clerk:e})=>(o.useEffect(()=>{c("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),ee=K(({clerk:e})=>(o.useEffect(()=>{c("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),et=K(({clerk:e})=>(o.useEffect(()=>{c("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),er=K(({clerk:e,...t})=>(o.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback")},34551:(e,t,r)=>{r.d(t,{FJ:()=>b,YF:()=>y}),r(27332);var i,n,o,s,a,l,u,c,d,h,p,g,f,m,v,k=r(73967);r(38523),i=new WeakMap,n=new WeakMap,o=new WeakSet,s=function(e){let{sk:t,pk:r,payload:i,...n}=e,o={...i,...n};return JSON.stringify(Object.keys({...i,...n}).sort().map(e=>o[e]))},a=function(){let e=localStorage.getItem((0,k.S7)(this,i));return e?JSON.parse(e):{}},l=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,k.S7)(this,i)),!1}};u=new WeakMap,c=new WeakMap,d=new WeakMap,h=new WeakMap,p=new WeakMap,g=new WeakSet,f=function(e,t){let r=Math.random();return!!(r<=(0,k.S7)(this,u).samplingRate&&(void 0===t||r<=t))&&!(0,k.S7)(this,c).isEventThrottled(e)},m=function(){fetch(new URL("/v1/event",(0,k.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,k.S7)(this,h)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,k.OV)(this,h,[])}).catch(()=>void 0)},v=function(){let e={name:(0,k.S7)(this,d).sdk,version:(0,k.S7)(this,d).sdkVersion};return"undefined"!=typeof window&&window.Clerk&&(e={...e,...window.Clerk.constructor.sdkMetadata}),e};function b(e,t){return{event:"METHOD_CALLED",payload:{method:e,...t}}}function y(e){return{event:"FRAMEWORK_METADATA",eventSamplingRate:.1,payload:e}}},34921:(e,t,r)=>{r.d(t,{T5:()=>S,nO:()=>w,_R:()=>y,kX:()=>b});var i=/\/$|\/\?|\/#/,n=(e,t="5.58.1")=>{if(e)return e;let r=o(t);return r?"snapshot"===r?"5.58.1":r:s(t)},o=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],s=e=>e.trim().replace(/^v/,"").split(".")[0];function a(e){return e.startsWith("/")}var l={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!0,jitter:!0},u=async e=>new Promise(t=>setTimeout(t,e)),c=(e,t)=>t?e*(1+Math.random()):e,d=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=c(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await u(r()),t++}},h=async(e,t={})=>{let r=0,{shouldRetry:i,initialDelay:n,maxDelayBetweenRetries:o,factor:s,retryImmediately:a,jitter:h}={...l,...t},p=d({initialDelay:n,maxDelayBetweenRetries:o,factor:s,jitter:h});for(;;)try{return await e()}catch(e){if(!i(e,++r))throw e;a&&1===r?await u(c(100,h)):await p()}};async function p(e="",t){let{async:r,defer:i,beforeLoad:n,crossOrigin:o,nonce:s}=t||{};return h(()=>new Promise((t,a)=>{e||a(Error("loadScript cannot be called without a src")),document&&document.body||a("loadScript cannot be called when document does not exist");let l=document.createElement("script");o&&l.setAttribute("crossorigin",o),l.async=r||!1,l.defer=i||!1,l.addEventListener("load",()=>{l.remove(),t(l)}),l.addEventListener("error",()=>{l.remove(),a()}),l.src=e,l.nonce=s,n?.(l),document.body.appendChild(l)}),{shouldRetry:(e,t)=>t<=5})}var g=r(89081),f=r(72565),m="Clerk: Failed to load Clerk",{isDevOrStagingUrl:v}=(0,f.RZ)(),k=(0,g._r)({packageName:"@clerk/shared"});function b(e){k.setPackageName({packageName:e})}var y=async e=>{let t=document.querySelector("script[data-clerk-js-script]");return t?new Promise((e,r)=>{t.addEventListener("load",()=>{e(t)}),t.addEventListener("error",()=>{r(m)})}):e?.publishableKey?p(w(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:_(e)}).catch(()=>{throw Error(m)}):void k.throwMissingPublishableKeyError()},w=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:i,proxyUrl:o,domain:s,publishableKey:l}=e;if(t)return t;let u="";u=o&&function(e){var t;return!e||(t=e,/^http(s)?:\/\//.test(t||""))||a(e)}(o)?(function(e){return e?a(e)?new URL(e,window.location.origin).toString():e:""})(o).replace(/http(s)?:\/\//,""):s&&!v((0,f.q5)(l)?.frontendApi||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(s):(0,f.q5)(l)?.frontendApi||"";let c=r?`${r.replace(/\.+$/,"")}.`:"",d=n(i);return`https://${u}/npm/@clerk/clerk-js@${d}/dist/clerk.${c}browser.js`},S=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},_=e=>t=>{let r=S(e);for(let e in r)t.setAttribute(e,r[e])};r(73967)},40940:(e,t,r)=>{r.d(t,{FJ:()=>i.FJ,YF:()=>i.YF});var i=r(34551);r(27332),r(73967)},44970:(e,t,r)=>{var i=r(99004),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useState,s=i.useEffect,a=i.useLayoutEffect,l=i.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!n(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),i=o({inst:{value:r,getSnapshot:t}}),n=i[0].inst,c=i[1];return a(function(){n.value=r,n.getSnapshot=t,u(n)&&c({inst:n})},[e,r,t]),s(function(){return u(n)&&c({inst:n}),e(function(){u(n)&&c({inst:n})})},[e]),l(r),r};t.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:c},50323:(e,t,r)=>{r.d(t,{_r:()=>i._r});var i=r(89081);r(73967)},72565:(e,t,r)=>{r.d(t,{RZ:()=>l,rA:()=>a,q5:()=>s});var i=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,n=r(6281),o="pk_live_";function s(e,t={}){if(!(e=e||"")||!a(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!a(e))throw Error("Publishable key not valid.");return null}let r=e.startsWith(o)?"production":"development",n=i(e.split("_")[2]);return n=n.slice(0,-1),t.proxyUrl?n=t.proxyUrl:"development"!==r&&t.domain&&(n=`clerk.${t.domain}`),{instanceType:r,frontendApi:n}}function a(e=""){try{let t=e.startsWith(o)||e.startsWith("pk_test_"),r=i(e.split("_")[2]||"").endsWith("$");return t&&r}catch{return!1}}function l(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,i=e.get(r);return void 0===i&&(i=n.gE.some(e=>r.endsWith(e)),e.set(r,i)),i}}}},73967:(e,t,r)=>{r.d(t,{OV:()=>p,S7:()=>h,VA:()=>l,ie:()=>c,jq:()=>g});var i=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,a=e=>{throw TypeError(e)},l=(e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})},u=(e,t,r,a)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of o(t))s.call(e,l)||l===r||i(e,l,{get:()=>t[l],enumerable:!(a=n(t,l))||a.enumerable});return e},c=(e,t,r)=>(u(e,t,"default"),r&&u(r,t,"default")),d=(e,t,r)=>t.has(e)||a("Cannot "+r),h=(e,t,r)=>(d(e,t,"read from private field"),r?r.call(e):t.get(e)),p=(e,t,r,i)=>(d(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),g=(e,t,r)=>(d(e,t,"access private method"),r)},76781:(e,t,r)=>{let i,n;r.d(t,{ED:()=>eq,pc:()=>eH,TS:()=>e7,IC:()=>eX,Rs:()=>eQ,e3:()=>eK,MZ:()=>tl,Kz:()=>e3,ho:()=>ta,hQ:()=>eG,WD:()=>eY,Z5:()=>te,D_:()=>tr,Wp:()=>tc,wV:()=>tn,g7:()=>to,Jd:()=>ts});var o={};r.r(o),r.d(o,{SWRConfig:()=>eC,default:()=>ej,mutate:()=>et,preload:()=>ec,unstable_serialize:()=>e_,useSWRConfig:()=>eu});var s=r(34551),a=(...e)=>{},l=()=>{let e=a,t=a;return{promise:new Promise((r,i)=>{e=r,t=i}),resolve:e,reject:t}};r(27332);var u=r(89081),c="reverification-error",d=e=>({clerk_error:{type:"forbidden",reason:c,metadata:{reverification:e}}}),h=e=>e&&"object"==typeof e&&"clerk_error"in e&&e.clerk_error?.type==="forbidden"&&e.clerk_error?.reason===c,p=r(31257),g=r(73967),f=r(99004),m=r(8751),v=Object.prototype.hasOwnProperty;let k=new WeakMap,b=()=>{},y=b(),w=Object,S=e=>e===y,_=e=>"function"==typeof e,P=(e,t)=>({...e,...t}),O=e=>_(e.then),C={},j={},E="undefined",U=typeof window!=E,M=typeof document!=E,z=U&&"Deno"in window,I=()=>U&&typeof window.requestAnimationFrame!=E,L=(e,t)=>{let r=k.get(e);return[()=>!S(t)&&e.get(t)||C,i=>{if(!S(t)){let n=e.get(t);t in j||(j[t]=n),r[5](t,P(n,i),n||C)}},r[6],()=>!S(t)&&t in j?j[t]:!S(t)&&e.get(t)||C]},W=!0,[R,T]=U&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[b,b],A={initFocus:e=>(M&&document.addEventListener("visibilitychange",e),R("focus",e),()=>{M&&document.removeEventListener("visibilitychange",e),T("focus",e)}),initReconnect:e=>{let t=()=>{W=!0,e()},r=()=>{W=!1};return R("online",t),R("offline",r),()=>{T("online",t),T("offline",r)}}},x=!f.useId,D=!U||z,N=e=>I()?window.requestAnimationFrame(e):setTimeout(e,1),F=D?f.useEffect:f.useLayoutEffect,B="undefined"!=typeof navigator&&navigator.connection,V=!D&&B&&(["slow-2g","2g"].includes(B.effectiveType)||B.saveData),$=new WeakMap,K=(e,t)=>w.prototype.toString.call(e)==="[object ".concat(t,"]"),J=0,q=e=>{let t,r,i=typeof e,n=K(e,"Date"),o=K(e,"RegExp"),s=K(e,"Object");if(w(e)!==e||n||o)t=n?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=$.get(e))return t;if(t=++J+"~",$.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=q(e[r])+",";$.set(e,t)}if(s){t="#";let i=w.keys(e).sort();for(;!S(r=i.pop());)S(e[r])||(t+=r+":"+q(e[r])+",");$.set(e,t)}}return t},G=e=>{if(_(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?q(e):"",t]},Q=0,Z=()=>++Q;async function H(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[i,n,o,s]=t,a=P({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),l=a.populateCache,u=a.rollbackOnError,c=a.optimisticData,d=e=>"function"==typeof u?u(e):!1!==u,h=a.throwOnError;if(_(n)){let e=[];for(let t of i.keys())!/^\$(inf|sub)\$/.test(t)&&n(i.get(t)._k)&&e.push(t);return Promise.all(e.map(p))}return p(n);async function p(e){let r,[n]=G(e);if(!n)return;let[s,u]=L(i,n),[p,g,f,m]=k.get(i),v=()=>{let t=p[n];return(_(a.revalidate)?a.revalidate(s().data,e):!1!==a.revalidate)&&(delete f[n],delete m[n],t&&t[0])?t[0](2).then(()=>s().data):s().data};if(t.length<3)return v();let b=o,w=Z();g[n]=[w,0];let P=!S(c),C=s(),j=C.data,E=C._c,U=S(E)?j:E;if(P&&u({data:c=_(c)?c(U,j):c,_c:U}),_(b))try{b=b(U)}catch(e){r=e}if(b&&O(b)){if(b=await b.catch(e=>{r=e}),w!==g[n][0]){if(r)throw r;return b}r&&P&&d(r)&&(l=!0,u({data:U,_c:y}))}if(l&&!r&&(_(l)?u({data:l(b,U),error:y,_c:y}):u({data:b,error:y,_c:y})),g[n][1]=Z(),Promise.resolve(v()).then(()=>{u({_c:y})}),r){if(h)throw r;return}return b}}let Y=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},X=(e,t)=>{if(!k.has(e)){let r=P(A,t),i=Object.create(null),n=H.bind(y,e),o=b,s=Object.create(null),a=(e,t)=>{let r=s[e]||[];return s[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},l=(t,r,i)=>{e.set(t,r);let n=s[t];if(n)for(let e of n)e(r,i)},u=()=>{if(!k.has(e)&&(k.set(e,[i,Object.create(null),Object.create(null),Object.create(null),n,l,a]),!D)){let t=r.initFocus(setTimeout.bind(y,Y.bind(y,i,0))),n=r.initReconnect(setTimeout.bind(y,Y.bind(y,i,1)));o=()=>{t&&t(),n&&n(),k.delete(e)}}};return u(),[e,n,u,o]}return[e,k.get(e)[4]]},[ee,et]=X(new Map),er=P({onLoadingSlow:b,onSuccess:b,onError:b,onErrorRetry:(e,t,r,i,n)=>{let o=r.errorRetryCount,s=n.retryCount,a=~~((Math.random()+.5)*(1<<(s<8?s:8)))*r.errorRetryInterval;(S(o)||!(s>o))&&setTimeout(i,a,n)},onDiscarded:b,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:V?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:V?5e3:3e3,compare:function e(t,r){var i,n;if(t===r)return!0;if(t&&r&&(i=t.constructor)===r.constructor){if(i===Date)return t.getTime()===r.getTime();if(i===RegExp)return t.toString()===r.toString();if(i===Array){if((n=t.length)===r.length)for(;n--&&e(t[n],r[n]););return -1===n}if(!i||"object"==typeof t){for(i in n=0,t)if(v.call(t,i)&&++n&&!v.call(r,i)||!(i in r)||!e(t[i],r[i]))return!1;return Object.keys(r).length===n}}return t!=t&&r!=r},isPaused:()=>!1,cache:ee,mutate:et,fallback:{}},{isOnline:()=>W,isVisible:()=>{let e=M&&document.visibilityState;return S(e)||"hidden"!==e}}),ei=(e,t)=>{let r=P(e,t);if(t){let{use:i,fallback:n}=e,{use:o,fallback:s}=t;i&&o&&(r.use=i.concat(o)),n&&s&&(r.fallback=P(n,s))}return r},en=(0,f.createContext)({}),eo="$inf$",es=U&&window.__SWR_DEVTOOLS_USE__,ea=es?window.__SWR_DEVTOOLS_USE__:[],el=e=>_(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],eu=()=>P(er,(0,f.useContext)(en)),ec=(e,t)=>{let[r,i]=G(e),[,,,n]=k.get(ee);if(n[r])return n[r];let o=t(i);return n[r]=o,o},ed=ea.concat(e=>(t,r,i)=>{let n=r&&((...e)=>{let[i]=G(t),[,,,n]=k.get(ee);if(i.startsWith(eo))return r(...e);let o=n[i];return S(o)?r(...e):(delete n[i],o)});return e(t,n,i)}),eh=(e,t,r)=>{let i=t[e]||(t[e]=[]);return i.push(r),()=>{let e=i.indexOf(r);e>=0&&(i[e]=i[i.length-1],i.pop())}};es&&(window.__SWR_DEVTOOLS_REACT__=f);let ep=()=>{},eg=ep(),ef=Object,em=e=>e===eg,ev=e=>"function"==typeof e,ek=new WeakMap,eb=(e,t)=>ef.prototype.toString.call(e)===`[object ${t}]`,ey=0,ew=e=>{let t,r,i=typeof e,n=eb(e,"Date"),o=eb(e,"RegExp"),s=eb(e,"Object");if(ef(e)!==e||n||o)t=n?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=ek.get(e))return t;if(t=++ey+"~",ek.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=ew(e[r])+",";ek.set(e,t)}if(s){t="#";let i=ef.keys(e).sort();for(;!em(r=i.pop());)em(e[r])||(t+=r+":"+ew(e[r])+",");ek.set(e,t)}}return t},eS=e=>{if(ev(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?ew(e):"",t]},e_=e=>eS(e)[0],eP=f.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),eO={dedupe:!0},eC=w.defineProperty(e=>{let{value:t}=e,r=(0,f.useContext)(en),i=_(t),n=(0,f.useMemo)(()=>i?t(r):t,[i,r,t]),o=(0,f.useMemo)(()=>i?n:ei(r,n),[i,r,n]),s=n&&n.provider,a=(0,f.useRef)(y);s&&!a.current&&(a.current=X(s(o.cache||ee),n));let l=a.current;return l&&(o.cache=l[0],o.mutate=l[1]),F(()=>{if(l)return l[2]&&l[2](),l[3]},[]),(0,f.createElement)(en.Provider,P(e,{value:o}))},"defaultValue",{value:er}),ej=(i=(e,t,r)=>{let{cache:i,compare:n,suspense:o,fallbackData:s,revalidateOnMount:a,revalidateIfStale:l,refreshInterval:u,refreshWhenHidden:c,refreshWhenOffline:d,keepPreviousData:h}=r,[p,g,v,b]=k.get(i),[w,C]=G(e),j=(0,f.useRef)(!1),E=(0,f.useRef)(!1),U=(0,f.useRef)(w),M=(0,f.useRef)(t),z=(0,f.useRef)(r),I=()=>z.current,W=()=>I().isVisible()&&I().isOnline(),[R,T,A,B]=L(i,w),V=(0,f.useRef)({}).current,$=S(s)?S(r.fallback)?y:r.fallback[w]:s,K=(e,t)=>{for(let r in V)if("data"===r){if(!n(e[r],t[r])&&(!S(e[r])||!n(ei,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},J=(0,f.useMemo)(()=>{let e=!!w&&!!t&&(S(a)?!I().isPaused()&&!o&&!1!==l:a),r=t=>{let r=P(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},i=R(),n=B(),s=r(i),u=i===n?s:r(n),c=s;return[()=>{let e=r(R());return K(e,c)?(c.data=e.data,c.isLoading=e.isLoading,c.isValidating=e.isValidating,c.error=e.error,c):(c=e,e)},()=>u]},[i,w]),q=(0,m.useSyncExternalStore)((0,f.useCallback)(e=>A(w,(t,r)=>{K(r,t)||e()}),[i,w]),J[0],J[1]),Q=!j.current,Y=p[w]&&p[w].length>0,X=q.data,ee=S(X)?$&&O($)?eP($):$:X,et=q.error,er=(0,f.useRef)(ee),ei=h?S(X)?S(er.current)?ee:er.current:X:ee,en=(!Y||!!S(et))&&(Q&&!S(a)?a:!I().isPaused()&&(o?!S(ee)&&l:S(ee)||l)),eo=!!(w&&t&&Q&&en),es=S(q.isValidating)?eo:q.isValidating,ea=S(q.isLoading)?eo:q.isLoading,el=(0,f.useCallback)(async e=>{let t,i,o=M.current;if(!w||!o||E.current||I().isPaused())return!1;let s=!0,a=e||{},l=!v[w]||!a.dedupe,u=()=>x?!E.current&&w===U.current&&j.current:w===U.current,c={isValidating:!1,isLoading:!1},d=()=>{T(c)},h=()=>{let e=v[w];e&&e[1]===i&&delete v[w]},f={isValidating:!0};S(R().data)&&(f.isLoading=!0);try{if(l&&(T(f),r.loadingTimeout&&S(R().data)&&setTimeout(()=>{s&&u()&&I().onLoadingSlow(w,r)},r.loadingTimeout),v[w]=[o(C),Z()]),[t,i]=v[w],t=await t,l&&setTimeout(h,r.dedupingInterval),!v[w]||v[w][1]!==i)return l&&u()&&I().onDiscarded(w),!1;c.error=y;let e=g[w];if(!S(e)&&(i<=e[0]||i<=e[1]||0===e[1]))return d(),l&&u()&&I().onDiscarded(w),!1;let a=R().data;c.data=n(a,t)?a:t,l&&u()&&I().onSuccess(t,w,r)}catch(r){h();let e=I(),{shouldRetryOnError:t}=e;!e.isPaused()&&(c.error=r,l&&u()&&(e.onError(r,w,e),(!0===t||_(t)&&t(r))&&(!I().revalidateOnFocus||!I().revalidateOnReconnect||W())&&e.onErrorRetry(r,w,e,e=>{let t=p[w];t&&t[0]&&t[0](3,e)},{retryCount:(a.retryCount||0)+1,dedupe:!0})))}return s=!1,d(),!0},[w,i]),eu=(0,f.useCallback)((...e)=>H(i,U.current,...e),[]);if(F(()=>{M.current=t,z.current=r,S(X)||(er.current=X)}),F(()=>{if(!w)return;let e=el.bind(y,eO),t=0;I().revalidateOnFocus&&(t=Date.now()+I().focusThrottleInterval);let r=eh(w,p,(r,i={})=>{if(0==r){let r=Date.now();I().revalidateOnFocus&&r>t&&W()&&(t=r+I().focusThrottleInterval,e())}else if(1==r)I().revalidateOnReconnect&&W()&&e();else if(2==r)return el();else if(3==r)return el(i)});return E.current=!1,U.current=w,j.current=!0,T({_k:C}),en&&(S(ee)||D?e():N(e)),()=>{E.current=!0,r()}},[w]),F(()=>{let e;function t(){let t=_(u)?u(R().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!R().error&&(c||I().isVisible())&&(d||I().isOnline())?el(eO).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,c,d,w]),(0,f.useDebugValue)(ei),o&&S(ee)&&w){if(!x&&D)throw Error("Fallback data is required when using Suspense in SSR.");M.current=t,z.current=r,E.current=!1;let e=b[w];if(S(e)||eP(eu(e)),S(et)){let e=el(eO);S(ei)||(e.status="fulfilled",e.value=!0),eP(e)}else throw et}return{mutate:eu,get data(){return V.data=!0,ei},get error(){return V.error=!0,et},get isValidating(){return V.isValidating=!0,es},get isLoading(){return V.isLoading=!0,ea}}},function(...e){let t=eu(),[r,n,o]=el(e),s=ei(t,o),a=i,{use:l}=s,u=(l||[]).concat(ed);for(let e=u.length;e--;)a=u[e](a);return a(r,n||s.fetcher||null,s)}),eE=()=>{},eU=eE(),eM=Object,ez=e=>e===eU,eI=e=>"function"==typeof e,eL=new WeakMap,eW=(e,t)=>eM.prototype.toString.call(e)===`[object ${t}]`,eR=0,eT=e=>{let t,r,i=typeof e,n=eW(e,"Date"),o=eW(e,"RegExp"),s=eW(e,"Object");if(eM(e)!==e||n||o)t=n?e.toJSON():"symbol"==i?e.toString():"string"==i?JSON.stringify(e):""+e;else{if(t=eL.get(e))return t;if(t=++eR+"~",eL.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eT(e[r])+",";eL.set(e,t)}if(s){t="#";let i=eM.keys(e).sort();for(;!ez(r=i.pop());)ez(e[r])||(t+=r+":"+eT(e[r])+",");eL.set(e,t)}}return t},eA=e=>{if(eI(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eT(e):"",t]},ex=e=>eA(e?e(0,null):null)[0],eD=Promise.resolve(),eN=(n=e=>(t,r,i)=>{let n,o=(0,f.useRef)(!1),{cache:s,initialSize:a=1,revalidateAll:l=!1,persistSize:u=!1,revalidateFirstPage:c=!0,revalidateOnMount:d=!1,parallel:h=!1}=i,[,,,p]=k.get(ee);try{(n=ex(t))&&(n=eo+n)}catch(e){}let[g,v,b]=L(s,n),w=(0,f.useCallback)(()=>S(g()._l)?a:g()._l,[s,n,a]);(0,m.useSyncExternalStore)((0,f.useCallback)(e=>n?b(n,()=>{e()}):()=>{},[s,n]),w,w);let P=(0,f.useCallback)(()=>{let e=g()._l;return S(e)?a:e},[n,a]),O=(0,f.useRef)(P());F(()=>{if(!o.current){o.current=!0;return}n&&v({_l:u?O.current:P()})},[n,s]);let C=d&&!o.current,j=e(n,async e=>{let n=g()._i,o=g()._r;v({_r:y});let a=[],u=P(),[d]=L(s,e),f=d().data,m=[],k=null;for(let e=0;e<u;++e){let[u,d]=G(t(e,h?null:k));if(!u)break;let[g,v]=L(s,u),b=g().data,y=l||n||S(b)||c&&!e&&!S(f)||C||f&&!S(f[e])&&!i.compare(f[e],b);if(r&&("function"==typeof o?o(b,d):y)){let t=async()=>{if(u in p){let e=p[u];delete p[u],b=await e}else b=await r(d);v({data:b,_k:d}),a[e]=b};h?m.push(t):await t()}else a[e]=b;h||(k=b)}return h&&await Promise.all(m.map(e=>e())),v({_i:y}),a},i),E=(0,f.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},i=!1!==r.revalidate;return n?(i&&(S(e)?v({_i:!0,_r:r.revalidate}):v({_i:!1,_r:r.revalidate})),arguments.length?j.mutate(e,{...r,revalidate:i}):j.mutate()):eD},[n,s]),U=(0,f.useCallback)(e=>{let r;if(!n)return eD;let[,i]=L(s,n);if(_(e)?r=e(P()):"number"==typeof e&&(r=e),"number"!=typeof r)return eD;i({_l:r}),O.current=r;let o=[],[a]=L(s,n),l=null;for(let e=0;e<r;++e){let[r]=G(t(e,l)),[i]=L(s,r),n=r?i().data:y;if(S(n))return E(a().data);o.push(n),l=n}return E(o)},[n,s,E,P]);return{size:P(),setSize:U,mutate:E,get data(){return j.data},get error(){return j.error},get isValidating(){return j.isValidating},get isLoading(){return j.isLoading}}},(...e)=>{let[t,r,i]=el(e),o=(i.use||[]).concat(n);return ej(t,r,{...i,use:o})});var eF=Object.prototype.hasOwnProperty;function eB(e,t,r){for(r of e.keys())if(eV(r,t))return r}function eV(e,t){var r,i,n;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((i=e.length)===t.length)for(;i--&&eV(e[i],t[i]););return -1===i}if(r===Set){if(e.size!==t.size)return!1;for(i of e)if((n=i)&&"object"==typeof n&&!(n=eB(t,n))||!t.has(n))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(i of e)if((n=i[0])&&"object"==typeof n&&!(n=eB(t,n))||!eV(i[1],t.get(n)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((i=e.byteLength)===t.byteLength)for(;i--&&e.getInt8(i)===t.getInt8(i););return -1===i}if(ArrayBuffer.isView(e)){if((i=e.byteLength)===t.byteLength)for(;i--&&e[i]===t[i];);return -1===i}if(!r||"object"==typeof e){for(r in i=0,e)if(eF.call(e,r)&&++i&&!eF.call(t,r)||!(r in t)||!eV(e[r],t[r]))return!1;return Object.keys(t).length===i}}return e!=e&&t!=t}function e$(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var eK=(e,t)=>{let{assertCtxFn:r=e$}=t||{},i=f.createContext(void 0);return i.displayName=e,[i,()=>{let t=f.useContext(i);return r(t,`${e} not found`),t.value},()=>{let e=f.useContext(i);return e?e.value:{}}]},eJ={};(0,g.VA)(eJ,{useSWR:()=>ej,useSWRInfinite:()=>eN}),(0,g.ie)(eJ,o);var[eq,eG]=eK("ClerkInstanceContext"),[eQ,eZ]=eK("UserContext"),[eH,eY]=eK("ClientContext"),[eX,e0]=eK("SessionContext"),[e1,e2]=(f.createContext({}),eK("OrganizationContext")),e7=({children:e,organization:t,swrConfig:r})=>f.createElement(eJ.SWRConfig,{value:r},f.createElement(e1.Provider,{value:{value:{organization:t}}},e));function e3(e){if(!f.useContext(eq)){if("function"==typeof e)return void e();throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function e5(e,t){let r=new Set(Object.keys(t)),i={};for(let t of Object.keys(e))r.has(t)||(i[t]=e[t]);return i}var e9=(e,t)=>{let r="boolean"==typeof e&&e,i=(0,f.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),n=(0,f.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),o={};for(let i of Object.keys(t))o[i]=r?t[i]:e?.[i]??t[i];return{...o,initialPage:i.current,pageSize:n.current}},e8={dedupingInterval:6e4,focusThrottleInterval:12e4},e6=(e,t,r,i)=>{let[n,o]=(0,f.useState)(e.initialPage??1),s=(0,f.useRef)(e.initialPage??1),a=(0,f.useRef)(e.pageSize??10),l=r.enabled??!0,u=r.infinite??!1,c=r.keepPreviousData??!1,d={...i,...e,initialPage:n,pageSize:a.current},{data:h,isValidating:p,isLoading:g,error:m,mutate:v}=ej(!u&&t&&l?d:null,e=>{let r=e5(e,i);return t?.(r)},{keepPreviousData:c,...e8}),{data:k,isLoading:b,isValidating:y,error:w,size:S,setSize:_,mutate:P}=eN(t=>u&&l?{...e,...i,initialPage:s.current+t,pageSize:a.current}:null,e=>{let r=e5(e,i);return t?.(r)},e8),O=(0,f.useMemo)(()=>u?S:n,[u,S,n]),C=(0,f.useCallback)(e=>u?void _(e):o(e),[_]),j=(0,f.useMemo)(()=>u?k?.map(e=>e?.data).flat()??[]:h?.data??[],[u,h,k]),E=(0,f.useMemo)(()=>u?k?.[k?.length-1]?.total_count||0:h?.total_count??0,[u,h,k]),U=u?b:g,M=u?y:p,z=(u?w:m)??null,I=(0,f.useCallback)(()=>{C(e=>Math.max(0,e+1))},[C]),L=(0,f.useCallback)(()=>{C(e=>Math.max(0,e-1))},[C]),W=(s.current-1)*a.current,R=Math.ceil((E-W)/a.current),T=E-W*a.current>O*a.current,A=(O-1)*a.current>W*a.current,x=u?e=>P(e,{revalidate:!1}):e=>v(e,{revalidate:!1});return{data:j,count:E,error:z,isLoading:U,isFetching:M,isError:!!z,page:O,pageCount:R,fetchPage:C,fetchNext:I,fetchPrevious:L,hasNextPage:T,hasPreviousPage:A,revalidate:u?()=>P():()=>v(),setData:x}},e4={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function te(e){var t,r;let{domains:i,membershipRequests:n,memberships:o,invitations:a,subscriptions:l}=e||{};e3("useOrganization");let{organization:u}=e2(),c=e0(),d=e9(i,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),h=e9(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),p=e9(o,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1,query:void 0}),g=e9(a,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),f=e9(l,{initialPage:1,pageSize:10,status:void 0,keepPreviousData:!1,infinite:!1}),m=eG();m.telemetry?.record((0,s.FJ)("useOrganization"));let v=void 0===i?void 0:{initialPage:d.initialPage,pageSize:d.pageSize,enrollmentMode:d.enrollmentMode},k=void 0===n?void 0:{initialPage:h.initialPage,pageSize:h.pageSize,status:h.status},b=void 0===o?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,role:p.role,query:p.query},y=void 0===a?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,status:g.status},w=void 0===l?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,status:f.status},S=e6({...v},u?.getDomains,{keepPreviousData:d.keepPreviousData,infinite:d.infinite,enabled:!!v},{type:"domains",organizationId:u?.id}),_=e6({...k},u?.getMembershipRequests,{keepPreviousData:h.keepPreviousData,infinite:h.infinite,enabled:!!k},{type:"membershipRequests",organizationId:u?.id}),P=e6(b||{},u?.getMemberships,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!b},{type:"members",organizationId:u?.id}),O=e6({...y},u?.getInvitations,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!y},{type:"invitations",organizationId:u?.id}),C=e6({...w},u?.__experimental_getSubscriptions,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!w},{type:"subscriptions",organizationId:u?.id});return void 0===u?{isLoaded:!1,organization:void 0,membership:void 0,domains:e4,membershipRequests:e4,memberships:e4,invitations:e4,subscriptions:e4}:null===u?{isLoaded:!0,organization:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null,subscriptions:null}:!m.loaded&&u?{isLoaded:!0,organization:u,membership:void 0,domains:e4,membershipRequests:e4,memberships:e4,invitations:e4,subscriptions:e4}:{isLoaded:m.loaded,organization:u,membership:(t=c.user.organizationMemberships,r=u.id,t.find(e=>e.organization.id===r)),domains:S,membershipRequests:_,memberships:P,invitations:O,subscriptions:C}}var tt={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function tr(e){let{userMemberships:t,userInvitations:r,userSuggestions:i}=e||{};e3("useOrganizationList");let n=e9(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),o=e9(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),a=e9(i,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),l=eG(),u=eZ();l.telemetry?.record((0,s.FJ)("useOrganizationList"));let c=void 0===t?void 0:{initialPage:n.initialPage,pageSize:n.pageSize},d=void 0===r?void 0:{initialPage:o.initialPage,pageSize:o.pageSize,status:o.status},h=void 0===i?void 0:{initialPage:a.initialPage,pageSize:a.pageSize,status:a.status},p=!!(l.loaded&&u),g=e6(c||{},u?.getOrganizationMemberships,{keepPreviousData:n.keepPreviousData,infinite:n.infinite,enabled:!!c},{type:"userMemberships",userId:u?.id}),f=e6({...d},u?.getOrganizationInvitations,{keepPreviousData:o.keepPreviousData,infinite:o.infinite,enabled:!!d},{type:"userInvitations",userId:u?.id}),m=e6({...h},u?.getOrganizationSuggestions,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!h},{type:"userSuggestions",userId:u?.id});return p?{isLoaded:p,setActive:l.setActive,createOrganization:l.createOrganization,userMemberships:g,userInvitations:f,userSuggestions:m}:{isLoaded:!1,createOrganization:void 0,setActive:void 0,userMemberships:tt,userInvitations:tt,userSuggestions:tt}}var ti="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,tn=()=>{e3("useSession");let e=e0();return void 0===e?{isLoaded:!1,isSignedIn:void 0,session:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:e}},to=()=>{e3("useSessionList");let e=eG(),t=eY();return t?{isLoaded:!0,sessions:t.sessions,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setActive:void 0}};function ts(){e3("useUser");let e=eZ();return void 0===e?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var ta=()=>(e3("useClerk"),eG()),tl=eV;async function tu(e){try{let t=await e;if(t instanceof Response)return t.json();return t}catch(e){if((0,u.$R)(e)&&e.errors.find(({code:e})=>"session_reverification_required"===e))return d();throw e}}var tc=(e,t)=>{let{__internal_openReverification:r,telemetry:i}=ta(),n=(0,f.useRef)(e),o=(0,f.useRef)(t),a=(0,f.useMemo)(()=>(function(e){return function(t){return async(...r)=>{let i=await tu(t(...r));if(h(i)){let n=l(),o=(0,p.D)(i.clerk_error.metadata?.reverification),a=o?o().level:void 0,c=()=>{n.reject(new u.cR("User cancelled attempted verification",{code:"reverification_cancelled"}))},d=()=>{n.resolve(!0)};void 0===e.onNeedsReverification?e.openUIComponent?.({level:a,afterVerification:d,afterVerificationCancelled:c}):(e.telemetry?.record((0,s.FJ)("UserVerificationCustomUI")),e.onNeedsReverification({cancel:c,complete:d,level:a})),await n.promise,i=await tu(t(...r))}return i}}})({openUIComponent:r,telemetry:i,...o.current})(n.current),[r,n.current,o.current]);return ti(()=>{n.current=e,o.current=t}),a}},87905:(e,t,r)=>{r.d(t,{B$:()=>c.B$,z0:()=>c.z0,A0:()=>c.A0,lJ:()=>eO,ul:()=>ed,PQ:()=>ef,oE:()=>eg,nC:()=>ec,NC:()=>ep,EH:()=>c.EH,rm:()=>c.rm,m2:()=>c.m2,W5:()=>c.W5,mO:()=>c.mO,eG:()=>c.eG,Ls:()=>Y,hZ:()=>ev,M_:()=>ey,ct:()=>eb,Hx:()=>X,Ny:()=>ek,iB:()=>c.iB,Bl:()=>c.Bl,uF:()=>ea,Fv:()=>er,cP:()=>em,As:()=>c.As,ho:()=>c.ho,ui:()=>c.ui,Z5:()=>c.Z5,D_:()=>c.D_,Wp:()=>c.Wp,wV:()=>c.dy,g7:()=>c.g7,go:()=>c.go,yC:()=>c.yC,Jd:()=>c.Jd});var i,n,o,s,a,l,u,c=r(33116),d=e=>{throw TypeError(e)},h=(e,t,r)=>t.has(e)||d("Cannot "+r),p=(e,t,r)=>(h(e,t,"read from private field"),r?r.call(e):t.get(e)),g=(e,t,r)=>t.has(e)?d("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),f=(e,t,r,i)=>(h(e,t,"write to private field"),i?i.call(e,r):t.set(e,r),r),m=(e,t,r)=>(h(e,t,"access private method"),r),v=r(34921),k=r(26980),b=r(99004),y=r(32909),w=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r};r(73967);var S=r(76781),_=r(72565),P=(e,t,r)=>!e&&r?O(r):C(t),O=e=>{let t=e.userId,r=e.user,i=e.sessionId,n=e.sessionStatus,o=e.session,s=e.organization,a=e.orgId,l=e.orgRole,u=e.orgPermissions,c=e.orgSlug;return{userId:t,user:r,sessionId:i,session:o,sessionStatus:n,organization:s,orgId:a,orgRole:l,orgPermissions:u,orgSlug:c,actor:e.actor,factorVerificationAge:e.factorVerificationAge}},C=e=>{let t=e.user?e.user.id:e.user,r=e.user,i=e.session?e.session.id:e.session,n=e.session,o=e.session?.status,s=e.session?e.session.factorVerificationAge:null,a=n?.actor,l=e.organization,u=e.organization?e.organization.id:e.organization,c=l?.slug,d=l?r?.organizationMemberships?.find(e=>e.organization.id===u):l,h=d?d.permissions:d;return{userId:t,user:r,sessionId:i,session:n,sessionStatus:o,organization:l,orgId:u,orgRole:d?d.role:d,orgSlug:c,orgPermissions:h,actor:a,factorVerificationAge:s}},j=r(21730);"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var E=e=>t=>{try{return b.Children.only(e)}catch{return c.sb.throw((0,c.Wq)(t))}},U=(e,t)=>(e||(e=t),"string"==typeof e&&(e=b.createElement("button",null,e)),e),M=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},z=new Map,I=e=>{let t=Array(e.length).fill(null),[r,i]=(0,b.useState)(t);return e.map((e,t)=>({id:e.id,mount:e=>i(r=>r.map((r,i)=>i===t?e:r)),unmount:()=>i(e=>e.map((e,r)=>r===t?null:e)),portal:()=>b.createElement(b.Fragment,null,r[t]?(0,y.createPortal)(e.component,r[t]):null)}))},L=(e,t)=>!!e&&b.isValidElement(e)&&(null==e?void 0:e.type)===t,W=(e,t)=>A({children:e,reorderItemsLabels:["account","security"],LinkComponent:et,PageComponent:ee,MenuItemsComponent:en,componentName:"UserProfile"},t),R=(e,t)=>A({children:e,reorderItemsLabels:["general","members"],LinkComponent:eu,PageComponent:el,componentName:"OrganizationProfile"},t),T=e=>{let t=[],r=[eu,el,en,ee,et];return b.Children.forEach(e,e=>{r.some(t=>L(e,t))||t.push(e)}),t},A=(e,t)=>{let{children:r,LinkComponent:i,PageComponent:n,MenuItemsComponent:o,reorderItemsLabels:s,componentName:a}=e,{allowForAnyChildren:l=!1}=t||{},u=[];b.Children.forEach(r,e=>{if(!L(e,n)&&!L(e,i)&&!L(e,o)){e&&!l&&(0,k.s2)((0,c.n)(a));return}let{props:t}=e,{children:r,label:d,url:h,labelIcon:p}=t;if(L(e,n))if(x(t,s))u.push({label:d});else{if(!D(t))return void(0,k.s2)((0,c.sR)(a));u.push({label:d,labelIcon:p,children:r,url:h})}if(L(e,i))if(!N(t))return void(0,k.s2)((0,c.D)(a));else u.push({label:d,labelIcon:p,url:h})});let d=[],h=[],p=[];u.forEach((e,t)=>{if(D(e)){d.push({component:e.children,id:t}),h.push({component:e.labelIcon,id:t});return}N(e)&&p.push({component:e.labelIcon,id:t})});let g=I(d),f=I(h),m=I(p),v=[],y=[];return u.forEach((e,t)=>{if(x(e,s))return void v.push({label:e.label});if(D(e)){let{portal:r,mount:i,unmount:n}=g.find(e=>e.id===t),{portal:o,mount:s,unmount:a}=f.find(e=>e.id===t);v.push({label:e.label,url:e.url,mount:i,unmount:n,mountIcon:s,unmountIcon:a}),y.push(r),y.push(o);return}if(N(e)){let{portal:r,mount:i,unmount:n}=m.find(e=>e.id===t);v.push({label:e.label,url:e.url,mountIcon:i,unmountIcon:n}),y.push(r);return}}),{customPages:v,customPagesPortals:y}},x=(e,t)=>{let{children:r,label:i,url:n,labelIcon:o}=e;return!r&&!n&&!o&&t.some(e=>e===i)},D=e=>{let{children:t,label:r,url:i,labelIcon:n}=e;return!!t&&!!i&&!!n&&!!r},N=e=>{let{children:t,label:r,url:i,labelIcon:n}=e;return!t&&!!i&&!!n&&!!r},F=e=>B({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:en,MenuActionComponent:eo,MenuLinkComponent:es,UserProfileLinkComponent:et,UserProfilePageComponent:ee}),B=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:i,UserProfileLinkComponent:n,UserProfilePageComponent:o,reorderItemsLabels:s})=>{let a=[],l=[],u=[];b.Children.forEach(e,e=>{if(!L(e,t)&&!L(e,n)&&!L(e,o)){e&&(0,k.s2)(c.P6);return}if(L(e,n)||L(e,o))return;let{props:l}=e;b.Children.forEach(l.children,e=>{if(!L(e,r)&&!L(e,i)){e&&(0,k.s2)(c.wm);return}let{props:t}=e,{label:n,labelIcon:o,href:l,onClick:u,open:d}=t;if(L(e,r))if(V(t,s))a.push({label:n});else{if(!$(t))return void(0,k.s2)(c.Wv);let e={label:n,labelIcon:o};if(void 0!==u)a.push({...e,onClick:u});else{if(void 0===d)return void(0,k.s2)("Custom menu item must have either onClick or open property");a.push({...e,open:d.startsWith("/")?d:`/${d}`})}}if(L(e,i))if(!K(t))return void(0,k.s2)(c.ld);else a.push({label:n,labelIcon:o,href:l})})});let d=[],h=[];a.forEach((e,t)=>{$(e)&&d.push({component:e.labelIcon,id:t}),K(e)&&h.push({component:e.labelIcon,id:t})});let p=I(d),g=I(h);return a.forEach((e,t)=>{if(V(e,s)&&l.push({label:e.label}),$(e)){let{portal:r,mount:i,unmount:n}=p.find(e=>e.id===t),o={label:e.label,mountIcon:i,unmountIcon:n};"onClick"in e?o.onClick=e.onClick:"open"in e&&(o.open=e.open),l.push(o),u.push(r)}if(K(e)){let{portal:r,mount:i,unmount:n}=g.find(e=>e.id===t);l.push({label:e.label,href:e.href,mountIcon:i,unmountIcon:n}),u.push(r)}}),{customMenuItems:l,customMenuItemsPortals:u}},V=(e,t)=>{let{children:r,label:i,onClick:n,labelIcon:o}=e;return!r&&!n&&!o&&t.some(e=>e===i)},$=e=>{let{label:t,labelIcon:r,onClick:i,open:n}=e;return!!r&&!!t&&("function"==typeof i||"string"==typeof n)},K=e=>{let{label:t,href:r,labelIcon:i}=e;return!!r&&!!i&&!!t};function J(e){let t=(0,b.useRef)(),[r,i]=(0,b.useState)("rendering");return(0,b.useEffect)(()=>{if(!e)throw Error("Clerk: no component name provided, unable to detect mount.");"undefined"==typeof window||t.current||(t.current=(function(e){let{root:t=null==document?void 0:document.body,selector:r,timeout:i=0}=e;return new Promise((e,n)=>{if(!t)return void n(Error("No root element provided"));let o=t;if(r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)return void e();let s=new MutationObserver(i=>{for(let n of i)if("childList"===n.type&&(!o&&r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)){s.disconnect(),e();return}});s.observe(t,{childList:!0,subtree:!0}),i>0&&setTimeout(()=>{s.disconnect(),n(Error("Timeout waiting for element children"))},i)})})({selector:`[data-clerk-component="${e}"]`}).then(()=>{i("rendered")}).catch(()=>{i("error")}))},[e]),r}var q=e=>"mount"in e,G=e=>"open"in e,Q=e=>null==e?void 0:e.map(({mountIcon:e,unmountIcon:t,...r})=>r),Z=class extends b.PureComponent{constructor(){super(...arguments),this.rootRef=b.createRef()}componentDidUpdate(e){var t,r,i,n;if(!q(e)||!q(this.props))return;let o=w(e.props,"customPages","customMenuItems","children"),s=w(this.props.props,"customPages","customMenuItems","children"),a=(null==(t=o.customPages)?void 0:t.length)!==(null==(r=s.customPages)?void 0:r.length),l=(null==(i=o.customMenuItems)?void 0:i.length)!==(null==(n=s.customMenuItems)?void 0:n.length),u=Q(e.props.customMenuItems),c=Q(this.props.props.customMenuItems);(!(0,S.MZ)(o,s)||!(0,S.MZ)(u,c)||a||l)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(q(this.props)&&this.props.mount(this.rootRef.current,this.props.props),G(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(q(this.props)&&this.props.unmount(this.rootRef.current),G(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return b.createElement(b.Fragment,null,!e&&b.createElement("div",{...t}),this.props.children)}},H=e=>{var t,r;return b.createElement(b.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,b.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,b.createElement)(e,{key:t})))},Y=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"SignIn",renderWhileLoading:!0}),X=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"SignUp",renderWhileLoading:!0});function ee({children:e}){return(0,k.s2)(c.$n),b.createElement(b.Fragment,null,e)}function et({children:e}){return(0,k.s2)(c._I),b.createElement(b.Fragment,null,e)}var er=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:a}=W(i.children);return b.createElement(b.Fragment,null,n&&r,b.createElement(Z,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...i,customPages:s},rootProps:o},b.createElement(H,{customPagesPortals:a})))},{component:"UserProfile",renderWhileLoading:!0}),{Page:ee,Link:et}),ei=(0,b.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function en({children:e}){return(0,k.s2)(c.UX),b.createElement(b.Fragment,null,e)}function eo({children:e}){return(0,k.s2)(c.aU),b.createElement(b.Fragment,null,e)}function es({children:e}){return(0,k.s2)(c.Uw),b.createElement(b.Fragment,null,e)}var ea=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:a}=W(i.children,{allowForAnyChildren:!!i.__experimental_asProvider}),l=Object.assign(i.userProfileProps||{},{customPages:s}),{customMenuItems:u,customMenuItemsPortals:c}=F(i.children),d=T(i.children),h={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...i,userProfileProps:l,customMenuItems:u}};return b.createElement(ei.Provider,{value:h},n&&r,e.loaded&&b.createElement(Z,{component:t,...h,hideRootHtmlElement:!!i.__experimental_asProvider,rootProps:o},i.__experimental_asProvider?d:null,b.createElement(H,{customPagesPortals:a,customMenuItemsPortals:c})))},{component:"UserButton",renderWhileLoading:!0}),{UserProfilePage:ee,UserProfileLink:et,MenuItems:en,Action:eo,Link:es,__experimental_Outlet:function(e){let t=(0,b.useContext)(ei),r={...t,props:{...t.props,...e}};return b.createElement(Z,{...r})}});function el({children:e}){return(0,k.s2)(c.vb),b.createElement(b.Fragment,null,e)}function eu({children:e}){return(0,k.s2)(c.kf),b.createElement(b.Fragment,null,e)}var ec=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:a}=R(i.children);return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...i,customPages:s},rootProps:o},b.createElement(H,{customPagesPortals:a})))},{component:"OrganizationProfile",renderWhileLoading:!0}),{Page:el,Link:eu}),ed=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"CreateOrganization",renderWhileLoading:!0}),eh=(0,b.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),ep=Object.assign((0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:a}=R(i.children,{allowForAnyChildren:!!i.__experimental_asProvider}),l=Object.assign(i.organizationProfileProps||{},{customPages:s}),u=T(i.children),c={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...i,organizationProfileProps:l},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),b.createElement(eh.Provider,{value:c},b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{...c,hideRootHtmlElement:!!i.__experimental_asProvider},i.__experimental_asProvider?u:null,b.createElement(H,{customPagesPortals:a}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0}),{OrganizationProfilePage:el,OrganizationProfileLink:eu,__experimental_Outlet:function(e){let t=(0,b.useContext)(eh),r={...t,props:{...t.props,...e}};return b.createElement(Z,{...r})}}),eg=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"OrganizationList",renderWhileLoading:!0}),ef=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"GoogleOneTap",renderWhileLoading:!0}),em=(0,c.Q)(({clerk:e,component:t,fallback:r,...i})=>{let n="rendering"===J(t)||!e.loaded,o={...n&&r&&{style:{display:"none"}}};return b.createElement(b.Fragment,null,n&&r,e.loaded&&b.createElement(Z,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:i,rootProps:o}))},{component:"Waitlist",renderWhileLoading:!0}),ev=(0,c.Q)(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:i,forceRedirectUrl:n,fallbackRedirectUrl:o,signUpForceRedirectUrl:s,mode:a,initialValues:l,withSignUp:u,oauthFlow:c,...d}=r,h=E(t=U(t,"Sign in"))("SignInButton"),p=()=>{let t={forceRedirectUrl:n,fallbackRedirectUrl:o,signUpFallbackRedirectUrl:i,signUpForceRedirectUrl:s,initialValues:l,withSignUp:u,oauthFlow:c};return"modal"===a?e.openSignIn({...t,appearance:r.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:o,signInForceRedirectUrl:n})},g=async e=>(h&&"object"==typeof h&&"props"in h&&await M(h.props.onClick)(e),p()),f={...d,onClick:g};return b.cloneElement(h,f)},{component:"SignInButton",renderWhileLoading:!0}),ek=(0,c.Q)(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:i,forceRedirectUrl:n,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,mode:a,unsafeMetadata:l,initialValues:u,oauthFlow:c,...d}=r,h=E(t=U(t,"Sign up"))("SignUpButton"),p=()=>{let t={fallbackRedirectUrl:i,forceRedirectUrl:n,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,unsafeMetadata:l,initialValues:u,oauthFlow:c};return"modal"===a?e.openSignUp({...t,appearance:r.appearance}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:i,signUpForceRedirectUrl:n})},g=async e=>(h&&"object"==typeof h&&"props"in h&&await M(h.props.onClick)(e),p()),f={...d,onClick:g};return b.cloneElement(h,f)},{component:"SignUpButton",renderWhileLoading:!0}),eb=(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:i="/",signOutOptions:n,...o}=r,s=E(t=U(t,"Sign out"))("SignOutButton"),a=()=>e.signOut({redirectUrl:i,...n}),l=async e=>(await M(s.props.onClick)(e),a()),u={...o,onClick:l};return b.cloneElement(s,u)},{component:"SignOutButton",renderWhileLoading:!0}),ey=(0,c.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:i,...n}=r,o=E(t=U(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:i||void 0})}()},a=async e=>(await M(o.props.onClick)(e),s()),l={...n,onClick:a};return b.cloneElement(o,l)},{component:"SignInWithMetamask",renderWhileLoading:!0});void 0===globalThis.__BUILD_DISABLE_RHC__&&(globalThis.__BUILD_DISABLE_RHC__=!1);var ew={name:"@clerk/clerk-react",version:"5.25.5",environment:"production"},eS=class e{constructor(e){g(this,l),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],g(this,i,!1),g(this,n),g(this,o),g(this,s),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignInUrl(...e))||""};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("buildAfterSignInUrl",t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignUpUrl(...e))||""};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("buildAfterSignUpUrl",t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildWaitlistUrl())||""};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("buildWaitlistUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&p(this,i)?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.hydrateClerkJS=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__internal_openReverification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),null!==this.preOpenWaitlist&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),this.premountWaitlistNodes.forEach((t,r)=>{e.mountWaitlist(r,t)}),this.premountPricingTableNodes.forEach((t,r)=>{e.__experimental_mountPricingTable(r,t)}),f(this,i,!0),this.emitLoaded(),this.clerkjs},this.__unstable__updateProps=async e=>{let t=await m(this,l,u).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.__experimental_nextTask=async e=>this.clerkjs?this.clerkjs.__experimental_nextTask(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openReverification=e=>{this.clerkjs&&p(this,i)?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&p(this,i)?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&p(this,i)?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&p(this,i)?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.__experimental_mountPricingTable=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.__experimental_mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.__experimental_unmountPricingTable=e=>{this.clerkjs&&p(this,i)?this.clerkjs.__experimental_unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&p(this,i)?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&p(this,i)?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&p(this,i)?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&p(this,i)?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};return this.clerkjs&&p(this,i)?t():void this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};return this.clerkjs&&p(this,i)?t():void this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};return this.clerkjs&&p(this,i)?t():void this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};return this.clerkjs&&p(this,i)?e():void this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&p(this,i))return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&p(this,i)?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&p(this,i)?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};return this.clerkjs&&p(this,i)?e():void this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};return this.clerkjs&&p(this,i)?e():void this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToWaitlist()};return this.clerkjs&&p(this,i)?e():void this.premountMethodCalls.set("redirectToWaitlist",e)},this.handleRedirectCallback=async e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&p(this,i)?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=async(e,t)=>{var r;let n=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&p(this,i)?null==(r=n())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",n)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithOKXWallet(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("authenticateWithOKXWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await m(this,l,u).call(this)).authenticateWithGoogleOneTap(e),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("getOrganization",t)},this.joinWaitlist=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.joinWaitlist(e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("joinWaitlist",t)},this.signOut=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.signOut(...e)};if(this.clerkjs&&p(this,i))return t();this.premountMethodCalls.set("signOut",t)};let{Clerk:t=null,publishableKey:r}=e||{};f(this,s,r),f(this,o,null==e?void 0:e.proxyUrl),f(this,n,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=(0,j.M)()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=ew),p(this,s)&&this.loadClerkJS()}get publishableKey(){return p(this,s)}get loaded(){return p(this,i)}static getOrCreateInstance(t){return(0,j.M)()&&p(this,a)&&(!t.Clerk||p(this,a).Clerk===t.Clerk)&&p(this,a).publishableKey===t.publishableKey||f(this,a,new e(t)),p(this,a)}static clearInstance(){f(this,a,null)}get domain(){return"undefined"!=typeof window&&window.location?(0,k.VK)(p(this,n),new URL(window.location.href),""):"function"==typeof p(this,n)?c.sb.throw(c.Vo):p(this,n)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?(0,k.VK)(p(this,o),new URL(window.location.href),""):"function"==typeof p(this,o)?c.sb.throw(c.Vo):p(this,o)||""}__internal_getOption(e){var t;return null==(t=this.clerkjs)?void 0:t.__internal_getOption(e)}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?(0,k.VK)(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&c.sb.throw(c.Vo)}async loadClerkJS(){var e,t;if(!("browser"!==this.mode||p(this,i))){"undefined"!=typeof window&&(window.__clerk_publishable_key=p(this,s),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(p(this,s),{proxyUrl:this.proxyUrl,domain:this.domain}),await e.load(this.options)):(e=this.Clerk).loaded||await e.load(this.options),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await (0,v._R)({...this.options,publishableKey:p(this,s),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}get isSignedIn(){return!!this.clerkjs&&this.clerkjs.isSignedIn}get __experimental_commerce(){var e;return null==(e=this.clerkjs)?void 0:e.__experimental_commerce}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};function e_(e){let{isomorphicClerkOptions:t,initialState:r,children:i}=e,{isomorphicClerk:n,loaded:o}=eP(t),[s,a]=b.useState({client:n.client,session:n.session,user:n.user,organization:n.organization});b.useEffect(()=>n.addListener(e=>a({...e})),[]);let l=P(o,s,r),u=b.useMemo(()=>({value:n}),[o]),d=b.useMemo(()=>({value:s.client}),[s.client]),{sessionId:h,sessionStatus:p,session:g,userId:f,user:m,orgId:v,actor:k,organization:y,orgRole:w,orgSlug:_,orgPermissions:O,factorVerificationAge:C}=l,j=b.useMemo(()=>({value:{sessionId:h,sessionStatus:p,userId:f,actor:k,orgId:v,orgRole:w,orgSlug:_,orgPermissions:O,factorVerificationAge:C}}),[h,p,f,k,v,w,_,C]),E=b.useMemo(()=>({value:g}),[h,g]),U=b.useMemo(()=>({value:m}),[f,m]),M=b.useMemo(()=>({value:{organization:y}}),[v,y]);return b.createElement(c.SW.Provider,{value:u},b.createElement(S.pc.Provider,{value:d},b.createElement(S.IC.Provider,{value:E},b.createElement(S.TS,{...M.value},b.createElement(c.cy.Provider,{value:j},b.createElement(S.Rs.Provider,{value:U},i))))))}i=new WeakMap,n=new WeakMap,o=new WeakMap,s=new WeakMap,a=new WeakMap,l=new WeakSet,u=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},g(eS,a);var eP=e=>{let[t,r]=b.useState(!1),i=b.useMemo(()=>eS.getOrCreateInstance(e),[]);return b.useEffect(()=>{i.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),b.useEffect(()=>{i.__unstable__updateProps({options:e})},[e.localization]),b.useEffect(()=>{i.addOnLoaded(()=>r(!0))},[]),b.useEffect(()=>()=>{eS.clearInstance(),r(!1)},[]),{isomorphicClerk:i,loaded:t}},eO=function(e,t,r){let i=e.displayName||e.name||t||"Component",n=i=>(!function(e,t,r=1){b.useEffect(()=>{let i=z.get(e)||0;return i==r?c.sb.throw(t):(z.set(e,i+1),()=>{z.set(e,(z.get(e)||1)-1)})},[])}(t,r),b.createElement(e,{...i}));return n.displayName=`withMaxAllowedInstancesGuard(${i})`,n}(function(e){let{initialState:t,children:r,__internal_bypassMissingPublishableKey:i,...n}=e,{publishableKey:o="",Clerk:s}=n;return s||i||(o?o&&!(0,_.rA)(o)&&c.sb.throwInvalidPublishableKeyError({key:o}):c.sb.throwMissingPublishableKeyError()),b.createElement(e_,{initialState:t,isomorphicClerkOptions:n},r)},"ClerkProvider",c.yN);eO.displayName="ClerkProvider",(0,c.wV)({packageName:"@clerk/clerk-react"}),(0,v.kX)("@clerk/clerk-react")},89081:(e,t,r)=>{function i(e){return"clerkError"in e}r.d(t,{$R:()=>i,_r:()=>s,cR:()=>n});var n=class e extends Error{constructor(t,{code:r}){let i="\uD83D\uDD12 Clerk:",n=RegExp(i.replace(" ","\\s*"),"i"),o=t.replace(n,""),s=`${i} ${o.trim()}

(code="${r}")
`;super(s),this.toString=()=>`[${this.name}]
Message:${this.message}`,Object.setPrototypeOf(this,e.prototype),this.code=r,this.message=s,this.clerkRuntimeError=!0,this.name="ClerkRuntimeError"}},o=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function s({packageName:e,customMessages:t}){let r=e,i={...o,...t};function n(e,t){if(!t)return`${r}: ${e}`;let i=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();i=i.replace(`{{${r[1]}}}`,e)}return`${r}: ${i}`}return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(n(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(n(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(n(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(n(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(n(i.MissingClerkProvider,e))},throw(e){throw Error(n(e))}}}}}]);