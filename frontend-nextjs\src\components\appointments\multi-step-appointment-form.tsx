'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { format } from 'date-fns'
import { CalendarIcon, ChevronLeft, ChevronRight, Check } from 'lucide-react'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { appointmentsApi, patientsApi, treatmentsApi } from '@/lib/api'
import { Appointment, Patient, Treatment, User } from '@/types/clinic'
import { toast } from 'sonner'
import { formatApiError, formatSuccessMessage, formatLoadingMessage } from '@/lib/error-utils'

// Multi-step form schema
const appointmentSchema = z.object({
  appointmentType: z.enum(['consultation', 'treatment']).default('treatment'),
  patientId: z.string().min(1, 'Please select a patient'),
  treatmentId: z.string().min(1, 'Please select a treatment'),
  practitionerId: z.string().min(1, 'Please select a practitioner'),
  appointmentDate: z.date({
    required_error: 'Please select an appointment date and time',
  }),
  price: z.number().min(0, 'Price must be positive'),
  durationInMinutes: z.number().min(5, 'Duration must be at least 5 minutes'),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'no-show']).default('scheduled'),
})

type AppointmentFormData = z.infer<typeof appointmentSchema>

interface MultiStepAppointmentFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  appointment?: Appointment
  onSuccess?: () => void
}

const STEPS = [
  { id: 1, title: 'Select Patient', description: 'Choose the patient for this appointment' },
  { id: 2, title: 'Choose Treatment', description: 'Select the treatment to be performed' },
  { id: 3, title: 'Assign Practitioner', description: 'Choose the practitioner for this treatment' },
  { id: 4, title: 'Schedule Time', description: 'Pick the date and time for the appointment' },
  { id: 5, title: 'Review & Confirm', description: 'Review all details before confirming' },
]

export function MultiStepAppointmentForm({
  open,
  onOpenChange,
  appointment,
  onSuccess,
}: MultiStepAppointmentFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [patients, setPatients] = useState<Patient[]>([])
  const [treatments, setTreatments] = useState<Treatment[]>([])
  const [practitioners, setPractitioners] = useState<User[]>([])
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null)
  const [selectedTreatment, setSelectedTreatment] = useState<Treatment | null>(null)
  const [selectedPractitioner, setSelectedPractitioner] = useState<User | null>(null)

  const isEditing = !!appointment
  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      appointmentType: 'treatment',
      patientId: '',
      treatmentId: '',
      practitionerId: '',
      appointmentDate: new Date(),
      price: 0,
      durationInMinutes: 30,
      status: 'scheduled',
    },
  })

  // Load data when dialog opens
  useEffect(() => {
    if (open) {
      loadFormData()
      if (appointment) {
        // Pre-populate form for editing
        form.reset({
          appointmentType: appointment.appointmentType || 'treatment',
          patientId: appointment.patient.id,
          treatmentId: appointment.treatment?.id || '',
          practitionerId: appointment.practitioner.id,
          appointmentDate: new Date(appointment.appointmentDate),
          price: appointment.price,
          durationInMinutes: appointment.durationInMinutes,
          status: appointment.status,
        })
        // Set selected items for display
        setSelectedPatient(appointment.patient)
        setSelectedTreatment(appointment.treatment || null)
        setSelectedPractitioner(appointment.practitioner)
      } else {
        // Reset for new appointment
        setCurrentStep(1)
        setSelectedPatient(null)
        setSelectedTreatment(null)
        setSelectedPractitioner(null)
        form.reset()
      }
    }
  }, [open, appointment, form])

  const loadFormData = async () => {
    try {
      const [patientsRes, treatmentsRes, practitionersRes] = await Promise.all([
        patientsApi.getAll(),
        treatmentsApi.getAll(),
        // TODO: Add API call to get practitioners/users
        Promise.resolve({ docs: [] as User[] }),
      ])
      
      setPatients(patientsRes.docs || [])
      setTreatments(treatmentsRes.docs || [])
      setPractitioners(practitionersRes.docs || [])
    } catch (error) {
      console.error('Failed to load form data:', error)
      toast.error('Failed to load form data')
    }
  }

  const handleNext = () => {
    const currentStepValid = validateCurrentStep()
    if (currentStepValid && currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        return !!form.getValues('patientId')
      case 2:
        return !!form.getValues('treatmentId')
      case 3:
        return !!form.getValues('practitionerId')
      case 4:
        return !!form.getValues('appointmentDate')
      default:
        return true
    }
  }

  const onSubmit = async (data: AppointmentFormData) => {
    setLoading(true)
    
    const loadingToast = toast.loading(
      formatLoadingMessage(isEditing ? 'update' : 'create', 'appointment')
    )
    
    try {
      const appointmentData = {
        appointmentType: data.appointmentType,
        patient: data.patientId,
        treatment: data.treatmentId,
        practitioner: data.practitionerId,
        appointmentDate: data.appointmentDate.toISOString(),
        price: data.price,
        durationInMinutes: data.durationInMinutes,
        status: data.status,
      }

      if (isEditing) {
        await appointmentsApi.update(appointment.id, appointmentData)
        toast.success(formatSuccessMessage('update', 'appointment'), { id: loadingToast })
      } else {
        await appointmentsApi.create(appointmentData)
        toast.success(formatSuccessMessage('create', 'appointment'), { id: loadingToast })
      }

      onSuccess?.()
      onOpenChange(false)
      form.reset()
      setCurrentStep(1)
    } catch (error) {
      console.error('Failed to save appointment:', error)
      const errorMessage = formatApiError(error)
      toast.error(errorMessage, { id: loadingToast })
    } finally {
      setLoading(false)
    }
  }

  const handlePatientSelect = (patientId: string) => {
    form.setValue('patientId', patientId)
    const patient = patients.find(p => p.id === patientId)
    setSelectedPatient(patient || null)
  }

  const handleTreatmentSelect = (treatmentId: string) => {
    form.setValue('treatmentId', treatmentId)
    const treatment = treatments.find(t => t.id === treatmentId)
    setSelectedTreatment(treatment || null)
    
    // Auto-fill price and duration
    if (treatment) {
      form.setValue('price', treatment.defaultPrice)
      form.setValue('durationInMinutes', treatment.defaultDurationInMinutes)
    }
  }

  const handlePractitionerSelect = (practitionerId: string) => {
    form.setValue('practitionerId', practitionerId)
    const practitioner = practitioners.find(p => p.id === practitionerId)
    setSelectedPractitioner(practitioner || null)
  }

  const getStepStatus = (stepId: number) => {
    if (stepId < currentStep) return 'completed'
    if (stepId === currentStep) return 'current'
    return 'upcoming'
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Appointment' : 'New Appointment'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the appointment details below.' 
              : 'Follow the steps below to create a new appointment.'
            }
          </DialogDescription>
        </DialogHeader>

        {/* Progress Steps */}
        <div className="py-4">
          <div className="flex items-center justify-between">
            {STEPS.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      'flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium',
                      getStepStatus(step.id) === 'completed' && 'bg-primary border-primary text-primary-foreground',
                      getStepStatus(step.id) === 'current' && 'border-primary text-primary',
                      getStepStatus(step.id) === 'upcoming' && 'border-muted-foreground text-muted-foreground'
                    )}
                  >
                    {getStepStatus(step.id) === 'completed' ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      step.id
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <p className={cn(
                      'text-xs font-medium',
                      getStepStatus(step.id) === 'current' && 'text-primary',
                      getStepStatus(step.id) !== 'current' && 'text-muted-foreground'
                    )}>
                      {step.title}
                    </p>
                  </div>
                </div>
                {index < STEPS.length - 1 && (
                  <div className={cn(
                    'h-0.5 w-12 mx-2 mt-[-20px]',
                    getStepStatus(step.id) === 'completed' ? 'bg-primary' : 'bg-muted'
                  )} />
                )}
              </div>
            ))}
          </div>
        </div>

        <Separator />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Step 1: Select Patient */}
            {currentStep === 1 && (
              <Card>
                <CardHeader>
                  <CardTitle>Select Patient</CardTitle>
                  <CardDescription>Choose the patient for this appointment</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="patientId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Patient *</FormLabel>
                        <Select onValueChange={handlePatientSelect} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a patient" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {patients.map((patient) => (
                              <SelectItem key={patient.id} value={patient.id}>
                                <div className="flex flex-col">
                                  <span className="font-medium">{patient.fullName}</span>
                                  <span className="text-sm text-muted-foreground">{patient.phone}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {selectedPatient && (
                    <div className="mt-4 p-4 bg-muted rounded-lg">
                      <h4 className="font-medium mb-2">Selected Patient</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Name:</strong> {selectedPatient.fullName}</p>
                        <p><strong>Phone:</strong> {selectedPatient.phone}</p>
                        {selectedPatient.email && <p><strong>Email:</strong> {selectedPatient.email}</p>}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 2: Choose Treatment */}
            {currentStep === 2 && (
              <Card>
                <CardHeader>
                  <CardTitle>Choose Treatment</CardTitle>
                  <CardDescription>Select the treatment to be performed</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="treatmentId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Treatment *</FormLabel>
                        <Select onValueChange={handleTreatmentSelect} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a treatment" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {treatments.map((treatment) => (
                              <SelectItem key={treatment.id} value={treatment.id}>
                                <div className="flex flex-col">
                                  <span className="font-medium">{treatment.name}</span>
                                  <span className="text-sm text-muted-foreground">
                                    ${treatment.defaultPrice} • {treatment.defaultDurationInMinutes} min
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {selectedTreatment && (
                    <div className="mt-4 p-4 bg-muted rounded-lg">
                      <h4 className="font-medium mb-2">Selected Treatment</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Name:</strong> {selectedTreatment.name}</p>
                        <p><strong>Price:</strong> ${selectedTreatment.defaultPrice}</p>
                        <p><strong>Duration:</strong> {selectedTreatment.defaultDurationInMinutes} minutes</p>
                        {selectedTreatment.description && (
                          <p><strong>Description:</strong> {selectedTreatment.description}</p>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 3: Assign Practitioner */}
            {currentStep === 3 && (
              <Card>
                <CardHeader>
                  <CardTitle>Assign Practitioner</CardTitle>
                  <CardDescription>Choose the practitioner for this treatment</CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="practitionerId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Practitioner *</FormLabel>
                        <Select onValueChange={handlePractitionerSelect} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a practitioner" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {practitioners.map((practitioner) => (
                              <SelectItem key={practitioner.id} value={practitioner.id}>
                                <div className="flex flex-col">
                                  <span className="font-medium">
                                    {practitioner.firstName} {practitioner.lastName}
                                  </span>
                                  <span className="text-sm text-muted-foreground">
                                    {practitioner.role === 'doctor' ? 'Doctor' : 'Practitioner'}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {selectedPractitioner && (
                    <div className="mt-4 p-4 bg-muted rounded-lg">
                      <h4 className="font-medium mb-2">Selected Practitioner</h4>
                      <div className="space-y-1 text-sm">
                        <p><strong>Name:</strong> {selectedPractitioner.firstName} {selectedPractitioner.lastName}</p>
                        <p><strong>Role:</strong> {selectedPractitioner.role === 'doctor' ? 'Doctor' : 'Practitioner'}</p>
                        {selectedPractitioner.email && <p><strong>Email:</strong> {selectedPractitioner.email}</p>}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Step 4: Schedule Time */}
            {currentStep === 4 && (
              <Card>
                <CardHeader>
                  <CardTitle>Schedule Time</CardTitle>
                  <CardDescription>Pick the date and time for the appointment</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="appointmentDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Appointment Date & Time *</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full pl-3 text-left font-normal',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                {field.value ? (
                                  format(field.value, 'PPP p')
                                ) : (
                                  <span>Pick a date and time</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) => date < new Date()}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Price ($)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              min="0"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="durationInMinutes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Duration (minutes)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="5"
                              max="480"
                              placeholder="30"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Step 5: Review & Confirm */}
            {currentStep === 5 && (
              <Card>
                <CardHeader>
                  <CardTitle>Review & Confirm</CardTitle>
                  <CardDescription>Review all details before confirming the appointment</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Patient Summary */}
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Badge variant="outline">1</Badge>
                        Patient
                      </h4>
                      {selectedPatient && (
                        <div className="pl-8 space-y-1 text-sm">
                          <p><strong>Name:</strong> {selectedPatient.fullName}</p>
                          <p><strong>Phone:</strong> {selectedPatient.phone}</p>
                          {selectedPatient.email && <p><strong>Email:</strong> {selectedPatient.email}</p>}
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Treatment Summary */}
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Badge variant="outline">2</Badge>
                        Treatment
                      </h4>
                      {selectedTreatment && (
                        <div className="pl-8 space-y-1 text-sm">
                          <p><strong>Name:</strong> {selectedTreatment.name}</p>
                          <p><strong>Price:</strong> ${form.getValues('price')}</p>
                          <p><strong>Duration:</strong> {form.getValues('durationInMinutes')} minutes</p>
                          {selectedTreatment.description && (
                            <p><strong>Description:</strong> {selectedTreatment.description}</p>
                          )}
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Practitioner Summary */}
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Badge variant="outline">3</Badge>
                        Practitioner
                      </h4>
                      {selectedPractitioner && (
                        <div className="pl-8 space-y-1 text-sm">
                          <p><strong>Name:</strong> {selectedPractitioner.firstName} {selectedPractitioner.lastName}</p>
                          <p><strong>Role:</strong> {selectedPractitioner.role === 'doctor' ? 'Doctor' : 'Practitioner'}</p>
                        </div>
                      )}
                    </div>

                    <Separator />

                    {/* Schedule Summary */}
                    <div>
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <Badge variant="outline">4</Badge>
                        Schedule
                      </h4>
                      <div className="pl-8 space-y-1 text-sm">
                        <p><strong>Date & Time:</strong> {format(form.getValues('appointmentDate'), 'PPP p')}</p>
                        <p><strong>Duration:</strong> {form.getValues('durationInMinutes')} minutes</p>
                        <p><strong>Status:</strong> {form.getValues('status')}</p>
                      </div>
                    </div>

                    <Separator />

                    {/* Total Summary */}
                    <div className="bg-primary/5 p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Total Amount:</span>
                        <span className="text-lg font-bold">${form.getValues('price')}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Navigation Buttons */}
            <div className="flex justify-between pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
              
              {currentStep < STEPS.length ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={!validateCurrentStep()}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button type="submit" disabled={loading}>
                  {loading ? 'Saving...' : (isEditing ? 'Update Appointment' : 'Create Appointment')}
                </Button>
              )}
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
