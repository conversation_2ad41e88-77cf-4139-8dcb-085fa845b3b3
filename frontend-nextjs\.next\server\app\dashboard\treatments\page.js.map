{"version": 3, "file": "../app/dashboard/treatments/page.js", "mappings": "sbAAA,olBCAA,gSCeA,IAAMA,EAAkBC,EAAAA,EAAQ,CAAC,CAC/BC,KAAMD,EAAAA,EAAQ,GAAGE,GAAG,CAAC,EAAG,gBAAgBC,GAAG,CAAC,IAAK,kBAAkBC,KAAK,CAAC,wBAAyB,2BAClGC,YAAaL,EAAAA,EAAQ,GAAGG,GAAG,CAAC,IAAM,iBAAiBG,QAAQ,GAC3DC,aAAcP,EAAAA,EAAQ,GAAGE,GAAG,CAAC,EAAG,cAAcC,GAAG,CAAC,IAAO,iBAAiBK,UAAU,CAAC,IAAM,0BAC3FC,yBAA0BT,EAAAA,EAAQ,GAAGE,GAAG,CAAC,EAAG,aAAaC,GAAG,CAAC,IAAK,oBAAoBO,GAAG,CAAC,YAC5F,GAQO,SAASC,EAAoB,MAClCC,CAAI,CACJC,cAAY,WACZC,CAAS,WACTC,CAAS,CACgB,EACzB,GAAM,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjCC,EAAY,CAAC,CAACL,EACdM,EAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAoB,CACtCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACxB,GACtByB,cAAe,CACbvB,KAAMa,GAAWb,MAAQ,GACzBI,YAAaS,GAAWT,aAAe,GACvCE,aAAcO,GAAWP,cAAgB,EACzCE,yBAA0BK,GAAWL,0BAA4B,EACnE,CACF,GAaMgB,EAAW,MAAOC,IACtBT,GAAW,GACX,GAAI,CACF,IAAMU,EAAgB,CACpB1B,KAAMyB,EAAKzB,IAAI,CACfI,YAAaqB,EAAKrB,WAAW,OAAIuB,EACjCrB,aAAcmB,EAAKnB,YAAY,CAC/BE,yBAA0BiB,EAAKjB,wBAAwB,EAErDU,GACF,MAAMU,EAAAA,EAAaA,CAACC,MAAM,CAAChB,EAAUiB,EAAE,CAAEJ,GACzCK,EAAAA,KAAKA,CAACC,OAAO,CAAC,oCAEd,MAAMJ,EAAAA,EAAaA,CAACK,MAAM,CAACP,GAC3BK,EAAAA,KAAKA,CAACC,OAAO,CAAC,mCAEhBlB,MACAF,GAAa,GACbO,EAAKe,KAAK,EACZ,CAAE,MAAOC,EAAO,CACdC,QAAQD,KAAK,CAAC,4BAA6BA,GAC3CJ,EAAAA,KAAKA,CAACI,KAAK,CAAC,CAAC,UAAU,EAAEjB,EAAY,SAAW,SAAS,UAAU,CAAC,CACtE,QAAU,CACRF,GAAW,EACb,CACF,EACA,MAAO,UAACqB,EAAAA,EAAMA,CAAAA,CAAC1B,KAAMA,EAAMC,aAAcA,EAAc0B,sBAAoB,SAASC,wBAAsB,sBAAsBC,0BAAwB,qCACpJ,WAACC,EAAAA,EAAaA,CAAAA,CAACC,UAAU,mBAAmBJ,sBAAoB,gBAAgBE,0BAAwB,sCACtG,WAACG,EAAAA,EAAYA,CAAAA,CAACL,sBAAoB,eAAeE,0BAAwB,sCACvE,UAACI,EAAAA,EAAWA,CAAAA,CAACN,sBAAoB,cAAcE,0BAAwB,qCACpEtB,EAAY2B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,4BAA8BA,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,6BAEjD,UAACC,EAAAA,EAAiBA,CAAAA,CAACR,sBAAoB,oBAAoBE,0BAAwB,qCAChFtB,EAAY,aAAe,uBAIhC,UAAC6B,EAAAA,EAAIA,CAAAA,CAAE,GAAG5B,CAAI,CAAEmB,sBAAoB,OAAOE,0BAAwB,qCACjE,WAACrB,OAAAA,CAAKK,SAAUL,EAAK6B,YAAY,CAACxB,GAAWkB,UAAU,sBAErD,UAACO,EAAAA,EAASA,CAAAA,CAACC,QAAS/B,EAAK+B,OAAO,CAAElD,KAAK,OAAOmD,OAAQ,CAAC,OACvDC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,WAACC,EAAAA,EAASA,CAAAA,WAAET,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,wBAAwB,QACtC,UAACU,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACC,YAAaZ,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,mCAAqC,GAAGO,CAAK,KAErE,UAACM,EAAAA,EAAWA,CAAAA,CAAAA,MACDpB,sBAAoB,YAAYE,0BAAwB,8BAGzE,UAACS,EAAAA,EAASA,CAAAA,CAACC,QAAS/B,EAAK+B,OAAO,CAAElD,KAAK,cAAcmD,OAAQ,CAAC,OAC9DC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,UAACC,EAAAA,EAASA,CAAAA,UAAET,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,iCACd,UAACU,EAAAA,EAAWA,CAAAA,UACV,UAACI,EAAAA,CAAQA,CAAAA,CAACF,YAAaZ,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0CAA2CH,UAAU,eAAgB,GAAGU,CAAK,KAExG,UAACM,EAAAA,EAAWA,CAAAA,CAAAA,MACDpB,sBAAoB,YAAYE,0BAAwB,8BAEzE,WAACoB,MAAAA,CAAIlB,UAAU,mCAEb,UAACO,EAAAA,EAASA,CAAAA,CAACC,QAAS/B,EAAK+B,OAAO,CAAElD,KAAK,eAAemD,OAAQ,CAAC,OAC/DC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,WAACC,EAAAA,EAASA,CAAAA,WAAET,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yBAAyB,eACvC,UAACU,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACK,KAAK,SAASC,KAAK,OAAOL,YAAaZ,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,oCAAsC,GAAGO,CAAK,CAAEW,SAAUC,GAAKZ,EAAMW,QAAQ,CAACE,WAAWD,EAAEE,MAAM,CAACC,KAAK,GAAK,OAE9J,UAACT,EAAAA,EAAWA,CAAAA,CAAAA,MACDpB,sBAAoB,YAAYE,0BAAwB,8BAGzE,UAACS,EAAAA,EAASA,CAAAA,CAACC,QAAS/B,EAAK+B,OAAO,CAAElD,KAAK,2BAA2BmD,OAAQ,CAAC,OAC3EC,CAAK,CACN,GAAK,WAACC,EAAAA,EAAQA,CAAAA,WACP,WAACC,EAAAA,EAASA,CAAAA,WAAET,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,4BAA4B,aAC1C,UAACU,EAAAA,EAAWA,CAAAA,UACV,UAACC,EAAAA,CAAKA,CAAAA,CAACK,KAAK,SAASJ,YAAaZ,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAAyC,GAAGO,CAAK,CAAEW,SAAUC,GAAKZ,EAAMW,QAAQ,CAACK,SAASJ,EAAEE,MAAM,CAACC,KAAK,GAAK,OAEnJ,UAACT,EAAAA,EAAWA,CAAAA,CAAAA,MACDpB,sBAAoB,YAAYE,0BAAwB,iCAG3E,WAAC6B,EAAAA,EAAYA,CAAAA,CAAC/B,sBAAoB,eAAeE,0BAAwB,sCACvE,UAAC8B,EAAAA,CAAMA,CAAAA,CAACT,KAAK,SAASU,QAAQ,UAAUC,QAAS,IAAM5D,GAAa,GAAQ6D,SAAU1D,EAASuB,sBAAoB,SAASE,0BAAwB,qCACjJK,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,2BAEL,UAACyB,EAAAA,CAAMA,CAAAA,CAACT,KAAK,SAASY,SAAU1D,EAASuB,sBAAoB,SAASE,0BAAwB,qCAC3FzB,EAAU,SAAWG,EAAY,OAAS,qBAO3D,2BC5IO,SAASwD,IACd,GAAM,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAACC,EAAYC,EAAc,CAAG7D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAc,EAAE,EACtD,CAACF,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACkB,EAAO4C,EAAS,CAAG9D,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAAC+D,EAAgBC,EAAkB,CAAGhE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,CAACiE,EAAkBC,EAAoB,CAAGlE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAClD,CAACmE,EAAkBC,EAAoB,CAAGpE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACqE,EAAmBC,EAAqB,CAAGtE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GACpD,CAACuE,EAAeC,EAAiB,CAAGxE,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC7CyE,EAAkB,UACtB,GAAI,CACF1E,EAAW,IACX+D,EAAS,MACT,IAAMY,EAAW,MAAM/D,EAAAA,EAAaA,CAACgE,MAAM,CAAC,CAC1CC,MAAO,GACT,GACAf,EAAca,EAASG,IAAI,CAC7B,CAAE,MAAOC,EAAK,CACZ3D,QAAQD,KAAK,CAAC,8BAA+B4D,GAC7ChB,EAAS,qDACX,QAAU,CACR/D,GAAW,EACb,CACF,EAMMgF,EAAqB,KACzBb,OAAoBxD,GACpBsD,GAAkB,EACpB,EACMgB,EAAsB,IAC1Bd,EAAoBtE,GACpBoE,GAAkB,EACpB,EA0BMiB,EAAsB,UAC1B,GAAKZ,CAAD,EACJG,EAAiB,IACjB,GAAI,CACF,MAAM7D,EAAAA,EAAaA,CAACuE,MAAM,CAACb,EAAkBxD,EAAE,EAC/CC,EAAAA,KAAKA,CAACC,OAAO,CAAC,kCACdqD,GAAoB,GACpBE,OAAqB5D,GACrB+D,GACF,CAAE,MAAOvD,EAAO,CACdC,MAFmB,EAEXD,KAAK,CAAC,WAFwB,mBAEOA,GAC7CJ,EAAAA,KAAKA,CAACI,KAAK,CAAC,6BACd,QAAU,CACRsD,GAAiB,EACnB,EACF,EACMW,EAAoB,KACxBnB,GAAkB,GAClBE,EAAoBxD,QACpB+D,GACF,SACA,EACS,KAHY,EAER,CACJ,EAAC9B,MAAAA,CAAIlB,OAH0B,GAGhB,iDAClB,WAACkB,MAAAA,CAAIlB,UAAU,wBACb,UAACkB,MAAAA,CAAIlB,UAAU,6EACf,UAAC2D,IAAAA,CAAE3D,UAAU,iCAAyBG,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAI5CV,EACK,KADE,GACF,EAACyB,MAAAA,CAAIlB,UAAU,iDAClB,WAACkB,MAAAA,CAAIlB,UAAU,wBACb,UAAC2D,IAAAA,CAAE3D,UAAU,6BAAqBP,IAClC,UAACmC,EAAAA,CAAMA,CAAAA,CAACE,QAAS,IAAM8B,OAAOC,QAAQ,CAACC,MAAM,GAAIjC,QAAQ,mBAAU,YAMjD,GAAG,CAAzBM,EAAW4B,MAAM,CACZ,iCACH,UAAC7C,MAAAA,CAAIlB,UAAU,iDACb,WAACkB,MAAAA,CAAIlB,UAAU,wBACb,UAACgE,EAAAA,CAAeA,CAAAA,CAAChE,UAAU,iDAC3B,UAACiE,KAAAA,CAAGjE,UAAU,oCAA4BG,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,6BAC5C,UAACwD,IAAAA,CAAE3D,UAAU,sCAA6B,mBAG1C,UAACkE,EAAAA,EAAcA,CAAAA,CAACC,WAAW,+BACzB,WAACvC,EAAAA,CAAMA,CAAAA,CAACE,QAASwB,YACf,UAACc,EAAAA,CAAQA,CAAAA,CAACpE,UAAU,iBACnBG,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qCAMX,UAACnC,EAAmBA,CAACC,KAAMqE,EAAgBpE,SAAvBF,IAAqCuE,EAAmBpE,UAAWqE,EAAkBpE,UAAWsF,OAGnH,iCACH,WAACxC,MAAAA,CAAIlB,UAAU,sBAEb,WAACkB,MAAAA,CAAIlB,UAAU,8CACb,UAACkB,MAAAA,UACC,WAAC+C,KAAAA,CAAGjE,UAAU,gCACXmC,EAAW4B,MAAM,CAAC,IAAE5D,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,mCAG3B,UAAC+D,EAAAA,EAAcA,CAAAA,CAACC,WAAW,sBAAsBvE,sBAAoB,iBAAiBE,0BAAwB,+BAC5G,WAAC8B,EAAAA,CAAMA,CAAAA,CAACE,QAASwB,EAAoB1D,sBAAoB,SAASE,0BAAwB,gCACxF,UAACsE,EAAAA,CAAQA,CAAAA,CAACpE,UAAU,eAAeJ,sBAAoB,WAAWE,0BAAwB,wBACzFK,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,mCAMT,UAACe,MAAAA,CAAIlB,UAAU,oDACZmC,EAAWkC,GAAG,CAAClG,GAAa,WAAC+C,MAAAA,CAAuBlB,UAAU,4CAC3D,WAACkB,MAAAA,CAAIlB,UAAU,6CACb,WAACkB,MAAAA,CAAIlB,UAAU,sBACb,UAACsE,KAAAA,CAAGtE,UAAU,+BAAuB7B,EAAUb,IAAI,GAClDa,EAAUT,WAAW,EAAI,UAACiG,IAAAA,CAAE3D,UAAU,sDAClC7B,EAAUT,WAAW,MAG5B,UAACwG,EAAAA,EAAcA,CAAAA,CAACC,WAAW,6BACzB,WAACI,EAAAA,EAAYA,CAAAA,WACX,UAACC,EAAAA,EAAmBA,CAAAA,CAACC,OAAO,aAC1B,UAAC7C,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,QAAQ6C,KAAK,cAC3B,UAACC,EAAAA,CAAQA,CAAAA,CAAC3E,UAAU,gBAGxB,WAAC4E,EAAAA,EAAmBA,CAAAA,WAClB,WAACC,EAAAA,EAAgBA,CAAAA,CAAC/C,QAAS,IAAMyB,EAAoBpF,aACnD,UAACwG,EAAAA,CAAQA,CAAAA,CAAC3E,UAAU,iBACnBG,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0BAEL,WAAC0E,EAAAA,EAAgBA,CAAAA,CAAC/C,QAAS,KAC7Be,EAAqB1E,GACrBwE,GAAoB,EACtB,EAAG3C,UAAU,yBACP,UAAC8E,EAAAA,CAASA,CAAAA,CAAC9E,UAAU,iBACpBG,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAOb,UAACe,MAAAA,CAAIlB,UAAU,qDACb,WAACkB,MAAAA,CAAIlB,UAAU,sBACb,WAACkB,MAAAA,CAAIlB,UAAU,oCACb,UAAC+E,OAAAA,CAAK/E,UAAU,iCAAwB,QACxC,WAAC+E,OAAAA,CAAK/E,UAAU,wBAAc,OAAE7B,EAAUP,YAAY,EAAEoH,QAAQ,IAAM,YAEvE7G,EAAUL,wBAAwB,EAAI,WAACoD,MAAAA,CAAIlB,UAAU,oCAClD,UAAC+E,OAAAA,CAAK/E,UAAU,iCAAwB,QACxC,WAAC+E,OAAAA,WAAM5G,EAAUL,wBAAwB,CAAC,mBAxCfK,EAAUiB,EAAE,QAgDvD,UAACpB,EAAmBA,CAACC,KAAMqE,EAAgBpE,SAAvBF,IAAqCuE,EAAmBpE,UAAWqE,EAAkBpE,UAAWsF,EAAmB9D,sBAAoB,sBAAsBE,0BAAwB,wBAEzM,UAACmF,EAAAA,CAAkBA,CAAAA,CAAChH,KAAMyE,EAAkBxE,aAAcyE,EAAqBuC,MAAM,SAASxH,YAAa,CAAC,OAAO,EAAEkF,GAAmBtF,KAAK,WAAW,CAAC,CAAE6H,YAAY,KAAKtD,QAAQ,cAAcuD,UAAW5B,EAAqBnF,QAASyE,EAAelD,sBAAoB,qBAAqBE,0BAAwB,0BAEjU,0BClNA,qGCAA,mECAA,0GCAA,qDCAA,oMCQA,IAAMO,EAAOgF,EAAAA,EAAYA,CAInBC,EAAmBC,EAAAA,aAAmB,CAAwB,CAAC,GAC/DhF,EAAY,CAAkH,CAClI,GAAGiF,EACkC,GAC9B,UAACF,EAAiBG,QAAQ,EAAChE,MAAO,CACvCnE,KAAMkI,EAAMlI,IAAI,EACfsC,sBAAoB,4BAA4BC,wBAAsB,YAAYC,0BAAwB,oBACzG,UAAC4F,EAAAA,EAAUA,CAAAA,CAAE,GAAGF,CAAK,CAAE5F,sBAAoB,aAAaE,0BAAwB,eAGhF6F,EAAe,KACnB,IAAMC,EAAeL,EAAAA,UAAgB,CAACD,GAChCO,EAAcN,EAAAA,UAAgB,CAACO,GAC/B,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,GACZC,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,CAAC,CAC7B5I,KAAMsI,EAAatI,IACrB,GACM6I,EAAaJ,EAAcH,EAAatI,IAAI,CAAE2I,GACpD,GAAI,CAACL,EACH,MAAM,MADW,kDAGnB,GAAM,IACJxG,CAAE,CACH,CAAGyG,EACJ,MAAO,IACLzG,EACA9B,KAAMsI,EAAatI,IAAI,CACvB8I,WAAY,GAAGhH,EAAG,UAAU,CAAC,CAC7BiH,kBAAmB,GAAGjH,EAAG,sBAAsB,CAAC,CAChDkH,cAAe,GAAGlH,EAAG,kBAAkB,CAAC,CACxC,GAAG+G,CAAU,CAEjB,EAIML,EAAkBP,EAAAA,aAAmB,CAAuB,CAAC,GACnE,SAAS5E,EAAS,WAChBX,CAAS,CACT,GAAGwF,EACyB,EAC5B,IAAMpG,EAAKmG,EAAAA,KAAW,GACtB,MAAO,UAACO,EAAgBL,QAAQ,EAAChE,MAAO,IACtCrC,CACF,EAAGQ,sBAAoB,2BAA2BC,wBAAsB,WAAWC,0BAAwB,oBACvG,UAACoB,MAAAA,CAAIqF,YAAU,YAAYvG,UAAWwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAcxG,GAAa,GAAGwF,CAAK,IAElF,CACA,SAAS5E,EAAU,WACjBZ,CAAS,CACT,GAAGwF,EAC8C,EACjD,GAAM,CACJ/F,OAAK,YACL2G,CAAU,CACX,CAAGT,IACJ,MAAO,UAACc,EAAAA,CAAKA,CAAAA,CAACF,YAAU,aAAaG,aAAY,CAAC,CAACjH,EAAOO,UAAWwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCxG,GAAY2G,QAASP,EAAa,GAAGZ,CAAK,CAAE5F,sBAAoB,QAAQC,wBAAsB,YAAYC,0BAAwB,YAClP,CACA,SAASe,EAAY,CACnB,GAAG2E,EAC+B,EAClC,GAAM,OACJ/F,CAAK,YACL2G,CAAU,mBACVC,CAAiB,eACjBC,CAAa,CACd,CAAGX,IACJ,MAAO,UAACiB,EAAAA,EAAIA,CAAAA,CAACL,YAAU,eAAenH,GAAIgH,EAAYS,mBAAkB,EAAkC,GAAGR,EAAkB,CAAC,EAAEC,EAAAA,CAAe,CAAhE,GAAGD,EAAAA,CAAmB,CAA4CS,eAAc,CAAC,CAACrH,EAAQ,GAAG+F,CAAK,CAAE5F,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,YAC9Q,CACA,SAASiH,EAAgB,WACvB/G,CAAS,CACT,GAAGwF,EACuB,EAC1B,GAAM,mBACJa,CAAiB,CAClB,CAAGV,IACJ,MAAO,UAAChC,IAAAA,CAAE4C,YAAU,mBAAmBnH,GAAIiH,EAAmBrG,UAAWwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCxG,GAAa,GAAGwF,CAAK,CAAE3F,wBAAsB,kBAAkBC,0BAAwB,YACtM,CACA,SAASkB,EAAY,WACnBhB,CAAS,CACT,GAAGwF,EACuB,EAC1B,GAAM,OACJ/F,CAAK,eACL6G,CAAa,CACd,CAAGX,IACEqB,EAAOvH,EAAQwH,OAAOxH,GAAOyH,SAAW,IAAM1B,EAAM2B,QAAQ,QAC7DH,EAGE,EAHH,CAGG,CAHI,CAGJ,KAACrD,IAAAA,CAAE4C,YAAU,eAAenH,GAAIkH,EAAetG,UAAWwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BxG,GAAa,GAAGwF,CAAK,CAAE3F,wBAAsB,cAAcC,0BAAwB,oBAC9KkH,IAHI,IAKX,0BC3GA,oDCAA,iDCAA,iDCAA,wGCAA,6VCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,aACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAsI,CAoB1J,qGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,wGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,kCACA,iCAEA,cACA,YACA,WACA,CAAK,CACL,UACA,YACA,CACA,CAAC,0BC5FD,kDCAA,iECAA,sDCAA,yGCcO,SAAS/B,EAAmB,MACjChH,CAAI,cACJC,CAAY,OACZgH,CAAK,aACLxH,CAAW,CACXyH,cAAc,IAAI,YAClBiC,EAAa,IAAI,SACjBvF,EAAU,SAAS,WACnBuD,CAAS,SACT/G,GAAU,CAAK,CACS,EACxB,MAAO,UAACgJ,EAAAA,EAAWA,CAAAA,CAACpJ,KAAMA,EAAMC,aAAcA,EAAc0B,sBAAoB,cAAcC,wBAAsB,qBAAqBC,0BAAwB,mCAC7J,WAACwH,EAAAA,EAAkBA,CAAAA,CAAC1H,sBAAoB,qBAAqBE,0BAAwB,oCACnF,WAACyH,EAAAA,EAAiBA,CAAAA,CAAC3H,sBAAoB,oBAAoBE,0BAAwB,oCACjF,UAAC0H,EAAAA,EAAgBA,CAAAA,CAAC5H,sBAAoB,mBAAmBE,0BAAwB,mCAA2BoF,IAC5G,UAACuC,EAAAA,EAAsBA,CAAAA,CAAC7H,sBAAoB,yBAAyBE,0BAAwB,mCAA2BpC,OAE1H,WAACgK,EAAAA,EAAiBA,CAAAA,CAAC9H,sBAAoB,oBAAoBE,0BAAwB,oCACjF,UAAC6H,EAAAA,EAAiBA,CAAAA,CAAC5F,SAAU1D,EAASuB,sBAAoB,oBAAoBE,0BAAwB,mCAA2BsH,IACjI,UAACQ,EAAAA,EAAiBA,CAAAA,CAAC9F,QAASsD,EAAWrD,SAAU1D,EAAS2B,UAAuB,gBAAZ6B,EAA4B,qEAAuE,GAAIjC,sBAAoB,oBAAoBE,0BAAwB,mCACzOzB,EAAU,SAAW8G,WAKlC,0BCvCA,sDCAA,wDCAA,qDCAA,+DCAA,sCAAiL,CAEjL,uCAAiK,wBCFjK,qDCAA,kECAA,yDCAA,gDCAA,sCAAiL,CAEjL,uCAAiK,yBCFjK,6GCAA,+DGmBI,sBAAsB,+IFlB1B,EAAe,cAAqB,UAAW,aAAe,mBAAmB,CAAC,CAAC,OAAO,CAAC,EAAI,yEAA0E,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,0BAA0B,CAAM,UAAQ,GAAE,CAAC,OAAO,CAAC,EAAI,UAAU,IAAM,CAAO,OAAC,EAAE,CAAC,OAAO,CAAC,EAAI,QAAS,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,2CAA2C,CAAM,UAAQ,EAAC,CAAC,kCCMtX,eAAe0C,IAC5B,GAAM,OADsBA,CAE1BC,CAAM,CAFoBD,CAGxB,MAAME,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,QACLD,EAGEE,CAAAA,CAHH,CAGGA,CAHM,CAGNA,GAAAA,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAcrI,qBAAoB,iBAAgBC,uBAAsB,kBAAiBC,yBAAwB,YACrH,SAAAoI,CAAAA,EAAAA,EAAAA,IAAAA,CAAChH,CAAAA,KAAAA,CAAAA,CAAIlB,SAAU,4CAEbgI,CAAAA,EAAAA,EAAAA,GAAAA,CAAC9G,CAAAA,KAAAA,CAAAA,CAAIlB,SAAU,qCACb,SAAAkI,CAAAA,EAAAA,EAAAA,IAAAA,CAAChH,CAAAA,KAAAA,CAAAA,WACCgH,CAAAA,EAAAA,EAAAA,IAAAA,CAACC,CAAAA,IAAAA,CAAAA,CAAGnI,SAAU,uEACZgI,CAAAA,EAAAA,EAAAA,GAAAA,CAAChE,CAAAA,EAAAA,CAAgBhE,SAAU,GAA1BgE,OAAmCpE,qBAAoB,mBAAkBE,yBAAwB,cACjGK,CAAAA,EAAAA,EAAAA,CAAAA,CAAE,wBAEL6H,CAAAA,EAAAA,EAAAA,GAAAA,CAACrE,CAAAA,GAAAA,CAAAA,CAAE3D,SAAU,kCACVG,CAAAA,EAAAA,EAAAA,CAAAA,CAAE,+BAMT6H,CAAAA,EAAAA,EAAAA,GAAAA,CAAChG,CAAAA,EAAAA,cAAAA,CAAAA,CAAepC,qBAAoB,kBAAiBE,yBAAwB,mBAlB1EsI,CAAAA,EAAAA,EAAAA,QAAAA,CAAS,iBAqBpB,CCzBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAPZC,EAO8B,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EAAE,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KAA6B,CACrC,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,uBAAuB,CACvC,aAAa,CAAE,MAAM,mBACrB,EACA,aAAa,EADI,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,4DCAA,iDCAA,yDCAA,iECAA,uDCAA,sDCAA,yDCAA,iDCAA,2DCAA,yHCEA,SAASpH,EAAS,WAChBjB,CAAS,CACT,GAAGwF,EAC8B,EACjC,MAAO,UAAC8C,WAAAA,CAAS/B,YAAU,WAAWvG,UAAWwG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucxG,GAAa,GAAGwF,CAAK,CAAE3F,wBAAsB,WAAWC,0BAAwB,gBAC7kB,yBCPA,kDCAA,wDCAA,6DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/./src/components/treatments/treatment-form-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/treatments/treatments-list.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/form.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/?dbc0", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/confirmation-dialog.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/?9b13", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/?af1a", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconStethoscope.ts", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/treatments/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/textarea.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "'use client';\n\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { Button } from '@/components/ui/button';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { treatmentsApi } from '@/lib/api';\nimport { Treatment } from '@/types/clinic';\nimport { toast } from 'sonner';\nimport { t } from '@/lib/translations';\nconst treatmentSchema = z.object({\n  name: z.string().min(2, '治疗名称至少需要2个字符').max(100, '治疗名称不能超过100个字符').regex(/^[a-zA-Z0-9\\s\\-&()]+$/, '治疗名称只能包含字母、数字、空格、连字符和括号'),\n  description: z.string().max(1000, '描述不能超过1000个字符').optional(),\n  defaultPrice: z.number().min(0, '价格必须为0元或更高').max(10000, '价格不能超过10,000元').multipleOf(0.01, '价格必须是有效的货币金额（例如：99.99）'),\n  defaultDurationInMinutes: z.number().min(5, '时长至少需要5分钟').max(480, '时长不能超过8小时（480分钟）').int('时长必须是整数分钟')\n});\ntype TreatmentFormData = z.infer<typeof treatmentSchema>;\ninterface TreatmentFormDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  treatment?: Treatment;\n  onSuccess?: () => void;\n}\nexport function TreatmentFormDialog({\n  open,\n  onOpenChange,\n  treatment,\n  onSuccess\n}: TreatmentFormDialogProps) {\n  const [loading, setLoading] = useState(false);\n  const isEditing = !!treatment;\n  const form = useForm<TreatmentFormData>({\n    resolver: zodResolver(treatmentSchema),\n    defaultValues: {\n      name: treatment?.name || '',\n      description: treatment?.description || '',\n      defaultPrice: treatment?.defaultPrice || 0,\n      defaultDurationInMinutes: treatment?.defaultDurationInMinutes || 30\n    }\n  });\n\n  // Reset form when treatment changes or dialog opens\n  React.useEffect(() => {\n    if (open) {\n      form.reset({\n        name: treatment?.name || '',\n        description: treatment?.description || '',\n        defaultPrice: treatment?.defaultPrice || 0,\n        defaultDurationInMinutes: treatment?.defaultDurationInMinutes || 30\n      });\n    }\n  }, [open, treatment, form]);\n  const onSubmit = async (data: TreatmentFormData) => {\n    setLoading(true);\n    try {\n      const treatmentData = {\n        name: data.name,\n        description: data.description || undefined,\n        defaultPrice: data.defaultPrice,\n        defaultDurationInMinutes: data.defaultDurationInMinutes\n      };\n      if (isEditing) {\n        await treatmentsApi.update(treatment.id, treatmentData);\n        toast.success('Treatment updated successfully');\n      } else {\n        await treatmentsApi.create(treatmentData);\n        toast.success('Treatment created successfully');\n      }\n      onSuccess?.();\n      onOpenChange(false);\n      form.reset();\n    } catch (error) {\n      console.error('Failed to save treatment:', error);\n      toast.error(`Failed to ${isEditing ? 'update' : 'create'} treatment`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  return <Dialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"Dialog\" data-sentry-component=\"TreatmentFormDialog\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n      <DialogContent className=\"sm:max-w-[500px]\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n            {isEditing ? t('treatments.editTreatment') : t('treatments.newTreatment')}\n          </DialogTitle>\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n            {isEditing ? '更新下方的治疗信息。' : '填写治疗详细信息以创建新服务。'}\n          </DialogDescription>\n        </DialogHeader>\n\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            {/* Treatment Name */}\n            <FormField control={form.control} name=\"name\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('treatments.form.name')} *</FormLabel>\n                  <FormControl>\n                    <Input placeholder={t('treatments.form.namePlaceholder')} {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n\n            {/* Description */}\n            <FormField control={form.control} name=\"description\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('treatments.form.description')}</FormLabel>\n                  <FormControl>\n                    <Textarea placeholder={t('treatments.form.descriptionPlaceholder')} className=\"min-h-[80px]\" {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              {/* Default Price */}\n              <FormField control={form.control} name=\"defaultPrice\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>{t('treatments.form.price')} (¥) *</FormLabel>\n                    <FormControl>\n                      <Input type=\"number\" step=\"0.01\" placeholder={t('treatments.form.pricePlaceholder')} {...field} onChange={e => field.onChange(parseFloat(e.target.value) || 0)} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n\n              {/* Default Duration */}\n              <FormField control={form.control} name=\"defaultDurationInMinutes\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>{t('treatments.form.duration')} (分钟) *</FormLabel>\n                    <FormControl>\n                      <Input type=\"number\" placeholder={t('treatments.form.durationPlaceholder')} {...field} onChange={e => field.onChange(parseInt(e.target.value) || 0)} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"treatment-form-dialog.tsx\" />\n            </div>\n\n            <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)} disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n                {t('common.actions.cancel')}\n              </Button>\n              <Button type=\"submit\" disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"treatment-form-dialog.tsx\">\n                {loading ? '保存中...' : isEditing ? '更新治疗' : '创建治疗'}\n              </Button>\n            </DialogFooter>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { IconPlus, IconStethoscope, IconEdit, IconTrash } from '@tabler/icons-react';\nimport { treatmentsApi, appointmentsApi } from '@/lib/api';\nimport { Treatment } from '@/types/clinic';\nimport { TreatmentFormDialog } from './treatment-form-dialog';\nimport { ConfirmationDialog } from '@/components/ui/confirmation-dialog';\nimport { toast } from 'sonner';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { t } from '@/lib/translations';\nexport function TreatmentsList() {\n  const {\n    hasPermission\n  } = useRole();\n  const [treatments, setTreatments] = useState<Treatment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [formDialogOpen, setFormDialogOpen] = useState(false);\n  const [editingTreatment, setEditingTreatment] = useState<Treatment | undefined>();\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [treatmentToDelete, setTreatmentToDelete] = useState<Treatment | undefined>();\n  const [actionLoading, setActionLoading] = useState(false);\n  const fetchTreatments = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await treatmentsApi.getAll({\n        limit: 100\n      });\n      setTreatments(response.docs);\n    } catch (err) {\n      console.error('Failed to fetch treatments:', err);\n      setError('Failed to load treatments. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchTreatments();\n  }, []);\n\n  // Handler functions\n  const handleNewTreatment = () => {\n    setEditingTreatment(undefined);\n    setFormDialogOpen(true);\n  };\n  const handleEditTreatment = (treatment: Treatment) => {\n    setEditingTreatment(treatment);\n    setFormDialogOpen(true);\n  };\n  const handleDeleteTreatment = async (treatment: Treatment) => {\n    // Check if treatment has any appointments\n    try {\n      setActionLoading(true);\n      const appointmentsResponse = await appointmentsApi.getAll({\n        limit: 1,\n        where: {\n          treatment: {\n            equals: treatment.id\n          }\n        }\n      });\n      if (appointmentsResponse.docs.length > 0) {\n        toast.error('Cannot delete treatment with existing appointments. Please cancel or complete all appointments first.');\n        return;\n      }\n      setTreatmentToDelete(treatment);\n      setDeleteDialogOpen(true);\n    } catch (error) {\n      console.error('Failed to check treatment appointments:', error);\n      toast.error('Failed to verify treatment appointments');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleDeleteConfirm = async () => {\n    if (!treatmentToDelete) return;\n    setActionLoading(true);\n    try {\n      await treatmentsApi.delete(treatmentToDelete.id);\n      toast.success('Treatment deleted successfully');\n      setDeleteDialogOpen(false);\n      setTreatmentToDelete(undefined);\n      fetchTreatments(); // Refresh the list\n    } catch (error) {\n      console.error('Failed to delete treatment:', error);\n      toast.error('Failed to delete treatment');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleFormSuccess = () => {\n    setFormDialogOpen(false);\n    setEditingTreatment(undefined);\n    fetchTreatments(); // Refresh the list\n  };\n  if (loading) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">{t('treatments.loadingTreatments')}</p>\n        </div>\n      </div>;\n  }\n  if (error) {\n    return <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={() => window.location.reload()} variant=\"outline\">\n            重试\n          </Button>\n        </div>\n      </div>;\n  }\n  if (treatments.length === 0) {\n    return <>\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <IconStethoscope className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium mb-2\">{t('treatments.noTreatments')}</h3>\n            <p className=\"text-muted-foreground mb-4\">\n              开始添加您的第一个治疗项目。\n            </p>\n            <PermissionGate permission=\"canCreateTreatments\">\n              <Button onClick={handleNewTreatment}>\n                <IconPlus className=\"h-4 w-4 mr-2\" />\n                {t('treatments.newTreatment')}\n              </Button>\n            </PermissionGate>\n          </div>\n        </div>\n\n        <TreatmentFormDialog open={formDialogOpen} onOpenChange={setFormDialogOpen} treatment={editingTreatment} onSuccess={handleFormSuccess} />\n      </>;\n  }\n  return <>\n      <div className=\"space-y-4\">\n        {/* Header with Add Treatment button */}\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h3 className=\"text-lg font-medium\">\n              {treatments.length} {t('treatments.treatmentsCount')}\n            </h3>\n          </div>\n          <PermissionGate permission=\"canCreateTreatments\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"treatments-list.tsx\">\n            <Button onClick={handleNewTreatment} data-sentry-element=\"Button\" data-sentry-source-file=\"treatments-list.tsx\">\n              <IconPlus className=\"h-4 w-4 mr-2\" data-sentry-element=\"IconPlus\" data-sentry-source-file=\"treatments-list.tsx\" />\n              {t('treatments.newTreatment')}\n            </Button>\n          </PermissionGate>\n        </div>\n\n        {/* Treatments Grid */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {treatments.map(treatment => <div key={treatment.id} className=\"border rounded-lg p-4 space-y-3\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"space-y-1\">\n                  <h4 className=\"font-medium text-lg\">{treatment.name}</h4>\n                  {treatment.description && <p className=\"text-sm text-muted-foreground line-clamp-2\">\n                      {treatment.description}\n                    </p>}\n                </div>\n                <PermissionGate permission=\"canEditTreatments\">\n                  <DropdownMenu>\n                    <DropdownMenuTrigger asChild>\n                      <Button variant=\"ghost\" size=\"sm\">\n                        <IconEdit className=\"h-4 w-4\" />\n                      </Button>\n                    </DropdownMenuTrigger>\n                    <DropdownMenuContent>\n                      <DropdownMenuItem onClick={() => handleEditTreatment(treatment)}>\n                        <IconEdit className=\"h-4 w-4 mr-2\" />\n                        {t('common.actions.edit')}\n                      </DropdownMenuItem>\n                      <DropdownMenuItem onClick={() => {\n                    setTreatmentToDelete(treatment);\n                    setDeleteDialogOpen(true);\n                  }} className=\"text-red-600\">\n                        <IconTrash className=\"h-4 w-4 mr-2\" />\n                        {t('common.actions.delete')}\n                      </DropdownMenuItem>\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                </PermissionGate>\n              </div>\n\n              <div className=\"flex items-center justify-between text-sm\">\n                <div className=\"space-y-1\">\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"text-muted-foreground\">价格:</span>\n                    <span className=\"font-medium\">¥{treatment.defaultPrice?.toFixed(2) || '未设置'}</span>\n                  </div>\n                  {treatment.defaultDurationInMinutes && <div className=\"flex items-center gap-2\">\n                      <span className=\"text-muted-foreground\">时长:</span>\n                      <span>{treatment.defaultDurationInMinutes} 分钟</span>\n                    </div>}\n                </div>\n              </div>\n            </div>)}\n        </div>\n      </div>\n\n      <TreatmentFormDialog open={formDialogOpen} onOpenChange={setFormDialogOpen} treatment={editingTreatment} onSuccess={handleFormSuccess} data-sentry-element=\"TreatmentFormDialog\" data-sentry-source-file=\"treatments-list.tsx\" />\n\n      <ConfirmationDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen} title=\"删除治疗项目\" description={`您确定要删除\"${treatmentToDelete?.name}\"吗？此操作无法撤销。`} confirmText=\"删除\" variant=\"destructive\" onConfirm={handleDeleteConfirm} loading={actionLoading} data-sentry-element=\"ConfirmationDialog\" data-sentry-source-file=\"treatments-list.tsx\" />\n    </>;\n}", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport { Controller, FormProvider, useFormContext, useFormState, type ControllerProps, type FieldPath, type FieldValues } from 'react-hook-form';\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\nconst Form = FormProvider;\ntype FormFieldContextValue<TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>> = {\n  name: TName;\n};\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\nconst FormField = <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return <FormFieldContext.Provider value={{\n    name: props.name\n  }} data-sentry-element=\"FormFieldContext.Provider\" data-sentry-component=\"FormField\" data-sentry-source-file=\"form.tsx\">\r\n      <Controller {...props} data-sentry-element=\"Controller\" data-sentry-source-file=\"form.tsx\" />\r\n    </FormFieldContext.Provider>;\n};\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const {\n    getFieldState\n  } = useFormContext();\n  const formState = useFormState({\n    name: fieldContext.name\n  });\n  const fieldState = getFieldState(fieldContext.name, formState);\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n  const {\n    id\n  } = itemContext;\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\ntype FormItemContextValue = {\n  id: string;\n};\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\nfunction FormItem({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  const id = React.useId();\n  return <FormItemContext.Provider value={{\n    id\n  }} data-sentry-element=\"FormItemContext.Provider\" data-sentry-component=\"FormItem\" data-sentry-source-file=\"form.tsx\">\r\n      <div data-slot='form-item' className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>;\n}\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const {\n    error,\n    formItemId\n  } = useFormField();\n  return <Label data-slot='form-label' data-error={!!error} className={cn('data-[error=true]:text-destructive', className)} htmlFor={formItemId} {...props} data-sentry-element=\"Label\" data-sentry-component=\"FormLabel\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormControl({\n  ...props\n}: React.ComponentProps<typeof Slot>) {\n  const {\n    error,\n    formItemId,\n    formDescriptionId,\n    formMessageId\n  } = useFormField();\n  return <Slot data-slot='form-control' id={formItemId} aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`} aria-invalid={!!error} {...props} data-sentry-element=\"Slot\" data-sentry-component=\"FormControl\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormDescription({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    formDescriptionId\n  } = useFormField();\n  return <p data-slot='form-description' id={formDescriptionId} className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"FormDescription\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormMessage({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    error,\n    formMessageId\n  } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n  if (!body) {\n    return null;\n  }\n  return <p data-slot='form-message' id={formMessageId} className={cn('text-destructive text-sm', className)} {...props} data-sentry-component=\"FormMessage\" data-sentry-source-file=\"form.tsx\">\r\n      {body}\r\n    </p>;\n}\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField };", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\treatments\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'treatments',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\treatments\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\treatments\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/treatments/page\",\n        pathname: \"/dashboard/treatments\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "'use client';\n\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\ninterface ConfirmationDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  title: string;\n  description: string;\n  confirmText?: string;\n  cancelText?: string;\n  variant?: 'default' | 'destructive';\n  onConfirm: () => void;\n  loading?: boolean;\n}\nexport function ConfirmationDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = '确认',\n  cancelText = '取消',\n  variant = 'default',\n  onConfirm,\n  loading = false\n}: ConfirmationDialogProps) {\n  return <AlertDialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"AlertDialog\" data-sentry-component=\"ConfirmationDialog\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n      <AlertDialogContent data-sentry-element=\"AlertDialogContent\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n        <AlertDialogHeader data-sentry-element=\"AlertDialogHeader\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n          <AlertDialogTitle data-sentry-element=\"AlertDialogTitle\" data-sentry-source-file=\"confirmation-dialog.tsx\">{title}</AlertDialogTitle>\n          <AlertDialogDescription data-sentry-element=\"AlertDialogDescription\" data-sentry-source-file=\"confirmation-dialog.tsx\">{description}</AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter data-sentry-element=\"AlertDialogFooter\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n          <AlertDialogCancel disabled={loading} data-sentry-element=\"AlertDialogCancel\" data-sentry-source-file=\"confirmation-dialog.tsx\">{cancelText}</AlertDialogCancel>\n          <AlertDialogAction onClick={onConfirm} disabled={loading} className={variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''} data-sentry-element=\"AlertDialogAction\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n            {loading ? '加载中...' : confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>;\n}", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "import(/* webpackMode: \"eager\", webpackExports: [\"TreatmentsList\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\treatments\\\\treatments-list.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import(/* webpackMode: \"eager\", webpackExports: [\"TreatmentsList\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\treatments\\\\treatments-list.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'stethoscope', 'IconStethoscope', [[\"path\",{\"d\":\"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M8 15a6 6 0 1 0 12 0v-3\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M11 3v2\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M6 3v2\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0\",\"key\":\"svg-4\"}]]);", "import { auth } from '@clerk/nextjs/server';\nimport { redirect } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport { Button } from '@/components/ui/button';\nimport { IconPlus, IconStethoscope } from '@tabler/icons-react';\nimport { TreatmentsList } from '@/components/treatments/treatments-list';\nimport { t } from '@/lib/translations';\nexport default async function TreatmentsPage() {\n  const {\n    userId\n  } = await auth();\n  if (!userId) {\n    return redirect('/auth/sign-in');\n  }\n  return <PageContainer data-sentry-element=\"PageContainer\" data-sentry-component=\"TreatmentsPage\" data-sentry-source-file=\"page.tsx\">\n      <div className='flex flex-1 flex-col space-y-4'>\n        {/* Header */}\n        <div className='flex items-center justify-between'>\n          <div>\n            <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>\n              <IconStethoscope className='size-6' data-sentry-element=\"IconStethoscope\" data-sentry-source-file=\"page.tsx\" />\n              {t('treatments.title')}\n            </h2>\n            <p className='text-muted-foreground'>\n              {t('treatments.subtitle')}\n            </p>\n          </div>\n        </div>\n\n        {/* Treatments List - Client Component */}\n        <TreatmentsList data-sentry-element=\"TreatmentsList\" data-sentry-source-file=\"page.tsx\" />\n      </div>\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/treatments',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/treatments',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/treatments',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/treatments',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["treatmentSchema", "z", "name", "min", "max", "regex", "description", "optional", "defaultPrice", "multipleOf", "defaultDurationInMinutes", "int", "TreatmentFormDialog", "open", "onOpenChange", "treatment", "onSuccess", "loading", "setLoading", "useState", "isEditing", "form", "useForm", "resolver", "zodResolver", "defaultValues", "onSubmit", "data", "treatmentData", "undefined", "treatmentsApi", "update", "id", "toast", "success", "create", "reset", "error", "console", "Dialog", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "DialogHeader", "DialogTitle", "t", "DialogDescription", "Form", "handleSubmit", "FormField", "control", "render", "field", "FormItem", "FormLabel", "FormControl", "Input", "placeholder", "FormMessage", "Textarea", "div", "type", "step", "onChange", "e", "parseFloat", "target", "value", "parseInt", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "variant", "onClick", "disabled", "TreatmentsList", "hasPermission", "useRole", "treatments", "setTreatments", "setError", "formDialogOpen", "setFormDialogOpen", "editingTreatment", "setEditingTreatment", "deleteDialogOpen", "setDeleteDialogOpen", "treatmentToDelete", "setTreatmentToDelete", "actionLoading", "setActionLoading", "fetchTreatments", "response", "getAll", "limit", "docs", "err", "handleNewTreatment", "handleEditTreatment", "handleDeleteConfirm", "delete", "handleFormSuccess", "p", "window", "location", "reload", "length", "IconStethoscope", "h3", "PermissionGate", "permission", "IconPlus", "map", "h4", "DropdownMenu", "DropdownMenuTrigger", "<PERSON><PERSON><PERSON><PERSON>", "size", "IconEdit", "DropdownMenuContent", "DropdownMenuItem", "IconTrash", "span", "toFixed", "ConfirmationDialog", "title", "confirmText", "onConfirm", "FormProvider", "FormFieldContext", "React", "props", "Provider", "Controller", "useFormField", "fieldContext", "itemContext", "FormItemContext", "getFieldState", "useFormContext", "formState", "useFormState", "fieldState", "formItemId", "formDescriptionId", "formMessageId", "data-slot", "cn", "Label", "data-error", "htmlFor", "Slot", "aria-<PERSON><PERSON>", "aria-invalid", "FormDescription", "body", "String", "message", "children", "cancelText", "AlertDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "TreatmentsPage", "userId", "auth", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "_jsxs", "h2", "redirect", "serverComponentModule.default", "textarea"], "sourceRoot": ""}