import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';
import { PatientTask } from '@/types/clinic';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const data = await payloadClient.getPatientTask(params.id) as PatientTask;

    // Check if user has permission to view this task
    if (user.role === 'doctor') {
      const allowedTypes = ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'];
      const assignedToId = typeof data.assignedTo === 'object' ? data.assignedTo.id : data.assignedTo;
      const createdById = typeof data.createdBy === 'object' ? data.createdBy.id : data.createdBy;
      if (assignedToId !== user.payloadUserId && createdById !== user.payloadUserId && !allowedTypes.includes(data.taskType)) {
        return createErrorResponse('Permission denied', 403);
      }
    } else if (user.role === 'front-desk') {
      const allowedTypes = ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'];
      const assignedToId = typeof data.assignedTo === 'object' ? data.assignedTo.id : data.assignedTo;
      if (assignedToId !== user.payloadUserId && !allowedTypes.includes(data.taskType)) {
        return createErrorResponse('Permission denied', 403);
      }
    }
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient task:', error);
    return createErrorResponse('Failed to fetch patient task');
  }
});

export const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const updateData = await request.json();
    
    // Get the existing task to check permissions
    const existingTask = await payloadClient.getPatientTask(params.id) as PatientTask;

    // Check if user can update this task
    const assignedToId = typeof existingTask.assignedTo === 'object' ? existingTask.assignedTo.id : existingTask.assignedTo;
    const createdById = typeof existingTask.createdBy === 'object' ? existingTask.createdBy.id : existingTask.createdBy;
    if (user.role !== 'admin' && assignedToId !== user.payloadUserId && createdById !== user.payloadUserId) {
      return createErrorResponse('You can only update tasks assigned to you or created by you', 403);
    }
    
    // Prevent changing the creator
    delete updateData.createdBy;
    
    // If status is being changed to completed, set completion time
    if (updateData.status === 'completed' && existingTask.status !== 'completed') {
      updateData.completedAt = new Date();
    }
    
    // If status is being changed from completed to something else, clear completion data
    if (updateData.status !== 'completed' && existingTask.status === 'completed') {
      updateData.completedAt = null;
      updateData.completionNotes = null;
    }
    
    const data = await payloadClient.updatePatientTask(params.id, updateData);
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error updating patient task:', error);
    return createErrorResponse('Failed to update patient task');
  }
});

export const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    // Only admin can delete tasks
    if (user.role !== 'admin') {
      return createErrorResponse('Only administrators can delete tasks', 403);
    }
    
    const payloadClient = createPayloadClient(user);
    await payloadClient.deletePatientTask(params.id);
    
    return createSuccessResponse({ message: 'Patient task deleted successfully' });
  } catch (error) {
    console.error('Error deleting patient task:', error);
    return createErrorResponse('Failed to delete patient task');
  }
});
