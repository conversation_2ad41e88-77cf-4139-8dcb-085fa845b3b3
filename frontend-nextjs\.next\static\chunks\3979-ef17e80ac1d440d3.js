try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="9a2e370a-d21b-49dd-a099-7a9c9482a0d9",e._sentryDebugIdIdentifier="sentry-dbid-9a2e370a-d21b-49dd-a099-7a9c9482a0d9")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3979],{2122:(e,t,l)=>{l.d(t,{IN:()=>b,GJ:()=>v,tU:()=>h,ZA:()=>C,ab:()=>y});var n=l(30178);function r(e,t,l){try{return e(t)}catch(e){return(0,n.R8)("[nuqs] Error while parsing value `%s`: %O"+(l?" (for key `%s`)":""),t,e,l),null}}var o=function(){if("undefined"==typeof window||!window.GestureEvent)return 50;try{let e=navigator.userAgent?.match(/version\/([\d\.]+) safari/i);return parseFloat(e[1])>=17?120:320}catch{return 320}}(),i=new Map,a={history:"replace",scroll:!1,shallow:!0,throttleMs:o},u=new Set,s=0,d=null;function g(e,t,l,r){let s=null===t?null:l(t);return(0,n.Yz)("[nuqs queue] Enqueueing %s=%s %O",e,s,r),i.set(e,s),"push"===r.history&&(a.history="push"),r.scroll&&(a.scroll=!0),!1===r.shallow&&(a.shallow=!1),r.startTransition&&u.add(r.startTransition),a.throttleMs=Math.max(r.throttleMs??o,Number.isFinite(a.throttleMs)?a.throttleMs:0),s}function c(){return new URLSearchParams(location.search)}function f({getSearchParamsSnapshot:e=c,updateUrl:t,rateLimitFactor:l=1}){return null===d&&(d=new Promise((r,g)=>{if(!Number.isFinite(a.throttleMs)){(0,n.Yz)("[nuqs queue] Skipping flush due to throttleMs=Infinity"),r(e()),setTimeout(()=>{d=null},0);return}function c(){s=performance.now();let[l,c]=function({updateUrl:e,getSearchParamsSnapshot:t}){let l=t();if(0===i.size)return[l,null];let r=Array.from(i.entries()),s={...a},d=Array.from(u);for(let[e,t]of(i.clear(),u.clear(),a.history="replace",a.scroll=!1,a.shallow=!0,a.throttleMs=o,(0,n.Yz)("[nuqs queue] Flushing queue %O with options %O",r,s),r))null===t?l.delete(e):l.set(e,t);try{return function(e,t){let l=n=>{if(n===e.length)return t();let r=e[n];if(!r)throw Error("Invalid transition function");r(()=>l(n+1))};l(0)}(d,()=>{e(l,{history:s.history,scroll:s.scroll,shallow:s.shallow})}),[l,null]}catch(e){return console.error((0,n.z3)(429),r.map(([e])=>e).join(),e),[l,e]}}({updateUrl:t,getSearchParamsSnapshot:e});null===c?r(l):g(l),d=null}setTimeout(function(){let e=performance.now()-s,t=a.throttleMs,r=l*Math.max(0,Math.min(t,t-e));(0,n.Yz)("[nuqs queue] Scheduling flush in %f ms. Throttled at %f ms",r,t),0===r?c():setTimeout(c,r)},0)})),d}var p=l(99004);function m(e){function t(t){if(void 0===t)return null;let l="";if(Array.isArray(t)){if(void 0===t[0])return null;l=t[0]}return"string"==typeof t&&(l=t),r(e.parse,l)}return{eq:(e,t)=>e===t,...e,parseServerSide:t,withDefault(e){return{...this,defaultValue:e,parseServerSide(l){var n;return null!=(n=t(l))?n:e}}},withOptions(e){return{...this,...e}}}}var h=m({parse:e=>e,serialize:e=>"".concat(e)}),v=m({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:t},serialize:e=>Math.round(e).toFixed()});function w(e,t){return e.valueOf()===t.valueOf()}function b(e){var t;let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:",",n=null!=(t=e.eq)?t:(e,t)=>e===t,o=encodeURIComponent(l);return m({parse:t=>""===t?[]:t.split(l).map((t,n)=>r(e.parse,t.replaceAll(o,l),"[".concat(n,"]"))).filter(e=>null!=e),serialize:t=>t.map(t=>(e.serialize?e.serialize(t):String(t)).replaceAll(l,o)).join(l),eq:(e,t)=>e===t||e.length===t.length&&e.every((e,l)=>n(e,t[l]))})}m({parse:e=>{let t=v.parse(e);return null===t?null:t-1},serialize:e=>v.serialize(e+1)}),m({parse:e=>{let t=parseInt(e,16);return Number.isNaN(t)?null:t},serialize:e=>{let t=Math.round(e).toString(16);return t.padStart(t.length+t.length%2,"0")}}),m({parse:e=>{let t=parseFloat(e);return Number.isNaN(t)?null:t},serialize:e=>e.toString()}),m({parse:e=>"true"===e,serialize:e=>e?"true":"false"}),m({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:new Date(t)},serialize:e=>e.valueOf().toString(),eq:w}),m({parse:e=>{let t=new Date(e);return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString(),eq:w}),m({parse:e=>{let t=new Date(e.slice(0,10));return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString().slice(0,10),eq:w});var S=function(e){return{all:e=e||new Map,on:function(t,l){var n=e.get(t);n?n.push(l):e.set(t,[l])},off:function(t,l){var n=e.get(t);n&&(l?n.splice(n.indexOf(l)>>>0,1):e.set(t,[]))},emit:function(t,l){var n=e.get(t);n&&n.slice().map(function(e){e(l)}),(n=e.get("*"))&&n.slice().map(function(e){e(t,l)})}}}();function C(e){var t,l,a;let{history:u="replace",shallow:s=!0,scroll:d=!1,throttleMs:c=o,parse:m=e=>e,serialize:h=String,eq:v=(e,t)=>e===t,defaultValue:w,clearOnDefault:b=!0,startTransition:C}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{history:"replace",scroll:!1,shallow:!0,throttleMs:o,parse:e=>e,serialize:String,eq:(e,t)=>e===t,clearOnDefault:!0,defaultValue:void 0},R=(0,n.V7)(),y=R.searchParams,M=(0,p.useRef)(null!=(t=null==y?void 0:y.get(e))?t:null),[F,x]=(0,p.useState)(()=>{var t;let l=i.get(e),n=void 0===l?null!=(t=null==y?void 0:y.get(e))?t:null:l;return null===n?null:r(m,n,e)}),I=(0,p.useRef)(F);(0,n.Yz)("[nuqs `%s`] render - state: %O, iSP: %s",e,F,null!=(l=null==y?void 0:y.get(e))?l:null),(0,p.useEffect)(()=>{var t;let l=null!=(t=null==y?void 0:y.get(e))?t:null;if(l===M.current)return;let o=null===l?null:r(m,l,e);(0,n.Yz)("[nuqs `%s`] syncFromUseSearchParams %O",e,o),I.current=o,M.current=l,x(o)},[null==y?void 0:y.get(e),e]),(0,p.useEffect)(()=>{function t(t){let{state:l,query:r}=t;(0,n.Yz)("[nuqs `%s`] updateInternalState %O",e,l),I.current=l,M.current=r,x(l)}return(0,n.Yz)("[nuqs `%s`] subscribing to sync",e),S.on(e,t),()=>{(0,n.Yz)("[nuqs `%s`] unsubscribing from sync",e),S.off(e,t)}},[e]);let P=(0,p.useCallback)(function(t){var l,n,r,o,i,a,p,m;let y=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},M="function"==typeof t?t(null!=(n=null!=(l=I.current)?l:w)?n:null):t;(null!=(r=y.clearOnDefault)?r:b)&&null!==M&&void 0!==w&&v(M,w)&&(M=null);let F=g(e,M,h,{history:null!=(o=y.history)?o:u,shallow:null!=(i=y.shallow)?i:s,scroll:null!=(a=y.scroll)?a:d,throttleMs:null!=(p=y.throttleMs)?p:c,startTransition:null!=(m=y.startTransition)?m:C});return S.emit(e,{state:M,query:F}),f(R)},[e,u,s,d,c,C,R.updateUrl,R.getSearchParamsSnapshot,R.rateLimitFactor]);return[null!=(a=null!=F?F:w)?a:null,P]}var R={};function y(e){let{history:t="replace",scroll:l=!1,shallow:r=!0,throttleMs:i=o,clearOnDefault:a=!0,startTransition:u,urlKeys:s=R}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=Object.keys(e).join(","),c=(0,p.useMemo)(()=>Object.fromEntries(Object.keys(e).map(e=>{var t;return[e,null!=(t=s[e])?t:e]})),[d,JSON.stringify(s)]),m=(0,n.V7)(),h=m.searchParams,v=(0,p.useRef)({}),w=(0,p.useMemo)(()=>Object.fromEntries(Object.keys(e).map(t=>{var l;return[t,null!=(l=e[t].defaultValue)?l:null]})),[Object.values(e).map(e=>{let{defaultValue:t}=e;return t}).join(",")]),[b,C]=(0,p.useState)(()=>M(e,s,null!=h?h:new URLSearchParams).state),y=(0,p.useRef)(b);if((0,n.Yz)("[nuq+ `%s`] render - state: %O, iSP: %s",d,b,h),Object.keys(v.current).join("&")!==Object.values(c).join("&")){let{state:t,hasChanged:l}=M(e,s,h,v.current,y.current);l&&(y.current=t,C(t)),v.current=Object.fromEntries(Object.values(c).map(e=>{var t;return[e,null!=(t=null==h?void 0:h.get(e))?t:null]}))}(0,p.useEffect)(()=>{let{state:t,hasChanged:l}=M(e,s,h,v.current,y.current);l&&(y.current=t,C(t))},[Object.values(c).map(e=>"".concat(e,"=").concat(null==h?void 0:h.get(e))).join("&")]),(0,p.useEffect)(()=>{let t=Object.keys(e).reduce((t,l)=>(t[l]=t=>{var r,o;let{state:i,query:a}=t,{defaultValue:u}=e[l],s=c[l];y.current={...y.current,[l]:null!=(r=null!=i?i:u)?r:null},v.current[s]=a,(0,n.Yz)("[nuq+ `%s`] Cross-hook key sync %s: %O (default: %O). Resolved: %O",d,s,i,u,y.current),o=y.current,(0,n.Yz)("[nuq+ `%s`] updateInternalState %O",d,o),y.current=o,C(o)},t),{});for(let l of Object.keys(e)){let e=c[l];(0,n.Yz)("[nuq+ `%s`] Subscribing to sync for `%s`",d,e),S.on(e,t[l])}return()=>{for(let l of Object.keys(e)){let e=c[l];(0,n.Yz)("[nuq+ `%s`] Unsubscribing to sync for `%s`",d,e),S.off(e,t[l])}}},[d,c]);let x=(0,p.useCallback)(function(o){var s,p,h,v,b,C,R,M,x,I,P,E,V,_,A;let z=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},D=Object.fromEntries(Object.keys(e).map(e=>[e,null])),O="function"==typeof o?null!=(s=o(F(y.current,w)))?s:D:null!=o?o:D;for(let[o,s]of((0,n.Yz)("[nuq+ `%s`] setState: %O",d,O),Object.entries(O))){let n=e[o],d=c[o];if(!n)continue;(null!=(h=null!=(p=z.clearOnDefault)?p:n.clearOnDefault)?h:a)&&null!==s&&void 0!==n.defaultValue&&(null!=(v=n.eq)?v:(e,t)=>e===t)(s,n.defaultValue)&&(s=null);let f=g(d,s,null!=(b=n.serialize)?b:String,{history:null!=(R=null!=(C=z.history)?C:n.history)?R:t,shallow:null!=(x=null!=(M=z.shallow)?M:n.shallow)?x:r,scroll:null!=(P=null!=(I=z.scroll)?I:n.scroll)?P:l,throttleMs:null!=(V=null!=(E=z.throttleMs)?E:n.throttleMs)?V:i,startTransition:null!=(A=null!=(_=z.startTransition)?_:n.startTransition)?A:u});S.emit(d,{state:s,query:f})}return f(m)},[d,t,r,l,i,u,c,m.updateUrl,m.getSearchParamsSnapshot,m.rateLimitFactor,w]);return[(0,p.useMemo)(()=>F(b,w),[b,w]),x]}function M(e,t,l,n,o){let a=!1,u=Object.keys(e).reduce((u,s)=>{var d,g,c,f;let p=null!=(d=null==t?void 0:t[s])?d:s,{parse:m}=e[s],h=i.get(p),v=void 0===h?null!=(g=null==l?void 0:l.get(p))?g:null:h;if(n&&o&&(null!=(c=n[p])?c:null)===v)return u[s]=null!=(f=o[s])?f:null,u;a=!0;let w=null===v?null:r(m,v,s);return u[s]=null!=w?w:null,n&&(n[p]=v),u},{});if(!a){let t=Object.keys(e),l=Object.keys(null!=o?o:{});a=t.length!==l.length||t.some(e=>!l.includes(e))}return{state:u,hasChanged:a}}function F(e,t){return Object.fromEntries(Object.keys(e).map(l=>{var n,r;return[l,null!=(r=null!=(n=e[l])?n:t[l])?r:null]}))}},12320:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("Settings2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},13238:(e,t,l)=>{l.d(t,{Cp:()=>o}),l(99004);var n={303:"Multiple adapter contexts detected. This might happen in monorepos.",404:"nuqs requires an adapter to work with your framework.",409:"Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` (via the %s adapter) was about to load on top.",414:"Max safe URL length exceeded. Some browsers may not be able to accept this URL. Consider limiting the amount of state stored in the URL.",429:"URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O",500:"Empty search params cache. Search params can't be accessed in Layouts.",501:"Search params cache already populated. Have you called `parse` twice?"};Symbol("Input");var r=function(){try{if("undefined"==typeof localStorage)return!1;let e="nuqs-localStorage-test";localStorage.setItem(e,e);let t=localStorage.getItem(e)===e;if(localStorage.removeItem(e),!t)return!1}catch(e){return console.error("[nuqs]: debug mode is disabled (localStorage unavailable).",e),!1}return(localStorage.getItem("debug")??"").includes("nuqs")}();function o(e){function t(t){if(void 0===t)return null;let l="";if(Array.isArray(t)){if(void 0===t[0])return null;l=t[0]}return"string"==typeof t&&(l=t),function(e,t,l){try{return e(t)}catch(e){return!function(e,...t){r&&console.warn(e,...t)}("[nuqs] Error while parsing value `%s`: %O",t,e,l),null}}(e.parse,l)}return{eq:(e,t)=>e===t,...e,parseServerSide:t,withDefault(e){return{...this,defaultValue:e,parseServerSide:l=>t(l)??e}},withOptions(e){return{...this,...e}}}}o({parse:e=>e,serialize:e=>`${e}`});var i=o({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:t},serialize:e=>Math.round(e).toFixed()});function a(e,t){return e.valueOf()===t.valueOf()}o({parse:e=>{let t=i.parse(e);return null===t?null:t-1},serialize:e=>i.serialize(e+1)}),o({parse:e=>{let t=parseInt(e,16);return Number.isNaN(t)?null:t},serialize:e=>{let t=Math.round(e).toString(16);return t.padStart(t.length+t.length%2,"0")}}),o({parse:e=>{let t=parseFloat(e);return Number.isNaN(t)?null:t},serialize:e=>e.toString()}),o({parse:e=>"true"===e,serialize:e=>e?"true":"false"}),o({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:new Date(t)},serialize:e=>e.valueOf().toString(),eq:a}),o({parse:e=>{let t=new Date(e);return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString(),eq:a}),o({parse:e=>{let t=new Date(e.slice(0,10));return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString().slice(0,10),eq:a})},23933:(e,t,l)=>{l.d(t,{uB:()=>A});var n=/[\\\/_+.#"@\[\(\{&]/,r=/[\\\/_+.#"@\[\(\{&]/g,o=/[\s-]/,i=/[\s-]/g;function a(e){return e.toLowerCase().replace(i," ")}var u=l(88749),s=l(99004),d=l(51452),g=l(29548),c=l(39552),f='[cmdk-group=""]',p='[cmdk-group-items=""]',m='[cmdk-item=""]',h="".concat(m,':not([aria-disabled="true"])'),v="cmdk-item-select",w="data-value",b=(e,t,l)=>(function(e,t,l){return function e(t,l,a,u,s,d,g){if(d===l.length)return s===t.length?1:.99;var c=`${s},${d}`;if(void 0!==g[c])return g[c];for(var f,p,m,h,v=u.charAt(d),w=a.indexOf(v,s),b=0;w>=0;)(f=e(t,l,a,u,w+1,d+1,g))>b&&(w===s?f*=1:n.test(t.charAt(w-1))?(f*=.8,(m=t.slice(s,w-1).match(r))&&s>0&&(f*=Math.pow(.999,m.length))):o.test(t.charAt(w-1))?(f*=.9,(h=t.slice(s,w-1).match(i))&&s>0&&(f*=Math.pow(.999,h.length))):(f*=.17,s>0&&(f*=Math.pow(.999,w-s))),t.charAt(w)!==l.charAt(d)&&(f*=.9999)),(f<.1&&a.charAt(w-1)===u.charAt(d+1)||u.charAt(d+1)===u.charAt(d)&&a.charAt(w-1)!==u.charAt(d))&&.1*(p=e(t,l,a,u,w+1,d+2,g))>f&&(f=.1*p),f>b&&(b=f),w=a.indexOf(v,w+1);return g[c]=b,b}(e=l&&l.length>0?`${e+" "+l.join(" ")}`:e,t,a(e),a(t),0,0,{})})(e,t,l),S=s.createContext(void 0),C=()=>s.useContext(S),R=s.createContext(void 0),y=()=>s.useContext(R),M=s.createContext(void 0),F=s.forwardRef((e,t)=>{let l=O(()=>{var t,l;return{search:"",value:null!=(l=null!=(t=e.value)?t:e.defaultValue)?l:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=O(()=>new Set),r=O(()=>new Map),o=O(()=>new Map),i=O(()=>new Set),a=z(e),{label:u,children:c,value:C,onValueChange:y,filter:M,shouldFilter:F,loop:x,disablePointerSelection:I=!1,vimBindings:P=!0,...E}=e,V=(0,g.B)(),_=(0,g.B)(),A=(0,g.B)(),L=s.useRef(null),k=G();D(()=>{if(void 0!==C){let e=C.trim();l.current.value=e,N.emit()}},[C]),D(()=>{k(6,K)},[]);let N=s.useMemo(()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>l.current,setState:(e,t,n)=>{var r,o,i,u;if(!Object.is(l.current[e],t)){if(l.current[e]=t,"search"===e)$(),B(),k(1,U);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(A);e?e.focus():null==(r=document.getElementById(V))||r.focus()}if(k(7,()=>{var e;l.current.selectedItemId=null==(e=Y())?void 0:e.id,N.emit()}),n||k(5,K),(null==(o=a.current)?void 0:o.value)!==void 0){null==(u=(i=a.current).onValueChange)||u.call(i,null!=t?t:"");return}}N.emit()}},emit:()=>{i.current.forEach(e=>e())}}),[]),j=s.useMemo(()=>({value:(e,t,n)=>{var r;t!==(null==(r=o.current.get(e))?void 0:r.value)&&(o.current.set(e,{value:t,keywords:n}),l.current.filtered.items.set(e,T(t,n)),k(2,()=>{B(),N.emit()}))},item:(e,t)=>(n.current.add(e),t&&(r.current.has(t)?r.current.get(t).add(e):r.current.set(t,new Set([e]))),k(3,()=>{$(),B(),l.current.value||U(),N.emit()}),()=>{o.current.delete(e),n.current.delete(e),l.current.filtered.items.delete(e);let t=Y();k(4,()=>{$(),(null==t?void 0:t.getAttribute("id"))===e&&U(),N.emit()})}),group:e=>(r.current.has(e)||r.current.set(e,new Set),()=>{o.current.delete(e),r.current.delete(e)}),filter:()=>a.current.shouldFilter,label:u||e["aria-label"],getDisablePointerSelection:()=>a.current.disablePointerSelection,listId:V,inputId:A,labelId:_,listInnerRef:L}),[]);function T(e,t){var n,r;let o=null!=(r=null==(n=a.current)?void 0:n.filter)?r:b;return e?o(e,l.current.search,t):0}function B(){if(!l.current.search||!1===a.current.shouldFilter)return;let e=l.current.filtered.items,t=[];l.current.filtered.groups.forEach(l=>{let n=r.current.get(l),o=0;n.forEach(t=>{o=Math.max(e.get(t),o)}),t.push([l,o])});let n=L.current;X().sort((t,l)=>{var n,r;let o=t.getAttribute("id"),i=l.getAttribute("id");return(null!=(n=e.get(i))?n:0)-(null!=(r=e.get(o))?r:0)}).forEach(e=>{let t=e.closest(p);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(p," > *"))):n.appendChild(e.parentElement===n?e:e.closest("".concat(p," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let l=null==(t=L.current)?void 0:t.querySelector("".concat(f,"[").concat(w,'="').concat(encodeURIComponent(e[0]),'"]'));null==l||l.parentElement.appendChild(l)})}function U(){let e=X().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(w);N.setState("value",t||void 0)}function $(){var e,t,i,u;if(!l.current.search||!1===a.current.shouldFilter){l.current.filtered.count=n.current.size;return}l.current.filtered.groups=new Set;let s=0;for(let r of n.current){let n=T(null!=(t=null==(e=o.current.get(r))?void 0:e.value)?t:"",null!=(u=null==(i=o.current.get(r))?void 0:i.keywords)?u:[]);l.current.filtered.items.set(r,n),n>0&&s++}for(let[e,t]of r.current)for(let n of t)if(l.current.filtered.items.get(n)>0){l.current.filtered.groups.add(e);break}l.current.filtered.count=s}function K(){var e,t,l;let n=Y();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(l=null==(t=n.closest(f))?void 0:t.querySelector('[cmdk-group-heading=""]'))||l.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function Y(){var e;return null==(e=L.current)?void 0:e.querySelector("".concat(m,'[aria-selected="true"]'))}function X(){var e;return Array.from((null==(e=L.current)?void 0:e.querySelectorAll(h))||[])}function Z(e){let t=X()[e];t&&N.setState("value",t.getAttribute(w))}function J(e){var t;let l=Y(),n=X(),r=n.findIndex(e=>e===l),o=n[r+e];null!=(t=a.current)&&t.loop&&(o=r+e<0?n[n.length-1]:r+e===n.length?n[0]:n[r+e]),o&&N.setState("value",o.getAttribute(w))}function Q(e){let t=Y(),l=null==t?void 0:t.closest(f),n;for(;l&&!n;)n=null==(l=e>0?function(e,t){let l=e.nextElementSibling;for(;l;){if(l.matches(t))return l;l=l.nextElementSibling}}(l,f):function(e,t){let l=e.previousElementSibling;for(;l;){if(l.matches(t))return l;l=l.previousElementSibling}}(l,f))?void 0:l.querySelector(h);n?N.setState("value",n.getAttribute(w)):J(e)}let W=()=>Z(X().length-1),ee=e=>{e.preventDefault(),e.metaKey?W():e.altKey?Q(1):J(1)},et=e=>{e.preventDefault(),e.metaKey?Z(0):e.altKey?Q(-1):J(-1)};return s.createElement(d.sG.div,{ref:t,tabIndex:-1,...E,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=E.onKeyDown)||t.call(E,e);let l=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||l))switch(e.key){case"n":case"j":P&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":P&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),Z(0);break;case"End":e.preventDefault(),W();break;case"Enter":{e.preventDefault();let t=Y();if(t){let e=new Event(v);t.dispatchEvent(e)}}}}},s.createElement("label",{"cmdk-label":"",htmlFor:j.inputId,id:j.labelId,style:q},u),H(e,e=>s.createElement(R.Provider,{value:N},s.createElement(S.Provider,{value:j},e))))}),x=s.forwardRef((e,t)=>{var l,n;let r=(0,g.B)(),o=s.useRef(null),i=s.useContext(M),a=C(),u=z(e),f=null!=(n=null==(l=u.current)?void 0:l.forceMount)?n:null==i?void 0:i.forceMount;D(()=>{if(!f)return a.item(r,null==i?void 0:i.id)},[f]);let p=k(r,o,[e.value,e.children,o],e.keywords),m=y(),h=L(e=>e.value&&e.value===p.current),w=L(e=>!!f||!1===a.filter()||!e.search||e.filtered.items.get(r)>0);function b(){var e,t;S(),null==(t=(e=u.current).onSelect)||t.call(e,p.current)}function S(){m.setState("value",p.current,!0)}if(s.useEffect(()=>{let t=o.current;if(!(!t||e.disabled))return t.addEventListener(v,b),()=>t.removeEventListener(v,b)},[w,e.onSelect,e.disabled]),!w)return null;let{disabled:R,value:F,onSelect:x,forceMount:I,keywords:P,...E}=e;return s.createElement(d.sG.div,{ref:(0,c.t)(o,t),...E,id:r,"cmdk-item":"",role:"option","aria-disabled":!!R,"aria-selected":!!h,"data-disabled":!!R,"data-selected":!!h,onPointerMove:R||a.getDisablePointerSelection()?void 0:S,onClick:R?void 0:b},e.children)}),I=s.forwardRef((e,t)=>{let{heading:l,children:n,forceMount:r,...o}=e,i=(0,g.B)(),a=s.useRef(null),u=s.useRef(null),f=(0,g.B)(),p=C(),m=L(e=>!!r||!1===p.filter()||!e.search||e.filtered.groups.has(i));D(()=>p.group(i),[]),k(i,a,[e.value,e.heading,u]);let h=s.useMemo(()=>({id:i,forceMount:r}),[r]);return s.createElement(d.sG.div,{ref:(0,c.t)(a,t),...o,"cmdk-group":"",role:"presentation",hidden:!m||void 0},l&&s.createElement("div",{ref:u,"cmdk-group-heading":"","aria-hidden":!0,id:f},l),H(e,e=>s.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":l?f:void 0},s.createElement(M.Provider,{value:h},e))))}),P=s.forwardRef((e,t)=>{let{alwaysRender:l,...n}=e,r=s.useRef(null),o=L(e=>!e.search);return l||o?s.createElement(d.sG.div,{ref:(0,c.t)(r,t),...n,"cmdk-separator":"",role:"separator"}):null}),E=s.forwardRef((e,t)=>{let{onValueChange:l,...n}=e,r=null!=e.value,o=y(),i=L(e=>e.search),a=L(e=>e.selectedItemId),u=C();return s.useEffect(()=>{null!=e.value&&o.setState("search",e.value)},[e.value]),s.createElement(d.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":u.listId,"aria-labelledby":u.labelId,"aria-activedescendant":a,id:u.inputId,type:"text",value:r?e.value:i,onChange:e=>{r||o.setState("search",e.target.value),null==l||l(e.target.value)}})}),V=s.forwardRef((e,t)=>{let{children:l,label:n="Suggestions",...r}=e,o=s.useRef(null),i=s.useRef(null),a=L(e=>e.selectedItemId),u=C();return s.useEffect(()=>{if(i.current&&o.current){let e=i.current,t=o.current,l,n=new ResizeObserver(()=>{l=requestAnimationFrame(()=>{let l=e.offsetHeight;t.style.setProperty("--cmdk-list-height",l.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(l),n.unobserve(e)}}},[]),s.createElement(d.sG.div,{ref:(0,c.t)(o,t),...r,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":a,"aria-label":n,id:u.listId},H(e,e=>s.createElement("div",{ref:(0,c.t)(i,u.listInnerRef),"cmdk-list-sizer":""},e)))}),_=s.forwardRef((e,t)=>{let{open:l,onOpenChange:n,overlayClassName:r,contentClassName:o,container:i,...a}=e;return s.createElement(u.bL,{open:l,onOpenChange:n},s.createElement(u.ZL,{container:i},s.createElement(u.hJ,{"cmdk-overlay":"",className:r}),s.createElement(u.UC,{"aria-label":e.label,"cmdk-dialog":"",className:o},s.createElement(F,{ref:t,...a}))))}),A=Object.assign(F,{List:V,Item:x,Input:E,Group:I,Separator:P,Dialog:_,Empty:s.forwardRef((e,t)=>L(e=>0===e.filtered.count)?s.createElement(d.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:s.forwardRef((e,t)=>{let{progress:l,children:n,label:r="Loading...",...o}=e;return s.createElement(d.sG.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":l,"aria-valuemin":0,"aria-valuemax":100,"aria-label":r},H(e,e=>s.createElement("div",{"aria-hidden":!0},e)))})});function z(e){let t=s.useRef(e);return D(()=>{t.current=e}),t}var D="undefined"==typeof window?s.useEffect:s.useLayoutEffect;function O(e){let t=s.useRef();return void 0===t.current&&(t.current=e()),t}function L(e){let t=y(),l=()=>e(t.snapshot());return s.useSyncExternalStore(t.subscribe,l,l)}function k(e,t,l){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],r=s.useRef(),o=C();return D(()=>{var i;let a=(()=>{var e;for(let t of l){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():r.current}})(),u=n.map(e=>e.trim());o.value(e,a,u),null==(i=t.current)||i.setAttribute(w,a),r.current=a}),r}var G=()=>{let[e,t]=s.useState(),l=O(()=>new Map);return D(()=>{l.current.forEach(e=>e()),l.current=new Map},[e]),(e,n)=>{l.current.set(e,n),t({})}};function H(e,t){let l,{asChild:n,children:r}=e;return n&&s.isValidElement(r)?s.cloneElement("function"==typeof(l=r.type)?l(r.props):"render"in l?l.render(r.props):r,{ref:r.ref},t(r.props.children)):t(r)}var q={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},30178:(e,t,l)=>{l.d(t,{Hx:()=>g,OB:()=>i,R8:()=>s,V7:()=>c,Yz:()=>u,z3:()=>o});var n=l(99004),r={303:"Multiple adapter contexts detected. This might happen in monorepos.",404:"nuqs requires an adapter to work with your framework.",409:"Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` (via the %s adapter) was about to load on top.",414:"Max safe URL length exceeded. Some browsers may not be able to accept this URL. Consider limiting the amount of state stored in the URL.",429:"URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O",500:"Empty search params cache. Search params can't be accessed in Layouts.",501:"Search params cache already populated. Have you called `parse` twice?"};function o(e){return`[nuqs] ${r[e]}
  See https://err.47ng.com/NUQS-${e}`}function i(e){if(0===e.size)return"";let t=[];for(let[l,n]of e.entries()){let e=l.replace(/#/g,"%23").replace(/&/g,"%26").replace(/\+/g,"%2B").replace(/=/g,"%3D").replace(/\?/g,"%3F");t.push(`${e}=${n.replace(/%/g,"%25").replace(/\+/g,"%2B").replace(/ /g,"+").replace(/#/g,"%23").replace(/&/g,"%26").replace(/"/g,"%22").replace(/'/g,"%27").replace(/`/g,"%60").replace(/</g,"%3C").replace(/>/g,"%3E").replace(/[\x00-\x1F]/g,e=>encodeURIComponent(e))}`)}return"?"+t.join("&")}var a=function(){try{if("undefined"==typeof localStorage)return!1;let e="nuqs-localStorage-test";localStorage.setItem(e,e);let t=localStorage.getItem(e)===e;if(localStorage.removeItem(e),!t)return!1}catch(e){return console.error("[nuqs]: debug mode is disabled (localStorage unavailable).",e),!1}return(localStorage.getItem("debug")??"").includes("nuqs")}();function u(e,...t){if(!a)return;let l=function(e,...t){return e.replace(/%[sfdO]/g,e=>{let l=t.shift();return"%O"===e&&l?JSON.stringify(l).replace(/"([^"]+)":/g,"$1:"):String(l)})}(e,...t);performance.mark(l);try{console.log(e,...t)}catch(e){console.log(l)}}function s(e,...t){a&&console.warn(e,...t)}var d=(0,n.createContext)({useAdapter(){throw Error(o(404))}});function g(e){return({children:t,...l})=>(0,n.createElement)(d.Provider,{...l,value:{useAdapter:e}},t)}function c(){let e=(0,n.useContext)(d);if(!("useAdapter"in e))throw Error(o(404));return e.useAdapter()}d.displayName="NuqsAdapterContext",a&&"undefined"!=typeof window&&(window.__NuqsAdapterContext&&window.__NuqsAdapterContext!==d&&console.error(o(303)),window.__NuqsAdapterContext=d)},46189:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},49896:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]])},57065:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]])},68050:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},70819:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},74064:(e,t,l)=>{l.d(t,{Kv:()=>o,N4:()=>i});var n=l(99004),r=l(84080);function o(e,t){var l,r,o;return e?"function"==typeof(r=l=e)&&(()=>{let e=Object.getPrototypeOf(r);return e.prototype&&e.prototype.isReactComponent})()||"function"==typeof l||"object"==typeof(o=l)&&"symbol"==typeof o.$$typeof&&["react.memo","react.forward_ref"].includes(o.$$typeof.description)?n.createElement(e,t):e:null}function i(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[l]=n.useState(()=>({current:(0,r.ZR)(t)})),[o,i]=n.useState(()=>l.current.initialState);return l.current.setOptions(t=>({...t,...e,state:{...o,...e.state},onStateChange:t=>{i(t),null==e.onStateChange||e.onStateChange(t)}})),l.current}},74453:(e,t,l)=>{l.d(t,{CC:()=>T,Q6:()=>B,bL:()=>j,zi:()=>U});var n=l(99004),r=l(67051),o=l(84732),i=l(39552),a=l(38774),u=l(18608),s=l(51825),d=l(10751),g=l(66042),c=l(51452),f=l(59694),p=l(52880),m=["PageUp","PageDown"],h=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},w="Slider",[b,S,C]=(0,f.N)(w),[R,y]=(0,a.A)(w,[C]),[M,F]=R(w),x=n.forwardRef((e,t)=>{let{name:l,min:i=0,max:a=100,step:s=1,orientation:d="horizontal",disabled:g=!1,minStepsBetweenThumbs:c=0,defaultValue:f=[i],value:v,onValueChange:w=()=>{},onValueCommit:S=()=>{},inverted:C=!1,form:R,...y}=e,F=n.useRef(new Set),x=n.useRef(0),I="horizontal"===d,[P=[],_]=(0,u.i)({prop:v,defaultProp:f,onChange:e=>{var t;null==(t=[...F.current][x.current])||t.focus(),w(e)}}),A=n.useRef(P);function z(e,t){let{commit:l}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1};let n=(String(s).split(".")[1]||"").length,o=function(e,t){let l=Math.pow(10,t);return Math.round(e*l)/l}(Math.round((e-i)/s)*s+i,n),u=(0,r.q)(o,[i,a]);_(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,l=arguments.length>2?arguments[2]:void 0,n=[...e];return n[l]=t,n.sort((e,t)=>e-t)}(e,u,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,l)=>e[l+1]-t))>=t;return!0}(n,c*s))return e;{x.current=n.indexOf(u);let t=String(n)!==String(e);return t&&l&&S(n),t?n:e}})}return(0,p.jsx)(M,{scope:e.__scopeSlider,name:l,disabled:g,min:i,max:a,valueIndexToChangeRef:x,thumbs:F.current,values:P,orientation:d,form:R,children:(0,p.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,p.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,p.jsx)(I?E:V,{"aria-disabled":g,"data-disabled":g?"":void 0,...y,ref:t,onPointerDown:(0,o.m)(y.onPointerDown,()=>{g||(A.current=P)}),min:i,max:a,inverted:C,onSlideStart:g?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let l=e.map(e=>Math.abs(e-t)),n=Math.min(...l);return l.indexOf(n)}(P,e);z(e,t)},onSlideMove:g?void 0:function(e){z(e,x.current)},onSlideEnd:g?void 0:function(){let e=A.current[x.current];P[x.current]!==e&&S(P)},onHomeKeyDown:()=>!g&&z(i,0,{commit:!0}),onEndKeyDown:()=>!g&&z(a,P.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:l}=e;if(!g){let e=m.includes(t.key)||t.shiftKey&&h.includes(t.key),n=x.current;z(P[n]+s*(e?10:1)*l,n,{commit:!0})}}})})})})});x.displayName=w;var[I,P]=R(w,{startEdge:"left",endEdge:"right",size:"width",direction:1}),E=n.forwardRef((e,t)=>{let{min:l,max:r,dir:o,inverted:a,onSlideStart:u,onSlideMove:d,onSlideEnd:g,onStepKeyDown:c,...f}=e,[m,h]=n.useState(null),w=(0,i.s)(t,e=>h(e)),b=n.useRef(void 0),S=(0,s.jH)(o),C="ltr"===S,R=C&&!a||!C&&a;function y(e){let t=b.current||m.getBoundingClientRect(),n=N([0,t.width],R?[l,r]:[r,l]);return b.current=t,n(e-t.left)}return(0,p.jsx)(I,{scope:e.__scopeSlider,startEdge:R?"left":"right",endEdge:R?"right":"left",direction:R?1:-1,size:"width",children:(0,p.jsx)(_,{dir:S,"data-orientation":"horizontal",...f,ref:w,style:{...f.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=y(e.clientX);null==u||u(t)},onSlideMove:e=>{let t=y(e.clientX);null==d||d(t)},onSlideEnd:()=>{b.current=void 0,null==g||g()},onStepKeyDown:e=>{let t=v[R?"from-left":"from-right"].includes(e.key);null==c||c({event:e,direction:t?-1:1})}})})}),V=n.forwardRef((e,t)=>{let{min:l,max:r,inverted:o,onSlideStart:a,onSlideMove:u,onSlideEnd:s,onStepKeyDown:d,...g}=e,c=n.useRef(null),f=(0,i.s)(t,c),m=n.useRef(void 0),h=!o;function w(e){let t=m.current||c.current.getBoundingClientRect(),n=N([0,t.height],h?[r,l]:[l,r]);return m.current=t,n(e-t.top)}return(0,p.jsx)(I,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:(0,p.jsx)(_,{"data-orientation":"vertical",...g,ref:f,style:{...g.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=w(e.clientY);null==a||a(t)},onSlideMove:e=>{let t=w(e.clientY);null==u||u(t)},onSlideEnd:()=>{m.current=void 0,null==s||s()},onStepKeyDown:e=>{let t=v[h?"from-bottom":"from-top"].includes(e.key);null==d||d({event:e,direction:t?-1:1})}})})}),_=n.forwardRef((e,t)=>{let{__scopeSlider:l,onSlideStart:n,onSlideMove:r,onSlideEnd:i,onHomeKeyDown:a,onEndKeyDown:u,onStepKeyDown:s,...d}=e,g=F(w,l);return(0,p.jsx)(c.sG.span,{...d,ref:t,onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(u(e),e.preventDefault()):m.concat(h).includes(e.key)&&(s(e),e.preventDefault())}),onPointerDown:(0,o.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),g.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,o.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&r(e)}),onPointerUp:(0,o.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),A="SliderTrack",z=n.forwardRef((e,t)=>{let{__scopeSlider:l,...n}=e,r=F(A,l);return(0,p.jsx)(c.sG.span,{"data-disabled":r.disabled?"":void 0,"data-orientation":r.orientation,...n,ref:t})});z.displayName=A;var D="SliderRange",O=n.forwardRef((e,t)=>{let{__scopeSlider:l,...r}=e,o=F(D,l),a=P(D,l),u=n.useRef(null),s=(0,i.s)(t,u),d=o.values.length,g=o.values.map(e=>q(e,o.min,o.max)),f=d>1?Math.min(...g):0,m=100-Math.max(...g);return(0,p.jsx)(c.sG.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:s,style:{...e.style,[a.startEdge]:f+"%",[a.endEdge]:m+"%"}})});O.displayName=D;var L="SliderThumb",k=n.forwardRef((e,t)=>{let l=S(e.__scopeSlider),[r,o]=n.useState(null),a=(0,i.s)(t,e=>o(e)),u=n.useMemo(()=>r?l().findIndex(e=>e.ref.current===r):-1,[l,r]);return(0,p.jsx)(G,{...e,ref:a,index:u})}),G=n.forwardRef((e,t)=>{let{__scopeSlider:l,index:r,name:a,...u}=e,s=F(L,l),d=P(L,l),[f,m]=n.useState(null),h=(0,i.s)(t,e=>m(e)),v=!f||s.form||!!f.closest("form"),w=(0,g.X)(f),S=s.values[r],C=void 0===S?0:q(S,s.min,s.max),R=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(r,s.values.length),y=null==w?void 0:w[d.size],M=y?function(e,t,l){let n=e/2,r=N([0,50],[0,n]);return(n-r(t)*l)*l}(y,C,d.direction):0;return n.useEffect(()=>{if(f)return s.thumbs.add(f),()=>{s.thumbs.delete(f)}},[f,s.thumbs]),(0,p.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:"calc(".concat(C,"% + ").concat(M,"px)")},children:[(0,p.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,p.jsx)(c.sG.span,{role:"slider","aria-label":e["aria-label"]||R,"aria-valuemin":s.min,"aria-valuenow":S,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...u,ref:h,style:void 0===S?{display:"none"}:e.style,onFocus:(0,o.m)(e.onFocus,()=>{s.valueIndexToChangeRef.current=r})})}),v&&(0,p.jsx)(H,{name:null!=a?a:s.name?s.name+(s.values.length>1?"[]":""):void 0,form:s.form,value:S},r)]})});k.displayName=L;var H=e=>{let{value:t,...l}=e,r=n.useRef(null),o=(0,d.Z)(t);return n.useEffect(()=>{let e=r.current,l=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(o!==t&&l){let n=new Event("input",{bubbles:!0});l.call(e,t),e.dispatchEvent(n)}},[o,t]),(0,p.jsx)("input",{style:{display:"none"},...l,ref:r,defaultValue:t})};function q(e,t,l){return(0,r.q)(100/(l-t)*(e-t),[0,100])}function N(e,t){return l=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(l-e[0])}}var j=x,T=z,B=O,U=k},78912:(e,t,l)=>{l.d(t,{A:()=>n});let n=(0,l(23278).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84080:(e,t,l)=>{function n(e,t){return"function"==typeof e?e(t):e}function r(e,t){return l=>{t.setState(t=>({...t,[e]:n(l,t[e])}))}}function o(e){return e instanceof Function}l.d(t,{HT:()=>B,ZR:()=>T,h5:()=>J,hM:()=>X,kQ:()=>K,kW:()=>Z,oS:()=>Y,tX:()=>U});function i(e,t,l){let n,r=[];return o=>{let i,a;l.key&&l.debug&&(i=Date.now());let u=e(o);if(!(u.length!==r.length||u.some((e,t)=>r[t]!==e)))return n;if(r=u,l.key&&l.debug&&(a=Date.now()),n=t(...u),null==l||null==l.onChange||l.onChange(n),l.key&&l.debug&&null!=l&&l.debug()){let e=Math.round((Date.now()-i)*100)/100,t=Math.round((Date.now()-a)*100)/100,n=t/16,r=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${r(t,5)} /${r(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*n,120))}deg 100% 31%);`,null==l?void 0:l.key)}return n}}function a(e,t,l,n){return{debug:()=>{var l;return null!=(l=null==e?void 0:e.debugAll)?l:e[t]},key:!1,onChange:n}}let u="debugHeaders";function s(e,t,l){var n;let r={id:null!=(n=l.id)?n:t.id,column:t,index:l.index,isPlaceholder:!!l.isPlaceholder,placeholderId:l.placeholderId,depth:l.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(t),e.push(l)};return t(r),e},getContext:()=>({table:e,header:r,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(r,e)}),r}function d(e,t,l,n){var r,o;let i=0,a=function(e,t){void 0===t&&(t=1),i=Math.max(i,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var l;null!=(l=e.columns)&&l.length&&a(e.columns,t+1)},0)};a(e);let u=[],d=(e,t)=>{let r={depth:t,id:[n,`${t}`].filter(Boolean).join("_"),headers:[]},o=[];e.forEach(e=>{let i,a=[...o].reverse()[0],u=e.column.depth===r.depth,d=!1;if(u&&e.column.parent?i=e.column.parent:(i=e.column,d=!0),a&&(null==a?void 0:a.column)===i)a.subHeaders.push(e);else{let r=s(l,i,{id:[n,t,i.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:d,placeholderId:d?`${o.filter(e=>e.column===i).length}`:void 0,depth:t,index:o.length});r.subHeaders.push(e),o.push(r)}r.headers.push(e),e.headerGroup=r}),u.push(r),t>0&&d(o,t-1)};d(t.map((e,t)=>s(l,e,{depth:i,index:t})),i-1),u.reverse();let g=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,l=0,n=[0];return e.subHeaders&&e.subHeaders.length?(n=[],g(e.subHeaders).forEach(e=>{let{colSpan:l,rowSpan:r}=e;t+=l,n.push(r)})):t=1,l+=Math.min(...n),e.colSpan=t,e.rowSpan=l,{colSpan:t,rowSpan:l}});return g(null!=(r=null==(o=u[0])?void 0:o.headers)?r:[]),u}let g=(e,t,l,n,r,o,u)=>{let s={id:t,index:n,original:l,depth:r,parentId:u,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let l=e.getColumn(t);if(null!=l&&l.accessorFn)return s._valuesCache[t]=l.accessorFn(s.original,n),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let l=e.getColumn(t);if(null!=l&&l.accessorFn)return l.columnDef.getUniqueValues?s._uniqueValuesCache[t]=l.columnDef.getUniqueValues(s.original,n):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]},renderValue:t=>{var l;return null!=(l=s.getValue(t))?l:e.options.renderFallbackValue},subRows:null!=o?o:[],getLeafRows:()=>(function(e,t){let l=[],n=e=>{e.forEach(e=>{l.push(e);let r=t(e);null!=r&&r.length&&n(r)})};return n(e),l})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let l=t.getParentRow();if(!l)break;e.push(l),t=l}return e.reverse()},getAllCells:i(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,l,n){let r={id:`${t.id}_${l.id}`,row:t,column:l,getValue:()=>t.getValue(n),renderValue:()=>{var t;return null!=(t=r.getValue())?t:e.options.renderFallbackValue},getContext:i(()=>[e,l,t,r],(e,t,l,n)=>({table:e,column:t,row:l,cell:n,getValue:n.getValue,renderValue:n.renderValue}),a(e.options,"debugCells","cell.getContext"))};return e._features.forEach(n=>{null==n.createCell||n.createCell(r,l,t,e)},{}),r})(e,s,t,t.id)),a(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:i(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),a(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let l=e._features[t];null==l||null==l.createRow||l.createRow(s,e)}return s},c=(e,t,l)=>{var n,r;let o=null==l||null==(n=l.toString())?void 0:n.toLowerCase();return!!(null==(r=e.getValue(t))||null==(r=r.toString())||null==(r=r.toLowerCase())?void 0:r.includes(o))};c.autoRemove=e=>R(e);let f=(e,t,l)=>{var n;return!!(null==(n=e.getValue(t))||null==(n=n.toString())?void 0:n.includes(l))};f.autoRemove=e=>R(e);let p=(e,t,l)=>{var n;return(null==(n=e.getValue(t))||null==(n=n.toString())?void 0:n.toLowerCase())===(null==l?void 0:l.toLowerCase())};p.autoRemove=e=>R(e);let m=(e,t,l)=>{var n;return null==(n=e.getValue(t))?void 0:n.includes(l)};m.autoRemove=e=>R(e);let h=(e,t,l)=>!l.some(l=>{var n;return!(null!=(n=e.getValue(t))&&n.includes(l))});h.autoRemove=e=>R(e)||!(null!=e&&e.length);let v=(e,t,l)=>l.some(l=>{var n;return null==(n=e.getValue(t))?void 0:n.includes(l)});v.autoRemove=e=>R(e)||!(null!=e&&e.length);let w=(e,t,l)=>e.getValue(t)===l;w.autoRemove=e=>R(e);let b=(e,t,l)=>e.getValue(t)==l;b.autoRemove=e=>R(e);let S=(e,t,l)=>{let[n,r]=l,o=e.getValue(t);return o>=n&&o<=r};S.resolveFilterValue=e=>{let[t,l]=e,n="number"!=typeof t?parseFloat(t):t,r="number"!=typeof l?parseFloat(l):l,o=null===t||Number.isNaN(n)?-1/0:n,i=null===l||Number.isNaN(r)?1/0:r;if(o>i){let e=o;o=i,i=e}return[o,i]},S.autoRemove=e=>R(e)||R(e[0])&&R(e[1]);let C={includesString:c,includesStringSensitive:f,equalsString:p,arrIncludes:m,arrIncludesAll:h,arrIncludesSome:v,equals:w,weakEquals:b,inNumberRange:S};function R(e){return null==e||""===e}function y(e,t,l){return!!e&&!!e.autoRemove&&e.autoRemove(t,l)||void 0===t||"string"==typeof t&&!t}let M={sum:(e,t,l)=>l.reduce((t,l)=>{let n=l.getValue(e);return t+("number"==typeof n?n:0)},0),min:(e,t,l)=>{let n;return l.forEach(t=>{let l=t.getValue(e);null!=l&&(n>l||void 0===n&&l>=l)&&(n=l)}),n},max:(e,t,l)=>{let n;return l.forEach(t=>{let l=t.getValue(e);null!=l&&(n<l||void 0===n&&l>=l)&&(n=l)}),n},extent:(e,t,l)=>{let n,r;return l.forEach(t=>{let l=t.getValue(e);null!=l&&(void 0===n?l>=l&&(n=r=l):(n>l&&(n=l),r<l&&(r=l)))}),[n,r]},mean:(e,t)=>{let l=0,n=0;if(t.forEach(t=>{let r=t.getValue(e);null!=r&&(r*=1)>=r&&(++l,n+=r)}),l)return n/l},median:(e,t)=>{if(!t.length)return;let l=t.map(t=>t.getValue(e));if(!function(e){return Array.isArray(e)&&e.every(e=>"number"==typeof e)}(l))return;if(1===l.length)return l[0];let n=Math.floor(l.length/2),r=l.sort((e,t)=>e-t);return l.length%2!=0?r[n]:(r[n-1]+r[n])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},F=()=>({left:[],right:[]}),x={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},I=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),P=null;function E(e){return"touchstart"===e.type}function V(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let _=()=>({pageIndex:0,pageSize:10}),A=()=>({top:[],bottom:[]}),z=(e,t,l,n,r)=>{var o;let i=r.getRow(t,!0);l?(i.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),i.getCanSelect()&&(e[t]=!0)):delete e[t],n&&null!=(o=i.subRows)&&o.length&&i.getCanSelectSubRows()&&i.subRows.forEach(t=>z(e,t.id,l,n,r))};function D(e,t){let l=e.getState().rowSelection,n=[],r={},o=function(e,t){return e.map(e=>{var t;let i=O(e,l);if(i&&(n.push(e),r[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:o(e.subRows)}),i)return e}).filter(Boolean)};return{rows:o(t.rows),flatRows:n,rowsById:r}}function O(e,t){var l;return null!=(l=t[e.id])&&l}function L(e,t,l){var n;if(!(null!=(n=e.subRows)&&n.length))return!1;let r=!0,o=!1;return e.subRows.forEach(e=>{if((!o||r)&&(e.getCanSelect()&&(O(e,t)?o=!0:r=!1),e.subRows&&e.subRows.length)){let l=L(e,t);"all"===l?o=!0:("some"===l&&(o=!0),r=!1)}}),r?"all":!!o&&"some"}let k=/([0-9]+)/gm;function G(e,t){return e===t?0:e>t?1:-1}function H(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function q(e,t){let l=e.split(k).filter(Boolean),n=t.split(k).filter(Boolean);for(;l.length&&n.length;){let e=l.shift(),t=n.shift(),r=parseInt(e,10),o=parseInt(t,10),i=[r,o].sort();if(isNaN(i[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(i[1]))return isNaN(r)?-1:1;if(r>o)return 1;if(o>r)return -1}return l.length-n.length}let N={alphanumeric:(e,t,l)=>q(H(e.getValue(l)).toLowerCase(),H(t.getValue(l)).toLowerCase()),alphanumericCaseSensitive:(e,t,l)=>q(H(e.getValue(l)),H(t.getValue(l))),text:(e,t,l)=>G(H(e.getValue(l)).toLowerCase(),H(t.getValue(l)).toLowerCase()),textCaseSensitive:(e,t,l)=>G(H(e.getValue(l)),H(t.getValue(l))),datetime:(e,t,l)=>{let n=e.getValue(l),r=t.getValue(l);return n>r?1:n<r?-1:0},basic:(e,t,l)=>G(e.getValue(l),t.getValue(l))},j=[{createTable:e=>{e.getHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,l,n,r)=>{var o,i;let a=null!=(o=null==n?void 0:n.map(e=>l.find(t=>t.id===e)).filter(Boolean))?o:[],u=null!=(i=null==r?void 0:r.map(e=>l.find(t=>t.id===e)).filter(Boolean))?i:[];return d(t,[...a,...l.filter(e=>!(null!=n&&n.includes(e.id))&&!(null!=r&&r.includes(e.id))),...u],e)},a(e.options,u,"getHeaderGroups")),e.getCenterHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,l,n,r)=>d(t,l=l.filter(e=>!(null!=n&&n.includes(e.id))&&!(null!=r&&r.includes(e.id))),e,"center"),a(e.options,u,"getCenterHeaderGroups")),e.getLeftHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,l,n)=>{var r;return d(t,null!=(r=null==n?void 0:n.map(e=>l.find(t=>t.id===e)).filter(Boolean))?r:[],e,"left")},a(e.options,u,"getLeftHeaderGroups")),e.getRightHeaderGroups=i(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,l,n)=>{var r;return d(t,null!=(r=null==n?void 0:n.map(e=>l.find(t=>t.id===e)).filter(Boolean))?r:[],e,"right")},a(e.options,u,"getRightHeaderGroups")),e.getFooterGroups=i(()=>[e.getHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getFooterGroups")),e.getLeftFooterGroups=i(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getLeftFooterGroups")),e.getCenterFooterGroups=i(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getCenterFooterGroups")),e.getRightFooterGroups=i(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),a(e.options,u,"getRightFooterGroups")),e.getFlatHeaders=i(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getFlatHeaders")),e.getLeftFlatHeaders=i(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getLeftFlatHeaders")),e.getCenterFlatHeaders=i(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getCenterFlatHeaders")),e.getRightFlatHeaders=i(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),a(e.options,u,"getRightFlatHeaders")),e.getCenterLeafHeaders=i(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getCenterLeafHeaders")),e.getLeftLeafHeaders=i(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getLeftLeafHeaders")),e.getRightLeafHeaders=i(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),a(e.options,u,"getRightLeafHeaders")),e.getLeafHeaders=i(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,l)=>{var n,r,o,i,a,u;return[...null!=(n=null==(r=e[0])?void 0:r.headers)?n:[],...null!=(o=null==(i=t[0])?void 0:i.headers)?o:[],...null!=(a=null==(u=l[0])?void 0:u.headers)?a:[]].map(e=>e.getLeafHeaders()).flat()},a(e.options,u,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:r("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=l=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=l?l:!e.getIsVisible()}))},e.getIsVisible=()=>{var l,n;let r=e.columns;return null==(l=r.length?r.some(e=>e.getIsVisible()):null==(n=t.getState().columnVisibility)?void 0:n[e.id])||l},e.getCanHide=()=>{var l,n;return(null==(l=e.columnDef.enableHiding)||l)&&(null==(n=t.options.enableHiding)||n)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=i(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),a(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=i(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,l)=>[...e,...t,...l],a(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,l)=>i(()=>[l(),l().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),a(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var l;e.setColumnVisibility(t?{}:null!=(l=e.initialState.columnVisibility)?l:{})},e.toggleAllColumnsVisible=t=>{var l;t=null!=(l=t)?l:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,l)=>({...e,[l.id]:t||!(null!=l.getCanHide&&l.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var l;e.toggleAllColumnsVisible(null==(l=t.target)?void 0:l.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:r("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=i(e=>[V(t,e)],t=>t.findIndex(t=>t.id===e.id),a(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=l=>{var n;return(null==(n=V(t,l)[0])?void 0:n.id)===e.id},e.getIsLastColumn=l=>{var n;let r=V(t,l);return(null==(n=r[r.length-1])?void 0:n.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var l;e.setColumnOrder(t?[]:null!=(l=e.initialState.columnOrder)?l:[])},e._getOrderColumnsFn=i(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,l)=>n=>{let r=[];if(null!=e&&e.length){let t=[...e],l=[...n];for(;l.length&&t.length;){let e=t.shift(),n=l.findIndex(t=>t.id===e);n>-1&&r.push(l.splice(n,1)[0])}r=[...r,...l]}else r=n;return function(e,t,l){if(!(null!=t&&t.length)||!l)return e;let n=e.filter(e=>!t.includes(e.id));return"remove"===l?n:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...n]}(r,t,l)},a(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:F(),...e}),getDefaultOptions:e=>({onColumnPinningChange:r("columnPinning",e)}),createColumn:(e,t)=>{e.pin=l=>{let n=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,r,o,i,a,u;return"right"===l?{left:(null!=(o=null==e?void 0:e.left)?o:[]).filter(e=>!(null!=n&&n.includes(e))),right:[...(null!=(i=null==e?void 0:e.right)?i:[]).filter(e=>!(null!=n&&n.includes(e))),...n]}:"left"===l?{left:[...(null!=(a=null==e?void 0:e.left)?a:[]).filter(e=>!(null!=n&&n.includes(e))),...n],right:(null!=(u=null==e?void 0:e.right)?u:[]).filter(e=>!(null!=n&&n.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=n&&n.includes(e))),right:(null!=(r=null==e?void 0:e.right)?r:[]).filter(e=>!(null!=n&&n.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var l,n,r;return(null==(l=e.columnDef.enablePinning)||l)&&(null==(n=null!=(r=t.options.enableColumnPinning)?r:t.options.enablePinning)||n)}),e.getIsPinned=()=>{let l=e.getLeafColumns().map(e=>e.id),{left:n,right:r}=t.getState().columnPinning,o=l.some(e=>null==n?void 0:n.includes(e)),i=l.some(e=>null==r?void 0:r.includes(e));return o?"left":!!i&&"right"},e.getPinnedIndex=()=>{var l,n;let r=e.getIsPinned();return r?null!=(l=null==(n=t.getState().columnPinning)||null==(n=n[r])?void 0:n.indexOf(e.id))?l:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,l)=>{let n=[...null!=t?t:[],...null!=l?l:[]];return e.filter(e=>!n.includes(e.column.id))},a(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),a(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=i(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),a(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var l,n;return e.setColumnPinning(t?F():null!=(l=null==(n=e.initialState)?void 0:n.columnPinning)?l:F())},e.getIsSomeColumnsPinned=t=>{var l,n,r;let o=e.getState().columnPinning;return t?!!(null==(l=o[t])?void 0:l.length):!!((null==(n=o.left)?void 0:n.length)||(null==(r=o.right)?void 0:r.length))},e.getLeftLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),a(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=i(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,l)=>{let n=[...null!=t?t:[],...null!=l?l:[]];return e.filter(e=>!n.includes(e.id))},a(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:r("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let l=t.getCoreRowModel().flatRows[0],n=null==l?void 0:l.getValue(e.id);return"string"==typeof n?C.includesString:"number"==typeof n?C.inNumberRange:"boolean"==typeof n||null!==n&&"object"==typeof n?C.equals:Array.isArray(n)?C.arrIncludes:C.weakEquals},e.getFilterFn=()=>{var l,n;return o(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(l=null==(n=t.options.filterFns)?void 0:n[e.columnDef.filterFn])?l:C[e.columnDef.filterFn]},e.getCanFilter=()=>{var l,n,r;return(null==(l=e.columnDef.enableColumnFilter)||l)&&(null==(n=t.options.enableColumnFilters)||n)&&(null==(r=t.options.enableFilters)||r)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var l;return null==(l=t.getState().columnFilters)||null==(l=l.find(t=>t.id===e.id))?void 0:l.value},e.getFilterIndex=()=>{var l,n;return null!=(l=null==(n=t.getState().columnFilters)?void 0:n.findIndex(t=>t.id===e.id))?l:-1},e.setFilterValue=l=>{t.setColumnFilters(t=>{var r,o;let i=e.getFilterFn(),a=null==t?void 0:t.find(t=>t.id===e.id),u=n(l,a?a.value:void 0);if(y(i,u,e))return null!=(r=null==t?void 0:t.filter(t=>t.id!==e.id))?r:[];let s={id:e.id,value:u};return a?null!=(o=null==t?void 0:t.map(t=>t.id===e.id?s:t))?o:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let l=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var r;return null==(r=n(t,e))?void 0:r.filter(e=>{let t=l.find(t=>t.id===e.id);return!(t&&y(t.getFilterFn(),e.value,t))&&!0})})},e.resetColumnFilters=t=>{var l,n;e.setColumnFilters(t?[]:null!=(l=null==(n=e.initialState)?void 0:n.columnFilters)?l:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:r("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var l;let n=null==(l=e.getCoreRowModel().flatRows[0])||null==(l=l._getAllCellsByColumnId()[t.id])?void 0:l.getValue();return"string"==typeof n||"number"==typeof n}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var l,n,r,o;return(null==(l=e.columnDef.enableGlobalFilter)||l)&&(null==(n=t.options.enableGlobalFilter)||n)&&(null==(r=t.options.enableFilters)||r)&&(null==(o=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||o)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>C.includesString,e.getGlobalFilterFn=()=>{var t,l;let{globalFilterFn:n}=e.options;return o(n)?n:"auto"===n?e.getGlobalAutoFilterFn():null!=(t=null==(l=e.options.filterFns)?void 0:l[n])?t:C[n]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:r("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let l=t.getFilteredRowModel().flatRows.slice(10),n=!1;for(let t of l){let l=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(l))return N.datetime;if("string"==typeof l&&(n=!0,l.split(k).length>1))return N.alphanumeric}return n?N.text:N.basic},e.getAutoSortDir=()=>{let l=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==l?void 0:l.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var l,n;if(!e)throw Error();return o(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(l=null==(n=t.options.sortingFns)?void 0:n[e.columnDef.sortingFn])?l:N[e.columnDef.sortingFn]},e.toggleSorting=(l,n)=>{let r=e.getNextSortingOrder(),o=null!=l;t.setSorting(i=>{let a,u=null==i?void 0:i.find(t=>t.id===e.id),s=null==i?void 0:i.findIndex(t=>t.id===e.id),d=[],g=o?l:"desc"===r;if("toggle"!=(a=null!=i&&i.length&&e.getCanMultiSort()&&n?u?"toggle":"add":null!=i&&i.length&&s!==i.length-1?"replace":u?"toggle":"replace")||o||r||(a="remove"),"add"===a){var c;(d=[...i,{id:e.id,desc:g}]).splice(0,d.length-(null!=(c=t.options.maxMultiSortColCount)?c:Number.MAX_SAFE_INTEGER))}else d="toggle"===a?i.map(t=>t.id===e.id?{...t,desc:g}:t):"remove"===a?i.filter(t=>t.id!==e.id):[{id:e.id,desc:g}];return d})},e.getFirstSortDir=()=>{var l,n;return(null!=(l=null!=(n=e.columnDef.sortDescFirst)?n:t.options.sortDescFirst)?l:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=l=>{var n,r;let o=e.getFirstSortDir(),i=e.getIsSorted();return i?(i===o||null!=(n=t.options.enableSortingRemoval)&&!n||!!l&&null!=(r=t.options.enableMultiRemove)&&!r)&&("desc"===i?"asc":"desc"):o},e.getCanSort=()=>{var l,n;return(null==(l=e.columnDef.enableSorting)||l)&&(null==(n=t.options.enableSorting)||n)&&!!e.accessorFn},e.getCanMultiSort=()=>{var l,n;return null!=(l=null!=(n=e.columnDef.enableMultiSort)?n:t.options.enableMultiSort)?l:!!e.accessorFn},e.getIsSorted=()=>{var l;let n=null==(l=t.getState().sorting)?void 0:l.find(t=>t.id===e.id);return!!n&&(n.desc?"desc":"asc")},e.getSortIndex=()=>{var l,n;return null!=(l=null==(n=t.getState().sorting)?void 0:n.findIndex(t=>t.id===e.id))?l:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let l=e.getCanSort();return n=>{l&&(null==n.persist||n.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(n))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var l,n;e.setSorting(t?[]:null!=(l=null==(n=e.initialState)?void 0:n.sorting)?l:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,l;return null!=(t=null==(l=e.getValue())||null==l.toString?void 0:l.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:r("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var l,n;return(null==(l=e.columnDef.enableGrouping)||l)&&(null==(n=t.options.enableGrouping)||n)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var l;return null==(l=t.getState().grouping)?void 0:l.includes(e.id)},e.getGroupedIndex=()=>{var l;return null==(l=t.getState().grouping)?void 0:l.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let l=t.getCoreRowModel().flatRows[0],n=null==l?void 0:l.getValue(e.id);return"number"==typeof n?M.sum:"[object Date]"===Object.prototype.toString.call(n)?M.extent:void 0},e.getAggregationFn=()=>{var l,n;if(!e)throw Error();return o(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(l=null==(n=t.options.aggregationFns)?void 0:n[e.columnDef.aggregationFn])?l:M[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var l,n;e.setGrouping(t?[]:null!=(l=null==(n=e.initialState)?void 0:n.grouping)?l:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=l=>{if(e._groupingValuesCache.hasOwnProperty(l))return e._groupingValuesCache[l];let n=t.getColumn(l);return null!=n&&n.columnDef.getGroupingValue?(e._groupingValuesCache[l]=n.columnDef.getGroupingValue(e.original),e._groupingValuesCache[l]):e.getValue(l)},e._groupingValuesCache={}},createCell:(e,t,l,n)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===l.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=l.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:r("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,l=!1;e._autoResetExpanded=()=>{var n,r;if(!t)return void e._queue(()=>{t=!0});if(null!=(n=null!=(r=e.options.autoResetAll)?r:e.options.autoResetExpanded)?n:!e.options.manualExpanding){if(l)return;l=!0,e._queue(()=>{e.resetExpanded(),l=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var l,n;e.setExpanded(t?{}:null!=(l=null==(n=e.initialState)?void 0:n.expanded)?l:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let l=e.split(".");t=Math.max(t,l.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=l=>{t.setExpanded(n=>{var r;let o=!0===n||!!(null!=n&&n[e.id]),i={};if(!0===n?Object.keys(t.getRowModel().rowsById).forEach(e=>{i[e]=!0}):i=n,l=null!=(r=l)?r:!o,!o&&l)return{...i,[e.id]:!0};if(o&&!l){let{[e.id]:t,...l}=i;return l}return n})},e.getIsExpanded=()=>{var l;let n=t.getState().expanded;return!!(null!=(l=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?l:!0===n||(null==n?void 0:n[e.id]))},e.getCanExpand=()=>{var l,n,r;return null!=(l=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?l:(null==(n=t.options.enableExpanding)||n)&&!!(null!=(r=e.subRows)&&r.length)},e.getIsAllParentsExpanded=()=>{let l=!0,n=e;for(;l&&n.parentId;)l=(n=t.getRow(n.parentId,!0)).getIsExpanded();return l},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{..._(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:r("pagination",e)}),createTable:e=>{let t=!1,l=!1;e._autoResetPageIndex=()=>{var n,r;if(!t)return void e._queue(()=>{t=!0});if(null!=(n=null!=(r=e.options.autoResetAll)?r:e.options.autoResetPageIndex)?n:!e.options.manualPagination){if(l)return;l=!0,e._queue(()=>{e.resetPageIndex(),l=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>n(t,e)),e.resetPagination=t=>{var l;e.setPagination(t?_():null!=(l=e.initialState.pagination)?l:_())},e.setPageIndex=t=>{e.setPagination(l=>{let r=n(t,l.pageIndex);return r=Math.max(0,Math.min(r,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...l,pageIndex:r}})},e.resetPageIndex=t=>{var l,n;e.setPageIndex(t?0:null!=(l=null==(n=e.initialState)||null==(n=n.pagination)?void 0:n.pageIndex)?l:0)},e.resetPageSize=t=>{var l,n;e.setPageSize(t?10:null!=(l=null==(n=e.initialState)||null==(n=n.pagination)?void 0:n.pageSize)?l:10)},e.setPageSize=t=>{e.setPagination(e=>{let l=Math.max(1,n(t,e.pageSize)),r=Math.floor(e.pageSize*e.pageIndex/l);return{...e,pageIndex:r,pageSize:l}})},e.setPageCount=t=>e.setPagination(l=>{var r;let o=n(t,null!=(r=e.options.pageCount)?r:-1);return"number"==typeof o&&(o=Math.max(-1,o)),{...l,pageCount:o}}),e.getPageOptions=i(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},a(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,l=e.getPageCount();return -1===l||0!==l&&t<l-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:A(),...e}),getDefaultOptions:e=>({onRowPinningChange:r("rowPinning",e)}),createRow:(e,t)=>{e.pin=(l,n,r)=>{let o=n?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],i=new Set([...r?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...o]);t.setRowPinning(e=>{var t,n,r,o,a,u;return"bottom"===l?{top:(null!=(r=null==e?void 0:e.top)?r:[]).filter(e=>!(null!=i&&i.has(e))),bottom:[...(null!=(o=null==e?void 0:e.bottom)?o:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)]}:"top"===l?{top:[...(null!=(a=null==e?void 0:e.top)?a:[]).filter(e=>!(null!=i&&i.has(e))),...Array.from(i)],bottom:(null!=(u=null==e?void 0:e.bottom)?u:[]).filter(e=>!(null!=i&&i.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=i&&i.has(e))),bottom:(null!=(n=null==e?void 0:e.bottom)?n:[]).filter(e=>!(null!=i&&i.has(e)))}})},e.getCanPin=()=>{var l;let{enableRowPinning:n,enablePinning:r}=t.options;return"function"==typeof n?n(e):null==(l=null!=n?n:r)||l},e.getIsPinned=()=>{let l=[e.id],{top:n,bottom:r}=t.getState().rowPinning,o=l.some(e=>null==n?void 0:n.includes(e)),i=l.some(e=>null==r?void 0:r.includes(e));return o?"top":!!i&&"bottom"},e.getPinnedIndex=()=>{var l,n;let r=e.getIsPinned();if(!r)return -1;let o=null==(l="top"===r?t.getTopRows():t.getBottomRows())?void 0:l.map(e=>{let{id:t}=e;return t});return null!=(n=null==o?void 0:o.indexOf(e.id))?n:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var l,n;return e.setRowPinning(t?A():null!=(l=null==(n=e.initialState)?void 0:n.rowPinning)?l:A())},e.getIsSomeRowsPinned=t=>{var l,n,r;let o=e.getState().rowPinning;return t?!!(null==(l=o[t])?void 0:l.length):!!((null==(n=o.top)?void 0:n.length)||(null==(r=o.bottom)?void 0:r.length))},e._getPinnedRows=(t,l,n)=>{var r;return(null==(r=e.options.keepPinnedRows)||r?(null!=l?l:[]).map(t=>{let l=e.getRow(t,!0);return l.getIsAllParentsExpanded()?l:null}):(null!=l?l:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:n}))},e.getTopRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,l)=>e._getPinnedRows(t,l,"top"),a(e.options,"debugRows","getTopRows")),e.getBottomRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,l)=>e._getPinnedRows(t,l,"bottom"),a(e.options,"debugRows","getBottomRows")),e.getCenterRows=i(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,l)=>{let n=new Set([...null!=t?t:[],...null!=l?l:[]]);return e.filter(e=>!n.has(e.id))},a(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:r("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var l;return e.setRowSelection(t?{}:null!=(l=e.initialState.rowSelection)?l:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(l=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let n={...l},r=e.getPreGroupedRowModel().flatRows;return t?r.forEach(e=>{e.getCanSelect()&&(n[e.id]=!0)}):r.forEach(e=>{delete n[e.id]}),n})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(l=>{let n=void 0!==t?t:!e.getIsAllPageRowsSelected(),r={...l};return e.getRowModel().rows.forEach(t=>{z(r,t.id,n,!0,e)}),r}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=i(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,l)=>Object.keys(t).length?D(e,l):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=i(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,l)=>Object.keys(t).length?D(e,l):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=i(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,l)=>Object.keys(t).length?D(e,l):{rows:[],flatRows:[],rowsById:{}},a(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:l}=e.getState(),n=!!(t.length&&Object.keys(l).length);return n&&t.some(e=>e.getCanSelect()&&!l[e.id])&&(n=!1),n},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:l}=e.getState(),n=!!t.length;return n&&t.some(e=>!l[e.id])&&(n=!1),n},e.getIsSomeRowsSelected=()=>{var t;let l=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return l>0&&l<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(l,n)=>{let r=e.getIsSelected();t.setRowSelection(o=>{var i;if(l=void 0!==l?l:!r,e.getCanSelect()&&r===l)return o;let a={...o};return z(a,e.id,l,null==(i=null==n?void 0:n.selectChildren)||i,t),a})},e.getIsSelected=()=>{let{rowSelection:l}=t.getState();return O(e,l)},e.getIsSomeSelected=()=>{let{rowSelection:l}=t.getState();return"some"===L(e,l)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:l}=t.getState();return"all"===L(e,l)},e.getCanSelect=()=>{var l;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(l=t.options.enableRowSelection)||l},e.getCanSelectSubRows=()=>{var l;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(l=t.options.enableSubRowSelection)||l},e.getCanMultiSelect=()=>{var l;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(l=t.options.enableMultiRowSelection)||l},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return l=>{var n;t&&e.toggleSelected(null==(n=l.target)?void 0:n.checked)}}}},{getDefaultColumnDef:()=>x,getInitialState:e=>({columnSizing:{},columnSizingInfo:I(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:r("columnSizing",e),onColumnSizingInfoChange:r("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var l,n,r;let o=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(l=e.columnDef.minSize)?l:x.minSize,null!=(n=null!=o?o:e.columnDef.size)?n:x.size),null!=(r=e.columnDef.maxSize)?r:x.maxSize)},e.getStart=i(e=>[e,V(t,e),t.getState().columnSizing],(t,l)=>l.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getStart")),e.getAfter=i(e=>[e,V(t,e),t.getState().columnSizing],(t,l)=>l.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),a(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:l,...n}=t;return n})},e.getCanResize=()=>{var l,n;return(null==(l=e.columnDef.enableResizing)||l)&&(null==(n=t.options.enableColumnResizing)||n)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,l=e=>{if(e.subHeaders.length)e.subHeaders.forEach(l);else{var n;t+=null!=(n=e.column.getSize())?n:0}};return l(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=l=>{let n=t.getColumn(e.column.id),r=null==n?void 0:n.getCanResize();return o=>{if(!n||!r||(null==o.persist||o.persist(),E(o)&&o.touches&&o.touches.length>1))return;let i=e.getSize(),a=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[n.id,n.getSize()]],u=E(o)?Math.round(o.touches[0].clientX):o.clientX,s={},d=(e,l)=>{"number"==typeof l&&(t.setColumnSizingInfo(e=>{var n,r;let o="rtl"===t.options.columnResizeDirection?-1:1,i=(l-(null!=(n=null==e?void 0:e.startOffset)?n:0))*o,a=Math.max(i/(null!=(r=null==e?void 0:e.startSize)?r:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,l]=e;s[t]=Math.round(100*Math.max(l+l*a,0))/100}),{...e,deltaOffset:i,deltaPercentage:a}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},g=e=>d("move",e),c=e=>{d("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},f=l||"undefined"!=typeof document?document:null,p={moveHandler:e=>g(e.clientX),upHandler:e=>{null==f||f.removeEventListener("mousemove",p.moveHandler),null==f||f.removeEventListener("mouseup",p.upHandler),c(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),g(e.touches[0].clientX),!1),upHandler:e=>{var t;null==f||f.removeEventListener("touchmove",m.moveHandler),null==f||f.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(null==(t=e.touches[0])?void 0:t.clientX)}},h=!!function(){if("boolean"==typeof P)return P;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return P=e}()&&{passive:!1};E(o)?(null==f||f.addEventListener("touchmove",m.moveHandler,h),null==f||f.addEventListener("touchend",m.upHandler,h)):(null==f||f.addEventListener("mousemove",p.moveHandler,h),null==f||f.addEventListener("mouseup",p.upHandler,h)),t.setColumnSizingInfo(e=>({...e,startOffset:u,startSize:i,deltaOffset:0,deltaPercentage:0,columnSizingStart:a,isResizingColumn:n.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var l;e.setColumnSizing(t?{}:null!=(l=e.initialState.columnSizing)?l:{})},e.resetHeaderSizeInfo=t=>{var l;e.setColumnSizingInfo(t?I():null!=(l=e.initialState.columnSizingInfo)?l:I())},e.getTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getLeftHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getCenterHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,l;return null!=(t=null==(l=e.getRightHeaderGroups()[0])?void 0:l.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function T(e){var t,l;let r=[...j,...null!=(t=e._features)?t:[]],o={_features:r},u=o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(o)),{}),s=e=>o.options.mergeOptions?o.options.mergeOptions(u,e):{...u,...e},d={...null!=(l=e.initialState)?l:{}};o._features.forEach(e=>{var t;d=null!=(t=null==e.getInitialState?void 0:e.getInitialState(d))?t:d});let g=[],c=!1,f={_features:r,options:{...u,...e},initialState:d,_queue:e=>{g.push(e),c||(c=!0,Promise.resolve().then(()=>{for(;g.length;)g.shift()();c=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{o.setState(o.initialState)},setOptions:e=>{let t=n(e,o.options);o.options=s(t)},getState:()=>o.options.state,setState:e=>{null==o.options.onStateChange||o.options.onStateChange(e)},_getRowId:(e,t,l)=>{var n;return null!=(n=null==o.options.getRowId?void 0:o.options.getRowId(e,t,l))?n:`${l?[l.id,t].join("."):t}`},getCoreRowModel:()=>(o._getCoreRowModel||(o._getCoreRowModel=o.options.getCoreRowModel(o)),o._getCoreRowModel()),getRowModel:()=>o.getPaginationRowModel(),getRow:(e,t)=>{let l=(t?o.getPrePaginationRowModel():o.getRowModel()).rowsById[e];if(!l&&!(l=o.getCoreRowModel().rowsById[e]))throw Error();return l},_getDefaultColumnDef:i(()=>[o.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,l;return null!=(t=null==(l=e.renderValue())||null==l.toString?void 0:l.toString())?t:null},...o._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},a(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>o.options.columns,getAllColumns:i(()=>[o._getColumnDefs()],e=>{let t=function(e,l,n){return void 0===n&&(n=0),e.map(e=>{let r=function(e,t,l,n){var r,o;let u,s={...e._getDefaultColumnDef(),...t},d=s.accessorKey,g=null!=(r=null!=(o=s.id)?o:d?"function"==typeof String.prototype.replaceAll?d.replaceAll(".","_"):d.replace(/\./g,"_"):void 0)?r:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?u=s.accessorFn:d&&(u=d.includes(".")?e=>{let t=e;for(let e of d.split(".")){var l;t=null==(l=t)?void 0:l[e]}return t}:e=>e[s.accessorKey]),!g)throw Error();let c={id:`${String(g)}`,accessorFn:u,parent:n,depth:l,columnDef:s,columns:[],getFlatColumns:i(()=>[!0],()=>{var e;return[c,...null==(e=c.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},a(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:i(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=c.columns)&&t.length?e(c.columns.flatMap(e=>e.getLeafColumns())):[c]},a(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(c,e);return c}(o,e,n,l);return r.columns=e.columns?t(e.columns,r,n+1):[],r})};return t(e)},a(e,"debugColumns","getAllColumns")),getAllFlatColumns:i(()=>[o.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),a(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:i(()=>[o.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),a(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:i(()=>[o.getAllColumns(),o._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),a(e,"debugColumns","getAllLeafColumns")),getColumn:e=>o._getAllFlatColumnsById()[e]};Object.assign(o,f);for(let e=0;e<o._features.length;e++){let t=o._features[e];null==t||null==t.createTable||t.createTable(o)}return o}function B(){return e=>i(()=>[e.options.data],t=>{let l={rows:[],flatRows:[],rowsById:{}},n=function(t,r,o){void 0===r&&(r=0);let i=[];for(let u=0;u<t.length;u++){let s=g(e,e._getRowId(t[u],u,o),t[u],u,r,void 0,null==o?void 0:o.id);if(l.flatRows.push(s),l.rowsById[s.id]=s,i.push(s),e.options.getSubRows){var a;s.originalSubRows=e.options.getSubRows(t[u],u),null!=(a=s.originalSubRows)&&a.length&&(s.subRows=n(s.originalSubRows,r+1,s))}}return i};return l.rows=n(t),l},a(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function U(){return(e,t)=>i(()=>{var l;return[null==(l=e.getColumn(t))?void 0:l.getFacetedRowModel()]},e=>{if(!e)return;let l=e.flatRows.flatMap(e=>{var l;return null!=(l=e.getUniqueValues(t))?l:[]}).map(Number).filter(e=>!Number.isNaN(e));if(!l.length)return;let n=l[0],r=l[l.length-1];for(let e of l)e<n?n=e:e>r&&(r=e);return[n,r]},a(e.options,"debugTable","getFacetedMinMaxValues"))}function $(e,t,l){return l.options.filterFromLeafRows?function(e,t,l){var n;let r=[],o={},i=null!=(n=l.options.maxLeafRowFilterDepth)?n:100,a=function(e,n){void 0===n&&(n=0);let u=[];for(let d=0;d<e.length;d++){var s;let c=e[d],f=g(l,c.id,c.original,c.index,c.depth,void 0,c.parentId);if(f.columnFilters=c.columnFilters,null!=(s=c.subRows)&&s.length&&n<i){if(f.subRows=a(c.subRows,n+1),t(c=f)&&!f.subRows.length||t(c)||f.subRows.length){u.push(c),o[c.id]=c,r.push(c);continue}}else t(c=f)&&(u.push(c),o[c.id]=c,r.push(c))}return u};return{rows:a(e),flatRows:r,rowsById:o}}(e,t,l):function(e,t,l){var n;let r=[],o={},i=null!=(n=l.options.maxLeafRowFilterDepth)?n:100,a=function(e,n){void 0===n&&(n=0);let u=[];for(let d=0;d<e.length;d++){let c=e[d];if(t(c)){var s;if(null!=(s=c.subRows)&&s.length&&n<i){let e=g(l,c.id,c.original,c.index,c.depth,void 0,c.parentId);e.subRows=a(c.subRows,n+1),c=e}u.push(c),r.push(c),o[c.id]=c}}return u};return{rows:a(e),flatRows:r,rowsById:o}}(e,t,l)}function K(){return(e,t)=>i(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter,e.getFilteredRowModel()],(l,n,r)=>{if(!l.rows.length||!(null!=n&&n.length)&&!r)return l;let o=[...n.map(e=>e.id).filter(e=>e!==t),r?"__global__":void 0].filter(Boolean);return $(l.rows,e=>{for(let t=0;t<o.length;t++)if(!1===e.columnFilters[o[t]])return!1;return!0},e)},a(e.options,"debugTable","getFacetedRowModel"))}function Y(){return(e,t)=>i(()=>{var l;return[null==(l=e.getColumn(t))?void 0:l.getFacetedRowModel()]},e=>{if(!e)return new Map;let l=new Map;for(let r=0;r<e.flatRows.length;r++){let o=e.flatRows[r].getUniqueValues(t);for(let e=0;e<o.length;e++){let t=o[e];if(l.has(t)){var n;l.set(t,(null!=(n=l.get(t))?n:0)+1)}else l.set(t,1)}}return l},a(e.options,"debugTable",`getFacetedUniqueValues_${t}`))}function X(){return e=>i(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,l,n)=>{let r,o;if(!t.rows.length||!(null!=l&&l.length)&&!n){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let i=[],a=[];(null!=l?l:[]).forEach(t=>{var l;let n=e.getColumn(t.id);if(!n)return;let r=n.getFilterFn();r&&i.push({id:t.id,filterFn:r,resolvedValue:null!=(l=null==r.resolveFilterValue?void 0:r.resolveFilterValue(t.value))?l:t.value})});let u=(null!=l?l:[]).map(e=>e.id),s=e.getGlobalFilterFn(),d=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());n&&s&&d.length&&(u.push("__global__"),d.forEach(e=>{var t;a.push({id:e.id,filterFn:s,resolvedValue:null!=(t=null==s.resolveFilterValue?void 0:s.resolveFilterValue(n))?t:n})}));for(let e=0;e<t.flatRows.length;e++){let l=t.flatRows[e];if(l.columnFilters={},i.length)for(let e=0;e<i.length;e++){let t=(r=i[e]).id;l.columnFilters[t]=r.filterFn(l,t,r.resolvedValue,e=>{l.columnFiltersMeta[t]=e})}if(a.length){for(let e=0;e<a.length;e++){let t=(o=a[e]).id;if(o.filterFn(l,t,o.resolvedValue,e=>{l.columnFiltersMeta[t]=e})){l.columnFilters.__global__=!0;break}}!0!==l.columnFilters.__global__&&(l.columnFilters.__global__=!1)}}return $(t.rows,e=>{for(let t=0;t<u.length;t++)if(!1===e.columnFilters[u[t]])return!1;return!0},e)},a(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function Z(e){return e=>i(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,l)=>{let n;if(!l.rows.length)return l;let{pageSize:r,pageIndex:o}=t,{rows:i,flatRows:a,rowsById:u}=l,s=r*o;i=i.slice(s,s+r),(n=e.options.paginateExpandedRows?{rows:i,flatRows:a,rowsById:u}:function(e){let t=[],l=e=>{var n;t.push(e),null!=(n=e.subRows)&&n.length&&e.getIsExpanded()&&e.subRows.forEach(l)};return e.rows.forEach(l),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:i,flatRows:a,rowsById:u})).flatRows=[];let d=e=>{n.flatRows.push(e),e.subRows.length&&e.subRows.forEach(d)};return n.rows.forEach(d),n},a(e.options,"debugTable","getPaginationRowModel"))}function J(){return e=>i(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,l)=>{if(!l.rows.length||!(null!=t&&t.length))return l;let n=e.getState().sorting,r=[],o=n.filter(t=>{var l;return null==(l=e.getColumn(t.id))?void 0:l.getCanSort()}),i={};o.forEach(t=>{let l=e.getColumn(t.id);l&&(i[t.id]={sortUndefined:l.columnDef.sortUndefined,invertSorting:l.columnDef.invertSorting,sortingFn:l.getSortingFn()})});let a=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let n=0;n<o.length;n+=1){var l;let r=o[n],a=i[r.id],u=a.sortUndefined,s=null!=(l=null==r?void 0:r.desc)&&l,d=0;if(u){let l=e.getValue(r.id),n=t.getValue(r.id),o=void 0===l,i=void 0===n;if(o||i){if("first"===u)return o?-1:1;if("last"===u)return o?1:-1;d=o&&i?0:o?u:-u}}if(0===d&&(d=a.sortingFn(e,t,r.id)),0!==d)return s&&(d*=-1),a.invertSorting&&(d*=-1),d}return e.index-t.index}),t.forEach(e=>{var t;r.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=a(e.subRows))}),t};return{rows:a(l.rows),flatRows:r,rowsById:l.rowsById}},a(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}}}]);