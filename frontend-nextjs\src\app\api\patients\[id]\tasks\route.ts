import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const url = new URL(request.url);
    
    // Extract query parameters
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const page = parseInt(url.searchParams.get('page') || '1');
    const taskType = url.searchParams.get('taskType');
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    const assignedTo = url.searchParams.get('assignedTo');
    
    // Build where clause for this specific patient
    let whereClause: any = {
      patient: { equals: params.id },
    };
    
    if (taskType) {
      whereClause.taskType = { equals: taskType };
    }
    
    if (status) {
      whereClause.status = { equals: status };
    }
    
    if (priority) {
      whereClause.priority = { equals: priority };
    }
    
    if (assignedTo) {
      whereClause.assignedTo = { equals: assignedTo };
    }
    
    // Apply role-based filtering
    if (user.role === 'doctor') {
      whereClause.and = [
        whereClause,
        {
          or: [
            {
              assignedTo: {
                equals: user.payloadUserId,
              },
            },
            {
              createdBy: {
                equals: user.payloadUserId,
              },
            },
            {
              taskType: {
                in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],
              },
            },
          ],
        },
      ];
    } else if (user.role === 'front-desk') {
      whereClause.and = [
        whereClause,
        {
          or: [
            {
              assignedTo: {
                equals: user.payloadUserId,
              },
            },
            {
              taskType: {
                in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],
              },
            },
          ],
        },
      ];
    }
    
    const data = await payloadClient.getPatientTasks({
      limit,
      page,
      where: whereClause,
      sort: '-dueDate', // Sort by due date descending
    });
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient tasks:', error);
    return createErrorResponse('Failed to fetch patient tasks');
  }
});
