try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="29e17ecf-a976-4cd2-815d-dbf14d2124b0",e._sentryDebugIdIdentifier="sentry-dbid-29e17ecf-a976-4cd2-815d-dbf14d2124b0")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7131],{59694:(e,t,n)=>{n.d(t,{N:()=>f});var r=n(99004),i=n(38774),o=n(39552),l=n(50516),a=n(52880);function f(e){let t=e+"CollectionProvider",[n,f]=(0,i.A)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:n}=e,i=r.useRef(null),o=r.useRef(new Map).current;return(0,a.jsx)(c,{scope:t,itemMap:o,collectionRef:i,children:n})};u.displayName=t;let d=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=s(d,n),f=(0,o.s)(t,i.collectionRef);return(0,a.jsx)(l.DX,{ref:f,children:r})});p.displayName=d;let h=e+"CollectionItemSlot",m="data-radix-collection-item",g=r.forwardRef((e,t)=>{let{scope:n,children:i,...f}=e,c=r.useRef(null),u=(0,o.s)(t,c),d=s(h,n);return r.useEffect(()=>(d.itemMap.set(c,{ref:c,...f}),()=>void d.itemMap.delete(c))),(0,a.jsx)(l.DX,{...{[m]:""},ref:u,children:i})});return g.displayName=h,[{Provider:u,Slot:p,ItemSlot:g},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},f]}},66042:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(99004),i=n(88072);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},90502:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(23278).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},97677:(e,t,n)=>{n.d(t,{Mz:()=>eJ,i3:()=>eQ,UC:()=>eK,bL:()=>eU,Bk:()=>eM});var r=n(99004);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},u={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>u[e])}function x(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function v(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function R(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),f=g(a),c=p(t),s="y"===l,u=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,w=i[f]/2-o[f]/2;switch(c){case"top":r={x:u,y:i.y-o.height};break;case"bottom":r={x:u,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=w*(n&&s?-1:1);break;case"end":r[a]+=w*(n&&s?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:s,y:u}=R(c,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:w,reset:x}=await m({x:s,y:u,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=g?g:s,u=null!=y?y:u,p={...p,[o]:{...p[o],...w}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(c=!0===x.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):x.rects),{x:s,y:u}=R(c,d,f)),n=-1)}return{x:s,y:u,placement:d,strategy:i,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:u="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=v(h),g=a[p?"floating"===u?"reference":"floating":u],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:f})),w="floating"===u?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,x=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),R=await (null==o.isElement?void 0:o.isElement(x))&&await (null==o.getScale?void 0:o.getScale(x))||{x:1,y:1},A=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:x,strategy:f}):w);return{top:(y.top-A.top+m.top)/R.y,bottom:(A.bottom-y.bottom+m.bottom)/R.y,left:(y.left-A.left+m.left)/R.x,right:(A.right-y.right+m.right)/R.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return i.some(t=>e[t]>=0)}async function E(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),c=["left","top"].includes(l)?-1:1,s=o&&f?-1:1,u=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof u?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),f?{x:g*s,y:m*c}:{x:m*c,y:g*s}}function L(){return"undefined"!=typeof window}function O(e){return D(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(D(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function D(e){return!!L()&&(e instanceof Node||e instanceof P(e).Node)}function M(e){return!!L()&&(e instanceof Element||e instanceof P(e).Element)}function H(e){return!!L()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function N(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=I(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(e){let t=z(),n=M(e)?I(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(O(e))}function I(e){return P(e).getComputedStyle(e)}function _(e){return M(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||k(e);return N(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=V(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&j(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=P(i);if(o){let e=Y(l);return t.concat(l,l.visualViewport||[],j(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=I(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=H(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function G(e){return M(e)?e:e.contextElement}function $(e){let t=G(e);if(!H(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=q(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let U=c(0);function J(e){let t=P(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function K(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=G(e),a=c(1);t&&(r?M(r)&&(a=$(r)):a=$(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===P(l))&&i)?J(l):c(0),s=(o.left+f.x)/a.x,u=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=P(l),t=r&&M(r)?P(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=$(i),t=i.getBoundingClientRect(),r=I(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,u*=e.y,d*=e.x,p*=e.y,s+=o,u+=l,i=Y(n=P(i))}}return b({width:d,height:p,x:s,y:u})}function Q(e,t){let n=_(e).scrollLeft;return t?t.left+n:K(k(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=k(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=_(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),f=-n.scrollTop;return"rtl"===I(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(k(e));else if(M(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=H(e)?$(e):c(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=J(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===I(e).position}function en(e,t){if(!H(e)||"fixed"===I(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(F(e))return n;if(!H(e)){let t=V(e);for(;t&&!B(t);){if(M(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!W(r)?n:r||function(e){let t=V(e);for(;H(t)&&!B(t);){if(W(t))return t;if(F(t))break;t=V(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),i=k(t),o="fixed"===n,l=K(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=c(0);if(r||!r&&!o)if(("body"!==O(t)||j(i))&&(a=_(t)),r){let e=K(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=Q(i));let s=!i||r||o?c(0):Z(i,a);return{x:l.left+a.scrollLeft-f.x-s.x,y:l.top+a.scrollTop-f.y-s.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=k(r),a=!!t&&F(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},s=c(1),u=c(0),d=H(r);if((d||!d&&!o)&&(("body"!==O(r)||j(l))&&(f=_(r)),H(r))){let e=K(r);s=$(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}let p=!l||d||o?c(0):Z(l,f,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-f.scrollLeft*s.x+u.x+p.x,y:n.y*s.y-f.scrollTop*s.y+u.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>M(e)&&"body"!==O(e)),i=null,o="fixed"===I(e).position,l=o?V(e):e;for(;M(l)&&!B(l);){let t=I(l),n=W(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||j(l)&&!n&&function e(t,n){let r=V(t);return!(r===n||!M(r)||B(r))&&("fixed"===I(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=V(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],c=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,f,i));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:$,isElement:M,isRTL:function(e){return"rtl"===I(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:c,middlewareData:s}=t,{element:u,padding:p=0}=d(e,t)||{};if(null==u)return{};let w=v(p),x={x:n,y:r},b=m(y(i)),R=g(b),A=await f.getDimensions(u),C="y"===b,S=C?"clientHeight":"clientWidth",T=a.reference[R]+a.reference[b]-x[b]-a.floating[R],E=x[b]-a.reference[b],L=await (null==f.getOffsetParent?void 0:f.getOffsetParent(u)),O=L?L[S]:0;O&&await (null==f.isElement?void 0:f.isElement(L))||(O=c.floating[S]||a.floating[R]);let P=O/2-A[R]/2-1,k=o(w[C?"top":"left"],P),D=o(w[C?"bottom":"right"],P),M=O-A[R]-D,H=O/2-A[R]/2+(T/2-E/2),N=l(k,o(H,M)),j=!s.arrow&&null!=h(i)&&H!==N&&a.reference[R]/2-(H<k?k:D)-A[R]/2<0,F=j?H<k?H-k:H-M:0;return{[b]:x[b]+F,data:{[b]:N,centerOffset:H-N-F,...j&&{alignmentOffset:F}},reset:j}}}),ef=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return A(e,t,{...i,platform:o})};var ec=n(32909),es="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function eu(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eu(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eu(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return es(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await E(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(e,t),u={x:n,y:r},h=await C(t,s),g=y(p(i)),w=m(g),x=u[w],v=u[g];if(a){let e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=v+h[e],r=v-h[t];v=l(n,o(v,r))}let b=c.fn({...t,[w]:x,[g]:v});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:f}}}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:c=!0}=d(e,t),s={x:n,y:r},u=y(i),h=m(u),g=s[h],w=s[u],x=d(a,t),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+v.mainAxis,n=o.reference[h]+o.reference[e]-v.mainAxis;g<t?g=t:g>n&&(g=n)}if(c){var b,R;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[u]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[u])||0)+(t?0:v.crossAxis),r=o.reference[u]+o.reference[e]+(t?0:(null==(R=l.offset)?void 0:R[u])||0)-(t?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[u]:w}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:c,initialPlacement:s,platform:u,elements:v}=t,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:E=!0,...L}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let O=p(a),P=y(s),k=p(s)===s,D=await (null==u.isRTL?void 0:u.isRTL(v.floating)),M=A||(k||!E?[x(s)]:function(e){let t=x(e);return[w(e),t,w(t)]}(s)),H="none"!==T;!A&&H&&M.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(s,E,T,D));let N=[s,...M],j=await C(t,L),F=[],W=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&F.push(j[O]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=x(l)),[l,x(l)]}(a,c,D);F.push(j[e[0]],j[e[1]])}if(W=[...W,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=N[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(o=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=W.filter(e=>{if(H){let t=y(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:f,rects:c,platform:s,elements:u}=t,{apply:m=()=>{},...g}=d(e,t),w=await C(t,g),x=p(f),v=h(f),b="y"===y(f),{width:R,height:A}=c.floating;"top"===x||"bottom"===x?(i=x,a=v===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(a=x,i="end"===v?"top":"bottom");let S=A-w.top-w.bottom,T=R-w.left-w.right,E=o(A-w[i],S),L=o(R-w[a],T),O=!t.middlewareData.shift,P=E,k=L;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=S),O&&!v){let e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?k=R-2*(0!==e||0!==t?e+t:l(w.left,w.right)):P=A-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:k,availableHeight:P});let D=await s.getDimensions(u.floating);return R!==D.width||A!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=S(await C(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=S(await C(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}),eR=(e,t)=>({...em(e),options:[e,t]});var eA=n(51452),eC=n(52880),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eC.jsx)(eA.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eT=n(39552),eE=n(38774),eL=n(36962),eO=n(88072),eP=n(66042),ek="Popper",[eD,eM]=(0,eE.A)(ek),[eH,eN]=eD(ek),ej=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eC.jsx)(eH,{scope:t,anchor:i,onAnchorChange:o,children:n})};ej.displayName=ek;var eF="PopperAnchor",eW=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eN(eF,n),a=r.useRef(null),f=(0,eT.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eC.jsx)(eA.sG.div,{...o,ref:f})});eW.displayName=eF;var ez="PopperContent",[eB,eI]=eD(ez),e_=r.forwardRef((e,t)=>{var n,i,a,c,s,u,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:w=0,arrowPadding:x=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:R=0,sticky:A="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:T,...E}=e,L=eN(ez,h),[O,P]=r.useState(null),D=(0,eT.s)(t,e=>P(e)),[M,H]=r.useState(null),N=(0,eP.X)(M),j=null!=(d=null==N?void 0:N.width)?d:0,F=null!=(p=null==N?void 0:N.height)?p:0,W="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},z=Array.isArray(b)?b:[b],B=z.length>0,I={padding:W,boundary:z.filter(eq),altBoundary:B},{refs:_,floatingStyles:V,placement:Y,isPositioned:q,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:c,open:s}=e,[u,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);eu(p,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),v=r.useCallback(e=>{e!==C.current&&(C.current=e,w(e))},[]),b=l||m,R=a||y,A=r.useRef(null),C=r.useRef(null),S=r.useRef(u),T=null!=c,E=eh(c),L=eh(o),O=eh(s),P=r.useCallback(()=>{if(!A.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};L.current&&(e.platform=L.current),ef(A.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};k.current&&!eu(S.current,t)&&(S.current=t,ec.flushSync(()=>{d(t)}))})},[p,t,n,L,O]);es(()=>{!1===s&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let k=r.useRef(!1);es(()=>(k.current=!0,()=>{k.current=!1}),[]),es(()=>{if(b&&(A.current=b),R&&(C.current=R),b&&R){if(E.current)return E.current(b,R,P);P()}},[b,R,P,E,T]);let D=r.useMemo(()=>({reference:A,floating:C,setReference:x,setFloating:v}),[x,v]),M=r.useMemo(()=>({reference:b,floating:R}),[b,R]),H=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!M.floating)return e;let t=ep(M.floating,u.x),r=ep(M.floating,u.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,M.floating,u.x,u.y]);return r.useMemo(()=>({...u,update:P,refs:D,elements:M,floatingStyles:H}),[u,P,D,M,H])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=G(e),h=a||c?[...p?X(p):[],...X(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&u?function(e,t){let n,r=null,i=k(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,u){void 0===s&&(s=!1),void 0===u&&(u=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(s||t(),!m||!g)return;let y=f(h),w=f(i.clientWidth-(p+m)),x={rootMargin:-y+"px "+-w+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,u))||1},v=!0;function b(t){let r=t[0].intersectionRatio;if(r!==u){if(!v)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||c(),v=!1}try{r=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,x)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;s&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let w=d?K(e):null;return d&&function t(){let r=K(e);w&&!el(w,r)&&n(),w=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===S})},elements:{reference:L.anchor},middleware:[eg({mainAxis:g+F,alignmentAxis:w}),v&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===A?ew():void 0,...I}),v&&ex({...I}),ev({...I,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),M&&eR({element:M,padding:x}),eG({arrowWidth:j,arrowHeight:F}),C&&eb({strategy:"referenceHidden",...I})]}),[U,J]=e$(Y),Q=(0,eL.c)(T);(0,eO.N)(()=>{q&&(null==Q||Q())},[q,Q]);let Z=null==(n=$.arrow)?void 0:n.x,ee=null==(i=$.arrow)?void 0:i.y,et=(null==(a=$.arrow)?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eO.N)(()=>{O&&er(window.getComputedStyle(O).zIndex)},[O]),(0,eC.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:q?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null==(c=$.transformOrigin)?void 0:c.x,null==(s=$.transformOrigin)?void 0:s.y].join(" "),...(null==(u=$.hide)?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eB,{scope:h,placedSide:U,onArrowChange:H,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eA.sG.div,{"data-side":U,"data-align":J,...E,ref:D,style:{...E.style,animation:q?void 0:"none"}})})})});e_.displayName=ez;var eV="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eI(eV,n),o=eX[i.placedSide];return(0,eC.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eY.displayName=eV;var eG=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,u=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,h]=e$(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(o=null==(r=c.arrow)?void 0:r.x)?o:0)+u/2,y=(null!=(l=null==(i=c.arrow)?void 0:i.y)?l:0)+d/2,w="",x="";return"bottom"===p?(w=s?m:"".concat(g,"px"),x="".concat(-d,"px")):"top"===p?(w=s?m:"".concat(g,"px"),x="".concat(f.floating.height+d,"px")):"right"===p?(w="".concat(-d,"px"),x=s?m:"".concat(y,"px")):"left"===p&&(w="".concat(f.floating.width+d,"px"),x=s?m:"".concat(y,"px")),{data:{x:w,y:x}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=ej,eJ=eW,eK=e_,eQ=eY}}]);