import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

export const GET = withAuthentication(async (user: Authenticated<PERSON>ser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const url = new URL(request.url);
    
    // Extract query parameters
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const page = parseInt(url.searchParams.get('page') || '1');
    const interactionType = url.searchParams.get('interactionType');
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    
    // Build where clause for this specific patient
    let whereClause: any = {
      patient: { equals: params.id },
    };
    
    if (interactionType) {
      whereClause.interactionType = { equals: interactionType };
    }
    
    if (status) {
      whereClause.status = { equals: status };
    }
    
    if (priority) {
      whereClause.priority = { equals: priority };
    }
    
    // Apply role-based filtering
    if (user.role === 'doctor') {
      whereClause.and = [
        whereClause,
        {
          or: [
            {
              staffMember: {
                equals: user.payloadUserId,
              },
            },
            {
              interactionType: {
                in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],
              },
            },
          ],
        },
      ];
    } else if (user.role === 'front-desk') {
      whereClause.and = [
        whereClause,
        {
          interactionType: {
            in: ['phone-call', 'email', 'billing-inquiry'],
          },
        },
      ];
    }
    
    const data = await payloadClient.getPatientInteractions({
      limit,
      page,
      where: whereClause,
      sort: '-timestamp', // Sort by timestamp descending (newest first)
    });
    
    return createSuccessResponse(data);
  } catch (error) {
    console.error('Error fetching patient interactions:', error);
    return createErrorResponse('Failed to fetch patient interactions');
  }
});
