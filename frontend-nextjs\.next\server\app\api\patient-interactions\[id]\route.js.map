{"version": 3, "file": "../app/api/patient-interactions/[id]/route.js", "mappings": "ubAAA,wZCKO,IAAMA,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAChI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCK,CADoCL,CAAAA,MACvBG,EAAcG,WAAdH,UAAmC,CAACD,EAAOK,EAAE,EAGhE,GAAkB,QAAU,GAAxBP,EAAKQ,EAALR,EAAS,EAGX,GADsB,aAClBS,KADyBJ,EAAKK,EAAAA,SAAW,CAAgBL,EAAKK,EAAAA,SAAW,CAACH,EAAE,CAAGF,EAAKK,EAALL,SAAKK,IAClEV,EAAKW,EAAAA,WAAa,EAAI,CAACC,CAFvB,oBAAqB,uBAAwB,kBAAkB,CAE3BC,QAAQ,CAACR,EAAKS,EAAAA,aAAe,CAAG,CACxF,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,mBAAqB,KAClD,MACK,GAAkB,YAAc,GAA5Bf,EAAKQ,EAALR,EAAS,EAEd,CAACY,CADiB,WACJC,EADkB,QAAS,kBAAkB,CAC7CA,QAAQ,CAACR,EAAKS,EAALT,aAAoB,CAAG,CAChD,MAAOU,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,mBAAqB,MAIpD,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBX,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBY,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,qCAAuCA,CAAAA,GAC9CF,CAAAA,CAD8CE,CAAAA,EAC9CF,EAAAA,CAAoB,uCAC7B,CACF,CAAG,EAEUI,EAAQpB,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAClI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCoB,CADoCpB,CAAAA,MACjBC,EAAnBmB,IAA+B,CAAZnB,EAGnBoB,EAAsB,MAAMlB,EAAcG,WAAdH,UAAmC,CAACD,EAAOK,EAAE,EAGzEE,EAAgB,iBAAOY,EAAoBX,WAAW,CAAgBW,EAAoBX,GAA/CA,QAA0D,CAACH,EAAE,CAAGc,EAAjBX,WAAgD,CAChJ,GAAIV,EAD6GqB,UACxGb,IAAI,EAAgBC,IAAkBT,EAAKW,EAALX,KAAAA,MAAkB,CAC/D,CADiE,KAC1De,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,2CAA6C,KAI1E,QAAOK,EAAWV,WAAW,CAE7B,IAAML,EAAO,MAAMF,EAAcmB,WAAdnB,aAAsC,CAACD,EAAOK,EAAE,CAAEa,CAAXlB,EAE1D,MAAOc,CAF8DI,CAAAA,CAE9DJ,EAAAA,EAAAA,CAAsBX,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBY,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,qCAAuCA,CAAAA,GAC9CF,CAAAA,CAD8CE,CAC9CF,EAAAA,EAAAA,CAAoB,wCAC7B,CACF,CAAG,EAEUQ,EAASxB,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IACnI,GAAI,CAEF,GAAkB,OAAS,GAAvBF,EAAKQ,EAALR,EAAS,CACX,MAAOe,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,6CAA+C,MAG5E,IAAMZ,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GAG1C,CAH0CA,CAAAA,KAC1C,MAAMG,EAAcqB,WAAAA,aAAwB,CAACtB,EAAOK,EAAE,EAATL,CAEtCc,EAAAA,EAAAA,EAAAA,CAAsB,EAAES,OAAS,2CAA2C,EACrF,CAAE,MAAOR,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,qCAAuCA,CAAAA,GAC9CF,CAAAA,CAD8CE,CAAAA,EAC9CF,EAAAA,CAAoB,wCAC7B,CACF,CAAG,EC/DG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGa,wBAAwB,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CACrC,IAD0C,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,gCAAgC,SACpD,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAExB,CAIC,IAAC,EAAM,CAAH,CAAeW,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,CAAeC,OAA4B,EAAH,GAAQ,EAEnD,EAAQ,EAAYC,CAAf,CAA6C,KAAH,EAA5B,EAEnB,EAAS,EAAYC,EAAf,MAA2C,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,4CACA,0CACA,iBACA,oDACA,CAAK,CACL,+HACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,CAAQ,yDAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,YC5BA,uCCAA,wFCAA,iDCAA,sDCAA,6FCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patient-interactions/[id]/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\nimport { PatientInteraction } from '@/types/clinic';\n\nexport const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const data = await payloadClient.getPatientInteraction(params.id) as PatientInteraction;\n\n    // Check if user has permission to view this interaction\n    if (user.role === 'doctor') {\n      const allowedTypes = ['consultation-note', 'treatment-discussion', 'in-person-visit'];\n      const staffMemberId = typeof data.staffMember === 'object' ? data.staffMember.id : data.staffMember;\n      if (staffMemberId !== user.payloadUserId && !allowedTypes.includes(data.interactionType)) {\n        return createErrorResponse('Permission denied', 403);\n      }\n    } else if (user.role === 'front-desk') {\n      const allowedTypes = ['phone-call', 'email', 'billing-inquiry'];\n      if (!allowedTypes.includes(data.interactionType)) {\n        return createErrorResponse('Permission denied', 403);\n      }\n    }\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error fetching patient interaction:', error);\n    return createErrorResponse('Failed to fetch patient interaction');\n  }\n});\n\nexport const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const updateData = await request.json();\n    \n    // Get the existing interaction to check permissions\n    const existingInteraction = await payloadClient.getPatientInteraction(params.id) as PatientInteraction;\n\n    // Check if user can update this interaction\n    const staffMemberId = typeof existingInteraction.staffMember === 'object' ? existingInteraction.staffMember.id : existingInteraction.staffMember;\n    if (user.role !== 'admin' && staffMemberId !== user.payloadUserId) {\n      return createErrorResponse('You can only update your own interactions', 403);\n    }\n    \n    // Prevent changing the staff member\n    delete updateData.staffMember;\n    \n    const data = await payloadClient.updatePatientInteraction(params.id, updateData);\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error updating patient interaction:', error);\n    return createErrorResponse('Failed to update patient interaction');\n  }\n});\n\nexport const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    // Only admin can delete interactions\n    if (user.role !== 'admin') {\n      return createErrorResponse('Only administrators can delete interactions', 403);\n    }\n    \n    const payloadClient = createPayloadClient(user);\n    await payloadClient.deletePatientInteraction(params.id);\n    \n    return createSuccessResponse({ message: 'Patient interaction deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting patient interaction:', error);\n    return createErrorResponse('Failed to delete patient interaction');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patient-interactions/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-interactions\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patient-interactions/[id]/route\",\n        pathname: \"/api/patient-interactions/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/patient-interactions/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-interactions\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "params", "payloadClient", "createPayloadClient", "data", "getPatientInteraction", "id", "role", "staffMemberId", "staffMember", "payloadUserId", "allowedTypes", "includes", "interactionType", "createErrorResponse", "createSuccessResponse", "error", "console", "PATCH", "updateData", "existingInteraction", "updatePatientInteraction", "DELETE", "deletePatientInteraction", "message", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}