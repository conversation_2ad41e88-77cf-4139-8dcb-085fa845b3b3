import { NextRequest } from 'next/server';
import { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';
import { createPayloadClient } from '@/lib/payload-client';

interface TimelineItem {
  id: string;
  type: 'interaction' | 'task';
  timestamp: string;
  title: string;
  status: string;
  priority: string;
  staffMember?: any;
  assignedTo?: any;
  createdBy?: any;
  data: any;
}

export const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {
  try {
    const payloadClient = createPayloadClient(user);
    const url = new URL(request.url);
    
    // Extract query parameters
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const page = parseInt(url.searchParams.get('page') || '1');
    const type = url.searchParams.get('type'); // 'interaction', 'task', or null for both
    
    const timeline: TimelineItem[] = [];
    
    // Fetch interactions if requested
    if (!type || type === 'interaction') {
      let interactionWhere: any = {
        patient: { equals: params.id },
      };
      
      // Apply role-based filtering for interactions
      if (user.role === 'doctor') {
        interactionWhere.and = [
          interactionWhere,
          {
            or: [
              {
                staffMember: {
                  equals: user.payloadUserId,
                },
              },
              {
                interactionType: {
                  in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],
                },
              },
            ],
          },
        ];
      } else if (user.role === 'front-desk') {
        interactionWhere.and = [
          interactionWhere,
          {
            interactionType: {
              in: ['phone-call', 'email', 'billing-inquiry'],
            },
          },
        ];
      }
      
      const interactions = await payloadClient.getPatientInteractions({
        limit: 100, // Get more items to merge properly
        page: 1,
        where: interactionWhere,
        sort: '-timestamp',
      }) as any;

      interactions.docs.forEach((interaction: any) => {
        timeline.push({
          id: interaction.id,
          type: 'interaction',
          timestamp: interaction.timestamp,
          title: interaction.title,
          status: interaction.status,
          priority: interaction.priority,
          staffMember: interaction.staffMember,
          data: interaction,
        });
      });
    }
    
    // Fetch tasks if requested
    if (!type || type === 'task') {
      let taskWhere: any = {
        patient: { equals: params.id },
      };
      
      // Apply role-based filtering for tasks
      if (user.role === 'doctor') {
        taskWhere.and = [
          taskWhere,
          {
            or: [
              {
                assignedTo: {
                  equals: user.payloadUserId,
                },
              },
              {
                createdBy: {
                  equals: user.payloadUserId,
                },
              },
              {
                taskType: {
                  in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],
                },
              },
            ],
          },
        ];
      } else if (user.role === 'front-desk') {
        taskWhere.and = [
          taskWhere,
          {
            or: [
              {
                assignedTo: {
                  equals: user.payloadUserId,
                },
              },
              {
                taskType: {
                  in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],
                },
              },
            ],
          },
        ];
      }
      
      const tasks = await payloadClient.getPatientTasks({
        limit: 100, // Get more items to merge properly
        page: 1,
        where: taskWhere,
        sort: '-dueDate',
      }) as any;

      tasks.docs.forEach((task: any) => {
        timeline.push({
          id: task.id,
          type: 'task',
          timestamp: task.dueDate,
          title: task.title,
          status: task.status,
          priority: task.priority,
          assignedTo: task.assignedTo,
          createdBy: task.createdBy,
          data: task,
        });
      });
    }
    
    // Sort timeline by timestamp descending (newest first)
    timeline.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    // Apply pagination to the merged timeline
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTimeline = timeline.slice(startIndex, endIndex);
    
    const response = {
      docs: paginatedTimeline,
      totalDocs: timeline.length,
      limit,
      page,
      totalPages: Math.ceil(timeline.length / limit),
      hasNextPage: endIndex < timeline.length,
      hasPrevPage: page > 1,
      nextPage: endIndex < timeline.length ? page + 1 : null,
      prevPage: page > 1 ? page - 1 : null,
    };
    
    return createSuccessResponse(response);
  } catch (error) {
    console.error('Error fetching patient timeline:', error);
    return createErrorResponse('Failed to fetch patient timeline');
  }
});
