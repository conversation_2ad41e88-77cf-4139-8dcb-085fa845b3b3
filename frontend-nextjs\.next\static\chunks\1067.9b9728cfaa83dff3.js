try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="02688dd1-ff7e-4f33-83b5-343fb05aa523",e._sentryDebugIdIdentifier="sentry-dbid-02688dd1-ff7e-4f33-83b5-343fb05aa523")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1067],{61067:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let d=n(51532),f=n(88017),l=d._(n(44109)),s=n(6097);async function a(e){let{Component:t,ctx:n}=e;return{pageProps:await (0,s.loadGetInitialProps)(t,n)}}class o extends l.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,f.jsx)(e,{...t})}}o.origGetInitialProps=a,o.getInitialProps=a,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);