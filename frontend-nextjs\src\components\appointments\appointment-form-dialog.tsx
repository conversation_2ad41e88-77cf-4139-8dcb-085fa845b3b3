'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { format } from 'date-fns'
import { CalendarIcon } from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { cn } from '@/lib/utils'
import { appointmentsApi, patientsApi, treatmentsApi } from '@/lib/api'
import { Appointment, Patient, Treatment, User } from '@/types/clinic'
import { toast } from 'sonner'
import { checkAppointmentConflicts, validateAppointmentTiming, suggestAlternativeSlots } from '@/lib/appointment-utils'
import { appointmentNotifications } from '@/lib/appointment-notifications'
import { t } from '@/lib/translations'

const appointmentSchema = z.object({
  appointmentType: z.enum(['consultation', 'treatment'], {
    required_error: 'Please select appointment type',
  }),
  appointmentDate: z.date({
    required_error: 'Please select an appointment date and time',
  }).refine((date) => {
    const now = new Date();
    const minDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Allow up to 24 hours in the past
    return date >= minDate;
  }, {
    message: 'Appointment date cannot be more than 24 hours in the past',
  }),
  patientId: z.string()
    .min(1, 'Please select a patient from the dropdown'),
  treatmentId: z.string()
    .optional(),
  practitionerId: z.string()
    .min(1, 'Please select a practitioner from the dropdown'),
  price: z.number()
    .min(0, 'Price must be $0 or greater')
    .max(10000, 'Price cannot exceed $10,000')
    .multipleOf(0.01, 'Price must be a valid currency amount')
    .optional(),
  durationInMinutes: z.number()
    .min(5, 'Duration must be at least 5 minutes')
    .max(480, 'Duration cannot exceed 8 hours (480 minutes)')
    .int('Duration must be a whole number of minutes'),
  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'no-show'], {
    required_error: 'Please select an appointment status',
  }),
  consultationType: z.enum(['initial', 'follow-up', 'price-inquiry']).optional(),
  interestedTreatments: z.array(z.string()).optional(),
}).refine((data) => {
  // Treatment appointments must have a treatment selected
  if (data.appointmentType === 'treatment' && !data.treatmentId) {
    return false;
  }
  return true;
}, {
  message: 'Treatment appointments must have a treatment selected',
  path: ['treatmentId'],
})

type AppointmentFormData = z.infer<typeof appointmentSchema>

interface AppointmentFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  appointment?: Appointment
  onSuccess?: () => void
}

export function AppointmentFormDialog({
  open,
  onOpenChange,
  appointment,
  onSuccess,
}: AppointmentFormDialogProps) {
  const [loading, setLoading] = useState(false)
  const [patients, setPatients] = useState<Patient[]>([])
  const [treatments, setTreatments] = useState<Treatment[]>([])
  const [practitioners, setPractitioners] = useState<User[]>([])
  const [existingAppointments, setExistingAppointments] = useState<Appointment[]>([])
  const [conflictWarning, setConflictWarning] = useState<string | null>(null)
  const [suggestedSlots, setSuggestedSlots] = useState<any[]>([])

  const isEditing = !!appointment

  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      appointmentType: appointment?.appointmentType || 'consultation',
      appointmentDate: appointment ? new Date(appointment.appointmentDate) : new Date(),
      patientId: appointment?.patient.id || '',
      treatmentId: appointment?.treatment?.id || '',
      practitionerId: appointment?.practitioner.id || '',
      price: appointment?.price || 0,
      durationInMinutes: appointment?.durationInMinutes || 30,
      status: appointment?.status || 'scheduled',
      consultationType: appointment?.consultationType || 'initial',
      interestedTreatments: appointment?.interestedTreatments?.map(t => t.id) || [],
    },
  })

  // Load form data when dialog opens
  useEffect(() => {
    if (open) {
      loadFormData()
      if (appointment) {
        form.reset({
          appointmentDate: new Date(appointment.appointmentDate),
          patientId: appointment.patient.id,
          treatmentId: appointment.treatment?.id || '',
          practitionerId: appointment.practitioner.id,
          price: appointment.price,
          durationInMinutes: appointment.durationInMinutes,
          status: appointment.status,
        })
      }
    }
  }, [open, appointment, form])

  const loadFormData = async () => {
    try {
      const [patientsResponse, treatmentsResponse, appointmentsResponse] = await Promise.all([
        patientsApi.getAll({ limit: 100 }),
        treatmentsApi.getAll({ limit: 100 }),
        appointmentsApi.getAll({ limit: 1000 }), // Load more appointments for conflict checking
      ])

      setPatients(patientsResponse.docs)
      setTreatments(treatmentsResponse.docs)
      setExistingAppointments(appointmentsResponse.docs)
      
      // For now, we'll use a mock practitioner list
      // In a real app, you'd fetch this from the users API
      setPractitioners([
        {
          id: 'user-1',
          email: '<EMAIL>',
          role: 'doctor',
          clerkId: 'clerk-1',
          firstName: 'Dr. Jane',
          lastName: 'Smith',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ])
    } catch (error) {
      console.error('Failed to load form data:', error)
      toast.error('Failed to load form data')
    }
  }

  // Auto-fill price and duration when treatment changes
  const watchTreatment = form.watch('treatmentId')
  useEffect(() => {
    if (watchTreatment && !isEditing) {
      const selectedTreatment = treatments.find(t => t.id === watchTreatment)
      if (selectedTreatment) {
        form.setValue('price', selectedTreatment.defaultPrice)
        form.setValue('durationInMinutes', selectedTreatment.defaultDurationInMinutes)
      }
    }
  }, [watchTreatment, treatments, form, isEditing])

  // Check for appointment conflicts when relevant fields change
  const appointmentDate = form.watch('appointmentDate')
  const durationInMinutes = form.watch('durationInMinutes')
  const practitionerId = form.watch('practitionerId')

  useEffect(() => {

    if (appointmentDate && durationInMinutes && practitionerId && existingAppointments.length > 0) {
      // Validate appointment timing
      const timingValidation = validateAppointmentTiming(appointmentDate)
      if (!timingValidation.isValid) {
        setConflictWarning(timingValidation.message || 'Invalid appointment time')
        setSuggestedSlots([])
        return
      }

      // Check for conflicts
      const conflictCheck = checkAppointmentConflicts(
        {
          appointmentDate,
          durationInMinutes,
          practitionerId,
          id: appointment?.id, // For editing
        },
        existingAppointments
      )

      if (conflictCheck.hasConflict) {
        setConflictWarning(conflictCheck.message || '检测到时间段冲突')

        // Suggest alternative slots
        const alternatives = suggestAlternativeSlots(
          appointmentDate,
          practitionerId,
          durationInMinutes,
          existingAppointments,
          3
        )
        setSuggestedSlots(alternatives)
      } else {
        setConflictWarning(null)
        setSuggestedSlots([])
      }
    } else {
      setConflictWarning(null)
      setSuggestedSlots([])
    }
  }, [appointmentDate, durationInMinutes, practitionerId, existingAppointments, appointment?.id])

  const onSubmit = async (data: AppointmentFormData) => {
    setLoading(true)
    try {
      const appointmentData = {
        appointmentType: data.appointmentType,
        appointmentDate: data.appointmentDate.toISOString(),
        patient: data.patientId,
        treatment: data.treatmentId || undefined,
        practitioner: data.practitionerId,
        price: data.price || 0,
        durationInMinutes: data.durationInMinutes,
        status: data.status,
        consultationType: data.appointmentType === 'consultation' ? data.consultationType : undefined,
        interestedTreatments: data.appointmentType === 'consultation' ? data.interestedTreatments : undefined,
      }

      let result;
      if (isEditing) {
        result = await appointmentsApi.update(appointment.id, appointmentData)
        appointmentNotifications.appointment.updated(result)
      } else {
        result = await appointmentsApi.create(appointmentData)
        appointmentNotifications.appointment.created(result)
      }

      onSuccess?.()
      onOpenChange(false)
      form.reset()
    } catch (error) {
      console.error('Failed to save appointment:', error)
      appointmentNotifications.error.saveFailed(isEditing)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Appointment' : 'New Appointment'}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the appointment details below.'
              : 'Fill in the details to schedule a new appointment.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* Patient Selection */}
              <FormField
                control={form.control}
                name="patientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Patient</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select patient" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {patients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id}>
                            {patient.fullName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Treatment Selection */}
              <FormField
                control={form.control}
                name="treatmentId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Treatment</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select treatment" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {treatments.map((treatment) => (
                          <SelectItem key={treatment.id} value={treatment.id}>
                            {treatment.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Appointment Date */}
            <FormField
              control={form.control}
              name="appointmentDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Appointment Date & Time</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            'w-full pl-3 text-left font-normal',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {field.value ? (
                            format(field.value, 'PPP p')
                          ) : (
                            <span>Pick a date and time</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date() || date < new Date('1900-01-01')
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-3 gap-4">
              {/* Price */}
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price ($)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Duration */}
              <FormField
                control={form.control}
                name="durationInMinutes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (min)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Status */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="scheduled">Scheduled</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Practitioner Selection */}
            <FormField
              control={form.control}
              name="practitionerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Practitioner</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select practitioner" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {practitioners.map((practitioner) => (
                        <SelectItem key={practitioner.id} value={practitioner.id}>
                          {practitioner.firstName} {practitioner.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : isEditing ? 'Update' : 'Create'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
