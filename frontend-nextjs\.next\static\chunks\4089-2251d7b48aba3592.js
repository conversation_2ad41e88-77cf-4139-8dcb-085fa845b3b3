try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="e2aede99-08ff-4316-9d30-0e1843c7107b",e._sentryDebugIdIdentifier="sentry-dbid-e2aede99-08ff-4316-9d30-0e1843c7107b")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{6280:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(99004),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:i()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},10144:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,a=new WeakMap,i={},c=0,u=function(e){return e&&(e.host||u(e.parentNode))},l=function(e,t,n,r){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],f=[],d=new Set,p=new Set(l),v=function(e){!e||d.has(e)||(d.add(e),v(e.parentNode))};l.forEach(v);var h=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))h(e);else try{var t=e.getAttribute(r),i=null!==t&&"false"!==t,c=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,c),s.set(e,u),f.push(e),1===c&&i&&a.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),d.clear(),c++,function(){f.forEach(function(e){var t=o.get(e)-1,i=s.get(e)-1;o.set(e,t),s.set(e,i),t||(a.has(e)||e.removeAttribute(r),a.delete(e)),i||e.removeAttribute(n)}),--c||(o=new WeakMap,o=new WeakMap,a=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),a=t||r(e);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),l(o,a,n,"aria-hidden")):function(){return null}}},17430:(e,t,n)=>{n.d(t,{qW:()=>d});var r,o=n(99004),a=n(84732),i=n(51452),c=n(39552),u=n(36962),l=n(52880),s="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:h=!1,onEscapeKeyDown:y,onPointerDownOutside:m,onFocusOutside:b,onInteractOutside:g,onDismiss:w,...E}=e,_=o.useContext(f),[S,O]=o.useState(null),P=null!=(d=null==S?void 0:S.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,k]=o.useState({}),C=(0,c.s)(t,e=>O(e)),x=Array.from(_.layers),[j]=[..._.layersWithOutsidePointerEventsDisabled].slice(-1),T=x.indexOf(j),L=S?x.indexOf(S):-1,A=_.layersWithOutsidePointerEventsDisabled.size>0,N=L>=T,R=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){v("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[..._.branches].some(e=>e.contains(t));N&&!n&&(null==m||m(e),null==g||g(e),e.defaultPrevented||null==w||w())},P),D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;![..._.branches].some(e=>e.contains(t))&&(null==b||b(e),null==g||g(e),e.defaultPrevented||null==w||w())},P);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{L===_.layers.size-1&&(null==y||y(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},P),o.useEffect(()=>{if(S)return h&&(0===_.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),_.layersWithOutsidePointerEventsDisabled.add(S)),_.layers.add(S),p(),()=>{h&&1===_.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[S,P,h,_]),o.useEffect(()=>()=>{S&&(_.layers.delete(S),_.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,_]),o.useEffect(()=>{let e=()=>k({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(i.sG.div,{...E,ref:C,style:{pointerEvents:A?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,R.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,c=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,c):a.dispatchEvent(c)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),a=(0,c.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(i.sG.div,{...e,ref:a})}).displayName="DismissableLayerBranch"},18608:(e,t,n)=>{n.d(t,{i:()=>a});var r=n(99004),o=n(36962);function a({prop:e,defaultProp:t,onChange:n=()=>{}}){let[a,i]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[a]=n,i=r.useRef(a),c=(0,o.c)(t);return r.useEffect(()=>{i.current!==a&&(c(a),i.current=a)},[a,i,c]),n}({defaultProp:t,onChange:n}),c=void 0!==e,u=c?e:a,l=(0,o.c)(n);return[u,r.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else i(t)},[c,e,i,l])]}},20945:(e,t,n)=>{n.r(t),n.d(t,{__addDisposableResource:()=>D,__assign:()=>a,__asyncDelegator:()=>P,__asyncGenerator:()=>O,__asyncValues:()=>k,__await:()=>S,__awaiter:()=>v,__classPrivateFieldGet:()=>A,__classPrivateFieldIn:()=>R,__classPrivateFieldSet:()=>N,__createBinding:()=>y,__decorate:()=>c,__disposeResources:()=>M,__esDecorate:()=>l,__exportStar:()=>m,__extends:()=>o,__generator:()=>h,__importDefault:()=>L,__importStar:()=>T,__makeTemplateObject:()=>C,__metadata:()=>p,__param:()=>u,__propKey:()=>f,__read:()=>g,__rest:()=>i,__rewriteRelativeImportExtension:()=>W,__runInitializers:()=>s,__setFunctionName:()=>d,__spread:()=>w,__spreadArray:()=>_,__spreadArrays:()=>E,__values:()=>b,default:()=>F});var r=function(e,t){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function c(e,t,n,r){var o,a=arguments.length,i=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,n,r);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(i=(a<3?o(i):a>3?o(t,n,i):o(t,n))||i);return a>3&&i&&Object.defineProperty(t,n,i),i}function u(e,t){return function(n,r){t(n,r,e)}}function l(e,t,n,r,o,a){function i(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var c,u=r.kind,l="getter"===u?"get":"setter"===u?"set":"value",s=!t&&e?r.static?e:e.prototype:null,f=t||(s?Object.getOwnPropertyDescriptor(s,r.name):{}),d=!1,p=n.length-1;p>=0;p--){var v={};for(var h in r)v[h]="access"===h?{}:r[h];for(var h in r.access)v.access[h]=r.access[h];v.addInitializer=function(e){if(d)throw TypeError("Cannot add initializers after decoration has completed");a.push(i(e||null))};var y=(0,n[p])("accessor"===u?{get:f.get,set:f.set}:f[l],v);if("accessor"===u){if(void 0===y)continue;if(null===y||"object"!=typeof y)throw TypeError("Object expected");(c=i(y.get))&&(f.get=c),(c=i(y.set))&&(f.set=c),(c=i(y.init))&&o.unshift(c)}else(c=i(y))&&("field"===u?o.unshift(c):f[l]=c)}s&&Object.defineProperty(s,r.name,f),d=!0}function s(e,t,n){for(var r=arguments.length>2,o=0;o<t.length;o++)n=r?t[o].call(e,n):t[o].call(e);return r?n:void 0}function f(e){return"symbol"==typeof e?e:"".concat(e)}function d(e,t,n){return"symbol"==typeof t&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function v(e,t,n,r){return new(n||(n=Promise))(function(o,a){function i(e){try{u(r.next(e))}catch(e){a(e)}}function c(e){try{u(r.throw(e))}catch(e){a(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(i,c)}u((r=r.apply(e,t||[])).next())})}function h(e,t){var n,r,o,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},i=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return i.next=c(0),i.throw=c(1),i.return=c(2),"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function c(c){return function(u){var l=[c,u];if(n)throw TypeError("Generator is already executing.");for(;i&&(i=0,l[0]&&(a=0)),a;)try{if(n=1,r&&(o=2&l[0]?r.return:l[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,l[1])).done)return o;switch(r=0,o&&(l=[2&l[0],o.value]),l[0]){case 0:case 1:o=l;break;case 4:return a.label++,{value:l[1],done:!1};case 5:a.label++,r=l[1],l=[0];continue;case 7:l=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===l[0]||2===l[0])){a=0;continue}if(3===l[0]&&(!o||l[1]>o[0]&&l[1]<o[3])){a.label=l[1];break}if(6===l[0]&&a.label<o[1]){a.label=o[1],o=l;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(l);break}o[2]&&a.ops.pop(),a.trys.pop();continue}l=t.call(e,a)}catch(e){l=[6,e],r=0}finally{n=o=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}}var y=Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]};function m(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||y(t,e,n)}function b(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function g(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,a=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(r=a.next()).done;)i.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i}function w(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(g(arguments[t]));return e}function E(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),o=0,t=0;t<n;t++)for(var a=arguments[t],i=0,c=a.length;i<c;i++,o++)r[o]=a[i];return r}function _(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}function S(e){return this instanceof S?(this.v=e,this):new S(e)}function O(e,t,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(e,t||[]),a=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(e){return function(t){return Promise.resolve(t).then(e,l)}}),r[Symbol.asyncIterator]=function(){return this},r;function i(e,t){o[e]&&(r[e]=function(t){return new Promise(function(n,r){a.push([e,t,n,r])>1||c(e,t)})},t&&(r[e]=t(r[e])))}function c(e,t){try{var n;(n=o[e](t)).value instanceof S?Promise.resolve(n.value.v).then(u,l):s(a[0][2],n)}catch(e){s(a[0][3],e)}}function u(e){c("next",e)}function l(e){c("throw",e)}function s(e,t){e(t),a.shift(),a.length&&c(a[0][0],a[0][1])}}function P(e){var t,n;return t={},r("next"),r("throw",function(e){throw e}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(r,o){t[r]=e[r]?function(t){return(n=!n)?{value:S(e[r](t)),done:!1}:o?o(t):t}:o}}function k(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=b(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise(function(r,o){var a,i,c;a=r,i=o,c=(t=e[n](t)).done,Promise.resolve(t.value).then(function(e){a({value:e,done:c})},i)})}}}function C(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var x=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t},j=function(e){return(j=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t})(e)};function T(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=j(e),r=0;r<n.length;r++)"default"!==n[r]&&y(t,e,n[r]);return x(t,e),t}function L(e){return e&&e.__esModule?e:{default:e}}function A(e,t,n,r){if("a"===n&&!r)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?r:"a"===n?r.call(e):r?r.value:t.get(e)}function N(e,t,n,r,o){if("m"===r)throw TypeError("Private method is not writable");if("a"===r&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?o.call(e,n):o?o.value=n:t.set(e,n),n}function R(e,t){if(null===t||"object"!=typeof t&&"function"!=typeof t)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?t===e:e.has(t)}function D(e,t,n){if(null!=t){var r,o;if("object"!=typeof t&&"function"!=typeof t)throw TypeError("Object expected.");if(n){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(void 0===r){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(o=r)}if("function"!=typeof r)throw TypeError("Object not disposable.");o&&(r=function(){try{o.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var I="function"==typeof SuppressedError?SuppressedError:function(e,t,n){var r=Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};function M(e){function t(t){e.error=e.hasError?new I(t,e.error,"An error was suppressed during disposal."):t,e.hasError=!0}var n,r=0;return function o(){for(;n=e.stack.pop();)try{if(!n.async&&1===r)return r=0,e.stack.push(n),Promise.resolve().then(o);if(n.dispose){var a=n.dispose.call(n.value);if(n.async)return r|=2,Promise.resolve(a).then(o,function(e){return t(e),o()})}else r|=1}catch(e){t(e)}if(1===r)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function W(e,t){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,n,r,o,a){return n?t?".jsx":".js":!r||o&&a?r+o+"."+a.toLowerCase()+"js":e}):e}let F={__extends:o,__assign:a,__rest:i,__decorate:c,__param:u,__esDecorate:l,__runInitializers:s,__propKey:f,__setFunctionName:d,__metadata:p,__awaiter:v,__generator:h,__createBinding:y,__exportStar:m,__values:b,__read:g,__spread:w,__spreadArrays:E,__spreadArray:_,__await:S,__asyncGenerator:O,__asyncDelegator:P,__asyncValues:k,__makeTemplateObject:C,__importStar:T,__importDefault:L,__classPrivateFieldGet:A,__classPrivateFieldSet:N,__classPrivateFieldIn:R,__addDisposableResource:D,__disposeResources:M,__rewriteRelativeImportExtension:W}},23278:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(99004);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:c=2,absoluteStrokeWidth:u,className:l="",children:s,iconNode:f,...d}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:n,strokeWidth:u?24*Number(c)/Number(o):c,className:a("lucide",l),...d},[...f.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(s)?s:[s]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...l}=n;return(0,r.createElement)(c,{ref:i,iconNode:t,className:a("lucide-".concat(o(e)),u),...l})});return n.displayName="".concat(e),n}},29548:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(99004),a=n(88072),i=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),c=0;function u(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(c++))},[e]),e||(t?`radix-${t}`:"")}},40201:(e,t,n)=>{n.d(t,{n:()=>f});var r=n(99004),o=n(39552),a=n(51452),i=n(36962),c=n(52880),u="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:y,onUnmountAutoFocus:m,...b}=e,[g,w]=r.useState(null),E=(0,i.c)(y),_=(0,i.c)(m),S=r.useRef(null),O=(0,o.s)(t,e=>w(e)),P=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(P.paused||!g)return;let t=e.target;g.contains(t)?S.current=t:v(S.current,{select:!0})},t=function(e){if(P.paused||!g)return;let t=e.relatedTarget;null!==t&&(g.contains(t)||v(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(g)});return g&&n.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,g,P.paused]),r.useEffect(()=>{if(g){h.add(P);let e=document.activeElement;if(!g.contains(e)){let t=new CustomEvent(u,s);g.addEventListener(u,E),g.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(d(g).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(g))}return()=>{g.removeEventListener(u,E),setTimeout(()=>{let t=new CustomEvent(l,s);g.addEventListener(l,_),g.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),g.removeEventListener(l,_),h.remove(P)},0)}}},[g,E,_,P]);let k=r.useCallback(e=>{if(!n&&!f||P.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,a]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,P.paused]);return(0,c.jsx)(a.sG.div,{tabIndex:-1,...b,ref:O,onKeyDown:k})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=y(e,t)).unshift(t)},remove(t){var n;null==(n=(e=y(e,t))[0])||n.resume()}}}();function y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},49202:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(99004),o={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let a=(e,t,n,a)=>{let i=(0,r.forwardRef)((n,i)=>{let{color:c="currentColor",size:u=24,stroke:l=2,title:s,className:f,children:d,...p}=n;return(0,r.createElement)("svg",{ref:i,...o[e],width:u,height:u,className:["tabler-icon","tabler-icon-".concat(t),f].join(" "),..."filled"===e?{fill:c}:{strokeWidth:l,stroke:c},...p},[s&&(0,r.createElement)("title",{key:"svg-title"},s),...a.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(d)?d:[d]])});return i.displayName="".concat(n),i}},55173:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(99004),o=n(32909),a=n(51452),i=n(88072),c=n(52880),u=r.forwardRef((e,t)=>{var n,u;let{container:l,...s}=e,[f,d]=r.useState(!1);(0,i.N)(()=>d(!0),[]);let p=l||f&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,c.jsx)(a.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},85017:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(97921);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:c}=t,u=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==c?void 0:c[e];if(null===t)return null;let a=o(t)||o(r);return i[e][a]}),l=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,u,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...o}=t;return Object.entries(o).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...c,...l}[t]):({...c,...l})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},92350:(e,t,n)=>{n.d(t,{A:()=>Y});var r,o,a=n(20945),i=n(99004),c="right-scroll-bar-position",u="width-before-scroll-bar";function l(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,i=(t=null,void 0===n&&(n=d),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return i.options=(0,a.__assign)({async:!0,ssr:!1},e),i}(),v=function(){},h=i.forwardRef(function(e,t){var n,r,o,c,u=i.useRef(null),d=i.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),h=d[0],y=d[1],m=e.forwardProps,b=e.children,g=e.className,w=e.removeScrollBar,E=e.enabled,_=e.shards,S=e.sideCar,O=e.noIsolation,P=e.inert,k=e.allowPinchZoom,C=e.as,x=e.gapMode,j=(0,a.__rest)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),T=(n=[u,t],r=function(e){return n.forEach(function(t){return l(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,c=o.facade,s(function(){var e=f.get(c);if(e){var t=new Set(e),r=new Set(n),o=c.current;t.forEach(function(e){r.has(e)||l(e,null)}),r.forEach(function(e){t.has(e)||l(e,o)})}f.set(c,n)},[n]),c),L=(0,a.__assign)((0,a.__assign)({},j),h);return i.createElement(i.Fragment,null,E&&i.createElement(S,{sideCar:p,removeScrollBar:w,shards:_,noIsolation:O,inert:P,setCallbacks:y,allowPinchZoom:!!k,lockRef:u,gapMode:x}),m?i.cloneElement(i.Children.only(b),(0,a.__assign)((0,a.__assign)({},L),{ref:T})):i.createElement(void 0===C?"div":C,(0,a.__assign)({},L,{className:g,ref:T}),b))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:c};var y=function(e){var t=e.sideCar,n=(0,a.__rest)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,(0,a.__assign)({},n))};y.isSideCarExport=!0;var m=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=m();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},g=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},_=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=_(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},O=g(),P="data-scroll-locked",k=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(P,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(c," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(P,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(P)||"0",10);return isFinite(e)?e:0},x=function(){i.useEffect(function(){return document.body.setAttribute(P,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(P):document.body.setAttribute(P,e.toString())}},[])},j=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;x();var a=i.useMemo(function(){return S(o)},[o]);return i.createElement(O,{styles:k(a,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){T=!1}var A=!!T&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},R=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),D(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},D=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},M=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,f=c>0,d=0,p=0;do{var v=I(e,u),h=v[0],y=v[1]-v[2]-i*h;(h||y)&&D(e,u)&&(d+=y,p+=h),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&c>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-c>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},F=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,K=[];let G=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(z++)[0],c=i.useState(g)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,a.__spreadArray)([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=W(e),i=n.current,c="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,f=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=R(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=R(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var p=r.current||o;return M(p,t,e,"h"===p?c:l,!0)},[]),s=i.useCallback(function(e){if(K.length&&K[K.length-1]===c){var n="deltaY"in e?F(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=W(e),r.current=void 0},[]),p=i.useCallback(function(t){f(t.type,F(t),t.target,l(t,e.lockRef.current))},[]),v=i.useCallback(function(t){f(t.type,W(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return K.push(c),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:v}),document.addEventListener("wheel",s,A),document.addEventListener("touchmove",s,A),document.addEventListener("touchstart",d,A),function(){K=K.filter(function(e){return e!==c}),document.removeEventListener("wheel",s,A),document.removeEventListener("touchmove",s,A),document.removeEventListener("touchstart",d,A)}},[]);var h=e.removeScrollBar,y=e.inert;return i.createElement(i.Fragment,null,y?i.createElement(c,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(j,{gapMode:e.gapMode}):null)},p.useMedium(r),y);var X=i.forwardRef(function(e,t){return i.createElement(h,(0,a.__assign)({},e,{ref:t,sideCar:G}))});X.classNames=h.classNames;let Y=X}}]);