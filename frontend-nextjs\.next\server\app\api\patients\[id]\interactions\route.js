try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="4a988f61-1e74-42ad-85b4-1358b8e54f34",e._sentryDebugIdIdentifier="sentry-dbid-4a988f61-1e74-42ad-85b4-1358b8e54f34")}catch(e){}"use strict";(()=>{var e={};e.id=9310,e.ids=[9310],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},22868:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>w,serverHooks:()=>k,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>b});var o={};t.r(o),t.d(o,{DELETE:()=>m,GET:()=>y,HEAD:()=>v,OPTIONS:()=>P,PATCH:()=>f,POST:()=>h,PUT:()=>g});var s=t(86047),i=t(85544),n=t(36135),a=t(63033),p=t(53547),d=t(54360),u=t(19761);let c=(0,p.ZA)(async(e,r,{params:t})=>{try{let o=(0,d.o)(e),s=new URL(r.url),i=parseInt(s.searchParams.get("limit")||"20"),n=parseInt(s.searchParams.get("page")||"1"),a=s.searchParams.get("interactionType"),u=s.searchParams.get("status"),c=s.searchParams.get("priority"),l={patient:{equals:t.id}};a&&(l.interactionType={equals:a}),u&&(l.status={equals:u}),c&&(l.priority={equals:c}),"doctor"===e.role?l.and=[l,{or:[{staffMember:{equals:e.payloadUserId}},{interactionType:{in:["consultation-note","treatment-discussion","in-person-visit"]}}]}]:"front-desk"===e.role&&(l.and=[l,{interactionType:{in:["phone-call","email","billing-inquiry"]}}]);let x=await o.getPatientInteractions({limit:i,page:n,where:l,sort:"-timestamp"});return(0,p.$y)(x)}catch(e){return console.error("Error fetching patient interactions:",e),(0,p.WX)("Failed to fetch patient interactions")}}),l={...a},x="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;function q(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,o)=>{let s;try{let e=x?.getStore();s=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/patients/[id]/interactions",headers:s}).apply(t,o)}})}let y=q(c,"GET"),h=q(void 0,"POST"),g=q(void 0,"PUT"),f=q(void 0,"PATCH"),m=q(void 0,"DELETE"),v=q(void 0,"HEAD"),P=q(void 0,"OPTIONS"),w=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/patients/[id]/interactions/route",pathname:"/api/patients/[id]/interactions",filename:"route",bundlePath:"app/api/patients/[id]/interactions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patients\\[id]\\interactions\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:T,workUnitAsyncStorage:b,serverHooks:k}=w;function E(){return(0,n.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:b})}},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[55,3738,1950,5886,9615,125],()=>t(22868));module.exports=o})();
//# sourceMappingURL=route.js.map