try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="f392deed-aad9-42ab-bb9b-ad99c0ebdec9",e._sentryDebugIdIdentifier="sentry-dbid-f392deed-aad9-42ab-bb9b-ad99c0ebdec9")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1359],{22474:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(99004),o=r(39552),l=r(88072),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[o,i]=n.useState(),s=n.useRef({}),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=a(s.current);c.current="mounted"===d?e:"none"},[d]),(0,l.N)(()=>{let t=s.current,r=u.current;if(r!==e){let n=c.current,o=a(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=a(s.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(c.current=a(s.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(s.current=getComputedStyle(e)),i(e)},[])}}(t),s="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),u=(0,o.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||i.isPresent?n.cloneElement(s,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},36962:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(99004);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},38774:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>l});var n=r(99004),o=r(52880);function l(e,t){let r=n.createContext(t),l=e=>{let{children:t,...l}=e,i=n.useMemo(()=>l,Object.values(l));return(0,o.jsx)(r.Provider,{value:i,children:t})};return l.displayName=e+"Provider",[l,function(o){let l=n.useContext(r);if(l)return l;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;r=[...r,l];let s=t=>{let{scope:r,children:l,...s}=t,u=r?.[e]?.[a]||i,c=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:c,children:l})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||i,u=n.useContext(s);if(u)return u;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},39552:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>l});var n=r(99004);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},50516:(e,t,r)=>{r.d(t,{DX:()=>i,xV:()=>s});var n=r(99004),o=r(39552),l=r(52880),i=n.forwardRef((e,t)=>{let{children:r,...o}=e,i=n.Children.toArray(r),s=i.find(u);if(s){let e=s.props.children,r=i.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(a,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,l.jsx)(a,{...o,ref:t,children:r})});i.displayName="Slot";var a=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),i=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{l(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(i.ref=t?(0,o.t)(t,e):e),n.cloneElement(r,i)}return n.Children.count(r)>1?n.Children.only(null):null});a.displayName="SlotClone";var s=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function u(e){return n.isValidElement(e)&&e.type===s}},51452:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>a});var n=r(99004),o=r(32909),l=r(50516),i=r(52880),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,a=n?l.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},51825:(e,t,r)=>{r.d(t,{jH:()=>l});var n=r(99004);r(52880);var o=n.createContext(void 0);function l(e){let t=n.useContext(o);return e||t||"ltr"}},67051:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},71359:(e,t,r)=>{r.d(t,{LM:()=>G,OK:()=>q,VM:()=>C,bL:()=>B,lr:()=>O});var n=r(99004),o=r(51452),l=r(22474),i=r(38774),a=r(39552),s=r(36962),u=r(51825),c=r(88072),d=r(67051),f=r(84732),p=r(52880),v="ScrollArea",[h,m]=(0,i.A)(v),[w,g]=h(v),b=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:l="hover",dir:i,scrollHideDelay:s=600,...c}=e,[d,f]=n.useState(null),[v,h]=n.useState(null),[m,g]=n.useState(null),[b,y]=n.useState(null),[S,E]=n.useState(null),[C,x]=n.useState(0),[T,N]=n.useState(0),[R,P]=n.useState(!1),[_,L]=n.useState(!1),j=(0,a.s)(t,e=>f(e)),D=(0,u.jH)(i);return(0,p.jsx)(w,{scope:r,type:l,dir:D,scrollHideDelay:s,scrollArea:d,viewport:v,onViewportChange:h,content:m,onContentChange:g,scrollbarX:b,onScrollbarXChange:y,scrollbarXEnabled:R,onScrollbarXEnabledChange:P,scrollbarY:S,onScrollbarYChange:E,scrollbarYEnabled:_,onScrollbarYEnabledChange:L,onCornerWidthChange:x,onCornerHeightChange:N,children:(0,p.jsx)(o.sG.div,{dir:D,...c,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":T+"px",...e.style}})})});b.displayName=v;var y="ScrollAreaViewport",S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:l,nonce:i,...s}=e,u=g(y,r),c=n.useRef(null),d=(0,a.s)(t,c,u.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});S.displayName=y;var E="ScrollAreaScrollbar",C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,p.jsx)(x,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,p.jsx)(T,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,p.jsx)(N,{...o,ref:t,forceMount:r}):"always"===l.type?(0,p.jsx)(R,{...o,ref:t}):null});C.displayName=E;var x=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=g(E,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,p.jsx)(l.C,{present:r||a,children:(0,p.jsx)(N,{"data-state":a?"visible":"hidden",...o,ref:t})})}),T=n.forwardRef((e,t)=>{var r,o;let{forceMount:i,...a}=e,s=g(E,e.__scopeScrollArea),u="horizontal"===e.orientation,c=Y(()=>v("SCROLL_END"),100),[d,v]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=o[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>v("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,s.scrollHideDelay,v]),n.useEffect(()=>{let e=s.viewport,t=u?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(v("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,u,v,c]),(0,p.jsx)(l.C,{present:i||"hidden"!==d,children:(0,p.jsx)(R,{"data-state":"hidden"===d?"hidden":"visible",...a,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>v("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>v("POINTER_LEAVE"))})})}),N=n.forwardRef((e,t)=>{let r=g(E,e.__scopeScrollArea),{forceMount:o,...i}=e,[a,s]=n.useState(!1),u="horizontal"===e.orientation,c=Y(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return $(r.viewport,c),$(r.content,c),(0,p.jsx)(l.C,{present:o||a,children:(0,p.jsx)(R,{"data-state":a?"visible":"hidden",...i,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=g(E,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=z(s.viewport,s.content),d={...o,sizes:s,onSizesChange:u,hasThumb:!!(c>0&&c<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=k(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return F([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,p.jsx)(P,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollLeft,s,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===r?(0,p.jsx)(_,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=X(l.viewport.scrollTop,s);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=g(E,e.__scopeScrollArea),[s,u]=n.useState(),c=n.useRef(null),d=(0,a.s)(t,c,i.onScrollbarXChange);return n.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,p.jsx)(D,{"data-orientation":"horizontal",...l,ref:d,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":k(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:H(s.paddingLeft),paddingEnd:H(s.paddingRight)}})}})}),_=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,i=g(E,e.__scopeScrollArea),[s,u]=n.useState(),c=n.useRef(null),d=(0,a.s)(t,c,i.onScrollbarYChange);return n.useEffect(()=>{c.current&&u(getComputedStyle(c.current))},[c]),(0,p.jsx)(D,{"data-orientation":"vertical",...l,ref:d,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":k(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:H(s.paddingTop),paddingEnd:H(s.paddingBottom)}})}})}),[L,j]=h(E),D=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:l,hasThumb:i,onThumbChange:u,onThumbPointerUp:c,onThumbPointerDown:d,onThumbPositionChange:v,onDragScroll:h,onWheelScroll:m,onResize:w,...b}=e,y=g(E,r),[S,C]=n.useState(null),x=(0,a.s)(t,e=>C(e)),T=n.useRef(null),N=n.useRef(""),R=y.viewport,P=l.content-l.viewport,_=(0,s.c)(m),j=(0,s.c)(v),D=Y(w,10);function A(e){T.current&&h({x:e.clientX-T.current.left,y:e.clientY-T.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==S?void 0:S.contains(t))&&_(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,S,P,_]),n.useEffect(j,[l,j]),$(S,D),$(y.content,D),(0,p.jsx)(L,{scope:r,scrollbar:S,hasThumb:i,onThumbChange:(0,s.c)(u),onThumbPointerUp:(0,s.c)(c),onThumbPositionChange:j,onThumbPointerDown:(0,s.c)(d),children:(0,p.jsx)(o.sG.div,{...b,ref:x,style:{position:"absolute",...b.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),T.current=S.getBoundingClientRect(),N.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:(0,f.m)(e.onPointerMove,A),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=N.current,y.viewport&&(y.viewport.style.scrollBehavior=""),T.current=null})})})}),A="ScrollAreaThumb",O=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=j(A,e.__scopeScrollArea);return(0,p.jsx)(l.C,{present:r||o.hasThumb,children:(0,p.jsx)(M,{ref:t,...n})})}),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:l,...i}=e,s=g(A,r),u=j(A,r),{onThumbPositionChange:c}=u,d=(0,a.s)(t,e=>u.onThumbChange(e)),v=n.useRef(void 0),h=Y(()=>{v.current&&(v.current(),v.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{h(),v.current||(v.current=V(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,h,c]),(0,p.jsx)(o.sG.div,{"data-state":u.hasThumb?"visible":"hidden",...i,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,u.onThumbPointerUp)})});O.displayName=A;var I="ScrollAreaCorner",W=n.forwardRef((e,t)=>{let r=g(I,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(U,{...e,ref:t}):null});W.displayName=I;var U=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=g(I,r),[a,s]=n.useState(0),[u,c]=n.useState(0),d=!!(a&&u);return $(i.scrollbarX,()=>{var e;let t=(null==(e=i.scrollbarX)?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),c(t)}),$(i.scrollbarY,()=>{var e;let t=(null==(e=i.scrollbarY)?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),s(t)}),d?(0,p.jsx)(o.sG.div,{...l,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function z(e,t){let r=e/t;return isNaN(r)?0:r}function k(e){let t=z(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function X(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=k(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,d.q)(e,"ltr"===r?[0,i]:[-1*i,0]);return F([0,i],[0,l-n])(a)}function F(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var V=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function Y(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function $(e,t){let r=(0,s.c)(t);(0,c.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var B=b,G=S,q=W},84732:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},88072:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(99004),o=globalThis?.document?n.useLayoutEffect:()=>{}}}]);