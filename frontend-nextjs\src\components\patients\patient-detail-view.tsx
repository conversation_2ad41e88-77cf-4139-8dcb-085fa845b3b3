'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  IconArrowLeft,
  IconUser,
  IconPhone,
  IconMail,
  IconCalendar,
  IconClock,
  IconEdit,
  IconMessageCircle,
  IconFileText,
  IconActivity,
  IconTrendingUp
} from '@/components/icons';
import { InteractionTimeline } from '@/components/crm/interaction-timeline';
import { TaskManager } from '@/components/crm/task-manager';
import { QuickActions } from '@/components/crm/quick-actions';
import { CommunicationLog } from '@/components/crm/communication-log';
import { InteractionFormDialog } from '@/components/crm/interaction-form-dialog';
import { TaskFormDialog } from '@/components/crm/task-form-dialog';
import { Patient, PatientInteraction, PatientTask, TimelineItem, User, PatientInteractionFormData, PatientTaskFormData } from '@/types/clinic';
import { createPayloadClient } from '@/lib/payload-client';
import { useUser } from '@clerk/nextjs';
import { crmNotifications, showCrmLoadingToast } from '@/lib/crm-notifications';
import { toast } from 'sonner';
import { formatDateTime, formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface PatientDetailViewProps {
  patientId: string;
}

export default function PatientDetailView({ patientId }: PatientDetailViewProps) {
  const router = useRouter();
  const { user } = useUser();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [interactions, setInteractions] = useState<PatientInteraction[]>([]);
  const [tasks, setTasks] = useState<PatientTask[]>([]);
  const [timeline, setTimeline] = useState<TimelineItem[]>([]);
  const [availableStaff, setAvailableStaff] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Dialog states
  const [interactionDialogOpen, setInteractionDialogOpen] = useState(false);
  const [taskDialogOpen, setTaskDialogOpen] = useState(false);
  const [defaultInteractionType, setDefaultInteractionType] = useState<string>('');
  const [defaultTaskType, setDefaultTaskType] = useState<string>('');
  const [editingInteraction, setEditingInteraction] = useState<PatientInteraction | undefined>();
  const [editingTask, setEditingTask] = useState<PatientTask | undefined>();
  const [formLoading, setFormLoading] = useState(false);

  // Load patient data and CRM information
  useEffect(() => {
    const loadPatientData = async () => {
      if (!user) return;

      const loadingToast = showCrmLoadingToast('加载患者信息...');
      
      try {
        const payloadClient = createPayloadClient({
          clerkId: user.id,
          email: user.emailAddresses[0]?.emailAddress || '',
          firstName: user.firstName || '',
          lastName: user.lastName || '',
        });

        // Load patient basic info
        const patientData = await payloadClient.getPatient(patientId);
        setPatient(patientData as Patient);

        // Load interactions
        const interactionsData = await payloadClient.getPatientInteractionsByPatient(patientId, {
          limit: 50,
        });
        setInteractions((interactionsData as any).docs || []);

        // Load tasks
        const tasksData = await payloadClient.getPatientTasksByPatient(patientId, {
          limit: 50,
        });
        setTasks((tasksData as any).docs || []);

        // Load combined timeline
        const timelineData = await payloadClient.getPatientTimeline(patientId, {
          limit: 100,
        });
        setTimeline((timelineData as any).docs || []);

        // Load available staff for task assignment
        const staffData = await payloadClient.getUsers({
          limit: 50,
        });
        setAvailableStaff((staffData as any).docs || []);

        crmNotifications.success.dataRefreshed('患者信息');
      } catch (error) {
        console.error('Error loading patient data:', error);
        crmNotifications.error.loadFailed('患者信息', error instanceof Error ? error.message : undefined);
      } finally {
        setLoading(false);
        toast.dismiss(loadingToast);
      }
    };

    loadPatientData();
  }, [patientId, user]);

  const handleCreateInteraction = (type?: string) => {
    setDefaultInteractionType(type || '');
    setEditingInteraction(undefined);
    setInteractionDialogOpen(true);
  };

  const handleCreateTask = (type?: string) => {
    setDefaultTaskType(type || '');
    setEditingTask(undefined);
    setTaskDialogOpen(true);
  };

  const handleSubmitInteraction = async (data: PatientInteractionFormData) => {
    setFormLoading(true);
    try {
      const payloadClient = createPayloadClient({
        clerkId: user!.id,
        email: user!.emailAddresses[0]?.emailAddress || '',
        firstName: user!.firstName || '',
        lastName: user!.lastName || '',
      });

      if (editingInteraction) {
        const updated = await payloadClient.updatePatientInteraction(editingInteraction.id, data);
        setInteractions(prev => prev.map(item =>
          item.id === editingInteraction.id ? (updated as PatientInteraction) : item
        ));
        crmNotifications.interaction.updated(updated as PatientInteraction);
      } else {
        const created = await payloadClient.createPatientInteraction(data);
        setInteractions(prev => [(created as PatientInteraction), ...prev]);
        crmNotifications.interaction.created(created as PatientInteraction);
      }

      // Refresh timeline
      const timelineData = await payloadClient.getPatientTimeline(patientId, { limit: 100 });
      setTimeline((timelineData as any).docs || []);
    } catch (error) {
      console.error('Error submitting interaction:', error);
      crmNotifications.error.interactionCreateFailed(error instanceof Error ? error.message : undefined);
    } finally {
      setFormLoading(false);
    }
  };

  const handleSubmitTask = async (data: PatientTaskFormData) => {
    setFormLoading(true);
    try {
      const payloadClient = createPayloadClient({
        clerkId: user!.id,
        email: user!.emailAddresses[0]?.emailAddress || '',
        firstName: user!.firstName || '',
        lastName: user!.lastName || '',
      });

      if (editingTask) {
        const updated = await payloadClient.updatePatientTask(editingTask.id, data);
        setTasks(prev => prev.map(item =>
          item.id === editingTask.id ? (updated as PatientTask) : item
        ));
        crmNotifications.task.updated(updated as PatientTask);
      } else {
        const created = await payloadClient.createPatientTask(data);
        setTasks(prev => [(created as PatientTask), ...prev]);
        crmNotifications.task.created(created as PatientTask);
      }

      // Refresh timeline
      const timelineData = await payloadClient.getPatientTimeline(patientId, { limit: 100 });
      setTimeline((timelineData as any).docs || []);
    } catch (error) {
      console.error('Error submitting task:', error);
      crmNotifications.error.taskCreateFailed(error instanceof Error ? error.message : undefined);
    } finally {
      setFormLoading(false);
    }
  };

  const handleScheduleAppointment = () => {
    // TODO: Navigate to appointment creation
    router.push(`/dashboard/appointments/new?patientId=${patientId}`);
  };

  const handleViewBilling = () => {
    // TODO: Navigate to billing page
    router.push(`/dashboard/billing?patientId=${patientId}`);
  };

  const handleTaskStatusChange = async (taskId: string, status: PatientTask['status']) => {
    try {
      const payloadClient = createPayloadClient({
        clerkId: user!.id,
        email: user!.emailAddresses[0]?.emailAddress || '',
        firstName: user!.firstName || '',
        lastName: user!.lastName || '',
      });

      await payloadClient.updatePatientTask(taskId, { status });
      
      // Update local state
      setTasks(prev => prev.map(task => 
        task.id === taskId ? { ...task, status } : task
      ));

      const task = tasks.find(t => t.id === taskId);
      if (task) {
        crmNotifications.task.statusChanged(task, task.status, status);
      }
    } catch (error) {
      console.error('Error updating task status:', error);
      crmNotifications.error.updateFailed('task', error instanceof Error ? error.message : undefined);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4" />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-4">
              <div className="h-64 bg-gray-200 rounded" />
              <div className="h-96 bg-gray-200 rounded" />
            </div>
            <div className="space-y-4">
              <div className="h-48 bg-gray-200 rounded" />
              <div className="h-64 bg-gray-200 rounded" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="text-center py-12">
        <IconUser className="size-12 mx-auto mb-4 text-muted-foreground" />
        <h3 className="text-lg font-medium mb-2">患者未找到</h3>
        <p className="text-muted-foreground mb-4">请检查患者ID是否正确</p>
        <Button onClick={() => router.back()}>
          <IconArrowLeft className="size-4 mr-2" />
          返回
        </Button>
      </div>
    );
  }

  // Calculate summary statistics
  const totalInteractions = interactions.length;
  const pendingTasks = tasks.filter(task => task.status === 'pending').length;
  const overdueTasks = tasks.filter(task => 
    task.status !== 'completed' && new Date(task.dueDate) < new Date()
  ).length;
  const lastInteraction = interactions[0]; // Assuming sorted by timestamp desc

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <IconArrowLeft className="size-4 mr-2" />
            返回
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{patient.fullName}</h1>
            <p className="text-muted-foreground">患者详细信息</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <QuickActions
            patient={patient}
            onCreateInteraction={handleCreateInteraction}
            onCreateTask={handleCreateTask}
            onScheduleAppointment={handleScheduleAppointment}
            onViewBilling={handleViewBilling}
            compact
          />
          <Button variant="outline" size="sm">
            <IconEdit className="size-4 mr-2" />
            编辑
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">概览</TabsTrigger>
              <TabsTrigger value="interactions">
                互动记录 ({totalInteractions})
              </TabsTrigger>
              <TabsTrigger value="tasks">
                任务管理 ({tasks.length})
              </TabsTrigger>
              <TabsTrigger value="timeline">时间线</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Patient Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <IconUser className="size-5" />
                    基本信息
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start gap-4">
                    <Avatar className="size-16">
                      <AvatarImage 
                        src={typeof patient.photo === 'object' ? patient.photo?.url : undefined} 
                        alt={patient.fullName} 
                      />
                      <AvatarFallback className="text-lg">
                        {patient.fullName.slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="flex items-center gap-2">
                          <IconPhone className="size-4 text-muted-foreground" />
                          <span className="font-medium">{patient.phone}</span>
                        </div>
                        {patient.email && (
                          <div className="flex items-center gap-2">
                            <IconMail className="size-4 text-muted-foreground" />
                            <span>{patient.email}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-2">
                          <IconCalendar className="size-4 text-muted-foreground" />
                          <span>创建时间: {formatDateTime(new Date(patient.createdAt))}</span>
                        </div>
                        {patient.lastVisit && (
                          <div className="flex items-center gap-2">
                            <IconClock className="size-4 text-muted-foreground" />
                            <span>最后就诊: {formatDateTime(new Date(patient.lastVisit))}</span>
                          </div>
                        )}
                      </div>
                      
                      {patient.userType && (
                        <div className="flex items-center gap-2">
                          <Badge variant={patient.userType === 'patient' ? 'default' : 'secondary'}>
                            {patient.userType === 'patient' ? '正式患者' : '咨询用户'}
                          </Badge>
                          {patient.status && (
                            <Badge variant="outline">
                              {patient.status === 'active' && '活跃'}
                              {patient.status === 'inactive' && '非活跃'}
                              {patient.status === 'converted' && '已转换'}
                            </Badge>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Activity Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <IconActivity className="size-5" />
                    活动概要
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{totalInteractions}</div>
                      <div className="text-sm text-muted-foreground">总互动次数</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{pendingTasks}</div>
                      <div className="text-sm text-muted-foreground">待处理任务</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{overdueTasks}</div>
                      <div className="text-sm text-muted-foreground">逾期任务</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {lastInteraction ? formatRelativeTime(new Date(lastInteraction.timestamp)) : '无'}
                      </div>
                      <div className="text-sm text-muted-foreground">最后联系</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="interactions">
              <InteractionTimeline
                patientId={patientId}
                interactions={interactions}
                onCreateInteraction={handleCreateInteraction}
                onInteractionClick={(interaction) => console.log('View interaction:', interaction)}
              />
            </TabsContent>

            <TabsContent value="tasks">
              <TaskManager
                patientId={patientId}
                tasks={tasks}
                onCreateTask={handleCreateTask}
                onTaskClick={(task) => console.log('View task:', task)}
                onTaskStatusChange={handleTaskStatusChange}
                viewMode="list"
              />
            </TabsContent>

            <TabsContent value="timeline">
              <CommunicationLog
                patientId={patientId}
                timeline={timeline}
                onItemClick={(item) => console.log('View timeline item:', item)}
                onEditItem={(item) => console.log('Edit timeline item:', item)}
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <QuickActions
            patient={patient}
            onCreateInteraction={handleCreateInteraction}
            onCreateTask={handleCreateTask}
            onScheduleAppointment={handleScheduleAppointment}
            onViewBilling={handleViewBilling}
          />

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <IconTrendingUp className="size-5" />
                最近活动
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {timeline.slice(0, 5).map((item) => (
                  <div key={item.id} className="flex items-start gap-3 text-sm">
                    <div className="size-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="font-medium">{item.title}</p>
                      <p className="text-muted-foreground text-xs">
                        {formatRelativeTime(new Date(item.timestamp))}
                      </p>
                    </div>
                  </div>
                ))}
                {timeline.length === 0 && (
                  <p className="text-muted-foreground text-sm text-center py-4">
                    暂无活动记录
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Dialogs */}
      <InteractionFormDialog
        open={interactionDialogOpen}
        onOpenChange={setInteractionDialogOpen}
        patientId={patientId}
        patientName={patient.fullName}
        interaction={editingInteraction}
        defaultType={defaultInteractionType}
        onSubmit={handleSubmitInteraction}
        loading={formLoading}
      />

      <TaskFormDialog
        open={taskDialogOpen}
        onOpenChange={setTaskDialogOpen}
        patientId={patientId}
        patientName={patient.fullName}
        task={editingTask}
        defaultType={defaultTaskType}
        availableStaff={availableStaff}
        onSubmit={handleSubmitTask}
        loading={formLoading}
      />
    </div>
  );
}
