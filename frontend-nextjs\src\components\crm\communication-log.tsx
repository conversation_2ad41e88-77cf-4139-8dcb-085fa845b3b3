'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  IconMessageCircle,
  IconSearch,
  IconFilter,
  IconPhone,
  IconMail,
  IconStethoscope,
  IconUser,
  IconCreditCard,
  IconClock,
  IconArrowRight,
  IconEye,
  IconEdit,
  IconCalendar
} from '@/components/icons';
import { PatientInteraction, PatientTask, TimelineItem, User } from '@/types/clinic';
import { formatDateTime, formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface CommunicationLogProps {
  patientId: string;
  timeline: TimelineItem[];
  loading?: boolean;
  onItemClick?: (item: TimelineItem) => void;
  onEditItem?: (item: TimelineItem) => void;
  className?: string;
}

const interactionTypeConfig = {
  'phone-call': {
    label: '电话通话',
    icon: IconPhone,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  'email': {
    label: '邮件沟通',
    icon: IconMail,
    color: 'bg-green-100 text-green-800 border-green-200',
  },
  'consultation-note': {
    label: '咨询记录',
    icon: IconStethoscope,
    color: 'bg-purple-100 text-purple-800 border-purple-200',
  },
  'in-person-visit': {
    label: '到院就诊',
    icon: IconUser,
    color: 'bg-orange-100 text-orange-800 border-orange-200',
  },
  'treatment-discussion': {
    label: '治疗讨论',
    icon: IconMessageCircle,
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  },
  'billing-inquiry': {
    label: '账单咨询',
    icon: IconCreditCard,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
};

const taskTypeConfig = {
  'follow-up-call': {
    label: '跟进电话',
    icon: IconPhone,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
  },
  'appointment-scheduling': {
    label: '预约安排',
    icon: IconCalendar,
    color: 'bg-green-100 text-green-800 border-green-200',
  },
  'treatment-reminder': {
    label: '治疗提醒',
    icon: IconStethoscope,
    color: 'bg-purple-100 text-purple-800 border-purple-200',
  },
  'billing-follow-up': {
    label: '账单跟进',
    icon: IconCreditCard,
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  },
  'medical-record-update': {
    label: '病历更新',
    icon: IconUser,
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  },
  'consultation-follow-up': {
    label: '咨询跟进',
    icon: IconMessageCircle,
    color: 'bg-orange-100 text-orange-800 border-orange-200',
  },
};

export function CommunicationLog({
  patientId,
  timeline,
  loading = false,
  onItemClick,
  onEditItem,
  className,
}: CommunicationLogProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<string>('all');
  const [filteredTimeline, setFilteredTimeline] = useState<TimelineItem[]>(timeline);

  // Filter timeline based on search, filters, and active tab
  useEffect(() => {
    let filtered = timeline;

    // Tab filter
    if (activeTab !== 'all') {
      filtered = filtered.filter(item => item.type === activeTab);
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.type === 'interaction' && 
         (item.data as PatientInteraction).outcome?.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (item.type === 'task' && 
         (item.data as PatientTask).description?.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(item => {
        if (item.type === 'interaction') {
          return (item.data as PatientInteraction).interactionType === filterType;
        } else {
          return (item.data as PatientTask).taskType === filterType;
        }
      });
    }

    setFilteredTimeline(filtered);
  }, [timeline, searchTerm, filterType, activeTab]);

  const getItemIcon = (item: TimelineItem) => {
    if (item.type === 'interaction') {
      const interaction = item.data as PatientInteraction;
      const config = interactionTypeConfig[interaction.interactionType];
      const IconComponent = config?.icon || IconMessageCircle;
      return <IconComponent className="size-4" />;
    } else {
      const task = item.data as PatientTask;
      const config = taskTypeConfig[task.taskType];
      const IconComponent = config?.icon || IconClock;
      return <IconComponent className="size-4" />;
    }
  };

  const getItemColor = (item: TimelineItem) => {
    if (item.type === 'interaction') {
      const interaction = item.data as PatientInteraction;
      return interactionTypeConfig[interaction.interactionType]?.color || 'bg-gray-100 text-gray-800 border-gray-200';
    } else {
      const task = item.data as PatientTask;
      return taskTypeConfig[task.taskType]?.color || 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStaffName = (staff: User | string | undefined) => {
    if (!staff) return '未知';
    if (typeof staff === 'string') return '未知工作人员';
    return `${staff.firstName || ''} ${staff.lastName || ''}`.trim() || staff.email;
  };

  const renderTimelineItem = (item: TimelineItem, index: number) => {
    const isInteraction = item.type === 'interaction';
    const data = item.data;
    const itemColor = getItemColor(item);

    return (
      <div key={item.id} className="relative">
        {/* Timeline line */}
        {index < filteredTimeline.length - 1 && (
          <div className="absolute left-4 top-12 bottom-0 w-px bg-border" />
        )}

        <div 
          className={cn(
            "flex items-start gap-3 p-4 rounded-lg border transition-colors",
            onItemClick && "cursor-pointer hover:bg-muted/50"
          )}
          onClick={() => onItemClick?.(item)}
        >
          {/* Icon */}
          <div className={cn(
            "flex items-center justify-center size-8 rounded-full border-2 flex-shrink-0",
            itemColor
          )}>
            {getItemIcon(item)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <div>
                <h4 className="font-medium text-sm leading-tight mb-1">
                  {item.title}
                </h4>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline" className="text-xs">
                    {isInteraction ? '互动' : '任务'}
                  </Badge>
                  <span className="flex items-center gap-1">
                    <IconUser className="size-3" />
                    {isInteraction 
                      ? getStaffName(item.staffMember)
                      : `分配给: ${getStaffName(item.assignedTo)}`
                    }
                  </span>
                </div>
              </div>
              
              <div className="flex items-center gap-1 flex-shrink-0">
                <Badge variant="outline" className={
                  item.status === 'completed' || item.status === 'resolved' || item.status === 'closed'
                    ? 'bg-green-100 text-green-800'
                    : item.status === 'in-progress'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }>
                  {item.status === 'open' && '开放'}
                  {item.status === 'in-progress' && '进行中'}
                  {item.status === 'resolved' && '已解决'}
                  {item.status === 'closed' && '已关闭'}
                  {item.status === 'pending' && '待处理'}
                  {item.status === 'completed' && '已完成'}
                  {item.status === 'cancelled' && '已取消'}
                </Badge>
                <Badge variant="outline" className={
                  item.priority === 'urgent' || item.priority === 'high'
                    ? 'bg-red-100 text-red-800'
                    : item.priority === 'medium'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-gray-100 text-gray-600'
                }>
                  {item.priority === 'low' && '低'}
                  {item.priority === 'medium' && '中'}
                  {item.priority === 'high' && '高'}
                  {item.priority === 'urgent' && '紧急'}
                </Badge>
              </div>
            </div>

            {/* Content preview */}
            {isInteraction ? (
              <div>
                {(data as PatientInteraction).outcome && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                    {(data as PatientInteraction).outcome}
                  </p>
                )}
                {(data as PatientInteraction).followUpRequired && (
                  <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded mb-2">
                    <IconArrowRight className="size-3" />
                    需要跟进
                    {(data as PatientInteraction).followUpDate && (
                      <span>- {formatDateTime(new Date((data as PatientInteraction).followUpDate!))}</span>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div>
                {(data as PatientTask).description && (
                  <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                    {(data as PatientTask).description}
                  </p>
                )}
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <IconCalendar className="size-3" />
                    截止: {formatDateTime(new Date((data as PatientTask).dueDate))}
                  </span>
                  {(data as PatientTask).completedAt && (
                    <span className="flex items-center gap-1 text-green-600">
                      <IconClock className="size-3" />
                      完成: {(data as PatientTask).completedAt ? formatDateTime(new Date((data as PatientTask).completedAt!)) : '未完成'}
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Timestamp and actions */}
            <div className="flex items-center justify-between mt-3">
              <span className="text-xs text-muted-foreground">
                {formatRelativeTime(new Date(item.timestamp))} • {formatDateTime(new Date(item.timestamp))}
              </span>
              
              {(onItemClick || onEditItem) && (
                <div className="flex items-center gap-1">
                  {onItemClick && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        onItemClick(item);
                      }}
                      className="h-6 px-2 text-xs"
                    >
                      <IconEye className="size-3 mr-1" />
                      查看
                    </Button>
                  )}
                  {onEditItem && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditItem(item);
                      }}
                      className="h-6 px-2 text-xs"
                    >
                      <IconEdit className="size-3 mr-1" />
                      编辑
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconMessageCircle className="size-5" />
            沟通记录
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-start gap-3">
                  <div className="size-8 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const interactionCount = timeline.filter(item => item.type === 'interaction').length;
  const taskCount = timeline.filter(item => item.type === 'task').length;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <IconMessageCircle className="size-5" />
          沟通记录
          <Badge variant="secondary" className="ml-2">
            {filteredTimeline.length}
          </Badge>
        </CardTitle>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="relative flex-1">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
            <Input
              placeholder="搜索沟通记录..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-full sm:w-[140px]">
              <IconFilter className="size-4 mr-2" />
              <SelectValue placeholder="类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有类型</SelectItem>
              <Separator className="my-1" />
              <SelectItem value="phone-call">电话通话</SelectItem>
              <SelectItem value="email">邮件沟通</SelectItem>
              <SelectItem value="consultation-note">咨询记录</SelectItem>
              <SelectItem value="in-person-visit">到院就诊</SelectItem>
              <SelectItem value="treatment-discussion">治疗讨论</SelectItem>
              <SelectItem value="billing-inquiry">账单咨询</SelectItem>
              <Separator className="my-1" />
              <SelectItem value="follow-up-call">跟进电话</SelectItem>
              <SelectItem value="appointment-scheduling">预约安排</SelectItem>
              <SelectItem value="treatment-reminder">治疗提醒</SelectItem>
              <SelectItem value="billing-follow-up">账单跟进</SelectItem>
              <SelectItem value="medical-record-update">病历更新</SelectItem>
              <SelectItem value="consultation-follow-up">咨询跟进</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">
              全部 ({timeline.length})
            </TabsTrigger>
            <TabsTrigger value="interaction">
              互动 ({interactionCount})
            </TabsTrigger>
            <TabsTrigger value="task">
              任务 ({taskCount})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-4">
            <ScrollArea className="h-[600px]">
              {filteredTimeline.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <IconMessageCircle className="size-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">暂无沟通记录</p>
                  <p className="text-sm">开始记录与患者的互动和任务</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredTimeline.map(renderTimelineItem)}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
