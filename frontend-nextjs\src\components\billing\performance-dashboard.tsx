'use client';

// Performance monitoring dashboard for billing system
// Displays real-time performance metrics, cache statistics, and system health

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  IconActivity, 
  IconDatabase, 
  IconClock,
  IconTrendingUp,
  IconTrendingDown,
  IconRefresh,
  IconAlertTriangle,
  IconCheck,
  IconBolt
} from '@tabler/icons-react';
import { 
  billingCache, 
  performanceMonitor,
  Memoizer
} from '@/lib/billing-performance';

interface PerformanceDashboardProps {
  className?: string;
  refreshInterval?: number;
}

export function PerformanceDashboard({ 
  className = '', 
  refreshInterval = 5000 
}: PerformanceDashboardProps) {
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [performanceStats, setPerformanceStats] = useState<any>(null);
  const [memoryStats, setMemoryStats] = useState<any>(null);
  const [systemHealth, setSystemHealth] = useState<'good' | 'warning' | 'critical'>('good');
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // Update performance data
  const updatePerformanceData = () => {
    setCacheStats(billingCache.getStats());
    setPerformanceStats(performanceMonitor.getStats());
    setMemoryStats(performanceMonitor.getMemoryStats());
    setLastUpdate(new Date());

    // Determine system health
    const cache = billingCache.getStats();
    const memory = performanceMonitor.getMemoryStats();
    
    let health: 'good' | 'warning' | 'critical' = 'good';
    
    if (cache.hitRate < 0.5) {
      health = 'warning';
    }
    
    if (memory && memory.current > 100 * 1024 * 1024) { // 100MB
      health = 'critical';
    }
    
    setSystemHealth(health);
  };

  // Set up periodic updates
  useEffect(() => {
    updatePerformanceData();
    const interval = setInterval(updatePerformanceData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);

  // Format bytes to human readable
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format duration to human readable
  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  // Get health status color
  const getHealthColor = (health: string): string => {
    switch (health) {
      case 'good': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Get health status icon
  const getHealthIcon = (health: string) => {
    switch (health) {
      case 'good': return <IconCheck className="h-5 w-5 text-green-600" />;
      case 'warning': return <IconAlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'critical': return <IconAlertTriangle className="h-5 w-5 text-red-600" />;
      default: return <IconActivity className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <IconBolt className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">性能监控</h2>
          <div className="flex items-center space-x-2">
            {getHealthIcon(systemHealth)}
            <span className={`text-sm font-medium ${getHealthColor(systemHealth)}`}>
              {systemHealth === 'good' ? '系统正常' : 
               systemHealth === 'warning' ? '性能警告' : '性能严重'}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">
            最后更新: {lastUpdate.toLocaleTimeString()}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={updatePerformanceData}
          >
            <IconRefresh className="h-4 w-4 mr-2" />
            刷新
          </Button>
        </div>
      </div>

      {/* System Health Alert */}
      {systemHealth !== 'good' && (
        <Alert className={systemHealth === 'critical' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'}>
          <IconAlertTriangle className={`h-4 w-4 ${systemHealth === 'critical' ? 'text-red-600' : 'text-yellow-600'}`} />
          <AlertDescription>
            <div className="space-y-1">
              <p className={`font-medium ${systemHealth === 'critical' ? 'text-red-800' : 'text-yellow-800'}`}>
                {systemHealth === 'critical' ? '性能严重问题' : '性能警告'}
              </p>
              <ul className={`text-sm space-y-1 ${systemHealth === 'critical' ? 'text-red-700' : 'text-yellow-700'}`}>
                {cacheStats && cacheStats.hitRate < 0.5 && (
                  <li>• 缓存命中率过低 ({(cacheStats.hitRate * 100).toFixed(1)}%)</li>
                )}
                {memoryStats && memoryStats.current > 100 * 1024 * 1024 && (
                  <li>• 内存使用过高 ({formatBytes(memoryStats.current)})</li>
                )}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Performance Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="cache">缓存</TabsTrigger>
          <TabsTrigger value="performance">性能</TabsTrigger>
          <TabsTrigger value="memory">内存</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Cache Overview */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <IconDatabase className="h-5 w-5" />
                  <span>缓存状态</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {cacheStats ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">命中率</span>
                      <span className="font-medium">{(cacheStats.hitRate * 100).toFixed(1)}%</span>
                    </div>
                    <Progress value={cacheStats.hitRate * 100} className="h-2" />
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">缓存大小</span>
                        <div className="font-medium">{cacheStats.size}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">命中次数</span>
                        <div className="font-medium">{cacheStats.hitCount}</div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500">加载中...</div>
                )}
              </CardContent>
            </Card>

            {/* Performance Overview */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <IconClock className="h-5 w-5" />
                  <span>响应时间</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {performanceStats && Object.keys(performanceStats).length > 0 ? (
                  <div className="space-y-3">
                    {Object.entries(performanceStats).slice(0, 3).map(([operation, stats]: [string, any]) => (
                      <div key={operation} className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 truncate">{operation}</span>
                        <span className="font-medium">{formatDuration(stats.average)}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">暂无性能数据</div>
                )}
              </CardContent>
            </Card>

            {/* Memory Overview */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <IconActivity className="h-5 w-5" />
                  <span>内存使用</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {memoryStats ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">当前使用</span>
                      <span className="font-medium">{formatBytes(memoryStats.current)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">平均使用</span>
                      <span className="font-medium">{formatBytes(memoryStats.average)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">峰值使用</span>
                      <span className="font-medium">{formatBytes(memoryStats.peak)}</span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-gray-500">内存数据不可用</div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Cache Tab */}
        <TabsContent value="cache">
          <Card>
            <CardHeader>
              <CardTitle>缓存详细信息</CardTitle>
              <CardDescription>
                缓存系统的详细统计信息和配置
              </CardDescription>
            </CardHeader>
            <CardContent>
              {cacheStats ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{cacheStats.size}</div>
                      <div className="text-sm text-blue-600">缓存条目</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{cacheStats.hitCount}</div>
                      <div className="text-sm text-green-600">命中次数</div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{cacheStats.missCount}</div>
                      <div className="text-sm text-red-600">未命中次数</div>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">
                        {(cacheStats.hitRate * 100).toFixed(1)}%
                      </div>
                      <div className="text-sm text-purple-600">命中率</div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-3">缓存性能</h3>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span>命中率</span>
                        <div className="flex items-center space-x-2">
                          <Progress value={cacheStats.hitRate * 100} className="w-32 h-2" />
                          <span className="text-sm font-medium">{(cacheStats.hitRate * 100).toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      variant="outline"
                      onClick={() => {
                        billingCache.clear();
                        updatePerformanceData();
                      }}
                    >
                      清空缓存
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        Memoizer.clear();
                        updatePerformanceData();
                      }}
                    >
                      清空记忆化缓存
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  缓存数据加载中...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance">
          <Card>
            <CardHeader>
              <CardTitle>性能指标</CardTitle>
              <CardDescription>
                各项操作的详细性能统计
              </CardDescription>
            </CardHeader>
            <CardContent>
              {performanceStats && Object.keys(performanceStats).length > 0 ? (
                <div className="space-y-4">
                  {Object.entries(performanceStats).map(([operation, stats]: [string, any]) => (
                    <div key={operation} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium">{operation}</h3>
                        <Badge variant="outline">{stats.count} 次调用</Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">平均时间</span>
                          <div className="font-medium">{formatDuration(stats.average)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">最小时间</span>
                          <div className="font-medium">{formatDuration(stats.min)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">最大时间</span>
                          <div className="font-medium">{formatDuration(stats.max)}</div>
                        </div>
                        <div>
                          <span className="text-gray-600">P95</span>
                          <div className="font-medium">{formatDuration(stats.p95)}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  暂无性能数据
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Memory Tab */}
        <TabsContent value="memory">
          <Card>
            <CardHeader>
              <CardTitle>内存使用情况</CardTitle>
              <CardDescription>
                JavaScript 堆内存使用统计
              </CardDescription>
            </CardHeader>
            <CardContent>
              {memoryStats ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {formatBytes(memoryStats.current)}
                      </div>
                      <div className="text-sm text-blue-600">当前使用</div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">
                        {formatBytes(memoryStats.average)}
                      </div>
                      <div className="text-sm text-green-600">平均使用</div>
                    </div>
                    <div className="bg-red-50 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">
                        {formatBytes(memoryStats.peak)}
                      </div>
                      <div className="text-sm text-red-600">峰值使用</div>
                    </div>
                  </div>

                  {memoryStats.current > 50 * 1024 * 1024 && (
                    <Alert className="border-yellow-200 bg-yellow-50">
                      <IconAlertTriangle className="h-4 w-4 text-yellow-600" />
                      <AlertDescription className="text-yellow-800">
                        内存使用较高，建议清理缓存或刷新页面
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  内存监控在此浏览器中不可用
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
