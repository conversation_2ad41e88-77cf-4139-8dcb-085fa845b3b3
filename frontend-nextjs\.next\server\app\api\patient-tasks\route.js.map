{"version": 3, "file": "../app/api/patient-tasks/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,iDCAA,sDCAA,6FCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,wZCIO,IAAMA,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAAA,GACpE,EADoEA,CAChE,CACF,CAFkEA,GAE5DC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBH,CAAAA,GACpCI,CADoCJ,CAAAA,CAC9B,GAAIK,GAAIJ,CAAAA,EAAQG,GAAG,EAGzBE,EAAQC,QAASH,CAAAA,EAAII,CAAJJ,WAAgB,CAACK,GAAG,CAAC,OAAY,SAClDC,EAAOH,QAASH,CAAAA,EAAII,CAAJJ,WAAgB,CAACK,GAAG,CAAC,MAAW,QAChDE,EAAYP,EAAII,CAAJJ,IAAAA,OAAgB,CAACK,GAAG,CAAC,aACjCG,EAAWR,EAAII,CAAJJ,GAAAA,QAAgB,CAACK,GAAG,CAAC,YAChCI,EAAST,EAAII,CAAJJ,CAAAA,UAAgB,CAACK,GAAG,CAAC,UAC9BK,EAAWV,EAAII,CAAJJ,GAAAA,QAAgB,CAACK,GAAG,CAAC,YAChCM,EAAaX,EAAII,CAAJJ,KAAAA,MAAgB,CAACK,GAAG,CAAC,cAClCO,EAAYZ,EAAII,CAAJJ,IAAAA,OAAgB,CAACK,GAAG,CAAC,aACjCQ,EAAUb,EAAII,CAAJJ,EAAAA,SAAgB,CAACK,GAAG,CAAC,WAC/BS,EAASd,EAAII,CAAJJ,CAAAA,UAAgB,CAACK,GAAG,CAAC,UAGhCU,EAAmB,EAAC,CAEpBR,IACFQ,EAAYC,GADC,IACM,CAAG,CAAEC,MAAQV,CAAAA,CAAU,GAGxCC,IACFO,EAAYP,EADA,MACQ,CAApBO,CAAyBE,MAAQT,CAAAA,CAAS,GAGxCC,IACFM,EADU,MACQ,CAAG,CAAEE,CAAvBF,KAA+BN,CAAAA,CAAO,GAGpCC,IACFK,EAAYL,EADA,MACQ,CAAG,CAAEO,MAAQP,CAAAA,CAAS,GAGxCC,IACFI,EAAYJ,IADE,KACdI,CAAsB,CAAG,CAAEE,MAAQN,CAAAA,CAAW,GAG5CC,GACFG,GAAYH,GADC,KACbG,CAAqB,CAAG,CAAEE,MAAQL,CAAAA,CAAU,GAG9B,MAAQ,GAApBC,IACFE,EAAYG,GAAG,CAAG,CAChBH,EACA,CACEI,CAHJJ,MAGa,CAFXA,CAGIK,SAAAA,CAAW,IAAIC,IAAAA,CAEnB,EACA,CACEZ,MAAQ,EACNa,UAAY,YACd,CACF,EACD,EAGCR,IACFC,EADU,EACI,CAAG,CACf,CACEQ,IAFJR,CAEW,EACLS,QAAUV,CAAAA,CACZ,CACF,EACA,CACEW,WAAa,EACXD,QAAUV,CAAAA,CACZ,CACF,EACD,EAIClB,IAAAA,IAAwB,KAAnB8B,IAAI,CACXX,EAAYG,GAAG,CAAG,CAChBH,EACA,CACEY,CAHJZ,CAGQ,EACF,CACEJ,GAJNI,OAIkB,EACVE,MAAAA,CAAQrB,EAAKgC,aACf,CACF,EACA,CACEhB,SAAW,EACTK,MAAAA,CAAQrB,EAAKgC,aAAAA,CAEjB,EACA,CACEpB,QAAU,EACRqB,EAAI,EAAC,qBAAsB,wBAAyB,yBAAyB,CAEjF,EAEJ,EACD,CACsB,YAAc,GAA5BjC,EAAK8B,EAAL9B,EAAS,GAClBmB,EAAYG,GAAG,CAAG,CAChBH,EACA,CACEY,CAHJZ,CAGQ,EACF,CACEJ,GAJNI,OAIkB,EACVE,MAAAA,CAAQrB,EAAKgC,aAAAA,CAEjB,EACA,CACEpB,QAAU,EACRqB,EAAI,EAAC,iBAAkB,yBAA0B,oBAAoB,CAEzE,EACD,EAEJ,EAGH,IAAMC,EAAO,MAAMhC,EAAciC,WAAAA,IAAe,CAAC,OAC/C7B,KAAAA,EACAI,EACA0B,EADA1B,GACOS,CAAAA,EACPkB,IAAM,KADClB,MAET,GAEA,MAAOmB,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBJ,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBK,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,+BAAiCA,CAAAA,GACxCE,CAAAA,CADwCF,CACxCE,EAAAA,EAAAA,CAAoB,iCAC7B,CACF,CAAG,EAEUC,EAAO3C,CAAAA,EAAAA,CAAP2C,CAAO3C,EAAAA,CAAmB,OAAOC,EAAyBC,EAAAA,GACrE,EADqEA,CACjE,CACF,CAFmEA,GAE7DC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBH,CAAAA,GACpC2C,CADoC3C,CAAAA,MACpC2C,EAAyBC,IAAI,CAAZ3C,EAWvB,GARA0C,EAAS3B,MAAAA,GAAS,CAAGhB,EAAKgC,EAALhC,WAAkB,CAGnC,EAAUe,MAAAA,IAAU,EAAE,GACfA,KAAAA,KAAU,CAAGf,EAAKgC,EAALhC,WAAKgC,EAIzB,CAACW,EAASvB,MAATuB,CAAgB,EAAI,CAACA,EAAShB,KAAK,CAALA,CAAS,CAACgB,EAAS/B,QAAQ,EAAI,CAAC+B,EAASpB,MAAToB,CAAgB,CACjF,CADmF,KAC5EF,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,4DAA8D,MAI3F,GAAkB,YAAc,GAA5BzC,EAAK8B,EAAL9B,EAAS,EAEP,CADiB,CAAC,WACJ6C,MADsB,yBAA0B,oBAAoB,CACpEA,QAAQ,CAACF,EAAS/B,MAAT+B,EAAiB,CAAG,CAC7C,MAAOF,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,qGAAuG,MAItI,IAAMP,EAAO,MAAMhC,EAAc4C,WAAAA,MAAiB,CAACH,GAEnD,KAFmDA,CAAAA,CAE5CL,EAAAA,EAAAA,EAAAA,CAAsBJ,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBK,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,8BAAgCA,CAAAA,GACvCE,CAAAA,CADuCF,CAAAA,EACvCE,EAAAA,CAAoB,iCAC7B,CACF,CAAG,ECjKG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,oBAAoB,SACxC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CACF,CAAC,CAIC,IAAC,EAAM,CAAH,CAAeM,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,gCACA,8BACA,iBACA,wCACA,CAAK,CACL,kHACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patient-tasks/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/?551b", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessR<PERSON>ponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\n\nexport const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const url = new URL(request.url);\n    \n    // Extract query parameters\n    const limit = parseInt(url.searchParams.get('limit') || '10');\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const patientId = url.searchParams.get('patientId');\n    const taskType = url.searchParams.get('taskType');\n    const status = url.searchParams.get('status');\n    const priority = url.searchParams.get('priority');\n    const assignedTo = url.searchParams.get('assignedTo');\n    const createdBy = url.searchParams.get('createdBy');\n    const overdue = url.searchParams.get('overdue');\n    const search = url.searchParams.get('search');\n    \n    // Build where clause\n    let whereClause: any = {};\n    \n    if (patientId) {\n      whereClause.patient = { equals: patientId };\n    }\n    \n    if (taskType) {\n      whereClause.taskType = { equals: taskType };\n    }\n    \n    if (status) {\n      whereClause.status = { equals: status };\n    }\n    \n    if (priority) {\n      whereClause.priority = { equals: priority };\n    }\n    \n    if (assignedTo) {\n      whereClause.assignedTo = { equals: assignedTo };\n    }\n    \n    if (createdBy) {\n      whereClause.createdBy = { equals: createdBy };\n    }\n    \n    if (overdue === 'true') {\n      whereClause.and = [\n        whereClause,\n        {\n          dueDate: {\n            less_than: new Date(),\n          },\n        },\n        {\n          status: {\n            not_equals: 'completed',\n          },\n        },\n      ];\n    }\n    \n    if (search) {\n      whereClause.or = [\n        {\n          title: {\n            contains: search,\n          },\n        },\n        {\n          description: {\n            contains: search,\n          },\n        },\n      ];\n    }\n    \n    // Apply role-based filtering\n    if (user.role === 'doctor') {\n      whereClause.and = [\n        whereClause,\n        {\n          or: [\n            {\n              assignedTo: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              createdBy: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              taskType: {\n                in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],\n              },\n            },\n          ],\n        },\n      ];\n    } else if (user.role === 'front-desk') {\n      whereClause.and = [\n        whereClause,\n        {\n          or: [\n            {\n              assignedTo: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              taskType: {\n                in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],\n              },\n            },\n          ],\n        },\n      ];\n    }\n    \n    const data = await payloadClient.getPatientTasks({\n      limit,\n      page,\n      where: whereClause,\n      sort: '-dueDate', // Sort by due date descending\n    });\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error fetching patient tasks:', error);\n    return createErrorResponse('Failed to fetch patient tasks');\n  }\n});\n\nexport const POST = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const taskData = await request.json();\n    \n    // Set the creator to the current user\n    taskData.createdBy = user.payloadUserId;\n\n    // If no assignedTo is specified, assign to current user\n    if (!taskData.assignedTo) {\n      taskData.assignedTo = user.payloadUserId;\n    }\n    \n    // Validate required fields\n    if (!taskData.patient || !taskData.title || !taskData.taskType || !taskData.dueDate) {\n      return createErrorResponse('Missing required fields: patient, title, taskType, dueDate', 400);\n    }\n    \n    // Validate task type based on user role\n    if (user.role === 'front-desk') {\n      const allowedTypes = ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'];\n      if (!allowedTypes.includes(taskData.taskType)) {\n        return createErrorResponse('Front-desk staff can only create follow-up-call, appointment-scheduling, or billing-follow-up tasks', 403);\n      }\n    }\n    \n    const data = await payloadClient.createPatientTask(taskData);\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error creating patient task:', error);\n    return createErrorResponse('Failed to create patient task');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patient-tasks',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-tasks\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patient-tasks/route\",\n        pathname: \"/api/patient-tasks\",\n        filename: \"route\",\n        bundlePath: \"app/api/patient-tasks/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-tasks\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "payloadClient", "createPayloadClient", "url", "URL", "limit", "parseInt", "searchParams", "get", "page", "patientId", "taskType", "status", "priority", "assignedTo", "created<PERSON>y", "overdue", "search", "<PERSON><PERSON><PERSON><PERSON>", "patient", "equals", "and", "dueDate", "less_than", "Date", "not_equals", "title", "contains", "description", "role", "or", "payloadUserId", "in", "data", "getPatientTasks", "where", "sort", "createSuccessResponse", "error", "console", "createErrorResponse", "POST", "taskData", "json", "includes", "createPatientTask", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}