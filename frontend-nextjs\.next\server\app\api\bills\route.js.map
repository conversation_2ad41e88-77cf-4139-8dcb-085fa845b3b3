{"version": 3, "file": "../app/api/bills/route.js", "mappings": "ubAAA,yHEAA,oDCAA,qGCAA,mECAA,0GCAA,qDCAA,+CCAA,mDCAA,gDCAA,uGCAA,iECAA,kDCAA,iECAA,uDCAA,uDCAA,qDCAA,yDCAA,sGCAA,kEEAA,sECAA,mDCAA,mECAA,yDCAA,qYCMA,IAAMA,EAAcC,SAAAA,cAA+B,IAAI,GAGjDC,QAAiBC,EAAAA,KAAAA,CAAAA,WAEnBC,CAAAA,CAAe,CACRC,EAAiB,GAAG,CACpBC,CAAa,CACpB,CAAoB,CACpB,CACA,KAAK,CAACF,GAJCC,IAAAA,CAAAA,MAAAA,CAAAA,EACAC,IAAAA,CAAAA,IAAAA,CAAAA,EAAAA,IAAAA,CACAC,OAAAA,CAAAA,EAGP,IAAI,CAHGA,IAGE,CAAG,UACd,CACF,CAGA,eAAeC,EAAiBC,CAAc,EAC5C,GAAI,CACF,IAAMC,EAAW,MAAMC,KAAAA,CAAM,CAAC,+BAA+B,EAAEF,EAAAA,CAAQ,CAAE,CACvEG,OAAS,EACPC,aAAAA,CAAe,CAAC,OAAO,EAAEZ,QAAQa,GAAG,CAACC,gBAAgB,CAAE,EAE3D,GAEA,GAAI,CAACL,EAASM,EAAE,CACd,CADgB,EAAJA,GACN,IAAId,EAAS,mCAAoC,GAAK,2BAG9D,OAAO,MAAMQ,EAASO,IAAI,EAC5B,CAAE,MAAOC,EAAO,CAEd,EAFc,IACdC,OAAQD,CAAAA,KAAK,CAAC,4BAA8BA,CAAAA,GACtC,EADsCA,CAAAA,CAClChB,EAAS,qCAAsC,GAAK,sBAChE,CACF,CAGA,eAAekB,EAAmBC,CAAW,CAAEC,CAAoB,CAAEb,CAAc,CAAEc,CAAiB,EACpG,GAAI,CACF,IAAMb,EAAW,MAAXA,KAAiBC,CAAMU,EAAK,IAC7BC,CAAO,CACVV,OAAS,EACP,cAAgB,oBAChB,iBAAmBH,CAAAA,EACnB,IADmBA,UACHc,CAAAA,EAChB,GAAGD,EAAQV,EADKW,KACLX,CAEf,GAEMY,EAAO,EAAPA,IAAad,EAASO,IAAI,EAAbP,CAEnB,GAAI,CAACA,EAASM,EAAE,CACd,CADgB,EAAJA,GACN,IAAId,EACRsB,EAAKN,EAALM,EAAAA,CAAU,EAAI,CAAC,wBAAwB,EAAEd,EAASL,MAATK,CAAe,CAAE,CAC1DA,EAASL,MAAAA,CACTmB,EAAKlB,EAAAA,EAAI,EAAI,eACbkB,CAAAA,GAIJ,CAJIA,CAAAA,KAIGA,CACT,CAAE,EADOA,IACAN,EAAO,CACd,EADc,CACVA,aAAiBhB,EACnB,MAD6B,CAK/B,IAJQgB,GAGRC,OAAQD,CAAAA,KAAK,CAAC,wBAA0BA,CAAAA,GAClC,EADkCA,CAAAA,CAC9BhB,EAAS,8BAA+B,GAAK,yBACzD,CACF,CAKO,eAAeuB,EAAIC,CAAoB,EAAxBD,GAChB,CACF,GAAM,QAAEhB,CAAM,CAAE,CAAG,MAAMkB,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAAClB,EAYH,IAZW,GACXmB,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/B,WACA,aACA,yBACA,OACA,EAAEC,QAAU,aAAa,EACzB,GACA,yBACAJ,CAAAA,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CACtB,CACEC,KAAO,2BACPZ,IAAM,iBACNF,OAAS,eAEX,EAAEC,MAAQ,IAAI,GAKlB,IAAM2B,EAAkBC,EAAAA,EAAYC,CAAAA,QAA9BF,MAA4C,CAACvB,GACnD,GADmDA,CAAAA,EAC9B0B,OAAO,CAY1B,CAZ4B,IAATA,EACnBP,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/BpB,EACA,UACA,yBACA,OACA,EAAEqB,QAAU,cAAcM,SAAAA,CAAWJ,EAAgBI,SAAAA,GACrD,EACA,qBACAV,CAAAA,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CACtB,CACEC,KAAO,uBACPZ,IAAM,uBACNF,OAAS,gBACTgC,SAAAA,CAAWJ,EAAgBI,SAAAA,CAE7B,EAAE/B,MAAQ,IAAI,GAKlB,IAAMgB,EAAM,IAAIgB,GAAIX,CAAAA,EAAQL,GAAG,EAAXK,EACAY,MAAOC,CAAAA,WAAW,CAAClB,EAAImB,CAAAA,WAAY,CAACC,OAAO,IAG/D,GAAIC,EAAYC,KAAK,GAAKC,CAAVD,KAAgBE,MAAOH,CAAAA,EAAYC,KAAK,IAAjBD,MAA8BA,CAAAA,EAAYC,KAAK,EAAI,EAArBD,CAAqB,CAAE,CAAI,OACvFX,EAAAA,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAO,2BACPZ,IAAM,iBACNF,OAAS,qBAEX,EAAEC,MAAQ,IAAI,GAIlB,GAAIqC,EAAYI,IAAI,GAAKF,EAATE,IAAeD,MAAOH,CAAAA,EAAYI,IAAI,IAAiC,CAAjDJ,CAAqD,MAAxBA,CAAAA,EAAYI,IAAI,GACjF,EADiEJ,KAC1DX,EAAAA,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAO,0BACPZ,IAAM,gBACNF,OAAS,eAEX,EAAEC,MAAQ,IAAI,GAKlB,IAAM0C,EAAO,EAAPA,IAAavC,EAAiBC,GAC9Bc,EAAYwB,CADkBtC,CACbuC,EAAAA,KADavC,QACE,CAAC,EAAE,EAAEwC,aAAiB,KAE5D,GAAI,CAAC1B,EACH,OADc,EACPQ,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAO,wBACPZ,IAAM,sBACNF,OAAS,mBAEX,EAAEC,MAAQ,IAAI,GAKlB,IAAM6C,EAAa,CAAGlD,EAAAA,EAAY,SAAZA,CAAsB,EAAEqB,EAAI8B,CAAJ9B,KAAU,CAAE,EACpDG,EAAO,EAAPA,IAAaJ,EAAmB8B,EAAY,CAAEE,MAAQ,CAAV,KAAgB,CAA/ChC,CAAkDX,EAAQc,GAkB7E,CAlB6EA,KAAAA,CAG7EK,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/BpB,EACAc,EACA,EADAA,KAAAA,KAEA,OACA,EACEO,QAAU,cACVY,WAAAA,CAAaJ,OAAOC,WAAW,CAAClB,EAAImB,CAAAA,WAAY,CAACC,OAAO,IACxDY,WAAa7B,CAAAA,EAAK8B,EAAL9B,EAAS,EAAE+B,MAAU,GACpC,GACA,OACAC,EACA9B,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CAACO,EAC3B,CAAE,CADyBA,CAAAA,IAClBN,EAAO,CACd,EADc,CACVA,aAAiBhB,EACnB,MAD6B,CACtB6B,EAAAA,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAAA,CAAOA,EAAMd,OAAO,CACpBE,IAAAA,CAAMY,EAAMZ,IAAI,CAChBF,OAAAA,CAASc,EAAMd,OAAAA,CAEjB,EAAEC,MAAAA,CAAQa,EAAMb,MAAAA,GAKpB,OADAc,OAAQD,CAAAA,KAAK,CAAC,qCAAuCA,CAAAA,GAC9Ca,EAD8Cb,CAAAA,WAC9Ca,CAAad,IAAI,CACtB,CACEC,KAAO,yBACPZ,IAAM,kBACNF,OAAS,iBAEX,EAAEC,MAAQ,IAAI,EAElB,CACF,CAKO,eAAeoD,EAAK/B,CAAoB,EAC7C,CADoB+B,EAChB,CACF,IA6BIC,EA7BE,EA6BFA,MA7BIjD,CAAM,CAAE,CAAG,MAAMkB,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,CAEzB,GAAI,CAAClB,EACH,IADW,GACJsB,EAAAA,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAO,2BACPZ,IAAM,iBACNF,OAAS,aAEX,EAAEC,MAAQ,IAAI,GAKlB,IAAM0C,EAAO,EAAPA,IAAavC,EAAiBC,GAC9Bc,EAAYwB,CADkBtC,CAAAA,EACbuC,KADavC,QACE,CAAC,EAAE,EAAEwC,aAAiB,KAE5D,GAAI,CAAC1B,EACH,OAAOQ,EAAAA,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAO,wBACPZ,IAAM,sBACNF,OAAS,mBAEX,EAAEC,MAAQ,IAAI,GAMlB,GAAI,CACFqD,EAAO,MAAMhC,EAAQT,IAAI,EAC3B,CAAE,MAAOC,EAAO,CAYd,EAZc,KACdU,EAAAA,EAAAA,CAAYC,qBAAqB,CAC/BpB,EACAc,EACA,EADAA,KAAAA,yBAEA,OACA,QAAEL,CAAM,GACR,EACA,mCACAQ,CAAAA,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CACtB,CACEC,KAAO,gCACPZ,IAAM,gBACNF,OAAS,YAEX,EAAEC,MAAQ,IAAI,EAElB,CAGA,GAAI,CACEqD,EAAKC,EAALD,SAAgB,EAAE,CACpBA,EAAKC,EAALD,SAAgB,CAAGE,EAAAA,EAAAA,CAAeC,YAAY,CAACH,EAAKC,EAALD,SAAgB,CAAE,MAE/DA,EAAKI,EAALJ,GAAU,EAAE,CACdA,EAAKI,EAALJ,GAAU,CAAGE,EAAAA,EAAAA,CAAeC,YAAY,CAACH,EAAKI,EAALJ,GAAU,CAAE,MAEnDA,EAAKK,EAALL,GAAU,EAAE,CACdA,EAAKK,EAAAA,GAAK,CAAGL,EAAKK,EAAAA,GAAK,CAACC,GAAG,CAAC,IAAgB,CAC1C,GAAGC,CAAI,CACPC,QAAAA,CAAUN,EAAAA,EAAeC,CAAAA,YAAY,CAACI,EAAKC,EAALD,MAAa,CAAE,KACrDE,QAAAA,CAAUP,EAAAA,EAAeQ,CAAAA,cAAc,CAACH,EAAKE,EAALF,MAAa,EACrDI,SAAAA,CAAWT,EAAAA,EAAeQ,CAAAA,cAAc,CAACH,EAAKI,EAALJ,OAAc,EACzD,GAEJ,CAAE,MAAOK,EAAmB,CAY1B,OAXA1C,EAAAA,EAAAA,CAAYC,EADc,mBACO,CAC/BpB,EACAc,EACA,EADAA,KAAAA,2BAEA,OACA,EAAEL,KAAOoD,CAAAA,CAAkB,GAC3B,EACAA,aAA6BnE,IAAAA,CAAAA,CAAQmE,EAAkBlE,OAAO,CAAG,OAAVA,cACvDsB,CAAAA,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CACtB,CACEC,KAAO,6BACPZ,IAAM,sBACNF,OAAS,aAEX,EAAEC,MAAQ,IAAI,EAElB,CAGA,IAAMkE,EAAmBC,EAAAA,EAAeC,CAAAA,SAAlCF,CAA4Cb,GAClD,CADkDA,CAAAA,CAC9C,CAACa,EAAiBG,OAAO,CAAE,CAC7B,IAAMC,CADcD,CACIE,CAAAA,EAAAA,EAAAA,EAAAA,CAAuBL,CAAAA,EAAiBrD,EAA1DyD,GAA+D,EAarE,OAb+CJ,EAE/C3C,EAAAA,CAAYC,qBAAqB,CAC/BpB,EACAc,EACA,EADAA,KAAAA,yBAEA,OACA,EAAEsD,gBAAkBF,CAAAA,CAAgB,EACpC,GACA,mBACAjD,CAAAA,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CACtB,CACEC,KAAO,qBACPZ,IAAM,oBACNF,OAAS,YACTG,OAASoE,CAAAA,EAEX,EAAEtE,MAAQ,IAAI,EAElB,CAKA,IAAMyE,EAAgBP,EAAiB/C,IAAI,CAGrCuD,EAAaD,EAHbA,KAGgC,CAACE,KAANjB,CAAY,CAAC,CAACkB,EAAKhB,CAAAA,GAAAA,CAClD,EADkDA,EAC5CiB,EAAYjB,EAAKE,EAALF,GAAAA,GAAa,CAAGA,EAAKI,SAAS,CAC1Cc,EAAeD,EAAa,IAAME,GAAN,CAA5BD,QAA8C,EAAI,GAAK,IAAE,CAC/D,OAAOF,GAAAA,EAAmBE,CAAAA,CAAW,CACpC,IADaD,EAGWG,IAAAA,CAAKC,KAAK,CAAc,GAAO,CAApBP,CAAXM,EAA+B,IACpDE,EAAiBT,EAAcS,UAA/BA,CAA+BA,GAAc,EAAI,EACjDC,EAAYV,EAAcU,KAA1BA,IAAmC,EAATA,EAC1BC,EAAwBJ,IAAKC,CAAAA,KAAK,CAAEI,CAAAA,EAAqBF,EAAYD,CAAAA,CAAa,CAAlFE,GAA8F,CAArCD,CAAqC,IAG9FtC,EAAa,GAAGlD,EAAY,GAAf,MAAe,CAAU,CAAC,CACvC2F,EAAW,CACf,GAAGb,CAAa,CADD,QAELY,CAAAA,EACVE,WAAaH,CAAAA,CACf,EACMjE,CAHMkE,CAGC,EAAPlE,IAAaJ,EACjB8B,EACA,CACEE,MAAQ,OAHOhC,CAIfsC,IAAMmC,CAAAA,IAAAA,CAAKC,SAAS,CAACH,EACvB,EACAlF,EACAc,EAHuBoE,CAyBzB,CAtBEpE,KAAAA,CAAAA,EAIFK,EAAAA,CAAYC,qBAAqB,CAC/BpB,EACAc,EACA,EADAA,KAAAA,OAEA,OACA,EACEwE,MAAAA,CAAQvE,EAAKwE,EAAE,CACfC,SAAAA,CAAWnB,EAAcoB,OAAO,CAChCC,QAAAA,CAAUrB,EAAcqB,QAAQ,CAChCC,QAAUV,CAAAA,EACVE,WAAaH,CAAAA,EACbY,EAFUX,OAECZ,CAAAA,EAAcf,KAAK,EAAER,IAArBuB,EAA+B,GAC5C,GACA,OACAtB,EACA9B,GAGKK,EAAAA,EAHLL,CAAAA,SAGKK,CAAad,IAAI,CAACO,EAAM,CAAEnB,CAAF,KAAU,IAAI,EAC/C,CAAE,MAAOa,EAAO,CACd,EADc,CACVA,aAAiBhB,EACnB,MAD6B,CACtB6B,EAAAA,YAAAA,CAAad,IAAI,CACtB,CACEC,KAAAA,CAAOA,EAAMd,OAAO,CACpBE,IAAAA,CAAMY,EAAMZ,IAAI,CAChBF,OAAAA,CAASc,EAAMd,OAAO,CACtBG,OAAAA,CAASW,EAAMX,OAAAA,CAEjB,EAAEF,MAAAA,CAAQa,EAAMb,MAAAA,GAKpB,OADAc,OAAQD,CAAAA,KAAK,CAAC,sCAAwCA,CAAAA,GAC/Ca,EAD+Cb,CAAAA,WAC/Ca,CAAad,IAAI,CACtB,CACEC,KAAO,yBACPZ,IAAM,kBACNF,OAAS,sBAEX,EAAEC,MAAQ,IAAI,EAElB,CACF,CCzZA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,YAAY,SAChC,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAeiG,EAA4B,GAAH,EAAQ,EAEnD,EAAO,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,wBACA,sBACA,iBACA,gCACA,CAAK,CACL,0GACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,yBC5BA,8GCAA,qDCAA,4DCAA,wDCAA,iECAA,uDCAA,sDCAA,yDCAA,iDCAA,2DCAA,2DCAA,iDCAA,yDCAA,4DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/?e6b9", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/src/app/api/bills/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/?019d", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", null, "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", null, "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\nimport { billFormSchema, billFilterSchema } from '@/lib/validation/billing-schemas';\nimport { formatValidationErrors } from '@/lib/validation/billing-schemas';\nimport { auditLogger, rateLimiter, InputSanitizer } from '@/lib/billing-security';\n\nconst BACKEND_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';\n\n// Enhanced error handling utility\nclass APIError extends Error {\n  constructor(\n    message: string,\n    public status: number = 500,\n    public code?: string,\n    public details?: any\n  ) {\n    super(message);\n    this.name = 'APIError';\n  }\n}\n\n// Utility to get user info from Clerk with error handling\nasync function getClerkUserInfo(userId: string) {\n  try {\n    const response = await fetch(`https://api.clerk.com/v1/users/${userId}`, {\n      headers: {\n        Authorization: `Bearer ${process.env.CLERK_SECRET_KEY}`,\n      },\n    });\n\n    if (!response.ok) {\n      throw new APIError('Failed to fetch user information', 401, 'CLERK_USER_FETCH_ERROR');\n    }\n\n    return await response.json();\n  } catch (error) {\n    console.error('Error fetching Clerk user:', error);\n    throw new APIError('Authentication service unavailable', 503, 'AUTH_SERVICE_ERROR');\n  }\n}\n\n// Utility to make backend requests with comprehensive error handling\nasync function makeBackendRequest(url: string, options: RequestInit, userId: string, userEmail: string) {\n  try {\n    const response = await fetch(url, {\n      ...options,\n      headers: {\n        'Content-Type': 'application/json',\n        'x-clerk-user-id': userId,\n        'x-user-email': userEmail,\n        ...options.headers,\n      },\n    });\n\n    const data = await response.json();\n\n    if (!response.ok) {\n      throw new APIError(\n        data.error || `Backend request failed: ${response.status}`,\n        response.status,\n        data.code || 'BACKEND_ERROR',\n        data\n      );\n    }\n\n    return data;\n  } catch (error) {\n    if (error instanceof APIError) {\n      throw error;\n    }\n\n    console.error('Backend request error:', error);\n    throw new APIError('Backend service unavailable', 503, 'BACKEND_SERVICE_ERROR');\n  }\n}\n\n/**\n * GET /api/bills - Proxy to backend bills API with enhanced validation and error handling\n */\nexport async function GET(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      auditLogger.logFinancialOperation(\n        'anonymous',\n        'anonymous',\n        'GET_BILLS_UNAUTHORIZED',\n        'bills',\n        { endpoint: '/api/bills' },\n        false,\n        'Authentication required',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Authentication required',\n          code: 'AUTH_REQUIRED',\n          message: '请先登录以访问账单数据'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Check rate limiting\n    const rateLimitResult = rateLimiter.checkRateLimit(userId);\n    if (!rateLimitResult.allowed) {\n      auditLogger.logFinancialOperation(\n        userId,\n        'unknown',\n        'GET_BILLS_RATE_LIMITED',\n        'bills',\n        { endpoint: '/api/bills', resetTime: rateLimitResult.resetTime },\n        false,\n        'Rate limit exceeded',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Rate limit exceeded',\n          code: 'RATE_LIMIT_EXCEEDED',\n          message: '请求过于频繁，请稍后重试',\n          resetTime: rateLimitResult.resetTime\n        },\n        { status: 429 }\n      );\n    }\n\n    // Validate query parameters\n    const url = new URL(request.url);\n    const queryParams = Object.fromEntries(url.searchParams.entries());\n\n    // Basic query parameter validation\n    if (queryParams.limit && (isNaN(Number(queryParams.limit)) || Number(queryParams.limit) > 100)) {\n      return NextResponse.json(\n        {\n          error: 'Invalid limit parameter',\n          code: 'INVALID_LIMIT',\n          message: '分页限制必须是1-100之间的数字'\n        },\n        { status: 400 }\n      );\n    }\n\n    if (queryParams.page && (isNaN(Number(queryParams.page)) || Number(queryParams.page) < 1)) {\n      return NextResponse.json(\n        {\n          error: 'Invalid page parameter',\n          code: 'INVALID_PAGE',\n          message: '页码必须是大于0的数字'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Get user info from Clerk with error handling\n    const user = await getClerkUserInfo(userId);\n    const userEmail = user.email_addresses[0]?.email_address || '';\n\n    if (!userEmail) {\n      return NextResponse.json(\n        {\n          error: 'User email not found',\n          code: 'USER_EMAIL_MISSING',\n          message: '用户邮箱信息缺失，请联系管理员'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bills${url.search}`;\n    const data = await makeBackendRequest(backendUrl, { method: 'GET' }, userId, userEmail);\n\n    // Log successful operation\n    auditLogger.logFinancialOperation(\n      userId,\n      userEmail,\n      'GET_BILLS',\n      'bills',\n      {\n        endpoint: '/api/bills',\n        queryParams: Object.fromEntries(url.searchParams.entries()),\n        resultCount: data.docs?.length || 0\n      },\n      true,\n      undefined,\n      request\n    );\n\n    return NextResponse.json(data);\n  } catch (error) {\n    if (error instanceof APIError) {\n      return NextResponse.json(\n        {\n          error: error.message,\n          code: error.code,\n          message: error.message\n        },\n        { status: error.status }\n      );\n    }\n\n    console.error('Unexpected error in GET /api/bills:', error);\n    return NextResponse.json(\n      {\n        error: 'Internal server error',\n        code: 'INTERNAL_ERROR',\n        message: '服务器内部错误，请稍后重试'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n/**\n * POST /api/bills - Create new bill with comprehensive validation and error handling\n */\nexport async function POST(request: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      return NextResponse.json(\n        {\n          error: 'Authentication required',\n          code: 'AUTH_REQUIRED',\n          message: '请先登录以创建账单'\n        },\n        { status: 401 }\n      );\n    }\n\n    // Get user info from Clerk with error handling early\n    const user = await getClerkUserInfo(userId);\n    const userEmail = user.email_addresses[0]?.email_address || '';\n\n    if (!userEmail) {\n      return NextResponse.json(\n        {\n          error: 'User email not found',\n          code: 'USER_EMAIL_MISSING',\n          message: '用户邮箱信息缺失，请联系管理员'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Parse and validate request body\n    let body;\n    try {\n      body = await request.json();\n    } catch (error) {\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_BILL_JSON_PARSE_FAILED',\n        'bills',\n        { error },\n        false,\n        'Failed to parse JSON request body',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Invalid JSON in request body',\n          code: 'INVALID_JSON',\n          message: '请求数据格式错误'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Sanitize input data\n    try {\n      if (body.description) {\n        body.description = InputSanitizer.sanitizeText(body.description, 500);\n      }\n      if (body.notes) {\n        body.notes = InputSanitizer.sanitizeText(body.notes, 1000);\n      }\n      if (body.items) {\n        body.items = body.items.map((item: any) => ({\n          ...item,\n          itemName: InputSanitizer.sanitizeText(item.itemName, 200),\n          quantity: InputSanitizer.sanitizeAmount(item.quantity),\n          unitPrice: InputSanitizer.sanitizeAmount(item.unitPrice),\n        }));\n      }\n    } catch (sanitizationError) {\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_BILL_SANITIZATION_FAILED',\n        'bills',\n        { error: sanitizationError },\n        false,\n        sanitizationError instanceof Error ? sanitizationError.message : 'Sanitization failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Input sanitization failed',\n          code: 'SANITIZATION_ERROR',\n          message: '输入数据格式不正确'\n        },\n        { status: 400 }\n      );\n    }\n\n    // Validate bill data using Zod schema\n    const validationResult = billFormSchema.safeParse(body);\n    if (!validationResult.success) {\n      const formattedErrors = formatValidationErrors(validationResult.error);\n\n      auditLogger.logFinancialOperation(\n        userId,\n        userEmail,\n        'CREATE_BILL_VALIDATION_FAILED',\n        'bills',\n        { validationErrors: formattedErrors },\n        false,\n        'Validation failed',\n        request\n      );\n\n      return NextResponse.json(\n        {\n          error: 'Validation failed',\n          code: 'VALIDATION_ERROR',\n          message: '账单数据验证失败',\n          details: formattedErrors\n        },\n        { status: 400 }\n      );\n    }\n\n    // User email is already declared at the beginning of the function\n\n    // Add additional server-side business logic validation\n    const validatedData = validationResult.data;\n\n    // Calculate subtotal from items\n    const itemsTotal = validatedData.items.reduce((sum, item) => {\n      const itemTotal = item.quantity * item.unitPrice;\n      const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n      return sum + (itemTotal - itemDiscount);\n    }, 0);\n\n    const calculatedSubtotal = Math.round(itemsTotal * 100) / 100;\n    const discountAmount = validatedData.discountAmount || 0;\n    const taxAmount = validatedData.taxAmount || 0;\n    const calculatedTotalAmount = Math.round((calculatedSubtotal + taxAmount - discountAmount) * 100) / 100;\n\n    // Forward request to backend with authentication headers\n    const backendUrl = `${BACKEND_URL}/api/bills`;\n    const billData = {\n      ...validatedData,\n      subtotal: calculatedSubtotal,\n      totalAmount: calculatedTotalAmount\n    };\n    const data = await makeBackendRequest(\n      backendUrl,\n      {\n        method: 'POST',\n        body: JSON.stringify(billData)\n      },\n      userId,\n      userEmail\n    );\n\n    // Log successful bill creation\n    auditLogger.logFinancialOperation(\n      userId,\n      userEmail,\n      'CREATE_BILL',\n      'bills',\n      {\n        billId: data.id,\n        patientId: validatedData.patient,\n        billType: validatedData.billType,\n        subtotal: calculatedSubtotal,\n        totalAmount: calculatedTotalAmount,\n        itemCount: validatedData.items?.length || 0\n      },\n      true,\n      undefined,\n      request\n    );\n\n    return NextResponse.json(data, { status: 201 });\n  } catch (error) {\n    if (error instanceof APIError) {\n      return NextResponse.json(\n        {\n          error: error.message,\n          code: error.code,\n          message: error.message,\n          details: error.details\n        },\n        { status: error.status }\n      );\n    }\n\n    console.error('Unexpected error in POST /api/bills:', error);\n    return NextResponse.json(\n      {\n        error: 'Internal server error',\n        code: 'INTERNAL_ERROR',\n        message: '创建账单时发生服务器错误，请稍后重试'\n      },\n      { status: 500 }\n    );\n  }\n}\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/bills',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\bills\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/bills/route\",\n        pathname: \"/api/bills\",\n        filename: \"route\",\n        bundlePath: \"app/api/bills/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\bills\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["BACKEND_URL", "process", "APIError", "Error", "message", "status", "code", "details", "getClerkUserInfo", "userId", "response", "fetch", "headers", "Authorization", "env", "CLERK_SECRET_KEY", "ok", "json", "error", "console", "makeBackendRequest", "url", "options", "userEmail", "data", "GET", "request", "auth", "auditLogger", "logFinancialOperation", "endpoint", "NextResponse", "rateLimitResult", "rateLimiter", "checkRateLimit", "allowed", "resetTime", "URL", "Object", "fromEntries", "searchParams", "entries", "queryParams", "limit", "isNaN", "Number", "page", "user", "email_addresses", "email_address", "backendUrl", "search", "method", "resultCount", "docs", "length", "undefined", "POST", "body", "description", "InputSanitizer", "sanitizeText", "notes", "items", "map", "item", "itemName", "quantity", "sanitizeAmount", "unitPrice", "sanitizationError", "validationResult", "billFormSchema", "safeParse", "success", "formattedErrors", "formatValidationErrors", "validationErrors", "validatedData", "itemsTotal", "reduce", "sum", "itemTotal", "itemDiscount", "discountRate", "Math", "round", "discountAmount", "taxAmount", "calculatedTotalAmount", "calculatedSubtotal", "billData", "totalAmount", "JSON", "stringify", "billId", "id", "patientId", "patient", "billType", "subtotal", "itemCount", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}