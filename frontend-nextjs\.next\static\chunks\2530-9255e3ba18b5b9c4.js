try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="81831756-f18b-4451-8530-f3d55b51283f",t._sentryDebugIdIdentifier="sentry-dbid-81831756-f18b-4451-8530-f3d55b51283f")}catch(t){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2530],{3356:(t,e,r)=>{var n=r(63194),i=r(44809),o=r(50980),a=r(22461),u=r(66760);t.exports=function(t,e,r){var c=a(t)?n:o;return r&&u(t,e,r)&&(e=void 0),c(t,i(e,3))}},3921:(t,e,r)=>{"use strict";r.d(e,{i:()=>c});let n=Math.PI,i=2*n,o=i-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,i,o){if(t*=1,e*=1,r*=1,i*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let a=this._x1,u=this._y1,c=r-t,l=i-e,s=a-t,f=u-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6)if(Math.abs(f*c-l*s)>1e-6&&o){let h=r-a,d=i-u,y=c*c+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=o*Math.tan((n-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*s},${e+g*f}`,this._append`A${o},${o},0,0,${+(f*h>s*d)},${this._x1=t+x*c},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,a,u,c){if(t*=1,e*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,p=e+s,h=1^c,d=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(d<0&&(d=d%i+i),d>o?this._append`A${r},${r},0,1,${h},${t-l},${e-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${h},${this._x1=t+r*Math.cos(u)},${this._y1=e+r*Math.sin(u)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new u(e)}u.prototype},6559:(t,e,r)=>{"use strict";r.d(e,{i:()=>I});var n=r(99004),i=r(26258),o=r.n(i);let a=Math.cos,u=Math.sin,c=Math.sqrt,l=Math.PI,s=2*l,f={draw(t,e){let r=c(e/l);t.moveTo(r,0),t.arc(0,0,r,0,s)}},p=c(1/3),h=2*p,d=u(l/10)/u(7*l/10),y=u(s/10)*d,v=-a(s/10)*d,m=c(3),b=c(3)/2,g=1/c(12),x=(g/2+1)*3;var O=r(68391),w=r(3921);c(3),c(3);var S=r(97921),j=r(56770);function A(t){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var P=["type","size","sizeType"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function k(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var T={symbolCircle:f,symbolCross:{draw(t,e){let r=c(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=c(e/h),n=r*p;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=c(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=c(.8908130915292852*e),n=y*r,i=v*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=s*e/5,c=a(o),l=u(o);t.lineTo(l*r,-c*r),t.lineTo(c*n-l*i,l*n+c*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-c(e/(3*m));t.moveTo(0,2*r),t.lineTo(-m*r,-r),t.lineTo(m*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=c(e/x),n=r/2,i=r*g,o=r*g+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-b*i,b*n+-.5*i),t.lineTo(-.5*n-b*o,b*n+-.5*o),t.lineTo(-.5*a-b*o,b*a+-.5*o),t.lineTo(-.5*n+b*i,-.5*i-b*n),t.lineTo(-.5*n+b*o,-.5*o-b*n),t.lineTo(-.5*a+b*o,-.5*o-b*a),t.closePath()}}},_=Math.PI/180,C=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*_;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},I=function(t){var e,r=t.type,i=void 0===r?"circle":r,a=t.size,u=void 0===a?64:a,c=t.sizeType,l=void 0===c?"area":c,s=k(k({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,P)),{},{type:i,size:u,sizeType:l}),p=s.className,h=s.cx,d=s.cy,y=(0,j.J9)(s,!0);return h===+h&&d===+d&&u===+u?n.createElement("path",E({},y,{className:(0,S.A)("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=T["symbol".concat(o()(i))]||f,(function(t,e){let r=null,n=(0,w.i)(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:(0,O.A)(t||f),e="function"==typeof e?e:(0,O.A)(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,O.A)(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,O.A)(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(C(u,l,i))())})):null};I.registerSymbol=function(t,e){T["symbol".concat(o()(t))]=e}},8528:(t,e,r)=>{var n=r(44809),i=r(72473);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},10731:(t,e,r)=>{var n=r(53541),i=r(54483),o=r(44809),a=r(22461),u=r(66760);t.exports=function(t,e,r){var c=a(t)?n:i;return r&&u(t,e,r)&&(e=void 0),c(t,o(e,3))}},11428:(t,e,r)=>{"use strict";r.d(e,{A:()=>function t(){var e=new n,r=[],i=[],o=u;function c(t){let n=e.get(t);if(void 0===n){if(o!==u)return o;e.set(t,n=r.push(t)-1)}return i[n%i.length]}return c.domain=function(t){if(!arguments.length)return r.slice();for(let i of(r=[],e=new n,t))e.has(i)||e.set(i,r.push(i)-1);return c},c.range=function(t){return arguments.length?(i=Array.from(t),c):i.slice()},c.unknown=function(t){return arguments.length?(o=t,c):o},c.copy=function(){return t(r,i).unknown(o)},a.C.apply(c,arguments),c},h:()=>u});class n extends Map{constructor(t,e=o){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(i(this,t))}has(t){return super.has(i(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function i({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function o(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(84082);let u=Symbol("implicit")},11751:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>u,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(99004),i=r(75063),o=r.n(i);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!o()(r))return null;var i={};return Object.keys(r).forEach(function(t){s.includes(t)&&(i[t]=e||function(e){return r[t](r,e)})}),i},p=function(t,e,r){if(!o()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];s.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n}},14499:t=>{t.exports=function(t,e){return t>e}},15998:(t,e,r)=>{"use strict";r.d(e,{u:()=>d});var n=r(97921),i=r(99004),o=r(86560),a=r.n(o),u=r(85915),c=r(40017),l=r(56770);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var d=(0,i.forwardRef)(function(t,e){var r,o=t.aspect,s=t.initialDimension,f=void 0===s?{width:-1,height:-1}:s,d=t.width,y=void 0===d?"100%":d,v=t.height,m=void 0===v?"100%":v,b=t.minWidth,g=void 0===b?0:b,x=t.minHeight,O=t.maxHeight,w=t.children,S=t.debounce,j=void 0===S?0:S,A=t.id,P=t.className,E=t.onResize,M=t.style,k=(0,i.useRef)(null),T=(0,i.useRef)();T.current=E,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(k.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),k.current},configurable:!0})});var _=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:f.width,containerHeight:f.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(r,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=_[0],I=_[1],D=(0,i.useCallback)(function(t,e){I(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;D(n,i),null==(e=T.current)||e.call(T,n,i)};j>0&&(t=a()(t,j,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=k.current.getBoundingClientRect();return D(r.width,r.height),e.observe(k.current),function(){e.disconnect()}},[D,j]);var N=(0,i.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,c.R)((0,u._3)(y)||(0,u._3)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,c.R)(!o||o>0,"The aspect(%s) must be greater than zero.",o);var r=(0,u._3)(y)?t:y,n=(0,u._3)(m)?e:m;o&&o>0&&(r?n=r/o:n&&(r=n*o),O&&n>O&&(n=O)),(0,c.R)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,y,m,g,x,o);var a=!Array.isArray(w)&&(0,l.Mn)(w.type).endsWith("Chart");return i.Children.map(w,function(t){return i.isValidElement(t)?(0,i.cloneElement)(t,p({width:r,height:n},a?{style:p({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[o,w,m,O,x,g,C,y]);return i.createElement("div",{id:A?"".concat(A):void 0,className:(0,n.A)("recharts-responsive-container",P),style:p(p({},void 0===M?{}:M),{},{width:y,height:m,minWidth:g,minHeight:x,maxHeight:O}),ref:k},N)})},17557:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},17706:(t,e,r)=>{"use strict";t.exports=r(57990)},17743:(t,e,r)=>{"use strict";r.d(e,{DR:()=>g,pj:()=>w,rY:()=>M,yi:()=>E,Yp:()=>x,hj:()=>P,sk:()=>A,AF:()=>O,Nk:()=>j,$G:()=>S});var n=r(99004),i=r(41406),o=r(71991),a=r.n(o),u=r(10731),c=r.n(u),l=r(81108),s=r.n(l)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f=r(85915),p=(0,n.createContext)(void 0),h=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,i=e.yAxisMap,o=e.offset,a=t.clipPathId,u=t.children,c=t.width,l=t.height,f=s(o);return n.createElement(p.Provider,{value:r},n.createElement(h.Provider,{value:i},n.createElement(y.Provider,{value:o},n.createElement(d.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:c},u)))))))},x=function(){return(0,n.useContext)(v)},O=function(t){var e=(0,n.useContext)(p);null==e&&(0,i.A)(!1);var r=e[t];return null==r&&(0,i.A)(!1),r},w=function(){var t=(0,n.useContext)(p);return(0,f.lX)(t)},S=function(){var t=(0,n.useContext)(h);return a()(t,function(t){return c()(t.domain,Number.isFinite)})||(0,f.lX)(t)},j=function(t){var e=(0,n.useContext)(h);null==e&&(0,i.A)(!1);var r=e[t];return null==r&&(0,i.A)(!1),r},A=function(){return(0,n.useContext)(d)},P=function(){return(0,n.useContext)(y)},E=function(){return(0,n.useContext)(b)},M=function(){return(0,n.useContext)(m)}},19442:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[i,o,a].join("|")+")"+c+u+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(c+u+l),"g");t.exports=function(t){return t.match(s)||[]}},20830:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},23204:function(t,e,r){var n;!function(i){"use strict";var o,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",l=c+"Invalid argument: ",s=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(t,e){var r,n,i,o,a,c,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?P(e,p):e;if(l=t.d,s=e.d,a=t.e,i=e.e,l=l.slice(),o=a-i){for(o<0?(n=l,o=-o,c=s.length):(n=s,i=a,c=l.length),o>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=l.length)-(o=s.length)<0&&(o=c,n=s,s=l,l=n),r=0;o;)r=(l[--o]=l[o]+s[o]+r)/1e7|0,l[o]%=1e7;for(r&&(l.unshift(r),++i),c=l.length;0==l[--c];)l.pop();return e.d=l,e.e=i,u?P(e,p):e}function m(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function b(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=S(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=S(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return g(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return P(g(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(o))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(o)?new r(0):(u=!1,e=g(j(this,i),j(t,i),i),u=!0,P(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c+"NaN");return this.s?(u=!1,e=g(this,t,0,1).times(t),u=!0,this.minus(e)):P(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(l+t);if(e=O(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,i,o,a,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(c+"NaN")}for(t=O(this),u=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=b(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new l(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new l(i.toString()),i=a=(r=l.precision)+3;;)if(n=(o=n).plus(g(this,o,a+2)).times(.5),b(o.d).slice(0,a)===(e=b(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(P(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return u=!0,P(n,r)},y.times=y.mul=function(t){var e,r,n,i,o,a,c,l,s,f=this.constructor,p=this.d,h=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(l=p.length)<(s=h.length)&&(o=p,p=h,h=o,a=l,l=s,s=a),o=[],n=a=l+s;n--;)o.push(0);for(n=s;--n>=0;){for(e=0,i=l+n;i>n;)c=o[i]+h[n]*p[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,u?P(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(m(t,0,1e9),void 0===e?e=n.rounding:m(e,0,8),P(r,t+O(r)+1,e))},y.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=M(n,!0):(m(t,0,1e9),void 0===e?e=i.rounding:m(e,0,8),r=M(n=P(new i(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?M(this):(m(t,0,1e9),void 0===e?e=i.rounding:m(e,0,8),r=M((n=P(new i(this),t+O(this)+1,e)).abs(),!1,t+O(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return P(new t(this),O(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,i,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(o);if(!(s=new p(s)).s){if(t.s<1)throw Error(c+"Infinity");return s}if(s.eq(o))return s;if(n=p.precision,t.eq(o))return P(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=h<0?-h:h)<=0x1fffffffffffff){for(i=new p(o),e=Math.ceil(n/7+4),u=!1;r%2&&k((i=i.times(s)).d,e),0!==(r=f(r/2));)k((s=s.times(s)).d,e);return u=!0,t.s<0?new p(o).div(i):P(i,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,u=!1,i=t.times(j(s,n+12)),u=!0,(i=x(i)).s=a,i},y.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=O(i),n=M(i,r<=o.toExpNeg||r>=o.toExpPos)):(m(t,1,1e9),void 0===e?e=o.rounding:m(e,0,8),r=O(i=P(new o(i),t,e)),n=M(i,t<=r||r<=o.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(m(t,1,1e9),void 0===e?e=r.rounding:m(e,0,8)),P(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=O(this),e=this.constructor;return M(this,t<=e.toExpNeg||t>=e.toExpPos)};var g=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var u,l,s,f,p,h,d,y,v,m,b,g,x,w,S,j,A,E,M=n.constructor,k=n.s==i.s?1:-1,T=n.d,_=i.d;if(!n.s)return new M(n);if(!i.s)throw Error(c+"Division by zero");for(s=0,l=n.e-i.e,A=_.length,S=T.length,y=(d=new M(k)).d=[];_[s]==(T[s]||0);)++s;if(_[s]>(T[s]||0)&&--l,(g=null==o?o=M.precision:a?o+(O(n)-O(i))+1:o)<0)return new M(0);if(g=g/7+2|0,s=0,1==A)for(f=0,_=_[0],g++;(s<S||f)&&g--;s++)x=1e7*f+(T[s]||0),y[s]=x/_|0,f=x%_|0;else{for((f=1e7/(_[0]+1)|0)>1&&(_=t(_,f),T=t(T,f),A=_.length,S=T.length),w=A,m=(v=T.slice(0,A)).length;m<A;)v[m++]=0;(E=_.slice()).unshift(0),j=_[0],_[1]>=1e7/2&&++j;do f=0,(u=e(_,v,A,m))<0?(b=v[0],A!=m&&(b=1e7*b+(v[1]||0)),(f=b/j|0)>1?(f>=1e7&&(f=1e7-1),h=(p=t(_,f)).length,m=v.length,1==(u=e(p,v,h,m))&&(f--,r(p,A<h?E:_,h))):(0==f&&(u=f=1),p=_.slice()),(h=p.length)<m&&p.unshift(0),r(v,p,m),-1==u&&(m=v.length,(u=e(_,v,A,m))<1&&(f++,r(v,A<m?E:_,m))),m=v.length):0===u&&(f++,v=[0]),y[s++]=f,u&&v[0]?v[m++]=T[w]||0:(v=[T[w]],m=1);while((w++<S||void 0!==v[0])&&g--)}return y[0]||y.shift(),d.e=l,P(d,a?o+O(d)+1:o)}}();function x(t,e){var r,n,i,a,c,l=0,f=0,h=t.constructor,d=h.precision;if(O(t)>16)throw Error(s+O(t));if(!t.s)return new h(o);for(null==e?(u=!1,c=d):c=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=i=new h(o),h.precision=c;;){if(n=P(n.times(t),c),r=r.times(++l),b((a=i.plus(g(n,r,c))).d).slice(0,c)===b(i.d).slice(0,c)){for(;f--;)i=P(i.times(i),c);return h.precision=d,null==e?(u=!0,P(i,d)):i}i=a}}function O(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function w(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(c+"LN10 precision limit exceeded");return P(new t(t.LN10),e)}function S(t){for(var e="";t--;)e+="0";return e}function j(t,e){var r,n,i,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new m(0);if(null==e?(u=!1,p=x):p=e,y.eq(10))return null==e&&(u=!0),w(m,p);if(m.precision=p+=10,n=(r=b(v)).charAt(0),!(15e14>Math.abs(a=O(y))))return f=w(m,p+2,x).times(a+""),y=j(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==e?(u=!0,P(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=b((y=y.times(t)).d)).charAt(0),d++;for(a=O(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=g(y.minus(o),y.plus(o),p),h=P(y.times(y),p),i=3;;){if(l=P(l.times(h),p),b((f=s.plus(g(l,new m(i),p))).d).slice(0,p)===b(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(w(m,p+2,x).times(a+""))),s=g(s,new m(d),p),m.precision=x,null==e?(u=!0,P(s,x)):s;s=f,i+=2}}function A(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=f((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>d||t.e<-d))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function P(t,e,r){var n,i,o,a,c,l,h,y,v=t.d;for(a=1,o=v[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(o=v.length))return t;for(a=1,h=o=v[y];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=h/(o=p(10,a-i-1))%10|0,l=e<0||void 0!==v[y+1]||h%o,l=r<4?(c||l)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||l||6==r&&(n>0?i>0?h/p(10,a-i):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return l?(o=O(t),v.length=1,e=e-o-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,o=1,y--):(v.length=y+1,o=p(10,7-n),v[y]=i>0?(h/p(10,a-i)%p(10,i)|0)*o:0),l)for(;;)if(0==y){1e7==(v[0]+=o)&&(v[0]=1,++t.e);break}else{if(v[y]+=o,1e7!=v[y])break;v[y--]=0,o=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(t.e>d||t.e<-d))throw Error(s+O(t));return t}function E(t,e){var r,n,i,o,a,c,l,s,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?P(e,d):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,c=p.length):(r=p,n=s,c=l.length),a>(i=Math.max(Math.ceil(d/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((f=(i=l.length)<(c=p.length))&&(c=i),i=0;i<c;i++)if(l[i]!=p[i]){f=l[i]<p[i];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),c=l.length,i=p.length-c;i>0;--i)l[c++]=0;for(i=p.length;i>a;){if(l[--i]<p[i]){for(o=i;o&&0===l[--o];)l[o]=1e7-1;--l[o],l[i]+=1e7}l[i]-=p[i]}for(;0===l[--c];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,u?P(e,d):e):new h(0)}function M(t,e,r){var n,i=O(t),o=b(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+S(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+S(-i-1)+o,r&&(n=r-a)>0&&(o+=S(n))):i>=a?(o+=S(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+S(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=S(n))),t.s<0?"-"+o:o}function k(t,e){if(t.length>e)return t.length=e,!0}function T(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(f(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(l+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(l+r+": "+n);return this}(a=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(l+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return A(this,t.toString())}if("string"!=typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,h.test(t))A(this,t);else throw Error(l+t)}if(o.prototype=y,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=T,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}(a)).default=a.Decimal=a,o=new a(1),void 0===(n=(function(){return a}).call(e,r,e,t))||(t.exports=n)}(0)},23485:(t,e,r)=>{var n=r(77849);t.exports=function(t){return n(t)&&t!=+t}},23701:(t,e,r)=>{"use strict";r.d(e,{J:()=>A});var n=r(99004),i=r(90671),o=r.n(i),a=r(52780),u=r.n(a),c=r(75063),l=r.n(c),s=r(97921),f=r(28524),p=r(56770),h=r(85915),d=r(63295);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var O=function(t){var e=t.value,r=t.formatter,n=o()(t.children)?e:t.children;return u()(r)?r(n):n},w=function(t,e,r){var i,a,u=t.position,c=t.viewBox,l=t.offset,f=t.className,p=c.cx,y=c.cy,v=c.innerRadius,m=c.outerRadius,b=c.startAngle,g=c.endAngle,O=c.clockWise,w=(v+m)/2,S=(0,h.sA)(g-b)*Math.min(Math.abs(g-b),360),j=S>=0?1:-1;"insideStart"===u?(i=b+j*l,a=O):"insideEnd"===u?(i=g-j*l,a=!O):"end"===u&&(i=g+j*l,a=O),a=S<=0?a:!a;var A=(0,d.IZ)(p,y,w,i),P=(0,d.IZ)(p,y,w,i+(a?1:-1)*359),E="M".concat(A.x,",").concat(A.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!a,",\n    ").concat(P.x,",").concat(P.y),M=o()(t.id)?(0,h.NF)("recharts-radial-line-"):t.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:M,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(M)},e))},S=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,u=e.outerRadius,c=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,d.IZ)(i,o,u+r,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=(0,d.IZ)(i,o,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},j=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,u=e.width,c=e.height,s=c>=0?1:-1,f=s*n,p=s>0?"end":"start",d=s>0?"start":"end",y=u>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return g(g({},{x:o+u/2,y:a-s*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===i)return g(g({},{x:o+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===i){var x={x:o-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===i){var O={x:o+u+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:c}:{})}var w=r?{width:u,height:c}:{};return"insideLeft"===i?g({x:o+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===i?g({x:o+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===i?g({x:o+u/2,y:a+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===i?g({x:o+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===i?g({x:o+v,y:a+f,textAnchor:b,verticalAnchor:d},w):"insideTopRight"===i?g({x:o+u-v,y:a+f,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===i?g({x:o+v,y:a+c-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===i?g({x:o+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},w):l()(i)&&((0,h.Et)(i.x)||(0,h._3)(i.x))&&((0,h.Et)(i.y)||(0,h._3)(i.y))?g({x:o+(0,h.F4)(i.x,u),y:a+(0,h.F4)(i.y,c),textAnchor:"end",verticalAnchor:"end"},w):g({x:o+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function A(t){var e,r=t.offset,i=g({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,v)),a=i.viewBox,c=i.position,l=i.value,d=i.children,y=i.content,m=i.className,b=i.textBreakAll;if(!a||o()(l)&&o()(d)&&!(0,n.isValidElement)(y)&&!u()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,i);if(u()(y)){if(e=(0,n.createElement)(y,i),(0,n.isValidElement)(e))return e}else e=O(i);var A="cx"in a&&(0,h.Et)(a.cx),P=(0,p.J9)(i,!0);if(A&&("insideStart"===c||"insideEnd"===c||"end"===c))return w(i,e,P);var E=A?S(i):j(i);return n.createElement(f.E,x({className:(0,s.A)("recharts-label",void 0===m?"":m)},P,E,{breakAll:b}),e)}A.displayName="Label";var P=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.Et)(y)&&(0,h.Et)(v)){if((0,h.Et)(s)&&(0,h.Et)(f))return{x:s,y:f,width:y,height:v};if((0,h.Et)(p)&&(0,h.Et)(d))return{x:p,y:d,width:y,height:v}}return(0,h.Et)(s)&&(0,h.Et)(f)?{x:s,y:f,width:0,height:0}:(0,h.Et)(e)&&(0,h.Et)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};A.parseViewBox=P,A.renderCallByParent=function(t,e){var r,i,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var a=t.children,c=P(t),s=(0,p.aS)(a,A).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||c,key:"label-".concat(r)})});if(!o)return s;return[(r=t.label,i=e||c,!r?null:!0===r?n.createElement(A,{key:"label-implicit",viewBox:i}):(0,h.vh)(r)?n.createElement(A,{key:"label-implicit",viewBox:i,value:r}):(0,n.isValidElement)(r)?r.type===A?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:i}):n.createElement(A,{key:"label-implicit",content:r,viewBox:i}):u()(r)?n.createElement(A,{key:"label-implicit",content:r,viewBox:i}):l()(r)?n.createElement(A,x({viewBox:i},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return m(t)}(s)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(s)||function(t,e){if(t){if("string"==typeof t)return m(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(t,e)}}(s)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},23933:(t,e,r)=>{var n=r(48062),i=r(20830),o=r(19442);t.exports=function(t){return i(t)?o(t):n(t)}},26258:(t,e,r)=>{t.exports=r(52885)("toUpperCase")},28359:(t,e,r)=>{var n=r(55951),i=r(38264),o=r(99339);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},28524:(t,e,r)=>{"use strict";r.d(e,{E:()=>R});var n=r(99004),i=r(90671),o=r.n(i),a=r(97921),u=r(85915),c=r(53033),l=r(56770),s=r(63111);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),O=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*g[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=p(null!=(e=b.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&d(r.prototype,t),e&&d(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function w(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!=(r=y.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],u=O.parse(null!=i?i:""),c=O.parse(null!=a?a:""),l="*"===o?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";e=e.replace(y,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!=(s=v.exec(e))?s:[],4),h=f[1],d=f[2],m=f[3],b=O.parse(null!=h?h:""),g=O.parse(null!=m?m:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var S=/\(([^()]*)\)/;function j(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=p(S.exec(e),2)[1];e=e.replace(S,w(r))}return e}(e),e=w(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var A=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],P=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function k(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return T(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var _=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];o()(e)||(i=r?e.toString().split(""):e.toString().split(_));var a=i.map(function(t){return{word:t,width:(0,s.Pu)(t,n).width}}),u=r?0:(0,s.Pu)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(t){return null}},I=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,l=t.style,s=t.breakAll,f=(0,u.Et)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,u=t[t.length-1];return u&&(null==n||i||u.width+a+r<Number(n))?(u.words.push(o),u.width+=a+r):t.push({words:[o],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(C({breakAll:s,style:l,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var b=Math.floor((y+v)/2),g=k(d(b-1),2),x=g[0],O=g[1],w=k(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){o=O;break}m++}return o||h},D=function(t){return[{words:o()(t)?[]:t.toString().split(_)}]},N=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!c.m.isSsr){var u=C({breakAll:o,children:n,style:i});if(!u)return D(n);var l=u.wordsWithComputedWidth,s=u.spaceWidth;return I({breakAll:o,children:n,maxLines:a,style:i},l,s,e,r)}return D(n)},B="#808080",R=function(t){var e,r=t.x,i=void 0===r?0:r,o=t.y,c=void 0===o?0:o,s=t.lineHeight,f=void 0===s?"1em":s,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=t.verticalAnchor,b=t.fill,g=void 0===b?B:b,x=M(t,A),O=(0,n.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),w=x.dx,S=x.dy,k=x.angle,T=x.className,_=x.breakAll,C=M(x,P);if(!(0,u.vh)(i)||!(0,u.vh)(c))return null;var I=i+((0,u.Et)(w)?w:0),D=c+((0,u.Et)(S)?S:0);switch(void 0===m?"end":m){case"start":e=j("calc(".concat(h,")"));break;case"middle":e=j("calc(".concat((O.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:e=j("calc(".concat(O.length-1," * -").concat(f,")"))}var R=[];if(y){var L=O[0].width,U=x.width;R.push("scale(".concat(((0,u.Et)(U)?U/L:1)/L,")"))}return k&&R.push("rotate(".concat(k,", ").concat(I,", ").concat(D,")")),R.length&&(C.transform=R.join(" ")),n.createElement("text",E({},(0,l.J9)(C,!0),{x:I,y:D,className:(0,a.A)("recharts-text",T),textAnchor:void 0===v?"start":v,fill:g.includes("url")?B:g}),O.map(function(t,r){var i=t.words.join(_?"":" ");return n.createElement("tspan",{x:I,dy:0===r?e:f,key:"".concat(i,"-").concat(r)},i)}))}},29573:t=>{t.exports=function(t){return t!=t}},29776:(t,e,r)=>{"use strict";r.d(e,{s0:()=>n3,gH:()=>n0,YB:()=>io,HQ:()=>ir,xi:()=>ia,Hj:()=>iO,BX:()=>n5,tA:()=>n2,DW:()=>iy,y2:()=>id,nb:()=>ih,PW:()=>n7,Ay:()=>nQ,vf:()=>n4,Mk:()=>im,Ps:()=>n1,Mn:()=>is,kA:()=>iv,Rh:()=>it,w7:()=>ip,zb:()=>iS,kr:()=>nJ,_L:()=>n8,KC:()=>iw,A1:()=>n6,W7:()=>ii,AQ:()=>ix,_f:()=>iu});var n,i,o,a,u,c,l,s={};r.r(s),r.d(s,{scaleBand:()=>f.A,scaleDiverging:()=>function t(){var e=tN(rJ()(tv));return e.copy=function(){return rV(e,t())},tS.K.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tq(rJ()).domain([.1,1,10]);return e.copy=function(){return rV(e,t()).base(e.base())},tS.K.apply(e,arguments)},scaleDivergingPow:()=>rQ,scaleDivergingSqrt:()=>r0,scaleDivergingSymlog:()=>function t(){var e=tY(rJ());return e.copy=function(){return rV(e,t()).constant(e.constant())},tS.K.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,td),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,td):[0,1],tN(n)},scaleImplicit:()=>tK.h,scaleLinear:()=>tB,scaleLog:()=>function t(){let e=tq(tO()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tS.C.apply(e,arguments),e},scaleOrdinal:()=>tK.A,scalePoint:()=>f.z,scalePow:()=>tQ,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=S){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[A(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(g),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tS.C.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[A(o,t,0,i)]:e}function c(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,c()):[r,n]},u.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return o.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tS.C.apply(tN(u),arguments)},scaleRadial:()=>function t(){var e,r=tw(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(t1(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,td)).map(t1)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},tS.C.apply(o,arguments),tN(o)},scaleSequential:()=>function t(){var e=tN(rK()(tv));return e.copy=function(){return rV(e,t())},tS.K.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tq(rK()).domain([1,10]);return e.copy=function(){return rV(e,t()).base(e.base())},tS.K.apply(e,arguments)},scaleSequentialPow:()=>rG,scaleSequentialQuantile:()=>function t(){var e=[],r=tv;function n(t){if(null!=t&&!isNaN(t*=1))return r((A(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(g),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return t5(t);if(e>=1)return t2(t);var n,i=(n-1)*e,o=Math.floor(i),a=t2((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?t3:function(t=g){if(t===g)return t3;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),p=Math.min(i,Math.floor(r+(a-u)*l/a+s));t(e,r,f,p,o)}let a=e[r],u=n,c=i;for(t9(e,n,r),o(e[i],a)>0&&t9(e,n,i);u<c;){for(t9(e,u,c),++u,--c;0>o(e[u],a);)++u;for(;o(e[c],a)>0;)--c}0===o(e[n],a)?t9(e,n,c):t9(e,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return e})(t,o).subarray(0,o+1));return a+(t5(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tS.K.apply(n,arguments)},scaleSequentialSqrt:()=>rZ,scaleSequentialSymlog:()=>function t(){var e=tY(rK());return e.copy=function(){return rV(e,t()).constant(e.constant())},tS.K.apply(e,arguments)},scaleSqrt:()=>t0,scaleSymlog:()=>function t(){var e=tY(tO());return e.copy=function(){return tx(e,t()).constant(e.constant())},tS.C.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[A(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},tS.C.apply(o,arguments)},scaleTime:()=>rH,scaleUtc:()=>rY,tickFormat:()=>tD});var f=r(61476);let p=Math.sqrt(50),h=Math.sqrt(10),d=Math.sqrt(2);function y(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),l=c>=p?10:c>=h?5:c>=d?2:1;return(u<0?(n=Math.round(t*(o=Math.pow(10,-u)/l)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,u)*l)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?y(t,e,2*r):[n,i,o]}function v(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?y(e,t,r):y(t,e,r);if(!(o>=i))return[];let u=o-i+1,c=Array(u);if(n)if(a<0)for(let t=0;t<u;++t)c[t]=-((o-t)/a);else for(let t=0;t<u;++t)c[t]=(o-t)*a;else if(a<0)for(let t=0;t<u;++t)c[t]=-((i+t)/a);else for(let t=0;t<u;++t)c[t]=(i+t)*a;return c}function m(t,e,r){return y(t*=1,e*=1,r*=1)[2]}function b(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?m(e,t,r):m(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function g(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function O(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=g,r=(e,r)=>g(t(e),r),n=(e,r)=>t(e)-r):(e=t===g||t===x?t:w,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function w(){return 0}function S(t){return null===t?NaN:+t}let j=O(g),A=j.right;function P(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function E(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function M(){}j.left,O(S).center;var k="\\s*([+-]?\\d+)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,I=RegExp(`^rgb\\(${k},${k},${k}\\)$`),D=RegExp(`^rgb\\(${_},${_},${_}\\)$`),N=RegExp(`^rgba\\(${k},${k},${k},${T}\\)$`),B=RegExp(`^rgba\\(${_},${_},${_},${T}\\)$`),R=RegExp(`^hsl\\(${T},${_},${_}\\)$`),L=RegExp(`^hsla\\(${T},${_},${_},${T}\\)$`),U={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function z(){return this.rgb().formatHex()}function $(){return this.rgb().formatRgb()}function F(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=C.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?W(e):3===r?new H(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?q(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?q(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=I.exec(t))?new H(e[1],e[2],e[3],1):(e=D.exec(t))?new H(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?q(e[1],e[2],e[3],e[4]):(e=B.exec(t))?q(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=R.exec(t))?J(e[1],e[2]/100,e[3]/100,1):(e=L.exec(t))?J(e[1],e[2]/100,e[3]/100,e[4]):U.hasOwnProperty(t)?W(U[t]):"transparent"===t?new H(NaN,NaN,NaN,0):null}function W(t){return new H(t>>16&255,t>>8&255,255&t,1)}function q(t,e,r,n){return n<=0&&(t=e=r=NaN),new H(t,e,r,n)}function X(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof M||(i=F(i)),i)?new H((i=i.rgb()).r,i.g,i.b,i.opacity):new H:new H(t,e,r,null==n?1:n)}function H(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function Y(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}`}function K(){let t=V(this.opacity);return`${1===t?"rgb(":"rgba("}${G(this.r)}, ${G(this.g)}, ${G(this.b)}${1===t?")":`, ${t})`}`}function V(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function G(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Z(t){return((t=G(t))<16?"0":"")+t.toString(16)}function J(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,r,n)}function Q(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof M||(t=F(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,u=o-i,c=(o+i)/2;return u?(a=e===o?(r-n)/u+(r<n)*6:r===o?(n-e)/u+2:(e-r)/u+4,u/=c<.5?o+i:2-o-i,a*=60):u=c>0&&c<1?0:a,new tt(a,u,c,t.opacity)}function tt(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function te(t){return(t=(t||0)%360)<0?t+360:t}function tr(t){return Math.max(0,Math.min(1,t||0))}function tn(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function ti(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}P(M,F,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:z,formatHex:z,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:$,toString:$}),P(H,X,E(M,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new H(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new H(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new H(G(this.r),G(this.g),G(this.b),V(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Y,formatHex:Y,formatHex8:function(){return`#${Z(this.r)}${Z(this.g)}${Z(this.b)}${Z((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:K,toString:K})),P(tt,function(t,e,r,n){return 1==arguments.length?Q(t):new tt(t,e,r,null==n?1:n)},E(M,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new H(tn(t>=240?t-240:t+120,i,n),tn(t,i,n),tn(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new tt(te(this.h),tr(this.s),tr(this.l),V(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=V(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tr(this.s)}%, ${100*tr(this.l)}%${1===t?")":`, ${t})`}`}}));let to=t=>()=>t;function ta(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):to(isNaN(t)?e:t)}let tu=function t(e){var r,n=1==(r=+e)?ta:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):to(isNaN(t)?e:t)};function i(t,e){var r=n((t=X(t)).r,(e=X(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function tc(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),u=Array(i);for(r=0;r<i;++r)n=X(e[r]),o[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return o=t(o),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=u(t),n+""}}}tc(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,u=n<e-1?t[n+2]:2*o-i;return ti((r-n/e)*e,a,i,o,u)}}),tc(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return ti((r-n/e)*e,i,o,a,u)}});function tl(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var ts=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(ts.source,"g");function tp(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?to(e):("number"===i?tl:"string"===i?(n=F(e))?(e=n,tu):function(t,e){var r,n,i,o,a,u=ts.lastIndex=tf.lastIndex=0,c=-1,l=[],s=[];for(t+="",e+="";(i=ts.exec(t))&&(o=tf.exec(e));)(a=o.index)>u&&(a=e.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(i=i[0])===(o=o[0])?l[c]?l[c]+=o:l[++c]=o:(l[++c]=null,s.push({i:c,x:tl(i,o)})),u=tf.lastIndex;return u<e.length&&(a=e.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof F?tu:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=tp(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tp(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:tl:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function th(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function td(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tb(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=tm(i,n),o=r(a,o)):(n=tm(n,i),o=r(o,a)),function(t){return o(n(t))}}function tg(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=tm(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=A(t,e,1,n)-1;return o[r](i[r](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tO(){var t,e,r,n,i,o,a=ty,u=ty,c=tp,l=tv;function s(){var t,e,r,c=Math.min(a.length,u.length);return l!==tv&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tg:tb,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(u,a.map(t),tl)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,td),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=th,s()},f.clamp=function(t){return arguments.length?(l=!!t||tv,s()):l!==tv},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function tw(){return tO()(tv,tv)}var tS=r(84082),tj=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tA(t){var e;if(!(e=tj.exec(t)))throw Error("invalid format: "+t);return new tP({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tP(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tM(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tk(t,e){var r=tE(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}tA.prototype=tP.prototype,tP.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let tT={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tk(100*t,e),r:tk,s:function(t,e){var r=tE(t,e);if(!r)return t+"";var i=r[0],o=r[1],a=o-(n=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,u=i.length;return a===u?i:a>u?i+Array(a-u+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function t_(t){return t}var tC=Array.prototype.map,tI=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tD(t,e,r,n){var i,u,c,l=b(t,e,r);switch((n=tA(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(c=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tM(s)/3)))-tM(Math.abs(l))))||(n.precision=c),a(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(c=Math.max(0,tM(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=l)))-tM(i))+1)||(n.precision=c-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(c=Math.max(0,-tM(Math.abs(l))))||(n.precision=c-("%"===n.type)*2)}return o(n)}function tN(t){var e=t.domain;return t.ticks=function(t){var r=e();return v(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tD(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,u=o.length-1,c=o[a],l=o[u],s=10;for(l<c&&(i=c,c=l,l=i,i=a,a=u,u=i);s-- >0;){if((i=m(c,l,r))===n)return o[a]=c,o[u]=l,e(o);if(i>0)c=Math.floor(c/i)*i,l=Math.ceil(l/i)*i;else if(i<0)c=Math.ceil(c*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function tB(){var t=tw();return t.copy=function(){return tx(t,tB())},tS.C.apply(t,arguments),tN(t)}function tR(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function tL(t){return Math.log(t)}function tU(t){return Math.exp(t)}function tz(t){return-Math.log(-t)}function t$(t){return-Math.exp(-t)}function tF(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tW(t){return(e,r)=>-t(-e,r)}function tq(t){let e,r,n=t(tL,tU),i=n.domain,a=10;function u(){var o,u;return e=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),t=>Math.log(t)/o),r=10===(u=a)?tF:u===Math.E?Math.exp:t=>Math.pow(u,t),i()[0]<0?(e=tW(e),r=tW(r),t(tz,t$)):t(tL,tU),n}return n.base=function(t){return arguments.length?(a=+t,u()):a},n.domain=function(t){return arguments.length?(i(t),u()):i()},n.ticks=t=>{let n,o,u=i(),c=u[0],l=u[u.length-1],s=l<c;s&&([c,l]=[l,c]);let f=e(c),p=e(l),h=null==t?10:+t,d=[];if(!(a%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>l)break;d.push(o)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>l)break;d.push(o)}2*d.length<h&&(d=v(c,l,h))}else d=v(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=tA(i)).precision||(i.trim=!0),i=o(i)),t===1/0)return i;let u=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=u?i(t):""}},n.nice=()=>i(tR(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tX(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tH(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tY(t){var e=1,r=t(tX(1),tH(e));return r.constant=function(r){return arguments.length?t(tX(e=+r),tH(e)):e},tN(r)}o=(i=function(t){var e,r,i,o=void 0===t.grouping||void 0===t.thousands?t_:(e=tC.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),o.push(t.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return o.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",l=void 0===t.numerals?t_:(i=tC.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return i[+t]})}),s=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tA(t)).fill,r=t.align,i=t.sign,h=t.symbol,d=t.zero,y=t.width,v=t.comma,m=t.precision,b=t.trim,g=t.type;"n"===g?(v=!0,g="g"):tT[g]||(void 0===m&&(m=12),b=!0,g="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var x="$"===h?a:"#"===h&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",O="$"===h?u:/[%p]/.test(g)?s:"",w=tT[g],S=/[defgprs%]/.test(g);function j(t){var a,u,s,h=x,j=O;if("c"===g)j=w(t)+j,t="";else{var A=(t*=1)<0||1/t<0;if(t=isNaN(t)?p:w(Math.abs(t),m),b&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==i&&(A=!1),h=(A?"("===i?i:f:"-"===i||"("===i?"":i)+h,j=("s"===g?tI[8+n/3]:"")+j+(A&&"("===i?")":""),S){for(a=-1,u=t.length;++a<u;)if(48>(s=t.charCodeAt(a))||s>57){j=(46===s?c+t.slice(a+1):t.slice(a))+j,t=t.slice(0,a);break}}}v&&!d&&(t=o(t,1/0));var P=h.length+t.length+j.length,E=P<y?Array(y-P+1).join(e):"";switch(v&&d&&(t=o(E+t,E.length?y-j.length:1/0),E=""),r){case"<":t=h+t+j+E;break;case"=":t=h+E+t+j;break;case"^":t=E.slice(0,P=E.length>>1)+h+t+j+E.slice(P);break;default:t=E+h+t+j}return l(t)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),j.toString=function(){return t+""},j}return{format:h,formatPrefix:function(t,e){var r=h(((t=tA(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tM(e)/3))),i=Math.pow(10,-n),o=tI[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=i.formatPrefix;var tK=r(11428);function tV(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tG(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tZ(t){return t<0?-t*t:t*t}function tJ(t){var e=t(tv,tv),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tv,tv):.5===r?t(tG,tZ):t(tV(r),tV(1/r)):r},tN(e)}function tQ(){var t=tJ(tO());return t.copy=function(){return tx(t,tQ()).exponent(t.exponent())},tS.C.apply(t,arguments),t}function t0(){return tQ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function t5(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function t3(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function t9(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t6=new Date,t4=new Date;function t8(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,u=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return u;do u.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return u},i.filter=r=>t8(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(t6.setTime(+e),t4.setTime(+n),t(t6),t(t4),Math.floor(r(t6,t4))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let t7=t8(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t7.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t8(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t7:null,t7.range;let et=t8(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());et.range;let ee=t8(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ee.range;let er=t8(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());er.range;let en=t8(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());en.range;let ei=t8(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());ei.range;let eo=t8(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);eo.range;let ea=t8(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ea.range;let eu=t8(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ec(t){return t8(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}eu.range;let el=ec(0),es=ec(1),ef=ec(2),ep=ec(3),eh=ec(4),ed=ec(5),ey=ec(6);function ev(t){return t8(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}el.range,es.range,ef.range,ep.range,eh.range,ed.range,ey.range;let em=ev(0),eb=ev(1),eg=ev(2),ex=ev(3),eO=ev(4),ew=ev(5),eS=ev(6);em.range,eb.range,eg.range,ex.range,eO.range,ew.range,eS.range;let ej=t8(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ej.range;let eA=t8(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eA.range;let eP=t8(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eP.every=t=>isFinite(t=Math.floor(t))&&t>0?t8(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eP.range;let eE=t8(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function eM(t,e,r,n,i,o){let a=[[et,1,1e3],[et,5,5e3],[et,15,15e3],[et,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function u(e,r,n){let i=Math.abs(r-e)/n,o=O(([,,t])=>t).right(a,i);if(o===a.length)return t.every(b(e/31536e6,r/31536e6,n));if(0===o)return t7.every(Math.max(b(e,r,n),1));let[u,c]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return u.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:u(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},u]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t8(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eE.range;let[ek,eT]=eM(eE,eA,em,eu,ei,er),[e_,eC]=eM(eP,ej,el,eo,en,ee);function eI(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eD(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eN(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eB={"-":"",_:" ",0:"0"},eR=/^\s*\d+/,eL=/^%/,eU=/[\\^$*+?|[\]().{}]/g;function ez(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function e$(t){return t.replace(eU,"\\$&")}function eF(t){return RegExp("^(?:"+t.map(e$).join("|")+")","i")}function eW(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eq(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eX(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eH(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=eR.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eZ(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eJ(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eQ(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e0(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eR.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=eR.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e9(t,e,r){var n=eR.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eR.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e4(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e8(t,e,r){var n=eR.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=eR.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function rt(t,e){return ez(t.getDate(),e,2)}function re(t,e){return ez(t.getHours(),e,2)}function rr(t,e){return ez(t.getHours()%12||12,e,2)}function rn(t,e){return ez(1+eo.count(eP(t),t),e,3)}function ri(t,e){return ez(t.getMilliseconds(),e,3)}function ro(t,e){return ri(t,e)+"000"}function ra(t,e){return ez(t.getMonth()+1,e,2)}function ru(t,e){return ez(t.getMinutes(),e,2)}function rc(t,e){return ez(t.getSeconds(),e,2)}function rl(t){var e=t.getDay();return 0===e?7:e}function rs(t,e){return ez(el.count(eP(t)-1,t),e,2)}function rf(t){var e=t.getDay();return e>=4||0===e?eh(t):eh.ceil(t)}function rp(t,e){return t=rf(t),ez(eh.count(eP(t),t)+(4===eP(t).getDay()),e,2)}function rh(t){return t.getDay()}function rd(t,e){return ez(es.count(eP(t)-1,t),e,2)}function ry(t,e){return ez(t.getFullYear()%100,e,2)}function rv(t,e){return ez((t=rf(t)).getFullYear()%100,e,2)}function rm(t,e){return ez(t.getFullYear()%1e4,e,4)}function rb(t,e){var r=t.getDay();return ez((t=r>=4||0===r?eh(t):eh.ceil(t)).getFullYear()%1e4,e,4)}function rg(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+ez(e/60|0,"0",2)+ez(e%60,"0",2)}function rx(t,e){return ez(t.getUTCDate(),e,2)}function rO(t,e){return ez(t.getUTCHours(),e,2)}function rw(t,e){return ez(t.getUTCHours()%12||12,e,2)}function rS(t,e){return ez(1+ea.count(eE(t),t),e,3)}function rj(t,e){return ez(t.getUTCMilliseconds(),e,3)}function rA(t,e){return rj(t,e)+"000"}function rP(t,e){return ez(t.getUTCMonth()+1,e,2)}function rE(t,e){return ez(t.getUTCMinutes(),e,2)}function rM(t,e){return ez(t.getUTCSeconds(),e,2)}function rk(t){var e=t.getUTCDay();return 0===e?7:e}function rT(t,e){return ez(em.count(eE(t)-1,t),e,2)}function r_(t){var e=t.getUTCDay();return e>=4||0===e?eO(t):eO.ceil(t)}function rC(t,e){return t=r_(t),ez(eO.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function rI(t){return t.getUTCDay()}function rD(t,e){return ez(eb.count(eE(t)-1,t),e,2)}function rN(t,e){return ez(t.getUTCFullYear()%100,e,2)}function rB(t,e){return ez((t=r_(t)).getUTCFullYear()%100,e,2)}function rR(t,e){return ez(t.getUTCFullYear()%1e4,e,4)}function rL(t,e){var r=t.getUTCDay();return ez((t=r>=4||0===r?eO(t):eO.ceil(t)).getUTCFullYear()%1e4,e,4)}function rU(){return"+0000"}function rz(){return"%"}function r$(t){return+t}function rF(t){return Math.floor(t/1e3)}function rW(t){return new Date(t)}function rq(t){return t instanceof Date?+t:+new Date(+t)}function rX(t,e,r,n,i,o,a,u,c,l){var s=tw(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rq)):p().map(rW)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tR(r,t)):s},s.copy=function(){return tx(s,rX(t,e,r,n,i,o,a,u,c,l))},s}function rH(){return tS.C.apply(rX(e_,eC,eP,ej,el,eo,en,ee,et,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rY(){return tS.C.apply(rX(ek,eT,eE,eA,em,ea,ei,er,et,l).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rK(){var t,e,r,n,i,o=0,a=1,u=tv,c=!1;function l(e){return null==e||isNaN(e*=1)?i:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(tp),l.rangeRound=s(th),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function rV(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rG(){var t=tJ(rK());return t.copy=function(){return rV(t,rG()).exponent(t.exponent())},tS.K.apply(t,arguments)}function rZ(){return rG.apply(null,arguments).exponent(.5)}function rJ(){var t,e,r,n,i,o,a,u=0,c=.5,l=1,s=1,f=tv,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=o(u*=1),e=o(c*=1),r=o(l*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(tp),h.rangeRound=d(th),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function rQ(){var t=tJ(rJ());return t.copy=function(){return rV(t,rQ()).exponent(t.exponent())},tS.K.apply(t,arguments)}function r0(){return rQ.apply(null,arguments).exponent(.5)}function r1(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],u=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}c=(u=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=eF(i),s=eW(i),f=eF(o),p=eW(o),h=eF(a),d=eW(a),y=eF(u),v=eW(u),m=eF(c),b=eW(c),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:rt,e:rt,f:ro,g:rv,G:rb,H:re,I:rr,j:rn,L:ri,m:ra,M:ru,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:r$,s:rF,S:rc,u:rl,U:rs,V:rp,w:rh,W:rd,x:null,X:null,y:ry,Y:rm,Z:rg,"%":rz},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:rx,e:rx,f:rA,g:rB,G:rL,H:rO,I:rw,j:rS,L:rj,m:rP,M:rE,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:r$,s:rF,S:rM,u:rk,U:rT,V:rC,w:rI,W:rD,x:null,X:null,y:rN,Y:rR,Z:rU,"%":rz},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return j(t,e,r,n)},d:e0,e:e0,f:e6,g:eG,G:eV,H:e2,I:e2,j:e1,L:e9,m:eQ,M:e5,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eJ,Q:e8,s:e7,S:e3,u:eX,U:eH,V:eY,w:eq,W:eK,x:function(t,e,n){return j(t,r,e,n)},X:function(t,e,r){return j(t,n,e,r)},y:eG,Y:eV,Z:eZ,"%":e4};function w(t,e){return function(r){var n,i,o,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(i=eB[n=t.charAt(++u)])?n=t.charAt(++u):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function S(t,e){return function(r){var n,i,o=eN(1900,void 0,1);if(j(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=eD(eN(o.y,0,1))).getUTCDay())>4||0===i?eb.ceil(n):eb(n),n=ea.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=eI(eN(o.y,0,1))).getDay())>4||0===i?es.ceil(n):es(n),n=eo.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?eD(eN(o.y,0,1)).getUTCDay():eI(eN(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,eD(o)):eI(o)}}function j(t,e,r,n){for(var i,o,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=O[(i=e.charAt(a++))in eB?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=S(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=S(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,l=u.utcFormat,u.utcParse;var r2=r(94960),r5=r(68391);function r3(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function r9(t,e){return t[e]}function r6(t){let e=[];return e.key=t,e}var r4=r(62442),r8=r.n(r4),r7=r(48176),nt=r.n(r7),ne=r(90671),nr=r.n(ne),nn=r(52780),ni=r.n(nn),no=r(59877),na=r.n(no),nu=r(18504),nc=r.n(nu),nl=r(72699),ns=r.n(nl),nf=r(23485),np=r.n(nf),nh=r(26258),nd=r.n(nh),ny=r(30012),nv=r.n(ny),nm=r(74017),nb=r.n(nm),ng=r(23204),nx=r.n(ng);function nO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nw=function(t){return t},nS={},nj=function(t){return t===nS},nA=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nj(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nP=function(t){return function t(e,r){return 1===e?r:nA(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==nS}).length;return a>=e?r.apply(void 0,i):t(e-a,nA(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return nj(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nO(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return nO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nO(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nE=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nM=nP(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),nk=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nw;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},nT=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},n_=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};nP(function(t,e,r){var n=+t;return n+r*(e-n)}),nP(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),nP(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let nC={rangeStep:function(t,e,r){for(var n=new(nx())(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new(nx())(t).abs().log(10).toNumber())+1}};function nI(t){return function(t){if(Array.isArray(t))return nB(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nD(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==u.return||u.return()}finally{if(i)throw o}}return r}}(t,e)||nN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){if(t){if("string"==typeof t)return nB(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nB(t,e)}}function nB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nR(t){var e=nD(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function nL(t,e,r){if(t.lte(0))return new(nx())(0);var n=nC.getDigitCount(t.toNumber()),i=new(nx())(10).pow(n),o=t.div(i),a=1!==n?.05:.1,u=new(nx())(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?u:new(nx())(Math.ceil(u))}function nU(t,e,r){var n=1,i=new(nx())(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new(nx())(10).pow(nC.getDigitCount(t)-1),i=new(nx())(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new(nx())(Math.floor(t)))}else 0===t?i=new(nx())(Math.floor((e-1)/2)):r||(i=new(nx())(Math.floor(t)));var a=Math.floor((e-1)/2);return nk(nM(function(t){return i.add(new(nx())(t-a).mul(n)).toNumber()}),nE)(0,e)}var nz=n_(function(t){var e=nD(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),u=nD(nR([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(nI(nE(0,i-1).map(function(){return 1/0}))):[].concat(nI(nE(0,i-1).map(function(){return-1/0})),[l]);return r>n?nT(s):s}if(c===l)return nU(c,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new(nx())(0),tickMin:new(nx())(0),tickMax:new(nx())(0)};var u=nL(new(nx())(r).sub(e).div(n-1),i,a),c=Math.ceil((o=e<=0&&r>=0?new(nx())(0):(o=new(nx())(e).add(r).div(2)).sub(new(nx())(o).mod(u))).sub(e).div(u).toNumber()),l=Math.ceil(new(nx())(r).sub(o).div(u).toNumber()),s=c+l+1;return s>n?t(e,r,n,i,a+1):(s<n&&(l=r>0?l+(n-s):l,c=r>0?c:c+(n-s)),{step:u,tickMin:o.sub(new(nx())(c).mul(u)),tickMax:o.add(new(nx())(l).mul(u))})}(c,l,a,o),p=f.step,h=f.tickMin,d=f.tickMax,y=nC.rangeStep(h,d.add(new(nx())(.1).mul(p)),p);return r>n?nT(y):y});n_(function(t){var e=nD(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),u=nD(nR([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return nU(c,i,o);var s=nL(new(nx())(l).sub(c).div(a-1),o,0),f=nk(nM(function(t){return new(nx())(c).add(new(nx())(t).mul(s)).toNumber()}),nE)(0,a).filter(function(t){return t>=c&&t<=l});return r>n?nT(f):f});var n$=n_(function(t,e){var r=nD(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nD(nR([n,i]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,i];if(u===c)return[u];var l=Math.max(e,2),s=nL(new(nx())(c).sub(u).div(l-1),o,0),f=[].concat(nI(nC.rangeStep(new(nx())(u),new(nx())(c).sub(new(nx())(.99).mul(s)),s)),[c]);return n>i?nT(f):f}),nF=r(59049),nW=r(85915),nq=r(56770),nX=r(84663);function nH(t){return(nH="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nY(t){return function(t){if(Array.isArray(t))return nK(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nK(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nK(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nV(Object(r),!0).forEach(function(e){nZ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nZ(t,e,r){var n;return(n=function(t,e){if("object"!=nH(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nH(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nH(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nJ(t,e,r){return nr()(t)||nr()(e)?r:(0,nW.vh)(e)?nc()(t,e,r):ni()(e)?e(t):r}function nQ(t,e,r,n){var i=ns()(t,function(t){return nJ(t,e)});if("number"===r){var o=i.filter(function(t){return(0,nW.Et)(t)||parseFloat(t)});return o.length?[nt()(o),r8()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!nr()(t)}):i).map(function(t){return(0,nW.vh)(t)||t instanceof Date?t:""})}var n0=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var u=i.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,nW.sA)(s-l)!==(0,nW.sA)(f-s)){var h=[];if((0,nW.sA)(f-s)===(0,nW.sA)(u[1]-u[0])){p=f;var d=s+u[1]-u[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+u[1]-u[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},n1=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},n2=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),u=0,c=a.length;u<c;u++)for(var l=i[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,nq.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?nG(nG({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var O=nr()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:nr()(O)?void 0:(0,nW.F4)(O,r,0)})}}return o},n5=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,u=t.maxBarSize,c=a.length;if(c<1)return null;var l=(0,nW.F4)(r,i,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/c,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=i&&(h-=(c-1)*l,l=0),h>=i&&p>0&&(f=!0,p*=.9,h=c*p);var d={offset:((i-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(nY(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,nW.F4)(n,i,0,!0);i-2*y-(c-1)*l<=0&&(l=0);var v=(i-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;e=a.reduce(function(t,e,r){var n=[].concat(nY(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},n3=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,u=o-(a.left||0)-(a.right||0),c=(0,nX.g)({children:i,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,nW.Et)(t[p]))return nG(nG({},t),{},nZ({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,nW.Et)(t[h]))return nG(nG({},t),{},nZ({},h,t[h]+(f||0)))}return t},n9=function(t,e,r,n,i){var o=e.props.children,a=(0,nq.aS)(o,nF.u).filter(function(t){var e;return e=t.props.direction,!!nr()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=nJ(e,r);if(nr()(n))return t;var i=Array.isArray(n)?[nt()(n),r8()(n)]:[n,n],o=u.reduce(function(t,r){var n=nJ(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},n6=function(t,e,r,n,i){var o=e.map(function(e){return n9(t,e,r,i,n)}).filter(function(t){return!nr()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},n4=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&n9(t,e,o,n)||nQ(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},n8=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},n7=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},it=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,nW.sA)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+c,value:t,offset:c}}).filter(function(t){return!np()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+c,value:t,offset:c}}):n.domain().map(function(t,e){return{coordinate:n(t)+c,value:i?i[t]:t,index:e,offset:c}})},ie=new WeakMap,ir=function(t,e){if("function"!=typeof e)return t;ie.has(t)||ie.set(t,new WeakMap);var r=ie.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ii=function(t,e,r){var n=t.scale,i=t.type,o=t.layout,a=t.axisType;if("auto"===n)return"radial"===o&&"radiusAxis"===a?{scale:f.A(),realScaleType:"band"}:"radial"===o&&"angleAxis"===a?{scale:tB(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:f.z(),realScaleType:"point"}:"category"===i?{scale:f.A(),realScaleType:"band"}:{scale:tB(),realScaleType:"linear"};if(na()(n)){var u="scale".concat(nd()(n));return{scale:(s[u]||f.z)(),realScaleType:s[u]?u:"point"}}return ni()(n)?{scale:n}:{scale:f.z(),realScaleType:"point"}},io=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),u=t(e[r-1]);(a<i||a>o||u<i||u>o)&&t.domain([e[0],e[r-1]])}},ia=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},iu=function(t,e){if(!e||2!==e.length||!(0,nW.Et)(e[0])||!(0,nW.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!(0,nW.Et)(t[0])||t[0]<r)&&(i[0]=r),(!(0,nW.Et)(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},ic={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var u=np()(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}r1(t,e)}},none:r1,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;i[n][1]+=i[n][0]=-u/2}r1(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<i;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=o,c&&(o-=l/c)}r[a-1][1]+=r[a-1][0]=o,r1(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=np()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},il=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=ic[r];return(function(){var t=(0,r5.A)([]),e=r3,r=r1,n=r9;function i(i){var o,a,u=Array.from(t.apply(this,arguments),r6),c=u.length,l=-1;for(let t of i)for(o=0,++l;o<c;++o)(u[o][l]=[0,+n(t,u[o].key,l,i)]).data=t;for(o=0,a=(0,r2.A)(e(u));o<c;++o)u[a[o]].index=o;return r(u,a),u}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,r5.A)(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,r5.A)(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?r3:"function"==typeof t?t:(0,r5.A)(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?r1:t,i):r},i})().keys(n).value(function(t,e){return+nJ(t,e,0)}).order(r3).offset(i)(t)},is=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?nG(nG({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var u=o[r],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,nW.vh)(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,nW.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return nG(nG({},t),{},nZ({},u,c))},{});return Object.keys(a).reduce(function(e,o){var u=a[o];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,o){var a=u.stackGroups[o];return nG(nG({},e),{},nZ({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:il(t,a.items,i)}))},{})),nG(nG({},e),{},nZ({},o,u))},{})},ip=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var c=t.domain();if(!c.length)return null;var l=nz(c,i,a);return t.domain([nt()(l),r8()(l)]),{niceTicks:l}}return i&&"number"===n?{niceTicks:n$(t.domain(),i,a)}:null};function ih(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!nr()(i[e.dataKey])){var u=(0,nW.eP)(r,"value",i[e.dataKey]);if(u)return u.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var c=nJ(i,nr()(a)?e.dataKey:a);return nr()(c)?null:e.scale(c)}var id=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=nJ(o,e.dataKey,e.domain[a]);return nr()(u)?null:e.scale(u)-i/2+n},iy=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},iv=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props).stackId;if((0,nW.vh)(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},im=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[nt()(e.concat([t[0]]).filter(nW.Et)),r8()(e.concat([t[1]]).filter(nW.Et))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},ib=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ig=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ix=function(t,e,r){if(ni()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,nW.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(ib.test(t[0])){var i=+ib.exec(t[0])[1];n[0]=e[0]-i}else ni()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,nW.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(ig.test(t[1])){var o=+ig.exec(t[1])[1];n[1]=e[1]+o}else ni()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},iO=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=nb()(e,function(t){return t.coordinate}),o=1/0,a=1,u=i.length;a<u;a++){var c=i[a],l=i[a-1];o=Math.min((c.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},iw=function(t,e,r){return!t||!t.length||nv()(t,nc()(r,"type.defaultProps.domain"))?e:t},iS=function(t,e){var r=t.type.defaultProps?nG(nG({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return nG(nG({},(0,nq.J9)(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:n1(t),value:nJ(e,n),type:u,payload:e,chartType:c,hide:l})}},36676:(t,e,r)=>{var n=r(90417);t.exports=function(){return n.Date.now()}},37193:(t,e,r)=>{"use strict";r.d(e,{f:()=>n});var n=function(t){return null};n.displayName="Cell"},37544:(t,e,r)=>{var n=r(69936),i=r(10608);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},38264:t=>{t.exports=function(){}},39214:(t,e,r)=>{"use strict";r.d(e,{W:()=>c});var n=r(99004),i=r(97921),o=r(56770),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=n.forwardRef(function(t,e){var r=t.children,c=t.className,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,a),s=(0,i.A)("recharts-layer",c);return n.createElement("g",u({className:s},(0,o.J9)(l,!0),{ref:e}),r)})},39598:(t,e,r)=>{"use strict";r.d(e,{c:()=>c});var n=r(99004),i=r(97921),o=r(11751),a=r(56770);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=function(t){var e=t.cx,r=t.cy,c=t.r,l=t.className,s=(0,i.A)("recharts-dot",l);return e===+e&&r===+r&&c===+c?n.createElement("circle",u({},(0,a.J9)(t,!1),(0,o._U)(t),{className:s,cx:e,cy:r,r:c})):null}},40017:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},40942:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{b:()=>n})},41406:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});function n(t,e){if(!t)throw Error("Invariant failed")}},46570:(t,e,r)=>{"use strict";r.d(e,{Z:()=>A});var n=r(99004),i=r(90671),o=r.n(i),a=r(75063),u=r.n(a),c=r(52780),l=r.n(c),s=r(23312),f=r.n(s),p=r(23701),h=r(39214),d=r(56770),y=r(29776);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var j=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function A(t){var e=t.valueAccessor,r=void 0===e?j:e,i=S(t,m),a=i.data,u=i.dataKey,c=i.clockWise,l=i.id,s=i.textBreakAll,f=S(i,b);return a&&a.length?n.createElement(h.W,{className:"recharts-label-list"},a.map(function(t,e){var i=o()(u)?r(t,e):(0,y.kr)(t&&t.payload,u),a=o()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p.J,x({},(0,d.J9)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:i,textBreakAll:s,viewBox:p.J.parseViewBox(o()(c)?t:w(w({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}A.displayName="LabelList",A.renderCallByParent=function(t,e){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var o=t.children,a=(0,d.aS)(o,A).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return i?[(r=t.label,!r?null:!0===r?n.createElement(A,{key:"labelList-implicit",data:e}):n.isValidElement(r)||l()(r)?n.createElement(A,{key:"labelList-implicit",data:e,content:r}):u()(r)?n.createElement(A,x({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return g(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return g(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a}},48062:t=>{t.exports=function(t){return t.split("")}},48176:(t,e,r)=>{var n=r(61495),i=r(52580),o=r(17462);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},49683:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>tj});var n=r(99004),i=r(84586),o=r.n(i),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function s(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var u=t(e,r,n);return i.delete(e),i.delete(r),u}}function f(t){return a(t).concat(u(t))}var p=Object.hasOwn||function(t,e){return c.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),u=t.entries(),c=0;(n=u.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,h=i.value;if(r.equals(p[0],h[0],c,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function O(t,e,r){var n=y(t),i=n.length;if(y(e).length!==i)return!1;for(;i-- >0;)if(!M(t,e,r,n[i]))return!1;return!0}function w(t,e,r){var n,i,o,a=f(t),u=a.length;if(f(e).length!==u)return!1;for(;u-- >0;)if(!M(t,e,r,n=a[u])||(i=d(t,n),o=d(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function S(t,e){return h(t.valueOf(),e.valueOf())}function j(t,e){return t.source===e.source&&t.flags===e.flags}function A(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),u=t.values();(n=u.next())&&!n.done;){for(var c=e.values(),l=!1,s=0;(i=c.next())&&!i.done;){if(!a[s]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function P(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function E(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function M(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var k=Array.isArray,T="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,_=Object.assign,C=Object.prototype.toString.call.bind(Object.prototype.toString),I=D();function D(t){void 0===t&&(t={});var e,r,n,i,o,a,u,c,f,p,d,y,M,I=t.circular,D=t.createInternalComparator,N=t.createState,B=t.strict,R=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?w:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,w):x,areNumbersEqual:h,areObjectsEqual:n?w:O,arePrimitiveWrappersEqual:S,areRegExpsEqual:j,areSetsEqual:n?l(A,w):A,areTypedArraysEqual:n?w:P,areUrlsEqual:E};if(r&&(i=_({},i,r(i))),e){var o=s(i.areArraysEqual),a=s(i.areMapsEqual),u=s(i.areObjectsEqual),c=s(i.areSetsEqual);i=_({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,u=e.areNumbersEqual,c=e.areObjectsEqual,f=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,d=e.areSetsEqual,y=e.areTypedArraysEqual,M=e.areUrlsEqual,function(t,e,l){if(t===e)return!0;if(null==t||null==e)return!1;var s=typeof t;if(s!==typeof e)return!1;if("object"!==s)return"number"===s?u(t,e,l):"function"===s&&o(t,e,l);var h=t.constructor;if(h!==e.constructor)return!1;if(h===Object)return c(t,e,l);if(k(t))return r(t,e,l);if(null!=T&&T(t))return y(t,e,l);if(h===Date)return n(t,e,l);if(h===RegExp)return p(t,e,l);if(h===Map)return a(t,e,l);if(h===Set)return d(t,e,l);var v=C(t);return"[object Date]"===v?n(t,e,l):"[object RegExp]"===v?p(t,e,l):"[object Map]"===v?a(t,e,l):"[object Set]"===v?d(t,e,l):"[object Object]"===v?"function"!=typeof t.then&&"function"!=typeof e.then&&c(t,e,l):"[object URL]"===v?M(t,e,l):"[object Error]"===v?i(t,e,l):"[object Arguments]"===v?c(t,e,l):("[object Boolean]"===v||"[object Number]"===v||"[object String]"===v)&&f(t,e,l)}),L=D?D(R):function(t,e,r,n,i,o,a){return R(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var u=n(),c=u.cache;return r(t,a,{cache:void 0===c?e?new WeakMap:void 0:c,equals:i,meta:u.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==I&&I,comparator:R,createState:N,equals:L,strict:void 0!==B&&B})}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function $(t,e,r){var n;return(n=function(t,e){if("object"!==L(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===L(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}D({strict:!0}),D({circular:!0}),D({circular:!0,strict:!0}),D({createInternalComparator:function(){return h}}),D({strict:!0,createInternalComparator:function(){return h}}),D({circular:!0,createInternalComparator:function(){return h}}),D({circular:!0,createInternalComparator:function(){return h},strict:!0});var F=function(t){return t},W=function(t,e){return Object.keys(e).reduce(function(r,n){return z(z({},r),{},$({},n,t(n,e[n])))},{})},q=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},X=function(t,e,r,n,i,o,a,u){};function H(t,e){if(t){if("string"==typeof t)return Y(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Y(t,e)}}function Y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var K=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},V=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},G=function(t,e){return function(r){return V(K(t,e),r)}},Z=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,u=1,c=1;break;case"ease":o=.25,a=.1,u=.25,c=1;break;case"ease-in":o=.42,a=0,u=1,c=1;break;case"ease-out":o=.42,a=0,u=.58,c=1;break;case"ease-in-out":o=0,a=0,u=.58,c=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(s,4)||H(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],u=f[2],c=f[3]}else X(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}X([o,u,a,c].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=G(o,u),h=G(a,c),d=(t=o,e=u,function(r){var n;return V([].concat(function(t){if(Array.isArray(t))return Y(t)}(n=K(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||H(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=d(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return h(r)};return y.isStepper=!1,y},J=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,u=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,u=n*a/1e3+t;return 1e-4>Math.abs(u-e)&&1e-4>Math.abs(o)?[e,0]:[u,o]};return u.isStepper=!0,u.dt=a,u},Q=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Z(n);case"spring":return J();default:if("cubic-bezier"===n.split("(")[0])return Z(n);X(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(X(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(t){return function(t){if(Array.isArray(t))return ta(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||to(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){ti(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ti(t,e,r){var n;return(n=function(t,e){if("object"!==tt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===tt(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function to(t,e){if(t){if("string"==typeof t)return ta(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ta(t,e)}}function ta(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tu=function(t,e,r){return t+(e-t)*r},tc=function(t){return t.from!==t.to},tl=function t(e,r,n){var i=W(function(t,r){if(tc(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(n,2)||to(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return tn(tn({},r),{},{from:o,velocity:a})}return r},r);return n<1?W(function(t,e){return tc(e)?tn(tn({},e),{},{velocity:tu(e.velocity,i[t].velocity,n),from:tu(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let ts=function(t,e,r,n,i){var o,a,u=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),c=u.reduce(function(r,n){return tn(tn({},r),{},ti({},n,[t[n],e[n]]))},{}),l=u.reduce(function(r,n){return tn(tn({},r),{},ti({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;l=tl(r,l,a),i(tn(tn(tn({},t),e),W(function(t,e){return e.from},l))),o=n,Object.values(l).filter(tc).length&&(s=requestAnimationFrame(f))}:function(o){a||(a=o);var u=(o-a)/n,l=W(function(t,e){return tu.apply(void 0,te(e).concat([r(u)]))},c);if(i(tn(tn(tn({},t),e),l)),u<1)s=requestAnimationFrame(f);else{var p=W(function(t,e){return tu.apply(void 0,te(e).concat([r(1)]))},c);i(tn(tn(tn({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tf(t){return(tf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tp=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function th(t){return function(t){if(Array.isArray(t))return td(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return td(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return td(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function td(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ty(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ty(Object(r),!0).forEach(function(e){tm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ty(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tm(t,e,r){return(e=tb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tb(t){var e=function(t,e){if("object"!==tf(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tf(e)?e:String(e)}function tg(t,e){return(tg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tx(t,e){if(e&&("object"===tf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tO(t)}function tO(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tw(t){return(tw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tS=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&tg(o,t);var e,r,i=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tw(o);return t=e?Reflect.construct(r,arguments,tw(this).constructor):r.apply(this,arguments),tx(this,t)});function o(t,e){if(!(this instanceof o))throw TypeError("Cannot call a class as a function");var r=i.call(this,t,e),n=r.props,a=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tO(r)),r.changeStyle=r.changeStyle.bind(tO(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tx(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"==typeof f)return r.state={style:c},tx(r);r.state={style:u?tm({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n){if(!r){var l={style:i?tm({},i,a):a};this.state&&c&&(i&&c[i]!==a||!i&&c!==a)&&this.setState(l);return}if(!I(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||o?u:t.to;if(this.state&&c){var p={style:i?tm({},i,f):f};(i&&c[i]!==f||!i&&c!==f)&&this.setState(p)}this.runAnimation(tv(tv({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=ts(r,n,Q(o),i,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},i,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,u=o.duration;return this.manager.start([i].concat(th(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=i>0?r[i-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(th(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:o,easing:u}),o]);var h=q(p,o,u),d=tv(tv(tv({},f.style),c),{},{transition:h});return[].concat(th(t),[d,o,s]).filter(F)},[a,Math.max(void 0===u?0:u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return R(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void N(t.bind(null,a),o):(t(o),void N(t.bind(null,a)))}"object"===B(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,u=t.attributeName,c=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=u?tm({},u,c):c,v=q(Object.keys(y),a,l);d.start([s,o,tv(tv({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),i=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tp)),a=n.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!i||0===a||r<=0)return e;var c=function(t){var e=t.props,r=e.style,i=e.className;return(0,n.cloneElement)(t,tv(tv({},o),{},{style:tv(tv({},void 0===r?{}:r),u),className:i}))};return 1===a?c(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tb(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);tS.displayName="Animate",tS.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tS.propTypes={from:o().oneOfType([o().object,o().string]),to:o().oneOfType([o().object,o().string]),attributeName:o().string,duration:o().number,begin:o().number,easing:o().oneOfType([o().string,o().func]),steps:o().arrayOf(o().shape({duration:o().number.isRequired,style:o().object.isRequired,easing:o().oneOfType([o().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),o().func]),properties:o().arrayOf("string"),onAnimationEnd:o().func})),children:o().oneOfType([o().node,o().func]),isActive:o().bool,canBegin:o().bool,onAnimationEnd:o().func,shouldReAnimate:o().bool,onAnimationStart:o().func,onAnimationReStart:o().func};let tj=tS},49916:(t,e,r)=>{"use strict";r.d(e,{s:()=>u});var n=r(8528),i=r.n(n),o=r(52780),a=r.n(o);function u(t,e,r){return!0===e?i()(t,r):a()(e)?i()(t,e):t}},50980:(t,e,r)=>{var n=r(90933);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},52580:t=>{t.exports=function(t,e){return t<e}},52885:(t,e,r)=>{var n=r(57548),i=r(20830),o=r(23933),a=r(12436);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,u=r?r[0]:e.charAt(0),c=r?n(r,1).join(""):e.slice(1);return u[t]()+c}}},53033:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},53541:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},54483:(t,e,r)=>{var n=r(90933);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},56770:(t,e,r)=>{"use strict";r.d(e,{AW:()=>L,BU:()=>M,J9:()=>I,Me:()=>k,Mn:()=>S,OV:()=>D,X_:()=>R,aS:()=>E,ee:()=>B,sT:()=>_});var n=r(18504),i=r.n(n),o=r(90671),a=r.n(o),u=r(59877),c=r.n(u),l=r(52780),s=r.n(l),f=r(75063),p=r.n(f),h=r(99004),d=r(17706),y=r(85915),v=r(40942),m=r(11751),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},S=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},j=null,A=null,P=function t(e){if(e===j&&Array.isArray(A))return A;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),A=r,j=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return S(t)}):[S(e)],P(t).forEach(function(t){var e=i()(t,"type.displayName")||i()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function M(t,e){var r=E(t,e);return r&&r[0]}var k=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.Et)(r)&&!(r<=0)&&!!(0,y.Et)(n)&&!(n<=0)},T=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_=function(t){return t&&"object"===O(t)&&"clipDot"in t},C=function(t,e,r,n){var i,o=null!=(i=null===m.VU||void 0===m.VU?void 0:m.VU[n])?i:[];return!s()(t)&&(n&&o.includes(e)||m.QQ.includes(e))||r&&m.j2.includes(e)},I=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;C(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},D=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var o=e[i],a=r[i];if(Array.isArray(o)||Array.isArray(a)){if(!t(o,a))return!1}else if(!N(o,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,i=x(r,b),o=e.props||{},u=o.children,c=x(o,g);if(n&&u)return(0,v.b)(i,c)&&D(n,u);if(!n&&!u)return(0,v.b)(i,c)}return!1},B=function(t,e){var r=[],n={};return P(t).forEach(function(t,i){var o;if((o=t)&&o.type&&c()(o.type)&&T.indexOf(o.type)>=0)r.push(t);else if(t){var a=S(t.type),u=e[a]||{},l=u.handler,s=u.once;if(l&&(!s||!n[a])){var f=l(t,a,i);r.push(f),n[a]=!0}}}),r},R=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},L=function(t,e){return P(e).indexOf(t)}},57548:(t,e,r)=>{var n=r(7758);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},57990:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case u:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case c:return t;default:return e}}case i:return e}}}(t)===o}},59049:(t,e,r)=>{"use strict";r.d(e,{u:()=>v});var n=r(99004),i=r(41406),o=r(39214),a=r(56770),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}var v=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=p(t),function(t,e){if(e&&("object"===c(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f()?Reflect.construct(t,e||[],p(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&h(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,c=t.width,f=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,u),m=(0,a.J9)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,i.A)(!1);var b=p.map(function(t){var i,a,u=h(t,f),p=u.x,v=u.y,b=u.value,g=u.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(g,2)||function(t,e){if(t){if("string"==typeof t)return s(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=O[0],a=O[1]}else i=a=g;if("vertical"===r){var w=d.scale,S=v+e,j=S+c,A=S-c,P=w(b-i),E=w(b+a);x.push({x1:E,y1:j,x2:E,y2:A}),x.push({x1:P,y1:S,x2:E,y2:S}),x.push({x1:P,y1:j,x2:P,y2:A})}else if("horizontal"===r){var M=y.scale,k=p+e,T=k-c,_=k+c,C=M(b-i),I=M(b+a);x.push({x1:T,y1:I,x2:_,y2:I}),x.push({x1:k,y1:C,x2:k,y2:I}),x.push({x1:T,y1:C,x2:_,y2:C})}return n.createElement(o.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(o.W,{className:"recharts-errorBars"},b)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);d(v,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),d(v,"displayName","ErrorBar")},59877:(t,e,r)=>{var n=r(69936),i=r(22461),o=r(10608);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},61476:(t,e,r)=>{"use strict";r.d(e,{A:()=>o,z:()=>a});var n=r(84082),i=r(11428);function o(){var t,e,r=(0,i.A)().unknown(void 0),a=r.domain,u=r.range,c=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<c,i=n?l:c,o=n?c:l;t=(o-i)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),i+=(o-i-t*(r-f))*h,e=t*(1-f),s&&(i=Math.round(i),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return i+t*e});return u(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([c,l]=t,c*=1,l*=1,d()):[c,l]},r.rangeRound=function(t){return[c,l]=t,c*=1,l*=1,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return o(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.C.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(o.apply(null,arguments).paddingInner(1))}},61495:(t,e,r)=>{var n=r(31768);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],u=e(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,l=a}return l}},61549:(t,e,r)=>{var n=r(7353),i=r(29573),o=r(17557);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},61563:(t,e,r)=>{var n=r(61549);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},62442:(t,e,r)=>{var n=r(61495),i=r(14499),o=r(17462);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},63111:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(53033);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,o,a;n=t,o=e,a=r[e],(o=function(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var c={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",f=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:i});if(c.widthCache[o])return c.widthCache[o];try{var u=document.getElementById(s);u||((u=document.createElement("span")).setAttribute("id",s),u.setAttribute("aria-hidden","true"),document.body.appendChild(u));var f=a(a({},l),i);Object.assign(u.style,f),u.textContent="".concat(t);var p=u.getBoundingClientRect(),h={width:p.width,height:p.height};return c.widthCache[o]=h,++c.cacheCount>2e3&&(c.cacheCount=0,c.widthCache={}),h}catch(t){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},63295:(t,e,r)=>{"use strict";r.d(e,{IZ:()=>v,Kg:()=>y,Zk:()=>S,lY:()=>m,pr:()=>b,yy:()=>w});var n=r(90671),i=r.n(n),o=r(99004),a=r(52780),u=r.n(a),c=r(85915),l=r(29776);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){var n;return(n=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=Math.PI/180,v=function(t,e,r,n){return{x:t+Math.cos(-y*n)*r,y:e+Math.sin(-y*n)*r}},m=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},b=function(t,e,r,n,o){var a=t.width,u=t.height,s=t.startAngle,f=t.endAngle,y=(0,c.F4)(t.cx,a,a/2),v=(0,c.F4)(t.cy,u,u/2),b=m(a,u,r),g=(0,c.F4)(t.innerRadius,b,0),x=(0,c.F4)(t.outerRadius,b,.8*b);return Object.keys(e).reduce(function(t,r){var a,u=e[r],c=u.domain,m=u.reversed;if(i()(u.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[g,x]),m&&(a=[a[1],a[0]]);else{var b,O=function(t){if(Array.isArray(t))return t}(b=a=u.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(b,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=O[0],f=O[1]}var w=(0,l.W7)(u,o),S=w.realScaleType,j=w.scale;j.domain(c).range(a),(0,l.YB)(j);var A=(0,l.w7)(j,p(p({},u),{},{realScaleType:S})),P=p(p(p({},u),A),{},{range:a,radius:x,realScaleType:S,scale:j,cx:y,cy:v,innerRadius:g,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},h({},r,P))},{})},g=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},x=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=g({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var u=Math.acos((r-i)/a);return n>o&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},O=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},w=function(t,e){var r,n=x({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,u=e.outerRadius;if(i<a||i>u)return!1;if(0===i)return!0;var c=O(e),l=c.startAngle,s=c.endAngle,f=o;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?p(p({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},S=function(t){return(0,o.isValidElement)(t)||u()(t)||"boolean"==typeof t?"":t.className}},66394:(t,e,r)=>{var n=r(44809),i=r(99730),o=r(50632);t.exports=function(t){return function(e,r,a){var u=Object(e);if(!i(e)){var c=n(r,3);e=o(e),r=function(t){return c(u[t],t,u)}}var l=t(e,r,a);return l>-1?u[c?e[l]:l]:void 0}}},66644:(t,e,r)=>{"use strict";r.d(e,{y:()=>z});var n=r(99004),i=r(97921),o=r(49683),a=r(30012),u=r.n(a),c=r(90671),l=r.n(c),s=r(39214),f=r(59049),p=r(37193),h=r(46570),d=r(85915),y=r(56770),v=r(53033),m=r(29776),b=r(11751),g=r(41406),x=r(91516),O=["x","y"];function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(){return(S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,O),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(e.height||i.height),10),c=parseInt("".concat(e.width||i.width),10);return A(A(A(A(A({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:u,width:c,name:e.name,radius:e.radius})}function E(t){return n.createElement(x.yp,S({shapeType:"rectangle",propTransformer:P,activeClassName:"recharts-active-bar"},t))}var M=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||(0,g.A)(!1),e)}},k=["value","background"];function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _(){return(_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function I(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach(function(e){L(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function D(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,U(n.key),n)}}function N(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(N=function(){return!!t})()}function B(t){return(B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function R(t,e){return(R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function L(t,e,r){return(e=U(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function U(t){var e=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==T(e)?e:e+""}var z=function(t){var e,r;function a(){var t,e,r;if(!(this instanceof a))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e=a,r=[].concat(i),e=B(e),L(t=function(t,e){if(e&&("object"===T(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,N()?Reflect.construct(e,r||[],B(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),L(t,"id",(0,d.NF)("recharts-bar-")),L(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),L(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return a.prototype=Object.create(t&&t.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),t&&R(a,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,i=r.shape,o=r.dataKey,a=r.activeIndex,u=r.activeBar,c=(0,y.J9)(this.props,!1);return t&&t.map(function(t,r){var l=r===a,f=I(I(I({},c),t),{},{isActive:l,option:l?u:i,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(s.W,_({className:"recharts-bar-rectangle"},(0,b.XC)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),n.createElement(E,f))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,i=e.layout,a=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,l=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(o.Ay,{begin:u,duration:c,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var n=(0,d.Dj)(r.x,t.x),a=(0,d.Dj)(r.y,t.y),u=(0,d.Dj)(r.width,t.width),c=(0,d.Dj)(r.height,t.height);return I(I({},t),{},{x:n(o),y:a(o),width:u(o),height:c(o)})}if("horizontal"===i){var l=(0,d.Dj)(0,t.height)(o);return I(I({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,d.Dj)(0,t.width)(o);return I(I({},t),{},{width:s})});return n.createElement(s.W,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!u()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,i=e.dataKey,o=e.activeIndex,a=(0,y.J9)(this.props.background,!1);return r.map(function(e,r){e.value;var u=e.background,c=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,k);if(!u)return null;var l=I(I(I(I(I({},c),{},{fill:"#eee"},u),a),(0,b.XC)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(E,_({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,i=r.data,o=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,l=(0,y.aS)(c,f.u);if(!l)return null;var p="vertical"===u?i[0].height/2:i[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,m.kr)(t,e)}};return n.createElement(s.W,{clipPath:t?"url(#clipPath-".concat(e,")"):null},l.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:i,xAxis:o,yAxis:a,layout:u,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,o=t.className,a=t.xAxis,u=t.yAxis,c=t.left,f=t.top,p=t.width,d=t.height,y=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,i.A)("recharts-bar",o),x=a&&a.allowDataOverflow,O=u&&u.allowDataOverflow,w=x||O,S=l()(m)?this.id:m;return n.createElement(s.W,{className:g},x||O?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(S)},n.createElement("rect",{x:x?c:c-p/2,y:O?f:f-d/2,width:x?p:2*p,height:O?d:2*d}))):null,n.createElement(s.W,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(S,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,S),(!y||b)&&h.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&D(a.prototype,e),r&&D(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);L(z,"displayName","Bar"),L(z,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),L(z,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,h=t.offset,v=(0,m.xi)(n,r);if(!v)return null;var b=e.layout,g=r.type.defaultProps,x=void 0!==g?I(I({},g),r.props):r.props,O=x.dataKey,w=x.children,S=x.minPointSize,j="horizontal"===b?a:o,A=l?j.scale.domain():null,P=(0,m.DW)({numericAxis:j}),E=(0,y.aS)(w,p.f),k=f.map(function(t,e){l?f=(0,m._f)(l[s+e],A):Array.isArray(f=(0,m.kr)(t,O))||(f=[P,f]);var n=M(S,z.defaultProps.minPointSize)(f[1],e);if("horizontal"===b){var f,p,h,y,g,x,w,j=[a.scale(f[0]),a.scale(f[1])],k=j[0],T=j[1];p=(0,m.y2)({axis:o,ticks:u,bandSize:i,offset:v.offset,entry:t,index:e}),h=null!=(w=null!=T?T:k)?w:void 0,y=v.size;var _=k-T;if(g=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:y,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var C=(0,d.sA)(g||n)*(Math.abs(n)-Math.abs(g));h-=C,g+=C}}else{var D=[o.scale(f[0]),o.scale(f[1])],N=D[0],B=D[1];if(p=N,h=(0,m.y2)({axis:a,ticks:c,bandSize:i,offset:v.offset,entry:t,index:e}),y=B-N,g=v.size,x={x:o.x,y:h,width:o.width,height:g},Math.abs(n)>0&&Math.abs(y)<Math.abs(n)){var R=(0,d.sA)(y||n)*(Math.abs(n)-Math.abs(y));y+=R}}return I(I(I({},t),{},{x:p,y:h,width:y,height:g,value:l?f:f[1],payload:t,background:x},E&&E[e]&&E[e].props),{},{tooltipPayload:[(0,m.zb)(r,t)],tooltipPosition:{x:p+y/2,y:h+g/2}})});return I({data:k,layout:b},h)})},67892:(t,e,r)=>{"use strict";r.d(e,{m:()=>F});var n=r(99004),i=r(74017),o=r.n(i),a=r(90671),u=r.n(a),c=r(97921),l=r(85915);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t){return Array.isArray(t)&&(0,l.vh)(t[0])&&(0,l.vh)(t[1])?t.join(" ~ "):t}var v=function(t){var e=t.separator,r=void 0===e?" : ":e,i=t.contentStyle,a=t.itemStyle,s=void 0===a?{}:a,h=t.labelStyle,v=t.payload,m=t.formatter,b=t.itemSorter,g=t.wrapperClassName,x=t.labelClassName,O=t.label,w=t.labelFormatter,S=t.accessibilityLayer,j=d({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===i?{}:i),A=d({margin:0},void 0===h?{}:h),P=!u()(O),E=P?O:"",M=(0,c.A)("recharts-default-tooltip",g),k=(0,c.A)("recharts-tooltip-label",x);return P&&w&&null!=v&&(E=w(O,v)),n.createElement("div",f({className:M,style:j},void 0!==S&&S?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:k,style:A},n.isValidElement(E)?E:"".concat(E)),function(){if(v&&v.length){var t=(b?o()(v,b):v).map(function(t,e){if("none"===t.type)return null;var i=d({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},s),o=t.formatter||m||y,a=t.value,u=t.name,c=a,f=u;if(o&&null!=c&&null!=f){var h=o(a,u,t,e,v);if(Array.isArray(h)){var b=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(h,2)||function(t,e){if(t){if("string"==typeof t)return p(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=b[0],f=b[1]}else c=h}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:i},(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,l.vh)(f)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(t,e,r){var n;return(n=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var g="recharts-tooltip-wrapper",x={visibility:"hidden"};function O(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,s=t.viewBoxDimension;if(o&&(0,l.Et)(o[n]))return o[n];var f=r[n]-u-i,p=r[n]+i;return e[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+s?Math.max(f,c[n]):Math.max(p,c[n])}function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){M(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(A=function(){return!!t})()}function P(t){return(P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function E(t,e){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function M(t,e,r){return(e=k(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function k(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}var T=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=P(e),M(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,A()?Reflect.construct(e,n||[],P(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),M(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&E(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,i,o,a,u,s,f,p,h,d,y,v,m,w,S,A,P,E=this,M=this.props,k=M.active,T=M.allowEscapeViewBox,_=M.animationDuration,C=M.animationEasing,I=M.children,D=M.coordinate,N=M.hasPayload,B=M.isAnimationActive,R=M.offset,L=M.position,U=M.reverseDirection,z=M.useTranslate3d,$=M.viewBox,F=M.wrapperStyle,W=(d=(t={allowEscapeViewBox:T,coordinate:D,offsetTopLeft:R,position:L,reverseDirection:U,tooltipBox:this.state.lastBoundingBox,useTranslate3d:z,viewBox:$}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,w=t.reverseDirection,S=t.tooltipBox,A=t.useTranslate3d,P=t.viewBox,S.height>0&&S.width>0&&y?(r=(e={translateX:p=O({allowEscapeViewBox:d,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:S.width,viewBox:P,viewBoxDimension:P.width}),translateY:h=O({allowEscapeViewBox:d,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:S.height,viewBox:P,viewBoxDimension:P.height}),useTranslate3d:A}).translateX,i=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(i,"px, 0)"):"translate(".concat(r,"px, ").concat(i,"px)")}):f=x,{cssProperties:f,cssClasses:(a=(o={translateX:p,translateY:h,coordinate:y}).coordinate,u=o.translateX,s=o.translateY,(0,c.A)(g,b(b(b(b({},"".concat(g,"-right"),(0,l.Et)(u)&&a&&(0,l.Et)(a.x)&&u>=a.x),"".concat(g,"-left"),(0,l.Et)(u)&&a&&(0,l.Et)(a.x)&&u<a.x),"".concat(g,"-bottom"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s>=a.y),"".concat(g,"-top"),(0,l.Et)(s)&&a&&(0,l.Et)(a.y)&&s<a.y)))}),q=W.cssClasses,X=W.cssProperties,H=j(j({transition:B&&k?"transform ".concat(_,"ms ").concat(C):void 0},X),{},{pointerEvents:"none",visibility:!this.state.dismissed&&k&&N?"visible":"hidden",position:"absolute",top:0,left:0},F);return n.createElement("div",{tabIndex:-1,className:q,style:H,ref:function(t){E.wrapperNode=t}},I)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,k(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),_=r(53033),C=r(49916);function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function D(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function N(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?D(Object(r),!0).forEach(function(e){U(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):D(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function B(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(B=function(){return!!t})()}function R(t){return(R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function L(t,e){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function U(t,e,r){return(e=z(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function z(t){var e=function(t,e){if("object"!=I(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=I(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==I(e)?e:e+""}function $(t){return t.dataKey}var F=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=R(t),function(t,e){if(e&&("object"===I(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,B()?Reflect.construct(t,e||[],R(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&L(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,i=r.active,o=r.allowEscapeViewBox,a=r.animationDuration,u=r.animationEasing,c=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,m=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,O=null!=h?h:[];s&&O.length&&(O=(0,C.s)(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,$));var w=O.length>0;return n.createElement(T,{allowEscapeViewBox:o,animationDuration:a,animationEasing:u,isAnimationActive:f,active:i,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:m,useTranslate3d:b,viewBox:g,wrapperStyle:x},(t=N(N({},this.props),{},{payload:O}),n.isValidElement(c)?n.cloneElement(c,t):"function"==typeof c?n.createElement(c,t):n.createElement(v,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,z(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);U(F,"displayName","Tooltip"),U(F,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!_.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},68391:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},71991:(t,e,r)=>{t.exports=r(66394)(r(64497))},72473:(t,e,r)=>{var n=r(32783),i=r(61563),o=r(79607),a=r(10875),u=r(28359),c=r(99339);t.exports=function(t,e,r){var l=-1,s=i,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=o;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},72699:(t,e,r)=>{var n=r(58034),i=r(81298);t.exports=function(t,e){return n(i(t,e),1)}},77849:(t,e,r)=>{var n=r(69936),i=r(10608);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},78015:(t,e,r)=>{var n=r(75063),i=r(36676),o=r(72048),a=Math.max,u=Math.min;t.exports=function(t,e,r){var c,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=c,n=l;return c=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,o=i();if(g(o))return O(o);p=setTimeout(x,(t=o-h,r=o-d,n=e-t,v?u(n,s-r):n))}function O(t){return(p=void 0,m&&c)?b(t):(c=l=void 0,f)}function w(){var t,r=i(),n=g(r);if(c=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(o(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,c=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(i())},w}},78184:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new i(n,o||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,i,o,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var u=this._events[o];if(u.fn)u.fn!==e||i&&!u.once||n&&u.context!==n||a(this,o);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||i&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},79020:(t,e,r)=>{"use strict";r.d(e,{u:()=>c});var n=r(99004),i=r(97921),o=r(56770),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function c(t){var e=t.children,r=t.width,c=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,a),y=l||{width:r,height:c,x:0,y:0},v=(0,i.A)("recharts-surface",s);return n.createElement("svg",u({},(0,o.J9)(d,!0,"svg"),{className:v,width:r,height:c,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),n.createElement("title",null,p),n.createElement("desc",null,h),e)}},79607:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},80760:(t,e,r)=>{"use strict";r.d(e,{J:()=>h,M:()=>y});var n=r(99004),i=r(97921),o=r(49683),a=r(56770);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),o+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),o+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),o+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,u=e.height;if(Math.abs(a)>0&&Math.abs(u)>0){var c=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+u),f=Math.max(o,o+u);return r>=c&&r<=l&&n>=s&&n<=f}return!1},d={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,r=f(f({},d),t),u=(0,n.useRef)(),s=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return l(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=s[0],y=s[1];(0,n.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var t=u.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=r.x,m=r.y,b=r.width,g=r.height,x=r.radius,O=r.className,w=r.animationEasing,S=r.animationDuration,j=r.animationBegin,A=r.isAnimationActive,P=r.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var E=(0,i.A)("recharts-rectangle",O);return P?n.createElement(o.Ay,{canBegin:h>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:S,animationEasing:w,isActive:P},function(t){var e=t.width,i=t.height,l=t.x,s=t.y;return n.createElement(o.Ay,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:S,isActive:A,easing:w},n.createElement("path",c({},(0,a.J9)(r,!0),{className:E,d:p(l,s,e,i,x),ref:u})))}):n.createElement("path",c({},(0,a.J9)(r,!0),{className:E,d:p(v,m,b,g,x)}))}},81298:(t,e,r)=>{var n=r(72668),i=r(44809),o=r(55738),a=r(22461);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},82701:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(99004),i=r(97921),o=r(56770),a=r(63295),u=r(85915);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(u?1:-1)+n,f=Math.asin(c/s)/a.Kg,p=l?i:i+o*f;return{center:(0,a.IZ)(e,r,s,p),circleTangency:(0,a.IZ)(e,r,n,p),lineTangency:(0,a.IZ)(e,r,s*Math.cos(f*a.Kg),l?i-o*f:i),theta:f}},h=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,c=t.endAngle,l=(0,u.sA)(c-o)*Math.min(Math.abs(c-o),359.999),s=o+l,f=(0,a.IZ)(e,r,i,o),p=(0,a.IZ)(e,r,i,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(o>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(n>0){var d=(0,a.IZ)(e,r,n,o),y=(0,a.IZ)(e,r,n,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(o<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(e,",").concat(r," Z");return h},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,u.sA)(s-l),d=p({cx:e,cy:r,radius:i,angle:l,sign:f,cornerRadius:o,cornerIsExternal:c}),y=d.circleTangency,v=d.lineTangency,m=d.theta,b=p({cx:e,cy:r,radius:i,angle:s,sign:-f,cornerRadius:o,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=c?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:l,endAngle:s});var S="M ".concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var j=p({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=j.circleTangency,P=j.lineTangency,E=j.theta,M=p({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),k=M.circleTangency,T=M.lineTangency,_=M.theta,C=c?Math.abs(l-s):Math.abs(l-s)-E-_;if(C<0&&0===o)return"".concat(S,"L").concat(e,",").concat(r,"Z");S+="L".concat(T.x,",").concat(T.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(A.x,",").concat(A.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else S+="L".concat(e,",").concat(r,"Z");return S},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,r=f(f({},y),t),a=r.cx,c=r.cy,s=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(p<s||g===x)return null;var w=(0,i.A)("recharts-sector",O),S=p-s,j=(0,u.F4)(v,S,0,!0);return e=j>0&&360>Math.abs(g-x)?d({cx:a,cy:c,innerRadius:s,outerRadius:p,cornerRadius:Math.min(j,S/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):h({cx:a,cy:c,innerRadius:s,outerRadius:p,startAngle:g,endAngle:x}),n.createElement("path",l({},(0,o.J9)(r,!0),{className:w,d:e,role:"img"}))}},83319:(t,e,r)=>{"use strict";r.d(e,{I:()=>Y});var n=r(99004);function i(){}function o(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:o(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:o(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function h(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function d(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,u=(o-n)/3;t._context.bezierCurveTo(n+u,i+u*e,o-u,a-u*r,o,a)}function v(t){this._context=t}function m(t){this._context=new b(t)}function b(t){this._context=t}function g(t){this._context=t}function x(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function O(t,e){this._context=t,this._t=e}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,r=h(this,t,e)),r);break;default:y(this,this._t0,r=h(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},b.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),i=x(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var w=r(94960),S=r(68391),j=r(3921);function A(t){return t[0]}function P(t){return t[1]}function E(t,e){var r=(0,S.A)(!0),n=null,i=p,o=null,a=(0,j.i)(u);function u(u){var c,l,s,f=(u=(0,w.A)(u)).length,p=!1;for(null==n&&(o=i(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(l,c,u),+e(l,c,u));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?A:(0,S.A)(t),e="function"==typeof e?e:void 0===e?P:(0,S.A)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,S.A)(!!t),u):r},u.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),u):i},u.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),u):n},u}function M(t,e,r){var n=null,i=(0,S.A)(!0),o=null,a=p,u=null,c=(0,j.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,w.A)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(u=a(d=c())),s=0;s<=y;++s){if(!(s<y&&i(h=l[s],s,l))===v)if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),u.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return u=null,d+""||null}function s(){return E().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?A:(0,S.A)(+t),e="function"==typeof e?e:void 0===e?(0,S.A)(0):(0,S.A)(+e),r="function"==typeof r?r:void 0===r?P:(0,S.A)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,S.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,S.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,S.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,S.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:(0,S.A)(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(u=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=u=null:u=a(o=t),l):o},l}var k=r(26258),T=r.n(k),_=r(52780),C=r.n(_),I=r(97921),D=r(11751),N=r(56770),B=r(85915);function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(){return(L=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=R(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=R(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==R(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var $={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new g(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},F=function(t){return t.x===+t.x&&t.y===+t.y},W=function(t){return t.x},q=function(t){return t.y},X=function(t,e){if(C()(t))return t;var r="curve".concat(T()(t));return("curveMonotone"===r||"curveBump"===r)&&e?$["".concat(r).concat("vertical"===e?"Y":"X")]:$[r]||p},H=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,u=t.connectNulls,c=void 0!==u&&u,l=X(void 0===r?"linear":r,a),s=c?i.filter(function(t){return F(t)}):i;if(Array.isArray(o)){var f=c?o.filter(function(t){return F(t)}):o,p=s.map(function(t,e){return z(z({},t),{},{base:f[e]})});return(e="vertical"===a?M().y(q).x1(W).x0(function(t){return t.base.x}):M().x(W).y1(q).y0(function(t){return t.base.y})).defined(F).curve(l),e(p)}return(e="vertical"===a&&(0,B.Et)(o)?M().y(q).x1(W).x0(o):(0,B.Et)(o)?M().x(W).y1(q).y0(o):E().x(W).y(q)).defined(F).curve(l),e(s)},Y=function(t){var e=t.className,r=t.points,i=t.path,o=t.pathRef;if((!r||!r.length)&&!i)return null;var a=r&&r.length?H(t):i;return n.createElement("path",L({},(0,N.J9)(t,!1),(0,D._U)(t),{className:(0,I.A)("recharts-curve",e),d:a,ref:o}))}},84082:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function i(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>i})},84663:(t,e,r)=>{"use strict";r.d(e,{g:()=>l});var n=r(92942),i=r(29776),o=r(56770);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,l=t.legendContent,s=(0,o.BU)(r,n.s);if(!s)return null;var f=n.s.defaultProps,p=void 0!==f?c(c({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?c(c({},r),e.props):{},o=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:o,type:p.iconType||u||"square",color:(0,i.Ps)(e),value:a||o,payload:n}}),c(c(c({},p),n.s.getWithHeight(s,u)),{},{payload:e,item:s})}},84925:(t,e,r)=>{"use strict";r.d(e,{gu:()=>eI});var n=r(99004),i=r(90671),o=r.n(i),a=r(52780),u=r.n(a),c=r(11485),l=r.n(c),s=r(18504),f=r.n(s),p=r(74017),h=r.n(p),d=r(86560),y=r.n(d),v=r(97921),m=r(41406),b=r(79020),g=r(39214),x=r(67892),O=r(92942),w=r(39598),S=r(80760),j=r(56770),A=r(61476),P=r(28524),E=r(29776),M=r(85915);function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e,r){var n;return(n=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==k(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var I=["Webkit","Moz","O","ms"],D=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=I.reduce(function(t,n){return _(_({},t),{},C({},n+r,e))},{});return n[t]=e,n};function N(t){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(){return(B=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function U(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q(n.key),n)}}function z(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(z=function(){return!!t})()}function $(t){return($=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function F(t,e){return(F=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function W(t,e,r){return(e=q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q(t){var e=function(t,e){if("object"!=N(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=N(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==N(e)?e:e+""}var X=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,A.z)().domain(l()(0,u)).range([i,i+o-a]),s=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}},H=function(t){return t.changedTouches&&!!t.changedTouches.length},Y=function(t){var e,r;function i(t){var e,r,n;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return r=i,n=[t],r=$(r),W(e=function(t,e){if(e&&("object"===N(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,z()?Reflect.construct(r,n||[],$(this).constructor):r.apply(this,n)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),W(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),W(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),W(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),W(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),W(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),W(e,"handleSlideDragStart",function(t){var r=H(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&F(i,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,o=this.props,a=o.gap,u=o.data.length-1,c=Math.min(e,r),l=Math.max(e,r),s=i.getIndexInRange(n,c),f=i.getIndexInRange(n,l);return{startIndex:s-s%a,endIndex:f===u?u:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=(0,E.kr)(r[t],i,t);return u()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,u=o.width,c=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-i,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=H(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(W(W({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,u=this.state[e],c=i.indexOf(u);if(-1!==c){var l=c+t;if(-1!==l&&!(l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(W({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.fill,u=t.stroke;return n.createElement("rect",{stroke:u,fill:a,x:e,y:r,width:i,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,o=t.height,a=t.data,u=t.children,c=t.padding,l=n.Children.only(u);return l?n.cloneElement(l,{x:e,y:r,width:i,height:o,margin:c,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,h=u.data,d=u.startIndex,y=u.endIndex,v=Math.max(t,this.props.x),m=L(L({},(0,j.J9)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(o=h[y])?void 0:o.name);return n.createElement(g.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},i.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,i=r.y,o=r.height,a=r.stroke,u=r.travellerWidth,c=Math.min(t,e)+u,l=Math.max(Math.abs(e-t)-u,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:c,y:i,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,i=t.y,o=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return n.createElement(g.W,{className:"recharts-brush-texts"},n.createElement(P.E,B({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:i+o/2},f),this.getTextOfTick(e)),n.createElement(P.E,B({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:i+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,i=t.children,o=t.x,a=t.y,u=t.width,c=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!(0,M.Et)(o)||!(0,M.Et)(a)||!(0,M.Et)(u)||!(0,M.Et)(c)||u<=0||c<=0)return null;var b=(0,v.A)("recharts-brush",r),x=1===n.Children.count(i),O=D("userSelect","none");return n.createElement(g.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:O},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||m||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,i=t.width,o=t.height,a=t.stroke,u=Math.floor(r+o/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:i,height:o,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:u,x2:e+i-1,y2:u,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:u+2,x2:e+i-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):i.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return L({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?X({data:r,width:n,x:i,travellerWidth:o,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&U(i.prototype,e),r&&U(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);W(Y,"displayName","Brush"),W(Y,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var K=r(63111),V=r(84663),G=r(23701),Z=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},J=r(98165),Q=r(40017);function tt(){return(tt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function te(t){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){tu(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ti(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ti=function(){return!!t})()}function to(t){return(to=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ta(t,e){return(ta=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tu(t,e,r){return(e=tc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tc(t){var e=function(t,e){if("object"!=te(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=te(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==te(e)?e:e+""}var tl=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=(0,J.P2)({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return Z(t,"discard")&&!o.isInRange(a)?null:a},ts=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=to(t),function(t,e){if(e&&("object"===te(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ti()?Reflect.construct(t,e||[],to(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ta(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,i=t.y,o=t.r,a=t.alwaysShow,u=t.clipPathId,c=(0,M.vh)(e),l=(0,M.vh)(i);if((0,Q.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var s=tl(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,m=tn(tn({clipPath:Z(this.props,"hidden")?"url(#".concat(u,")"):void 0},(0,j.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-dot",y)},r.renderDot(d,m),G.J.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tc(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);tu(ts,"displayName","ReferenceDot"),tu(ts,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tu(ts,"renderDot",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(w.c,tt({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var tf=r(3356),tp=r.n(tf),th=r(17743);function td(t){return(td="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ty(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ty=function(){return!!t})()}function tv(t){return(tv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tm(t,e){return(tm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tb(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tg(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tb(Object(r),!0).forEach(function(e){tx(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tb(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tx(t,e,r){return(e=tO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tO(t){var e=function(t,e){if("object"!=td(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=td(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==td(e)?e:e+""}function tw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tS(){return(tS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tj=function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement("line",tS({},e,{className:"recharts-reference-line-line"}))},tA=function(t,e,r,n,i,o,a,u,c){var l=i.x,s=i.y,f=i.width,p=i.height;if(r){var h=c.y,d=t.y.apply(h,{position:o});if(Z(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:o});if(Z(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map(function(e){return t.apply(e,{position:o})});return Z(c,"discard")&&tp()(g,function(e){return!t.isInRange(e)})?null:g}return null};function tP(t){var e=t.x,r=t.y,i=t.segment,o=t.xAxisId,a=t.yAxisId,u=t.shape,c=t.className,l=t.alwaysShow,s=(0,th.Yp)(),f=(0,th.AF)(o),p=(0,th.Nk)(a),h=(0,th.sk)();if(!s||!h)return null;(0,Q.R)(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=tA((0,J.P2)({x:f.scale,y:p.scale}),(0,M.vh)(e),(0,M.vh)(r),i&&2===i.length,h,t.position,f.orientation,p.orientation,t);if(!d)return null;var y=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tw(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tw(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=y[0],b=m.x,x=m.y,O=y[1],w=O.x,S=O.y,A=tg(tg({clipPath:Z(t,"hidden")?"url(#".concat(s,")"):void 0},(0,j.J9)(t,!0)),{},{x1:b,y1:x,x2:w,y2:S});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-line",c)},tj(u,A),G.J.renderCallByParent(t,(0,J.vh)({x1:b,y1:x,x2:w,y2:S})))}var tE=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tv(t),function(t,e){if(e&&("object"===td(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ty()?Reflect.construct(t,e||[],tv(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tm(r,t),e=[{key:"render",value:function(){return n.createElement(tP,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tO(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tM(){return(tM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tk(t){return(tk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tT(Object(r),!0).forEach(function(e){tN(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}tx(tE,"displayName","ReferenceLine"),tx(tE,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function tC(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tC=function(){return!!t})()}function tI(t){return(tI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tD(t,e){return(tD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tN(t,e,r){return(e=tB(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tB(t){var e=function(t,e){if("object"!=tk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tk(e)?e:e+""}var tR=function(t,e,r,n,i){var o=i.x1,a=i.x2,u=i.y1,c=i.y2,l=i.xAxis,s=i.yAxis;if(!l||!s)return null;var f=(0,J.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!Z(i,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,J.sl)(p,h):null},tL=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tI(t),function(t,e){if(e&&("object"===tk(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tC()?Reflect.construct(t,e||[],tI(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tD(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,i=t.x2,o=t.y1,a=t.y2,u=t.className,c=t.alwaysShow,l=t.clipPathId;(0,Q.R)(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,M.vh)(e),f=(0,M.vh)(i),p=(0,M.vh)(o),h=(0,M.vh)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=tR(s,f,p,h,this.props);if(!y&&!d)return null;var m=Z(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(g.W,{className:(0,v.A)("recharts-reference-area",u)},r.renderRect(d,t_(t_({clipPath:m},(0,j.J9)(this.props,!0)),y)),G.J.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tB(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);function tU(t){return function(t){if(Array.isArray(t))return tz(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tz(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tz(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tz(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}tN(tL,"displayName","ReferenceArea"),tN(tL,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),tN(tL,"renderRect",function(t,e){var r;return n.isValidElement(t)?n.cloneElement(t,e):u()(t)?t(e):n.createElement(S.M,tM({},e,{className:"recharts-reference-area-rect"}))});var t$=function(t,e,r,n,i){var o=(0,j.aS)(t,tE),a=(0,j.aS)(t,ts),u=[].concat(tU(o),tU(a)),c=(0,j.aS)(t,tL),l="".concat(n,"Id"),s=n[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===r&&Z(e.props,"extendDomain")&&(0,M.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===r&&Z(e.props,"extendDomain")&&(0,M.Et)(e.props[p])&&(0,M.Et)(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return(0,M.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},tF=r(63295),tW=r(40942),tq=r(78184),tX=new(r.n(tq)()),tH="recharts.syncMouseEvents",tY=r(11751);function tK(t){return(tK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tV(t,e,r){return(e=tG(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tG(t){var e=function(t,e){if("object"!=tK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tK(e)?e:e+""}var tZ=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");tV(this,"activeIndex",0),tV(this,"coordinateList",[]),tV(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null==(t=window)?void 0:t.scrollX)||0,c=(null==(e=window)?void 0:e.scrollY)||0,l=i+this.offset.top+o/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tG(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),tJ=r(91516),tQ=r(83319);function t0(t){return(t0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var t1=["x","y","top","left","width","height","className"];function t2(){return(t2=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var t3=function(t){var e=t.x,r=void 0===e?0:e,i=t.y,o=void 0===i?0:i,a=t.top,u=void 0===a?0:a,c=t.left,l=void 0===c?0:c,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t5(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=t0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t0(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:u,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,t1));return(0,M.Et)(r)&&(0,M.Et)(o)&&(0,M.Et)(f)&&(0,M.Et)(h)&&(0,M.Et)(u)&&(0,M.Et)(l)?n.createElement("path",t2({},(0,j.J9)(y,!0),{className:(0,v.A)("recharts-cross",d),d:"M".concat(r,",").concat(u,"v").concat(h,"M").concat(l,",").concat(o,"h").concat(f)})):null};function t9(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[(0,tF.IZ)(e,r,n,i),(0,tF.IZ)(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}var t6=r(82701);function t4(t){return(t4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t7(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t8(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=t4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t4(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function et(t){var e,r,i,o,a=t.element,u=t.tooltipEventType,c=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,m=null!=(r=a.props.cursor)?r:null==(i=a.type.defaultProps)?void 0:i.cursor;if(!a||!m||!c||!l||"ScatterChart"!==y&&"axis"!==u)return null;var b=tQ.I;if("ScatterChart"===y)o=l,b=t3;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},b=S.M;else if("radial"===d){var g=t9(l),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=t6.h}else o={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return t9(e);else{var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,tF.IZ)(u,c,l,f),h=(0,tF.IZ)(u,c,s,f);n=p.x,i=p.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(d,l,f)},b=tQ.I;var A=t7(t7(t7(t7({stroke:"#ccc",pointerEvents:"none"},f),o),(0,j.J9)(m,!1)),{},{payload:s,payloadIndex:p,className:(0,v.A)("recharts-tooltip-cursor",m.className)});return(0,n.isValidElement)(m)?(0,n.cloneElement)(m,A):(0,n.createElement)(b,A)}var ee=["item"],er=["children","className","width","height","style","compact","title","desc"];function en(t){return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ei(){return(ei=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eo(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(t,e)||ef(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ea(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function eu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eu=function(){return!!t})()}function ec(t){return(ec=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function el(t,e){return(el=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function es(t){return function(t){if(Array.isArray(t))return ep(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ef(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ef(t,e){if(t){if("string"==typeof t)return ep(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ep(t,e)}}function ep(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function eh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ed(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eh(Object(r),!0).forEach(function(e){ey(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ey(t,e,r){return(e=ev(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ev(t){var e=function(t,e){if("object"!=en(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=en(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==en(e)?e:e+""}var em={xAxis:["bottom","top"],yAxis:["left","right"]},eb={width:"100%",height:"100%"},eg={x:0,y:0};function ex(t){return t}var eO=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return ed(ed(ed({},n),(0,tF.IZ)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var u=i.coordinate,c=n.angle;return ed(ed(ed({},n),(0,tF.IZ)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return eg},ew=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(es(t),es(r)):t},[]);return o.length>0?o:t&&t.length&&(0,M.Et)(n)&&(0,M.Et)(i)?t.slice(n,i+1):[]};function eS(t){return"number"===t?[0,"auto"]:void 0}var ej=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=ew(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,u){var c,l,s=null!=(c=u.props.data)?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),o.dataKey&&!o.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,M.eP)(f,o.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(es(i),[(0,E.zb)(u,l)]):i},[])},eA=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,E.gH)(o,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=ej(t,e,l,s),p=eO(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eP=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,a=e.axisIdKey,u=e.stackGroups,c=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,E._L)(f,i);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?ed(ed({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,O=y.ticks,w=y.includeHidden,S=y[a];if(e[S])return e;var j=ew(t.data,{graphicalItems:n.filter(function(t){var e;return(a in t.props?t.props[a]:null==(e=t.type.defaultProps)?void 0:e[a])===S}),dataStartIndex:c,dataEndIndex:s}),A=j.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&(0,M.Et)(n)&&(0,M.Et)(i))return!0}return!1})(y.domain,b,v)&&(T=(0,E.AQ)(y.domain,null,b),d&&("number"===v||"auto"!==x)&&(C=(0,E.Ay)(j,m,"category")));var P=eS(v);if(!T||0===T.length){var k,T,_,C,I,D=null!=(I=y.domain)?I:P;if(m){if(T=(0,E.Ay)(j,m,v),"category"===v&&d){var N=(0,M.CG)(T);g&&N?(_=T,T=l()(0,A)):g||(T=(0,E.KC)(D,T,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(es(t),[e])},[]))}else if("category"===v)T=g?T.filter(function(t){return""!==t&&!o()(t)}):(0,E.KC)(D,T,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||o()(e)?t:[].concat(es(t),[e])},[]);else if("number"===v){var B=(0,E.A1)(j,n.filter(function(t){var e,r,n=a in t.props?t.props[a]:null==(e=t.type.defaultProps)?void 0:e[a],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===S&&(w||!i)}),m,i,f);B&&(T=B)}d&&("number"===v||"auto"!==x)&&(C=(0,E.Ay)(j,m,"category"))}else T=d?l()(0,A):u&&u[S]&&u[S].hasStack&&"number"===v?"expand"===h?[0,1]:(0,E.Mk)(u[S].stackGroups,c,s):(0,E.vf)(j,n.filter(function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===S&&(w||!r)}),v,f,!0);"number"===v?(T=t$(p,T,S,i,O),D&&(T=(0,E.AQ)(D,T,b))):"category"===v&&D&&T.every(function(t){return D.indexOf(t)>=0})&&(T=D)}return ed(ed({},e),{},ey({},S,ed(ed({},y),{},{axisType:i,domain:T,categoricalDomain:C,duplicateDomain:_,originalDomain:null!=(k=y.domain)?k:P,isCategorical:d,layout:f})))},{})},eE=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.layout,p=t.children,h=ew(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),d=h.length,y=(0,E._L)(s,i),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?ed(ed({},e.type.defaultProps),e.props):e.props)[o],g=eS("number");return t[b]?t:(v++,m=y?l()(0,d):a&&a[b]&&a[b].hasStack?t$(p,m=(0,E.Mk)(a[b].stackGroups,u,c),b,i):t$(p,m=(0,E.AQ)(g,(0,E.vf)(h,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===b&&!i}),"number",s),n.defaultProps.allowDataOverflow),b,i),ed(ed({},t),{},ey({},b,ed(ed({axisType:i},n.defaultProps),{},{hide:!0,orientation:f()(em,"".concat(i,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:s}))))},{})},eM=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,j.aS)(l,i),p={};return f&&f.length?p=eP(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):o&&o.length&&(p=eE(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},ek=function(t){var e=(0,M.lX)(t),r=(0,E.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:h()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,E.Hj)(e,r)}},eT=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,j.BU)(e,Y),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},e_=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},eC=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,p=r.margin||{},h=(0,j.BU)(s,Y),d=(0,j.BU)(s,O.s),y=Object.keys(u).reduce(function(t,e){var r=u[e],n=r.orientation;return r.mirror||r.hide?t:ed(ed({},t),{},ey({},n,t[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:ed(ed({},t),{},ey({},n,f()(t,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=ed(ed({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||Y.defaultProps.height),d&&e&&(m=(0,E.s0)(m,n,r,e));var g=c-m.left-m.right,x=l-m.top-m.bottom;return ed(ed({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},eI=function(t){var e=t.chartName,r=t.GraphicalChild,i=t.defaultTooltipEventType,a=void 0===i?"axis":i,c=t.validateTooltipEventTypes,l=void 0===c?["axis"]:c,s=t.axisComponents,p=t.legendContent,h=t.formatAxisMap,d=t.defaultProps,O=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,a=e.updateId,u=e.dataStartIndex,c=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=e_(f),v=y.numericAxisName,b=y.cateAxisName,g=!!r&&!!r.length&&r.some(function(t){var e=(0,j.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var O=ew(t.data,{graphicalItems:[r],dataStartIndex:u,dataEndIndex:c}),w=void 0!==r.type.defaultProps?ed(ed({},r.type.defaultProps),r.props):r.props,S=w.dataKey,A=w.maxBarSize,P=w["".concat(v,"Id")],M=w["".concat(b,"Id")],k=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=w["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||(0,m.A)(!1);var o=n[i];return ed(ed({},t),{},ey(ey({},r.axisType,o),"".concat(r.axisType,"Ticks"),(0,E.Rh)(o)))},{}),T=k[b],_=k["".concat(b,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,E.kA)(r,n[P].stackGroups),I=(0,j.Mn)(r.type).indexOf("Bar")>=0,D=(0,E.Hj)(T,_),N=[],B=g&&(0,E.tA)({barSize:l,stackGroups:n,totalSize:"xAxis"===b?k[b].width:"yAxis"===b?k[b].height:void 0});if(I){var R,L,U=o()(A)?d:A,z=null!=(R=null!=(L=(0,E.Hj)(T,_,!0))?L:U)?R:0;N=(0,E.BX)({barGap:p,barCategoryGap:h,bandSize:z!==D?z:D,sizeList:B[M],maxBarSize:U}),z!==D&&(N=N.map(function(t){return ed(ed({},t),{},{position:ed(ed({},t.position),{},{offset:t.position.offset-z/2})})}))}var $=r&&r.type&&r.type.getComposedData;$&&x.push({props:ed(ed({},$(ed(ed({},k),{},{displayedData:O,props:t,dataKey:S,item:r,bandSize:D,barPosition:N,offset:i,stackedData:C,layout:f,dataStartIndex:u,dataEndIndex:c}))),{},ey(ey(ey({key:r.key||"item-".concat(y)},v,k[v]),b,k[b]),"animationId",a)),childIndex:(0,j.AW)(r,t.children),item:r})}),x},A=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!(0,j.Me)({props:i}))return null;var c=i.children,l=i.layout,f=i.stackOffset,p=i.data,d=i.reverseStackOrder,y=e_(l),v=y.numericAxisName,m=y.cateAxisName,b=(0,j.aS)(c,r),g=(0,E.Mn)(p,b,"".concat(v,"Id"),"".concat(m,"Id"),f,d),x=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ed(ed({},t),{},ey({},r,eM(i,ed(ed({},e),{},{graphicalItems:b,stackGroups:e.axisType===v&&g,dataStartIndex:o,dataEndIndex:a}))))},{}),w=eC(ed(ed({},x),{},{props:i,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(t){x[t]=h(i,x[t],w,t.replace("Map",""),e)});var S=ek(x["".concat(m,"Map")]),A=O(i,ed(ed({},x),{},{dataStartIndex:o,dataEndIndex:a,updateId:u,graphicalItems:b,stackGroups:g,offset:w}));return ed(ed({formattedGraphicalItems:A,graphicalItems:b,offset:w,stackGroups:g},S),x)},P=function(t){var r;function i(t){var r,a,c,l,s;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");return l=i,s=[t],l=ec(l),ey(c=function(t,e){if(e&&("object"===en(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eu()?Reflect.construct(l,s||[],ec(this).constructor):l.apply(this,s)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ey(c,"accessibilityManager",new tZ),ey(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;c.setState(ed({legendBBox:t},A({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:i},ed(ed({},c.state),{},{legendBBox:t}))))}}),ey(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),ey(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return ed({dataStartIndex:e,dataEndIndex:r},A({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),ey(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=ed(ed({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;u()(n)&&n(r,t)}}),ey(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?ed(ed({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;u()(n)&&n(r,t)}),ey(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),ey(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),ey(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),ey(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;u()(r)&&r(e,t)}),ey(c,"handleOuterEvent",function(t){var e,r,n=(0,j.X_)(t),i=f()(c.props,"".concat(n));n&&u()(i)&&i(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),ey(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=ed(ed({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;u()(n)&&n(r,t)}}),ey(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;u()(e)&&e(c.getMouseInfo(t),t)}),ey(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;u()(e)&&e(c.getMouseInfo(t),t)}),ey(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),ey(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),ey(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),ey(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;u()(e)&&e(c.getMouseInfo(t),t)}),ey(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;u()(e)&&e(c.getMouseInfo(t),t)}),ey(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&tX.emit(tH,c.props.syncId,t,c.eventEmitterSymbol)}),ey(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,i=c.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(ed({dataStartIndex:o,dataEndIndex:a},A({props:c.props,dataStartIndex:o,dataEndIndex:a,updateId:i},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ed(ed({},p),{},{x:p.left,y:p.top}),v=Math.min(u,y.x+y.width),m=Math.min(l,y.y+y.height),b=h[s]&&h[s].value,g=ej(c.state,c.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:eg;c.setState(ed(ed({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else c.setState(t)}),ey(c,"renderCursor",function(t){var r,i=c.state,o=i.isTooltipActive,a=i.activeCoordinate,u=i.activePayload,l=i.offset,s=i.activeTooltipIndex,f=i.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!=(r=t.props.active)?r:o,d=c.props.layout,y=t.key||"_recharts-cursor";return n.createElement(et,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),ey(c,"renderPolarAxis",function(t,e,r){var i=f()(t,"type.axisType"),o=f()(c.state,"".concat(i,"Map")),a=t.type.defaultProps,u=void 0!==a?ed(ed({},a),t.props):t.props,l=o&&o[u["".concat(i,"Id")]];return(0,n.cloneElement)(t,ed(ed({},l),{},{className:(0,v.A)(i,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,E.Rh)(l,!0)}))}),ey(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,i=e.polarAngles,o=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=(0,M.lX)(u),f=(0,M.lX)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(i)?i:(0,E.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:(0,E.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),ey(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,i=e.width,o=e.height,a=c.props.margin||{},u=i-(a.left||0)-(a.right||0),l=(0,V.g)({children:r,formattedGraphicalItems:t,legendWidth:u,legendContent:p});if(!l)return null;var s=l.item,f=ea(l,ee);return(0,n.cloneElement)(s,ed(ed({},f),{},{chartWidth:i,chartHeight:o,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),ey(c,"renderTooltip",function(){var t,e=c.props,r=e.children,i=e.accessibilityLayer,o=(0,j.BU)(r,x.m);if(!o)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=o.props.active)?t:u;return(0,n.cloneElement)(o,{viewBox:ed(ed({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:i})}),ey(c,"renderBrush",function(t){var e=c.props,r=e.margin,i=e.data,o=c.state,a=o.offset,u=o.dataStartIndex,l=o.dataEndIndex,s=o.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,E.HQ)(c.handleBrushChange,t.props.onChange),data:i,x:(0,M.Et)(t.props.x)?t.props.x:a.left,y:(0,M.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,M.Et)(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),ey(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var i=c.clipPathId,o=c.state,a=o.xAxisMap,u=o.yAxisMap,l=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:i})}),ey(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,o=t.childIndex,a=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?ed(ed({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ed(ed({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,E.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,j.J9)(s,!1)),(0,tY._U)(s));return u.push(i.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(o))),n?u.push(i.renderActiveDot(s,ed(ed({},f),{},{cx:n.x,cy:n.y}),"".concat(c,"-basePoint-").concat(o))):a&&u.push(null),u}),ey(c,"renderGraphicChild",function(t,e,r){var i=c.filterFormatItem(t,e,r);if(!i)return null;var a=c.getTooltipEventType(),u=c.state,l=u.isTooltipActive,s=u.tooltipAxis,f=u.activeTooltipIndex,p=u.activeLabel,h=c.props.children,d=(0,j.BU)(h,x.m),y=i.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==i.item.type.defaultProps?ed(ed({},i.item.type.defaultProps),i.item.props):i.item.props,O=g.activeDot,w=g.hide,S=g.activeBar,A=g.activeShape,P=!!(!w&&l&&d&&(O||S||A)),k={};"axis"!==a&&d&&"click"===d.props.trigger?k={onClick:(0,E.HQ)(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(k={onMouseLeave:(0,E.HQ)(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,E.HQ)(c.handleItemMouseEnter,t.props.onMouseEnter)});var T=(0,n.cloneElement)(t,ed(ed({},i.props),k));if(P)if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var _="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());I=(0,M.eP)(v,_,p),D=m&&b&&(0,M.eP)(b,_,p)}else I=null==v?void 0:v[f],D=m&&b&&b[f];if(A||S){var C=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,ed(ed(ed({},i.props),k),{},{activeIndex:C})),null,null]}if(!o()(I))return[T].concat(es(c.renderActivePoints({item:i,activePoint:I,basePoint:D,childIndex:f,isRange:m})))}else{var I,D,N,B=(null!=(N=c.getItemByXY(c.state.activeCoordinate))?N:{graphicalItem:T}).graphicalItem,R=B.item,L=void 0===R?t:R,U=B.childIndex,z=ed(ed(ed({},i.props),k),{},{activeIndex:U});return[(0,n.cloneElement)(L,z),null,null]}return m?[T,null,null]:[T,null]}),ey(c,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,ed(ed({key:"recharts-customized-".concat(r)},c.props),c.state))}),ey(c,"renderMap",{CartesianGrid:{handler:ex,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:ex},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:ex},YAxis:{handler:ex},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:(0,M.NF)("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=y()(c.triggeredAfterMouseMove,null!=(a=t.throttleDelay)?a:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&el(i,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=(0,j.BU)(e,x.m);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=ej(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ed(ed({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,j.OV)([(0,j.BU)(t.children,x.m)],[(0,j.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,j.BU)(this.props.children,x.m);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,K.A3)(r),i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap;if("axis"!==this.getTooltipEventType()&&c&&l){var s=(0,M.lX)(c).scale,f=(0,M.lX)(l).scale,p=s&&s.invert?s.invert(i.chartX):null,h=f&&f.invert?f.invert(i.chartY):null;return ed(ed({},i),{},{xValue:p,yValue:h})}var d=eA(this.state,this.props.data,this.props.layout,a);return d?ed(ed({},i),d):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,M.lX)(c);return(0,tF.yy)({x:i,y:o},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,j.BU)(t,x.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ed(ed({},(0,tY._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){tX.on(tH,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){tX.removeListener(tH,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===(0,j.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,i=e.top,o=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:i,height:o,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=eo(e,2),n=r[0],i=r[1];return ed(ed({},t),{},ey({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=eo(e,2),n=r[0],i=r[1];return ed(ed({},t),{},ey({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?ed(ed({},c.type.defaultProps),c.props):c.props,s=(0,j.Mn)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(e){return(0,S.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(e){return(0,tF.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,tJ.NE)(a,n)||(0,tJ.nZ)(a,n)||(0,tJ.xQ)(a,n)){var h=(0,tJ.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ed(ed({},a),{},{childIndex:d}),payload:(0,tJ.xQ)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,j.Me)(this))return null;var i=this.props,o=i.children,a=i.className,u=i.width,c=i.height,l=i.style,s=i.compact,f=i.title,p=i.desc,h=ea(i,er),d=(0,j.J9)(h,!1);if(s)return n.createElement(th.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.u,ei({},d,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,j.ee)(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(t=this.props.tabIndex)?t:0,d.role=null!=(e=this.props.role)?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(th.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",ei({className:(0,v.A)("recharts-wrapper",a),style:ed({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(t){r.container=t}}),n.createElement(b.u,ei({},d,{width:u,height:c,title:f,desc:p,style:eb}),this.renderClipPath(),(0,j.ee)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ev(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.Component);ey(P,"displayName",e),ey(P,"defaultProps",ed({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),ey(P,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,a=t.width,u=t.height,c=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=eT(t);return ed(ed(ed({},h),{},{updateId:0},A(ed(ed({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||u!==e.prevHeight||c!==e.prevLayout||l!==e.prevStackOffset||!(0,tW.b)(s,e.prevMargin)){var d=eT(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ed(ed({},eA(e,n,c)),{},{updateId:e.updateId+1}),m=ed(ed(ed({},d),y),v);return ed(ed(ed({},m),A(ed({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(!(0,j.OV)(i,e.prevChildren)){var b,g,x,O,w=(0,j.BU)(i,Y),S=w&&null!=(b=null==(g=w.props)?void 0:g.startIndex)?b:f,P=w&&null!=(x=null==(O=w.props)?void 0:O.endIndex)?x:p,E=o()(n)||S!==f||P!==p?e.updateId+1:e.updateId;return ed(ed({updateId:E},A(ed(ed({props:t},e),{},{updateId:E,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:i,dataStartIndex:S,dataEndIndex:P})}return null}),ey(P,"renderActiveDot",function(t,e,r){var i;return i=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):u()(t)?t(e):n.createElement(w.c,e),n.createElement(g.W,{className:"recharts-active-dot",key:r},i)});var k=(0,n.forwardRef)(function(t,e){return n.createElement(P,ei({},t,{ref:e}))});return k.displayName=P.displayName,k}},85915:(t,e,r)=>{"use strict";r.d(e,{CG:()=>g,Dj:()=>x,Et:()=>h,F4:()=>m,NF:()=>v,_3:()=>p,eP:()=>O,lX:()=>b,sA:()=>f,vh:()=>d});var n=r(59877),i=r.n(n),o=r(23485),a=r.n(o),u=r(18504),c=r.n(u),l=r(77849),s=r.n(l),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return i()(t)&&t.indexOf("%")===t.length-1},h=function(t){return s()(t)&&!a()(t)},d=function(t){return h(t)||i()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!i()(t))return n;if(p(t)){var u=t.indexOf("%");r=e*parseFloat(t.slice(0,u))/100}else r=+t;return a()(r)&&(r=n),o&&r>e&&(r=e),r},b=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},g=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},x=function(t,e){return h(t)&&h(e)?function(r){return t+r*(e-t)}:function(){return e}};function O(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===r}):null}},86560:(t,e,r)=>{var n=r(78015),i=r(75063);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},90671:t=>{t.exports=function(t){return null==t}},91516:(t,e,r)=>{"use strict";r.d(e,{yp:()=>C,GG:()=>U,NE:()=>I,nZ:()=>D,xQ:()=>N});var n=r(99004),i=r(52780),o=r.n(i),a=r(62643),u=r.n(a),c=r(37544),l=r.n(c),s=r(30012),f=r.n(s),p=r(80760),h=r(97921),d=r(49683),y=r(56770);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var O=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},S=function(t){var e,r=x(x({},w),t),i=(0,n.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,u=[],c=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(c=(n=o.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,i=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return b(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=o[0],u=o[1];(0,n.useEffect)(function(){if(i.current&&i.current.getTotalLength)try{var t=i.current.getTotalLength();t&&u(t)}catch(t){}},[]);var c=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,g=r.animationEasing,S=r.animationDuration,j=r.animationBegin,A=r.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||0===s&&0===f||0===p)return null;var P=(0,h.A)("recharts-trapezoid",v);return A?n.createElement(d.Ay,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:S,animationEasing:g,isActive:A},function(t){var e=t.upperWidth,o=t.lowerWidth,u=t.height,c=t.x,l=t.y;return n.createElement(d.Ay,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:S,easing:g},n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:O(c,l,e,o,u),ref:i})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(r,!0),{className:P,d:O(c,l,s,f,p)})))},j=r(82701),A=r(39214),P=r(6559),E=["option","shapeType","propTransformer","activeClassName","isActive"];function M(t){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(S,r);case"sector":return n.createElement(j.h,r);case"symbols":if("symbols"===e)return n.createElement(P.i,r);break;default:return null}}function C(t){var e,r=t.option,i=t.shapeType,a=t.propTransformer,c=t.activeClassName,s=t.isActive,f=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,E);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,T(T({},f),(0,n.isValidElement)(r)?r.props:r));else if(o()(r))e=r(f);else if(u()(r)&&!l()(r)){var p=(void 0===a?function(t,e){return T(T({},e),t)}:a)(r,f);e=n.createElement(_,{shapeType:i,elementProps:p})}else e=n.createElement(_,{shapeType:i,elementProps:f});return s?n.createElement(A.W,{className:void 0===c?"recharts-active-shape":c},e):e}function I(t,e){return null!=e&&"trapezoids"in t.props}function D(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function B(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function R(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function L(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}function U(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,u=(I(o,i)?e="trapezoids":D(o,i)?e="sectors":N(o,i)&&(e="points"),e),c=I(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:D(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:N(o,i)?i.payload:{},l=a.filter(function(t,e){var r=f()(c,t),n=o.props[u].filter(function(t){var e;return(I(o,i)?e=B:D(o,i)?e=R:N(o,i)&&(e=L),e)(t,i)}),a=o.props[u].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},92942:(t,e,r)=>{"use strict";r.d(e,{s:()=>I});var n=r(99004),i=r(52780),o=r.n(i),a=r(97921),u=r(40017),c=r(79020),l=r(6559),s=r(11751);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d=function(){return!!t})()}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function v(t,e){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function m(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var g=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=y(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,d()?Reflect.construct(t,e||[],y(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&v(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,i=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(i,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*i,",").concat(16,"\n            H").concat(32,"M").concat(2*i,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(i,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){m(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,n.cloneElement(t.legendIcon,a)}return n.createElement(l.i,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,i=e.iconSize,l=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var l=e.formatter||f,b=(0,a.A)(m(m({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=o()(e.value)?null:e.value;(0,u.R)(!o()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return n.createElement("li",p({className:b,style:y,key:"legend-item-".concat(r)},(0,s.XC)(t.props,e,r)),n.createElement(c.u,{width:i,height:i,viewBox:d,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(g,e,r):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,i=t.align;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?i:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,b(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);m(g,"displayName","Legend"),m(g,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var x=r(85915),O=r(49916);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var S=["ref"];function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){T(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function M(t){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function k(t,e){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function T(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}function C(t){return t.value}var I=function(t){var e,r;function i(){var t,e,r;if(!(this instanceof i))throw TypeError("Cannot call a class as a function");for(var n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];return e=i,r=[].concat(o),e=M(e),T(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,E()?Reflect.construct(e,r||[],M(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&k(i,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?A({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),A(A({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,i=e.width,o=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=A(A({position:"absolute",width:i||"auto",height:o||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,S);return n.createElement(g,r)}(r,A(A({},this.props),{},{payload:(0,O.s)(c,u,C)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=A(A({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,x.Et)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&P(i.prototype,e),r&&P(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);T(I,"displayName","Legend"),T(I,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},94960:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},98165:(t,e,r)=>{"use strict";r.d(e,{P2:()=>O,bx:()=>w,pr:()=>m,sl:()=>b,vh:()=>g});var n=r(83572),i=r.n(n),o=r(10731),a=r.n(o),u=r(29776),c=r(56770),l=r(85915),s=r(66644);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,i){var o=t.width,a=t.height,f=t.layout,p=t.children,h=Object.keys(e),v={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,c.BU)(p,s.y);return h.reduce(function(o,a){var c,s,p,h,b,g=e[a],x=g.orientation,O=g.domain,w=g.padding,S=void 0===w?{}:w,j=g.mirror,A=g.reversed,P="".concat(x).concat(j?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=O[1]-O[0],M=1/0,k=g.categoricalDomain.sort();if(k.forEach(function(t,e){e>0&&(M=Math.min((t||0)-(k[e-1]||0),M))}),Number.isFinite(M)){var T=M/E,_="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(c=T*_/2),"no-gap"===g.padding){var C=(0,l.F4)(t.barCategoryGap,T*_),I=T*_/2;c=I-C-(I-C)/_*C}}}s="xAxis"===n?[r.left+(S.left||0)+(c||0),r.left+r.width-(S.right||0)-(c||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(S.bottom||0),r.top+(S.top||0)]:[r.top+(S.top||0)+(c||0),r.top+r.height-(S.bottom||0)-(c||0)]:g.range,A&&(s=[s[1],s[0]]);var D=(0,u.W7)(g,i,m),N=D.scale,B=D.realScaleType;N.domain(O).range(s),(0,u.YB)(N);var R=(0,u.w7)(N,d(d({},g),{},{realScaleType:B}));"xAxis"===n?(b="top"===x&&!j||"bottom"===x&&j,p=r.left,h=v[P]-b*g.height):"yAxis"===n&&(b="left"===x&&!j||"right"===x&&j,p=v[P]-b*g.width,h=r.top);var L=d(d(d({},g),R),{},{realScaleType:B,x:p,y:h,scale:N,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return L.bandSize=(0,u.Hj)(L,R),g.hide||"xAxis"!==n?g.hide||(v[P]+=(b?-1:1)*L.width):v[P]+=(b?-1:1)*L.height,d(d({},o),{},y({},a,L))},{})},b=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},g=function(t){return b({x:t.x1,y:t.y1},{x:t.x2,y:t.y2})},x=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&p(r.prototype,t),e&&p(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();y(x,"EPS",1e-4);var O=function(t){var e=Object.keys(t).reduce(function(e,r){return d(d({},e),{},y({},r,x.create(t[r])))},{});return d(d({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return i()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})},w=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))}}}]);