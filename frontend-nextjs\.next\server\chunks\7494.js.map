{"version": 3, "file": "7494.js", "mappings": "+iBAKA,SAASA,EAAO,WACdC,CAAS,CACT,GAAGC,EAC+C,EAClD,MAAO,UAACC,EAAAA,EAAoB,EAACC,YAAU,SAASH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6DAA8DJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cACvP,CACA,SAASC,EAAY,WACnBR,CAAS,CACT,GAAGC,EACgD,EACnD,MAAO,UAACC,EAAAA,EAAqB,EAACC,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cACjO,CACA,SAASE,EAAe,CACtBT,WAAS,CACT,GAAGC,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAwB,EAACC,YAAU,kBAAkBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mEAAoEJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,cACtR,inBChBA,SAASG,EAAa,CACpB,GAAGT,EACqD,EACxD,MAAO,UAACU,EAAAA,EAA0B,EAACR,YAAU,gBAAiB,GAAGF,CAAK,CAAEI,sBAAoB,6BAA6BC,wBAAsB,eAAeC,0BAAwB,qBACxL,CAMA,SAASK,EAAoB,CAC3B,GAAGX,EACwD,EAC3D,MAAO,UAACU,EAAAA,EAA6B,EAACR,YAAU,wBAAyB,GAAGF,CAAK,CAAEI,sBAAoB,gCAAgCC,wBAAsB,sBAAsBC,0BAAwB,qBAC7M,CACA,SAASM,EAAoB,WAC3Bb,CAAS,CACTc,aAAa,CAAC,CACd,GAAGb,EACwD,EAC3D,MAAO,UAACU,EAAAA,EAA4B,EAACN,sBAAoB,+BAA+BC,wBAAsB,sBAAsBC,0BAAwB,6BACxJ,UAACI,EAAAA,EAA6B,EAACR,YAAU,wBAAwBW,WAAYA,EAAYd,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yjBAA0jBJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,gCAAgCE,0BAAwB,uBAE1wB,CACA,SAASQ,EAAkB,CACzB,GAAGd,EACsD,EACzD,MAAO,UAACU,EAAAA,EAA2B,EAACR,YAAU,sBAAuB,GAAGF,CAAK,CAAEI,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,qBACrM,CACA,SAASS,EAAiB,WACxBhB,CAAS,OACTiB,CAAK,SACLC,EAAU,SAAS,CACnB,GAAGjB,EAIJ,EACC,MAAO,UAACU,EAAAA,EAA0B,EAACR,YAAU,qBAAqBgB,aAAYF,EAAOG,eAAcF,EAASlB,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8mBAA+mBJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,6BAA6BC,wBAAsB,mBAAmBC,0BAAwB,qBACp3B,CACA,SAASc,EAAyB,WAChCrB,CAAS,UACTsB,CAAQ,SACRC,CAAO,CACP,GAAGtB,EAC6D,EAChE,MAAO,WAACU,EAAAA,EAAkC,EAACR,YAAU,8BAA8BH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+SAAgTJ,GAAYuB,QAASA,EAAU,GAAGtB,CAAK,CAAEI,sBAAoB,qCAAqCC,wBAAsB,2BAA2BC,0BAAwB,8BACxjB,UAACiB,OAAAA,CAAKxB,UAAU,yFACd,UAACW,EAAAA,EAAmC,EAACN,sBAAoB,sCAAsCE,0BAAwB,6BACrH,UAACkB,EAAAA,CAASA,CAAAA,CAACzB,UAAU,SAASK,sBAAoB,YAAYE,0BAAwB,0BAGzFe,IAEP,CAoBA,SAASI,EAAkB,WACzB1B,CAAS,OACTiB,CAAK,CACL,GAAGhB,EAGJ,EACC,MAAO,UAACU,EAAAA,EAA2B,EAACR,YAAU,sBAAsBgB,aAAYF,EAAOjB,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oDAAqDJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,qBACvS,CACA,SAASoB,EAAsB,WAC7B3B,CAAS,CACT,GAAGC,EAC0D,EAC7D,MAAO,UAACU,EAAAA,EAA+B,EAACR,YAAU,0BAA0BH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4BAA6BJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,kCAAkCC,wBAAsB,wBAAwBC,0BAAwB,qBAC5Q,0xBC1FO,IAAMqB,EAAQ,CACnBC,UAAWC,EAAAA,CAAmBA,CAC9BC,KAAMC,EAAAA,CAAWA,CACjBC,MAAOC,EAAAA,CAASA,CAChBC,MAAOC,EAAAA,CAAKA,CACZC,QAASC,EAAAA,CAAeA,CACxBC,QAASC,EAAAA,CAAWA,CACpBC,OAAQC,EAAAA,CAAgBA,CACxBC,YAAaC,EAAAA,CAAeA,CAC5BC,aAAcC,EAAAA,CAAgBA,CAC9BC,MAAOC,EAAAA,CAASA,CAChBC,SAAUC,EAAAA,CAASA,CACnBC,KAAMC,EAAAA,CAAYA,CAClBC,KAAMC,EAAAA,CAAQA,CACdC,QAASC,EAAAA,CAAYA,CACrBC,MAAOC,EAAAA,CAAcA,CACrBC,MAAOC,EAAAA,CAASA,CAChBC,SAAUC,EAAAA,CAAYA,CACtBC,QAASC,EAAAA,CAAcA,CACvBC,SAAUC,EAAAA,CAAgBA,CAC1BC,IAAKC,EAAAA,CAAQA,CACbC,QAASC,EAAAA,CAAiBA,CAC1BC,KAAMC,EAAAA,CAAQA,CACdC,WAAYC,EAAAA,CAAcA,CAC1BC,KAAMC,EAAAA,CAAcA,CACpBC,MAAOC,EAAAA,CAASA,CAChBC,IAAKC,EAAAA,CAAOA,CACZC,KAAMC,EAAAA,CAAQA,CACdC,OAAQC,EAAAA,CAAgBA,CACxBC,OAAQC,EAAAA,CAAeA,CACvBC,QAASC,EAAAA,CAAgBA,CACzBC,MAAOC,EAAAA,CAASA,CAEhBC,SAAUC,EAAAA,CAAYA,CACtBC,MAAOC,EAAAA,CAASA,CAChBC,QAASC,EAAAA,CAAeA,EACxB,qGCnCa,SAASC,IACtB,GAAM,OACJC,CAAK,CACN,CAAGC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,GACX,MAAO,UAACC,MAAAA,CAAIpG,UAAU,mBAAmBM,wBAAsB,cAAcC,0BAAwB,4BACjG,WAAC8F,EAAAA,CAAMA,CAAAA,CAACnF,QAAQ,UAAUlB,UAAU,kJAAkJsG,QAASJ,EAAMK,MAAM,CAAElG,sBAAoB,SAASE,0BAAwB,6BAChQ,UAACiG,EAAAA,CAAUA,CAAAA,CAACxG,UAAU,eAAeK,sBAAoB,aAAaE,0BAAwB,qBAAqB,YAEnH,WAACkG,MAAAA,CAAIzG,UAAU,oMACb,UAACwB,OAAAA,CAAKxB,UAAU,mBAAU,MAAQ,WAI5C,0ECNO,SAAS0G,EAAkB,WAChC1G,CAAS,UACT2G,GAAW,CAAK,MAChBpC,CAAI,CACmB,EACvB,MAAO,WAAC6B,MAAAA,CAAIpG,UAAU,0BAA0BM,wBAAsB,oBAAoBC,0BAAwB,oCAC9G,WAACR,EAAAA,MAAMA,CAAAA,CAACC,UAAWA,EAAWK,sBAAoB,SAASE,0BAAwB,oCACjF,UAACC,EAAAA,WAAWA,CAAAA,CAACoG,IAAKrC,GAAMsC,UAAY,GAAIC,IAAKvC,GAAMwC,UAAY,GAAI1G,sBAAoB,cAAcE,0BAAwB,4BAC7H,UAACE,EAAAA,cAAcA,CAAAA,CAACT,UAAU,aAAaK,sBAAoB,iBAAiBE,0BAAwB,mCACjGgE,GAAMwC,UAAUC,MAAM,EAAG,IAAIC,eAAiB,UAIlDN,GAAY,WAACP,MAAAA,CAAIpG,UAAU,wDACxB,UAACwB,OAAAA,CAAKxB,UAAU,kCAA0BuE,GAAMwC,UAAY,KAC5D,UAACvF,OAAAA,CAAKxB,UAAU,4BACbuE,GAAM2C,cAAc,CAAC,EAAE,CAACC,cAAgB,UAIrD,mGC3BA,SAASC,EAAM,WACbpH,CAAS,CACT,GAAGC,EAC8C,EACjD,MAAO,UAACoH,EAAAA,CAAmB,EAAClH,YAAU,QAAQH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,sNAAuNJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,sBAAsBC,wBAAsB,QAAQC,0BAAwB,aAC5Y,wFCRA,SAAS+G,EAAM,WACbtH,CAAS,MACTuH,CAAI,CACJ,GAAGtH,EAC2B,EAC9B,MAAO,UAACuH,QAAAA,CAAMD,KAAMA,EAAMpH,YAAU,QAAQH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kcAAmc,gFAAiF,yGAA0GJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,QAAQC,0BAAwB,aACvwB,uqBCJA,IAAMkH,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,8bAA+b,CACxdC,SAAU,CACRzG,QAAS,CACP0G,QAAS,mEACTC,YAAa,8JACbC,QAAS,wIACTC,UAAW,yEACXC,MAAO,uEACPC,KAAM,iDACR,EACAC,KAAM,CACJN,QAAS,gCACTO,GAAI,gDACJC,GAAI,uCACJC,KAAM,QACR,CACF,EACAC,gBAAiB,CACfpH,QAAS,UACTgH,KAAM,SACR,CACF,GACA,SAAS7B,EAAO,WACdrG,CAAS,SACTkB,CAAO,MACPgH,CAAI,SACJK,GAAU,CAAK,CACf,GAAGtI,EAGJ,EACC,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,SAC9B,MAAO,UAACD,EAAAA,CAAKrI,YAAU,SAASH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACqH,EAAe,SAC3DvG,EACAgH,iBACAlI,CACF,IAAM,GAAGC,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,SAASC,0BAAwB,cACpG,oMCnCA,SAASmI,EAAO,CACd,GAAGzI,EAC+C,EAClD,MAAO,UAAC0I,EAAAA,EAAoB,EAACxI,YAAU,SAAU,GAAGF,CAAK,CAAEI,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASqI,EAAY,CACnB,GAAG3I,EACgD,EACnD,MAAO,UAAC0I,EAAAA,EAAqB,EAACxI,YAAU,eAAgB,GAAGF,CAAK,CAAEI,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5K,CACA,SAASsI,EAAY,CACnB,GAAG5I,EACgD,EACnD,MAAO,UAAC0I,EAAAA,EAAqB,EAACxI,YAAU,eAAgB,GAAGF,CAAK,CAAEI,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5K,CACA,SAASuI,EAAc,WACrB9I,CAAS,MACTkI,EAAO,SAAS,UAChB5G,CAAQ,CACR,GAAGrB,EAGJ,EACC,MAAO,WAAC0I,EAAAA,EAAuB,EAACxI,YAAU,iBAAiB4I,YAAWb,EAAMlI,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+yBAAgzBJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,uBACxgCe,EACD,UAACqH,EAAAA,EAAoB,EAACJ,OAAO,IAAClI,sBAAoB,uBAAuBE,0BAAwB,sBAC/F,UAACyI,EAAAA,CAAeA,CAAAA,CAAChJ,UAAU,oBAAoBK,sBAAoB,kBAAkBE,0BAAwB,mBAGrH,CACA,SAAS0I,EAAc,WACrBjJ,CAAS,UACTsB,CAAQ,UACR4H,EAAW,QAAQ,CACnB,GAAGjJ,EACkD,EACrD,MAAO,UAAC0I,EAAAA,EAAsB,EAACtI,sBAAoB,yBAAyBC,wBAAsB,gBAAgBC,0BAAwB,sBACtI,WAACoI,EAAAA,EAAuB,EAACxI,YAAU,iBAAiBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gjBAA8jB,WAAb8I,GAAyB,kIAAmIlJ,GAAYkJ,SAAUA,EAAW,GAAGjJ,CAAK,CAAEI,sBAAoB,0BAA0BE,0BAAwB,uBAC93B,UAAC4I,EAAAA,CAAqB9I,sBAAoB,uBAAuBE,0BAAwB,eACzF,UAACoI,EAAAA,EAAwB,EAAC3I,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,MAAoB,WAAb8I,GAAyB,uGAAwG7I,sBAAoB,2BAA2BE,0BAAwB,sBACpPe,IAEH,UAAC8H,EAAAA,CAAuB/I,sBAAoB,yBAAyBE,0BAAwB,mBAGrG,CACA,SAAS8I,EAAY,WACnBrJ,CAAS,CACT,GAAGC,EACgD,EACnD,MAAO,UAAC0I,EAAAA,EAAqB,EAACxI,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4CAA6CJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cACnP,CACA,SAAS+I,EAAW,WAClBtJ,CAAS,UACTsB,CAAQ,CACR,GAAGrB,EAC+C,EAClD,MAAO,WAAC0I,EAAAA,EAAoB,EAACxI,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4aAA6aJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,uBAAuBC,wBAAsB,aAAaC,0BAAwB,uBACzmB,UAACiB,OAAAA,CAAKxB,UAAU,sEACd,UAAC2I,EAAAA,EAA6B,EAACtI,sBAAoB,gCAAgCE,0BAAwB,sBACzG,UAACkB,EAAAA,CAASA,CAAAA,CAACzB,UAAU,SAASK,sBAAoB,YAAYE,0BAAwB,mBAG1F,UAACoI,EAAAA,EAAwB,EAACtI,sBAAoB,2BAA2BE,0BAAwB,sBAAce,MAErH,CACA,SAASiI,EAAgB,CACvBvJ,WAAS,CACT,GAAGC,EACoD,EACvD,MAAO,UAAC0I,EAAAA,EAAyB,EAACxI,YAAU,mBAAmBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gDAAiDJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,4BAA4BC,wBAAsB,kBAAkBC,0BAAwB,cACvQ,CACA,SAAS4I,EAAqB,WAC5BnJ,CAAS,CACT,GAAGC,EACyD,EAC5D,MAAO,UAAC0I,EAAAA,EAA8B,EAACxI,YAAU,0BAA0BH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,iCAAiCC,wBAAsB,uBAAuBC,0BAAwB,sBAC9R,UAACiJ,EAAAA,CAAaA,CAAAA,CAACxJ,UAAU,SAASK,sBAAoB,gBAAgBE,0BAAwB,gBAEpG,CACA,SAAS6I,EAAuB,CAC9BpJ,WAAS,CACT,GAAGC,EAC2D,EAC9D,MAAO,UAAC0I,EAAAA,EAAgC,EAACxI,YAAU,4BAA4BH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uDAAwDJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,mCAAmCC,wBAAsB,yBAAyBC,0BAAwB,sBACtS,UAACyI,EAAAA,CAAeA,CAAAA,CAAChJ,UAAU,SAASK,sBAAoB,kBAAkBE,0BAAwB,gBAExG,sfCvBO,SAASkJ,EAAmBC,CAAc,EAC/C,OAAQA,GACN,IAAK,QACH,MAAO,CACLC,mBAAmB,EACnBC,iBAAiB,EACjBC,kBAAmB,GACnBC,oBAAqB,GACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,wBAAwB,EACxBC,wBAAwB,EACxBC,uBAAuB,EACvBC,qBAAqB,EACrBC,mBAAmB,EACnBC,qBAAqB,EACrBC,eAAgB,GAChBC,iBAAkB,GAClBC,sBAAsB,EAEtBC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,iBAAiB,EACjBC,oBAAoB,EACpBC,iBAAiB,EACjBC,qBAAqB,EACrBC,kBAAkB,EAClBC,oBAAoB,EACpBC,0BAA2B,GAC3BC,yBAAyB,EAEzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,mBAAoB,GACpBC,yBAA0B,GAC1BC,oBAAoB,EACpBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,uBAAuB,EACvBC,cAAc,EACdC,yBAA0B,GAC1BC,0BAA0B,EAE1BC,mBAAmB,EACnBC,gBAAiB,GACjBC,mBAAmB,EACnBC,kBAAkB,EAClBC,mBAAmB,EACnBC,sBAAuB,GAEvBC,qBAAqB,EACrBC,uBAAuB,EACvBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,4BAA6B,GAC7BC,gCAAgC,EAChCC,kBAAkB,EAClBC,oBAAoB,CACtB,CAEF,KAAK,SACH,MAAO,CACLtD,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,wBAAwB,EACxBC,wBAAwB,EACxBC,uBAAuB,EACvBC,qBAAqB,EACrBC,mBAAmB,EACnBC,qBAAqB,EACrBC,gBAAgB,EAChBC,iBAAkB,GAClBC,sBAAsB,EAEtBC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,iBAAiB,EACjBC,oBAAoB,EACpBC,iBAAiB,EACjBC,qBAAqB,EACrBC,kBAAkB,EAClBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,yBAAyB,EAEzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,oBAAoB,EACpBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,uBAAuB,EACvBC,cAAc,EACdC,0BAA0B,EAC1BC,0BAA0B,EAE1BC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,kBAAkB,EAClBC,mBAAmB,EACnBC,uBAAuB,EAEvBC,qBAAqB,EACrBC,uBAAuB,EACvBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,gCAAgC,EAChCC,kBAAkB,EAClBC,oBAAoB,CACtB,CAEF,KAAK,aACH,MAAO,CACLtD,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,wBAAwB,EACxBC,uBAAwB,GACxBC,uBAAuB,EACvBC,oBAAqB,GACrBC,mBAAmB,EACnBC,qBAAqB,EACrBC,gBAAgB,EAChBC,kBAAkB,EAClBC,sBAAsB,EAEtBC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,gBAAiB,GACjBC,iBAAiB,EACjBC,oBAAoB,EACpBC,gBAAiB,GACjBC,qBAAqB,EACrBC,kBAAkB,EAClBC,oBAAoB,EACpBC,0BAA2B,GAC3BC,yBAAyB,EAEzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,4BAA4B,EAC5BC,wBAAwB,EACxBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,oBAAoB,EACpBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,+BAAgC,GAChCC,uBAAuB,EACvBC,cAAc,EACdC,0BAA0B,EAC1BC,0BAA0B,EAE1BC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,kBAAkB,EAClBC,mBAAmB,EACnBC,uBAAuB,EAEvBC,oBAAqB,GACrBC,uBAAuB,EACvBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,gCAAgC,EAChCC,kBAAkB,EAClBC,oBAAoB,CACtB,CAEF,SAEE,MAAO,CACLtD,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,wBAAwB,EACxBC,wBAAwB,EACxBC,uBAAuB,EACvBC,oBAAqB,GACrBC,mBAAmB,EACnBC,qBAAqB,EACrBC,gBAAgB,EAChBC,kBAAkB,EAClBC,sBAAsB,EAEtBC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,iBAAiB,EACjBC,gBAAiB,GACjBC,oBAAoB,EACpBC,iBAAiB,EACjBC,qBAAqB,EACrBC,kBAAkB,EAClBC,oBAAoB,EACpBC,2BAA2B,EAC3BC,yBAAyB,EAEzBC,mBAAmB,EACnBC,mBAAmB,EACnBC,2BAA4B,GAC5BC,wBAAwB,EACxBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,oBAAoB,EACpBC,mBAAmB,EACnBC,0BAA0B,EAC1BC,gCAAgC,EAChCC,sBAAuB,GACvBC,cAAc,EACdC,0BAA0B,EAC1BC,0BAA0B,EAE1BC,mBAAmB,EACnBC,iBAAiB,EACjBC,mBAAmB,EACnBC,kBAAkB,EAClBC,mBAAmB,EACnBC,uBAAuB,EAEvBC,qBAAqB,EACrBC,sBAAuB,GACvBC,sBAAsB,EACtBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,gCAAgC,EAChCC,kBAAkB,EAClBC,oBAAoB,CACtB,CACJ,CACF,CAKO,SAASC,EACd3I,CAA8B,CAC9B4I,CAAiC,QAEjC,CAAI,CAAC5I,IAAQ,CAACA,EAAKmF,IAAI,EAAE,EAEcnF,EAAKmF,GAFZ,CAEgB,CAC9B,CAACyD,EAAW,CAMzB,SAASC,EACd7I,CAA8B,CAC9B8I,CAA4B,QAE5B,CAAI,CAAC9I,IAAQ,CAACA,EAAKmF,IAAI,EAAE,CAEJ4D,MAFW,OAEE,CAACD,GAASA,EAAQ,CAACA,EAAM,EACvCE,QAAQ,CAAChJ,EAAKmF,IAAI,CACxC,CA4CO,SAAS8D,EAAmB9D,CAAc,EAC/C,OAAQA,GACN,IAAK,QACH,MAAO,KACT,KAAK,aACH,MAAO,IACT,KAAK,SACH,MAAO,IACT,SACE,MAAO,MACX,CACF,CAKO,SAAS+D,EAAkB/D,CAAc,EAC9C,OAAQA,GACN,IAAK,QACH,MAAO,yBACT,KAAK,SACH,MAAO,2BACT,KAAK,aACH,MAAO,6BACT,SACE,MAAO,2BACX,CACF,sGCxZO,IAAMgE,EAAsB,CACjC,CACEC,MAAO,MACPC,IAAK,aACLvF,KAAM,YACNwF,UAAU,EACVC,SAAU,CAAC,IAAK,IAAI,CACpBC,MAAO,EAAE,CACTV,MAAO,CAAC,QAAS,aAAc,SAAS,CAAC,CAE3C,CACEM,MAAO,OACPC,IAAK,cAJqE,YAK1EvF,KAAM,WACNyF,SAAU,CAAC,IAAK,IAAI,CACpBD,UAAU,EACVE,MAAO,EAAE,CACTV,MAAO,CAAC,QAAS,aAAc,SAAS,CAAC,CAE3C,CACEM,MAAO,OACPC,IAAK,iBAJwE,KAK7EvF,KAAM,QACNyF,SAAU,CAAC,IAAK,IAAI,CACpBD,UAAU,EACVE,MAAO,EAAE,CACTV,MAAO,CAAC,QAAS,aAAc,SACjC,CAD2C,CAE3C,CACEM,MAAO,OACPC,IAAK,aAJoE,WAKzEvF,KAAM,UACNyF,SAAU,CAAC,IAAK,IAAI,CACpBD,UAAU,EACVE,MAAO,EAAE,CACTV,MAAO,CAAC,QAAQ,CAAC,CAEnB,CACEM,MAAO,OACPC,IAAK,gBAJ+C,KAKpDvF,KAAM,UACNyF,SAAU,CAAC,IAAK,IAAI,CACpBD,UAAU,EACVE,MAAO,EAAE,CACTV,MAAO,CAAC,QAAS,aACnB,CADiC,CAEjC,CACEM,MAAO,OACPC,IAAK,mBACLvF,IALyE,CAKnE,UACNyF,SAAU,CAAC,IAAK,IAAI,CACpBD,UAAU,EACVE,MAAO,EAAE,CACTV,MAAO,CAAC,QAAQ,CAAC,CAEnB,CACEM,MAAO,KACPC,IAAK,IACLvF,KAAM,UACNwF,IANyD,MAM/C,EACVE,MAAO,CACL,CACEJ,MAAO,OACPC,IAAK,qBACLvF,KAAM,UACNyF,SAAU,CAAC,IAAK,IAAI,EAEtB,CACEH,MAAO,KACPG,SAAU,CAAC,IAAK,IAAI,CACpBF,IAAK,IACLvF,KAAM,OACR,EACD,EAEJ,CAAC,yjEEnFF,SAAS2F,EAAM,CACb,GAAG/N,EAC8C,EACjD,MAAO,UAACgO,EAAAA,EAAmB,EAAC9N,YAAU,QAAS,GAAGF,CAAK,CAAEI,sBAAoB,sBAAsBC,wBAAsB,QAAQC,0BAAwB,aAC3J,CAWA,SAAS2N,EAAY,CACnB,GAAGjO,EACgD,EACnD,MAAO,UAACgO,EAAAA,EAAqB,EAAC9N,YAAU,eAAgB,GAAGF,CAAK,CAAEI,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,aAC5K,CACA,SAAS4N,EAAa,WACpBnO,CAAS,CACT,GAAGC,EACiD,EACpD,MAAO,UAACgO,EAAAA,EAAsB,EAAC9N,YAAU,gBAAgBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,aACpW,CACA,SAAS6N,EAAa,WACpBpO,CAAS,UACTsB,CAAQ,MACR+M,EAAO,OAAO,CACd,GAAGpO,EAGJ,EACC,MAAO,WAACiO,EAAAA,CAAY7N,sBAAoB,cAAcC,wBAAsB,eAAeC,0BAAwB,sBAC/G,UAAC4N,EAAAA,CAAa9N,sBAAoB,eAAeE,0BAAwB,cACzE,WAAC0N,EAAAA,EAAsB,EAAC9N,YAAU,gBAAgBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6MAAuN,UAATiO,GAAoB,mIAA6I,SAATA,GAAmB,gIAAiIA,WAAkB,2GAAqH,WAATA,GAAqB,oHAAqHrO,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,yBAAyBE,0BAAwB,sBAC35Be,EACD,WAAC2M,EAAAA,EAAoB,EAACjO,UAAU,6OAA6OK,sBAAoB,uBAAuBE,0BAAwB,sBAC9U,UAAC+N,EAAAA,CAAKA,CAAAA,CAACtO,UAAU,SAASK,sBAAoB,QAAQE,0BAAwB,cAC9E,UAACiB,OAAAA,CAAKxB,UAAU,mBAAU,kBAIpC,CACA,SAASuO,EAAY,WACnBvO,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,eAAeH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4BAA6BJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,cAAcC,0BAAwB,aACrK,CAOA,SAASiO,EAAW,WAClBxO,CAAS,CACT,GAAGC,EAC+C,EAClD,MAAO,UAACgO,EAAAA,EAAoB,EAAC9N,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,uBAAuBC,wBAAsB,aAAaC,0BAAwB,aACnO,CACA,SAASkO,EAAiB,WACxBzO,CAAS,CACT,GAAGC,EACqD,EACxD,MAAO,UAACgO,EAAAA,EAA0B,EAAC9N,YAAU,oBAAoBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,6BAA6BC,wBAAsB,mBAAmBC,0BAAwB,aAC3P,CCzEA,SAASmO,EAAS,CAChB1O,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,WAAWH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,WAAWC,0BAAwB,gBACvK,gBCDA,SAASoO,EAAgB,CACvBC,gBAAgB,CAAC,CACjB,GAAG3O,EACoD,EACvD,MAAO,UAAC4O,EAAAA,EAAyB,EAAC1O,YAAU,mBAAmByO,cAAeA,EAAgB,GAAG3O,CAAK,CAAEI,sBAAoB,4BAA4BC,wBAAsB,kBAAkBC,0BAAwB,eAC1N,CACA,SAASuO,EAAQ,CACf,GAAG7O,EACgD,EACnD,MAAO,UAAC0O,EAAAA,CAAgBtO,sBAAoB,kBAAkBC,wBAAsB,UAAUC,0BAAwB,uBAClH,UAACsO,EAAAA,EAAqB,EAAC1O,YAAU,UAAW,GAAGF,CAAK,CAAEI,sBAAoB,wBAAwBE,0BAAwB,iBAEhI,CACA,SAASwO,EAAe,CACtB,GAAG9O,EACmD,EACtD,MAAO,UAAC4O,EAAAA,EAAwB,EAAC1O,YAAU,kBAAmB,GAAGF,CAAK,CAAEI,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,eACxL,CACA,SAASyO,EAAe,WACtBhP,CAAS,YACTc,EAAa,CAAC,CACdQ,UAAQ,CACR,GAAGrB,EACmD,EACtD,MAAO,UAAC4O,EAAAA,EAAuB,EAACxO,sBAAoB,0BAA0BC,wBAAsB,iBAAiBC,0BAAwB,uBACzI,WAACsO,EAAAA,EAAwB,EAAC1O,YAAU,kBAAkBW,WAAYA,EAAYd,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yaAA0aJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,2BAA2BE,0BAAwB,wBACjmBe,EACD,UAACuN,EAAAA,EAAsB,EAAC7O,UAAU,+FAA+FK,sBAAoB,yBAAyBE,0BAAwB,oBAG9M,CCNA,IAAM0O,EAAiBC,EAAAA,aAAmB,CAA6B,MACvE,SAASC,IACP,IAAMC,EAAUF,EAAAA,UAAgB,CAACD,GACjC,GAAI,CAACG,EACH,MAAM,CADM,KACI,qDAElB,OAAOA,CACT,CACA,SAASC,EAAgB,aACvBC,GAAc,CAAI,CAClBC,KAAMC,CAAQ,CACdC,aAAcC,CAAW,WACzB1P,CAAS,CACT2P,OAAK,UACLrO,CAAQ,CACR,GAAGrB,EAKJ,EACC,IAAM2P,EJhDD,SAASC,EIgDcA,GJ/CtB,CAACD,EAAUE,EAAY,CAAGZ,EAAAA,QAAc,MAAsBa,GAUpE,OATAb,EAAAA,SAAe,CAAC,KACd,IAAMc,EAAMC,OAAOC,UAAU,CAAC,CAAC,YAAY,EAAEC,OACvCC,EAAW,KACfN,EAAYG,IAFmD,EAAE,CAE9CI,EAFiD,CAAC,OAExC,GAAGF,EAClC,EAGA,OAFAH,EAAIM,gBAAgB,CAAC,SAAUF,GAC/BN,EAAYG,OAAOI,UAAU,CATP,EASUF,GACzB,IAAMH,EAAIO,mBAAmB,CAAC,SAAUH,EACjD,EAAG,EAAE,EACE,CAAC,CAACR,CACX,IIqCQ,CAACY,EAAYC,EAAc,CAAGvB,EAAAA,QAAc,CAAC,IAI7C,CAACwB,EAAOC,EAAS,CAAGzB,EAAAA,QAAc,CAACI,GACnCC,EAAOC,GAAYkB,EACnBE,EAAU1B,EAAAA,WAAiB,CAAC,IAChC,IAAM2B,EAA6B,YAAjB,OAAOC,EAAuBA,EAAMvB,GAAQuB,EAC1DpB,EACFA,EAAYmB,GAEZF,EAASE,GAIXE,CAPiB,QAORC,MAAM,CAAG,GAAGC,cAAuBJ,MAAH,CAAC,GAAY,iBAA4C,CAA1B,CACvE,CADyEK,EAC3D3B,EAAK,EAGhB4B,EAAgBjC,EAAAA,WAAiB,CAAC,IAC/BU,EAAWa,EAAclB,GAAQ,CAACA,GAAQqB,EAAQrB,GAAQ,CAACA,GACjE,CAACK,EAAUgB,EAASH,EAAc,EAGrCvB,EAAAA,SAAe,CAAC,KACd,IAAMkC,EAAgB,IAzDQ,MA0DxBC,EAAMC,GAAG,GAAmCD,EAAME,OAAO,EAAIF,EAAMG,OAAO,GAAG,CAC/EH,EAAMI,KADwCJ,SAC1B,GACpBF,IAEJ,EAEA,OADAlB,OAAOK,gBAAgB,CAAC,UAAWc,GAC5B,IAAMnB,OAAOM,mBAAmB,CAAC,UAAWa,EACrD,EAAG,CAACD,EAAc,EAIlB,IAAMO,EAAQnC,EAAO,WAAa,YAC5BoC,EAAezC,EAAAA,OAAa,CAAsB,IAAO,EAC7DwC,QACAnC,eACAqB,WACAhB,aACAY,gBACAC,gBACAU,EACF,EAAI,CAACO,EAAOnC,EAAMqB,EAAShB,EAAUY,EAAYC,EAAeU,EAAc,EAC9E,MAAO,UAAClC,EAAe2C,QAAQ,EAACd,MAAOa,EAActR,sBAAoB,0BAA0BC,wBAAsB,kBAAkBC,0BAAwB,uBAC/J,UAACoO,EAAeA,CAACC,YAADD,EAAgB,EAAGtO,sBAAoB,kBAAkBE,0BAAwB,uBAC/F,UAAC6F,MAAAA,CAAIjG,YAAU,kBAAkBwP,MAAO,CACxC,kBArFc,CAqFKkC,OACnB,uBApFmB,CAoFKC,MACxB,GAAGnC,CAAK,EACgB3P,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kFAAmFJ,GAAa,GAAGC,CAAK,UAC3IqB,OAIX,CACA,SAASyQ,EAAQ,MACf1D,EAAO,MAAM,SACbnN,EAAU,SAAS,CACnB8Q,cAAc,WAAW,WACzBhS,CAAS,UACTsB,CAAQ,CACR,GAAGrB,EAKJ,EACC,GAAM,CACJ2P,UAAQ,CACR8B,OAAK,YACLlB,CAAU,eACVC,CAAa,CACd,CAAGtB,UACJ,QAA4B,CAAxB6C,EACK,UAAC5L,MAAAA,CAAIjG,YAAU,UAAUH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8EAA+EJ,GAAa,GAAGC,CAAK,UAC7IqB,IAGHsO,EACK,QADK,EACJ5B,EAAKA,CAACuB,EAADvB,GAAOwC,EAAYf,aAAcgB,EAAgB,GAAGxQ,CAAK,UAClE,WAACmO,EAAYA,CAAC6D,SAAD7D,MAAc,UAAUjO,YAAU,UAAU+R,cAAY,OAAOlS,UAAU,+EAA+E2P,MAAO,CAC5K,kBAvHqB,CAuHFwC,MACrB,EAA0B9D,KAAMA,YAC5B,WAACE,EAAWA,CAACvO,QAADuO,EAAW,oBACrB,UAACC,EAAUA,QAAAA,EAAC,YACZ,UAACC,EAAgBA,UAAC,IAADA,gCAEnB,UAACrI,MAAAA,CAAIpG,UAAU,uCAA+BsB,SAI/C,WAAC8E,MAAAA,CAAIpG,UAAU,qDAAqDoS,aAAYV,EAAOW,mBAAkBX,gBAAwBM,EAAc,GAAI5Q,eAAcF,EAASoR,YAAWjE,EAAMlO,YAAU,UAAUG,wBAAsB,UAAUC,0BAAwB,wBAE1Q,UAAC6F,MAAAA,CAAIjG,YAAU,cAAcH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0FAA2F,yCAA0C,qCAAkD,aAAZc,GAAsC,UAAZA,EAAsB,mFAAqF,4DAC3V,UAACkF,MAAAA,CAAIjG,YAAU,oBAAoBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uHAAwHiO,WAAkB,iFAAmF,mFAEpQ,CADZ,YACAnN,GAAsC,UAAZA,EAAsB,2BADM,gEACuF,0HAA2HlB,GAAa,GAAGC,CAAK,UACzR,UAACmG,MAAAA,CAAI6L,eAAa,UAAU9R,YAAU,gBAAgBH,UAAU,4NAC7DsB,QAIX,CACA,SAASiR,EAAe,WACtBvS,CAAS,SACTsG,CAAO,CACP,GAAGrG,EACiC,EACpC,GAAM,eACJkR,CAAa,CACd,CAAGhC,IACJ,MAAO,WAAC9I,EAAAA,CAAMA,CAAAA,CAAC4L,eAAa,UAAU9R,YAAU,kBAAkBe,QAAQ,QAAQgH,KAAK,OAAOlI,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,SAAUJ,GAAYsG,QAAS+K,IACzI/K,IAAU+K,GACVF,GACF,EAAI,GAAGlR,CAAK,CAAEI,sBAAoB,SAASC,wBAAsB,iBAAiBC,0BAAwB,wBACtG,UAACiS,EAAAA,CAAaA,CAAAA,CAACnS,sBAAoB,gBAAgBE,0BAAwB,gBAC3E,UAACiB,OAAAA,CAAKxB,UAAU,mBAAU,qBAEhC,CACA,SAASyS,EAAY,WACnBzS,CAAS,CACT,GAAGC,EAC4B,EAC/B,GAAM,eACJkR,CAAa,CACd,CAAGhC,IACJ,MAAO,UAACuD,SAAAA,CAAOT,eAAa,OAAO9R,YAAU,eAAewS,aAAW,iBAAiBC,SAAU,CAAC,EAAGtM,QAAS6K,EAAexD,MAAM,iBAAiB3N,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,kPAAmP,2EAA4E,yHAA0H,0JAA2J,4DAA6D,4DAA6DJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,cAAcC,0BAAwB,eACt8B,CACA,SAASsS,EAAa,WACpB7S,CAAS,CACT,GAAGC,EAC0B,EAC7B,MAAO,UAAC6S,OAAAA,CAAK3S,YAAU,gBAAgBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsD,kNAAmNJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,eAAeC,0BAAwB,eACpZ,CACA,SAASwS,EAAa,CACpB/S,WAAS,CACT,GAAGC,EACgC,EACnC,MAAO,UAACqH,EAAAA,CAAKA,CAAAA,CAACnH,YAAU,gBAAgB8R,eAAa,QAAQjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,uCAAwCJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,QAAQC,wBAAsB,eAAeC,0BAAwB,eACrO,CACA,SAASyS,EAAc,CACrBhT,WAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,iBAAiB8R,eAAa,SAASjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,gBAAgBC,0BAAwB,eAC7L,CACA,SAAS0S,EAAc,WACrBjT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,iBAAiB8R,eAAa,SAASjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0BAA2BJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,gBAAgBC,0BAAwB,eAC7L,CACA,SAAS2S,EAAiB,WACxBlT,CAAS,CACT,GAAGC,EACoC,EACvC,MAAO,UAACkT,EAAAA,SAASA,CAAAA,CAAChT,YAAU,oBAAoB8R,eAAa,YAAYjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,YAAYC,wBAAsB,mBAAmBC,0BAAwB,eAClP,CACA,SAAS6S,EAAe,WACtBpT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,kBAAkB8R,eAAa,UAAUjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iGAAkGJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,iBAAiBC,0BAAwB,eACvQ,CACA,SAAS8S,EAAa,WACpBrT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,gBAAgB8R,eAAa,QAAQjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,4CAA6CJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,eAAeC,0BAAwB,eAC5M,CACA,SAAS+S,EAAkB,CACzBtT,WAAS,SACTuI,GAAU,CAAK,CACf,GAAGtI,EAGJ,EACC,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,MAC9B,MAAO,UAACD,EAAAA,CAAKrI,YAAU,sBAAsB8R,eAAa,cAAcjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2OAA4O,8EAA+EJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,oBAAoBC,0BAAwB,eACvgB,CACA,SAASgT,EAAmB,WAC1BvT,CAAS,CACTuI,WAAU,CAAK,CACf,GAAGtI,EAGJ,EACC,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,SAC9B,MAAO,UAACD,EAAAA,CAAKrI,YAAU,uBAAuB8R,eAAa,eAAejS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6RAExF,CADA,+CACiD,GADC,oCACuCJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,qBAAqBC,0BAAwB,eAChN,CACA,SAASiT,EAAoB,WAC3BxT,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,wBAAwB8R,eAAa,gBAAgBjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iBAAkBJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,sBAAsBC,0BAAwB,eACxM,CACA,SAASkT,EAAY,WACnBzT,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAACyT,KAAAA,CAAGvT,YAAU,eAAe8R,eAAa,OAAOjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,cAAcC,0BAAwB,eACjM,CACA,SAASoT,EAAgB,WACvB3T,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAAC2T,KAAAA,CAAGzT,YAAU,oBAAoB8R,eAAa,YAAYjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,kBAAkBC,0BAAwB,eACrM,CACA,IAAMsT,EAA4BnM,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,ozBAAqzB,CACz1BC,SAAU,CACRzG,QAAS,CACP0G,QAAS,+DACTE,QAAS,8KACX,EACAI,KAAM,CACJN,QAAS,cACTO,GAAI,cACJC,GAAI,iDACN,CACF,EACAE,gBAAiB,CACfpH,QAAS,UACTgH,KAAM,SACR,CACF,GACA,SAAS4L,EAAkB,SACzBvL,GAAU,CAAK,CACfsF,WAAW,EAAK,CAChB3M,UAAU,SAAS,MACnBgH,EAAO,SAAS,SAChB6L,CAAO,WACP/T,CAAS,CACT,GAAGC,EAK6C,EAChD,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,SACxB,UACJmH,CAAQ,OACR8B,CAAK,CACN,CAAGvC,IACEuD,EAAS,UAAClK,EAAAA,CAAKrI,YAAU,sBAAsB8R,eAAa,cAAclJ,YAAWb,EAAM8L,cAAanG,EAAU7N,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACyT,EAA0B,SAC9J3S,OACAgH,CACF,GAAIlI,GAAa,GAAGC,CAAK,UACzB,GAGuB,CAHnB,KAAU,IAGV,OAAO8T,IACTA,EAAU,CACRzS,SAAUyS,EACZ,EAEK,WAACjF,EAAOA,CAACzO,IAADyO,kBAAqB,UAAUxO,wBAAsB,oBAAoBC,0BAAwB,wBAC5G,UAACwO,EAAcA,CAACxG,OAAO,IAARwG,sBAA6B,iBAAiBxO,0BAAwB,uBAAemS,IACpG,UAAC1D,EAAcA,CAACX,KAAK,MAANW,EAAciF,MAAM,SAASC,OAAkB,cAAVxC,GAAyB9B,EAAW,GAAGmE,CAAO,CAAE1T,sBAAoB,iBAAiBE,0BAAwB,oBAT5JmS,CAWX,CACA,SAASyB,EAAkB,WACzBnU,CAAS,SACTuI,GAAU,CAAK,aACf6L,GAAc,CAAK,CACnB,GAAGnU,EAIJ,EACC,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,SAC9B,MAAO,UAACD,EAAAA,CAAKrI,YAAU,sBAAsB8R,eAAa,cAAcjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mVAEtF,CADA,+CACiD,GADC,qCACwC,+CAAgD,0CAA2C,uCAAwCgU,GAAe,2LAA4LpU,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,oBAAoBC,0BAAwB,eAC9hB,CACA,SAAS8T,EAAiB,WACxBrU,CAAS,CACT,GAAGC,EACyB,EAC5B,MAAO,UAACmG,MAAAA,CAAIjG,YAAU,qBAAqB8R,eAAa,aAAajS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yKAA0K,2HAA4H,wCAAyC,+CAAgD,0CAA2C,uCAAwCJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,mBAAmBC,0BAAwB,eAC/nB,CACA,SAAS+T,EAAoB,WAC3BtU,CAAS,UACTuU,GAAW,CAAK,CAChB,GAAGtU,EAGJ,EAEC,IAAMuU,EAAQtF,EAAAA,OAAa,CAAC,IACnB,GAAGuF,KAAKC,KAAK,CAAiB,GAAhBD,KAAKE,MAAM,IAAW,GAAG,CAAC,CAAC,CAC/C,EAAE,EACL,MAAO,WAACvO,MAAAA,CAAIjG,YAAU,wBAAwB8R,eAAa,gBAAgBjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8CAA+CJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,sBAAsBC,0BAAwB,wBAC9NgU,GAAY,UAAC7F,EAAQA,CAAC1O,KAAD0O,KAAW,oBAAoBuD,eAAa,uBAClE,UAACvD,EAAQA,CAAC1O,KAAD0O,KAAW,sCAAsCuD,eAAa,qBAAqBtC,MAAO,CACnG,mBAAoB6E,CACtB,EAA0BnU,sBAAoB,WAAWE,0BAAwB,kBAErF,CACA,SAASqU,EAAe,WACtB5U,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAACyT,KAAAA,CAAGvT,YAAU,mBAAmB8R,eAAa,WAAWjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iGAAkG,uCAAwCJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,iBAAiBC,0BAAwB,eAChT,CACA,SAASsU,EAAmB,WAC1B7U,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAAC2T,KAAAA,CAAGzT,YAAU,wBAAwB8R,eAAa,gBAAgBjS,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+BAAgCJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,qBAAqBC,0BAAwB,eACpN,CACA,SAASuU,EAAqB,SAC5BvM,EAAU,EAAK,MACfL,EAAO,IAAI,UACX2F,GAAW,CAAK,WAChB7N,CAAS,CACT,GAAGC,EAKJ,EACC,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,IAC9B,MAAO,UAACD,EAAAA,CAAKrI,YAAU,0BAA0B8R,eAAa,kBAAkBlJ,YAAWb,EAAM8L,cAAanG,EAAU7N,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gfAAif,yFAAmG,OAAT8H,GAAiB,UAAoB,OAATA,GAAiB,UAAW,uCAAwClI,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,uBAAuBC,0BAAwB,eAC16B,mBC5XA,uCAA8J,CAE9J,uCAA0J,CAE1J,uCAAkK,CAElK,uCAAmL,CAEnL,uCAA+J,CAE/J,uCAA2J,CAE3J,uCAAmK,CAEnK,uCAA8J,CAE9J,uCAA6H,CAE7H,uCAAyJ,iHChBzJ,IAAMwU,EAAa7F,EAAAA,UAAgB,CAAC,CAAC,CACnC8F,QAAM,QACNC,CAAM,qBACNC,CAAmB,CAKpB,CAAEC,KACD,IAAMC,EAAYlG,EAAAA,OAAa,CAAC,KAC9B,GAAI,CAACgG,EAAqB,OAAOF,EAAOI,SAAS,CACjD,IAAMC,EAAQL,EAAOI,SAAS,CAACE,SAAS,CAACC,GAAYA,EAASC,EAAE,GAAKN,GACrE,OAAOF,EAAOI,SAAS,CAACpO,KAAK,CAACqO,EAAQ,EACxC,EAAG,CAACL,EAAOI,SAAS,CAAEF,EAAoB,EAC1C,MAAO,WAAC9O,MAAAA,CAAI+O,IAAKA,EAAKnV,UAAW,CAAC,wEAAwE,CAAC,WACpGiV,GAAU,UAAC7O,MAAAA,CAAIoP,GAAG,mBAAmBxV,UAAU,oEAChD,WAACoG,MAAAA,CAAIpG,UAAU,kDACZgV,EAAO3M,IAAI,EAAI2M,EAAO3M,IAAI,CAC3B,WAACjC,MAAAA,CAAIpG,UAAU,0BACb,WAACoG,MAAAA,WACEgP,EAAUK,MAAM,CAAG,GAAKL,EAAUM,GAAG,CAACH,GAAY,WAACrG,EAAAA,QAAc,YAC5D,UAAC1N,OAAAA,CAAKxB,UAAU,sCACbuV,EAASI,IAAI,GAEhB,UAACnU,OAAAA,CAAKxB,UAAU,gBAAO,QAJ2CuV,EAASC,EAAE,GAMnF,UAAChU,OAAAA,UAAMwT,EAAOW,IAAI,MAEnBX,EAAOY,QAAQ,EAAI,UAACpU,OAAAA,CAAKxB,UAAU,yCAC/BgV,EAAOY,QAAQ,SAIvBZ,EAAOlH,QAAQ,EAAE2H,OAAS,UAACrP,MAAAA,CAAIpG,UAAU,kDACrCgV,EAAOlH,QAAQ,CAAC4H,GAAG,CAAC,CAACG,EAAIC,IAAM,UAACrP,MAAAA,CAAiBzG,UAAU,iGACvD6V,GADqCA,EAAKC,MAGxC,OAEnB,GCvCe,SAASC,IACtB,GAAM,SACJC,CAAO,cACPC,CAAY,CACb,CAAGC,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,GACd,MAAO,UAACC,EAAAA,WAAWA,CAAAA,CAACpI,MAAOiI,EAASI,SAAU,CAAC,MAC7CC,CAAI,QACJpB,CAAM,CACP,GAAqB,UAAhB,OAAOoB,EAAoB,UAACjQ,MAAAA,CAAIpG,UAAU,0EACrCqW,IACM,UD+BJtB,EC/BeA,CAACC,OAAQqB,CAATtB,CD+BJ,OC/B2BE,EAAQC,oBAAqBe,GAAgB,KAAQ5V,sBAAoB,cAAcC,wBAAsB,gBAAgBC,0BAAwB,qBAC1M,CD6BAwU,EAAWuB,WAAW,CAAG,gCEbzB,MA3B0B,KACxB,GAAM,OACJC,CAAK,KAyBMC,KAxBXC,CAAQ,CACT,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,EAuBkBF,CAF9BG,CAAAA,EAAAA,EAAAA,kBAAAA,CAAkBA,CAACC,CAjBE,CACnBpB,GAAI,cACJG,KAAM,eACN7H,SAAU,CAAC,IAAK,IAAI,CACpB+I,QAAS,QACTC,QARkB,CAQTC,IAPTN,EAASF,YAAoB,OAAS,QACxC,CAOA,EAAG,CACDf,GAAI,gBACJG,KAAM,kBACNkB,QAAS,QACTC,QAAS,IAAML,EAAS,QAC1B,EAAG,CACDjB,GAAI,eACJG,KAAM,iBACNkB,QAAS,QACTC,QAAS,IAAML,EAAS,OAC1B,EAAE,CAC8B,CAACF,EAAM,CACzC,ECpBe,SAASS,EAAK,CAC3B1V,UAAQ,CAGT,EACC,IAAM2V,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAGlBC,EAAUC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,KAEtB,IAAMC,EAAczJ,IAClBqJ,EAAOK,IAAI,CAAC1J,EACd,EACA,OAAOF,EAAAA,CAAQA,CAAC6J,OAAO,CAACC,IAEtB,IAAMC,EAA6B,MAAhBD,EAAQ5J,GAAG,CAAW,CACvC4H,GAAI,GAAGgC,EAAQ7J,KAAK,CAAC+J,WAAW,GAAG,MAAM,CAAC,CAC1C/B,KAAM6B,EAAQ7J,KAAK,CACnBG,SAAU0J,EAAQ1J,QAAQ,CAC1B6J,SAAUH,EAAQ7J,KAAK,CAAC+J,WAAW,GACnCb,QAAS,aACTjB,SAAU,CAAC,MAAM,EAAE4B,EAAQ7J,KAAK,EAAE,CAClCmJ,QAAS,IAAMO,EAAWG,EAAQ5J,GAAG,CACvC,EAAI,KAGEgK,EAAeJ,EAAQzJ,KAAK,EAAE2H,IAAImC,GAAc,EACpDrC,GAAI,GAAGqC,CAD6C,CACnClK,KAAK,CAAC+J,WAAW,GAAG,MAAM,CAAC,CAC5C/B,KAAMkC,EAAUlK,KAAK,CACrBG,SAAU+J,EAAU/J,QAAQ,CAC5B6J,SAAUE,EAAUlK,KAAK,CAAC+J,WAAW,GACrCb,QAASW,EAAQ7J,KAAK,CACtBiI,SAAU,CAAC,MAAM,EAAEiC,EAAUlK,KAAK,EAAE,CACpCmJ,QAAS,IAAMO,EAAWQ,EAAUjK,GAAG,EACzC,IAAO,EAAE,CAGT,OAAO6J,EAAa,CAACA,KAAeG,EAAa,CAAGA,CACtD,EACF,EAAG,CAACX,EAAO,EACX,MAAO,UAACa,EAAAA,YAAYA,CAAAA,CAACX,QAASA,EAAS9W,sBAAoB,eAAeC,wBAAsB,OAAOC,0BAAwB,qBAC3H,UAACwX,EAAAA,CAAc1X,sBAAoB,gBAAgBE,0BAAwB,qBAAae,KAE9F,CACA,IAAMyW,EAAgB,CAAC,CACrBzW,UAAQ,CAGT,IACCkV,IACO,eADUA,MACV,YACH,UAACwB,EAAAA,UAAUA,CAAAA,CAAC3X,sBAAoB,aAAaE,0BAAwB,qBACnE,UAAC0X,EAAAA,cAAcA,CAAAA,CAACjY,UAAU,+DAA+DK,sBAAoB,iBAAiBE,0BAAwB,qBACpJ,WAAC2X,EAAAA,YAAYA,CAAAA,CAAClY,UAAU,iIAAiIK,sBAAoB,eAAeE,0BAAwB,sBAClN,UAAC6F,MAAAA,CAAIpG,UAAU,4DACb,UAACmY,EAAAA,UAAUA,CAAAA,CAACnY,UAAU,oHAAoHK,sBAAoB,aAAaE,0BAAwB,gBAErM,UAAC6F,MAAAA,CAAIpG,UAAU,yBACb,UAAC+V,EAAaA,CAAC1V,UAAD0V,YAAqB,gBAAgBxV,0BAAwB,uBAKlFe,iIChEA,SAAS8W,IACd,GAAM,MACJ7T,CAAI,CACL,CAAG8T,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACLpB,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GACxB,GAAI3S,EACF,IADQ,EACD,WAAC7D,EAAAA,EAAYA,CAAAA,WAChB,UAACE,EAAAA,EAAmBA,CAAAA,CAAC2H,OAAO,aAC1B,UAAClC,EAAAA,CAAMA,CAAAA,CAACnF,QAAQ,QAAQlB,UAAU,yCAChC,UAAC0G,EAAAA,CAAiBA,CAAAA,CAACnC,KAAMA,QAG7B,WAAC1D,EAAAA,EAAmBA,CAAAA,CAACb,UAAU,OAAOiU,MAAM,MAAMnT,WAAY,GAAIwX,UAAU,cAC1E,UAAC5W,EAAAA,EAAiBA,CAAAA,CAAC1B,UAAU,uBAC3B,WAACoG,MAAAA,CAAIpG,UAAU,oCACb,UAACuY,IAAAA,CAAEvY,UAAU,4CACVuE,EAAKwC,QAAQ,GAEhB,UAACwR,IAAAA,CAAEvY,UAAU,sDACVuE,EAAK2C,cAAc,CAAC,EAAE,CAACC,YAAY,QAI1C,UAACxF,EAAAA,EAAqBA,CAAAA,CAAAA,GACtB,WAACZ,EAAAA,CAAiBA,CAAAA,WAChB,UAACC,EAAAA,EAAgBA,CAAAA,CAACsF,QAAS,IAAM2Q,EAAOK,IAAI,CAAC,+BAAuB,YAGpE,UAACtW,EAAAA,EAAgBA,CAAAA,UAAC,YAClB,UAACA,EAAAA,EAAgBA,CAAAA,UAAC,aAClB,UAACA,EAAAA,EAAgBA,CAAAA,UAAC,gBAEpB,UAACW,EAAAA,EAAqBA,CAAAA,CAAAA,GACtB,UAACX,EAAAA,EAAgBA,CAAAA,UACf,UAACwX,EAAAA,EAAaA,CAAAA,CAACC,YAAY,yBAKvC,iFC3CA,SAASC,EAAY,CACnB,GAAGzY,EACoD,EACvD,MAAO,UAAC0Y,EAAAA,EAAyB,EAACxY,YAAU,cAAe,GAAGF,CAAK,CAAEI,sBAAoB,4BAA4BC,wBAAsB,cAAcC,0BAAwB,mBACnL,CACA,SAASqY,EAAmB,CAC1B,GAAG3Y,EACkE,EACrE,MAAO,UAAC0Y,EAAAA,EAAuC,EAACxY,YAAU,sBAAuB,GAAGF,CAAK,CAAEI,sBAAoB,0CAA0CC,wBAAsB,qBAAqBC,0BAAwB,mBAC9N,CACA,SAASsY,EAAmB,CAC1B,GAAG5Y,EACkE,EACrE,MAAO,UAAC0Y,EAAAA,EAAuC,EAACxY,YAAU,sBAAuB,GAAGF,CAAK,CAAEI,sBAAoB,0CAA0CC,wBAAsB,qBAAqBC,0BAAwB,mBAC9N,yOEPO,SAASuY,EAAY,SAC1BC,CAAO,CACPC,eAAa,CACbC,gBAAc,CAKf,EACC,GAAM,CAACC,EAAgBC,EAAkB,CAAGjK,EAAAA,QAAc,CAAqB8J,IAAkBD,EAAQtD,MAAM,CAAG,EAAIsD,CAAO,CAAC,EAAE,MAAGhJ,CAAAA,CAAQ,EACrIqJ,EAAqB,IACzBD,EAAkBE,GACdJ,GACFA,EAAeI,EAAO7D,EAAE,CAE5B,MAHsB,GAItB,EAGO,EAHH,CAGG,OAAC/B,EAAAA,EAHa,SAGFA,CAAAA,CAACpT,sBAAoB,cAAcC,wBAAsB,cAAcC,0BAAwB,4BAC9G,UAACoT,EAAAA,eAAeA,CAAAA,CAACtT,sBAAoB,kBAAkBE,0BAAwB,4BAC7E,WAACG,EAAAA,EAAYA,CAAAA,CAACL,sBAAoB,eAAeE,0BAAwB,6BACvE,UAACK,EAAAA,EAAmBA,CAAAA,CAAC2H,OAAO,IAAClI,sBAAoB,sBAAsBE,0BAAwB,4BAC7F,WAACuT,EAAAA,iBAAiBA,CAAAA,CAAC5L,KAAK,KAAKlI,UAAU,uFAAuFK,sBAAoB,oBAAoBE,0BAAwB,6BAC5L,UAAC6F,MAAAA,CAAIpG,UAAU,uHACb,UAACsZ,EAAAA,CAAkBA,CAAAA,CAACtZ,UAAU,SAASK,sBAAoB,qBAAqBE,0BAAwB,uBAE1G,WAAC6F,MAAAA,CAAIpG,UAAU,+CACb,UAACwB,OAAAA,CAAKxB,UAAU,yBAAgB,iBAChC,UAACwB,OAAAA,CAAKxB,UAAU,YAAIkZ,EAAevD,IAAI,MAEzC,UAAC4D,EAAAA,CAAcA,CAAAA,CAACvZ,UAAU,UAAUK,sBAAoB,iBAAiBE,0BAAwB,0BAGrG,UAACM,EAAAA,EAAmBA,CAAAA,CAACb,UAAU,0CAA0CiU,MAAM,QAAQ5T,sBAAoB,sBAAsBE,0BAAwB,4BACtJwY,EAAQrD,GAAG,CAAC2D,GAAU,WAACrY,EAAAA,EAAgBA,CAAAA,CAAiBwY,SAAU,IAAMJ,EAAmBC,aACvFA,EAAO1D,IAAI,CAAE,IACb0D,EAAO7D,EAAE,GAAK0D,EAAe1D,EAAE,EAAI,UAACiE,EAAAA,CAAKA,CAAAA,CAACzZ,UAAU,cAFXqZ,EAAO7D,EAAE,YAlBxD,IA0BX,CCpCuB,EAEfkE,CAAWA,CAEjB,IACIX,EAAU,CAAC,CACfvD,GAAI,IACJG,KAAM,UACR,EAAG,CACDH,GAAI,IACJG,KAAM,WACR,EAAG,CACDH,GAAI,IACJG,KAAM,WACR,EAAE,CACa,SAASgE,IACtB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,GACtB,QACJC,CAAM,CACP,CFlCI,SAASC,EACd,EEiCiBA,CFjCX,CAACD,EAAQE,EAAU,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAcrC,MAAO,QAAEH,CAAO,CAClB,IEmBQ,MACJvV,CAAI,CACL,CAAG8T,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CACJ9T,KAAM2V,CAAQ,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACLlD,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAIlBkD,EAAerB,CAAO,CAAC,EAAE,CAGzBsB,EAAmBnL,EAAAA,OAAa,CAAC,KACrC,GAAI,CAACgL,GAAUxQ,KAAM,OAAOgE,EAAAA,CAAQA,CACpC,IAAM4M,EAAWJ,EAASxQ,IAAI,CAC9B,OAAOgE,EAAAA,CAAQA,CAAC6M,MAAM,CAAClE,GAErB,CAAKA,EAAKhJ,CAAN,IAAW,EAA0B,GAAG,CAAzBgJ,EAAKhJ,IAA2B,CAAtB,CAACoI,MAAM,EAG7BY,EAAKhJ,KAAK,CAACE,QAAQ,CAAC+M,GAE/B,EAAG,CAACJ,GAAUxQ,KAAK,EAInB,OAHAwF,EAAAA,SAAe,CAAC,KAEhB,EAAG,CAAC4K,EAAO,EACJ,WAAC/H,EAAAA,OAAOA,CAAAA,CAACC,YAAY,OAAO3R,sBAAoB,UAAUC,wBAAsB,aAAaC,0BAAwB,4BACxH,UAACyS,EAAAA,aAAaA,CAAAA,CAAC3S,sBAAoB,gBAAgBE,0BAAwB,2BACzE,UAACuY,EAAWA,CAACC,QAASA,EAASC,cAAeoB,EAAcnB,eAtBtCuB,CAsBsDC,GApBlF,EAoBsGpa,sBAAoB,cAAcE,0BAAwB,sBAE5J,UAAC6S,EAAAA,cAAcA,CAAAA,CAACpT,UAAU,oBAAoBK,sBAAoB,iBAAiBE,0BAAwB,2BACzG,WAAC8S,EAAAA,YAAYA,CAAAA,CAAChT,sBAAoB,eAAeE,0BAAwB,4BACvE,UAAC+S,EAAAA,iBAAiBA,CAAAA,CAACjT,sBAAoB,oBAAoBE,0BAAwB,2BAAkB,OACrG,UAACkT,EAAAA,WAAWA,CAAAA,CAACpT,sBAAoB,cAAcE,0BAAwB,2BACpE8Z,EAAiB3E,GAAG,CAACW,IACtB,IAAMqE,EAAOrE,EAAKhO,IAAI,CAAGzG,EAAAA,EAAK,CAACyU,EAAKhO,IAAI,CAAC,CAAGzG,EAAAA,EAAKA,CAACG,IAAI,CACtD,OAAOsU,GAAMtI,OAASsI,GAAMtI,OAAO0H,OAAS,EAAI,UAACiD,EAAWA,CAAkBnQ,OAAO,CAAzBmQ,CAAyB,EAACpJ,YAAa+G,EAAKxI,QAAQ,CAAE7N,UAAU,6BACtH,WAAC2T,EAAAA,eAAeA,CAAAA,WACd,UAACiF,EAAkBA,CAACrQ,OAAO,QAARqQ,KACjB,WAAC9E,EAAAA,iBAAiBA,CAAAA,CAACC,QAASsC,EAAK1I,KAAK,CAAEE,SAAU+L,IAAavD,EAAKzI,GAAG,WACpEyI,EAAKhO,IAAI,EAAI,UAACqS,EAAAA,CAAAA,GACf,UAAClZ,OAAAA,UAAM6U,EAAK1I,KAAK,GACjB,UAAC7K,EAAAA,CAAgBA,CAAAA,CAAC9C,UAAU,iGAGhC,UAAC6Y,EAAkBA,UACjB,MADiBA,EACjB,EAACjE,EAAAA,cAAcA,CAAAA,UACZyB,EAAKtI,KAAK,EAAE2H,IAAIiF,GAAW,UAAC9F,EAAAA,kBAAkBA,CAAAA,UAC3C,UAACC,EAAAA,oBAAoBA,CAAAA,CAACvM,OAAO,IAACsF,SAAU+L,IAAae,EAAQ/M,GAAG,UAC9D,UAACgN,IAAIA,CAACC,KAAMF,EAAQ/M,GAAG,UACrB,UAACpM,OAAAA,UAAMmZ,EAAQhN,KAAK,QAHyBgN,EAAQhN,KAAK,WAXZ0I,EAAK1I,KAAK,EAqBvD,UAACgG,EAAAA,eAAeA,CAAAA,UAC/B,UAACG,EAAAA,iBAAiBA,CAAAA,CAACvL,OAAO,IAACwL,QAASsC,EAAK1I,KAAK,CAAEE,SAAU+L,IAAavD,EAAKzI,GAAG,UAC7E,WAACgN,IAAIA,CAACC,KAAMxE,EAAKzI,GAAG,WAClB,UAAC8M,EAAAA,CAAAA,GACD,UAAClZ,OAAAA,UAAM6U,EAAK1I,KAAK,SAJgB0I,EAAK1I,KAAK,CAQvD,UAIJ,UAACsF,EAAAA,aAAaA,CAAAA,CAAC5S,sBAAoB,gBAAgBE,0BAAwB,2BACzE,UAACkT,EAAAA,WAAWA,CAAAA,CAACpT,sBAAoB,cAAcE,0BAAwB,2BACrE,UAACoT,EAAAA,eAAeA,CAAAA,CAACtT,sBAAoB,kBAAkBE,0BAAwB,2BAC7E,WAACG,EAAAA,EAAYA,CAAAA,CAACL,sBAAoB,eAAeE,0BAAwB,4BACvE,UAACK,EAAAA,EAAmBA,CAAAA,CAAC2H,OAAO,IAAClI,sBAAoB,sBAAsBE,0BAAwB,2BAC7F,WAACuT,EAAAA,iBAAiBA,CAAAA,CAAC5L,KAAK,KAAKlI,UAAU,uFAAuFK,sBAAoB,oBAAoBE,0BAAwB,4BAC3LgE,GAAQ,UAACmC,EAAAA,CAAiBA,CAAAA,CAAC1G,UAAU,qBAAqB2G,QAAQ,IAACpC,KAAMA,IAC1E,UAACuW,EAAAA,CAAgBA,CAAAA,CAAC9a,UAAU,iBAAiBK,sBAAoB,mBAAmBE,0BAAwB,yBAGhH,WAACM,EAAAA,EAAmBA,CAAAA,CAACb,UAAU,8DAA8DqO,KAAK,SAAS4F,MAAM,MAAMnT,WAAY,EAAGT,sBAAoB,sBAAsBE,0BAAwB,4BACtM,UAACmB,EAAAA,EAAiBA,CAAAA,CAAC1B,UAAU,kBAAkBK,sBAAoB,oBAAoBE,0BAAwB,2BAC7G,UAAC6F,MAAAA,CAAIpG,UAAU,uBACZuE,GAAQ,UAACmC,EAAAA,CAAiBA,CAAAA,CAAC1G,UAAU,qBAAqB2G,QAAQ,IAACpC,KAAMA,QAG9E,UAAC5C,EAAAA,EAAqBA,CAAAA,CAACtB,sBAAoB,wBAAwBE,0BAAwB,oBAE3F,WAACQ,EAAAA,CAAiBA,CAAAA,CAACV,sBAAoB,oBAAoBE,0BAAwB,4BACjF,WAACS,EAAAA,EAAgBA,CAAAA,CAACsF,QAAS,IAAM2Q,EAAOK,IAAI,CAAC,sBAAuBjX,sBAAoB,mBAAmBE,0BAAwB,4BACjI,UAACmD,EAAAA,CAAcA,CAAAA,CAAC1D,UAAU,eAAeK,sBAAoB,iBAAiBE,0BAAwB,oBAAoB,aAG5H,WAACS,EAAAA,EAAgBA,CAAAA,CAACX,sBAAoB,mBAAmBE,0BAAwB,4BAC/E,UAACyD,EAAAA,CAAcA,CAAAA,CAAChE,UAAU,eAAeK,sBAAoB,iBAAiBE,0BAAwB,oBAAoB,aAG5H,WAACS,EAAAA,EAAgBA,CAAAA,CAACX,sBAAoB,mBAAmBE,0BAAwB,4BAC/E,UAACwa,EAAAA,CAAQA,CAAAA,CAAC/a,UAAU,eAAeK,sBAAoB,WAAWE,0BAAwB,oBAAoB,sBAIlH,UAACoB,EAAAA,EAAqBA,CAAAA,CAACtB,sBAAoB,wBAAwBE,0BAAwB,oBAC3F,WAACS,EAAAA,EAAgBA,CAAAA,CAACX,sBAAoB,mBAAmBE,0BAAwB,4BAC/E,UAACya,EAAAA,CAAUA,CAAAA,CAAChb,UAAU,eAAeK,sBAAoB,aAAaE,0BAAwB,oBAC9F,UAACiY,EAAAA,EAAaA,CAAAA,CAACC,YAAY,gBAAgBpY,sBAAoB,gBAAgBE,0BAAwB,mCAOnH,UAACkS,EAAAA,WAAWA,CAAAA,CAACpS,sBAAoB,cAAcE,0BAAwB,sBAE7E,oCIpII,sBAAsB,iQHhBX,SAAS0a,IACtB,MAAO,UAAC5U,EAAAA,CAAMA,CAAAA,CAACnF,QAAQ,QAAQqH,OAAO,IAACL,KAAK,KAAKlI,UAAU,iBAAiBK,sBAAoB,SAASC,wBAAsB,YAAYC,0BAAwB,0BAC/J,UAAC2a,IAAAA,CAAEL,KAAK,4DAA4DM,IAAI,sBAAsBC,OAAO,SAASpb,UAAU,gCACtH,UAACsF,EAAAA,CAAeA,CAAAA,CAACjF,sBAAoB,kBAAkBE,0BAAwB,sBAGvF,CCAe,SAAS8a,IACtB,MAAO,WAACC,SAAAA,CAAOtb,UAAU,qJAAqJM,wBAAsB,SAASC,0BAAwB,uBACjO,WAAC6F,MAAAA,CAAIpG,UAAU,yCACb,UAACuS,EAAAA,cAAcA,CAAAA,CAACvS,UAAU,QAAQK,sBAAoB,iBAAiBE,0BAAwB,eAC/F,UAAC4S,EAAAA,SAASA,CAAAA,CAACoI,YAAY,WAAWvb,UAAU,WAAWK,sBAAoB,YAAYE,0BAAwB,eAC/G,UAACib,EAAAA,WAAWA,CAAAA,CAACnb,sBAAoB,cAAcE,0BAAwB,kBAGzE,WAAC6F,MAAAA,CAAIpG,UAAU,yCACb,UAACib,EAASA,CAAC5a,MAAD4a,gBAAqB,YAAY1a,0BAAwB,eACnE,UAAC6F,MAAAA,CAAIpG,UAAU,0BACb,UAACiG,EAAAA,OAAWA,CAAAA,CAAC5F,sBAAoB,cAAcE,0BAAwB,iBAEzE,UAAC6X,EAAAA,OAAOA,CAAAA,CAAC/X,sBAAoB,UAAUE,0BAAwB,eAC/D,UAACkb,EAAAA,UAAUA,CAAAA,CAACpb,sBAAoB,aAAaE,0BAAwB,eACrE,UAACmb,EAAAA,aAAaA,CAAAA,CAACrb,sBAAoB,gBAAgBE,0BAAwB,oBAGnF,0CCpBaob,EAAqB,CAChChO,KADgC,CACzB,gCACPiO,WAAa,0CACf,EACe,eAAeC,EAAgB,CAC5Cva,UAAQ,CAGT,CAJ6Bua,CAM5B,IAAMC,EAAc,MAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAAA,EAAAA,CACpBzM,EAAcwM,EAAYE,GAAG,CAAC,GAAhBF,EAAAA,aAAkChL,KAAU,UAChE,MAAOmL,CAAAA,EAAAA,EAAAA,GAAAA,CAACC,CAAAA,EAAAA,YAAAA,CAAAA,CAAa7b,qBAAoB,gBAAeC,uBAAsB,mBAAkBC,yBAAwB,cACpH,SAAA0b,CAAAA,EAAAA,EAAAA,GAAAA,CAACjF,CAAAA,EAAAA,OAAAA,CAAAA,CAAK3W,qBAAoB,QAAOE,yBAAwB,cACvD,SAAA4b,CAAAA,EAAAA,EAAAA,IAAAA,CAAC9M,CAAAA,EAAAA,eAAAA,CAAAA,CAAgBC,WAAaA,CAAAA,EAAajP,SAAbiP,YAAiC,mBAAkB/O,yBAAwB,wBACvG0b,CAAAA,EAAAA,EAAAA,GAAAA,CAACtC,CAAAA,EAAAA,OAAAA,CAAAA,CAAWtZ,qBAAoB,cAAaE,yBAAwB,gBACrE4b,CAAAA,EAAAA,EAAAA,IAAAA,CAACtJ,CAAAA,EAAAA,YAAAA,CAAAA,CAAaxS,qBAAoB,gBAAeE,yBAAwB,wBACvE0b,CAAAA,EAAAA,EAAAA,GAAAA,CAACZ,CAAAA,EAAAA,CAAOhb,GAAPgb,kBAA2B,UAAS9a,yBAAwB,gBAE5De,WAMb,CCxBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,KAAK,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAIH,CALS,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IAIkC,EANxB,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,EAAI,OAC7D,EADsE,GACzC,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,QAAQ,mBACvB,gBACA,CADiB,SAEjB,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,uFC3E9B,IAAM8a,EAAiB,CAAC,CACtBzG,KAAM,UACN7E,MAAO,SACT,EAAG,CACD6E,KAAM,OACN7E,MAAO,MACT,EAAG,CACD6E,KAAM,QACN7E,MAAO,OACT,EAAG,CACD6E,KAAM,QACN7E,MAAO,OACT,EAAE,CACIuL,EAAgB,CAAC,CACrB1G,KAAM,UACN7E,MAAO,gBACT,EAAG,CACD6E,KAAM,OACN7E,MAAO,aACT,EAAE,CACIwL,EAAc,CAAC,CACnB3G,KAAM,OACN7E,MAAO,aACT,EAAE,CACK,SAAS4K,IACd,GAAM,aACJa,CAAW,gBACXC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,GAClB,MAAO,WAACrW,MAAAA,CAAIpG,UAAU,0BAA0BM,wBAAsB,gBAAgBC,0BAAwB,+BAC1G,UAAC6G,EAAAA,CAAKA,CAAAA,CAACsV,QAAQ,iBAAiB1c,UAAU,UAAUK,sBAAoB,QAAQE,0BAAwB,8BAAqB,UAG7H,WAACmI,EAAAA,EAAMA,CAAAA,CAACoI,MAAOyL,EAAaI,cAAeH,EAAgBnc,sBAAoB,SAASE,0BAAwB,+BAC9G,WAACuI,EAAAA,EAAaA,CAAAA,CAAC0M,GAAG,iBAAiBxV,UAAU,gDAAgDK,sBAAoB,gBAAgBE,0BAAwB,+BACvJ,UAACiB,OAAAA,CAAKxB,UAAU,iDAAwC,oBAGxD,UAACwB,OAAAA,CAAKxB,UAAU,iDAAwC,UACxD,UAAC6I,EAAAA,EAAWA,CAAAA,CAAC+T,YAAY,iBAAiBvc,sBAAoB,cAAcE,0BAAwB,0BAEtG,WAAC0I,EAAAA,EAAaA,CAAAA,CAACgL,MAAM,MAAM5T,sBAAoB,gBAAgBE,0BAAwB,+BACrF,WAACqI,EAAAA,EAAWA,CAAAA,CAACvI,sBAAoB,cAAcE,0BAAwB,+BACrE,UAAC8I,EAAAA,EAAWA,CAAAA,CAAChJ,sBAAoB,cAAcE,0BAAwB,8BAAqB,YAC3F6b,EAAe1G,GAAG,CAACa,GAAS,UAACjN,EAAAA,EAAUA,CAAAA,CAAkBwH,MAAOyF,EAAMzF,KAAK,UACvEyF,EAAMZ,IAAI,EAD+BY,EAAMZ,IAAI,MAI1D,UAACpM,EAAAA,EAAeA,CAAAA,CAAClJ,sBAAoB,kBAAkBE,0BAAwB,uBAC/E,WAACqI,EAAAA,EAAWA,CAAAA,CAACvI,sBAAoB,cAAcE,0BAAwB,+BACrE,UAAC8I,EAAAA,EAAWA,CAAAA,CAAChJ,sBAAoB,cAAcE,0BAAwB,8BAAqB,WAC3F8b,EAAc3G,GAAG,CAACa,GAAS,UAACjN,EAAAA,EAAUA,CAAAA,CAAkBwH,MAAOyF,EAAMzF,KAAK,UACtEyF,EAAMZ,IAAI,EAD8BY,EAAMZ,IAAI,MAIzD,WAAC/M,EAAAA,EAAWA,CAAAA,CAACvI,sBAAoB,cAAcE,0BAAwB,+BACrE,UAAC8I,EAAAA,EAAWA,CAAAA,CAAChJ,sBAAoB,cAAcE,0BAAwB,8BAAqB,eAC3F+b,EAAY5G,GAAG,CAACa,GAAS,UAACjN,EAAAA,EAAUA,CAAAA,CAAkBwH,MAAOyF,EAAMzF,KAAK,UACpEyF,EAAMZ,IAAI,EAD4BY,EAAMZ,IAAI,cAOjE,gmDCrDA,IAAMkH,EAAcC,CAAAA,EAAAA,EAAAA,aAAAA,CAAaA,MAA8B/M,GASxD,SAASmM,EAAa,UAC3B5a,CAAQ,CACU,EAClB,GAAM,CACJiD,KAAMwY,CAAS,CACfC,SAAUC,CAAW,CACtB,CAAG5E,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAAC9T,EAAM2Y,EAAQ,CAAGjD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAA2B,MACrD,CAACkD,EAASC,EAAW,CAAGnD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACoD,EAAOC,EAAS,CAAGrD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5CsD,EAAgB,UACpB,GAAI,CAACR,EAAW,CACdG,EAAQ,MACRE,GAAW,GACX,MACF,CACA,GAAI,CACFA,GAAW,GACXE,EAAS,MAGT,IAAME,EAAW,MAAMC,MAAM,iBAAkB,CAC7CC,OAAQ,OACRC,QAAS,CACP,eAAgB,kBAClB,CACF,GACA,GAAI,CAACH,EAASI,EAAE,CACd,CADgB,KACV,MAAU,4BAElB,IAAMC,EAAO,MAAML,EAASM,IAAI,GAG1BC,EAAuC,CAC3CC,QAASjB,EAAUvH,EAAE,CACrByI,MAAOlB,EAAU7V,cAAc,CAAC,EAAE,EAAEC,cAAgB,GACpD+W,UAAWnB,EAAUmB,SAAS,OAAInO,EAClCoO,SAAUpB,EAAUoB,QAAQ,OAAIpO,EAChCrG,KAAMmU,EAAKtZ,IAAI,EAAEmF,MAAQ,aAEzB0U,cAAeP,EAAKtZ,IAAI,EAAE6Z,aAC5B,CAD0C,CAE1ClB,EAAQa,EACV,CAAE,MAAOM,EAAK,CACZC,QAAQjB,KAAK,CAAC,4BAA6BgB,GAC3Cf,EAASe,aAAeE,MAAQF,EAAIG,OAAO,CAAG,6BAG1CzB,GACFG,EAAQ,CACNc,KAFW,GAEFjB,EAAUvH,EAAE,CACrByI,MAAOlB,EAAU7V,cAAc,CAAC,EAAE,EAAEC,cAAgB,GACpD+W,UAAWnB,EAAUmB,SAAS,OAAInO,EAClCoO,SAAUpB,EAAUoB,QAAQ,OAAIpO,EAChCrG,KAAM,aAEN0U,mBAAerO,CACjB,EAEJ,QAAU,CACRqN,GAAW,EACb,CACF,EAOMqB,EAAcla,GAAMmF,KAAOD,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAAClF,EAAKmF,IAAI,EAAI,KAUjE,MAAO,UAACmT,EAAYjL,QAAQ,EAACd,MATS,CASFa,KARlCpN,UACA4Y,QACAE,EACAoB,cACAvR,cAAe,GAAuCA,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAAC3I,EAAM4I,GAC1EC,QAAS,GAAkCA,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAC7I,EAAM8I,GACzDqR,YAAanB,CACf,EACkDld,sBAAoB,uBAAuBC,wBAAsB,eAAeC,0BAAwB,4BACrJe,GAEP,CAKO,SAAS6Y,IACd,IAAM/K,EAAUuP,CAAAA,EAAAA,EAAAA,UAAAA,CAAUA,CAAC9B,GAC3B,QAAgB9M,IAAZX,EACF,KADyB,CACnB,MAAU,8CAElB,OAAOA,CACT,CA8BO,SAASwP,EAAe,YAC7BzR,CAAU,CACV7L,UAAQ,CACRud,WAAW,IAAI,CACK,EAEpB,MAAOC,CA/BF,SAASC,CAA+C,EAC7D,GAAM,CACJ7R,eAAa,CACd,CAAGiN,IACJ,OAAOjN,EAAcC,EACvB,EAyB8CA,GACK,+BAAG0R,IAArB,+BAAGvd,GACpC,CAUO,SAAS0d,EAAS,OACvB3R,CAAK,UACL/L,CAAQ,UACRud,EAAW,IAAI,CACD,EAEd,MAAOI,CADiBC,SArCVA,CAAuC,EACrD,GAAM,SACJ9R,CAAO,CACR,CAAG+M,IACJ,OAAO/M,EAAQC,EACjB,EAgCqCA,GACQ,+BAAGwR,IAArB,+BAAGvd,GAC9B,2GCvKA,SAAS6R,EAAU,CACjBnT,WAAS,CACTub,cAAc,YAAY,YAC1B4D,GAAa,CAAI,CACjB,GAAGlf,EACkD,EACrD,MAAO,UAACmf,EAAAA,CAAuB,EAACjf,YAAU,iBAAiBgf,WAAYA,EAAY5D,YAAaA,EAAavb,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iKAAkKJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,0BAA0BC,wBAAsB,YAAYC,0BAAwB,iBAC9Z,mBCZA,uCAA8J,CAE9J,uCAA0J,CAE1J,uCAAkK,CAElK,uCAAmL,CAEnL,uCAA+J,CAE/J,uCAA2J,CAE3J,uCAAmK,CAEnK,sCAA8J,CAE9J,uCAA6H,CAE7H,uCAAyJ,mHCZlJ,SAASkb,IACd,GAAM,UACJhF,CAAQ,CACR4I,eAAa,CACd,CAAG3I,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,GACN4I,EAAoBpQ,EAAAA,WAAiB,CAAC,IAC1C,IAAMqQ,EAA4B,SAAlBF,EAA2B,QAAU,OAC/CG,EAAOzO,SAAS0O,eAAe,CACrC,GAAI,CAAC1O,SAAS2O,mBAAmB,CAAE,YACjCjJ,EAAS8I,GAKPI,GAAG,CACLH,EAAK7P,KAAK,CAACiQ,WAAW,CAAC,MAAO,GAAGD,EAAEE,OAAO,CAAC,EAAE,CAAC,EAC9CL,EAAK7P,KAAK,CAACiQ,WAAW,CAAC,MAAO,GAAGD,EAAEG,OAAO,CAAC,EAAE,CAAC,GAEhD/O,SAAS2O,mBAAmB,CAAC,KAC3BjJ,EAAS8I,EACX,EACF,EAAG,CAACF,EAAe5I,EAAS,EAC5B,MAAO,WAACpQ,EAAAA,CAAMA,CAAAA,CAACnF,QAAQ,YAAYgH,KAAK,OAAOlI,UAAU,sBAAsBsG,QAASgZ,EAAmBjf,sBAAoB,SAASC,wBAAsB,aAAaC,0BAAwB,6BAC/L,UAACwf,EAAAA,CAAcA,CAAAA,CAAC1f,sBAAoB,iBAAiBE,0BAAwB,qBAC7E,UAACiB,OAAAA,CAAKxB,UAAU,mBAAU,mBAEhC,iraC5BA,SAASggB,EAAW,CAClB,GAAG/f,EACyB,EAC5B,MAAO,UAACggB,MAAAA,CAAItN,aAAW,aAAaxS,YAAU,aAAc,GAAGF,CAAK,CAAEK,wBAAsB,aAAaC,0BAAwB,kBACnI,CACA,SAAS2f,EAAe,WACtBlgB,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAACkgB,KAAAA,CAAGhgB,YAAU,kBAAkBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2FAA4FJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,iBAAiBC,0BAAwB,kBACzO,CACA,SAAS6f,EAAe,WACtBpgB,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAAC2T,KAAAA,CAAGzT,YAAU,kBAAkBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mCAAoCJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,iBAAiBC,0BAAwB,kBACjL,CACA,SAAS8f,EAAe,SACtB9X,CAAO,WACPvI,CAAS,CACT,GAAGC,EAGJ,EACC,IAAMuI,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,IAC9B,MAAO,UAACD,EAAAA,CAAKrI,YAAU,kBAAkBH,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CJ,GAAa,GAAGC,CAAK,CAAEI,sBAAoB,OAAOC,wBAAsB,iBAAiBC,0BAAwB,kBACrN,CACA,SAAS+f,EAAe,WACtBtgB,CAAS,CACT,GAAGC,EAC0B,EAC7B,MAAO,UAACuB,OAAAA,CAAKrB,YAAU,kBAAkBuJ,KAAK,OAAO6W,gBAAc,OAAOC,eAAa,OAAOxgB,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8BAA+BJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,iBAAiBC,0BAAwB,kBACnO,CACA,SAASkgB,EAAoB,UAC3Bnf,CAAQ,WACRtB,CAAS,CACT,GAAGC,EACwB,EAC3B,MAAO,UAAC2T,KAAAA,CAAGzT,YAAU,uBAAuBuJ,KAAK,eAAegX,cAAY,OAAO1gB,UAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,mBAAoBJ,GAAa,GAAGC,CAAK,CAAEK,wBAAsB,sBAAsBC,0BAAwB,0BAC3Me,GAAY,UAACqf,EAAAA,CAAYA,CAAAA,CAAAA,IAEhC,gBCnCA,IAAMC,EAAiD,CACrD,aAAc,CAAC,CACbjT,MAAO,YACP1F,KAAM,YACR,EAAE,CACF,sBAAuB,CAAC,CACtB0F,MAAO,YACP1F,KAAM,YACR,EAAG,CACD0F,MAAO,WACP1F,KAAM,qBACR,EAAE,CACF,qBAAsB,CAAC,CACrB0F,MAAO,YACP1F,KAAM,YACR,EAAG,CACD0F,MAAO,UACP1F,KAAM,oBACR,EAEF,iBCxBO,SAASuT,IACd,IAAMzN,EDwBD,SAAS8S,EACd,GCzB4BA,CDyBtBjH,EAAWC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,GAiB5B,MAhBoBzC,CAgBb0J,EAhBa1J,EAAAA,OAAAA,CAAOA,CAAC,KAE1B,GAAIwJ,CAAY,CAAChH,EAAS,CACxB,CAD0B,MACnBgH,CAAY,CAAChH,EAAS,CAI/B,IAAMmH,EAAWnH,EAASoH,KAAK,CAAC,KAAKzG,MAAM,CAAC0G,SAC5C,OAAOF,EAASrL,GAAG,CAAC,CAACwL,EAAS7L,KAC5B,IAAM8L,EAAO,CAAC,CAAC,EAAEJ,EAAS/Z,KAAK,CAAC,EAAGqO,EAAQ,GAAG+L,IAAI,CAAC,MAAM,CACzD,MAAO,CACLzT,MAAOuT,EAAQG,MAAM,CAAC,GAAGpa,WAAW,GAAKia,EAAQla,KAAK,CAAC,GACvDiB,KAAMkZ,CACR,CACF,EACF,EAAG,CAACvH,EAAS,CAEf,WC1CE,GAAwB,CAApB7L,EAAM0H,MAAM,CAAe,KACxB,UAACuK,EAAUA,CAAC3f,OAAD2f,eAAqB,aAAa1f,wBAAsB,cAAcC,0BAAwB,2BAC5G,UAAC2f,EAAcA,CAAC7f,WAAD6f,WAAqB,iBAAiB3f,0BAAwB,2BAC1EwN,EAAM2H,GAAG,CAAC,CAACW,EAAMhB,IAAU,WAACiM,EAAAA,QAAQA,CAAAA,WAChCjM,IAAUtH,EAAM0H,MAAM,CAAG,GAAK,UAAC2K,EAAcA,CAACpgB,UAAU,CAAXogB,0BAC1C,UAACC,EAAcA,CAACxF,KAAMxE,EAAKpO,IAAI,UAAGoO,EAAK1I,KAAK,KAE/C0H,EAAQtH,EAAM0H,MAAM,CAAG,GAAK,UAACgL,EAAmBA,CAACzgB,UAAU,MAAXygB,qBAC7C,UAACc,EAAAA,CAASA,CAAAA,CAAAA,KAEblM,IAAUtH,EAAM0H,MAAM,CAAG,GAAK,UAAC6K,EAAcA,UAAEjK,EAAFiK,KAAY,KAPnBjK,EAAK1I,KAAK,MAW7D", "sources": ["webpack://next-shadcn-dashboard-starter/./src/components/ui/avatar.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/dropdown-menu.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/icons.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/search-input.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/user-avatar-profile.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/label.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/input.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/button.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/select.tsx", "webpack://next-shadcn-dashboard-starter/./src/lib/role-utils.ts", "webpack://next-shadcn-dashboard-starter/./src/constants/data.ts", "webpack://next-shadcn-dashboard-starter/./src/hooks/use-mobile.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/sheet.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/skeleton.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/tooltip.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/sidebar.tsx", "webpack://next-shadcn-dashboard-starter/?e8c6", "webpack://next-shadcn-dashboard-starter/./src/components/kbar/result-item.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/kbar/render-result.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/kbar/use-theme-switching.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/kbar/index.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/user-nav.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/collapsible.tsx", "webpack://next-shadcn-dashboard-starter/./src/hooks/use-media-query.ts", "webpack://next-shadcn-dashboard-starter/./src/components/org-switcher.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/app-sidebar.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/cta-github.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/header.tsx", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/layout.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/./src/components/theme-selector.tsx", "webpack://next-shadcn-dashboard-starter/./src/lib/role-context.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/separator.tsx", "webpack://next-shadcn-dashboard-starter/?df24", "webpack://next-shadcn-dashboard-starter/./src/components/layout/ThemeToggle/theme-toggle.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/breadcrumb.tsx", "webpack://next-shadcn-dashboard-starter/./src/hooks/use-breadcrumbs.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/breadcrumbs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\nimport { cn } from '@/lib/utils';\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return <AvatarPrimitive.Root data-slot='avatar' className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)} {...props} data-sentry-element=\"AvatarPrimitive.Root\" data-sentry-component=\"Avatar\" data-sentry-source-file=\"avatar.tsx\" />;\n}\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return <AvatarPrimitive.Image data-slot='avatar-image' className={cn('aspect-square size-full', className)} {...props} data-sentry-element=\"AvatarPrimitive.Image\" data-sentry-component=\"AvatarImage\" data-sentry-source-file=\"avatar.tsx\" />;\n}\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return <AvatarPrimitive.Fallback data-slot='avatar-fallback' className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)} {...props} data-sentry-element=\"AvatarPrimitive.Fallback\" data-sentry-component=\"AvatarFallback\" data-sentry-source-file=\"avatar.tsx\" />;\n}\nexport { Avatar, AvatarImage, AvatarFallback };", "'use client';\n\nimport * as React from 'react';\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu';\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot='dropdown-menu' {...props} data-sentry-element=\"DropdownMenuPrimitive.Root\" data-sentry-component=\"DropdownMenu\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return <DropdownMenuPrimitive.Portal data-slot='dropdown-menu-portal' {...props} data-sentry-element=\"DropdownMenuPrimitive.Portal\" data-sentry-component=\"DropdownMenuPortal\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return <DropdownMenuPrimitive.Trigger data-slot='dropdown-menu-trigger' {...props} data-sentry-element=\"DropdownMenuPrimitive.Trigger\" data-sentry-component=\"DropdownMenuTrigger\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return <DropdownMenuPrimitive.Portal data-sentry-element=\"DropdownMenuPrimitive.Portal\" data-sentry-component=\"DropdownMenuContent\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      <DropdownMenuPrimitive.Content data-slot='dropdown-menu-content' sideOffset={sideOffset} className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Content\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n    </DropdownMenuPrimitive.Portal>;\n}\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return <DropdownMenuPrimitive.Group data-slot='dropdown-menu-group' {...props} data-sentry-element=\"DropdownMenuPrimitive.Group\" data-sentry-component=\"DropdownMenuGroup\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = 'default',\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean;\n  variant?: 'default' | 'destructive';\n}) {\n  return <DropdownMenuPrimitive.Item data-slot='dropdown-menu-item' data-inset={inset} data-variant={variant} className={cn(\"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Item\" data-sentry-component=\"DropdownMenuItem\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return <DropdownMenuPrimitive.CheckboxItem data-slot='dropdown-menu-checkbox-item' className={cn(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} checked={checked} {...props} data-sentry-element=\"DropdownMenuPrimitive.CheckboxItem\" data-sentry-component=\"DropdownMenuCheckboxItem\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator data-sentry-element=\"DropdownMenuPrimitive.ItemIndicator\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n          <CheckIcon className='size-4' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>;\n}\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return <DropdownMenuPrimitive.RadioGroup data-slot='dropdown-menu-radio-group' {...props} data-sentry-element=\"DropdownMenuPrimitive.RadioGroup\" data-sentry-component=\"DropdownMenuRadioGroup\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return <DropdownMenuPrimitive.RadioItem data-slot='dropdown-menu-radio-item' className={cn(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.RadioItem\" data-sentry-component=\"DropdownMenuRadioItem\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      <span className='pointer-events-none absolute left-2 flex size-3.5 items-center justify-center'>\r\n        <DropdownMenuPrimitive.ItemIndicator data-sentry-element=\"DropdownMenuPrimitive.ItemIndicator\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n          <CircleIcon className='size-2 fill-current' data-sentry-element=\"CircleIcon\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>;\n}\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean;\n}) {\n  return <DropdownMenuPrimitive.Label data-slot='dropdown-menu-label' data-inset={inset} className={cn('px-2 py-1.5 text-sm font-medium data-[inset]:pl-8', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Label\" data-sentry-component=\"DropdownMenuLabel\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return <DropdownMenuPrimitive.Separator data-slot='dropdown-menu-separator' className={cn('bg-border -mx-1 my-1 h-px', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.Separator\" data-sentry-component=\"DropdownMenuSeparator\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) {\n  return <span data-slot='dropdown-menu-shortcut' className={cn('text-muted-foreground ml-auto text-xs tracking-widest', className)} {...props} data-sentry-component=\"DropdownMenuShortcut\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot='dropdown-menu-sub' {...props} data-sentry-element=\"DropdownMenuPrimitive.Sub\" data-sentry-component=\"DropdownMenuSub\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean;\n}) {\n  return <DropdownMenuPrimitive.SubTrigger data-slot='dropdown-menu-sub-trigger' data-inset={inset} className={cn('focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.SubTrigger\" data-sentry-component=\"DropdownMenuSubTrigger\" data-sentry-source-file=\"dropdown-menu.tsx\">\r\n      {children}\r\n      <ChevronRightIcon className='ml-auto size-4' data-sentry-element=\"ChevronRightIcon\" data-sentry-source-file=\"dropdown-menu.tsx\" />\r\n    </DropdownMenuPrimitive.SubTrigger>;\n}\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return <DropdownMenuPrimitive.SubContent data-slot='dropdown-menu-sub-content' className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg', className)} {...props} data-sentry-element=\"DropdownMenuPrimitive.SubContent\" data-sentry-component=\"DropdownMenuSubContent\" data-sentry-source-file=\"dropdown-menu.tsx\" />;\n}\nexport { DropdownMenu, DropdownMenuPortal, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuGroup, DropdownMenuLabel, DropdownMenuItem, DropdownMenuCheckboxItem, DropdownMenuRadioGroup, DropdownMenuRadioItem, DropdownMenuSeparator, DropdownMenuShortcut, DropdownMenuSub, DropdownMenuSubTrigger, DropdownMenuSubContent };", "import { IconAlertTriangle, IconArrowRight, IconArrowLeft, IconCalendar, IconCheck, IconChevronLeft, IconChevronRight, IconChevronDown, IconCommand, IconCreditCard, IconFile, IconFileText, IconHelpCircle, IconPhoto, IconDeviceLaptop, IconLayoutDashboard, IconLoader2, IconLogin, IconProps, IconShoppingBag, IconMoon, IconDotsVertical, IconPizza, IconPlus, IconSettings, IconStethoscope, IconSun, IconTrash, IconBrandTwitter, IconUser, IconUserCircle, IconUserEdit, IconUsers, IconUserX, IconX, IconLayoutKanban, IconBrandGithub,\n// CRM specific icons\nIconPhone, IconMail, IconMessageCircle, IconClock, IconSearch, IconFilter, IconEye, IconEdit, IconActivity, IconTrendingUp, IconDownload, IconRefresh } from '@tabler/icons-react';\nexport type Icon = React.ComponentType<IconProps>;\nexport const Icons = {\n  dashboard: IconLayoutDashboard,\n  logo: IconCommand,\n  login: IconLogin,\n  close: IconX,\n  product: IconShoppingBag,\n  spinner: IconLoader2,\n  kanban: IconLayoutKanban,\n  chevronLeft: IconChevronLeft,\n  chevronRight: IconChevronRight,\n  trash: IconTrash,\n  employee: IconUserX,\n  post: IconFileText,\n  page: IconFile,\n  userPen: IconUserEdit,\n  user2: IconUserCircle,\n  media: IconPhoto,\n  settings: IconSettings,\n  billing: IconCreditCard,\n  ellipsis: IconDotsVertical,\n  add: IconPlus,\n  warning: IconAlertTriangle,\n  user: IconUser,\n  arrowRight: IconArrowRight,\n  help: IconHelpCircle,\n  pizza: IconPizza,\n  sun: IconSun,\n  moon: IconMoon,\n  laptop: IconDeviceLaptop,\n  github: IconBrandGithub,\n  twitter: IconBrandTwitter,\n  check: IconCheck,\n  // Medical clinic specific icons\n  calendar: IconCalendar,\n  users: IconUsers,\n  medical: IconStethoscope\n};\n\n// Export individual icons for CRM components\nexport { IconAlertTriangle, IconArrowRight, IconArrowLeft, IconCalendar, IconCheck, IconChevronDown, IconCreditCard, IconFileText, IconPlus, IconStethoscope, IconUser, IconUsers, IconX,\n// CRM specific exports\nIconPhone, IconMail, IconMessageCircle, IconClock, IconSearch, IconFilter, IconEye, IconEdit, IconActivity, IconTrendingUp, IconDownload, IconRefresh };", "'use client';\n\nimport { useK<PERSON><PERSON> } from 'kbar';\nimport { IconSearch } from '@tabler/icons-react';\nimport { Button } from './ui/button';\nexport default function SearchInput() {\n  const {\n    query\n  } = useKBar();\n  return <div className='w-full space-y-2' data-sentry-component=\"SearchInput\" data-sentry-source-file=\"search-input.tsx\">\r\n      <Button variant='outline' className='bg-background text-muted-foreground relative h-9 w-full justify-start rounded-[0.5rem] text-sm font-normal shadow-none sm:pr-12 md:w-40 lg:w-64' onClick={query.toggle} data-sentry-element=\"Button\" data-sentry-source-file=\"search-input.tsx\">\r\n        <IconSearch className='mr-2 h-4 w-4' data-sentry-element=\"IconSearch\" data-sentry-source-file=\"search-input.tsx\" />\r\n        Search...\r\n        <kbd className='bg-muted pointer-events-none absolute top-[0.3rem] right-[0.3rem] hidden h-6 items-center gap-1 rounded border px-1.5 font-mono text-[10px] font-medium opacity-100 select-none sm:flex'>\r\n          <span className='text-xs'>⌘</span>K\r\n        </kbd>\r\n      </Button>\r\n    </div>;\n}", "import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\ninterface UserAvatarProfileProps {\n  className?: string;\n  showInfo?: boolean;\n  user: {\n    imageUrl?: string;\n    fullName?: string | null;\n    emailAddresses: Array<{\n      emailAddress: string;\n    }>;\n  } | null;\n}\nexport function UserAvatarProfile({\n  className,\n  showInfo = false,\n  user\n}: UserAvatarProfileProps) {\n  return <div className='flex items-center gap-2' data-sentry-component=\"UserAvatarProfile\" data-sentry-source-file=\"user-avatar-profile.tsx\">\r\n      <Avatar className={className} data-sentry-element=\"Avatar\" data-sentry-source-file=\"user-avatar-profile.tsx\">\r\n        <AvatarImage src={user?.imageUrl || ''} alt={user?.fullName || ''} data-sentry-element=\"AvatarImage\" data-sentry-source-file=\"user-avatar-profile.tsx\" />\r\n        <AvatarFallback className='rounded-lg' data-sentry-element=\"AvatarFallback\" data-sentry-source-file=\"user-avatar-profile.tsx\">\r\n          {user?.fullName?.slice(0, 2)?.toUpperCase() || 'CN'}\r\n        </AvatarFallback>\r\n      </Avatar>\r\n\r\n      {showInfo && <div className='grid flex-1 text-left text-sm leading-tight'>\r\n          <span className='truncate font-semibold'>{user?.fullName || ''}</span>\r\n          <span className='truncate text-xs'>\r\n            {user?.emailAddresses[0].emailAddress || ''}\r\n          </span>\r\n        </div>}\r\n    </div>;\n}", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cn } from '@/lib/utils';\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return <LabelPrimitive.Root data-slot='label' className={cn('flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50', className)} {...props} data-sentry-element=\"LabelPrimitive.Root\" data-sentry-component=\"Label\" data-sentry-source-file=\"label.tsx\" />;\n}\nexport { Label };", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Input({\n  className,\n  type,\n  ...props\n}: React.ComponentProps<'input'>) {\n  return <input type={type} data-slot='input' className={cn('file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', 'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]', 'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive', className)} {...props} data-sentry-component=\"Input\" data-sentry-source-file=\"input.tsx\" />;\n}\nexport { Input };", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst buttonVariants = cva(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n  variants: {\n    variant: {\n      default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n      destructive: 'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\n      secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n      link: 'text-primary underline-offset-4 hover:underline'\n    },\n    size: {\n      default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n      sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\n      lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n      icon: 'size-9'\n    }\n  },\n  defaultVariants: {\n    variant: 'default',\n    size: 'default'\n  }\n});\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> & VariantProps<typeof buttonVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='button' className={cn(buttonVariants({\n    variant,\n    size,\n    className\n  }))} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Button\" data-sentry-source-file=\"button.tsx\" />;\n}\nexport { Button, buttonVariants };", "'use client';\n\nimport * as React from 'react';\nimport * as SelectPrimitive from '@radix-ui/react-select';\nimport { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot='select' {...props} data-sentry-element=\"SelectPrimitive.Root\" data-sentry-component=\"Select\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot='select-group' {...props} data-sentry-element=\"SelectPrimitive.Group\" data-sentry-component=\"SelectGroup\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot='select-value' {...props} data-sentry-element=\"SelectPrimitive.Value\" data-sentry-component=\"SelectValue\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectTrigger({\n  className,\n  size = 'default',\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: 'sm' | 'default';\n}) {\n  return <SelectPrimitive.Trigger data-slot='select-trigger' data-size={size} className={cn(\"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className)} {...props} data-sentry-element=\"SelectPrimitive.Trigger\" data-sentry-component=\"SelectTrigger\" data-sentry-source-file=\"select.tsx\">\r\n      {children}\r\n      <SelectPrimitive.Icon asChild data-sentry-element=\"SelectPrimitive.Icon\" data-sentry-source-file=\"select.tsx\">\r\n        <ChevronDownIcon className='size-4 opacity-50' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>;\n}\nfunction SelectContent({\n  className,\n  children,\n  position = 'popper',\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return <SelectPrimitive.Portal data-sentry-element=\"SelectPrimitive.Portal\" data-sentry-component=\"SelectContent\" data-sentry-source-file=\"select.tsx\">\r\n      <SelectPrimitive.Content data-slot='select-content' className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md', position === 'popper' && 'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1', className)} position={position} {...props} data-sentry-element=\"SelectPrimitive.Content\" data-sentry-source-file=\"select.tsx\">\r\n        <SelectScrollUpButton data-sentry-element=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\" />\r\n        <SelectPrimitive.Viewport className={cn('p-1', position === 'popper' && 'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1')} data-sentry-element=\"SelectPrimitive.Viewport\" data-sentry-source-file=\"select.tsx\">\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton data-sentry-element=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\" />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>;\n}\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return <SelectPrimitive.Label data-slot='select-label' className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)} {...props} data-sentry-element=\"SelectPrimitive.Label\" data-sentry-component=\"SelectLabel\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return <SelectPrimitive.Item data-slot='select-item' className={cn(\"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\", className)} {...props} data-sentry-element=\"SelectPrimitive.Item\" data-sentry-component=\"SelectItem\" data-sentry-source-file=\"select.tsx\">\r\n      <span className='absolute right-2 flex size-3.5 items-center justify-center'>\r\n        <SelectPrimitive.ItemIndicator data-sentry-element=\"SelectPrimitive.ItemIndicator\" data-sentry-source-file=\"select.tsx\">\r\n          <CheckIcon className='size-4' data-sentry-element=\"CheckIcon\" data-sentry-source-file=\"select.tsx\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText data-sentry-element=\"SelectPrimitive.ItemText\" data-sentry-source-file=\"select.tsx\">{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>;\n}\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return <SelectPrimitive.Separator data-slot='select-separator' className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)} {...props} data-sentry-element=\"SelectPrimitive.Separator\" data-sentry-component=\"SelectSeparator\" data-sentry-source-file=\"select.tsx\" />;\n}\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return <SelectPrimitive.ScrollUpButton data-slot='select-scroll-up-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollUpButton\" data-sentry-component=\"SelectScrollUpButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronUpIcon className='size-4' data-sentry-element=\"ChevronUpIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollUpButton>;\n}\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return <SelectPrimitive.ScrollDownButton data-slot='select-scroll-down-button' className={cn('flex cursor-default items-center justify-center py-1', className)} {...props} data-sentry-element=\"SelectPrimitive.ScrollDownButton\" data-sentry-component=\"SelectScrollDownButton\" data-sentry-source-file=\"select.tsx\">\r\n      <ChevronDownIcon className='size-4' data-sentry-element=\"ChevronDownIcon\" data-sentry-source-file=\"select.tsx\" />\r\n    </SelectPrimitive.ScrollDownButton>;\n}\nexport { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectScrollDownButton, SelectScrollUpButton, SelectSeparator, SelectTrigger, SelectValue };", "// Role-based access control utilities for frontend components\nimport { AuthenticatedUser } from './auth-middleware';\n\nexport type UserRole = 'admin' | 'front-desk' | 'doctor';\n\nexport interface RolePermissions {\n  canCreatePatients: boolean;\n  canEditPatients: boolean;\n  canDeletePatients: boolean;\n  canViewMedicalNotes: boolean;\n  canEditMedicalNotes: boolean;\n  canCreateAppointments: boolean;\n  canEditAllAppointments: boolean;\n  canEditOwnAppointments: boolean;\n  canDeleteAppointments: boolean;\n  canCreateTreatments: boolean;\n  canEditTreatments: boolean;\n  canDeleteTreatments: boolean;\n  canManageUsers: boolean;\n  canViewAnalytics: boolean;\n  canViewFinancialData: boolean;\n  // Billing and Payment Permissions\n  canCreateBills: boolean;\n  canEditBills: boolean;\n  canDeleteBills: boolean;\n  canViewAllBills: boolean;\n  canViewOwnBills: boolean;\n  canProcessPayments: boolean;\n  canViewPayments: boolean;\n  canGenerateReceipts: boolean;\n  canHandleRefunds: boolean;\n  canGenerateReports: boolean;\n  canViewDetailedFinancials: boolean;\n  canManagePaymentMethods: boolean;\n  // Advanced Billing Permissions\n  canApplyDiscounts: boolean;\n  canApproveRefunds: boolean;\n  canViewSensitiveFinancials: boolean;\n  canExportFinancialData: boolean;\n  canBulkUpdateBills: boolean;\n  canOverridePaymentLimits: boolean;\n  canAccessAuditLogs: boolean;\n  canManageDeposits: boolean;\n  canProcessDepositRefunds: boolean;\n  canViewPatientFinancialHistory: boolean;\n  canModifyBillDueDates: boolean;\n  canWaiveFees: boolean;\n  canAccessAdvancedReports: boolean;\n  canManageBillingSettings: boolean;\n  // Deposit Management Permissions\n  canCreateDeposits: boolean;\n  canEditDeposits: boolean;\n  canDeleteDeposits: boolean;\n  canApplyDeposits: boolean;\n  canRefundDeposits: boolean;\n  canViewDepositHistory: boolean;\n  // Financial Reporting Permissions\n  canViewDailyReports: boolean;\n  canViewMonthlyReports: boolean;\n  canViewYearlyReports: boolean;\n  canViewOutstandingReports: boolean;\n  canViewPaymentMethodReports: boolean;\n  canViewTreatmentRevenueReports: boolean;\n  canExportReports: boolean;\n  canScheduleReports: boolean;\n}\n\n/**\n * Get permissions for a specific user role\n */\nexport function getRolePermissions(role: UserRole): RolePermissions {\n  switch (role) {\n    case 'admin':\n      return {\n        canCreatePatients: true,\n        canEditPatients: true,\n        canDeletePatients: true,\n        canViewMedicalNotes: true,\n        canEditMedicalNotes: true,\n        canCreateAppointments: true,\n        canEditAllAppointments: true,\n        canEditOwnAppointments: true,\n        canDeleteAppointments: true,\n        canCreateTreatments: true,\n        canEditTreatments: true,\n        canDeleteTreatments: true,\n        canManageUsers: true,\n        canViewAnalytics: true,\n        canViewFinancialData: true,\n        // Billing and Payment Permissions - Admin has full access\n        canCreateBills: true,\n        canEditBills: true,\n        canDeleteBills: true,\n        canViewAllBills: true,\n        canViewOwnBills: true,\n        canProcessPayments: true,\n        canViewPayments: true,\n        canGenerateReceipts: true,\n        canHandleRefunds: true,\n        canGenerateReports: true,\n        canViewDetailedFinancials: true,\n        canManagePaymentMethods: true,\n        // Advanced Billing Permissions - Admin has full access\n        canApplyDiscounts: true,\n        canApproveRefunds: true,\n        canViewSensitiveFinancials: true,\n        canExportFinancialData: true,\n        canBulkUpdateBills: true,\n        canOverridePaymentLimits: true,\n        canAccessAuditLogs: true,\n        canManageDeposits: true,\n        canProcessDepositRefunds: true,\n        canViewPatientFinancialHistory: true,\n        canModifyBillDueDates: true,\n        canWaiveFees: true,\n        canAccessAdvancedReports: true,\n        canManageBillingSettings: true,\n        // Deposit Management Permissions - Admin has full access\n        canCreateDeposits: true,\n        canEditDeposits: true,\n        canDeleteDeposits: true,\n        canApplyDeposits: true,\n        canRefundDeposits: true,\n        canViewDepositHistory: true,\n        // Financial Reporting Permissions - Admin has full access\n        canViewDailyReports: true,\n        canViewMonthlyReports: true,\n        canViewYearlyReports: true,\n        canViewOutstandingReports: true,\n        canViewPaymentMethodReports: true,\n        canViewTreatmentRevenueReports: true,\n        canExportReports: true,\n        canScheduleReports: true,\n      };\n\n    case 'doctor':\n      return {\n        canCreatePatients: false,\n        canEditPatients: false, // Can only edit medical notes\n        canDeletePatients: false,\n        canViewMedicalNotes: true,\n        canEditMedicalNotes: true,\n        canCreateAppointments: false,\n        canEditAllAppointments: false,\n        canEditOwnAppointments: true,\n        canDeleteAppointments: false,\n        canCreateTreatments: false,\n        canEditTreatments: false,\n        canDeleteTreatments: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canViewFinancialData: false,\n        // Billing and Payment Permissions - Doctor has limited access\n        canCreateBills: false,\n        canEditBills: false,\n        canDeleteBills: false,\n        canViewAllBills: false,\n        canViewOwnBills: true, // Can view bills related to their appointments\n        canProcessPayments: false,\n        canViewPayments: true, // Can view payment status for their patients\n        canGenerateReceipts: false,\n        canHandleRefunds: false,\n        canGenerateReports: false,\n        canViewDetailedFinancials: false,\n        canManagePaymentMethods: false,\n        // Advanced Billing Permissions - Doctor has very limited access\n        canApplyDiscounts: false,\n        canApproveRefunds: false,\n        canViewSensitiveFinancials: false,\n        canExportFinancialData: false,\n        canBulkUpdateBills: false,\n        canOverridePaymentLimits: false,\n        canAccessAuditLogs: false,\n        canManageDeposits: false,\n        canProcessDepositRefunds: false,\n        canViewPatientFinancialHistory: true, // Can view their patients' financial history\n        canModifyBillDueDates: false,\n        canWaiveFees: false,\n        canAccessAdvancedReports: false,\n        canManageBillingSettings: false,\n        // Deposit Management Permissions - Doctor has no access\n        canCreateDeposits: false,\n        canEditDeposits: false,\n        canDeleteDeposits: false,\n        canApplyDeposits: false,\n        canRefundDeposits: false,\n        canViewDepositHistory: false,\n        // Financial Reporting Permissions - Doctor has no access\n        canViewDailyReports: false,\n        canViewMonthlyReports: false,\n        canViewYearlyReports: false,\n        canViewOutstandingReports: false,\n        canViewPaymentMethodReports: false,\n        canViewTreatmentRevenueReports: false,\n        canExportReports: false,\n        canScheduleReports: false,\n      };\n\n    case 'front-desk':\n      return {\n        canCreatePatients: true,\n        canEditPatients: true,\n        canDeletePatients: true,\n        canViewMedicalNotes: false,\n        canEditMedicalNotes: false,\n        canCreateAppointments: true,\n        canEditAllAppointments: true,\n        canEditOwnAppointments: true,\n        canDeleteAppointments: true,\n        canCreateTreatments: false,\n        canEditTreatments: false,\n        canDeleteTreatments: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canViewFinancialData: false,\n        // Billing and Payment Permissions - Front-desk handles payments but limited financial access\n        canCreateBills: true,\n        canEditBills: true,\n        canDeleteBills: false, // Cannot delete bills\n        canViewAllBills: true,\n        canViewOwnBills: true,\n        canProcessPayments: true,\n        canViewPayments: true,\n        canGenerateReceipts: true,\n        canHandleRefunds: false, // Cannot handle refunds without approval\n        canGenerateReports: false, // Limited reporting access\n        canViewDetailedFinancials: false,\n        canManagePaymentMethods: true,\n        // Advanced Billing Permissions - Front-desk has moderate access\n        canApplyDiscounts: true, // Can apply standard discounts\n        canApproveRefunds: false, // Cannot approve refunds\n        canViewSensitiveFinancials: false,\n        canExportFinancialData: false,\n        canBulkUpdateBills: true, // Can perform bulk operations\n        canOverridePaymentLimits: false,\n        canAccessAuditLogs: false,\n        canManageDeposits: true,\n        canProcessDepositRefunds: false, // Cannot process deposit refunds\n        canViewPatientFinancialHistory: true,\n        canModifyBillDueDates: true, // Can modify due dates\n        canWaiveFees: false, // Cannot waive fees\n        canAccessAdvancedReports: false,\n        canManageBillingSettings: false,\n        // Deposit Management Permissions - Front-desk has good access\n        canCreateDeposits: true,\n        canEditDeposits: true,\n        canDeleteDeposits: false, // Cannot delete deposits\n        canApplyDeposits: true,\n        canRefundDeposits: false, // Cannot refund deposits\n        canViewDepositHistory: true,\n        // Financial Reporting Permissions - Front-desk has basic reporting access\n        canViewDailyReports: true,\n        canViewMonthlyReports: true,\n        canViewYearlyReports: false,\n        canViewOutstandingReports: true,\n        canViewPaymentMethodReports: true,\n        canViewTreatmentRevenueReports: false,\n        canExportReports: false,\n        canScheduleReports: false,\n      };\n\n    default:\n      // Default to no permissions for unknown roles\n      return {\n        canCreatePatients: false,\n        canEditPatients: false,\n        canDeletePatients: false,\n        canViewMedicalNotes: false,\n        canEditMedicalNotes: false,\n        canCreateAppointments: false,\n        canEditAllAppointments: false,\n        canEditOwnAppointments: false,\n        canDeleteAppointments: false,\n        canCreateTreatments: false,\n        canEditTreatments: false,\n        canDeleteTreatments: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canViewFinancialData: false,\n        // Billing and Payment Permissions - No access for unknown roles\n        canCreateBills: false,\n        canEditBills: false,\n        canDeleteBills: false,\n        canViewAllBills: false,\n        canViewOwnBills: false,\n        canProcessPayments: false,\n        canViewPayments: false,\n        canGenerateReceipts: false,\n        canHandleRefunds: false,\n        canGenerateReports: false,\n        canViewDetailedFinancials: false,\n        canManagePaymentMethods: false,\n        // Advanced Billing Permissions - No access for unknown roles\n        canApplyDiscounts: false,\n        canApproveRefunds: false,\n        canViewSensitiveFinancials: false,\n        canExportFinancialData: false,\n        canBulkUpdateBills: false,\n        canOverridePaymentLimits: false,\n        canAccessAuditLogs: false,\n        canManageDeposits: false,\n        canProcessDepositRefunds: false,\n        canViewPatientFinancialHistory: false,\n        canModifyBillDueDates: false,\n        canWaiveFees: false,\n        canAccessAdvancedReports: false,\n        canManageBillingSettings: false,\n        // Deposit Management Permissions - No access for unknown roles\n        canCreateDeposits: false,\n        canEditDeposits: false,\n        canDeleteDeposits: false,\n        canApplyDeposits: false,\n        canRefundDeposits: false,\n        canViewDepositHistory: false,\n        // Financial Reporting Permissions - No access for unknown roles\n        canViewDailyReports: false,\n        canViewMonthlyReports: false,\n        canViewYearlyReports: false,\n        canViewOutstandingReports: false,\n        canViewPaymentMethodReports: false,\n        canViewTreatmentRevenueReports: false,\n        canExportReports: false,\n        canScheduleReports: false,\n      };\n  }\n}\n\n/**\n * Check if user has a specific permission\n */\nexport function hasPermission(\n  user: AuthenticatedUser | null,\n  permission: keyof RolePermissions\n): boolean {\n  if (!user || !user.role) return false;\n  \n  const permissions = getRolePermissions(user.role);\n  return permissions[permission];\n}\n\n/**\n * Check if user has any of the specified roles\n */\nexport function hasRole(\n  user: AuthenticatedUser | null,\n  roles: UserRole | UserRole[]\n): boolean {\n  if (!user || !user.role) return false;\n  \n  const allowedRoles = Array.isArray(roles) ? roles : [roles];\n  return allowedRoles.includes(user.role);\n}\n\n/**\n * Check if user can access a specific appointment (for doctors)\n */\nexport function canAccessAppointment(\n  user: AuthenticatedUser | null,\n  appointmentPractitionerId: string\n): boolean {\n  if (!user || !user.role) return false;\n  \n  // Admin and front-desk can access all appointments\n  if (user.role === 'admin' || user.role === 'front-desk') {\n    return true;\n  }\n  \n  // Doctors can only access their own appointments\n  if (user.role === 'doctor') {\n    return user.payloadUserId === appointmentPractitionerId;\n  }\n  \n  return false;\n}\n\n/**\n * Get user display name\n */\nexport function getUserDisplayName(user: AuthenticatedUser | null): string {\n  if (!user) return '未知用户';\n\n  if (user.firstName && user.lastName) {\n    return `${user.firstName} ${user.lastName}`;\n  }\n\n  if (user.firstName) {\n    return user.firstName;\n  }\n\n  return user.email;\n}\n\n/**\n * Get role display name\n */\nexport function getRoleDisplayName(role: UserRole): string {\n  switch (role) {\n    case 'admin':\n      return '管理员';\n    case 'front-desk':\n      return '前台';\n    case 'doctor':\n      return '医生';\n    default:\n      return '未知角色';\n  }\n}\n\n/**\n * Get role badge color for UI display\n */\nexport function getRoleBadgeColor(role: UserRole): string {\n  switch (role) {\n    case 'admin':\n      return 'bg-red-100 text-red-800';\n    case 'doctor':\n      return 'bg-blue-100 text-blue-800';\n    case 'front-desk':\n      return 'bg-green-100 text-green-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n}\n\n/**\n * Navigation items that should be hidden based on role\n */\nexport function getHiddenNavItems(role: UserRole): string[] {\n  switch (role) {\n    case 'admin':\n      return []; // Admin can see everything\n    \n    case 'doctor':\n      return ['treatments', 'analytics', 'users']; // Hide treatment management, analytics, user management\n    \n    case 'front-desk':\n      return ['analytics', 'users']; // Hide analytics and user management\n    \n    default:\n      return ['appointments', 'patients', 'treatments', 'analytics', 'users']; // Hide everything for unknown roles\n  }\n}\n\n/**\n * Check if a navigation item should be visible for the user\n */\nexport function isNavItemVisible(\n  user: AuthenticatedUser | null,\n  navItem: string\n): boolean {\n  if (!user || !user.role) return false;\n  \n  const hiddenItems = getHiddenNavItems(user.role);\n  return !hiddenItems.includes(navItem);\n}\n", "import { NavItem } from '@/types';\r\n\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n//Info: The following data is used for the sidebar navigation and Cmd K bar.\r\nexport const navItems: NavItem[] = [\r\n  {\r\n    title: '仪表板',\r\n    url: '/dashboard',\r\n    icon: 'dashboard',\r\n    isActive: false,\r\n    shortcut: ['d', 'd'],\r\n    items: [], // Empty array as there are no child items for Dashboard\r\n    roles: ['admin', 'front-desk', 'doctor'] // All roles can access dashboard\r\n  },\r\n  {\r\n    title: '预约管理',\r\n    url: '/dashboard/appointments',\r\n    icon: 'calendar',\r\n    shortcut: ['a', 'a'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin', 'front-desk', 'doctor'] // All roles can access appointments\r\n  },\r\n  {\r\n    title: '患者管理',\r\n    url: '/dashboard/patients',\r\n    icon: 'users',\r\n    shortcut: ['p', 'p'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin', 'front-desk', 'doctor'] // All roles can access patients\r\n  },\r\n  {\r\n    title: '治疗项目',\r\n    url: '/dashboard/treatments',\r\n    icon: 'medical',\r\n    shortcut: ['t', 't'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin'] // Only admin can manage treatments\r\n  },\r\n  {\r\n    title: '账单管理',\r\n    url: '/dashboard/billing',\r\n    icon: 'billing',\r\n    shortcut: ['b', 'b'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin', 'front-desk'] // Admin and front-desk can manage billing\r\n  },\r\n  {\r\n    title: '系统管理',\r\n    url: '/dashboard/admin',\r\n    icon: 'userPen',\r\n    shortcut: ['u', 'u'],\r\n    isActive: false,\r\n    items: [], // No child items\r\n    roles: ['admin'] // Only admin can access user management\r\n  },\r\n  {\r\n    title: '账户',\r\n    url: '#', // Placeholder as there is no direct link for the parent\r\n    icon: 'billing',\r\n    isActive: false,\r\n    items: [\r\n      {\r\n        title: '个人资料',\r\n        url: '/dashboard/profile',\r\n        icon: 'userPen',\r\n        shortcut: ['m', 'm']\r\n      },\r\n      {\r\n        title: '登录',\r\n        shortcut: ['l', 'l'],\r\n        url: '/',\r\n        icon: 'login'\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\nexport interface SaleUser {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  amount: string;\r\n  image: string;\r\n  initials: string;\r\n}\r\n\r\nexport const recentSalesData: SaleUser[] = [\r\n  {\r\n    id: 1,\r\n    name: 'Olivia Martin',\r\n    email: '<EMAIL>',\r\n    amount: '+$1,999.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/1.png',\r\n    initials: 'OM'\r\n  },\r\n  {\r\n    id: 2,\r\n    name: 'Jackson Lee',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/2.png',\r\n    initials: 'JL'\r\n  },\r\n  {\r\n    id: 3,\r\n    name: 'Isabella Nguyen',\r\n    email: '<EMAIL>',\r\n    amount: '+$299.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/3.png',\r\n    initials: 'IN'\r\n  },\r\n  {\r\n    id: 4,\r\n    name: 'William Kim',\r\n    email: '<EMAIL>',\r\n    amount: '+$99.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/4.png',\r\n    initials: 'WK'\r\n  },\r\n  {\r\n    id: 5,\r\n    name: 'Sofia Davis',\r\n    email: '<EMAIL>',\r\n    amount: '+$39.00',\r\n    image: 'https://api.slingacademy.com/public/sample-users/5.png',\r\n    initials: 'SD'\r\n  }\r\n];\r\n", "import * as React from 'react';\nconst MOBILE_BREAKPOINT = 768;\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined);\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    };\n    mql.addEventListener('change', onChange);\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\n    return () => mql.removeEventListener('change', onChange);\n  }, []);\n  return !!isMobile;\n}", "'use client';\n\nimport * as React from 'react';\nimport * as SheetPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Sheet({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot='sheet' {...props} data-sentry-element=\"SheetPrimitive.Root\" data-sentry-component=\"Sheet\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot='sheet-trigger' {...props} data-sentry-element=\"SheetPrimitive.Trigger\" data-sentry-component=\"SheetTrigger\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot='sheet-close' {...props} data-sentry-element=\"SheetPrimitive.Close\" data-sentry-component=\"SheetClose\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot='sheet-portal' {...props} data-sentry-element=\"SheetPrimitive.Portal\" data-sentry-component=\"SheetPortal\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return <SheetPrimitive.Overlay data-slot='sheet-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"SheetPrimitive.Overlay\" data-sentry-component=\"SheetOverlay\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetContent({\n  className,\n  children,\n  side = 'right',\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: 'top' | 'right' | 'bottom' | 'left';\n}) {\n  return <SheetPortal data-sentry-element=\"SheetPortal\" data-sentry-component=\"SheetContent\" data-sentry-source-file=\"sheet.tsx\">\r\n      <SheetOverlay data-sentry-element=\"SheetOverlay\" data-sentry-source-file=\"sheet.tsx\" />\r\n      <SheetPrimitive.Content data-slot='sheet-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500', side === 'right' && 'data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm', side === 'left' && 'data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm', side === 'top' && 'data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b', side === 'bottom' && 'data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t', className)} {...props} data-sentry-element=\"SheetPrimitive.Content\" data-sentry-source-file=\"sheet.tsx\">\r\n        {children}\r\n        <SheetPrimitive.Close className='ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none' data-sentry-element=\"SheetPrimitive.Close\" data-sentry-source-file=\"sheet.tsx\">\r\n          <XIcon className='size-4' data-sentry-element=\"XIcon\" data-sentry-source-file=\"sheet.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>;\n}\nfunction SheetHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sheet-header' className={cn('flex flex-col gap-1.5 p-4', className)} {...props} data-sentry-component=\"SheetHeader\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sheet-footer' className={cn('mt-auto flex flex-col gap-2 p-4', className)} {...props} data-sentry-component=\"SheetFooter\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return <SheetPrimitive.Title data-slot='sheet-title' className={cn('text-foreground font-semibold', className)} {...props} data-sentry-element=\"SheetPrimitive.Title\" data-sentry-component=\"SheetTitle\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return <SheetPrimitive.Description data-slot='sheet-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"SheetPrimitive.Description\" data-sentry-component=\"SheetDescription\" data-sentry-source-file=\"sheet.tsx\" />;\n}\nexport { Sheet, SheetTrigger, SheetClose, SheetContent, SheetHeader, SheetFooter, SheetTitle, SheetDescription };", "import { cn } from '@/lib/utils';\nfunction Skeleton({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='skeleton' className={cn('bg-accent animate-pulse rounded-md', className)} {...props} data-sentry-component=\"Skeleton\" data-sentry-source-file=\"skeleton.tsx\" />;\n}\nexport { Skeleton };", "'use client';\n\nimport * as React from 'react';\nimport * as TooltipPrimitive from '@radix-ui/react-tooltip';\nimport { cn } from '@/lib/utils';\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return <TooltipPrimitive.Provider data-slot='tooltip-provider' delayDuration={delayDuration} {...props} data-sentry-element=\"TooltipPrimitive.Provider\" data-sentry-component=\"TooltipProvider\" data-sentry-source-file=\"tooltip.tsx\" />;\n}\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return <TooltipProvider data-sentry-element=\"TooltipProvider\" data-sentry-component=\"Tooltip\" data-sentry-source-file=\"tooltip.tsx\">\r\n      <TooltipPrimitive.Root data-slot='tooltip' {...props} data-sentry-element=\"TooltipPrimitive.Root\" data-sentry-source-file=\"tooltip.tsx\" />\r\n    </TooltipProvider>;\n}\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot='tooltip-trigger' {...props} data-sentry-element=\"TooltipPrimitive.Trigger\" data-sentry-component=\"TooltipTrigger\" data-sentry-source-file=\"tooltip.tsx\" />;\n}\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return <TooltipPrimitive.Portal data-sentry-element=\"TooltipPrimitive.Portal\" data-sentry-component=\"TooltipContent\" data-sentry-source-file=\"tooltip.tsx\">\r\n      <TooltipPrimitive.Content data-slot='tooltip-content' sideOffset={sideOffset} className={cn('bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance', className)} {...props} data-sentry-element=\"TooltipPrimitive.Content\" data-sentry-source-file=\"tooltip.tsx\">\r\n        {children}\r\n        <TooltipPrimitive.Arrow className='bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]' data-sentry-element=\"TooltipPrimitive.Arrow\" data-sentry-source-file=\"tooltip.tsx\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>;\n}\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };", "'use client';\n\nimport * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { VariantProps, cva } from 'class-variance-authority';\nimport { PanelLeftIcon } from 'lucide-react';\nimport { useIsMobile } from '@/hooks/use-mobile';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Separator } from '@/components/ui/separator';\nimport { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '@/components/ui/sheet';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\nconst SIDEBAR_COOKIE_NAME = 'sidebar_state';\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = '16rem';\nconst SIDEBAR_WIDTH_MOBILE = '18rem';\nconst SIDEBAR_WIDTH_ICON = '3rem';\nconst SIDEBAR_KEYBOARD_SHORTCUT = 'b';\ntype SidebarContextProps = {\n  state: 'expanded' | 'collapsed';\n  open: boolean;\n  setOpen: (open: boolean) => void;\n  openMobile: boolean;\n  setOpenMobile: (open: boolean) => void;\n  isMobile: boolean;\n  toggleSidebar: () => void;\n};\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext);\n  if (!context) {\n    throw new Error('useSidebar must be used within a SidebarProvider.');\n  }\n  return context;\n}\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<'div'> & {\n  defaultOpen?: boolean;\n  open?: boolean;\n  onOpenChange?: (open: boolean) => void;\n}) {\n  const isMobile = useIsMobile();\n  const [openMobile, setOpenMobile] = React.useState(false);\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen);\n  const open = openProp ?? _open;\n  const setOpen = React.useCallback((value: boolean | ((value: boolean) => boolean)) => {\n    const openState = typeof value === 'function' ? value(open) : value;\n    if (setOpenProp) {\n      setOpenProp(openState);\n    } else {\n      _setOpen(openState);\n    }\n\n    // This sets the cookie to keep the sidebar state.\n    document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\n  }, [setOpenProp, open]);\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile(open => !open) : setOpen(open => !open);\n  }, [isMobile, setOpen, setOpenMobile]);\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n        event.preventDefault();\n        toggleSidebar();\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [toggleSidebar]);\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? 'expanded' : 'collapsed';\n  const contextValue = React.useMemo<SidebarContextProps>(() => ({\n    state,\n    open,\n    setOpen,\n    isMobile,\n    openMobile,\n    setOpenMobile,\n    toggleSidebar\n  }), [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]);\n  return <SidebarContext.Provider value={contextValue} data-sentry-element=\"SidebarContext.Provider\" data-sentry-component=\"SidebarProvider\" data-sentry-source-file=\"sidebar.tsx\">\r\n      <TooltipProvider delayDuration={0} data-sentry-element=\"TooltipProvider\" data-sentry-source-file=\"sidebar.tsx\">\r\n        <div data-slot='sidebar-wrapper' style={{\n        '--sidebar-width': SIDEBAR_WIDTH,\n        '--sidebar-width-icon': SIDEBAR_WIDTH_ICON,\n        ...style\n      } as React.CSSProperties} className={cn('group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full', className)} {...props}>\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>;\n}\nfunction Sidebar({\n  side = 'left',\n  variant = 'sidebar',\n  collapsible = 'offcanvas',\n  className,\n  children,\n  ...props\n}: React.ComponentProps<'div'> & {\n  side?: 'left' | 'right';\n  variant?: 'sidebar' | 'floating' | 'inset';\n  collapsible?: 'offcanvas' | 'icon' | 'none';\n}) {\n  const {\n    isMobile,\n    state,\n    openMobile,\n    setOpenMobile\n  } = useSidebar();\n  if (collapsible === 'none') {\n    return <div data-slot='sidebar' className={cn('bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col', className)} {...props}>\r\n        {children}\r\n      </div>;\n  }\n  if (isMobile) {\n    return <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent data-sidebar='sidebar' data-slot='sidebar' data-mobile='true' className='bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden' style={{\n        '--sidebar-width': SIDEBAR_WIDTH_MOBILE\n      } as React.CSSProperties} side={side}>\r\n          <SheetHeader className='sr-only'>\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className='flex h-full w-full flex-col'>{children}</div>\r\n        </SheetContent>\r\n      </Sheet>;\n  }\n  return <div className='group peer text-sidebar-foreground hidden md:block' data-state={state} data-collapsible={state === 'collapsed' ? collapsible : ''} data-variant={variant} data-side={side} data-slot='sidebar' data-sentry-component=\"Sidebar\" data-sentry-source-file=\"sidebar.tsx\">\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div data-slot='sidebar-gap' className={cn('relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear', 'group-data-[collapsible=offcanvas]:w-0', 'group-data-[side=right]:rotate-180', variant === 'floating' || variant === 'inset' ? 'group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]' : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon)')} />\r\n      <div data-slot='sidebar-container' className={cn('fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex', side === 'left' ? 'left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]' : 'right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]',\n    // Adjust the padding for floating and inset variants.\n    variant === 'floating' || variant === 'inset' ? 'p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]' : 'group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l', className)} {...props}>\r\n        <div data-sidebar='sidebar' data-slot='sidebar-inner' className='bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm'>\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>;\n}\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const {\n    toggleSidebar\n  } = useSidebar();\n  return <Button data-sidebar='trigger' data-slot='sidebar-trigger' variant='ghost' size='icon' className={cn('size-7', className)} onClick={event => {\n    onClick?.(event);\n    toggleSidebar();\n  }} {...props} data-sentry-element=\"Button\" data-sentry-component=\"SidebarTrigger\" data-sentry-source-file=\"sidebar.tsx\">\r\n      <PanelLeftIcon data-sentry-element=\"PanelLeftIcon\" data-sentry-source-file=\"sidebar.tsx\" />\r\n      <span className='sr-only'>Toggle Sidebar</span>\r\n    </Button>;\n}\nfunction SidebarRail({\n  className,\n  ...props\n}: React.ComponentProps<'button'>) {\n  const {\n    toggleSidebar\n  } = useSidebar();\n  return <button data-sidebar='rail' data-slot='sidebar-rail' aria-label='Toggle Sidebar' tabIndex={-1} onClick={toggleSidebar} title='Toggle Sidebar' className={cn('hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex', 'in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize', '[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize', 'hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full', '[[data-side=left][data-collapsible=offcanvas]_&]:-right-2', '[[data-side=right][data-collapsible=offcanvas]_&]:-left-2', className)} {...props} data-sentry-component=\"SidebarRail\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarInset({\n  className,\n  ...props\n}: React.ComponentProps<'main'>) {\n  return <main data-slot='sidebar-inset' className={cn('bg-background relative flex w-full flex-1 flex-col', 'md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2', className)} {...props} data-sentry-component=\"SidebarInset\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return <Input data-slot='sidebar-input' data-sidebar='input' className={cn('bg-background h-8 w-full shadow-none', className)} {...props} data-sentry-element=\"Input\" data-sentry-component=\"SidebarInput\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-header' data-sidebar='header' className={cn('flex flex-col gap-2 p-2', className)} {...props} data-sentry-component=\"SidebarHeader\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-footer' data-sidebar='footer' className={cn('flex flex-col gap-2 p-2', className)} {...props} data-sentry-component=\"SidebarFooter\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return <Separator data-slot='sidebar-separator' data-sidebar='separator' className={cn('bg-sidebar-border mx-2 w-auto', className)} {...props} data-sentry-element=\"Separator\" data-sentry-component=\"SidebarSeparator\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-content' data-sidebar='content' className={cn('flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden', className)} {...props} data-sentry-component=\"SidebarContent\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroup({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-group' data-sidebar='group' className={cn('relative flex w-full min-w-0 flex-col p-2', className)} {...props} data-sentry-component=\"SidebarGroup\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'div'> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'div';\n  return <Comp data-slot='sidebar-group-label' data-sidebar='group-label' className={cn('text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0', 'group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarGroupLabel\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='sidebar-group-action' data-sidebar='group-action' className={cn('text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\n  // Increases the hit area of the button on mobile.\n  'after:absolute after:-inset-2 md:after:hidden', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarGroupAction\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-group-content' data-sidebar='group-content' className={cn('w-full text-sm', className)} {...props} data-sentry-component=\"SidebarGroupContent\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenu({\n  className,\n  ...props\n}: React.ComponentProps<'ul'>) {\n  return <ul data-slot='sidebar-menu' data-sidebar='menu' className={cn('flex w-full min-w-0 flex-col gap-1', className)} {...props} data-sentry-component=\"SidebarMenu\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='sidebar-menu-item' data-sidebar='menu-item' className={cn('group/menu-item relative', className)} {...props} data-sentry-component=\"SidebarMenuItem\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nconst sidebarMenuButtonVariants = cva('peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0', {\n  variants: {\n    variant: {\n      default: 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground',\n      outline: 'bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]'\n    },\n    size: {\n      default: 'h-8 text-sm',\n      sm: 'h-7 text-xs',\n      lg: 'h-12 text-sm group-data-[collapsible=icon]:p-0!'\n    }\n  },\n  defaultVariants: {\n    variant: 'default',\n    size: 'default'\n  }\n});\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = 'default',\n  size = 'default',\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<'button'> & {\n  asChild?: boolean;\n  isActive?: boolean;\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : 'button';\n  const {\n    isMobile,\n    state\n  } = useSidebar();\n  const button = <Comp data-slot='sidebar-menu-button' data-sidebar='menu-button' data-size={size} data-active={isActive} className={cn(sidebarMenuButtonVariants({\n    variant,\n    size\n  }), className)} {...props} />;\n  if (!tooltip) {\n    return button;\n  }\n  if (typeof tooltip === 'string') {\n    tooltip = {\n      children: tooltip\n    };\n  }\n  return <Tooltip data-sentry-element=\"Tooltip\" data-sentry-component=\"SidebarMenuButton\" data-sentry-source-file=\"sidebar.tsx\">\r\n      <TooltipTrigger asChild data-sentry-element=\"TooltipTrigger\" data-sentry-source-file=\"sidebar.tsx\">{button}</TooltipTrigger>\r\n      <TooltipContent side='right' align='center' hidden={state !== 'collapsed' || isMobile} {...tooltip} data-sentry-element=\"TooltipContent\" data-sentry-source-file=\"sidebar.tsx\" />\r\n    </Tooltip>;\n}\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<'button'> & {\n  asChild?: boolean;\n  showOnHover?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'button';\n  return <Comp data-slot='sidebar-menu-action' data-sidebar='menu-action' className={cn('text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0',\n  // Increases the hit area of the button on mobile.\n  'after:absolute after:-inset-2 md:after:hidden', 'peer-data-[size=sm]/menu-button:top-1', 'peer-data-[size=default]/menu-button:top-1.5', 'peer-data-[size=lg]/menu-button:top-2.5', 'group-data-[collapsible=icon]:hidden', showOnHover && 'peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarMenuAction\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='sidebar-menu-badge' data-sidebar='menu-badge' className={cn('text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none', 'peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground', 'peer-data-[size=sm]/menu-button:top-1', 'peer-data-[size=default]/menu-button:top-1.5', 'peer-data-[size=lg]/menu-button:top-2.5', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-component=\"SidebarMenuBadge\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<'div'> & {\n  showIcon?: boolean;\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`;\n  }, []);\n  return <div data-slot='sidebar-menu-skeleton' data-sidebar='menu-skeleton' className={cn('flex h-8 items-center gap-2 rounded-md px-2', className)} {...props} data-sentry-component=\"SidebarMenuSkeleton\" data-sentry-source-file=\"sidebar.tsx\">\r\n      {showIcon && <Skeleton className='size-4 rounded-md' data-sidebar='menu-skeleton-icon' />}\r\n      <Skeleton className='h-4 max-w-(--skeleton-width) flex-1' data-sidebar='menu-skeleton-text' style={{\n      '--skeleton-width': width\n    } as React.CSSProperties} data-sentry-element=\"Skeleton\" data-sentry-source-file=\"sidebar.tsx\" />\r\n    </div>;\n}\nfunction SidebarMenuSub({\n  className,\n  ...props\n}: React.ComponentProps<'ul'>) {\n  return <ul data-slot='sidebar-menu-sub' data-sidebar='menu-sub' className={cn('border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-component=\"SidebarMenuSub\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='sidebar-menu-sub-item' data-sidebar='menu-sub-item' className={cn('group/menu-sub-item relative', className)} {...props} data-sentry-component=\"SidebarMenuSubItem\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = 'md',\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<'a'> & {\n  asChild?: boolean;\n  size?: 'sm' | 'md';\n  isActive?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'a';\n  return <Comp data-slot='sidebar-menu-sub-button' data-sidebar='menu-sub-button' data-size={size} data-active={isActive} className={cn('text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0', 'data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground', size === 'sm' && 'text-xs', size === 'md' && 'text-sm', 'group-data-[collapsible=icon]:hidden', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"SidebarMenuSubButton\" data-sentry-source-file=\"sidebar.tsx\" />;\n}\nexport { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupAction, SidebarGroupContent, SidebarGroupLabel, SidebarHeader, SidebarInput, SidebarInset, SidebarMenu, SidebarMenuAction, SidebarMenuBadge, SidebarMenuButton, SidebarMenuItem, SidebarMenuSkeleton, SidebarMenuSub, SidebarMenuSubButton, SidebarMenuSubItem, SidebarProvider, SidebarRail, SidebarSeparator, SidebarTrigger, useSidebar };", "import(/* webpackMode: \"eager\", webpackExports: [\"Breadcrumbs\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\breadcrumbs.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ModeToggle\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\ThemeToggle\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"UserNav\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\user-nav.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\search-input.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeSelector\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\theme-selector.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Separator\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\separator.tsx\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"RoleProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\lib\\\\role-context.tsx\");\n", "import type { ActionId, ActionImpl } from 'kbar';\nimport * as React from 'react';\nconst ResultItem = React.forwardRef(({\n  action,\n  active,\n  currentRootActionId\n}: {\n  action: ActionImpl;\n  active: boolean;\n  currentRootActionId: ActionId;\n}, ref: React.Ref<HTMLDivElement>) => {\n  const ancestors = React.useMemo(() => {\n    if (!currentRootActionId) return action.ancestors;\n    const index = action.ancestors.findIndex(ancestor => ancestor.id === currentRootActionId);\n    return action.ancestors.slice(index + 1);\n  }, [action.ancestors, currentRootActionId]);\n  return <div ref={ref} className={`relative z-10 flex cursor-pointer items-center justify-between px-4 py-3`}>\r\n        {active && <div id='kbar-result-item' className='border-primary bg-accent/50 absolute inset-0 z-[-1]! border-l-4'></div>}\r\n        <div className='relative z-10 flex items-center gap-2'>\r\n          {action.icon && action.icon}\r\n          <div className='flex flex-col'>\r\n            <div>\r\n              {ancestors.length > 0 && ancestors.map(ancestor => <React.Fragment key={ancestor.id}>\r\n                    <span className='text-muted-foreground mr-2'>\r\n                      {ancestor.name}\r\n                    </span>\r\n                    <span className='mr-2'>&rsaquo;</span>\r\n                  </React.Fragment>)}\r\n              <span>{action.name}</span>\r\n            </div>\r\n            {action.subtitle && <span className='text-muted-foreground text-sm'>\r\n                {action.subtitle}\r\n              </span>}\r\n          </div>\r\n        </div>\r\n        {action.shortcut?.length ? <div className='relative z-10 grid grid-flow-col gap-1'>\r\n            {action.shortcut.map((sc, i) => <kbd key={sc + i} className='bg-muted flex h-5 items-center gap-1 rounded-md border px-1.5 text-[10px] font-medium'>\r\n                {sc}\r\n              </kbd>)}\r\n          </div> : null}\r\n      </div>;\n});\nResultItem.displayName = 'KBarResultItem';\nexport default ResultItem;", "import { KBarResults, useMatches } from 'kbar';\nimport ResultItem from './result-item';\nexport default function RenderResults() {\n  const {\n    results,\n    rootActionId\n  } = useMatches();\n  return <KBarResults items={results} onRender={({\n    item,\n    active\n  }) => typeof item === 'string' ? <div className='text-primary-foreground px-4 py-2 text-sm uppercase opacity-50'>\r\n            {item}\r\n          </div> : <ResultItem action={item} active={active} currentRootActionId={rootActionId ?? ''} />} data-sentry-element=\"KBarResults\" data-sentry-component=\"RenderResults\" data-sentry-source-file=\"render-result.tsx\" />;\n}", "import { useRegisterActions } from 'kbar';\nimport { useTheme } from 'next-themes';\nconst useThemeSwitching = () => {\n  const {\n    theme,\n    setTheme\n  } = useTheme();\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light');\n  };\n  const themeAction = [{\n    id: 'toggleTheme',\n    name: 'Toggle Theme',\n    shortcut: ['t', 't'],\n    section: 'Theme',\n    perform: toggleTheme\n  }, {\n    id: 'setLightTheme',\n    name: 'Set Light Theme',\n    section: 'Theme',\n    perform: () => setTheme('light')\n  }, {\n    id: 'setDarkTheme',\n    name: 'Set Dark Theme',\n    section: 'Theme',\n    perform: () => setTheme('dark')\n  }];\n  useRegisterActions(themeAction, [theme]);\n};\nexport default useThemeSwitching;", "'use client';\n\nimport { navItems } from '@/constants/data';\nimport { KBarAnimator, KBarPortal, KBarPositioner, KBarProvider, KBarSearch } from 'kbar';\nimport { useRouter } from 'next/navigation';\nimport { useMemo } from 'react';\nimport RenderResults from './render-result';\nimport useThemeSwitching from './use-theme-switching';\nexport default function KBar({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  const router = useRouter();\n\n  // These action are for the navigation\n  const actions = useMemo(() => {\n    // Define navigateTo inside the useMemo callback to avoid dependency array issues\n    const navigateTo = (url: string) => {\n      router.push(url);\n    };\n    return navItems.flatMap(navItem => {\n      // Only include base action if the navItem has a real URL and is not just a container\n      const baseAction = navItem.url !== '#' ? {\n        id: `${navItem.title.toLowerCase()}Action`,\n        name: navItem.title,\n        shortcut: navItem.shortcut,\n        keywords: navItem.title.toLowerCase(),\n        section: 'Navigation',\n        subtitle: `Go to ${navItem.title}`,\n        perform: () => navigateTo(navItem.url)\n      } : null;\n\n      // Map child items into actions\n      const childActions = navItem.items?.map(childItem => ({\n        id: `${childItem.title.toLowerCase()}Action`,\n        name: childItem.title,\n        shortcut: childItem.shortcut,\n        keywords: childItem.title.toLowerCase(),\n        section: navItem.title,\n        subtitle: `Go to ${childItem.title}`,\n        perform: () => navigateTo(childItem.url)\n      })) ?? [];\n\n      // Return only valid actions (ignoring null base actions for containers)\n      return baseAction ? [baseAction, ...childActions] : childActions;\n    });\n  }, [router]);\n  return <KBarProvider actions={actions} data-sentry-element=\"KBarProvider\" data-sentry-component=\"KBar\" data-sentry-source-file=\"index.tsx\">\r\n      <KBarComponent data-sentry-element=\"KBarComponent\" data-sentry-source-file=\"index.tsx\">{children}</KBarComponent>\r\n    </KBarProvider>;\n}\nconst KBarComponent = ({\n  children\n}: {\n  children: React.ReactNode;\n}) => {\n  useThemeSwitching();\n  return <>\r\n      <KBarPortal data-sentry-element=\"KBarPortal\" data-sentry-source-file=\"index.tsx\">\r\n        <KBarPositioner className='bg-background/80 fixed inset-0 z-99999 p-0! backdrop-blur-sm' data-sentry-element=\"KBarPositioner\" data-sentry-source-file=\"index.tsx\">\r\n          <KBarAnimator className='bg-card text-card-foreground relative mt-64! w-full max-w-[600px] -translate-y-12! overflow-hidden rounded-lg border shadow-lg' data-sentry-element=\"KBarAnimator\" data-sentry-source-file=\"index.tsx\">\r\n            <div className='bg-card border-border sticky top-0 z-10 border-b'>\r\n              <KBarSearch className='bg-card w-full border-none px-6 py-4 text-lg outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden' data-sentry-element=\"KBarSearch\" data-sentry-source-file=\"index.tsx\" />\r\n            </div>\r\n            <div className='max-h-[400px]'>\r\n              <RenderResults data-sentry-element=\"RenderResults\" data-sentry-source-file=\"index.tsx\" />\r\n            </div>\r\n          </KBarAnimator>\r\n        </KBarPositioner>\r\n      </KBarPortal>\r\n      {children}\r\n    </>;\n};", "'use client';\n\nimport { But<PERSON> } from '@/components/ui/button';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\nimport { SignOutButton, useUser } from '@clerk/nextjs';\nimport { useRouter } from 'next/navigation';\nexport function UserNav() {\n  const {\n    user\n  } = useUser();\n  const router = useRouter();\n  if (user) {\n    return <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant='ghost' className='relative h-8 w-8 rounded-full'>\r\n            <UserAvatarProfile user={user} />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent className='w-56' align='end' sideOffset={10} forceMount>\r\n          <DropdownMenuLabel className='font-normal'>\r\n            <div className='flex flex-col space-y-1'>\r\n              <p className='text-sm leading-none font-medium'>\r\n                {user.fullName}\r\n              </p>\r\n              <p className='text-muted-foreground text-xs leading-none'>\r\n                {user.emailAddresses[0].emailAddress}\r\n              </p>\r\n            </div>\r\n          </DropdownMenuLabel>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuGroup>\r\n            <DropdownMenuItem onClick={() => router.push('/dashboard/profile')}>\r\n              Profile\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem>Billing</DropdownMenuItem>\r\n            <DropdownMenuItem>Settings</DropdownMenuItem>\r\n            <DropdownMenuItem>New Team</DropdownMenuItem>\r\n          </DropdownMenuGroup>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem>\r\n            <SignOutButton redirectUrl='/auth/sign-in' />\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>;\n  }\n}", "'use client';\n\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nfunction Collapsible({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\n  return <CollapsiblePrimitive.Root data-slot='collapsible' {...props} data-sentry-element=\"CollapsiblePrimitive.Root\" data-sentry-component=\"Collapsible\" data-sentry-source-file=\"collapsible.tsx\" />;\n}\nfunction CollapsibleTrigger({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\n  return <CollapsiblePrimitive.CollapsibleTrigger data-slot='collapsible-trigger' {...props} data-sentry-element=\"CollapsiblePrimitive.CollapsibleTrigger\" data-sentry-component=\"CollapsibleTrigger\" data-sentry-source-file=\"collapsible.tsx\" />;\n}\nfunction CollapsibleContent({\n  ...props\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\n  return <CollapsiblePrimitive.CollapsibleContent data-slot='collapsible-content' {...props} data-sentry-element=\"CollapsiblePrimitive.CollapsibleContent\" data-sentry-component=\"CollapsibleContent\" data-sentry-source-file=\"collapsible.tsx\" />;\n}\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent };", "import { useEffect, useState } from 'react';\r\n\r\nexport function useMediaQuery() {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const mediaQuery = window.matchMedia('(max-width: 768px)');\r\n    setIsOpen(mediaQuery.matches);\r\n\r\n    const handler = (e: MediaQueryListEvent) => {\r\n      setIsOpen(e.matches);\r\n    };\r\n\r\n    mediaQuery.addEventListener('change', handler);\r\n    return () => mediaQuery.removeEventListener('change', handler);\r\n  }, []);\r\n\r\n  return { isOpen };\r\n}\r\n", "'use client';\n\nimport { Check, ChevronsUpDown, GalleryVerticalEnd } from 'lucide-react';\nimport * as React from 'react';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';\ninterface Tenant {\n  id: string;\n  name: string;\n}\nexport function OrgSwitcher({\n  tenants,\n  defaultTenant,\n  onTenantSwitch\n}: {\n  tenants: Tenant[];\n  defaultTenant: Tenant;\n  onTenantSwitch?: (tenantId: string) => void;\n}) {\n  const [selectedTenant, setSelectedTenant] = React.useState<Tenant | undefined>(defaultTenant || (tenants.length > 0 ? tenants[0] : undefined));\n  const handleTenantSwitch = (tenant: Tenant) => {\n    setSelectedTenant(tenant);\n    if (onTenantSwitch) {\n      onTenantSwitch(tenant.id);\n    }\n  };\n  if (!selectedTenant) {\n    return null;\n  }\n  return <SidebarMenu data-sentry-element=\"SidebarMenu\" data-sentry-component=\"OrgSwitcher\" data-sentry-source-file=\"org-switcher.tsx\">\r\n      <SidebarMenuItem data-sentry-element=\"SidebarMenuItem\" data-sentry-source-file=\"org-switcher.tsx\">\r\n        <DropdownMenu data-sentry-element=\"DropdownMenu\" data-sentry-source-file=\"org-switcher.tsx\">\r\n          <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"org-switcher.tsx\">\r\n            <SidebarMenuButton size='lg' className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground' data-sentry-element=\"SidebarMenuButton\" data-sentry-source-file=\"org-switcher.tsx\">\r\n              <div className='bg-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg'>\r\n                <GalleryVerticalEnd className='size-4' data-sentry-element=\"GalleryVerticalEnd\" data-sentry-source-file=\"org-switcher.tsx\" />\r\n              </div>\r\n              <div className='flex flex-col gap-0.5 leading-none'>\r\n                <span className='font-semibold'>Next Starter</span>\r\n                <span className=''>{selectedTenant.name}</span>\r\n              </div>\r\n              <ChevronsUpDown className='ml-auto' data-sentry-element=\"ChevronsUpDown\" data-sentry-source-file=\"org-switcher.tsx\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent className='w-[--radix-dropdown-menu-trigger-width]' align='start' data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"org-switcher.tsx\">\r\n            {tenants.map(tenant => <DropdownMenuItem key={tenant.id} onSelect={() => handleTenantSwitch(tenant)}>\r\n                {tenant.name}{' '}\r\n                {tenant.id === selectedTenant.id && <Check className='ml-auto' />}\r\n              </DropdownMenuItem>)}\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>;\n}", "'use client';\n\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { Sidebar, SidebarContent, SidebarFooter, SidebarGroup, SidebarGroupLabel, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, SidebarMenuSub, SidebarMenuSubButton, SidebarMenuSubItem, SidebarRail } from '@/components/ui/sidebar';\nimport { UserAvatarProfile } from '@/components/user-avatar-profile';\nimport { navItems } from '@/constants/data';\nimport { useMediaQuery } from '@/hooks/use-media-query';\nimport { useRole } from '@/lib/role-context';\nimport { useUser } from '@clerk/nextjs';\nimport { IconBell, IconChevronRight, IconChevronsDown, IconCreditCard, IconLogout, IconPhotoUp, IconUserCircle } from '@tabler/icons-react';\nimport { SignOutButton } from '@clerk/nextjs';\nimport Link from 'next/link';\nimport { usePathname, useRouter } from 'next/navigation';\nimport * as React from 'react';\nimport { Icons } from '../icons';\nimport { OrgSwitcher } from '../org-switcher';\nexport const company = {\n  name: 'Acme Inc',\n  logo: IconPhotoUp,\n  plan: 'Enterprise'\n};\nconst tenants = [{\n  id: '1',\n  name: 'Acme Inc'\n}, {\n  id: '2',\n  name: 'Beta Corp'\n}, {\n  id: '3',\n  name: 'Gamma Ltd'\n}];\nexport default function AppSidebar() {\n  const pathname = usePathname();\n  const {\n    isOpen\n  } = useMediaQuery();\n  const {\n    user\n  } = useUser();\n  const {\n    user: roleUser\n  } = useRole();\n  const router = useRouter();\n  const handleSwitchTenant = (_tenantId: string) => {\n    // Tenant switching functionality would be implemented here\n  };\n  const activeTenant = tenants[0];\n\n  // Filter navigation items based on user role\n  const filteredNavItems = React.useMemo(() => {\n    if (!roleUser?.role) return navItems;\n    const userRole = roleUser.role;\n    return navItems.filter(item => {\n      // If no roles specified, show to all users\n      if (!item.roles || item.roles.length === 0) return true;\n\n      // Check if user's role is in the allowed roles\n      return item.roles.includes(userRole);\n    });\n  }, [roleUser?.role]);\n  React.useEffect(() => {\n    // Side effects based on sidebar state changes\n  }, [isOpen]);\n  return <Sidebar collapsible='icon' data-sentry-element=\"Sidebar\" data-sentry-component=\"AppSidebar\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n      <SidebarHeader data-sentry-element=\"SidebarHeader\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n        <OrgSwitcher tenants={tenants} defaultTenant={activeTenant} onTenantSwitch={handleSwitchTenant} data-sentry-element=\"OrgSwitcher\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n      </SidebarHeader>\r\n      <SidebarContent className='overflow-x-hidden' data-sentry-element=\"SidebarContent\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n        <SidebarGroup data-sentry-element=\"SidebarGroup\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n          <SidebarGroupLabel data-sentry-element=\"SidebarGroupLabel\" data-sentry-source-file=\"app-sidebar.tsx\">概览</SidebarGroupLabel>\r\n          <SidebarMenu data-sentry-element=\"SidebarMenu\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n            {filteredNavItems.map(item => {\n            const Icon = item.icon ? Icons[item.icon] : Icons.logo;\n            return item?.items && item?.items?.length > 0 ? <Collapsible key={item.title} asChild defaultOpen={item.isActive} className='group/collapsible'>\r\n                  <SidebarMenuItem>\r\n                    <CollapsibleTrigger asChild>\r\n                      <SidebarMenuButton tooltip={item.title} isActive={pathname === item.url}>\r\n                        {item.icon && <Icon />}\r\n                        <span>{item.title}</span>\r\n                        <IconChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />\r\n                      </SidebarMenuButton>\r\n                    </CollapsibleTrigger>\r\n                    <CollapsibleContent>\r\n                      <SidebarMenuSub>\r\n                        {item.items?.map(subItem => <SidebarMenuSubItem key={subItem.title}>\r\n                            <SidebarMenuSubButton asChild isActive={pathname === subItem.url}>\r\n                              <Link href={subItem.url}>\r\n                                <span>{subItem.title}</span>\r\n                              </Link>\r\n                            </SidebarMenuSubButton>\r\n                          </SidebarMenuSubItem>)}\r\n                      </SidebarMenuSub>\r\n                    </CollapsibleContent>\r\n                  </SidebarMenuItem>\r\n                </Collapsible> : <SidebarMenuItem key={item.title}>\r\n                  <SidebarMenuButton asChild tooltip={item.title} isActive={pathname === item.url}>\r\n                    <Link href={item.url}>\r\n                      <Icon />\r\n                      <span>{item.title}</span>\r\n                    </Link>\r\n                  </SidebarMenuButton>\r\n                </SidebarMenuItem>;\n          })}\r\n          </SidebarMenu>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter data-sentry-element=\"SidebarFooter\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n        <SidebarMenu data-sentry-element=\"SidebarMenu\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n          <SidebarMenuItem data-sentry-element=\"SidebarMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n            <DropdownMenu data-sentry-element=\"DropdownMenu\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n              <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                <SidebarMenuButton size='lg' className='data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground' data-sentry-element=\"SidebarMenuButton\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  {user && <UserAvatarProfile className='h-8 w-8 rounded-lg' showInfo user={user} />}\r\n                  <IconChevronsDown className='ml-auto size-4' data-sentry-element=\"IconChevronsDown\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                </SidebarMenuButton>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent className='w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg' side='bottom' align='end' sideOffset={4} data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                <DropdownMenuLabel className='p-0 font-normal' data-sentry-element=\"DropdownMenuLabel\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <div className='px-1 py-1.5'>\r\n                    {user && <UserAvatarProfile className='h-8 w-8 rounded-lg' showInfo user={user} />}\r\n                  </div>\r\n                </DropdownMenuLabel>\r\n                <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n\r\n                <DropdownMenuGroup data-sentry-element=\"DropdownMenuGroup\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <DropdownMenuItem onClick={() => router.push('/dashboard/profile')} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                    <IconUserCircle className='mr-2 h-4 w-4' data-sentry-element=\"IconUserCircle\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                    Profile\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                    <IconCreditCard className='mr-2 h-4 w-4' data-sentry-element=\"IconCreditCard\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                    Billing\r\n                  </DropdownMenuItem>\r\n                  <DropdownMenuItem data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                    <IconBell className='mr-2 h-4 w-4' data-sentry-element=\"IconBell\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                    Notifications\r\n                  </DropdownMenuItem>\r\n                </DropdownMenuGroup>\r\n                <DropdownMenuSeparator data-sentry-element=\"DropdownMenuSeparator\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                <DropdownMenuItem data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"app-sidebar.tsx\">\r\n                  <IconLogout className='mr-2 h-4 w-4' data-sentry-element=\"IconLogout\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                  <SignOutButton redirectUrl='/auth/sign-in' data-sentry-element=\"SignOutButton\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarFooter>\r\n      <SidebarRail data-sentry-element=\"SidebarRail\" data-sentry-source-file=\"app-sidebar.tsx\" />\r\n    </Sidebar>;\n}", "import React from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { IconBrandGithub } from '@tabler/icons-react';\nexport default function CtaGithub() {\n  return <Button variant='ghost' asChild size='sm' className='hidden sm:flex' data-sentry-element=\"Button\" data-sentry-component=\"CtaGithub\" data-sentry-source-file=\"cta-github.tsx\">\r\n      <a href='https://github.com/Kiranism/next-shadcn-dashboard-starter' rel='noopener noreferrer' target='_blank' className='dark:text-foreground'>\r\n        <IconBrandGithub data-sentry-element=\"IconBrandGithub\" data-sentry-source-file=\"cta-github.tsx\" />\r\n      </a>\r\n    </Button>;\n}", "import React from 'react';\nimport { SidebarTrigger } from '../ui/sidebar';\nimport { Separator } from '../ui/separator';\nimport { Breadcrumbs } from '../breadcrumbs';\nimport SearchInput from '../search-input';\nimport { UserNav } from './user-nav';\nimport { ThemeSelector } from '../theme-selector';\nimport { ModeToggle } from './ThemeToggle/theme-toggle';\nimport CtaGithub from './cta-github';\nexport default function Header() {\n  return <header className='flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12' data-sentry-component=\"Header\" data-sentry-source-file=\"header.tsx\">\r\n      <div className='flex items-center gap-2 px-4'>\r\n        <SidebarTrigger className='-ml-1' data-sentry-element=\"SidebarTrigger\" data-sentry-source-file=\"header.tsx\" />\r\n        <Separator orientation='vertical' className='mr-2 h-4' data-sentry-element=\"Separator\" data-sentry-source-file=\"header.tsx\" />\r\n        <Breadcrumbs data-sentry-element=\"Breadcrumbs\" data-sentry-source-file=\"header.tsx\" />\r\n      </div>\r\n\r\n      <div className='flex items-center gap-2 px-4'>\r\n        <CtaGithub data-sentry-element=\"CtaGithub\" data-sentry-source-file=\"header.tsx\" />\r\n        <div className='hidden md:flex'>\r\n          <SearchInput data-sentry-element=\"SearchInput\" data-sentry-source-file=\"header.tsx\" />\r\n        </div>\r\n        <UserNav data-sentry-element=\"UserNav\" data-sentry-source-file=\"header.tsx\" />\r\n        <ModeToggle data-sentry-element=\"ModeToggle\" data-sentry-source-file=\"header.tsx\" />\r\n        <ThemeSelector data-sentry-element=\"ThemeSelector\" data-sentry-source-file=\"header.tsx\" />\r\n      </div>\r\n    </header>;\n}", "import KBar from '@/components/kbar';\nimport AppSidebar from '@/components/layout/app-sidebar';\nimport Header from '@/components/layout/header';\nimport { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';\nimport { RoleProvider } from '@/lib/role-context';\nimport type { Metadata } from 'next';\nimport { cookies } from 'next/headers';\nexport const metadata: Metadata = {\n  title: 'Next Shadcn Dashboard Starter',\n  description: 'Basic dashboard with Next.js and Shadcn'\n};\nexport default async function DashboardLayout({\n  children\n}: {\n  children: React.ReactNode;\n}) {\n  // Persisting the sidebar state in the cookie.\n  const cookieStore = await cookies();\n  const defaultOpen = cookieStore.get(\"sidebar_state\")?.value === \"true\";\n  return <RoleProvider data-sentry-element=\"RoleProvider\" data-sentry-component=\"DashboardLayout\" data-sentry-source-file=\"layout.tsx\">\r\n      <KBar data-sentry-element=\"KBar\" data-sentry-source-file=\"layout.tsx\">\r\n        <SidebarProvider defaultOpen={defaultOpen} data-sentry-element=\"SidebarProvider\" data-sentry-source-file=\"layout.tsx\">\r\n          <AppSidebar data-sentry-element=\"AppSidebar\" data-sentry-source-file=\"layout.tsx\" />\r\n          <SidebarInset data-sentry-element=\"SidebarInset\" data-sentry-source-file=\"layout.tsx\">\r\n            <Header data-sentry-element=\"Header\" data-sentry-source-file=\"layout.tsx\" />\r\n            {/* page main content */}\r\n            {children}\r\n            {/* page main content ends */}\r\n          </SidebarInset>\r\n        </SidebarProvider>\r\n      </KBar>\r\n    </RoleProvider>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Layout',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "'use client';\n\nimport { useThemeConfig } from '@/components/active-theme';\nimport { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectSeparator, SelectTrigger, SelectValue } from '@/components/ui/select';\nconst DEFAULT_THEMES = [{\n  name: 'Default',\n  value: 'default'\n}, {\n  name: 'Blue',\n  value: 'blue'\n}, {\n  name: 'Green',\n  value: 'green'\n}, {\n  name: 'Amber',\n  value: 'amber'\n}];\nconst SCALED_THEMES = [{\n  name: 'Default',\n  value: 'default-scaled'\n}, {\n  name: 'Blue',\n  value: 'blue-scaled'\n}];\nconst MONO_THEMES = [{\n  name: 'Mono',\n  value: 'mono-scaled'\n}];\nexport function ThemeSelector() {\n  const {\n    activeTheme,\n    setActiveTheme\n  } = useThemeConfig();\n  return <div className='flex items-center gap-2' data-sentry-component=\"ThemeSelector\" data-sentry-source-file=\"theme-selector.tsx\">\r\n      <Label htmlFor='theme-selector' className='sr-only' data-sentry-element=\"Label\" data-sentry-source-file=\"theme-selector.tsx\">\r\n        Theme\r\n      </Label>\r\n      <Select value={activeTheme} onValueChange={setActiveTheme} data-sentry-element=\"Select\" data-sentry-source-file=\"theme-selector.tsx\">\r\n        <SelectTrigger id='theme-selector' className='justify-start *:data-[slot=select-value]:w-12' data-sentry-element=\"SelectTrigger\" data-sentry-source-file=\"theme-selector.tsx\">\r\n          <span className='text-muted-foreground hidden sm:block'>\r\n            Select a theme:\r\n          </span>\r\n          <span className='text-muted-foreground block sm:hidden'>Theme</span>\r\n          <SelectValue placeholder='Select a theme' data-sentry-element=\"SelectValue\" data-sentry-source-file=\"theme-selector.tsx\" />\r\n        </SelectTrigger>\r\n        <SelectContent align='end' data-sentry-element=\"SelectContent\" data-sentry-source-file=\"theme-selector.tsx\">\r\n          <SelectGroup data-sentry-element=\"SelectGroup\" data-sentry-source-file=\"theme-selector.tsx\">\r\n            <SelectLabel data-sentry-element=\"SelectLabel\" data-sentry-source-file=\"theme-selector.tsx\">Default</SelectLabel>\r\n            {DEFAULT_THEMES.map(theme => <SelectItem key={theme.name} value={theme.value}>\r\n                {theme.name}\r\n              </SelectItem>)}\r\n          </SelectGroup>\r\n          <SelectSeparator data-sentry-element=\"SelectSeparator\" data-sentry-source-file=\"theme-selector.tsx\" />\r\n          <SelectGroup data-sentry-element=\"SelectGroup\" data-sentry-source-file=\"theme-selector.tsx\">\r\n            <SelectLabel data-sentry-element=\"SelectLabel\" data-sentry-source-file=\"theme-selector.tsx\">Scaled</SelectLabel>\r\n            {SCALED_THEMES.map(theme => <SelectItem key={theme.name} value={theme.value}>\r\n                {theme.name}\r\n              </SelectItem>)}\r\n          </SelectGroup>\r\n          <SelectGroup data-sentry-element=\"SelectGroup\" data-sentry-source-file=\"theme-selector.tsx\">\r\n            <SelectLabel data-sentry-element=\"SelectLabel\" data-sentry-source-file=\"theme-selector.tsx\">Monospaced</SelectLabel>\r\n            {MONO_THEMES.map(theme => <SelectItem key={theme.name} value={theme.value}>\r\n                {theme.name}\r\n              </SelectItem>)}\r\n          </SelectGroup>\r\n        </SelectContent>\r\n      </Select>\r\n    </div>;\n}", "'use client';\n\n// Role-based access control context and hooks\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { useUser } from '@clerk/nextjs';\nimport { AuthenticatedUser } from './auth-middleware';\nimport { UserRole, hasPermission, hasRole, getRolePermissions, RolePermissions } from './role-utils';\ninterface RoleContextType {\n  user: AuthenticatedUser | null;\n  loading: boolean;\n  error: string | null;\n  permissions: RolePermissions | null;\n  hasPermission: (permission: keyof RolePermissions) => boolean;\n  hasRole: (roles: UserRole | UserRole[]) => boolean;\n  refreshUser: () => Promise<void>;\n}\nconst RoleContext = createContext<RoleContextType | undefined>(undefined);\ninterface RoleProviderProps {\n  children: React.ReactNode;\n}\n\n/**\n * Role Provider Component\n * Fetches user role information and provides role-based utilities\n */\nexport function RoleProvider({\n  children\n}: RoleProviderProps) {\n  const {\n    user: clerkUser,\n    isLoaded: clerkLoaded\n  } = useUser();\n  const [user, setUser] = useState<AuthenticatedUser | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const fetchUserRole = async () => {\n    if (!clerkUser) {\n      setUser(null);\n      setLoading(false);\n      return;\n    }\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Call the auth sync endpoint to get user role from backend\n      const response = await fetch('/api/auth/sync', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error('Failed to sync user data');\n      }\n      const data = await response.json();\n\n      // Use the synced user data from backend\n      const authenticatedUser: AuthenticatedUser = {\n        clerkId: clerkUser.id,\n        email: clerkUser.emailAddresses[0]?.emailAddress || '',\n        firstName: clerkUser.firstName || undefined,\n        lastName: clerkUser.lastName || undefined,\n        role: data.user?.role || 'front-desk',\n        // Use role from backend or default\n        payloadUserId: data.user?.payloadUserId // Set from backend response\n      };\n      setUser(authenticatedUser);\n    } catch (err) {\n      console.error('Error fetching user role:', err);\n      setError(err instanceof Error ? err.message : 'Failed to fetch user role');\n\n      // Set user with default role on error\n      if (clerkUser) {\n        setUser({\n          clerkId: clerkUser.id,\n          email: clerkUser.emailAddresses[0]?.emailAddress || '',\n          firstName: clerkUser.firstName || undefined,\n          lastName: clerkUser.lastName || undefined,\n          role: 'front-desk',\n          // Default fallback role\n          payloadUserId: undefined\n        });\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (clerkLoaded) {\n      fetchUserRole();\n    }\n  }, [clerkUser?.id, clerkLoaded]); // Use clerkUser.id instead of the whole object\n\n  const permissions = user?.role ? getRolePermissions(user.role) : null;\n  const contextValue: RoleContextType = {\n    user,\n    loading,\n    error,\n    permissions,\n    hasPermission: (permission: keyof RolePermissions) => hasPermission(user, permission),\n    hasRole: (roles: UserRole | UserRole[]) => hasRole(user, roles),\n    refreshUser: fetchUserRole\n  };\n  return <RoleContext.Provider value={contextValue} data-sentry-element=\"RoleContext.Provider\" data-sentry-component=\"RoleProvider\" data-sentry-source-file=\"role-context.tsx\">\n      {children}\n    </RoleContext.Provider>;\n}\n\n/**\n * Hook to access role context\n */\nexport function useRole(): RoleContextType {\n  const context = useContext(RoleContext);\n  if (context === undefined) {\n    throw new Error('useRole must be used within a RoleProvider');\n  }\n  return context;\n}\n\n/**\n * Hook for permission checking\n */\nexport function usePermission(permission: keyof RolePermissions): boolean {\n  const {\n    hasPermission\n  } = useRole();\n  return hasPermission(permission);\n}\n\n/**\n * Hook for role checking\n */\nexport function useHasRole(roles: UserRole | UserRole[]): boolean {\n  const {\n    hasRole\n  } = useRole();\n  return hasRole(roles);\n}\n\n/**\n * Component wrapper for conditional rendering based on permissions\n */\ninterface PermissionGateProps {\n  permission: keyof RolePermissions;\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\nexport function PermissionGate({\n  permission,\n  children,\n  fallback = null\n}: PermissionGateProps) {\n  const hasRequiredPermission = usePermission(permission);\n  return hasRequiredPermission ? <>{children}</> : <>{fallback}</>;\n}\n\n/**\n * Component wrapper for conditional rendering based on roles\n */\ninterface RoleGateProps {\n  roles: UserRole | UserRole[];\n  children: React.ReactNode;\n  fallback?: React.ReactNode;\n}\nexport function RoleGate({\n  roles,\n  children,\n  fallback = null\n}: RoleGateProps) {\n  const hasRequiredRole = useHasRole(roles);\n  return hasRequiredRole ? <>{children}</> : <>{fallback}</>;\n}\n\n/**\n * Higher-order component for role-based access control\n */\nexport function withRoleAccess<P extends object>(Component: React.ComponentType<P>, requiredRoles: UserRole | UserRole[], fallbackComponent?: React.ComponentType<P>) {\n  return function RoleProtectedComponent(props: P) {\n    const hasRequiredRole = useHasRole(requiredRoles);\n    if (!hasRequiredRole) {\n      if (fallbackComponent) {\n        const FallbackComponent = fallbackComponent;\n        return <FallbackComponent {...props} />;\n      }\n      return <div className=\"p-4 text-center text-muted-foreground\">\n          <p>You don't have permission to access this feature.</p>\n        </div>;\n    }\n    return <Component {...props} />;\n  };\n}\n\n/**\n * Loading component for role context\n */\nexport function RoleLoading() {\n  return <div className=\"flex items-center justify-center p-4\" data-sentry-component=\"RoleLoading\" data-sentry-source-file=\"role-context.tsx\">\n      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"></div>\n      <span className=\"ml-2 text-muted-foreground\">Loading user permissions...</span>\n    </div>;\n}", "'use client';\n\nimport * as React from 'react';\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\nimport { cn } from '@/lib/utils';\nfunction Separator({\n  className,\n  orientation = 'horizontal',\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return <SeparatorPrimitive.Root data-slot='separator-root' decorative={decorative} orientation={orientation} className={cn('bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px', className)} {...props} data-sentry-element=\"SeparatorPrimitive.Root\" data-sentry-component=\"Separator\" data-sentry-source-file=\"separator.tsx\" />;\n}\nexport { Separator };", "import(/* webpackMode: \"eager\", webpackExports: [\"Breadcrumbs\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\breadcrumbs.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\kbar\\\\index.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\app-sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ModeToggle\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\ThemeToggle\\\\theme-toggle.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"UserNav\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\layout\\\\user-nav.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\search-input.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ThemeSelector\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\theme-selector.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Separator\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\separator.tsx\");\n;\nimport(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\sidebar.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"RoleProvider\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\lib\\\\role-context.tsx\");\n", "'use client';\n\nimport { IconBrightness } from '@tabler/icons-react';\nimport { useTheme } from 'next-themes';\nimport * as React from 'react';\nimport { Button } from '@/components/ui/button';\nexport function ModeToggle() {\n  const {\n    setTheme,\n    resolvedTheme\n  } = useTheme();\n  const handleThemeToggle = React.useCallback((e?: React.MouseEvent) => {\n    const newMode = resolvedTheme === 'dark' ? 'light' : 'dark';\n    const root = document.documentElement;\n    if (!document.startViewTransition) {\n      setTheme(newMode);\n      return;\n    }\n\n    // Set coordinates from the click event\n    if (e) {\n      root.style.setProperty('--x', `${e.clientX}px`);\n      root.style.setProperty('--y', `${e.clientY}px`);\n    }\n    document.startViewTransition(() => {\n      setTheme(newMode);\n    });\n  }, [resolvedTheme, setTheme]);\n  return <Button variant='secondary' size='icon' className='group/toggle size-8' onClick={handleThemeToggle} data-sentry-element=\"Button\" data-sentry-component=\"ModeToggle\" data-sentry-source-file=\"theme-toggle.tsx\">\r\n      <IconBrightness data-sentry-element=\"IconBrightness\" data-sentry-source-file=\"theme-toggle.tsx\" />\r\n      <span className='sr-only'>Toggle theme</span>\r\n    </Button>;\n}", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { ChevronRight, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Breadcrumb({\n  ...props\n}: React.ComponentProps<'nav'>) {\n  return <nav aria-label='breadcrumb' data-slot='breadcrumb' {...props} data-sentry-component=\"Breadcrumb\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbList({\n  className,\n  ...props\n}: React.ComponentProps<'ol'>) {\n  return <ol data-slot='breadcrumb-list' className={cn('text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5', className)} {...props} data-sentry-component=\"BreadcrumbList\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbItem({\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='breadcrumb-item' className={cn('inline-flex items-center gap-1.5', className)} {...props} data-sentry-component=\"BreadcrumbItem\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbLink({\n  asChild,\n  className,\n  ...props\n}: React.ComponentProps<'a'> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'a';\n  return <Comp data-slot='breadcrumb-link' className={cn('hover:text-foreground transition-colors', className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"BreadcrumbLink\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbPage({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) {\n  return <span data-slot='breadcrumb-page' role='link' aria-disabled='true' aria-current='page' className={cn('text-foreground font-normal', className)} {...props} data-sentry-component=\"BreadcrumbPage\" data-sentry-source-file=\"breadcrumb.tsx\" />;\n}\nfunction BreadcrumbSeparator({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<'li'>) {\n  return <li data-slot='breadcrumb-separator' role='presentation' aria-hidden='true' className={cn('[&>svg]:size-3.5', className)} {...props} data-sentry-component=\"BreadcrumbSeparator\" data-sentry-source-file=\"breadcrumb.tsx\">\r\n      {children ?? <ChevronRight />}\r\n    </li>;\n}\nfunction BreadcrumbEllipsis({\n  className,\n  ...props\n}: React.ComponentProps<'span'>) {\n  return <span data-slot='breadcrumb-ellipsis' role='presentation' aria-hidden='true' className={cn('flex size-9 items-center justify-center', className)} {...props} data-sentry-component=\"BreadcrumbEllipsis\" data-sentry-source-file=\"breadcrumb.tsx\">\r\n      <MoreHorizontal className='size-4' data-sentry-element=\"MoreHorizontal\" data-sentry-source-file=\"breadcrumb.tsx\" />\r\n      <span className='sr-only'>More</span>\r\n    </span>;\n}\nexport { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator, BreadcrumbEllipsis };", "'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { useMemo } from 'react';\ntype BreadcrumbItem = {\n  title: string;\n  link: string;\n};\n\n// This allows to add custom title as well\nconst routeMapping: Record<string, BreadcrumbItem[]> = {\n  '/dashboard': [{\n    title: 'Dashboard',\n    link: '/dashboard'\n  }],\n  '/dashboard/employee': [{\n    title: 'Dashboard',\n    link: '/dashboard'\n  }, {\n    title: 'Employee',\n    link: '/dashboard/employee'\n  }],\n  '/dashboard/product': [{\n    title: 'Dashboard',\n    link: '/dashboard'\n  }, {\n    title: 'Product',\n    link: '/dashboard/product'\n  }]\n  // Add more custom mappings as needed\n};\nexport function useBreadcrumbs() {\n  const pathname = usePathname();\n  const breadcrumbs = useMemo(() => {\n    // Check if we have a custom mapping for this exact path\n    if (routeMapping[pathname]) {\n      return routeMapping[pathname];\n    }\n\n    // If no exact match, fall back to generating breadcrumbs from the path\n    const segments = pathname.split('/').filter(Boolean);\n    return segments.map((segment, index) => {\n      const path = `/${segments.slice(0, index + 1).join('/')}`;\n      return {\n        title: segment.charAt(0).toUpperCase() + segment.slice(1),\n        link: path\n      };\n    });\n  }, [pathname]);\n  return breadcrumbs;\n}", "'use client';\n\nimport { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';\nimport { useBreadcrumbs } from '@/hooks/use-breadcrumbs';\nimport { IconSlash } from '@tabler/icons-react';\nimport { Fragment } from 'react';\nexport function Breadcrumbs() {\n  const items = useBreadcrumbs();\n  if (items.length === 0) return null;\n  return <Breadcrumb data-sentry-element=\"Breadcrumb\" data-sentry-component=\"Breadcrumbs\" data-sentry-source-file=\"breadcrumbs.tsx\">\r\n      <BreadcrumbList data-sentry-element=\"BreadcrumbList\" data-sentry-source-file=\"breadcrumbs.tsx\">\r\n        {items.map((item, index) => <Fragment key={item.title}>\r\n            {index !== items.length - 1 && <BreadcrumbItem className='hidden md:block'>\r\n                <BreadcrumbLink href={item.link}>{item.title}</BreadcrumbLink>\r\n              </BreadcrumbItem>}\r\n            {index < items.length - 1 && <BreadcrumbSeparator className='hidden md:block'>\r\n                <IconSlash />\r\n              </BreadcrumbSeparator>}\r\n            {index === items.length - 1 && <BreadcrumbPage>{item.title}</BreadcrumbPage>}\r\n          </Fragment>)}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>;\n}"], "names": ["Avatar", "className", "props", "AvatarPrimitive", "data-slot", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "AvatarImage", "AvatarFallback", "DropdownMenu", "DropdownMenuPrimitive", "DropdownMenuTrigger", "DropdownMenuContent", "sideOffset", "DropdownMenuGroup", "DropdownMenuItem", "inset", "variant", "data-inset", "data-variant", "DropdownMenuCheckboxItem", "children", "checked", "span", "CheckIcon", "DropdownMenuLabel", "DropdownMenuSeparator", "Icons", "dashboard", "IconLayoutDashboard", "logo", "IconCommand", "login", "IconLogin", "close", "IconX", "product", "IconShoppingBag", "spinner", "IconLoader2", "kanban", "IconLayoutKanban", "chevronLeft", "IconChevronLeft", "chevronRight", "IconChevronRight", "trash", "IconTrash", "employee", "IconUserX", "post", "IconFileText", "page", "IconFile", "userPen", "IconUserEdit", "user2", "IconUserCircle", "media", "IconPhoto", "settings", "IconSettings", "billing", "IconCreditCard", "ellipsis", "IconDotsVertical", "add", "IconPlus", "warning", "IconAlertTriangle", "user", "IconUser", "arrowRight", "IconArrowRight", "help", "IconHelpCircle", "pizza", "IconPizza", "sun", "IconSun", "moon", "IconMoon", "laptop", "IconDeviceLaptop", "github", "IconBrandGithub", "twitter", "IconBrandTwitter", "check", "IconCheck", "calendar", "IconCalendar", "users", "IconUsers", "medical", "IconStethoscope", "SearchInput", "query", "useKBar", "div", "<PERSON><PERSON>", "onClick", "toggle", "IconSearch", "kbd", "UserAvatarProfile", "showInfo", "src", "imageUrl", "alt", "fullName", "slice", "toUpperCase", "emailAddresses", "emailAddress", "Label", "LabelPrimitive", "Input", "type", "input", "buttonVariants", "cva", "variants", "default", "destructive", "outline", "secondary", "ghost", "link", "size", "sm", "lg", "icon", "defaultVariants", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "Select", "SelectPrimitive", "SelectGroup", "SelectValue", "SelectTrigger", "data-size", "ChevronDownIcon", "SelectContent", "position", "SelectScrollUpButton", "SelectScrollDownButton", "SelectLabel", "SelectItem", "SelectSeparator", "ChevronUpIcon", "getRolePermissions", "role", "canCreatePatients", "canEditPatients", "canDeletePatients", "canViewMedicalNotes", "canEditMedicalNotes", "canCreateAppointments", "canEditAllAppointments", "canEditOwnAppointments", "canDeleteAppointments", "canCreateTreatments", "canEditTreatments", "canDeleteTreatments", "canManageUsers", "canViewAnalytics", "canViewFinancialData", "canCreateBills", "canEditBills", "canDeleteBills", "canViewAllBills", "canViewOwnBills", "canProcessPayments", "canViewPayments", "canGenerateReceipts", "canHandleRefunds", "canGenerateReports", "canViewDetailedFinancials", "canManagePaymentMethods", "canApplyDiscounts", "canApproveRefunds", "canViewSensitiveFinancials", "canExportFinancialData", "canBulkUpdateBills", "canOverridePaymentLimits", "canAccessAuditLogs", "canManageDeposits", "canProcessDepositRefunds", "canViewPatientFinancialHistory", "canModifyBillDueDates", "canWaiveFees", "canAccessAdvancedReports", "canManageBillingSettings", "canCreateDeposits", "canEditDeposits", "canDeleteDeposits", "canApplyDeposits", "canRefundDeposits", "canViewDepositHistory", "canViewDailyReports", "canViewMonthlyReports", "canViewYearlyReports", "canViewOutstandingReports", "canViewPaymentMethodReports", "canViewTreatmentRevenueReports", "canExportReports", "canScheduleReports", "hasPermission", "permission", "hasRole", "roles", "Array", "includes", "getRoleDisplayName", "getRoleBadgeColor", "navItems", "title", "url", "isActive", "shortcut", "items", "Sheet", "SheetPrimitive", "SheetPort<PERSON>", "SheetOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "side", "XIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SheetDescription", "Skeleton", "TooltipProvider", "delayDuration", "TooltipPrimitive", "<PERSON><PERSON><PERSON>", "TooltipTrigger", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SidebarContext", "React", "useSidebar", "context", "SidebarProvider", "defaultOpen", "open", "openProp", "onOpenChange", "setOpenProp", "style", "isMobile", "useIsMobile", "setIsMobile", "undefined", "mql", "window", "matchMedia", "MOBILE_BREAKPOINT", "onChange", "innerWidth", "addEventListener", "removeEventListener", "openMobile", "setOpenMobile", "_open", "_setOpen", "<PERSON><PERSON><PERSON>", "openState", "value", "document", "cookie", "SIDEBAR_COOKIE_NAME", "SIDEBAR_COOKIE_MAX_AGE", "toggleSidebar", "handleKeyDown", "event", "key", "metaKey", "ctrl<PERSON>ey", "preventDefault", "state", "contextValue", "Provider", "SIDEBAR_WIDTH", "SIDEBAR_WIDTH_ICON", "Sidebar", "collapsible", "data-sidebar", "data-mobile", "SIDEBAR_WIDTH_MOBILE", "data-state", "data-collapsible", "data-side", "SidebarTrigger", "PanelLeftIcon", "SidebarRail", "button", "aria-label", "tabIndex", "SidebarInset", "main", "SidebarInput", "SidebarHeader", "SidebarFooter", "SidebarSeparator", "Separator", "<PERSON>bar<PERSON><PERSON>nt", "SidebarGroup", "SidebarGroupLabel", "SidebarGroupAction", "SidebarGroupContent", "SidebarMenu", "ul", "SidebarMenuItem", "li", "sidebarMenuButtonVariants", "SidebarMenuButton", "tooltip", "data-active", "align", "hidden", "SidebarMenuAction", "showOnHover", "SidebarMenuBadge", "SidebarMenuSkeleton", "showIcon", "width", "Math", "floor", "random", "SidebarMenuSub", "SidebarMenuSubItem", "SidebarMenuSubButton", "ResultItem", "action", "active", "currentRootActionId", "ref", "ancestors", "index", "findIndex", "ancestor", "id", "length", "map", "name", "subtitle", "sc", "i", "RenderResults", "results", "rootActionId", "useMatches", "KBarResults", "onRender", "item", "displayName", "theme", "useThemeSwitching", "setTheme", "useTheme", "useRegisterActions", "themeAction", "section", "perform", "toggleTheme", "KBar", "router", "useRouter", "actions", "useMemo", "navigateTo", "push", "flatMap", "navItem", "baseAction", "toLowerCase", "keywords", "childActions", "childItem", "KBarProvider", "KBarComponent", "KBarPortal", "KBarPositioner", "KBarAnimator", "KBarSearch", "UserNav", "useUser", "forceMount", "p", "SignOutButton", "redirectUrl", "Collapsible", "CollapsiblePrimitive", "CollapsibleTrigger", "Collapsible<PERSON><PERSON>nt", "OrgSwitcher", "tenants", "defaultTenant", "onTenantSwitch", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedTenant", "handleTenantSwitch", "tenant", "GalleryVerticalEnd", "ChevronsUpDown", "onSelect", "Check", "IconPhotoUp", "AppSidebar", "pathname", "usePathname", "isOpen", "useMediaQuery", "setIsOpen", "useState", "roleUser", "useRole", "activeTenant", "filteredNavItems", "userRole", "filter", "_tenantId", "handleSwitchTenant", "Icon", "subItem", "Link", "href", "IconChevronsDown", "IconBell", "IconLogout", "CtaGithub", "a", "rel", "target", "Header", "header", "orientation", "Breadcrumbs", "ModeToggle", "ThemeSelector", "metadata", "description", "DashboardLayout", "cookieStore", "cookies", "get", "_jsx", "Role<PERSON>rovider", "_jsxs", "DEFAULT_THEMES", "SCALED_THEMES", "MONO_THEMES", "activeTheme", "setActiveTheme", "useThemeConfig", "htmlFor", "onValueChange", "placeholder", "RoleContext", "createContext", "clerkUser", "isLoaded", "clerkLoaded", "setUser", "loading", "setLoading", "error", "setError", "fetchUserRole", "response", "fetch", "method", "headers", "ok", "data", "json", "authenticatedUser", "clerkId", "email", "firstName", "lastName", "payloadUserId", "err", "console", "Error", "message", "permissions", "refreshUser", "useContext", "PermissionGate", "fallback", "hasRequiredPermission", "usePermission", "RoleGate", "hasRequiredRole", "useHasRole", "decorative", "SeparatorPrimitive", "resolvedTheme", "handleThemeToggle", "newMode", "root", "documentElement", "startViewTransition", "e", "setProperty", "clientX", "clientY", "IconBrightness", "Breadcrumb", "nav", "BreadcrumbList", "ol", "BreadcrumbItem", "BreadcrumbLink", "BreadcrumbPage", "aria-disabled", "aria-current", "BreadcrumbSeparator", "aria-hidden", "ChevronRight", "routeMapping", "useBreadcrumbs", "breadcrumbs", "segments", "split", "Boolean", "segment", "path", "join", "char<PERSON>t", "Fragment", "IconSlash"], "sourceRoot": ""}