{"version": 3, "file": "../app/dashboard/product/page.js", "mappings": "ubAAA,yMCKA,SAASA,EAAQ,CACf,GAAGC,EACgD,EACnD,MAAO,UAACC,EAAAA,EAAqB,EAACC,YAAU,UAAW,GAAGF,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,UAAUC,0BAAwB,eACnK,CACA,SAASC,EAAe,CACtB,GAAGN,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAwB,EAACC,YAAU,kBAAmB,GAAGF,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,iBAAiBC,0BAAwB,eACxL,CACA,SAASE,EAAe,CACtBC,WAAS,OACTC,EAAQ,QAAQ,YAChBC,EAAa,CAAC,CACd,GAAGV,EACmD,EACtD,MAAO,UAACC,EAAAA,EAAuB,EAACE,sBAAoB,0BAA0BC,wBAAsB,iBAAiBC,0BAAwB,uBACzI,UAACJ,EAAAA,EAAwB,EAACC,YAAU,kBAAkBO,MAAOA,EAAOC,WAAYA,EAAYF,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,ieAAkeH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,2BAA2BE,0BAAwB,iBAEhrB,iRCxBA,yICIA,IAAMO,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACbb,CAAS,SACTO,CAAO,SACPO,GAAU,CAAK,CACf,GAAGtB,EAGJ,EACC,IAAMuB,EAAOD,EAAUE,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKrB,YAAU,QAAQM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACC,EAAc,SACzDG,CACF,GAAIP,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,sJCCA,SAASoB,EAAS,WAChBjB,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAAC0B,MAAAA,CAAIxB,YAAU,WAAWM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,WAAWC,0BAAwB,gBACvK,0BCNA,kECAA,2GCAA,qDCAA,gDCAA,oDCAA,kDCAA,gDCAA,wGCAA,+DCAA,mDCAA,iECAA,6FCIO,IAAMsB,EAAkC,CAAC,OAC9CC,CAAK,aACLC,CAAW,CACZ,GACQ,WAACH,MAAAA,CAAItB,wBAAsB,UAAUC,0BAAwB,wBAChE,UAACyB,KAAAA,CAAGtB,UAAU,6CAAqCoB,IACnD,UAACG,IAAAA,CAAEvB,UAAU,yCAAiCqB,8BCVpD,8hBCAA,4CAA4Q,CAE5Q,uCAAiK,CAEjK,uCAA8J,CAE9J,uCAAmN,CAEnN,uCAA0L,CAE1L,uCAA6L,yBCV7L,sDCAA,gHCOO,IAAMG,EAAQ,GACnB,IAAIC,QAAQ,GAAaC,WAAWC,EAASC,IAelCC,CAfuC,CAexB,CAC1BC,QAAS,EAAE,CAGXC,aACE,IAAMC,EAA4B,EAAE,CA4BpC,IAAK,IAAIC,EAAI,EAAGA,GAAK,GAAIA,IAAK,OAC5BD,EAAeE,IAAI,CAhBZ,CACLC,EAAAA,CAb+BA,EAAU,EAczCC,KAAMC,EAAAA,CAAKA,CAACC,QAAQ,CAACC,WAAW,GAChClB,YAAagB,EAAAA,CAAKA,CAACC,QAAQ,CAACE,kBAAkB,GAC9CC,WAAYJ,EAAAA,CAAKA,CAACK,IAAI,CACnBC,OAAO,CAAC,CAAEC,KAAM,aAAcC,GAAI,YAAa,GAC/CC,WAAW,GACdC,MAAOC,WAAWX,EAAAA,CAAKA,CAACC,QAAQ,CAACS,KAAK,CAAC,CAAEE,IAAK,EAAGC,IAAK,IAAKC,IAAK,CAAE,IAClEC,UAAW,CAAC,oDAAoD,EAAEjB,EAAG,IAAI,CAAC,CAC1EkB,SAAUhB,EAAAA,CAAKA,CAACiB,OAAO,CAACC,YAAY,CApBnB,CACjB,cACA,YACA,WACA,OACA,YACA,QACA,UACA,kBACD,EAYCC,WAAYnB,EAAAA,CAAKA,CAACK,IAAI,CAACe,MAAM,GAAGX,WAAW,EAC7C,EAK8Cb,CAGhD,IAAI,CAACH,OAAO,CAAGE,CACjB,EAGA,MAAM0B,OAAO,YACXC,EAAa,EAAE,CACfC,QAAM,CAIP,EACC,IAAIC,EAAW,IAAI,IAAI,CAAC/B,OAAO,CAAC,CAgBhC,OAbI6B,EAAWG,MAAM,CAAG,GAAG,CACzBD,EAAWA,EAASE,MAAM,CAAC,GACzBJ,EAAWK,QAAQ,CAACC,EAAQZ,QAAQ,IAKpCO,IACFC,EAAWK,CAAAA,CADD,CACCA,EAAAA,EAAAA,CAAWA,CAACL,EAAUD,EAAQ,CACvCO,KAAM,CAAC,OAAQ,cAAe,WAAW,EAC3C,EAGKN,CACT,EAGA,MAAMO,YAAY,MAChBC,EAAO,CAAC,OACRC,EAAQ,EAAE,YACVX,CAAU,QACVC,CAAM,CAMP,EACC,MAAMpC,EAAM,KACZ,IAAM+C,EAAkBZ,EAAaA,EAAWa,KAAK,CAAC,KAAO,EAAE,CACzDC,EAAc,MAAM,IAAI,CAACf,MAAM,CAAC,CACpCC,WAAYY,SACZX,CACF,GACMc,EAAgBD,EAAYX,MAAM,CAGlCa,EAAUN,IAAO,EAAKC,EACtBM,EAAoBH,EAAYI,KAAK,CAACF,EAAQA,EAASL,GAM7D,MAAO,CACLQ,SAAS,EACTC,KALkB,CAKZC,GALgBC,OAAOnC,WAAW,GAMxCoC,QAAS,gDACTC,eAAgBT,EAChBC,eACAL,EACAT,SAAUe,CACZ,CACF,EAGA,MAAMQ,eAAejD,CAAU,EAC7B,MAAMX,EAAM,KAGZ,EAHmB,EAGbyC,EAAU,IAAI,CAACnC,OAAO,CAACuD,EAHS,EAGL,CAAC,GAAapB,EAAQ9B,EAAE,GAAKA,UAEzD8B,EAUE,CACLa,CAXE,KAAU,GAWH,EACTC,KAJkB,CAIZC,GAJgBC,OAAOnC,WAAW,GAKxCoC,QAAS,CAAC,gBAAgB,EAAE/C,EAAG,MAAM,CAAC,SACtC8B,CACF,EAdS,CACLa,SAAS,EACTI,QAAS,CAAC,gBAAgB,EAAE/C,EAAG,UAAU,CAAC,CAahD,CACF,EAAE,EAGWJ,UAAU,2BC5JvB,ghBCAA,+DCAA,2CAA4Q,CAE5Q,uCAAiK,CAEjK,sCAA8J,CAE9J,uCAAmN,CAEnN,uCAA0L,CAE1L,uCAA6L,yBCV7L,oDCAA,kECAA,yDCAA,yHCiCM,MAAS,OAAiB,UA9BI,CA8BM,CA5BtC,OACA,CACE,CAAG,kGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAwC,0CAAK,SAAU,EACrE,CACE,OACA,CACE,CAAG,gGACH,GAAK,SACP,EACF,CACA,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC7C,uCCTO,SAASuD,EAAqC,QACnDC,CAAM,OACNnE,CAAK,WACLpB,CAAS,CACT,GAAGR,EACuC,SAC1C,EAAYgG,UAAU,IAAOD,EAAD,UAAkB,GAGvC,CAH2C,EAG3C,QAACE,EAAAA,EAAYA,CAAAA,CAAC9F,sBAAoB,eAAeC,wBAAsB,wBAAwBC,0BAAwB,yCAC1H,WAAC6F,EAAAA,EAAmBA,CAAAA,CAAC1F,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yNAA0NH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,sBAAsBE,0BAAwB,yCACnVuB,EACAmE,EAAOC,UAAU,KAAgC,CAAzBD,QAAAA,EAAOI,WAAW,GAAgB,UAACC,EAAAA,GAAeA,CAAAA,CAAAA,GAA+B,QAAzBL,EAAOI,WAAW,GAAe,UAACE,EAAAA,GAAaA,CAAAA,CAAAA,GAAM,UAACC,EAAAA,GAAaA,CAAAA,CAAAA,EAAAA,CAAE,IAExJ,WAACC,EAAAA,EAAmBA,CAAAA,CAAC9F,MAAM,QAAQD,UAAU,OAAOL,sBAAoB,sBAAsBE,0BAAwB,yCACnH0F,EAAOC,UAAU,IAAM,iCACpB,WAACQ,EAAAA,EAAwBA,CAAAA,CAAChG,UAAU,+GAA+GiG,QAAkC,QAAzBV,EAAOI,WAAW,GAAcO,QAAS,IAAMX,EAAOY,aAAa,EAAC,aAC9N,UAACN,EAAAA,GAAaA,CAAAA,CAAAA,GAAG,SAGnB,WAACG,EAAAA,EAAwBA,CAAAA,CAAChG,UAAU,+GAA+GiG,QAASV,WAAOI,WAAW,GAAeO,QAAS,IAAMX,EAAOY,aAAa,EAAC,aAC/N,UAACP,EAAAA,GAAeA,CAAAA,CAAAA,GAAG,UAGpBL,EAAOI,WAAW,IAAM,WAACS,EAAAA,EAAgBA,CAAAA,CAACpG,UAAU,qCAAqCkG,QAAS,IAAMX,EAAOc,YAAY,aACxH,UAACC,EAAAA,GAAUA,CAAAA,CAAAA,GAAG,cAIrBf,EAAOgB,UAAU,IAAM,WAACP,EAAAA,EAAwBA,CAAAA,CAAChG,UAAU,+GAA+GiG,QAAS,CAACV,EAAOiB,YAAY,GAAIN,QAAS,IAAMX,EAAOkB,gBAAgB,EAAC,aAC/O,UAACC,EAAMA,CAAAA,GAAAA,gBAvBR,UAACxF,MAAAA,CAAIlB,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACH,YAAaoB,GA4B3C,CC1BM,MAAO,OAAiB,QAjBM,CAClC,CAAC,MAAQ,EAAE,EAAG,CAAa,CAgBmB,UAhBnB,IAAK,SAAU,EAC1C,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC3C,CAAC,MAAQ,EAAE,EAAG,CAAc,gBAAK,SAAU,EAC7C,ECYM,EAAc,OAAiB,GAAjB,YAhBgB,CAgBgB,CAfjD,QAAU,EAAE,EAAI,MAAM,CAAI,CAeiC,CAfjC,KAAM,CAAG,MAAM,GAAK,UAAU,EACzD,CAAC,MAAQ,EAAE,EAAG,CAAiB,mBAAK,SAAU,EAChD,6DCIO,IAAMuF,EAA8B,CAAC,OAC1CvF,CAAK,aACLC,CAAW,CACXuF,QAAM,CACNC,SAAO,UACPC,CAAQ,CACT,GAMQ,UAACC,EAAAA,EAAMA,CAAAA,CAACC,KAAMJ,EAAQK,aALXD,CAKyBE,GAJrC,GACFL,GAEJ,EACqDlH,sBAAoB,SAASC,wBAAsB,QAAQC,0BAAwB,qBACpI,WAACsH,EAAAA,EAAaA,CAAAA,CAACxH,sBAAoB,gBAAgBE,0BAAwB,sBACzE,WAACuH,EAAAA,EAAYA,CAAAA,CAACzH,sBAAoB,eAAeE,0BAAwB,sBACvE,UAACwH,EAAAA,EAAWA,CAAAA,CAAC1H,sBAAoB,cAAcE,0BAAwB,qBAAauB,IACpF,UAACkG,EAAAA,EAAiBA,CAAAA,CAAC3H,sBAAoB,oBAAoBE,0BAAwB,qBAAawB,OAElG,UAACH,MAAAA,UAAK4F,SCjBDS,EAAwC,CAAC,CACpDX,QAAM,CACNC,SAAO,WACPW,CAAS,SACTC,CAAO,CACR,IACC,GAAM,CAACC,EAAWC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,UAI3C,CAHAC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRF,GAAa,EACf,EAAG,EAAE,EACAD,GAGE,UAACf,EAAKA,CAACvF,EAADuF,IAAO,gBAAgBtF,YAAY,gCAAgCuF,OAAQA,EAAQC,QAASA,EAASlH,sBAAoB,QAAQC,wBAAsB,aAAaC,0BAAwB,2BACrM,WAACqB,MAAAA,CAAIlB,UAAU,gEACb,UAAC8H,EAAAA,CAAMA,CAAAA,CAACC,SAAUN,EAASlH,QAAQ,UAAU2F,QAASW,EAASlH,sBAAoB,SAASE,0BAAwB,2BAAkB,WAGtI,UAACiI,EAAAA,CAAMA,CAAAA,CAACC,SAAUN,EAASlH,QAAQ,cAAc2F,QAASsB,EAAW7H,sBAAoB,SAASE,0BAAwB,2BAAkB,kBAPzI,IAYX,EAAE,8CCtBK,IAAMmI,EAAwC,CAAC,MACpDC,CAAI,CACL,IACC,GAAM,CAACR,EAAQ,CAAGG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACrB,CAACZ,EAAMkB,EAAQ,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC3BO,EAASC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,GAClBZ,EAAY,UAAa,EAC/B,MAAO,iCACH,UAACD,EAAUA,CAACX,OAADW,EAAeV,QAAS,IAAMqB,GAAQ,GAAQV,UAAWA,EAAWC,QAASA,EAAS9H,sBAAoB,aAAaE,0BAAwB,oBAC1J,WAAC4F,EAAAA,EAAYA,CAAAA,CAAC4C,OAAO,EAAO1I,sBAAoB,eAAeE,0BAAwB,4BACrF,UAAC6F,EAAAA,EAAmBA,CAAAA,CAAC5E,OAAO,IAACnB,sBAAoB,sBAAsBE,0BAAwB,2BAC7F,WAACiI,EAAAA,CAAMA,CAAAA,CAACvH,QAAQ,QAAQP,UAAU,cAAcL,sBAAoB,SAASE,0BAAwB,4BACnG,UAACyI,OAAAA,CAAKtI,UAAU,mBAAU,cAC1B,UAACuI,EAAAA,CAAgBA,CAAAA,CAACvI,UAAU,UAAUL,sBAAoB,mBAAmBE,0BAAwB,yBAGzG,WAACkG,EAAAA,EAAmBA,CAAAA,CAAC9F,MAAM,MAAMN,sBAAoB,sBAAsBE,0BAAwB,4BACjG,UAAC2I,EAAAA,EAAiBA,CAAAA,CAAC7I,sBAAoB,oBAAoBE,0BAAwB,2BAAkB,YAErG,WAACuG,EAAAA,EAAgBA,CAAAA,CAACF,QAAS,IAAMiC,EAAOjG,IAAI,CAAC,CAAC,mBAAmB,EAAE+F,EAAK9F,EAAE,EAAE,EAAGxC,sBAAoB,mBAAmBE,0BAAwB,4BAC5I,UAAC4I,EAAAA,CAAQA,CAAAA,CAACzI,UAAU,eAAeL,sBAAoB,WAAWE,0BAAwB,oBAAoB,aAEhH,WAACuG,EAAAA,EAAgBA,CAAAA,CAACF,QAAS,IAAMgC,GAAQ,GAAOvI,sBAAoB,mBAAmBE,0BAAwB,4BAC7G,UAAC6I,EAAAA,CAASA,CAAAA,CAAC1I,UAAU,eAAeL,sBAAoB,YAAYE,0BAAwB,oBAAoB,qBAK5H,EAAE,EE9B2C,CAAC,CAC5C8I,YAAa,YACbC,OAAQ,QACRC,KAAM,CAAC,KACLC,CAAG,CACJ,GACQ,UAAC5H,MAAAA,CAAIlB,UAAU,kCAChB,UAAC+I,EAAAA,OAAKA,CAAAA,CAACC,IAAKF,EAAIG,QAAQ,CAAC,aAAcC,IAAKJ,EAAIG,QAAQ,CAAC,QAASE,IAAI,IAACnJ,UAAU,gBAG3F,EAAG,CACDmC,GAAI,OACJwG,YAAa,OACbC,OAAQ,CAAC,QACPrD,CAAM,CAGP,GAAK,UAACD,EAAqBA,CAACC,OAAQA,EAAQnE,MAAM,GAAvBkE,MAC5BuD,KAAM,CAAC,MACLA,CAAI,CACL,GAAK,UAAC3H,MAAAA,UAAK2H,EAAKI,QAAQ,KACzBG,KAAM,CACJC,MAAO,OACPC,YAAa,qBACb/I,QAAS,OACTgJ,KAAMC,CACR,EACAC,CAFYD,mBAEQ,CACtB,EAAG,CACDrH,GAAI,WACJwG,YAAa,WACbC,OAAQ,CAAC,QACPrD,CAAM,CAGP,GAAK,UAACD,EAAqBA,CAACC,OAAQA,EAAQnE,MAAM,GAAvBkE,UAC5BuD,KAAM,CAAC,MACLA,CAAI,CACL,IACC,IAAMa,EAASb,EAAKI,QAAQ,GACtBU,EAAkB,WAAXD,EAAsBE,EAAeC,EAAAA,CAAOA,CACzD,KAD+CD,CACxC,WAAC/I,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUP,UAAU,uBACpC,UAAC2J,EAAAA,CAAAA,GACAD,IAET,EACAD,oBAAoB,EACpBL,KAAM,CACJC,MAAO,aACP9I,QAAS,cACTuJ,QD5D4B,CC4DnBC,CD3DXC,MAAO,SC2DoBD,KD1D3BV,MAAO,aACT,EAAG,CACDW,MAAO,YACPX,MAAO,WACT,EAAG,CACDW,MAAO,WACPX,MAAO,UACT,EAAG,CACDW,MAAO,OACPX,MAAO,MACT,EAAG,CACDW,MAAO,YACPX,MAAO,WACT,EAAG,CACDW,MAAO,QACPX,MAAO,OACT,EAAG,CACDW,MAAO,UACPX,MAAO,SACT,EAAG,CACDW,MAAO,kBACPX,MAAO,iBACT,ECqCE,CDrCC,ECsCA,CACDV,YAAa,QACbC,OAAQ,OACV,EAAG,CACDD,YAAa,cACbC,OAAQ,aACV,EAAG,CACDzG,GAAI,UACJ0G,KAAM,CAAC,KACLC,CAAG,CACJ,GAAK,UAACd,EAAUA,CAACC,KAAMa,EAAPd,QAAmB,EACtC,EAAE,CAAC,mKCnEH,SAASjB,EAAO,CACd,GAAGvH,EAC+C,EAClD,MAAO,UAACyK,EAAAA,EAAoB,EAACvK,YAAU,SAAU,GAAGF,CAAK,CAAEG,sBAAoB,uBAAuBC,wBAAsB,SAASC,0BAAwB,cAC/J,CACA,SAASqK,EAAc,CACrB,GAAG1K,EACkD,EACrD,MAAO,UAACyK,EAAAA,EAAuB,EAACvK,YAAU,iBAAkB,GAAGF,CAAK,CAAEG,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACpL,CACA,SAASsK,EAAa,CACpB,GAAG3K,EACiD,EACpD,MAAO,UAACyK,EAAAA,EAAsB,EAACvK,YAAU,gBAAiB,GAAGF,CAAK,CAAEG,sBAAoB,yBAAyBC,wBAAsB,eAAeC,0BAAwB,cAChL,CAMA,SAASuK,EAAc,WACrBpK,CAAS,CACT,GAAGR,EACkD,EACrD,MAAO,UAACyK,EAAAA,EAAuB,EAACvK,YAAU,iBAAiBM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yJAA0JH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,0BAA0BC,wBAAsB,gBAAgBC,0BAAwB,cACxW,CACA,SAASsH,EAAc,WACrBnH,CAAS,UACT8G,CAAQ,CACR,GAAGtH,EACkD,EACrD,MAAO,WAAC2K,EAAAA,CAAazK,YAAU,gBAAgBC,sBAAoB,eAAeC,wBAAsB,gBAAgBC,0BAAwB,uBAC5I,UAACuK,EAAAA,CAAczK,sBAAoB,gBAAgBE,0BAAwB,eAC3E,WAACoK,EAAAA,EAAuB,EAACvK,YAAU,iBAAiBM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,8WAA+WH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,0BAA0BE,0BAAwB,uBAC3gBiH,EACD,WAACmD,EAAAA,EAAqB,EAACjK,UAAU,oWAAoWL,sBAAoB,wBAAwBE,0BAAwB,uBACvc,UAACwK,EAAAA,CAAKA,CAAAA,CAAC1K,sBAAoB,QAAQE,0BAAwB,eAC3D,UAACyI,OAAAA,CAAKtI,UAAU,mBAAU,kBAIpC,CACA,SAASoH,EAAa,WACpBpH,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAAC0B,MAAAA,CAAIxB,YAAU,gBAAgBM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,+CAAgDH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,cAC1L,CACA,SAASyK,EAAa,WACpBtK,CAAS,CACT,GAAGR,EACyB,EAC5B,MAAO,UAAC0B,MAAAA,CAAIxB,YAAU,gBAAgBM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,yDAA0DH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,eAAeC,0BAAwB,cACpM,CACA,SAASwH,EAAY,WACnBrH,CAAS,CACT,GAAGR,EACgD,EACnD,MAAO,UAACyK,EAAAA,EAAqB,EAACvK,YAAU,eAAeM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,wBAAwBC,wBAAsB,cAAcC,0BAAwB,cAC5O,CACA,SAASyH,EAAkB,WACzBtH,CAAS,CACT,GAAGR,EACsD,EACzD,MAAO,UAACyK,EAAAA,EAA2B,EAACvK,YAAU,qBAAqBM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,8BAA8BC,wBAAsB,oBAAoBC,0BAAwB,cAC/P,0BCvEA,uMCKA,SAAS0K,EAAW,WAClBvK,CAAS,UACT8G,CAAQ,CACR,GAAGtH,EACmD,EACtD,MAAO,WAACgL,EAAAA,EAAwB,EAAC9K,YAAU,cAAcM,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYH,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,2BAA2BC,wBAAsB,aAAaC,0BAAwB,4BAChN,UAAC2K,EAAAA,EAA4B,EAAC9K,YAAU,uBAAuBM,UAAU,qJAAqJL,sBAAoB,+BAA+BE,0BAAwB,2BACtSiH,IAEH,UAAC2D,EAAAA,CAAU9K,sBAAoB,YAAYE,0BAAwB,oBACnE,UAAC2K,EAAAA,EAA0B,EAAC7K,sBAAoB,6BAA6BE,0BAAwB,sBAE3G,CACA,SAAS4K,EAAU,WACjBzK,CAAS,CACT0K,cAAc,UAAU,CACxB,GAAGlL,EACkE,EACrE,MAAO,UAACgL,EAAAA,EAAuC,EAAC9K,YAAU,wBAAwBgL,YAAaA,EAAa1K,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsDuK,gBAA8B,6CAA8CA,kBAAgC,+CAAgD1K,GAAa,GAAGR,CAAK,CAAEG,sBAAoB,0CAA0CC,wBAAsB,YAAYC,0BAAwB,2BACvd,UAAC2K,EAAAA,EAAmC,EAAC9K,YAAU,oBAAoBM,UAAU,yCAAyCL,sBAAoB,sCAAsCE,0BAAwB,qBAE9M,88GE1BA,+DOmBI,sBAAsB,yMNPnB,SAAS8K,EAAkB,aAChCC,CAAW,UACXC,EAAW,EAAE,aACbC,EAAc,CAAC,YACfC,EAAa,CAAC,OAAO,iBACrBC,GAAkB,CAAI,gBACtBC,EAAiB,EAAI,YACrBC,GAAa,CAAK,WAClBlL,CAAS,CACT,GAAGR,EACoB,EACvB,IAAM2L,EAAiBC,MAAMxI,IAAI,CAAC,CAChCkB,OAAQ8G,CACV,EAAG,CAACS,EAAGC,IAAUP,CAAU,CAACO,EAAQP,EAAWjH,MAAM,CAAC,EAAI,QAC1D,MAAO,WAAC5C,MAAAA,CAAIlB,UAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iCAAkCH,GAAa,GAAGR,CAAK,CAAEI,wBAAsB,oBAAoBC,0BAAwB,oCACjJ,WAACqB,MAAAA,CAAIlB,UAAU,6EACb,UAACkB,MAAAA,CAAIlB,UAAU,0CACZ8K,EAAc,EAAIM,MAAMxI,IAAI,CAAC,CAC9BkB,OAAQgH,CACV,GAAGS,GAAG,CAAC,CAACF,EAAGpJ,IAAM,UAAChB,EAAAA,CAAQA,CAAAA,CAASjB,UAAU,gCAAbiC,IAAkD,OAEjF+I,EAAkB,UAAC/J,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,0CAA6C,QAGtF,UAACkB,MAAAA,CAAIlB,UAAU,oCACb,WAACwL,EAAAA,KAAKA,CAAAA,CAAC7L,sBAAoB,QAAQE,0BAAwB,oCACzD,UAAC4L,EAAAA,WAAWA,CAAAA,CAAC9L,sBAAoB,cAAcE,0BAAwB,mCACpEuL,MAAMxI,IAAI,CAAC,CACZkB,OAAQ,CACV,GAAGyH,GAAG,CAAC,CAACF,EAAGpJ,IAAM,UAACyJ,EAAAA,QAAQA,CAAAA,CAAS1L,UAAU,gCACtCoL,MAAMxI,IAAI,CAAC,CACdkB,OAAQ8G,CACV,GAAGW,GAAG,CAAC,CAACF,EAAGM,IAAM,UAACC,EAAAA,SAASA,CAAAA,CAASC,MAAO,CACzCC,MAAOX,CAAc,CAACQ,EAAE,CACxBI,SAAUb,EAAaC,CAAc,CAACQ,EAAE,CAAG,MAC7C,WACQ,UAAC1K,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,gBAJK2L,KAHH1J,MAWhC,UAAC+J,EAAAA,SAASA,CAAAA,CAACrM,sBAAoB,YAAYE,0BAAwB,mCAChEuL,MAAMxI,IAAI,CAAC,CACZkB,OAAQ+G,CACV,GAAGU,GAAG,CAAC,CAACF,EAAGpJ,IAAM,UAACyJ,EAAAA,QAAQA,CAAAA,CAAS1L,UAAU,gCACtCoL,MAAMxI,IAAI,CAAC,CACdkB,OAAQ8G,CACV,GAAGW,GAAG,CAAC,CAACF,EAAGM,IAAM,UAACM,EAAAA,SAASA,CAAAA,CAASJ,MAAO,CACzCC,MAAOX,CAAc,CAACQ,EAAE,CACxBI,SAAUb,EAAaC,CAAc,CAACQ,EAAE,CAAG,MAC7C,WACQ,UAAC1K,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,gBAJK2L,KAHH1J,WAanCgJ,EAAiB,WAAC/J,MAAAA,CAAIlB,UAAU,sFAC7B,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,sBACpB,WAACkB,MAAAA,CAAIlB,UAAU,sDACb,WAACkB,MAAAA,CAAIlB,UAAU,oCACb,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,aACpB,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,sBAEtB,UAACkB,MAAAA,CAAIlB,UAAU,gEACb,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,eAEtB,WAACkB,MAAAA,CAAIlB,UAAU,oCACb,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,2BACpB,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,WACpB,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,WACpB,UAACiB,EAAAA,CAAQA,CAAAA,CAACjB,UAAU,oCAGjB,OAEjB,2BClFA,GACA,0EACA,4DACA,uKACA,+IACA,mGACA,6EACA,2EACA,EACA,cACA,gBAAmB;AACnB,kCAAkC,EAAK,EA8DvC,sBA4DA,aAQA,IACA,oCACA,SAEA,+BACA,0BACA,kCAEA,GADA,2BACA,GACA,QAEA,CAAI,SAKJ,OAJA,cACA,6DACA,GAEA,EACA,CAEA,OADA,mCACA,gBACA,IAkBA,cACA,cACA,cACA,YAEA,SACA,qBACA,iBACA,YAEA,OAKA,MAHA,oBACA,MA5BA,gBACA,IACA,WACA,CAAI,SAOJ,OANA,SAlCA,QACA,GAGA,oBACA,EA8BA,4CACA,EACA,EACA,GAEA,IACA,CACA,EAkBA,UACA,CACA,OACA,gBACA,KACA,kBACA,eACA,OACA,QACA,eACA,mBACA,OAEA,CACA,CAAK,CACL,eACA,OACA,QACA,KAEA,CACA,CACA,CACA,SACA,WACA,gBAAuB,EAAE,EACxB,EACD,KACA,UACA,yBACA,gBACA,KAEA,CACA,CAAG,CACH,oCACA,CAAC,EAsCD,gBACA,gCACA,CAvCA,GACA,UACA,wBACA,SACA,KAEA,GACA,CAAG,CACH,6BACA,CAAC,EACD,GACA,UACA,4BACA,gBACA,KAEA,CACA,CAAG,CACH,cACA,iCACA,0CACA,CACA,CAAC,EACD,GACA,UACA,2BACA,gBACA,KAEA,CACA,CAAG,CACH,yBACA,CAAC,EACD,GACA,oBACA,6BACA,CAAC,EAID,GACA,UACA,yBACA,gBACA,KAEA,WACA,CAAG,CACH,oCACA,IACA,CAAC,EACD,GACA,UACA,yBACA,0BACA,KAEA,CACA,CAAG,CACH,6BACA,IACA,CAAC,EACD,GACA,UACA,qCACA,0BACA,KAEA,CACA,CAAG,CACH,yCACA,IACA,CAAC,EC7SM,IAAMkM,EAAe,CAC1B7H,KAAM8H,EAAeC,WAAW,CAAZD,GACpBE,QAASF,EAAeC,WAAW,CAAZD,IACvB/J,KAAMkK,EACNC,OAAQD,EACRjJ,EAFmBiJ,OAETA,CAIZ,CALuBA,CAKrB,ED+DF,QCnEyBA,CDmEzB,WAA4C,MAAe,EAAI,EAC/D,MA5DA,oBAAiC,MAAe,EAAI,EAcpD,OAbA,cACA,wBACA,uBAEA,eAWA,GACA,IACA,wBACA,SACA,wCAEA,2BAGA,oBACA,sBAEA,gCACA,SAEA,uBACA,wBACA,sBACA,iBACA,oBACA,eACA,mBAEU,YACV,WAGA,QACA,CACA,uBACA,qCACA,+BAEA,6BACA,CACA,CAAI,SAEJ,CACA,0BACA,EAlDA,GACA,KACA,kCACA,cACA,UACA,kCACA,CACA,QACA,CAEA,EA6CA,WAAuC,EAAS,EAChD,EAAmB,OAAW,OAC9B,eACA,EAAG,EACH,cACA,UACA,oCACA,kBAmCA,KACA,SACA,SAEA,iDACA,SAEA,eACA,eACA,SAGA,QACA,EAhDA,QACA,UAEA,oBACA,CAGA,OAFA,oBACA,OACA,6BACA,CAOA,aACA,iBAAY,GAAe,IAC3B,6BACA,oBAEA,QACA,CAYA,OAAW,MAxBX,mBACA,qBACA,UAEA,IACA,EAmBW,IAXX,YACA,iBAAY,GAAe,IAC3B,OACA,cACA,YACA;AACA,WAAW,UAAY,IAGvB,QACA,EACW,MACX,ECvGyDJ,IDwZzD,YACA,ICxZyCM,WDwZzC,KACA,aACA,CAAE,EAAI,EA4BN,ECtb0CN,cAAc,eCbzC,eAAeO,EAAmB,EAAsB,EAErE,IAAMpI,EAAOqI,EAAkBC,GAAG,CAAC,QAC7B/I,EAAS8I,CADeA,CACGC,GAAG,CAAC,QAC/BC,EAAYF,CADcA,CACIC,GAAG,CAAC,WAALD,EAChBA,EAAkBC,GAAG,CAAC,WAALD,CAC9BG,EAAU,MACdxI,EACAC,MAAOsI,EACP,GAAIhJ,GAAU,CACZA,QACF,CAAC,CACD,GAAID,GAAc,CAChBA,WAAYA,CACd,CAAC,EAEGsE,EAAO,MAAMpG,EAAAA,CAAYA,CAACuC,WAAW,CAACyI,GACtCnI,EAAgBuD,EAAK9C,cAAc,CACnCtB,EAAsBoE,EAAKpE,QAAQ,CACzC,MAAO,UAACiJ,EAAAA,YAAYA,CAAAA,CAAC7E,KAAMpE,EAAUkJ,WAAYrI,EAAesI,QAASA,EAAAA,OAAOA,CAAErN,sBAAoB,eAAeC,wBAAsB,qBAAqBC,0BAAwB,uBAC1L,CCzBA,MAAe,cAAqB,UAAW,MAAQ,YAAY,CAAC,CAAC,OAAO,CAAC,EAAI,YAAa,KAAM,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAI,aAAa,CAAM,UAAQ,EAAC,CAAC,oCCW3IoN,EAAW,CACtB7L,KADsB,CACf,qBACT,EAIe,eAAe8L,EAAK1N,CAAgB,EACjD,IAAM0M,EAAe,MAAM1M,EAAM0M,EAA3BA,CAAqB1M,SAAkB,CAO7C,OALAkN,EAAkBS,KAAK,CAACjB,GAKjBkB,CAAAA,EAAAA,EAAAA,CALPV,EAKOU,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAcC,UAAY,IAAO3N,qBAAoB,iBAAgBC,uBAAsB,QAAOC,yBAAwB,YAC9H,SAAA0N,CAAAA,EAAAA,EAAAA,IAAAA,CAACrM,CAAAA,KAAAA,CAAAA,CAAIlB,SAAU,4CACbuN,CAAAA,EAAAA,EAAAA,IAAAA,CAACrM,CAAAA,KAAAA,CAAAA,CAAIlB,SAAU,8CACboN,CAAAA,EAAAA,EAAAA,GAAAA,CAACjM,CAAAA,EAAAA,CAAAA,CAAAA,CAAQC,KAAM,YAAWC,WAAY,wDAAuD1B,qBAAoB,WAAUE,yBAAwB,cACnJ0N,CAAAA,EAAAA,EAAAA,IAAAA,CAACC,CAAAA,IAAAA,CAAKC,IAAK,MAAVD,oBAAmCxN,SAAAA,CAAWG,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAACuN,CAAAA,EAAAA,EAAAA,CAAAA,CAAkB,yBAAuB/N,qBAAoB,QAAOE,yBAAwB,sBAC5IuN,CAAAA,EAAAA,EAAAA,GAAAA,CAACO,CAAAA,EAAAA,CAAS3N,KAAT2N,IAAmB,gBAAehO,qBAAoB,YAAWE,yBAAwB,cAAa,iBAG3GuN,CAAAA,EAAAA,EAAAA,GAAAA,CAACQ,CAAAA,EAAAA,SAAAA,CAAAA,CAAUjO,qBAAoB,aAAYE,yBAAwB,cACnEuN,CAAAA,EAAAA,EAAAA,GAAAA,CAACS,CAAAA,EAAAA,QAAAA,CAAAA,CAEHC,QAAAA,CAAUV,CAAAA,EAAAA,EAAAA,GAAAA,CAACzC,CAAAA,EAAAA,CAAkBC,WAAa,GAA/BD,QAA4C,GAAGG,WAAa,KAAOnL,qBAAoB,YAAWE,yBAAwB,YACjI,SAAAuN,CAAAA,EAAAA,EAAAA,GAAAA,CAACX,CAAAA,EAAAA,CAAmB9M,eAAnB8M,MAAuC,sBAAqB5M,yBAAwB,oBAI/F,CClCA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAAyB,IAAI,CATd,IASmB,CAAC,EAAiB,CAClD,CARiD,IAQ5C,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAC,EAAd,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EADyC,KAE9E,CAAO,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,oBAAoB,CACpC,aAAa,CAAE,MAAM,CACrB,iBAAiB,iBACjB,UACA,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAOjB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,4DCAA,kDCAA,wDCAA,iECAA,sDCAA,uDCAA,yDCAA,iDCAA,2DCAA,wVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,UACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAmI,CAoBvJ,kGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,qGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,+BACA,8BAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,wFC1Fc,SAASwN,EAAc,UACpCvG,CAAQ,YACRwG,GAAa,CAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAAC/C,EAAAA,UAAUA,CAAAA,CAACvK,UAAU,iCAChC,UAACkB,MAAAA,CAAIlB,UAAU,mCAA2B8G,MAC5B,UAAC5F,MAAAA,CAAIlB,UAAU,mCAA2B8G,KAElE,0BCdA,iDCAA,wDCAA,m5BCAA,iJCYO,SAASgG,EAA4B,MAC1C7E,CAAI,YACJ8E,CAAU,CACVC,SAAO,CAC2B,EAClC,GAAM,CAACe,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAaA,CAAC,UAAW7B,EAAAA,EAAcA,CAACC,WAAW,CAAC,KACjE6B,EAAYC,KAAKC,IAAI,CAACpB,EAAagB,GACnC,OACJK,CAAK,CACN,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAYA,CAAC,MACfpG,UAEA+E,EAEAiB,UAAWA,EACXK,SAAS,EAETC,WAAY,GACd,GACA,MAAO,UAACC,EAAAA,CAASA,CAAAA,CAACJ,MAAOA,EAAOzO,sBAAoB,YAAYC,wBAAsB,eAAeC,0BAAwB,qBACzH,UAAC4O,EAAAA,CAAgBA,CAAAA,CAACL,MAAOA,EAAOzO,sBAAoB,mBAAmBE,0BAAwB,eAErG", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/popover.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/badge.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/skeleton.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/heading.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/?fee3", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/./src/constants/mock-api.ts", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/?4539", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/../../../src/icons/eye-off.ts", "webpack://next-shadcn-dashboard-starter/./src/components/ui/table/data-table-column-header.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/icons/text.ts", "webpack://next-shadcn-dashboard-starter/../../../src/icons/circle-check.ts", "webpack://next-shadcn-dashboard-starter/./src/components/ui/modal.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/modal/alert-modal.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-tables/cell-action.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-tables/options.tsx", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-tables/columns.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/ui/dialog.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/scroll-area.tsx", "webpack://next-shadcn-dashboard-starter/?b8d0", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/table/data-table-skeleton.tsx", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/nuqs@2.4.1_next@15.3.2_@bab_c9b7efe6019a68e9ed2b5d9ba26d6a74/node_modules/nuqs/dist/server.js", "webpack://next-shadcn-dashboard-starter/./src/lib/searchparams.ts", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-listing.tsx", "webpack://next-shadcn-dashboard-starter/../../../src/icons/IconPlus.ts", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/product/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/?6277", "webpack://next-shadcn-dashboard-starter/./src/components/layout/page-container.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\"", "webpack://next-shadcn-dashboard-starter/./src/features/products/components/product-tables/index.tsx"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "'use client';\n\nimport * as React from 'react';\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\nimport { cn } from '@/lib/utils';\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot='popover' {...props} data-sentry-element=\"PopoverPrimitive.Root\" data-sentry-component=\"Popover\" data-sentry-source-file=\"popover.tsx\" />;\n}\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot='popover-trigger' {...props} data-sentry-element=\"PopoverPrimitive.Trigger\" data-sentry-component=\"PopoverTrigger\" data-sentry-source-file=\"popover.tsx\" />;\n}\nfunction PopoverContent({\n  className,\n  align = 'center',\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return <PopoverPrimitive.Portal data-sentry-element=\"PopoverPrimitive.Portal\" data-sentry-component=\"PopoverContent\" data-sentry-source-file=\"popover.tsx\">\r\n      <PopoverPrimitive.Content data-slot='popover-content' align={align} sideOffset={sideOffset} className={cn('bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden', className)} {...props} data-sentry-element=\"PopoverPrimitive.Content\" data-sentry-source-file=\"popover.tsx\" />\r\n    </PopoverPrimitive.Portal>;\n}\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot='popover-anchor' {...props} data-sentry-element=\"PopoverPrimitive.Anchor\" data-sentry-component=\"PopoverAnchor\" data-sentry-source-file=\"popover.tsx\" />;\n}\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import { cn } from '@/lib/utils';\nfunction Skeleton({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='skeleton' className={cn('bg-accent animate-pulse rounded-md', className)} {...props} data-sentry-component=\"Skeleton\" data-sentry-source-file=\"skeleton.tsx\" />;\n}\nexport { Skeleton };", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "interface HeadingProps {\n  title: string;\n  description: string;\n}\nexport const Heading: React.FC<HeadingProps> = ({\n  title,\n  description\n}) => {\n  return <div data-sentry-component=\"Heading\" data-sentry-source-file=\"heading.tsx\">\r\n      <h2 className='text-3xl font-bold tracking-tight'>{title}</h2>\r\n      <p className='text-muted-foreground text-sm'>{description}</p>\r\n    </div>;\n};", "module.exports = require(\"node:zlib\");", "import(/* webpackMode: \"eager\", webpackExports: [\"__esModule\",\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\app-dir\\\\link.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Separator\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\separator.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Table\",\"TableHeader\",\"TableRow\",\"TableHead\",\"TableBody\",\"TableCell\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\table.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"columns\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-tables\\\\columns.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ProductTable\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-tables\\\\index.tsx\");\n", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "////////////////////////////////////////////////////////////////////////////////\r\n// 🛑 Nothing in here has anything to do with Nextjs, it's just a fake database\r\n////////////////////////////////////////////////////////////////////////////////\r\n\r\nimport { faker } from '@faker-js/faker';\r\nimport { matchSorter } from 'match-sorter'; // For filtering\r\n\r\nexport const delay = (ms: number) =>\r\n  new Promise((resolve) => setTimeout(resolve, ms));\r\n\r\n// Define the shape of Product data\r\nexport type Product = {\r\n  photo_url: string;\r\n  name: string;\r\n  description: string;\r\n  created_at: string;\r\n  price: number;\r\n  id: number;\r\n  category: string;\r\n  updated_at: string;\r\n};\r\n\r\n// Mock product data store\r\nexport const fakeProducts = {\r\n  records: [] as Product[], // Holds the list of product objects\r\n\r\n  // Initialize with sample data\r\n  initialize() {\r\n    const sampleProducts: Product[] = [];\r\n    function generateRandomProductData(id: number): Product {\r\n      const categories = [\r\n        'Electronics',\r\n        'Furniture',\r\n        'Clothing',\r\n        'Toys',\r\n        'Groceries',\r\n        'Books',\r\n        'Jewelry',\r\n        'Beauty Products'\r\n      ];\r\n\r\n      return {\r\n        id,\r\n        name: faker.commerce.productName(),\r\n        description: faker.commerce.productDescription(),\r\n        created_at: faker.date\r\n          .between({ from: '2022-01-01', to: '2023-12-31' })\r\n          .toISOString(),\r\n        price: parseFloat(faker.commerce.price({ min: 5, max: 500, dec: 2 })),\r\n        photo_url: `https://api.slingacademy.com/public/sample-products/${id}.png`,\r\n        category: faker.helpers.arrayElement(categories),\r\n        updated_at: faker.date.recent().toISOString()\r\n      };\r\n    }\r\n\r\n    // Generate remaining records\r\n    for (let i = 1; i <= 20; i++) {\r\n      sampleProducts.push(generateRandomProductData(i));\r\n    }\r\n\r\n    this.records = sampleProducts;\r\n  },\r\n\r\n  // Get all products with optional category filtering and search\r\n  async getAll({\r\n    categories = [],\r\n    search\r\n  }: {\r\n    categories?: string[];\r\n    search?: string;\r\n  }) {\r\n    let products = [...this.records];\r\n\r\n    // Filter products based on selected categories\r\n    if (categories.length > 0) {\r\n      products = products.filter((product) =>\r\n        categories.includes(product.category)\r\n      );\r\n    }\r\n\r\n    // Search functionality across multiple fields\r\n    if (search) {\r\n      products = matchSorter(products, search, {\r\n        keys: ['name', 'description', 'category']\r\n      });\r\n    }\r\n\r\n    return products;\r\n  },\r\n\r\n  // Get paginated results with optional category filtering and search\r\n  async getProducts({\r\n    page = 1,\r\n    limit = 10,\r\n    categories,\r\n    search\r\n  }: {\r\n    page?: number;\r\n    limit?: number;\r\n    categories?: string;\r\n    search?: string;\r\n  }) {\r\n    await delay(1000);\r\n    const categoriesArray = categories ? categories.split('.') : [];\r\n    const allProducts = await this.getAll({\r\n      categories: categoriesArray,\r\n      search\r\n    });\r\n    const totalProducts = allProducts.length;\r\n\r\n    // Pagination logic\r\n    const offset = (page - 1) * limit;\r\n    const paginatedProducts = allProducts.slice(offset, offset + limit);\r\n\r\n    // Mock current time\r\n    const currentTime = new Date().toISOString();\r\n\r\n    // Return paginated response\r\n    return {\r\n      success: true,\r\n      time: currentTime,\r\n      message: 'Sample data for testing and learning purposes',\r\n      total_products: totalProducts,\r\n      offset,\r\n      limit,\r\n      products: paginatedProducts\r\n    };\r\n  },\r\n\r\n  // Get a specific product by its ID\r\n  async getProductById(id: number) {\r\n    await delay(1000); // Simulate a delay\r\n\r\n    // Find the product by its ID\r\n    const product = this.records.find((product) => product.id === id);\r\n\r\n    if (!product) {\r\n      return {\r\n        success: false,\r\n        message: `Product with ID ${id} not found`\r\n      };\r\n    }\r\n\r\n    // Mock current time\r\n    const currentTime = new Date().toISOString();\r\n\r\n    return {\r\n      success: true,\r\n      time: currentTime,\r\n      message: `Product with ID ${id} found`,\r\n      product\r\n    };\r\n  }\r\n};\r\n\r\n// Initialize sample products\r\nfakeProducts.initialize();\r\n", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "import(/* webpackMode: \"eager\", webpackExports: [\"__esModule\",\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\\\node_modules\\\\next\\\\dist\\\\client\\\\app-dir\\\\link.js\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Separator\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\separator.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"Table\",\"TableHeader\",\"TableRow\",\"TableHead\",\"TableBody\",\"TableCell\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\table.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"columns\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-tables\\\\columns.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ProductTable\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\products\\\\components\\\\product-tables\\\\index.tsx\");\n", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49',\n      key: 'ct8e1f',\n    },\n  ],\n  ['path', { d: 'M14.084 14.158a3 3 0 0 1-4.242-4.242', key: '151rxh' }],\n  [\n    'path',\n    {\n      d: 'M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143',\n      key: '13bj9a',\n    },\n  ],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n];\n\n/**\n * @component @name EyeOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAuNzMzIDUuMDc2YTEwLjc0NCAxMC43NDQgMCAwIDEgMTEuMjA1IDYuNTc1IDEgMSAwIDAgMSAwIC42OTYgMTAuNzQ3IDEwLjc0NyAwIDAgMS0xLjQ0NCAyLjQ5IiAvPgogIDxwYXRoIGQ9Ik0xNC4wODQgMTQuMTU4YTMgMyAwIDAgMS00LjI0Mi00LjI0MiIgLz4KICA8cGF0aCBkPSJNMTcuNDc5IDE3LjQ5OWExMC43NSAxMC43NSAwIDAgMS0xNS40MTctNS4xNTEgMSAxIDAgMCAxIDAtLjY5NiAxMC43NSAxMC43NSAwIDAgMSA0LjQ0Ni01LjE0MyIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst EyeOff = createLucideIcon('EyeOff', __iconNode);\n\nexport default EyeOff;\n", "'use client';\n\nimport type { Column } from '@tanstack/react-table';\nimport { EyeOff } from 'lucide-react';\nimport { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { cn } from '@/lib/utils';\nimport { ChevronDownIcon, ChevronUpIcon, CaretSortIcon, Cross2Icon } from '@radix-ui/react-icons';\ninterface DataTableColumnHeaderProps<TData, TValue> extends React.ComponentProps<typeof DropdownMenuTrigger> {\n  column: Column<TData, TValue>;\n  title: string;\n}\nexport function DataTableColumnHeader<TData, TValue>({\n  column,\n  title,\n  className,\n  ...props\n}: DataTableColumnHeaderProps<TData, TValue>) {\n  if (!column.getCanSort() && !column.getCanHide()) {\n    return <div className={cn(className)}>{title}</div>;\n  }\n  return <DropdownMenu data-sentry-element=\"DropdownMenu\" data-sentry-component=\"DataTableColumnHeader\" data-sentry-source-file=\"data-table-column-header.tsx\">\r\n      <DropdownMenuTrigger className={cn('hover:bg-accent focus:ring-ring data-[state=open]:bg-accent [&_svg]:text-muted-foreground -ml-1.5 flex h-8 items-center gap-1.5 rounded-md px-2 py-1.5 focus:ring-1 focus:outline-none [&_svg]:size-4 [&_svg]:shrink-0', className)} {...props} data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"data-table-column-header.tsx\">\r\n        {title}\r\n        {column.getCanSort() && (column.getIsSorted() === 'desc' ? <ChevronDownIcon /> : column.getIsSorted() === 'asc' ? <ChevronUpIcon /> : <CaretSortIcon />)}\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align='start' className='w-28' data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"data-table-column-header.tsx\">\r\n        {column.getCanSort() && <>\r\n            <DropdownMenuCheckboxItem className='[&_svg]:text-muted-foreground relative pr-8 pl-2 [&>span:first-child]:right-2 [&>span:first-child]:left-auto' checked={column.getIsSorted() === 'asc'} onClick={() => column.toggleSorting(false)}>\r\n              <ChevronUpIcon />\r\n              Asc\r\n            </DropdownMenuCheckboxItem>\r\n            <DropdownMenuCheckboxItem className='[&_svg]:text-muted-foreground relative pr-8 pl-2 [&>span:first-child]:right-2 [&>span:first-child]:left-auto' checked={column.getIsSorted() === 'desc'} onClick={() => column.toggleSorting(true)}>\r\n              <ChevronDownIcon />\r\n              Desc\r\n            </DropdownMenuCheckboxItem>\r\n            {column.getIsSorted() && <DropdownMenuItem className='[&_svg]:text-muted-foreground pl-2' onClick={() => column.clearSorting()}>\r\n                <Cross2Icon />\r\n                Reset\r\n              </DropdownMenuItem>}\r\n          </>}\r\n        {column.getCanHide() && <DropdownMenuCheckboxItem className='[&_svg]:text-muted-foreground relative pr-8 pl-2 [&>span:first-child]:right-2 [&>span:first-child]:left-auto' checked={!column.getIsVisible()} onClick={() => column.toggleVisibility(false)}>\r\n            <EyeOff />\r\n            Hide\r\n          </DropdownMenuCheckboxItem>}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>;\n}", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M17 6.1H3', key: 'wptmhv' }],\n  ['path', { d: 'M21 12.1H3', key: '1j38uz' }],\n  ['path', { d: 'M15.1 18H3', key: '1nb16a' }],\n];\n\n/**\n * @component @name Text\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgNi4xSDMiIC8+CiAgPHBhdGggZD0iTTIxIDEyLjFIMyIgLz4KICA8cGF0aCBkPSJNMTUuMSAxOEgzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Text = createLucideIcon('Text', __iconNode);\n\nexport default Text;\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm9 12 2 2 4-4', key: 'dzmm74' }],\n];\n\n/**\n * @component @name CircleCheck\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtOSAxMiAyIDIgNC00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheck = createLucideIcon('CircleCheck', __iconNode);\n\nexport default CircleCheck;\n", "'use client';\n\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';\ninterface ModalProps {\n  title: string;\n  description: string;\n  isOpen: boolean;\n  onClose: () => void;\n  children?: React.ReactNode;\n}\nexport const Modal: React.FC<ModalProps> = ({\n  title,\n  description,\n  isOpen,\n  onClose,\n  children\n}) => {\n  const onChange = (open: boolean) => {\n    if (!open) {\n      onClose();\n    }\n  };\n  return <Dialog open={isOpen} onOpenChange={onChange} data-sentry-element=\"Dialog\" data-sentry-component=\"Modal\" data-sentry-source-file=\"modal.tsx\">\r\n      <DialogContent data-sentry-element=\"DialogContent\" data-sentry-source-file=\"modal.tsx\">\r\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"modal.tsx\">\r\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"modal.tsx\">{title}</DialogTitle>\r\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"modal.tsx\">{description}</DialogDescription>\r\n        </DialogHeader>\r\n        <div>{children}</div>\r\n      </DialogContent>\r\n    </Dialog>;\n};", "'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Modal } from '@/components/ui/modal';\ninterface AlertModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => void;\n  loading: boolean;\n}\nexport const AlertModal: React.FC<AlertModalProps> = ({\n  isOpen,\n  onClose,\n  onConfirm,\n  loading\n}) => {\n  const [isMounted, setIsMounted] = useState(false);\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n  if (!isMounted) {\n    return null;\n  }\n  return <Modal title='Are you sure?' description='This action cannot be undone.' isOpen={isOpen} onClose={onClose} data-sentry-element=\"Modal\" data-sentry-component=\"AlertModal\" data-sentry-source-file=\"alert-modal.tsx\">\r\n      <div className='flex w-full items-center justify-end space-x-2 pt-6'>\r\n        <Button disabled={loading} variant='outline' onClick={onClose} data-sentry-element=\"Button\" data-sentry-source-file=\"alert-modal.tsx\">\r\n          Cancel\r\n        </Button>\r\n        <Button disabled={loading} variant='destructive' onClick={onConfirm} data-sentry-element=\"Button\" data-sentry-source-file=\"alert-modal.tsx\">\r\n          Continue\r\n        </Button>\r\n      </div>\r\n    </Modal>;\n};", "'use client';\n\nimport { AlertModal } from '@/components/modal/alert-modal';\nimport { Button } from '@/components/ui/button';\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';\nimport { Product } from '@/constants/data';\nimport { IconEdit, IconDotsVertical, IconTrash } from '@tabler/icons-react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\ninterface CellActionProps {\n  data: Product;\n}\nexport const CellAction: React.FC<CellActionProps> = ({\n  data\n}) => {\n  const [loading] = useState(false);\n  const [open, setOpen] = useState(false);\n  const router = useRouter();\n  const onConfirm = async () => {};\n  return <>\r\n      <AlertModal isOpen={open} onClose={() => setOpen(false)} onConfirm={onConfirm} loading={loading} data-sentry-element=\"AlertModal\" data-sentry-source-file=\"cell-action.tsx\" />\r\n      <DropdownMenu modal={false} data-sentry-element=\"DropdownMenu\" data-sentry-source-file=\"cell-action.tsx\">\r\n        <DropdownMenuTrigger asChild data-sentry-element=\"DropdownMenuTrigger\" data-sentry-source-file=\"cell-action.tsx\">\r\n          <Button variant='ghost' className='h-8 w-8 p-0' data-sentry-element=\"Button\" data-sentry-source-file=\"cell-action.tsx\">\r\n            <span className='sr-only'>Open menu</span>\r\n            <IconDotsVertical className='h-4 w-4' data-sentry-element=\"IconDotsVertical\" data-sentry-source-file=\"cell-action.tsx\" />\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align='end' data-sentry-element=\"DropdownMenuContent\" data-sentry-source-file=\"cell-action.tsx\">\r\n          <DropdownMenuLabel data-sentry-element=\"DropdownMenuLabel\" data-sentry-source-file=\"cell-action.tsx\">Actions</DropdownMenuLabel>\r\n\r\n          <DropdownMenuItem onClick={() => router.push(`/dashboard/product/${data.id}`)} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"cell-action.tsx\">\r\n            <IconEdit className='mr-2 h-4 w-4' data-sentry-element=\"IconEdit\" data-sentry-source-file=\"cell-action.tsx\" /> Update\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setOpen(true)} data-sentry-element=\"DropdownMenuItem\" data-sentry-source-file=\"cell-action.tsx\">\r\n            <IconTrash className='mr-2 h-4 w-4' data-sentry-element=\"IconTrash\" data-sentry-source-file=\"cell-action.tsx\" /> Delete\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </>;\n};", "export const CATEGORY_OPTIONS = [{\n  value: 'Electronics',\n  label: 'Electronics'\n}, {\n  value: 'Furniture',\n  label: 'Furniture'\n}, {\n  value: 'Clothing',\n  label: 'Clothing'\n}, {\n  value: 'Toys',\n  label: 'Toys'\n}, {\n  value: 'Groceries',\n  label: 'Groceries'\n}, {\n  value: 'Books',\n  label: 'Books'\n}, {\n  value: 'Jewelry',\n  label: 'Jewelry'\n}, {\n  value: 'Beauty Products',\n  label: 'Beauty Products'\n}];", "'use client';\n\nimport { Badge } from '@/components/ui/badge';\nimport { DataTableColumnHeader } from '@/components/ui/table/data-table-column-header';\nimport { Product } from '@/constants/data';\nimport { Column, ColumnDef } from '@tanstack/react-table';\nimport { CheckCircle2, Text, XCircle } from 'lucide-react';\nimport Image from 'next/image';\nimport { CellAction } from './cell-action';\nimport { CATEGORY_OPTIONS } from './options';\nexport const columns: ColumnDef<Product>[] = [{\n  accessorKey: 'photo_url',\n  header: 'IMAGE',\n  cell: ({\n    row\n  }) => {\n    return <div className='relative aspect-square'>\r\n          <Image src={row.getValue('photo_url')} alt={row.getValue('name')} fill className='rounded-lg' />\r\n        </div>;\n  }\n}, {\n  id: 'name',\n  accessorKey: 'name',\n  header: ({\n    column\n  }: {\n    column: Column<Product, unknown>;\n  }) => <DataTableColumnHeader column={column} title='Name' />,\n  cell: ({\n    cell\n  }) => <div>{cell.getValue<Product['name']>()}</div>,\n  meta: {\n    label: 'Name',\n    placeholder: 'Search products...',\n    variant: 'text',\n    icon: Text\n  },\n  enableColumnFilter: true\n}, {\n  id: 'category',\n  accessorKey: 'category',\n  header: ({\n    column\n  }: {\n    column: Column<Product, unknown>;\n  }) => <DataTableColumnHeader column={column} title='Category' />,\n  cell: ({\n    cell\n  }) => {\n    const status = cell.getValue<Product['category']>();\n    const Icon = status === 'active' ? CheckCircle2 : XCircle;\n    return <Badge variant='outline' className='capitalize'>\r\n          <Icon />\r\n          {status}\r\n        </Badge>;\n  },\n  enableColumnFilter: true,\n  meta: {\n    label: 'categories',\n    variant: 'multiSelect',\n    options: CATEGORY_OPTIONS\n  }\n}, {\n  accessorKey: 'price',\n  header: 'PRICE'\n}, {\n  accessorKey: 'description',\n  header: 'DESCRIPTION'\n}, {\n  id: 'actions',\n  cell: ({\n    row\n  }) => <CellAction data={row.original} />\n}];", "'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { XIcon } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot='dialog' {...props} data-sentry-element=\"DialogPrimitive.Root\" data-sentry-component=\"Dialog\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} data-sentry-element=\"DialogPrimitive.Trigger\" data-sentry-component=\"DialogTrigger\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} data-sentry-element=\"DialogPrimitive.Portal\" data-sentry-component=\"DialogPortal\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} data-sentry-element=\"DialogPrimitive.Close\" data-sentry-component=\"DialogClose\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return <DialogPrimitive.Overlay data-slot='dialog-overlay' className={cn('data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50', className)} {...props} data-sentry-element=\"DialogPrimitive.Overlay\" data-sentry-component=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\n  return <DialogPortal data-slot='dialog-portal' data-sentry-element=\"DialogPortal\" data-sentry-component=\"DialogContent\" data-sentry-source-file=\"dialog.tsx\">\r\n      <DialogOverlay data-sentry-element=\"DialogOverlay\" data-sentry-source-file=\"dialog.tsx\" />\r\n      <DialogPrimitive.Content data-slot='dialog-content' className={cn('bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg', className)} {...props} data-sentry-element=\"DialogPrimitive.Content\" data-sentry-source-file=\"dialog.tsx\">\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\" data-sentry-element=\"DialogPrimitive.Close\" data-sentry-source-file=\"dialog.tsx\">\r\n          <XIcon data-sentry-element=\"XIcon\" data-sentry-source-file=\"dialog.tsx\" />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>;\n}\nfunction DialogHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-header' className={cn('flex flex-col gap-2 text-center sm:text-left', className)} {...props} data-sentry-component=\"DialogHeader\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='dialog-footer' className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)} {...props} data-sentry-component=\"DialogFooter\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return <DialogPrimitive.Title data-slot='dialog-title' className={cn('text-lg leading-none font-semibold', className)} {...props} data-sentry-element=\"DialogPrimitive.Title\" data-sentry-component=\"DialogTitle\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return <DialogPrimitive.Description data-slot='dialog-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-element=\"DialogPrimitive.Description\" data-sentry-component=\"DialogDescription\" data-sentry-source-file=\"dialog.tsx\" />;\n}\nexport { Dialog, DialogClose, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogOverlay, DialogPortal, DialogTitle, DialogTrigger };", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "'use client';\n\nimport * as React from 'react';\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\nimport { cn } from '@/lib/utils';\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.Root\" data-sentry-component=\"ScrollArea\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.Viewport data-slot='scroll-area-viewport' className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1' data-sentry-element=\"ScrollAreaPrimitive.Viewport\" data-sentry-source-file=\"scroll-area.tsx\">\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n      <ScrollAreaPrimitive.Corner data-sentry-element=\"ScrollAreaPrimitive.Corner\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.Root>;\n}\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return <ScrollAreaPrimitive.ScrollAreaScrollbar data-slot='scroll-area-scrollbar' orientation={orientation} className={cn('flex touch-none p-px transition-colors select-none', orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent', orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaScrollbar\" data-sentry-component=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.ScrollAreaThumb data-slot='scroll-area-thumb' className='bg-border relative flex-1 rounded-full' data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaThumb\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>;\n}\nexport { ScrollArea, ScrollBar };", "\nexport { deleteKeylessAction as \"7f7b45347fd50452ee6e2850ded1018991a7b086f0\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { syncKeylessConfigAction as \"7f909588461cb83e855875f4939d6f26e4ae81b49e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { createOrReadKeylessAction as \"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { invalidateCacheAction as \"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\server-actions.js\"\n", "module.exports = require(\"node:fs\");", "import { Skeleton } from '@/components/ui/skeleton';\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';\nimport { cn } from '@/lib/utils';\ninterface DataTableSkeletonProps extends React.ComponentProps<'div'> {\n  columnCount: number;\n  rowCount?: number;\n  filterCount?: number;\n  cellWidths?: string[];\n  withViewOptions?: boolean;\n  withPagination?: boolean;\n  shrinkZero?: boolean;\n}\nexport function DataTableSkeleton({\n  columnCount,\n  rowCount = 10,\n  filterCount = 0,\n  cellWidths = ['auto'],\n  withViewOptions = true,\n  withPagination = true,\n  shrinkZero = false,\n  className,\n  ...props\n}: DataTableSkeletonProps) {\n  const cozyCellWidths = Array.from({\n    length: columnCount\n  }, (_, index) => cellWidths[index % cellWidths.length] ?? 'auto');\n  return <div className={cn('flex flex-1 flex-col space-y-4', className)} {...props} data-sentry-component=\"DataTableSkeleton\" data-sentry-source-file=\"data-table-skeleton.tsx\">\r\n      <div className='flex w-full items-center justify-between gap-2 overflow-auto p-1'>\r\n        <div className='flex flex-1 items-center gap-2'>\r\n          {filterCount > 0 ? Array.from({\n          length: filterCount\n        }).map((_, i) => <Skeleton key={i} className='h-7 w-[4.5rem] border-dashed' />) : null}\r\n        </div>\r\n        {withViewOptions ? <Skeleton className='ml-auto hidden h-7 w-[4.5rem] lg:flex' /> : null}\r\n      </div>\r\n\r\n      <div className='flex-1 rounded-md border'>\r\n        <Table data-sentry-element=\"Table\" data-sentry-source-file=\"data-table-skeleton.tsx\">\r\n          <TableHeader data-sentry-element=\"TableHeader\" data-sentry-source-file=\"data-table-skeleton.tsx\">\r\n            {Array.from({\n            length: 1\n          }).map((_, i) => <TableRow key={i} className='hover:bg-transparent'>\r\n                {Array.from({\n              length: columnCount\n            }).map((_, j) => <TableHead key={j} style={{\n              width: cozyCellWidths[j],\n              minWidth: shrinkZero ? cozyCellWidths[j] : 'auto'\n            }}>\r\n                    <Skeleton className='h-6 w-full' />\r\n                  </TableHead>)}\r\n              </TableRow>)}\r\n          </TableHeader>\r\n          <TableBody data-sentry-element=\"TableBody\" data-sentry-source-file=\"data-table-skeleton.tsx\">\r\n            {Array.from({\n            length: rowCount\n          }).map((_, i) => <TableRow key={i} className='hover:bg-transparent'>\r\n                {Array.from({\n              length: columnCount\n            }).map((_, j) => <TableCell key={j} style={{\n              width: cozyCellWidths[j],\n              minWidth: shrinkZero ? cozyCellWidths[j] : 'auto'\n            }}>\r\n                    <Skeleton className='h-6 w-full' />\r\n                  </TableCell>)}\r\n              </TableRow>)}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      {withPagination ? <div className='flex w-full items-center justify-between gap-4 overflow-auto p-1 sm:gap-8'>\r\n          <Skeleton className='h-7 w-40 shrink-0' />\r\n          <div className='flex items-center gap-4 sm:gap-6 lg:gap-8'>\r\n            <div className='flex items-center gap-2'>\r\n              <Skeleton className='h-7 w-24' />\r\n              <Skeleton className='h-7 w-[4.5rem]' />\r\n            </div>\r\n            <div className='flex items-center justify-center text-sm font-medium'>\r\n              <Skeleton className='h-7 w-20' />\r\n            </div>\r\n            <div className='flex items-center gap-2'>\r\n              <Skeleton className='hidden size-7 lg:block' />\r\n              <Skeleton className='size-7' />\r\n              <Skeleton className='size-7' />\r\n              <Skeleton className='hidden size-7 lg:block' />\r\n            </div>\r\n          </div>\r\n        </div> : null}\r\n    </div>;\n}", "import * as React from 'react';\n\n// src/cache.ts\n\n// src/errors.ts\nvar errors = {\n  303: \"Multiple adapter contexts detected. This might happen in monorepos.\",\n  404: \"nuqs requires an adapter to work with your framework.\",\n  409: \"Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` (via the %s adapter) was about to load on top.\",\n  414: \"Max safe URL length exceeded. Some browsers may not be able to accept this URL. Consider limiting the amount of state stored in the URL.\",\n  429: \"URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O\",\n  500: \"Empty search params cache. Search params can't be accessed in Layouts.\",\n  501: \"Search params cache already populated. Have you called `parse` twice?\"\n};\nfunction error(code) {\n  return `[nuqs] ${errors[code]}\n  See https://err.47ng.com/NUQS-${code}`;\n}\n\n// src/loader.ts\nfunction createLoader(parsers, { urlKeys = {} } = {}) {\n  function loadSearchParams(input) {\n    if (input instanceof Promise) {\n      return input.then((i) => loadSearchParams(i));\n    }\n    const searchParams = extractSearchParams(input);\n    const result = {};\n    for (const [key, parser] of Object.entries(parsers)) {\n      const urlKey = urlKeys[key] ?? key;\n      const value = searchParams.get(urlKey);\n      result[key] = parser.parseServerSide(value ?? undefined);\n    }\n    return result;\n  }\n  return loadSearchParams;\n}\nfunction extractSearchParams(input) {\n  try {\n    if (input instanceof Request) {\n      if (input.url) {\n        return new URL(input.url).searchParams;\n      } else {\n        return new URLSearchParams();\n      }\n    }\n    if (input instanceof URL) {\n      return input.searchParams;\n    }\n    if (input instanceof URLSearchParams) {\n      return input;\n    }\n    if (typeof input === \"object\") {\n      const entries = Object.entries(input);\n      const searchParams = new URLSearchParams();\n      for (const [key, value] of entries) {\n        if (Array.isArray(value)) {\n          for (const v of value) {\n            searchParams.append(key, v);\n          }\n        } else if (value !== void 0) {\n          searchParams.set(key, value);\n        }\n      }\n      return searchParams;\n    }\n    if (typeof input === \"string\") {\n      if (\"canParse\" in URL && URL.canParse(input)) {\n        return new URL(input).searchParams;\n      }\n      return new URLSearchParams(input);\n    }\n  } catch (e) {\n    return new URLSearchParams();\n  }\n  return new URLSearchParams();\n}\n\n// src/cache.ts\nvar $input = Symbol(\"Input\");\nfunction createSearchParamsCache(parsers, { urlKeys = {} } = {}) {\n  const load = createLoader(parsers, { urlKeys });\n  const getCache = React.cache(() => ({\n    searchParams: {}\n  }));\n  function parseSync(searchParams) {\n    const c = getCache();\n    if (Object.isFrozen(c.searchParams)) {\n      if (c[$input] && compareSearchParams(searchParams, c[$input])) {\n        return all();\n      }\n      throw new Error(error(501));\n    }\n    c.searchParams = load(searchParams);\n    c[$input] = searchParams;\n    return Object.freeze(c.searchParams);\n  }\n  function parse(searchParams) {\n    if (searchParams instanceof Promise) {\n      return searchParams.then(parseSync);\n    }\n    return parseSync(searchParams);\n  }\n  function all() {\n    const { searchParams } = getCache();\n    if (Object.keys(searchParams).length === 0) {\n      throw new Error(error(500));\n    }\n    return searchParams;\n  }\n  function get(key) {\n    const { searchParams } = getCache();\n    const entry = searchParams[key];\n    if (typeof entry === \"undefined\") {\n      throw new Error(\n        error(500) + `\n  in get(${String(key)})`\n      );\n    }\n    return entry;\n  }\n  return { parse, get, all };\n}\nfunction compareSearchParams(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// src/debug.ts\nvar debugEnabled = isDebugEnabled();\nfunction warn(message, ...args) {\n  if (!debugEnabled) {\n    return;\n  }\n  console.warn(message, ...args);\n}\nfunction isDebugEnabled() {\n  try {\n    if (typeof localStorage === \"undefined\") {\n      return false;\n    }\n    const test = \"nuqs-localStorage-test\";\n    localStorage.setItem(test, test);\n    const isStorageAvailable = localStorage.getItem(test) === test;\n    localStorage.removeItem(test);\n    if (!isStorageAvailable) {\n      return false;\n    }\n  } catch (error2) {\n    console.error(\n      \"[nuqs]: debug mode is disabled (localStorage unavailable).\",\n      error2\n    );\n    return false;\n  }\n  const debug = localStorage.getItem(\"debug\") ?? \"\";\n  return debug.includes(\"nuqs\");\n}\n\n// src/utils.ts\nfunction safeParse(parser, value, key) {\n  try {\n    return parser(value);\n  } catch (error2) {\n    warn(\n      \"[nuqs] Error while parsing value `%s`: %O\" + (key ? \" (for key `%s`)\" : \"\"),\n      value,\n      error2,\n      key\n    );\n    return null;\n  }\n}\n\n// src/parsers.ts\nfunction createParser(parser) {\n  function parseServerSideNullable(value) {\n    if (typeof value === \"undefined\") {\n      return null;\n    }\n    let str = \"\";\n    if (Array.isArray(value)) {\n      if (value[0] === undefined) {\n        return null;\n      }\n      str = value[0];\n    }\n    if (typeof value === \"string\") {\n      str = value;\n    }\n    return safeParse(parser.parse, str);\n  }\n  return {\n    eq: (a, b) => a === b,\n    ...parser,\n    parseServerSide: parseServerSideNullable,\n    withDefault(defaultValue) {\n      return {\n        ...this,\n        defaultValue,\n        parseServerSide(value) {\n          return parseServerSideNullable(value) ?? defaultValue;\n        }\n      };\n    },\n    withOptions(options) {\n      return {\n        ...this,\n        ...options\n      };\n    }\n  };\n}\nvar parseAsString = createParser({\n  parse: (v) => v,\n  serialize: (v) => `${v}`\n});\nvar parseAsInteger = createParser({\n  parse: (v) => {\n    const int = parseInt(v);\n    if (Number.isNaN(int)) {\n      return null;\n    }\n    return int;\n  },\n  serialize: (v) => Math.round(v).toFixed()\n});\nvar parseAsIndex = createParser({\n  parse: (v) => {\n    const int = parseAsInteger.parse(v);\n    if (int === null) {\n      return null;\n    }\n    return int - 1;\n  },\n  serialize: (v) => parseAsInteger.serialize(v + 1)\n});\nvar parseAsHex = createParser({\n  parse: (v) => {\n    const int = parseInt(v, 16);\n    if (Number.isNaN(int)) {\n      return null;\n    }\n    return int;\n  },\n  serialize: (v) => {\n    const hex = Math.round(v).toString(16);\n    return hex.padStart(hex.length + hex.length % 2, \"0\");\n  }\n});\nvar parseAsFloat = createParser({\n  parse: (v) => {\n    const float = parseFloat(v);\n    if (Number.isNaN(float)) {\n      return null;\n    }\n    return float;\n  },\n  serialize: (v) => v.toString()\n});\nvar parseAsBoolean = createParser({\n  parse: (v) => v === \"true\",\n  serialize: (v) => v ? \"true\" : \"false\"\n});\nfunction compareDates(a, b) {\n  return a.valueOf() === b.valueOf();\n}\nvar parseAsTimestamp = createParser({\n  parse: (v) => {\n    const ms = parseInt(v);\n    if (Number.isNaN(ms)) {\n      return null;\n    }\n    return new Date(ms);\n  },\n  serialize: (v) => v.valueOf().toString(),\n  eq: compareDates\n});\nvar parseAsIsoDateTime = createParser({\n  parse: (v) => {\n    const date = new Date(v);\n    if (Number.isNaN(date.valueOf())) {\n      return null;\n    }\n    return date;\n  },\n  serialize: (v) => v.toISOString(),\n  eq: compareDates\n});\nvar parseAsIsoDate = createParser({\n  parse: (v) => {\n    const date = new Date(v.slice(0, 10));\n    if (Number.isNaN(date.valueOf())) {\n      return null;\n    }\n    return date;\n  },\n  serialize: (v) => v.toISOString().slice(0, 10),\n  eq: compareDates\n});\nfunction parseAsStringEnum(validValues) {\n  return createParser({\n    parse: (query) => {\n      const asEnum = query;\n      if (validValues.includes(asEnum)) {\n        return asEnum;\n      }\n      return null;\n    },\n    serialize: (value) => value.toString()\n  });\n}\nfunction parseAsStringLiteral(validValues) {\n  return createParser({\n    parse: (query) => {\n      const asConst = query;\n      if (validValues.includes(asConst)) {\n        return asConst;\n      }\n      return null;\n    },\n    serialize: (value) => value.toString()\n  });\n}\nfunction parseAsNumberLiteral(validValues) {\n  return createParser({\n    parse: (query) => {\n      const asConst = parseFloat(query);\n      if (validValues.includes(asConst)) {\n        return asConst;\n      }\n      return null;\n    },\n    serialize: (value) => value.toString()\n  });\n}\nfunction parseAsJson(runtimeParser) {\n  return createParser({\n    parse: (query) => {\n      try {\n        const obj = JSON.parse(query);\n        return runtimeParser(obj);\n      } catch {\n        return null;\n      }\n    },\n    serialize: (value) => JSON.stringify(value),\n    eq(a, b) {\n      return a === b || JSON.stringify(a) === JSON.stringify(b);\n    }\n  });\n}\nfunction parseAsArrayOf(itemParser, separator = \",\") {\n  const itemEq = itemParser.eq ?? ((a, b) => a === b);\n  const encodedSeparator = encodeURIComponent(separator);\n  return createParser({\n    parse: (query) => {\n      if (query === \"\") {\n        return [];\n      }\n      return query.split(separator).map(\n        (item, index) => safeParse(\n          itemParser.parse,\n          item.replaceAll(encodedSeparator, separator),\n          `[${index}]`\n        )\n      ).filter((value) => value !== null && value !== undefined);\n    },\n    serialize: (values) => values.map((value) => {\n      const str = itemParser.serialize ? itemParser.serialize(value) : String(value);\n      return str.replaceAll(separator, encodedSeparator);\n    }).join(separator),\n    eq(a, b) {\n      if (a === b) {\n        return true;\n      }\n      if (a.length !== b.length) {\n        return false;\n      }\n      return a.every((value, index) => itemEq(value, b[index]));\n    }\n  });\n}\n\n// src/url-encoding.ts\nfunction renderQueryString(search) {\n  if (search.size === 0) {\n    return \"\";\n  }\n  const query = [];\n  for (const [key, value] of search.entries()) {\n    const safeKey = key.replace(/#/g, \"%23\").replace(/&/g, \"%26\").replace(/\\+/g, \"%2B\").replace(/=/g, \"%3D\").replace(/\\?/g, \"%3F\");\n    query.push(`${safeKey}=${encodeQueryValue(value)}`);\n  }\n  const queryString = \"?\" + query.join(\"&\");\n  warnIfURLIsTooLong(queryString);\n  return queryString;\n}\nfunction encodeQueryValue(input) {\n  return input.replace(/%/g, \"%25\").replace(/\\+/g, \"%2B\").replace(/ /g, \"+\").replace(/#/g, \"%23\").replace(/&/g, \"%26\").replace(/\"/g, \"%22\").replace(/'/g, \"%27\").replace(/`/g, \"%60\").replace(/</g, \"%3C\").replace(/>/g, \"%3E\").replace(/[\\x00-\\x1F]/g, (char) => encodeURIComponent(char));\n}\nvar URL_MAX_LENGTH = 2e3;\nfunction warnIfURLIsTooLong(queryString) {\n  if (process.env.NODE_ENV === \"production\") {\n    return;\n  }\n  if (typeof location === \"undefined\") {\n    return;\n  }\n  const url = new URL(location.href);\n  url.search = queryString;\n  if (url.href.length > URL_MAX_LENGTH) {\n    console.warn(error(414));\n  }\n}\n\n// src/serializer.ts\nfunction createSerializer(parsers, {\n  clearOnDefault = true,\n  urlKeys = {}\n} = {}) {\n  function serialize(arg1BaseOrValues, arg2values = {}) {\n    const [base, search] = isBase(arg1BaseOrValues) ? splitBase(arg1BaseOrValues) : [\"\", new URLSearchParams()];\n    const values = isBase(arg1BaseOrValues) ? arg2values : arg1BaseOrValues;\n    if (values === null) {\n      for (const key in parsers) {\n        const urlKey = urlKeys[key] ?? key;\n        search.delete(urlKey);\n      }\n      return base + renderQueryString(search);\n    }\n    for (const key in parsers) {\n      const parser = parsers[key];\n      const value = values[key];\n      if (!parser || value === undefined) {\n        continue;\n      }\n      const urlKey = urlKeys[key] ?? key;\n      const isMatchingDefault = parser.defaultValue !== undefined && (parser.eq ?? ((a, b) => a === b))(value, parser.defaultValue);\n      if (value === null || (parser.clearOnDefault ?? clearOnDefault ?? true) && isMatchingDefault) {\n        search.delete(urlKey);\n      } else {\n        search.set(urlKey, parser.serialize(value));\n      }\n    }\n    return base + renderQueryString(search);\n  }\n  return serialize;\n}\nfunction isBase(base) {\n  return typeof base === \"string\" || base instanceof URLSearchParams || base instanceof URL;\n}\nfunction splitBase(base) {\n  if (typeof base === \"string\") {\n    const [path = \"\", ...search] = base.split(\"?\");\n    return [path, new URLSearchParams(search.join(\"?\"))];\n  } else if (base instanceof URLSearchParams) {\n    return [\"\", new URLSearchParams(base)];\n  } else {\n    return [\n      base.origin + base.pathname,\n      new URLSearchParams(base.searchParams)\n    ];\n  }\n}\n\nexport { createLoader, createParser, createSearchParamsCache, createSerializer, parseAsArrayOf, parseAsBoolean, parseAsFloat, parseAsHex, parseAsIndex, parseAsInteger, parseAsIsoDate, parseAsIsoDateTime, parseAsJson, parseAsNumberLiteral, parseAsString, parseAsStringEnum, parseAsStringLiteral, parseAsTimestamp };\n", "import {\r\n  createSearchParamsCache,\r\n  createSerializer,\r\n  parseAsInteger,\r\n  parseAsString\r\n} from 'nuqs/server';\r\n\r\nexport const searchParams = {\r\n  page: parseAsInteger.withDefault(1),\r\n  perPage: parseAsInteger.withDefault(10),\r\n  name: parseAsString,\r\n  gender: parseAsString,\r\n  category: parseAsString\r\n  // advanced filter\r\n  // filters: getFiltersStateParser().withDefault([]),\r\n  // joinOperator: parseAsStringEnum(['and', 'or']).withDefault('and')\r\n};\r\n\r\nexport const searchParamsCache = createSearchParamsCache(searchParams);\r\nexport const serialize = createSerializer(searchParams);\r\n", "import { Product } from '@/constants/data';\nimport { fakeProducts } from '@/constants/mock-api';\nimport { searchParamsCache } from '@/lib/searchparams';\nimport { ProductTable } from './product-tables';\nimport { columns } from './product-tables/columns';\ntype ProductListingPage = {};\nexport default async function ProductListingPage({}: ProductListingPage) {\n  // Showcasing the use of search params cache in nested RSCs\n  const page = searchParamsCache.get('page');\n  const search = searchParamsCache.get('name');\n  const pageLimit = searchParamsCache.get('perPage');\n  const categories = searchParamsCache.get('category');\n  const filters = {\n    page,\n    limit: pageLimit,\n    ...(search && {\n      search\n    }),\n    ...(categories && {\n      categories: categories\n    })\n  };\n  const data = await fakeProducts.getProducts(filters);\n  const totalProducts = data.total_products;\n  const products: Product[] = data.products;\n  return <ProductTable data={products} totalItems={totalProducts} columns={columns} data-sentry-element=\"ProductTable\" data-sentry-component=\"ProductListingPage\" data-sentry-source-file=\"product-listing.tsx\" />;\n}", "import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'plus', 'IconPlus', [[\"path\",{\"d\":\"M12 5l0 14\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M5 12l14 0\",\"key\":\"svg-1\"}]]);", "import PageContainer from '@/components/layout/page-container';\nimport { buttonVariants } from '@/components/ui/button';\nimport { Heading } from '@/components/ui/heading';\nimport { Separator } from '@/components/ui/separator';\nimport { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';\nimport ProductListingPage from '@/features/products/components/product-listing';\nimport { searchParamsCache, serialize } from '@/lib/searchparams';\nimport { cn } from '@/lib/utils';\nimport { IconPlus } from '@tabler/icons-react';\nimport Link from 'next/link';\nimport { SearchParams } from 'nuqs/server';\nimport { Suspense } from 'react';\nexport const metadata = {\n  title: 'Dashboard: Products'\n};\ntype pageProps = {\n  searchParams: Promise<SearchParams>;\n};\nexport default async function Page(props: pageProps) {\n  const searchParams = await props.searchParams;\n  // Allow nested RSCs to access the search params (in a type-safe way)\n  searchParamsCache.parse(searchParams);\n\n  // This key is used for invoke suspense if any of the search params changed (used for filters).\n  // const key = serialize({ ...searchParams });\n\n  return <PageContainer scrollable={false} data-sentry-element=\"PageContainer\" data-sentry-component=\"Page\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex flex-1 flex-col space-y-4'>\r\n        <div className='flex items-start justify-between'>\r\n          <Heading title='Products' description='Manage products (Server side table functionalities.)' data-sentry-element=\"Heading\" data-sentry-source-file=\"page.tsx\" />\r\n          <Link href='/dashboard/product/new' className={cn(buttonVariants(), 'text-xs md:text-sm')} data-sentry-element=\"Link\" data-sentry-source-file=\"page.tsx\">\r\n            <IconPlus className='mr-2 h-4 w-4' data-sentry-element=\"IconPlus\" data-sentry-source-file=\"page.tsx\" /> Add New\r\n          </Link>\r\n        </div>\r\n        <Separator data-sentry-element=\"Separator\" data-sentry-source-file=\"page.tsx\" />\r\n        <Suspense\n      // key={key}\n      fallback={<DataTableSkeleton columnCount={5} rowCount={8} filterCount={2} />} data-sentry-element=\"Suspense\" data-sentry-source-file=\"page.tsx\">\r\n          <ProductListingPage data-sentry-element=\"ProductListingPage\" data-sentry-source-file=\"page.tsx\" />\r\n        </Suspense>\r\n      </div>\r\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/product',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/product',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/product',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/product',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\product\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'product',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\product\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\product\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/product/page\",\n        pathname: \"/dashboard/product\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");", "'use client';\n\nimport { DataTable } from '@/components/ui/table/data-table';\nimport { DataTableToolbar } from '@/components/ui/table/data-table-toolbar';\nimport { useDataTable } from '@/hooks/use-data-table';\nimport { ColumnDef } from '@tanstack/react-table';\nimport { parseAsInteger, useQueryState } from 'nuqs';\ninterface ProductTableParams<TData, TValue> {\n  data: TData[];\n  totalItems: number;\n  columns: ColumnDef<TData, TValue>[];\n}\nexport function ProductTable<TData, TValue>({\n  data,\n  totalItems,\n  columns\n}: ProductTableParams<TData, TValue>) {\n  const [pageSize] = useQueryState('perPage', parseAsInteger.withDefault(10));\n  const pageCount = Math.ceil(totalItems / pageSize);\n  const {\n    table\n  } = useDataTable({\n    data,\n    // product data\n    columns,\n    // product columns\n    pageCount: pageCount,\n    shallow: false,\n    //Setting to false triggers a network request with the updated querystring.\n    debounceMs: 500\n  });\n  return <DataTable table={table} data-sentry-element=\"DataTable\" data-sentry-component=\"ProductTable\" data-sentry-source-file=\"index.tsx\">\r\n      <DataTableToolbar table={table} data-sentry-element=\"DataTableToolbar\" data-sentry-source-file=\"index.tsx\" />\r\n    </DataTable>;\n}"], "names": ["Popover", "props", "PopoverPrimitive", "data-slot", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "PopoverTrigger", "PopoverC<PERSON>nt", "className", "align", "sideOffset", "cn", "badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "<PERSON><PERSON><PERSON><PERSON>", "Comp", "Slot", "Skeleton", "div", "Heading", "title", "description", "h2", "p", "delay", "Promise", "setTimeout", "resolve", "ms", "fakeProducts", "records", "initialize", "sampleProducts", "i", "push", "id", "name", "faker", "commerce", "productName", "productDescription", "created_at", "date", "between", "from", "to", "toISOString", "price", "parseFloat", "min", "max", "dec", "photo_url", "category", "helpers", "arrayElement", "updated_at", "recent", "getAll", "categories", "search", "products", "length", "filter", "includes", "product", "matchSorter", "keys", "getProducts", "page", "limit", "categoriesArray", "split", "allProducts", "totalProducts", "offset", "paginatedProducts", "slice", "success", "time", "currentTime", "Date", "message", "total_products", "getProductById", "find", "DataTableColumnHeader", "column", "getCanSort", "DropdownMenu", "DropdownMenuTrigger", "getIsSorted", "ChevronDownIcon", "ChevronUpIcon", "CaretSortIcon", "DropdownMenuContent", "DropdownMenuCheckboxItem", "checked", "onClick", "toggleSorting", "DropdownMenuItem", "clearSorting", "Cross2Icon", "getCanHide", "getIsVisible", "toggleVisibility", "Eye<PERSON>ff", "Modal", "isOpen", "onClose", "children", "Dialog", "open", "onOpenChange", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "AlertModal", "onConfirm", "loading", "isMounted", "setIsMounted", "useState", "useEffect", "<PERSON><PERSON>", "disabled", "CellAction", "data", "<PERSON><PERSON><PERSON>", "router", "useRouter", "modal", "span", "IconDotsVertical", "DropdownMenuLabel", "IconEdit", "IconTrash", "accessorKey", "header", "cell", "row", "Image", "src", "getValue", "alt", "fill", "meta", "label", "placeholder", "icon", "Text", "enableColumnFilter", "status", "Icon", "CheckCircle2", "XCircle", "options", "CATEGORY_OPTIONS", "value", "DialogPrimitive", "DialogTrigger", "DialogPortal", "DialogOverlay", "XIcon", "<PERSON><PERSON><PERSON><PERSON>er", "ScrollArea", "ScrollAreaPrimitive", "<PERSON><PERSON>Bar", "orientation", "DataTableSkeleton", "columnCount", "rowCount", "filterCount", "cellWidths", "withViewOptions", "withPagination", "shrinkZero", "cozyCell<PERSON>idths", "Array", "_", "index", "map", "Table", "TableHeader", "TableRow", "j", "TableHead", "style", "width", "min<PERSON><PERSON><PERSON>", "TableBody", "TableCell", "searchParams", "parseAsInteger", "<PERSON><PERSON><PERSON><PERSON>", "perPage", "parseAsString", "gender", "createSerializer", "ProductListingPage", "searchParamsCache", "get", "pageLimit", "filters", "ProductTable", "totalItems", "columns", "metadata", "Page", "parse", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "scrollable", "_jsxs", "Link", "href", "buttonVariants", "IconPlus", "Separator", "Suspense", "fallback", "pageSize", "useQueryState", "pageCount", "Math", "ceil", "table", "useDataTable", "shallow", "debounceMs", "DataTable", "DataTableToolbar"], "sourceRoot": ""}