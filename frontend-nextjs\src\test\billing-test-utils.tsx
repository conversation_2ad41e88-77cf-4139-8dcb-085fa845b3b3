import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'sonner'
import { Bill, Payment, BillItem, Deposit } from '@/types/clinic'

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode
}

const TestWrapper: React.FC<TestWrapperProps> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <Toaster />
    </QueryClientProvider>
  )
}

// Custom render function with providers
export const renderWithProviders = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  return render(ui, { wrapper: TestWrapper, ...options })
}

// Factory functions for creating test data
export const createMockBill = (overrides: Partial<Bill> = {}): Bill => ({
  id: 'test-bill-1',
  billNumber: 'BILL-TEST-001',
  patientId: 'test-patient-1',
  appointmentId: 'test-appointment-1',
  treatmentId: 'test-treatment-1',
  billType: 'treatment',
  status: 'confirmed',
  subtotal: 500.00,
  discountAmount: 50.00,
  taxAmount: 0.00,
  totalAmount: 450.00,
  remainingAmount: 450.00,
  dueDate: '2024-12-31T00:00:00.000Z',
  description: '测试账单',
  notes: '测试备注',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  createdBy: 'test-user-1',
  ...overrides,
})

export const createMockBillItem = (overrides: Partial<BillItem> = {}): BillItem => ({
  id: 'test-bill-item-1',
  billId: 'test-bill-1',
  itemType: 'treatment',
  itemId: 'test-treatment-1',
  itemName: '测试治疗项目',
  description: '测试治疗项目描述',
  quantity: 1,
  unitPrice: 500.00,
  discountRate: 10,
  totalPrice: 450.00,
  createdAt: '2024-01-01T00:00:00.000Z',
  ...overrides,
})

export const createMockPayment = (overrides: Partial<Payment> = {}): Payment => ({
  id: 'test-payment-1',
  paymentNumber: 'PAY-TEST-001',
  billId: 'test-bill-1',
  patientId: 'test-patient-1',
  amount: 450.00,
  paymentMethod: 'cash',
  paymentStatus: 'completed',
  transactionId: 'TXN-TEST-001',
  paymentDate: '2024-01-01T10:00:00.000Z',
  receivedBy: 'test-user-1',
  notes: '测试支付',
  receiptNumber: 'RCP-TEST-001',
  createdAt: '2024-01-01T10:00:00.000Z',
  updatedAt: '2024-01-01T10:00:00.000Z',
  ...overrides,
})

export const createMockDeposit = (overrides: Partial<Deposit> = {}): Deposit => ({
  id: 'test-deposit-1',
  depositNumber: 'DEP-TEST-001',
  patientId: 'test-patient-1',
  appointmentId: 'test-appointment-1',
  treatmentId: 'test-treatment-1',
  depositType: 'treatment',
  amount: 1000.00,
  status: 'active',
  usedAmount: 200.00,
  remainingAmount: 800.00,
  depositDate: '2024-01-01T00:00:00.000Z',
  expiryDate: '2024-12-31T00:00:00.000Z',
  purpose: '测试预付款',
  notes: '测试预付款备注',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  ...overrides,
})

// Test data generators for different scenarios
export const generateBillsForTesting = (count: number = 5): Bill[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockBill({
      id: `test-bill-${index + 1}`,
      billNumber: `BILL-TEST-${String(index + 1).padStart(3, '0')}`,
      totalAmount: (index + 1) * 100,
      remainingAmount: (index + 1) * 50,
      status: index % 2 === 0 ? 'confirmed' : 'paid',
    })
  )
}

export const generatePaymentsForTesting = (count: number = 3): Payment[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockPayment({
      id: `test-payment-${index + 1}`,
      paymentNumber: `PAY-TEST-${String(index + 1).padStart(3, '0')}`,
      amount: (index + 1) * 100,
      paymentMethod: ['cash', 'card', 'wechat'][index % 3] as Payment['paymentMethod'],
    })
  )
}

// Validation helpers
export const validateBillData = (bill: Bill): string[] => {
  const errors: string[] = []
  
  if (!bill.patientId) errors.push('Patient ID is required')
  if (!bill.description) errors.push('Description is required')
  if (bill.totalAmount < 0) errors.push('Total amount cannot be negative')
  if (bill.remainingAmount < 0) errors.push('Remaining amount cannot be negative')
  if (bill.remainingAmount > bill.totalAmount) errors.push('Remaining amount cannot exceed total amount')
  
  return errors
}

export const validatePaymentData = (payment: Payment): string[] => {
  const errors: string[] = []
  
  if (!payment.billId) errors.push('Bill ID is required')
  if (!payment.patientId) errors.push('Patient ID is required')
  if (payment.amount <= 0) errors.push('Payment amount must be positive')
  if (!payment.paymentMethod) errors.push('Payment method is required')
  
  return errors
}

// Mock API response helpers
export const createMockApiResponse = <T,>(data: T, pagination?: any) => ({
  docs: Array.isArray(data) ? data : [data],
  totalDocs: Array.isArray(data) ? data.length : 1,
  limit: pagination?.limit || 10,
  totalPages: pagination?.totalPages || 1,
  page: pagination?.page || 1,
  pagingCounter: pagination?.pagingCounter || 1,
  hasPrevPage: pagination?.hasPrevPage || false,
  hasNextPage: pagination?.hasNextPage || false,
})

// Error simulation helpers
export const createMockApiError = (status: number, message: string) => ({
  error: message,
  status,
  timestamp: new Date().toISOString(),
})

// Test constants
export const TEST_CONSTANTS = {
  VALID_PHONE_NUMBERS: ['+86-138-0013-8000', '13800138000', '138-0013-8000'],
  INVALID_PHONE_NUMBERS: ['123', '12345678901234567890', 'abc'],
  VALID_EMAIL_ADDRESSES: ['<EMAIL>', '<EMAIL>'],
  INVALID_EMAIL_ADDRESSES: ['invalid-email', '@example.com', 'test@'],
  PAYMENT_METHODS: ['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'],
  BILL_STATUSES: ['draft', 'sent', 'confirmed', 'paid', 'cancelled'],
  PAYMENT_STATUSES: ['pending', 'completed', 'failed', 'refunded'],
  DEPOSIT_STATUSES: ['active', 'used', 'refunded', 'expired'],
}

// Async test helpers
export const waitForApiCall = (ms: number = 100) => 
  new Promise(resolve => setTimeout(resolve, ms))

export const simulateNetworkDelay = () => waitForApiCall(50)

// Form interaction helpers
export const fillBillForm = async (
  getByTestId: (testId: string) => HTMLElement,
  billData: Partial<Bill>
) => {
  // This would be implemented based on actual form structure
  // Example implementation would fill form fields
}

export const fillPaymentForm = async (
  getByTestId: (testId: string) => HTMLElement,
  paymentData: Partial<Payment>
) => {
  // This would be implemented based on actual form structure
  // Example implementation would fill payment form fields
}

// Re-export commonly used testing utilities
export * from '@testing-library/react'
export * from '@testing-library/user-event'
export { vi } from 'vitest'
