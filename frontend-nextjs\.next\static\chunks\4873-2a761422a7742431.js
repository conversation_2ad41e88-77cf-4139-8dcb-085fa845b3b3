try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="68c3671a-0c15-4253-8779-0f13da6c8971",e._sentryDebugIdIdentifier="sentry-dbid-68c3671a-0c15-4253-8779-0f13da6c8971")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4873],{4056:(e,t,a)=>{a.d(t,{b:()=>x});var n=a(52880),l=a(74064),r=a(49896),s=a(57065),o=a(62054),i=a(45450),d=a(54651),c=a(86183);function u(e){let{table:t,pageSizeOptions:a=[10,20,30,40,50],className:l,...u}=e;return(0,n.jsxs)("div",{className:(0,d.cn)("flex w-full flex-col-reverse items-center justify-between gap-4 overflow-auto p-1 sm:flex-row sm:gap-8",l),...u,"data-sentry-component":"DataTablePagination","data-sentry-source-file":"data-table-pagination.tsx",children:[(0,n.jsx)("div",{className:"text-muted-foreground flex-1 text-sm whitespace-nowrap",children:t.getFilteredSelectedRowModel().rows.length>0?(0,n.jsxs)(n.Fragment,{children:[t.getFilteredSelectedRowModel().rows.length," of"," ",t.getFilteredRowModel().rows.length," row(s) selected."]}):(0,n.jsxs)(n.Fragment,{children:[t.getFilteredRowModel().rows.length," row(s) total."]})}),(0,n.jsxs)("div",{className:"flex flex-col-reverse items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("p",{className:"text-sm font-medium whitespace-nowrap",children:"Rows per page"}),(0,n.jsxs)(i.l6,{value:"".concat(t.getState().pagination.pageSize),onValueChange:e=>{t.setPageSize(Number(e))},"data-sentry-element":"Select","data-sentry-source-file":"data-table-pagination.tsx",children:[(0,n.jsx)(i.bq,{className:"h-8 w-[4.5rem] [&[data-size]]:h-8","data-sentry-element":"SelectTrigger","data-sentry-source-file":"data-table-pagination.tsx",children:(0,n.jsx)(i.yv,{placeholder:t.getState().pagination.pageSize,"data-sentry-element":"SelectValue","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,n.jsx)(i.gC,{side:"top","data-sentry-element":"SelectContent","data-sentry-source-file":"data-table-pagination.tsx",children:a.map(e=>(0,n.jsx)(i.eb,{value:"".concat(e),children:e},e))})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-center text-sm font-medium",children:["Page ",t.getState().pagination.pageIndex+1," of"," ",t.getPageCount()]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(o.$,{"aria-label":"Go to first page",variant:"outline",size:"icon",className:"hidden size-8 lg:flex",onClick:()=>t.setPageIndex(0),disabled:!t.getCanPreviousPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,n.jsx)(r.A,{"data-sentry-element":"ChevronsLeft","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,n.jsx)(o.$,{"aria-label":"Go to previous page",variant:"outline",size:"icon",className:"size-8",onClick:()=>t.previousPage(),disabled:!t.getCanPreviousPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,n.jsx)(c.YJP,{"data-sentry-element":"ChevronLeftIcon","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,n.jsx)(o.$,{"aria-label":"Go to next page",variant:"outline",size:"icon",className:"size-8",onClick:()=>t.nextPage(),disabled:!t.getCanNextPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,n.jsx)(c.vKP,{"data-sentry-element":"ChevronRightIcon","data-sentry-source-file":"data-table-pagination.tsx"})}),(0,n.jsx)(o.$,{"aria-label":"Go to last page",variant:"outline",size:"icon",className:"hidden size-8 lg:flex",onClick:()=>t.setPageIndex(t.getPageCount()-1),disabled:!t.getCanNextPage(),"data-sentry-element":"Button","data-sentry-source-file":"data-table-pagination.tsx",children:(0,n.jsx)(s.A,{"data-sentry-element":"ChevronsRight","data-sentry-source-file":"data-table-pagination.tsx"})})]})]})]})}var m=a(45550);function p(e){let{column:t,withBorder:a=!1}=e,n=t.getIsPinned(),l="left"===n&&t.getIsLastColumn("left"),r="right"===n&&t.getIsFirstColumn("right");return{boxShadow:a?l?"-4px 0 4px -4px hsl(var(--border)) inset":r?"4px 0 4px -4px hsl(var(--border)) inset":void 0:void 0,left:"left"===n?"".concat(t.getStart("left"),"px"):void 0,right:"right"===n?"".concat(t.getAfter("right"),"px"):void 0,opacity:n?.97:1,position:n?"sticky":"relative",background:"hsl(var(--background))",width:t.getSize(),zIndex:+!!n}}a(97164);var f=a(90917);function x(e){var t;let{table:a,actionBar:r,children:s}=e;return(0,n.jsxs)("div",{className:"flex flex-1 flex-col space-y-4","data-sentry-component":"DataTable","data-sentry-source-file":"data-table.tsx",children:[s,(0,n.jsx)("div",{className:"relative flex flex-1",children:(0,n.jsx)("div",{className:"absolute inset-0 flex overflow-hidden rounded-lg border",children:(0,n.jsxs)(f.ScrollArea,{className:"h-full w-full","data-sentry-element":"ScrollArea","data-sentry-source-file":"data-table.tsx",children:[(0,n.jsxs)(m.Table,{"data-sentry-element":"Table","data-sentry-source-file":"data-table.tsx",children:[(0,n.jsx)(m.TableHeader,{className:"bg-muted sticky top-0 z-10","data-sentry-element":"TableHeader","data-sentry-source-file":"data-table.tsx",children:a.getHeaderGroups().map(e=>(0,n.jsx)(m.TableRow,{children:e.headers.map(e=>(0,n.jsx)(m.TableHead,{colSpan:e.colSpan,style:{...p({column:e.column})},children:e.isPlaceholder?null:(0,l.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,n.jsx)(m.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"data-table.tsx",children:(null==(t=a.getRowModel().rows)?void 0:t.length)?a.getRowModel().rows.map(e=>(0,n.jsx)(m.TableRow,{"data-state":e.getIsSelected()&&"selected",children:e.getVisibleCells().map(e=>(0,n.jsx)(m.TableCell,{style:{...p({column:e.column})},children:(0,l.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,n.jsx)(m.TableRow,{children:(0,n.jsx)(m.TableCell,{colSpan:a.getAllColumns().length,className:"h-24 text-center",children:"No results."})})})]}),(0,n.jsx)(f.$,{orientation:"horizontal","data-sentry-element":"ScrollBar","data-sentry-source-file":"data-table.tsx"})]})})}),(0,n.jsxs)("div",{className:"flex flex-col gap-2.5",children:[(0,n.jsx)(u,{table:a,"data-sentry-element":"DataTablePagination","data-sentry-source-file":"data-table.tsx"}),r&&a.getFilteredSelectedRowModel().rows.length>0&&r]})]})}},29648:(e,t,a)=>{a.d(t,{u:()=>p});var n=a(74064),l=a(84080),r=a(2122),s=a(99004),o=a(99098),i=a(13238),d=a(73259),c=a(97164);let u=d.z.object({id:d.z.string(),desc:d.z.boolean()}),m=e=>{let t=e?e instanceof Set?e:new Set(e):null;return(0,i.Cp)({parse:e=>{try{let a=JSON.parse(e),n=d.z.array(u).safeParse(a);if(!n.success||t&&n.data.some(e=>!t.has(e.id)))return null;return n.data}catch(e){return null}},serialize:e=>JSON.stringify(e),eq:(e,t)=>e.length===t.length&&e.every((e,a)=>{var n,l;return e.id===(null==(n=t[a])?void 0:n.id)&&e.desc===(null==(l=t[a])?void 0:l.desc)})})};function p(e){var t,a,i,d,c;let{columns:u,pageCount:p=-1,initialState:f,history:x="replace",debounceMs:y=300,throttleMs:g=50,clearOnDefault:b=!1,enableAdvancedFilter:v=!1,scroll:h=!1,shallow:w=!0,startTransition:j,...N}=e,C=s.useMemo(()=>({history:x,scroll:h,shallow:w,throttleMs:g,debounceMs:y,clearOnDefault:b,startTransition:j}),[x,h,w,g,y,b,j]),[I,S]=s.useState(null!=(a=null==f?void 0:f.rowSelection)?a:{}),[z,k]=s.useState(null!=(i=null==f?void 0:f.columnVisibility)?i:{}),[P,M]=(0,r.ZA)("page",r.GJ.withOptions(C).withDefault(1)),[T,F]=(0,r.ZA)("perPage",r.GJ.withOptions(C).withDefault(null!=(d=null==f||null==(t=f.pagination)?void 0:t.pageSize)?d:10)),A=s.useMemo(()=>({pageIndex:P-1,pageSize:T}),[P,T]),D=s.useCallback(e=>{if("function"==typeof e){let t=e(A);M(t.pageIndex+1),F(t.pageSize)}else M(e.pageIndex+1),F(e.pageSize)},[A,M,F]),_=s.useMemo(()=>new Set(u.map(e=>e.id).filter(Boolean)),[u]),[V,R]=(0,r.ZA)("sort",m(_).withOptions(C).withDefault(null!=(c=null==f?void 0:f.sorting)?c:[])),B=s.useCallback(e=>{"function"==typeof e?R(e(V)):R(e)},[V,R]),E=s.useMemo(()=>v?[]:u.filter(e=>e.enableColumnFilter),[u,v]),L=s.useMemo(()=>v?{}:E.reduce((e,t)=>{var a,n,l;return(null==(a=t.meta)?void 0:a.options)?e[null!=(n=t.id)?n:""]=(0,r.IN)(r.tU,",").withOptions(C):e[null!=(l=t.id)?l:""]=r.tU.withOptions(C),e},{}),[E,C,v]),[O,G]=(0,r.ab)(L),H=function(e,t){let a=(0,o.c)(e),n=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(n.current),[]),s.useCallback(function(){for(var e=arguments.length,l=Array(e),r=0;r<e;r++)l[r]=arguments[r];window.clearTimeout(n.current),n.current=window.setTimeout(()=>a(...l),t)},[a,t])}(e=>{M(1),G(e)},y),q=s.useMemo(()=>v?[]:Object.entries(O).reduce((e,t)=>{let[a,n]=t;if(null!==n){let t=Array.isArray(n)?n:"string"==typeof n&&/[^a-zA-Z0-9]/.test(n)?n.split(/[^a-zA-Z0-9]+/).filter(Boolean):[n];e.push({id:a,value:t})}return e},[]),[O,v]),[J,$]=s.useState(q),U=s.useCallback(e=>{v||$(t=>{let a="function"==typeof e?e(t):e,n=a.reduce((e,t)=>(E.find(e=>e.id===t.id)&&(e[t.id]=t.value),e),{});for(let e of t)a.some(t=>t.id===e.id)||(n[e.id]=null);return H(n),a})},[H,E,v]);return{table:(0,n.N4)({...N,columns:u,initialState:f,pageCount:p,state:{pagination:A,sorting:V,columnVisibility:z,rowSelection:I,columnFilters:J},defaultColumn:{...N.defaultColumn,enableColumnFilter:!1},enableRowSelection:!0,onRowSelectionChange:S,onPaginationChange:D,onSortingChange:B,onColumnFiltersChange:U,onColumnVisibilityChange:k,getCoreRowModel:(0,l.HT)(),getFilteredRowModel:(0,l.hM)(),getPaginationRowModel:(0,l.kW)(),getSortedRowModel:(0,l.h5)(),getFacetedRowModel:(0,l.kQ)(),getFacetedUniqueValues:(0,l.oS)(),getFacetedMinMaxValues:(0,l.tX)(),manualPagination:!0,manualSorting:!0,manualFiltering:!0}),shallow:w,debounceMs:y,throttleMs:g}}d.z.object({id:d.z.string(),value:d.z.union([d.z.string(),d.z.array(d.z.string())]),variant:d.z.enum(c.r.filterVariants),operator:d.z.enum(c.r.operators),filterId:d.z.string()})},39372:(e,t,a)=>{a.d(t,{V:()=>c});var n=a(52880);a(99004);var l=a(25782),r=a(54651),s=a(62054),o=a(86183);let i=()=>(0,n.jsx)(o.YJP,{className:"size-4","data-sentry-element":"ChevronLeftIcon","data-sentry-component":"LeftIcon","data-sentry-source-file":"calendar.tsx"}),d=()=>(0,n.jsx)(o.vKP,{className:"size-4","data-sentry-element":"ChevronRightIcon","data-sentry-component":"RightIcon","data-sentry-source-file":"calendar.tsx"});function c(e){let{className:t,classNames:a,showOutsideDays:o=!0,...c}=e;return(0,n.jsx)(l.hv,{showOutsideDays:o,className:(0,r.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,r.cn)((0,s.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,r.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===c.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,r.cn)((0,s.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:i,IconRight:d},...c,"data-sentry-element":"DayPicker","data-sentry-component":"Calendar","data-sentry-source-file":"calendar.tsx"})}},45550:(e,t,a)=>{a.d(t,{Table:()=>r,TableBody:()=>o,TableCell:()=>c,TableHead:()=>d,TableHeader:()=>s,TableRow:()=>i});var n=a(52880);a(99004);var l=a(54651);function r(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto","data-sentry-component":"Table","data-sentry-source-file":"table.tsx",children:(0,n.jsx)("table",{"data-slot":"table",className:(0,l.cn)("w-full caption-bottom text-sm",t),...a})})}function s(e){let{className:t,...a}=e;return(0,n.jsx)("thead",{"data-slot":"table-header",className:(0,l.cn)("[&_tr]:border-b",t),...a,"data-sentry-component":"TableHeader","data-sentry-source-file":"table.tsx"})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("tbody",{"data-slot":"table-body",className:(0,l.cn)("[&_tr:last-child]:border-0",t),...a,"data-sentry-component":"TableBody","data-sentry-source-file":"table.tsx"})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("tr",{"data-slot":"table-row",className:(0,l.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a,"data-sentry-component":"TableRow","data-sentry-source-file":"table.tsx"})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("th",{"data-slot":"table-head",className:(0,l.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableHead","data-sentry-source-file":"table.tsx"})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("td",{"data-slot":"table-cell",className:(0,l.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a,"data-sentry-component":"TableCell","data-sentry-source-file":"table.tsx"})}},60171:(e,t,a)=>{a.d(t,{I:()=>c,SQ:()=>d,_2:()=>u,hO:()=>m,lp:()=>p,mB:()=>f,rI:()=>o,ty:()=>i});var n=a(52880);a(99004);var l=a(83028),r=a(90502),s=a(54651);function o(e){let{...t}=e;return(0,n.jsx)(l.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function i(e){let{...t}=e;return(0,n.jsx)(l.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function d(e){let{className:t,sideOffset:a=4,...r}=e;return(0,n.jsx)(l.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,n.jsx)(l.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,n.jsx)(l.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,inset:a,variant:r="default",...o}=e;return(0,n.jsx)(l.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,children:a,checked:o,...i}=e;return(0,n.jsxs)(l.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,s.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...i,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,n.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(l.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,n.jsx)(r.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function p(e){let{className:t,inset:a,...r}=e;return(0,n.jsx)(l.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function f(e){let{className:t,...a}=e;return(0,n.jsx)(l.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},76143:(e,t,a)=>{a.d(t,{w:()=>B});var n=a(52880),l=a(99004),r=a(70819),s=a(78912),o=a(62054),i=a(39372),d=a(34901),c=a(35355);function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e)return"";try{var a,n,l;return new Intl.DateTimeFormat("en-US",{month:null!=(a=t.month)?a:"long",day:null!=(n=t.day)?n:"numeric",year:null!=(l=t.year)?l:"numeric",...t}).format(new Date(e))}catch(e){return""}}function m(e){return e&&"object"==typeof e&&!Array.isArray(e)}function p(e){if(!e)return;let t=new Date("string"==typeof e?Number(e):e);return Number.isNaN(t.getTime())?void 0:t}function f(e){return null==e?[]:Array.isArray(e)?e.map(e=>{if("number"==typeof e||"string"==typeof e)return e}):"string"==typeof e||"number"==typeof e?[e]:[]}function x(e){let{column:t,title:a,multiple:x}=e,y=t.getFilterValue(),g=l.useMemo(()=>{if(!y)return x?{from:void 0,to:void 0}:[];if(x){let e=f(y);return{from:p(e[0]),to:p(e[1])}}let e=p(f(y)[0]);return e?[e]:[]},[y,x]),b=l.useCallback(e=>{if(!e)return void t.setFilterValue(void 0);if(!x||"getTime"in e)!x&&"getTime"in e&&t.setFilterValue(e.getTime());else{var a,n;let l=null==(a=e.from)?void 0:a.getTime(),r=null==(n=e.to)?void 0:n.getTime();t.setFilterValue(l||r?[l,r]:void 0)}},[t,x]),v=l.useCallback(e=>{e.stopPropagation(),t.setFilterValue(void 0)},[t]),h=l.useMemo(()=>x?!!m(g)&&(g.from||g.to):!!Array.isArray(g)&&g.length>0,[x,g]),w=l.useCallback(e=>{var t;return e.from||e.to?e.from&&e.to?"".concat(u(e.from)," - ").concat(u(e.to)):u(null!=(t=e.from)?t:e.to):""},[]),j=l.useMemo(()=>{if(x){if(!m(g))return null;let e=g.from||g.to,t=e?w(g):"Select date range";return(0,n.jsxs)("span",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{children:a}),e&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),(0,n.jsx)("span",{children:t})]})]})}if(m(g))return null;let e=g.length>0,t=e?u(g[0]):"Select date";return(0,n.jsxs)("span",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{children:a}),e&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),(0,n.jsx)("span",{children:t})]})]})},[g,x,w,a]);return(0,n.jsxs)(d.AM,{"data-sentry-element":"Popover","data-sentry-component":"DataTableDateFilter","data-sentry-source-file":"data-table-date-filter.tsx",children:[(0,n.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-date-filter.tsx",children:(0,n.jsxs)(o.$,{variant:"outline",size:"sm",className:"border-dashed","data-sentry-element":"Button","data-sentry-source-file":"data-table-date-filter.tsx",children:[h?(0,n.jsx)("div",{role:"button","aria-label":"Clear ".concat(a," filter"),tabIndex:0,onClick:v,className:"focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none",children:(0,n.jsx)(r.A,{})}):(0,n.jsx)(s.A,{}),j]})}),(0,n.jsx)(d.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-date-filter.tsx",children:x?(0,n.jsx)(i.V,{initialFocus:!0,mode:"range",selected:m(g)?g:{from:void 0,to:void 0},onSelect:b}):(0,n.jsx)(i.V,{initialFocus:!0,mode:"single",selected:m(g)?void 0:g[0],onSelect:b})})]})}var y=a(46189),g=a(88151),b=a(23933),v=a(68050),h=a(54651);function w(e){let{className:t,...a}=e;return(0,n.jsx)(b.uB,{"data-slot":"command",className:(0,h.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...a,"data-sentry-element":"CommandPrimitive","data-sentry-component":"Command","data-sentry-source-file":"command.tsx"})}function j(e){let{className:t,...a}=e;return(0,n.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3","data-sentry-component":"CommandInput","data-sentry-source-file":"command.tsx",children:[(0,n.jsx)(v.A,{className:"size-4 shrink-0 opacity-50","data-sentry-element":"SearchIcon","data-sentry-source-file":"command.tsx"}),(0,n.jsx)(b.uB.Input,{"data-slot":"command-input",className:(0,h.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",t),...a,"data-sentry-element":"CommandPrimitive.Input","data-sentry-source-file":"command.tsx"})]})}function N(e){let{className:t,...a}=e;return(0,n.jsx)(b.uB.List,{"data-slot":"command-list",className:(0,h.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",t),...a,"data-sentry-element":"CommandPrimitive.List","data-sentry-component":"CommandList","data-sentry-source-file":"command.tsx"})}function C(e){let{...t}=e;return(0,n.jsx)(b.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t,"data-sentry-element":"CommandPrimitive.Empty","data-sentry-component":"CommandEmpty","data-sentry-source-file":"command.tsx"})}function I(e){let{className:t,...a}=e;return(0,n.jsx)(b.uB.Group,{"data-slot":"command-group",className:(0,h.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...a,"data-sentry-element":"CommandPrimitive.Group","data-sentry-component":"CommandGroup","data-sentry-source-file":"command.tsx"})}function S(e){let{className:t,...a}=e;return(0,n.jsx)(b.uB.Separator,{"data-slot":"command-separator",className:(0,h.cn)("bg-border -mx-1 h-px",t),...a,"data-sentry-element":"CommandPrimitive.Separator","data-sentry-component":"CommandSeparator","data-sentry-source-file":"command.tsx"})}function z(e){let{className:t,...a}=e;return(0,n.jsx)(b.uB.Item,{"data-slot":"command-item",className:(0,h.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a,"data-sentry-element":"CommandPrimitive.Item","data-sentry-component":"CommandItem","data-sentry-source-file":"command.tsx"})}a(77362);var k=a(86183);function P(e){let{column:t,title:a,options:s,multiple:i}=e,[u,m]=l.useState(!1),p=null==t?void 0:t.getFilterValue(),f=l.useMemo(()=>new Set(Array.isArray(p)?p:[]),[p]),x=l.useCallback((e,a)=>{if(t)if(i){let n=new Set(f);a?n.delete(e.value):n.add(e.value);let l=Array.from(n);t.setFilterValue(l.length?l:void 0)}else t.setFilterValue(a?void 0:[e.value]),m(!1)},[t,i,f]),b=l.useCallback(e=>{null==e||e.stopPropagation(),null==t||t.setFilterValue(void 0)},[t]);return(0,n.jsxs)(d.AM,{open:u,onOpenChange:m,"data-sentry-element":"Popover","data-sentry-component":"DataTableFacetedFilter","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(0,n.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-faceted-filter.tsx",children:(0,n.jsxs)(o.$,{variant:"outline",size:"sm",className:"border-dashed","data-sentry-element":"Button","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(null==f?void 0:f.size)>0?(0,n.jsx)("div",{role:"button","aria-label":"Clear ".concat(a," filter"),tabIndex:0,onClick:b,className:"focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none",children:(0,n.jsx)(r.A,{})}):(0,n.jsx)(y.A,{}),a,(null==f?void 0:f.size)>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),(0,n.jsx)(g.E,{variant:"secondary",className:"rounded-sm px-1 font-normal lg:hidden",children:f.size}),(0,n.jsx)("div",{className:"hidden items-center gap-1 lg:flex",children:f.size>2?(0,n.jsxs)(g.E,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:[f.size," selected"]}):s.filter(e=>f.has(e.value)).map(e=>(0,n.jsx)(g.E,{variant:"secondary",className:"rounded-sm px-1 font-normal",children:e.label},e.value))})]})]})}),(0,n.jsx)(d.hl,{className:"w-[12.5rem] p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-faceted-filter.tsx",children:(0,n.jsxs)(w,{"data-sentry-element":"Command","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(0,n.jsx)(j,{placeholder:a,"data-sentry-element":"CommandInput","data-sentry-source-file":"data-table-faceted-filter.tsx"}),(0,n.jsxs)(N,{className:"max-h-full","data-sentry-element":"CommandList","data-sentry-source-file":"data-table-faceted-filter.tsx",children:[(0,n.jsx)(C,{"data-sentry-element":"CommandEmpty","data-sentry-source-file":"data-table-faceted-filter.tsx",children:"No results found."}),(0,n.jsx)(I,{className:"max-h-[18.75rem] overflow-x-hidden overflow-y-auto","data-sentry-element":"CommandGroup","data-sentry-source-file":"data-table-faceted-filter.tsx",children:s.map(e=>{let t=f.has(e.value);return(0,n.jsxs)(z,{onSelect:()=>x(e,t),children:[(0,n.jsx)("div",{className:(0,h.cn)("border-primary flex size-4 items-center justify-center rounded-sm border",t?"bg-primary":"opacity-50 [&_svg]:invisible"),children:(0,n.jsx)(k.Srz,{})}),e.icon&&(0,n.jsx)(e.icon,{}),(0,n.jsx)("span",{className:"truncate",children:e.label}),e.count&&(0,n.jsx)("span",{className:"ml-auto font-mono text-xs",children:e.count})]},e.value)})}),f.size>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(S,{}),(0,n.jsx)(I,{children:(0,n.jsx)(z,{onSelect:()=>b(),className:"justify-center text-center",children:"Clear filters"})})]})]})]})})]})}var M=a(42094),T=a(84692),F=a(74453);function A(e){let{className:t,defaultValue:a,value:r,min:s=0,max:o=100,...i}=e,d=l.useMemo(()=>Array.isArray(r)?r:Array.isArray(a)?a:[s,o],[r,a,s,o]);return(0,n.jsxs)(F.bL,{"data-slot":"slider",defaultValue:a,value:r,min:s,max:o,className:(0,h.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",t),...i,"data-sentry-element":"SliderPrimitive.Root","data-sentry-component":"Slider","data-sentry-source-file":"slider.tsx",children:[(0,n.jsx)(F.CC,{"data-slot":"slider-track",className:(0,h.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),"data-sentry-element":"SliderPrimitive.Track","data-sentry-source-file":"slider.tsx",children:(0,n.jsx)(F.Q6,{"data-slot":"slider-range",className:(0,h.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full"),"data-sentry-element":"SliderPrimitive.Range","data-sentry-source-file":"slider.tsx"})}),Array.from({length:d.length},(e,t)=>(0,n.jsx)(F.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},t))]})}function D(e){return Array.isArray(e)&&2===e.length&&"number"==typeof e[0]&&"number"==typeof e[1]}function _(e){var t,a,s,i;let{column:u,title:m}=e,p=l.useId(),f=D(u.getFilterValue())?u.getFilterValue():void 0,x=null==(t=u.columnDef.meta)?void 0:t.range,g=null==(a=u.columnDef.meta)?void 0:a.unit,{min:b,max:v,step:w}=l.useMemo(()=>{let e=0,t=100;if(x&&D(x))[e,t]=x;else{let a=u.getFacetedMinMaxValues();if(a&&Array.isArray(a)&&2===a.length){let[n,l]=a;"number"==typeof n&&"number"==typeof l&&(e=n,t=l)}}let a=t-e;return{min:e,max:t,step:a<=20?1:a<=100?Math.ceil(a/20):Math.ceil(a/50)}},[u,x]),j=l.useMemo(()=>null!=f?f:[b,v],[f,b,v]),N=l.useCallback(e=>e.toLocaleString(void 0,{maximumFractionDigits:0}),[]),C=l.useCallback(e=>{let t=Number(e.target.value);!Number.isNaN(t)&&t>=b&&t<=j[1]&&u.setFilterValue([t,j[1]])},[u,b,j]),I=l.useCallback(e=>{let t=Number(e.target.value);!Number.isNaN(t)&&t<=v&&t>=j[0]&&u.setFilterValue([j[0],t])},[u,v,j]),S=l.useCallback(e=>{Array.isArray(e)&&2===e.length&&u.setFilterValue(e)},[u]),z=l.useCallback(e=>{e.target instanceof HTMLDivElement&&e.stopPropagation(),u.setFilterValue(void 0)},[u]);return(0,n.jsxs)(d.AM,{"data-sentry-element":"Popover","data-sentry-component":"DataTableSliderFilter","data-sentry-source-file":"data-table-slider-filter.tsx",children:[(0,n.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-slider-filter.tsx",children:(0,n.jsxs)(o.$,{variant:"outline",size:"sm",className:"border-dashed","data-sentry-element":"Button","data-sentry-source-file":"data-table-slider-filter.tsx",children:[f?(0,n.jsx)("div",{role:"button","aria-label":"Clear ".concat(m," filter"),tabIndex:0,className:"focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none",onClick:z,children:(0,n.jsx)(r.A,{})}):(0,n.jsx)(y.A,{}),(0,n.jsx)("span",{children:m}),f?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.Separator,{orientation:"vertical",className:"mx-0.5 data-[orientation=vertical]:h-4"}),N(f[0])," -"," ",N(f[1]),g?" ".concat(g):""]}):null]})}),(0,n.jsxs)(d.hl,{align:"start",className:"flex w-auto flex-col gap-4","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-slider-filter.tsx",children:[(0,n.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,n.jsx)("p",{className:"leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:m}),(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(T.J,{htmlFor:"".concat(p,"-from"),className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"data-table-slider-filter.tsx",children:"From"}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(M.p,{id:"".concat(p,"-from"),type:"number","aria-valuemin":b,"aria-valuemax":v,inputMode:"numeric",pattern:"[0-9]*",placeholder:b.toString(),min:b,max:v,value:null==(s=j[0])?void 0:s.toString(),onChange:C,className:(0,h.cn)("h-8 w-24",g&&"pr-8"),"data-sentry-element":"Input","data-sentry-source-file":"data-table-slider-filter.tsx"}),g&&(0,n.jsx)("span",{className:"bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm",children:g})]}),(0,n.jsx)(T.J,{htmlFor:"".concat(p,"-to"),className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"data-table-slider-filter.tsx",children:"to"}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(M.p,{id:"".concat(p,"-to"),type:"number","aria-valuemin":b,"aria-valuemax":v,inputMode:"numeric",pattern:"[0-9]*",placeholder:v.toString(),min:b,max:v,value:null==(i=j[1])?void 0:i.toString(),onChange:I,className:(0,h.cn)("h-8 w-24",g&&"pr-8"),"data-sentry-element":"Input","data-sentry-source-file":"data-table-slider-filter.tsx"}),g&&(0,n.jsx)("span",{className:"bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm",children:g})]})]}),(0,n.jsxs)(T.J,{htmlFor:"".concat(p,"-slider"),className:"sr-only","data-sentry-element":"Label","data-sentry-source-file":"data-table-slider-filter.tsx",children:[m," slider"]}),(0,n.jsx)(A,{id:"".concat(p,"-slider"),min:b,max:v,step:w,value:j,onValueChange:S,"data-sentry-element":"Slider","data-sentry-source-file":"data-table-slider-filter.tsx"})]}),(0,n.jsx)(o.$,{"aria-label":"Clear ".concat(m," filter"),variant:"outline",size:"sm",onClick:z,"data-sentry-element":"Button","data-sentry-source-file":"data-table-slider-filter.tsx",children:"Clear"})]})]})}var V=a(12320);function R(e){let{table:t}=e,a=l.useMemo(()=>t.getAllColumns().filter(e=>void 0!==e.accessorFn&&e.getCanHide()),[t]);return(0,n.jsxs)(d.AM,{"data-sentry-element":"Popover","data-sentry-component":"DataTableViewOptions","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,n.jsx)(d.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"data-table-view-options.tsx",children:(0,n.jsxs)(o.$,{"aria-label":"Toggle columns",role:"combobox",variant:"outline",size:"sm",className:"ml-auto hidden h-8 lg:flex","data-sentry-element":"Button","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,n.jsx)(V.A,{"data-sentry-element":"Settings2","data-sentry-source-file":"data-table-view-options.tsx"}),"View",(0,n.jsx)(k.TBE,{className:"ml-auto opacity-50","data-sentry-element":"CaretSortIcon","data-sentry-source-file":"data-table-view-options.tsx"})]})}),(0,n.jsx)(d.hl,{align:"end",className:"w-44 p-0","data-sentry-element":"PopoverContent","data-sentry-source-file":"data-table-view-options.tsx",children:(0,n.jsxs)(w,{"data-sentry-element":"Command","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,n.jsx)(j,{placeholder:"Search columns...","data-sentry-element":"CommandInput","data-sentry-source-file":"data-table-view-options.tsx"}),(0,n.jsxs)(N,{"data-sentry-element":"CommandList","data-sentry-source-file":"data-table-view-options.tsx",children:[(0,n.jsx)(C,{"data-sentry-element":"CommandEmpty","data-sentry-source-file":"data-table-view-options.tsx",children:"No columns found."}),(0,n.jsx)(I,{"data-sentry-element":"CommandGroup","data-sentry-source-file":"data-table-view-options.tsx",children:a.map(e=>{var t,a;return(0,n.jsxs)(z,{onSelect:()=>e.toggleVisibility(!e.getIsVisible()),children:[(0,n.jsx)("span",{className:"truncate",children:null!=(a=null==(t=e.columnDef.meta)?void 0:t.label)?a:e.id}),(0,n.jsx)(k.Srz,{className:(0,h.cn)("ml-auto size-4 shrink-0",e.getIsVisible()?"opacity-100":"opacity-0")})]},e.id)})})]})]})})]})}function B(e){let{table:t,children:a,className:r,...s}=e,i=t.getState().columnFilters.length>0,d=l.useMemo(()=>t.getAllColumns().filter(e=>e.getCanFilter()),[t]),c=l.useCallback(()=>{t.resetColumnFilters()},[t]);return(0,n.jsxs)("div",{role:"toolbar","aria-orientation":"horizontal",className:(0,h.cn)("flex w-full items-start justify-between gap-2 p-1",r),...s,"data-sentry-component":"DataTableToolbar","data-sentry-source-file":"data-table-toolbar.tsx",children:[(0,n.jsxs)("div",{className:"flex flex-1 flex-wrap items-center gap-2",children:[d.map(e=>(0,n.jsx)(E,{column:e},e.id)),i&&(0,n.jsxs)(o.$,{"aria-label":"Reset filters",variant:"outline",size:"sm",className:"border-dashed",onClick:c,children:[(0,n.jsx)(k.MKb,{}),"Reset"]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[a,(0,n.jsx)(R,{table:t,"data-sentry-element":"DataTableViewOptions","data-sentry-source-file":"data-table-toolbar.tsx"})]})]})}function E(e){let{column:t}=e;{let e=t.columnDef.meta;return l.useCallback(()=>{var a,l,r,s,o,i,d,c;if(!(null==e?void 0:e.variant))return null;switch(e.variant){case"text":return(0,n.jsx)(M.p,{placeholder:null!=(a=e.placeholder)?a:e.label,value:null!=(l=t.getFilterValue())?l:"",onChange:e=>t.setFilterValue(e.target.value),className:"h-8 w-40 lg:w-56"});case"number":return(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(M.p,{type:"number",inputMode:"numeric",placeholder:null!=(r=e.placeholder)?r:e.label,value:null!=(s=t.getFilterValue())?s:"",onChange:e=>t.setFilterValue(e.target.value),className:(0,h.cn)("h-8 w-[120px]",e.unit&&"pr-8")}),e.unit&&(0,n.jsx)("span",{className:"bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm",children:e.unit})]});case"range":return(0,n.jsx)(_,{column:t,title:null!=(o=e.label)?o:t.id});case"date":case"dateRange":return(0,n.jsx)(x,{column:t,title:null!=(i=e.label)?i:t.id,multiple:"dateRange"===e.variant});case"select":case"multiSelect":return(0,n.jsx)(P,{column:t,title:null!=(d=e.label)?d:t.id,options:null!=(c=e.options)?c:[],multiple:"multiSelect"===e.variant});default:return null}},[t,e])()}}},97164:(e,t,a)=>{a.d(t,{r:()=>n});let n={textOperators:[{label:"Contains",value:"iLike"},{label:"Does not contain",value:"notILike"},{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],numericOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is less than",value:"lt"},{label:"Is less than or equal to",value:"lte"},{label:"Is greater than",value:"gt"},{label:"Is greater than or equal to",value:"gte"},{label:"Is between",value:"isBetween"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],dateOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is before",value:"lt"},{label:"Is after",value:"gt"},{label:"Is on or before",value:"lte"},{label:"Is on or after",value:"gte"},{label:"Is between",value:"isBetween"},{label:"Is relative to today",value:"isRelativeToToday"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],selectOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],multiSelectOperators:[{label:"Has any of",value:"inArray"},{label:"Has none of",value:"notInArray"},{label:"Is empty",value:"isEmpty"},{label:"Is not empty",value:"isNotEmpty"}],booleanOperators:[{label:"Is",value:"eq"},{label:"Is not",value:"ne"}],sortOrders:[{label:"Asc",value:"asc"},{label:"Desc",value:"desc"}],filterVariants:["text","number","range","date","dateRange","boolean","select","multiSelect"],operators:["iLike","notILike","eq","ne","inArray","notInArray","isEmpty","isNotEmpty","lt","lte","gt","gte","isBetween","isRelativeToToday"],joinOperators:["and","or"]}},99098:(e,t,a)=>{a.d(t,{c:()=>l});var n=a(99004);function l(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>function(){for(var e,a=arguments.length,n=Array(a),l=0;l<a;l++)n[l]=arguments[l];return null==(e=t.current)?void 0:e.call(t,...n)},[])}}}]);