try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="c03d69d1-f903-4acc-81ca-8fa5a510799d",t._sentryDebugIdIdentifier="sentry-dbid-c03d69d1-f903-4acc-81ca-8fa5a510799d")}catch(t){}exports.id=8721,exports.ids=[8721],exports.modules={805:(t,e,r)=>{"use strict";r.d(e,{A:()=>function t(){var e=new n,r=[],i=[],o=c;function u(t){let n=e.get(t);if(void 0===n){if(o!==c)return o;e.set(t,n=r.push(t)-1)}return i[n%i.length]}return u.domain=function(t){if(!arguments.length)return r.slice();for(let i of(r=[],e=new n,t))e.has(i)||e.set(i,r.push(i)-1);return u},u.range=function(t){return arguments.length?(i=Array.from(t),u):i.slice()},u.unknown=function(t){return arguments.length?(o=t,u):o},u.copy=function(){return t(r,i).unknown(o)},a.C.apply(u,arguments),u},h:()=>c});class n extends Map{constructor(t,e=o){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(i(this,t))}has(t){return super.has(i(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function i({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function o(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(45656);let c=Symbol("implicit")},1024:(t,e,r)=>{"use strict";r.d(e,{F:()=>z});var n=r(60222),i=r.n(n),o=r(86704),a=r(88336),c=r.n(a),u=r(29736),l=r.n(u),s=r(8875),f=r.n(s),p=r(2802),h=r.n(p),d=r(70991),y=r(42956),v=r(28118),m=r(87158),b=r(31347),g=r(43371),x=r(89389),O=r(23872),w=r(10427),j=r(57013),A=r(1511),S=r(12810),P=r(28511),E=r(43891),k=r(91554);function M(t){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(){return(T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function C(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,L(n.key),n)}}function D(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(D=function(){return!!t})()}function N(t){return(N=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function B(t,e){return(B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function R(t,e,r){return(e=L(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function L(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}var z=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=N(r),R(e=function(t,e){if(e&&("object"===M(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,D()?Reflect.construct(r,i||[],N(this).constructor):r.apply(this,i)),"pieRef",null),R(e,"sectorRefs",[]),R(e,"id",(0,A.NF)("recharts-pie-")),R(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),h()(t)&&t()}),R(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),h()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&B(n,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,r=e.label,o=e.labelLine,a=e.dataKey,c=e.valueKey,u=(0,O.J9)(this.props,!1),l=(0,O.J9)(r,!1),s=(0,O.J9)(o,!1),p=r&&r.offsetRadius||20,h=t.map(function(t,e){var h=(t.startAngle+t.endAngle)/2,d=(0,j.IZ)(t.cx,t.cy,t.outerRadius+p,h),v=C(C(C(C({},u),t),{},{stroke:"none"},l),{},{index:e,textAnchor:n.getTextAnchor(d.x,t.cx)},d),m=C(C(C(C({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:e,points:[(0,j.IZ)(t.cx,t.cy,t.outerRadius,h),d]}),b=a;return f()(a)&&f()(c)?b="value":f()(a)&&(b=c),i().createElement(y.W,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&n.renderLabelLineItem(o,m,"line"),n.renderLabelItem(r,v,(0,S.kr)(t,b)))});return i().createElement(y.W,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,o=r.blendStroke,a=r.inactiveShape;return t.map(function(r,c){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==t.length)return null;var u=e.isActiveIndex(c),l=a&&e.hasActiveIndex()?a:null,s=C(C({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return i().createElement(y.W,T({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},(0,E.XC)(e.props,r,c),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),i().createElement(k.yp,T({option:u?n:l,isActive:u,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,a=e.animationBegin,u=e.animationDuration,l=e.animationEasing,s=e.animationId,f=this.state,p=f.prevSectors,h=f.prevIsAnimationActive;return i().createElement(o.Ay,{begin:a,duration:u,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var n=e.t,o=[],a=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=p&&p[e],i=e>0?c()(t,"paddingAngle",0):0;if(r){var u=(0,A.Dj)(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=C(C({},t),{},{startAngle:a+i,endAngle:a+u(n)+i});o.push(l),a=l.endAngle}else{var s=t.endAngle,f=t.startAngle,h=(0,A.Dj)(0,s-f)(n),d=C(C({},t),{},{startAngle:a+i,endAngle:a+h+i});o.push(d),a=d.endAngle}}),i().createElement(y.W,null,t.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return r&&e&&e.length&&(!n||!l()(n,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,o=e.className,a=e.label,c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!(0,A.Et)(c)||!(0,A.Et)(u)||!(0,A.Et)(l)||!(0,A.Et)(s))return null;var h=(0,d.A)("recharts-pie",o);return i().createElement(y.W,{tabIndex:this.props.rootTabIndex,className:h,ref:function(e){t.pieRef=e}},this.renderSectors(),a&&this.renderLabels(n),b.J.renderCallByParent(this.props,null,!1),(!f||p)&&g.Z.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(i().isValidElement(t))return i().cloneElement(t,e);if(h()(t))return t(e);var n=(0,d.A)("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return i().createElement(v.I,T({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(i().isValidElement(t))return i().cloneElement(t,e);var n=r;if(h()(t)&&(n=t(e),i().isValidElement(n)))return n;var o=(0,d.A)("recharts-pie-label-text","boolean"==typeof t||h()(t)?"":t.className);return i().createElement(m.E,T({},e,{alignmentBaseline:"middle",className:o}),n)}}],e&&I(n.prototype,e),r&&I(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);R(z,"displayName","Pie"),R(z,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!w.m.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),R(z,"parseDeltaAngle",function(t,e){return(0,A.sA)(e-t)*Math.min(Math.abs(e-t),360)}),R(z,"getRealPieData",function(t){var e=t.data,r=t.children,n=(0,O.J9)(t,!1),i=(0,O.aS)(r,x.f);return e&&e.length?e.map(function(t,e){return C(C(C({payload:t},n),t),i&&i[e]&&i[e].props)}):i&&i.length?i.map(function(t){return C(C({},n),t.props)}):[]}),R(z,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,i=e.width,o=e.height,a=(0,j.lY)(i,o);return{cx:n+(0,A.F4)(t.cx,i,i/2),cy:r+(0,A.F4)(t.cy,o,o/2),innerRadius:(0,A.F4)(t.innerRadius,a,0),outerRadius:(0,A.F4)(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(i*i+o*o)/2}}),R(z,"getComposedData",function(t){var e,r,n=t.item,i=t.offset,o=void 0!==n.type.defaultProps?C(C({},n.type.defaultProps),n.props):n.props,a=z.getRealPieData(o);if(!a||!a.length)return null;var c=o.cornerRadius,u=o.startAngle,l=o.endAngle,s=o.paddingAngle,p=o.dataKey,h=o.nameKey,d=o.valueKey,y=o.tooltipType,v=Math.abs(o.minAngle),m=z.parseCoordinateOfPie(o,i),b=z.parseDeltaAngle(u,l),g=Math.abs(b),x=p;f()(p)&&f()(d)?((0,P.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x="value"):f()(p)&&((0,P.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x=d);var O=a.filter(function(t){return 0!==(0,S.kr)(t,x,0)}).length,w=g-O*v-(g>=360?O:O-1)*s,E=a.reduce(function(t,e){var r=(0,S.kr)(e,x,0);return t+((0,A.Et)(r)?r:0)},0);return E>0&&(e=a.map(function(t,e){var n,i=(0,S.kr)(t,x,0),o=(0,S.kr)(t,h,e),a=((0,A.Et)(i)?i:0)/E,l=(n=e?r.endAngle+(0,A.sA)(b)*s*(0!==i):u)+(0,A.sA)(b)*((0!==i?v:0)+a*w),f=(n+l)/2,p=(m.innerRadius+m.outerRadius)/2,d=[{name:o,value:i,payload:t,dataKey:x,type:y}],g=(0,j.IZ)(m.cx,m.cy,p,f);return r=C(C(C({percent:a,cornerRadius:c,name:o,tooltipPayload:d,midAngle:f,middleRadius:p,tooltipPosition:g},t),m),{},{value:(0,S.kr)(t,x),startAngle:n,endAngle:l,payload:t,paddingAngle:(0,A.sA)(b)*s})})),C(C({},m),{},{sectors:e,data:a})})},1511:(t,e,r)=>{"use strict";r.d(e,{CG:()=>g,Dj:()=>x,Et:()=>h,F4:()=>m,NF:()=>v,_3:()=>p,eP:()=>O,lX:()=>b,sA:()=>f,vh:()=>d});var n=r(77719),i=r.n(n),o=r(83201),a=r.n(o),c=r(88336),u=r.n(c),l=r(50383),s=r.n(l),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return i()(t)&&t.indexOf("%")===t.length-1},h=function(t){return s()(t)&&!a()(t)},d=function(t){return h(t)||i()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!i()(t))return n;if(p(t)){var c=t.indexOf("%");r=e*parseFloat(t.slice(0,c))/100}else r=+t;return a()(r)&&(r=n),o&&r>e&&(r=e),r},b=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},g=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},x=function(t,e){return h(t)&&h(e)?function(r){return t+r*(e-t)}:function(){return e}};function O(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):u()(t,e))===r}):null}},2398:(t,e,r)=>{var n=r(97657),i=r(63305);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},2438:(t,e,r)=>{var n=r(15305),i=r(56097);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},3380:(t,e,r)=>{"use strict";r.d(e,{c:()=>l});var n=r(60222),i=r.n(n),o=r(70991),a=r(43891),c=r(23872);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l=function(t){var e=t.cx,r=t.cy,n=t.r,l=t.className,s=(0,o.A)("recharts-dot",l);return e===+e&&r===+r&&n===+n?i().createElement("circle",u({},(0,c.J9)(t,!1),(0,a._U)(t),{className:s,cx:e,cy:r,r:n})):null}},4854:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{b:()=>n})},5283:(t,e,r)=>{var n=r(51086),i=r(89069),o=r(97657),a=r(5517),c=r(7348);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},5398:(t,e,r)=>{"use strict";r.d(e,{m:()=>$});var n=r(60222),i=r.n(n),o=r(76111),a=r.n(o),c=r(8875),u=r.n(c),l=r(70991),s=r(1511);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function v(t){return Array.isArray(t)&&(0,s.vh)(t[0])&&(0,s.vh)(t[1])?t.join(" ~ "):t}var m=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,c=void 0===o?{}:o,f=t.labelStyle,d=t.payload,m=t.formatter,b=t.itemSorter,g=t.wrapperClassName,x=t.labelClassName,O=t.label,w=t.labelFormatter,j=t.accessibilityLayer,A=y({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),S=y({margin:0},void 0===f?{}:f),P=!u()(O),E=P?O:"",k=(0,l.A)("recharts-default-tooltip",g),M=(0,l.A)("recharts-tooltip-label",x);return P&&w&&null!=d&&(E=w(O,d)),i().createElement("div",p({className:k,style:A},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),i().createElement("p",{className:M,style:S},i().isValidElement(E)?E:"".concat(E)),function(){if(d&&d.length){var t=(b?a()(d,b):d).map(function(t,e){if("none"===t.type)return null;var n=y({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},c),o=t.formatter||m||v,a=t.value,u=t.name,l=a,f=u;if(o&&null!=l&&null!=f){var p=o(a,u,t,e,d);if(Array.isArray(p)){var b=function(t){if(Array.isArray(t))return t}(p)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(p,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();l=b[0],f=b[1]}else l=p}return i().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},(0,s.vh)(f)?i().createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,s.vh)(f)?i().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,i().createElement("span",{className:"recharts-tooltip-item-value"},l),i().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return i().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function g(t,e,r){var n;return(n=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==b(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var x="recharts-tooltip-wrapper",O={visibility:"hidden"};function w(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(o&&(0,s.Et)(o[n]))return o[n];var f=r[n]-c-i,p=r[n]+i;return e[n]?a[n]?f:p:a[n]?f<u[n]?Math.max(p,u[n]):Math.max(f,u[n]):p+c>u[n]+l?Math.max(f,u[n]):Math.max(p,u[n])}function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){M(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(P=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function k(t,e){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function M(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}var _=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=E(e),M(t=function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,P()?Reflect.construct(e,n||[],E(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),M(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&k(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,a,c,u,f,p,h,d,y,v,m,b,j,A,P,E=this,k=this.props,M=k.active,T=k.allowEscapeViewBox,_=k.animationDuration,C=k.animationEasing,I=k.children,D=k.coordinate,N=k.hasPayload,B=k.isAnimationActive,R=k.offset,L=k.position,z=k.reverseDirection,U=k.useTranslate3d,F=k.viewBox,W=k.wrapperStyle,$=(d=(t={allowEscapeViewBox:T,coordinate:D,offsetTopLeft:R,position:L,reverseDirection:z,tooltipBox:this.state.lastBoundingBox,useTranslate3d:U,viewBox:F}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,b=t.reverseDirection,j=t.tooltipBox,A=t.useTranslate3d,P=t.viewBox,j.height>0&&j.width>0&&y?(r=(e={translateX:p=w({allowEscapeViewBox:d,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:b,tooltipDimension:j.width,viewBox:P,viewBoxDimension:P.width}),translateY:h=w({allowEscapeViewBox:d,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:b,tooltipDimension:j.height,viewBox:P,viewBoxDimension:P.height}),useTranslate3d:A}).translateX,n=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):f=O,{cssProperties:f,cssClasses:(a=(o={translateX:p,translateY:h,coordinate:y}).coordinate,c=o.translateX,u=o.translateY,(0,l.A)(x,g(g(g(g({},"".concat(x,"-right"),(0,s.Et)(c)&&a&&(0,s.Et)(a.x)&&c>=a.x),"".concat(x,"-left"),(0,s.Et)(c)&&a&&(0,s.Et)(a.x)&&c<a.x),"".concat(x,"-bottom"),(0,s.Et)(u)&&a&&(0,s.Et)(a.y)&&u>=a.y),"".concat(x,"-top"),(0,s.Et)(u)&&a&&(0,s.Et)(a.y)&&u<a.y)))}),q=$.cssClasses,X=$.cssProperties,H=S(S({transition:B&&M?"transform ".concat(_,"ms ").concat(C):void 0},X),{},{pointerEvents:"none",visibility:!this.state.dismissed&&M&&N?"visible":"hidden",position:"absolute",top:0,left:0},W);return i().createElement("div",{tabIndex:-1,className:q,style:H,ref:function(t){E.wrapperNode=t}},I)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),C=r(10427),I=r(38506);function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){U(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(R=function(){return!!t})()}function L(t){return(L=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function z(t,e){return(z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function U(t,e,r){return(e=F(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function F(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}function W(t){return t.dataKey}var $=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=L(t),function(t,e){if(e&&("object"===D(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,R()?Reflect.construct(t,e||[],L(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&z(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,a=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,O=null!=h?h:[];s&&O.length&&(O=(0,I.s)(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,W));var w=O.length>0;return i().createElement(_,{allowEscapeViewBox:o,animationDuration:a,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:b,viewBox:g,wrapperStyle:x},(t=B(B({},this.props),{},{payload:O}),i().isValidElement(u)?i().cloneElement(u,t):"function"==typeof u?i().createElement(u,t):i().createElement(m,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,F(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);U($,"displayName","Tooltip"),U($,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!C.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},8875:t=>{t.exports=function(t){return null==t}},10427:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!0,get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},11468:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(19e3).A)("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]])},11634:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var r in e)Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}(e,{PARALLEL_ROUTE_DEFAULT_PATH:function(){return i},default:function(){return o}});let n=r(94653),i="next/dist/client/components/parallel-route-default.js";function o(){(0,n.notFound)()}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},12810:(t,e,r)=>{"use strict";r.d(e,{s0:()=>iO,gH:()=>im,YB:()=>i_,HQ:()=>iM,xi:()=>iC,Hj:()=>iH,BX:()=>ix,tA:()=>ig,DW:()=>iU,y2:()=>iz,nb:()=>iL,PW:()=>iP,Ay:()=>iv,vf:()=>iA,Mk:()=>iW,Ps:()=>ib,Mn:()=>iB,kA:()=>iF,Rh:()=>iE,w7:()=>iR,zb:()=>iK,kr:()=>iy,_L:()=>iS,KC:()=>iV,A1:()=>ij,W7:()=>iT,AQ:()=>iX,_f:()=>iI});var n={};r.r(n),r.d(n,{scaleBand:()=>i.A,scaleDiverging:()=>function t(){var e=tk(rX()(tl));return e.copy=function(){return rW(e,t())},tv.K.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tR(rX()).domain([.1,1,10]);return e.copy=function(){return rW(e,t()).base(e.base())},tv.K.apply(e,arguments)},scaleDivergingPow:()=>rH,scaleDivergingSqrt:()=>rV,scaleDivergingSymlog:()=>function t(){var e=tU(rX());return e.copy=function(){return rW(e,t()).constant(e.constant())},tv.K.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,tc),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,tc):[0,1],tk(n)},scaleImplicit:()=>tF.h,scaleLinear:()=>tM,scaleLog:()=>function t(){let e=tR(td()).domain([1,10]);return e.copy=()=>th(e,t()).base(e.base()),tv.C.apply(e,arguments),e},scaleOrdinal:()=>tF.A,scalePoint:()=>i.z,scalePow:()=>tH,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=v){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[b(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(p),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tv.C.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[b(o,t,0,i)]:e}function u(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tv.C.apply(tk(c),arguments)},scaleRadial:()=>function t(){var e,r=ty(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(tK(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,tc)).map(tK)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},tv.C.apply(o,arguments),tk(o)},scaleSequential:()=>function t(){var e=tk(rF()(tl));return e.copy=function(){return rW(e,t())},tv.K.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tR(rF()).domain([1,10]);return e.copy=function(){return rW(e,t()).base(e.base())},tv.K.apply(e,arguments)},scaleSequentialPow:()=>r$,scaleSequentialQuantile:()=>function t(){var e=[],r=tl;function n(t){if(null!=t&&!isNaN(t*=1))return r((b(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(p),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return tY(t);if(e>=1)return tG(t);var n,i=(n-1)*e,o=Math.floor(i),a=tG((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?tJ:function(t=p){if(t===p)return tJ;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(i,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,o)}let a=e[r],c=n,u=i;for(tZ(e,n,r),o(e[i],a)>0&&tZ(e,n,i);c<u;){for(tZ(e,c,u),++c,--u;0>o(e[c],a);)++c;for(;o(e[u],a)>0;)--u}0===o(e[n],a)?tZ(e,n,u):tZ(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,o).subarray(0,o+1));return a+(tY(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tv.K.apply(n,arguments)},scaleSequentialSqrt:()=>rq,scaleSequentialSymlog:()=>function t(){var e=tU(rF());return e.copy=function(){return rW(e,t()).constant(e.constant())},tv.K.apply(e,arguments)},scaleSqrt:()=>tV,scaleSymlog:()=>function t(){var e=tU(td());return e.copy=function(){return th(e,t()).constant(e.constant())},tv.C.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[b(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},tv.C.apply(o,arguments)},scaleTime:()=>rz,scaleUtc:()=>rU,tickFormat:()=>tE});var i=r(17552);let o=Math.sqrt(50),a=Math.sqrt(10),c=Math.sqrt(2);function u(t,e,r){let n,i,l,s=(e-t)/Math.max(0,r),f=Math.floor(Math.log10(s)),p=s/Math.pow(10,f),h=p>=o?10:p>=a?5:p>=c?2:1;return(f<0?(n=Math.round(t*(l=Math.pow(10,-f)/h)),i=Math.round(e*l),n/l<t&&++n,i/l>e&&--i,l=-l):(n=Math.round(t/(l=Math.pow(10,f)*h)),i=Math.round(e/l),n*l<t&&++n,i*l>e&&--i),i<n&&.5<=r&&r<2)?u(t,e,2*r):[n,i,l]}function l(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?u(e,t,r):u(t,e,r);if(!(o>=i))return[];let c=o-i+1,l=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)l[t]=-((o-t)/a);else for(let t=0;t<c;++t)l[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)l[t]=-((i+t)/a);else for(let t=0;t<c;++t)l[t]=(i+t)*a;return l}function s(t,e,r){return u(t*=1,e*=1,r*=1)[2]}function f(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?s(e,t,r):s(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function p(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function h(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function d(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=p,r=(e,r)=>p(t(e),r),n=(e,r)=>t(e)-r):(e=t===p||t===h?t:y,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function y(){return 0}function v(t){return null===t?NaN:+t}let m=d(p),b=m.right;function g(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function x(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function O(){}m.left,d(v).center;var w="\\s*([+-]?\\d+)\\s*",j="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",A="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",S=/^#([0-9a-f]{3,8})$/,P=RegExp(`^rgb\\(${w},${w},${w}\\)$`),E=RegExp(`^rgb\\(${A},${A},${A}\\)$`),k=RegExp(`^rgba\\(${w},${w},${w},${j}\\)$`),M=RegExp(`^rgba\\(${A},${A},${A},${j}\\)$`),T=RegExp(`^hsl\\(${j},${A},${A}\\)$`),_=RegExp(`^hsla\\(${j},${A},${A},${j}\\)$`),C={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function I(){return this.rgb().formatHex()}function D(){return this.rgb().formatRgb()}function N(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=S.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?B(e):3===r?new z(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?R(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?R(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=P.exec(t))?new z(e[1],e[2],e[3],1):(e=E.exec(t))?new z(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=k.exec(t))?R(e[1],e[2],e[3],e[4]):(e=M.exec(t))?R(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=T.exec(t))?X(e[1],e[2]/100,e[3]/100,1):(e=_.exec(t))?X(e[1],e[2]/100,e[3]/100,e[4]):C.hasOwnProperty(t)?B(C[t]):"transparent"===t?new z(NaN,NaN,NaN,0):null}function B(t){return new z(t>>16&255,t>>8&255,255&t,1)}function R(t,e,r,n){return n<=0&&(t=e=r=NaN),new z(t,e,r,n)}function L(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof O||(i=N(i)),i)?new z((i=i.rgb()).r,i.g,i.b,i.opacity):new z:new z(t,e,r,null==n?1:n)}function z(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function U(){return`#${q(this.r)}${q(this.g)}${q(this.b)}`}function F(){let t=W(this.opacity);return`${1===t?"rgb(":"rgba("}${$(this.r)}, ${$(this.g)}, ${$(this.b)}${1===t?")":`, ${t})`}`}function W(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function $(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function q(t){return((t=$(t))<16?"0":"")+t.toString(16)}function X(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new V(t,e,r,n)}function H(t){if(t instanceof V)return new V(t.h,t.s,t.l,t.opacity);if(t instanceof O||(t=N(t)),!t)return new V;if(t instanceof V)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new V(a,c,u,t.opacity)}function V(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function K(t){return(t=(t||0)%360)<0?t+360:t}function G(t){return Math.max(0,Math.min(1,t||0))}function Y(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function J(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}g(O,N,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:I,formatHex:I,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return H(this).formatHsl()},formatRgb:D,toString:D}),g(z,L,x(O,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new z(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new z(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new z($(this.r),$(this.g),$(this.b),W(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:U,formatHex:U,formatHex8:function(){return`#${q(this.r)}${q(this.g)}${q(this.b)}${q((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:F,toString:F})),g(V,function(t,e,r,n){return 1==arguments.length?H(t):new V(t,e,r,null==n?1:n)},x(O,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new V(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new V(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new z(Y(t>=240?t-240:t+120,i,n),Y(t,i,n),Y(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new V(K(this.h),G(this.s),G(this.l),W(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=W(this.opacity);return`${1===t?"hsl(":"hsla("}${K(this.h)}, ${100*G(this.s)}%, ${100*G(this.l)}%${1===t?")":`, ${t})`}`}}));let Z=t=>()=>t;function Q(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):Z(isNaN(t)?e:t)}let tt=function t(e){var r,n=1==(r=+e)?Q:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):Z(isNaN(t)?e:t)};function i(t,e){var r=n((t=L(t)).r,(e=L(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=Q(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function te(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=L(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}te(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return J((r-n/e)*e,a,i,o,c)}}),te(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return J((r-n/e)*e,i,o,a,c)}});function tr(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var tn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ti=RegExp(tn.source,"g");function to(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?Z(e):("number"===i?tr:"string"===i?(n=N(e))?(e=n,tt):function(t,e){var r,n,i,o,a,c=tn.lastIndex=ti.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(i=tn.exec(t))&&(o=ti.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(i=i[0])===(o=o[0])?l[u]?l[u]+=o:l[++u]=o:(l[++u]=null,s.push({i:u,x:tr(i,o)})),c=ti.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof N?tt:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=to(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=to(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:tr:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function ta(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function tc(t){return+t}var tu=[0,1];function tl(t){return t}function ts(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tf(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=ts(i,n),o=r(a,o)):(n=ts(n,i),o=r(o,a)),function(t){return o(n(t))}}function tp(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=ts(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=b(t,e,1,n)-1;return o[r](i[r](e))}}function th(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function td(){var t,e,r,n,i,o,a=tu,c=tu,u=to,l=tl;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==tl&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?tp:tf,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(c,a.map(t),tr)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,tc),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=ta,s()},f.clamp=function(t){return arguments.length?(l=!!t||tl,s()):l!==tl},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function ty(){return td()(tl,tl)}var tv=r(45656),tm=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tb(t){var e;if(!(e=tm.exec(t)))throw Error("invalid format: "+t);return new tg({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tg(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tx(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tO(t){return(t=tx(Math.abs(t)))?t[1]:NaN}function tw(t,e){var r=tx(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}tb.prototype=tg.prototype,tg.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let tj={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tw(100*t,e),r:tw,s:function(t,e){var r=tx(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(r0=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+tx(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tA(t){return t}var tS=Array.prototype.map,tP=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tE(t,e,r,n){var i,o,a,c=f(t,e,r);switch((n=tb(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tO(u)/3)))-tO(Math.abs(c))))||(n.precision=a),r3(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,tO(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-tO(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-tO(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return r2(n)}function tk(t){var e=t.domain;return t.ticks=function(t){var r=e();return l(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tE(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],l=o[c],f=10;for(l<u&&(i=u,u=l,l=i,i=a,a=c,c=i);f-- >0;){if((i=s(u,l,r))===n)return o[a]=u,o[c]=l,e(o);if(i>0)u=Math.floor(u/i)*i,l=Math.ceil(l/i)*i;else if(i<0)u=Math.ceil(u*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function tM(){var t=ty();return t.copy=function(){return th(t,tM())},tv.C.apply(t,arguments),tk(t)}function tT(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function t_(t){return Math.log(t)}function tC(t){return Math.exp(t)}function tI(t){return-Math.log(-t)}function tD(t){return-Math.exp(-t)}function tN(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tB(t){return(e,r)=>-t(-e,r)}function tR(t){let e,r,n=t(t_,tC),i=n.domain,o=10;function a(){var a,c;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=o)?tN:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=tB(e),r=tB(r),t(tI,tD)):t(t_,tC),n}return n.base=function(t){return arguments.length?(o=+t,a()):o},n.domain=function(t){return arguments.length?(i(t),a()):i()},n.ticks=t=>{let n,a,c=i(),u=c[0],s=c[c.length-1],f=s<u;f&&([u,s]=[s,u]);let p=e(u),h=e(s),d=null==t?10:+t,y=[];if(!(o%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),u>0){for(;p<=h;++p)for(n=1;n<o;++n)if(!((a=p<0?n/r(-p):n*r(p))<u)){if(a>s)break;y.push(a)}}else for(;p<=h;++p)for(n=o-1;n>=1;--n)if(!((a=p>0?n/r(-p):n*r(p))<u)){if(a>s)break;y.push(a)}2*y.length<d&&(y=l(u,s,d))}else y=l(p,h,Math.min(h-p,d)).map(r);return f?y.reverse():y},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=tb(i)).precision||(i.trim=!0),i=r2(i)),t===1/0)return i;let a=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=a?i(t):""}},n.nice=()=>i(tT(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tL(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tz(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tU(t){var e=1,r=t(tL(1),tz(e));return r.constant=function(r){return arguments.length?t(tL(e=+r),tz(e)):e},tk(r)}r2=(r1=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?tA:(e=tS.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?tA:(n=tS.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=tb(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):tj[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=tj[b],w=/[defgprs%]/.test(b);function j(t){var o,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var A=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==n&&(A=!1),p=(A?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?tP[8+r0/3]:"")+j+(A&&"("===n?")":""),w){for(o=-1,a=t.length;++o<a;)if(48>(l=t.charCodeAt(o))||l>57){j=(46===l?c+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}}y&&!h&&(t=i(t,1/0));var S=p.length+t.length+j.length,P=S<d?Array(d-S+1).join(e):"";switch(y&&h&&(t=i(P+t,P.length?d-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,S=P.length>>1)+p+t+j+P.slice(S);break;default:t=P+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=tb(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tO(e)/3))),i=Math.pow(10,-n),o=tP[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,r3=r1.formatPrefix;var tF=r(805);function tW(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function t$(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tq(t){return t<0?-t*t:t*t}function tX(t){var e=t(tl,tl),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tl,tl):.5===r?t(t$,tq):t(tW(r),tW(1/r)):r},tk(e)}function tH(){var t=tX(td());return t.copy=function(){return th(t,tH()).exponent(t.exponent())},tv.C.apply(t,arguments),t}function tV(){return tH.apply(null,arguments).exponent(.5)}function tK(t){return Math.sign(t)*t*t}function tG(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function tY(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}function tJ(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function tZ(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let tQ=new Date,t0=new Date;function t1(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>t1(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(tQ.setTime(+e),t0.setTime(+n),t(tQ),t(t0),Math.floor(r(tQ,t0))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let t2=t1(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t2.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t1(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t2:null,t2.range;let t3=t1(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());t3.range;let t5=t1(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());t5.range;let t8=t1(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());t8.range;let t6=t1(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());t6.range;let t7=t1(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());t7.range;let t9=t1(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);t9.range;let t4=t1(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);t4.range;let et=t1(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ee(t){return t1(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}et.range;let er=ee(0),en=ee(1),ei=ee(2),eo=ee(3),ea=ee(4),ec=ee(5),eu=ee(6);function el(t){return t1(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}er.range,en.range,ei.range,eo.range,ea.range,ec.range,eu.range;let es=el(0),ef=el(1),ep=el(2),eh=el(3),ed=el(4),ey=el(5),ev=el(6);es.range,ef.range,ep.range,eh.range,ed.range,ey.range,ev.range;let em=t1(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());em.range;let eb=t1(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eb.range;let eg=t1(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eg.every=t=>isFinite(t=Math.floor(t))&&t>0?t1(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eg.range;let ex=t1(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function eO(t,e,r,n,i,o){let a=[[t3,1,1e3],[t3,5,5e3],[t3,15,15e3],[t3,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=d(([,,t])=>t).right(a,i);if(o===a.length)return t.every(f(e/31536e6,r/31536e6,n));if(0===o)return t2.every(Math.max(f(e,r,n),1));let[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}ex.every=t=>isFinite(t=Math.floor(t))&&t>0?t1(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ex.range;let[ew,ej]=eO(ex,eb,es,et,t7,t8),[eA,eS]=eO(eg,em,er,t9,t6,t5);function eP(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eE(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ek(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eM={"-":"",_:" ",0:"0"},eT=/^\s*\d+/,e_=/^%/,eC=/[\\^$*+?|[\]().{}]/g;function eI(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function eD(t){return t.replace(eC,"\\$&")}function eN(t){return RegExp("^(?:"+t.map(eD).join("|")+")","i")}function eB(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eR(t,e,r){var n=eT.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eL(t,e,r){var n=eT.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function ez(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eU(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eF(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eW(t,e,r){var n=eT.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function e$(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eq(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eX(t,e,r){var n=eT.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eH(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function eV(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eT.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function eJ(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function eZ(t,e,r){var n=eT.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function eQ(t,e,r){var n=eT.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e0(t,e,r){var n=e_.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e1(t,e,r){var n=eT.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eT.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function e3(t,e){return eI(t.getDate(),e,2)}function e5(t,e){return eI(t.getHours(),e,2)}function e8(t,e){return eI(t.getHours()%12||12,e,2)}function e6(t,e){return eI(1+t9.count(eg(t),t),e,3)}function e7(t,e){return eI(t.getMilliseconds(),e,3)}function e9(t,e){return e7(t,e)+"000"}function e4(t,e){return eI(t.getMonth()+1,e,2)}function rt(t,e){return eI(t.getMinutes(),e,2)}function re(t,e){return eI(t.getSeconds(),e,2)}function rr(t){var e=t.getDay();return 0===e?7:e}function rn(t,e){return eI(er.count(eg(t)-1,t),e,2)}function ri(t){var e=t.getDay();return e>=4||0===e?ea(t):ea.ceil(t)}function ro(t,e){return t=ri(t),eI(ea.count(eg(t),t)+(4===eg(t).getDay()),e,2)}function ra(t){return t.getDay()}function rc(t,e){return eI(en.count(eg(t)-1,t),e,2)}function ru(t,e){return eI(t.getFullYear()%100,e,2)}function rl(t,e){return eI((t=ri(t)).getFullYear()%100,e,2)}function rs(t,e){return eI(t.getFullYear()%1e4,e,4)}function rf(t,e){var r=t.getDay();return eI((t=r>=4||0===r?ea(t):ea.ceil(t)).getFullYear()%1e4,e,4)}function rp(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eI(e/60|0,"0",2)+eI(e%60,"0",2)}function rh(t,e){return eI(t.getUTCDate(),e,2)}function rd(t,e){return eI(t.getUTCHours(),e,2)}function ry(t,e){return eI(t.getUTCHours()%12||12,e,2)}function rv(t,e){return eI(1+t4.count(ex(t),t),e,3)}function rm(t,e){return eI(t.getUTCMilliseconds(),e,3)}function rb(t,e){return rm(t,e)+"000"}function rg(t,e){return eI(t.getUTCMonth()+1,e,2)}function rx(t,e){return eI(t.getUTCMinutes(),e,2)}function rO(t,e){return eI(t.getUTCSeconds(),e,2)}function rw(t){var e=t.getUTCDay();return 0===e?7:e}function rj(t,e){return eI(es.count(ex(t)-1,t),e,2)}function rA(t){var e=t.getUTCDay();return e>=4||0===e?ed(t):ed.ceil(t)}function rS(t,e){return t=rA(t),eI(ed.count(ex(t),t)+(4===ex(t).getUTCDay()),e,2)}function rP(t){return t.getUTCDay()}function rE(t,e){return eI(ef.count(ex(t)-1,t),e,2)}function rk(t,e){return eI(t.getUTCFullYear()%100,e,2)}function rM(t,e){return eI((t=rA(t)).getUTCFullYear()%100,e,2)}function rT(t,e){return eI(t.getUTCFullYear()%1e4,e,4)}function r_(t,e){var r=t.getUTCDay();return eI((t=r>=4||0===r?ed(t):ed.ceil(t)).getUTCFullYear()%1e4,e,4)}function rC(){return"+0000"}function rI(){return"%"}function rD(t){return+t}function rN(t){return Math.floor(t/1e3)}function rB(t){return new Date(t)}function rR(t){return t instanceof Date?+t:+new Date(+t)}function rL(t,e,r,n,i,o,a,c,u,l){var s=ty(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rR)):p().map(rB)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tT(r,t)):s},s.copy=function(){return th(s,rL(t,e,r,n,i,o,a,c,u,l))},s}function rz(){return tv.C.apply(rL(eA,eS,eg,em,er,t9,t6,t5,t3,r8).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rU(){return tv.C.apply(rL(ew,ej,ex,eb,es,t4,t7,t8,t3,r6).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rF(){var t,e,r,n,i,o=0,a=1,c=tl,u=!1;function l(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(to),l.rangeRound=s(ta),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function rW(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function r$(){var t=tX(rF());return t.copy=function(){return rW(t,r$()).exponent(t.exponent())},tv.K.apply(t,arguments)}function rq(){return r$.apply(null,arguments).exponent(.5)}function rX(){var t,e,r,n,i,o,a,c=0,u=.5,l=1,s=1,f=tl,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=to);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=o(c*=1),e=o(u*=1),r=o(l*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(to),h.rangeRound=d(ta),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function rH(){var t=tX(rX());return t.copy=function(){return rW(t,rH()).exponent(t.exponent())},tv.K.apply(t,arguments)}function rV(){return rH.apply(null,arguments).exponent(.5)}function rK(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}r8=(r5=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=eN(i),s=eB(i),f=eN(o),p=eB(o),h=eN(a),d=eB(a),y=eN(c),v=eB(c),m=eN(u),b=eB(u),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:e3,e:e3,f:e9,g:rl,G:rf,H:e5,I:e8,j:e6,L:e7,m:e4,M:rt,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rD,s:rN,S:re,u:rr,U:rn,V:ro,w:ra,W:rc,x:null,X:null,y:ru,Y:rs,Z:rp,"%":rI},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:rh,e:rh,f:rb,g:rM,G:r_,H:rd,I:ry,j:rv,L:rm,m:rg,M:rx,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rD,s:rN,S:rO,u:rw,U:rj,V:rS,w:rP,W:rE,x:null,X:null,y:rk,Y:rT,Z:rC,"%":rI},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return A(t,e,r,n)},d:eV,e:eV,f:eQ,g:e$,G:eW,H:eG,I:eG,j:eK,L:eZ,m:eH,M:eY,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eX,Q:e1,s:e2,S:eJ,u:eL,U:ez,V:eU,w:eR,W:eF,x:function(t,e,n){return A(t,r,e,n)},X:function(t,e,r){return A(t,n,e,r)},y:e$,Y:eW,Z:eq,"%":e0};function w(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=eM[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,i,o=ek(1900,void 0,1);if(A(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=eE(ek(o.y,0,1))).getUTCDay())>4||0===i?ef.ceil(n):ef(n),n=t4.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=eP(ek(o.y,0,1))).getDay())>4||0===i?en.ceil(n):en(n),n=t9.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?eE(ek(o.y,0,1)).getUTCDay():eP(ek(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,eE(o)):eP(o)}}function A(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=O[(i=e.charAt(a++))in eM?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,r5.parse,r6=r5.utcFormat,r5.utcParse;var rG=r(78068),rY=r(42369);function rJ(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function rZ(t,e){return t[e]}function rQ(t){let e=[];return e.key=t,e}var r0,r1,r2,r3,r5,r8,r6,r7,r9,r4=r(32178),nt=r.n(r4),ne=r(95280),nr=r.n(ne),nn=r(8875),ni=r.n(nn),no=r(2802),na=r.n(no),nc=r(77719),nu=r.n(nc),nl=r(88336),ns=r.n(nl),nf=r(71491),np=r.n(nf),nh=r(83201),nd=r.n(nh),ny=r(39276),nv=r.n(ny),nm=r(29736),nb=r.n(nm),ng=r(76111),nx=r.n(ng),nO=!0,nw="[DecimalError] ",nj=nw+"Invalid argument: ",nA=nw+"Exponent out of range: ",nS=Math.floor,nP=Math.pow,nE=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,nk=nS(1286742750677284.5),nM={};function nT(t,e){var r,n,i,o,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),nO?nU(e,f):e;if(u=t.d,l=e.d,a=t.e,i=e.e,u=u.slice(),o=a-i){for(o<0?(n=u,o=-o,c=l.length):(n=l,i=a,c=u.length),o>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=u.length)-(o=l.length)<0&&(o=c,n=l,l=u,u=n),r=0;o;)r=(u[--o]=u[o]+l[o]+r)/1e7|0,u[o]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=i,nO?nU(e,f):e}function n_(t,e,r){if(t!==~~t||t<e||t>r)throw Error(nj+t)}function nC(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=nR(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=nR(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}nM.absoluteValue=nM.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},nM.comparedTo=nM.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},nM.decimalPlaces=nM.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},nM.dividedBy=nM.div=function(t){return nI(this,new this.constructor(t))},nM.dividedToIntegerBy=nM.idiv=function(t){var e=this.constructor;return nU(nI(this,new e(t),0,1),e.precision)},nM.equals=nM.eq=function(t){return!this.cmp(t)},nM.exponent=function(){return nN(this)},nM.greaterThan=nM.gt=function(t){return this.cmp(t)>0},nM.greaterThanOrEqualTo=nM.gte=function(t){return this.cmp(t)>=0},nM.isInteger=nM.isint=function(){return this.e>this.d.length-2},nM.isNegative=nM.isneg=function(){return this.s<0},nM.isPositive=nM.ispos=function(){return this.s>0},nM.isZero=function(){return 0===this.s},nM.lessThan=nM.lt=function(t){return 0>this.cmp(t)},nM.lessThanOrEqualTo=nM.lte=function(t){return 1>this.cmp(t)},nM.logarithm=nM.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(r9))throw Error(nw+"NaN");if(this.s<1)throw Error(nw+(this.s?"NaN":"-Infinity"));return this.eq(r9)?new r(0):(nO=!1,e=nI(nL(this,i),nL(t,i),i),nO=!0,nU(e,n))},nM.minus=nM.sub=function(t){return t=new this.constructor(t),this.s==t.s?nF(this,t):nT(this,(t.s=-t.s,t))},nM.modulo=nM.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(nw+"NaN");return this.s?(nO=!1,e=nI(this,t,0,1).times(t),nO=!0,this.minus(e)):nU(new r(this),n)},nM.naturalExponential=nM.exp=function(){return nD(this)},nM.naturalLogarithm=nM.ln=function(){return nL(this)},nM.negated=nM.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},nM.plus=nM.add=function(t){return t=new this.constructor(t),this.s==t.s?nT(this,t):nF(this,(t.s=-t.s,t))},nM.precision=nM.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(nj+t);if(e=nN(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},nM.squareRoot=nM.sqrt=function(){var t,e,r,n,i,o,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(nw+"NaN")}for(t=nN(this),nO=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=nC(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=nS((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=a=(r=c.precision)+3;;)if(n=(o=n).plus(nI(this,o,a+2)).times(.5),nC(o.d).slice(0,a)===(e=nC(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(nU(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return nO=!0,nU(n,r)},nM.times=nM.mul=function(t){var e,r,n,i,o,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(o=f,f=p,p=o,a=u,u=l,l=a),o=[],n=a=u+l;n--;)o.push(0);for(n=l;--n>=0;){for(e=0,i=u+n;i>n;)c=o[i]+p[n]*f[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,nO?nU(t,s.precision):t},nM.toDecimalPlaces=nM.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(n_(t,0,1e9),void 0===e?e=n.rounding:n_(e,0,8),nU(r,t+nN(r)+1,e))},nM.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=nW(n,!0):(n_(t,0,1e9),void 0===e?e=i.rounding:n_(e,0,8),r=nW(n=nU(new i(n),t+1,e),!0,t+1)),r},nM.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?nW(this):(n_(t,0,1e9),void 0===e?e=i.rounding:n_(e,0,8),r=nW((n=nU(new i(this),t+nN(this)+1,e)).abs(),!1,t+nN(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},nM.toInteger=nM.toint=function(){var t=this.constructor;return nU(new t(this),nN(this)+1,t.rounding)},nM.toNumber=function(){return+this},nM.toPower=nM.pow=function(t){var e,r,n,i,o,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(r9);if(!(c=new u(c)).s){if(t.s<1)throw Error(nw+"Infinity");return c}if(c.eq(r9))return c;if(n=u.precision,t.eq(r9))return nU(c,n);if(a=(e=t.e)>=(r=t.d.length-1),o=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(i=new u(r9),e=Math.ceil(n/7+4),nO=!1;r%2&&n$((i=i.times(c)).d,e),0!==(r=nS(r/2));)n$((c=c.times(c)).d,e);return nO=!0,t.s<0?new u(r9).div(i):nU(i,n)}}else if(o<0)throw Error(nw+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,nO=!1,i=t.times(nL(c,n+12)),nO=!0,(i=nD(i)).s=o,i},nM.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=nN(i),n=nW(i,r<=o.toExpNeg||r>=o.toExpPos)):(n_(t,1,1e9),void 0===e?e=o.rounding:n_(e,0,8),r=nN(i=nU(new o(i),t,e)),n=nW(i,t<=r||r<=o.toExpNeg,t)),n},nM.toSignificantDigits=nM.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(n_(t,1,1e9),void 0===e?e=r.rounding:n_(e,0,8)),nU(new r(this),t,e)},nM.toString=nM.valueOf=nM.val=nM.toJSON=nM[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=nN(this),e=this.constructor;return nW(this,t<=e.toExpNeg||t>=e.toExpPos)};var nI=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,A,S=n.constructor,P=n.s==i.s?1:-1,E=n.d,k=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(nw+"Division by zero");for(l=0,u=n.e-i.e,j=k.length,O=E.length,d=(h=new S(P)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==o?o=S.precision:a?o+(nN(n)-nN(i))+1:o)<0)return new S(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(A=k.slice()).unshift(0),w=k[0],k[1]>=1e7/2&&++w;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?A:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?A:k,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,nU(h,a?o+nN(h)+1:o)}}();function nD(t,e){var r,n,i,o,a,c=0,u=0,l=t.constructor,s=l.precision;if(nN(t)>16)throw Error(nA+nN(t));if(!t.s)return new l(r9);for(null==e?(nO=!1,a=s):a=e,o=new l(.03125);t.abs().gte(.1);)t=t.times(o),u+=5;for(a+=Math.log(nP(2,u))/Math.LN10*2+5|0,r=n=i=new l(r9),l.precision=a;;){if(n=nU(n.times(t),a),r=r.times(++c),nC((o=i.plus(nI(n,r,a))).d).slice(0,a)===nC(i.d).slice(0,a)){for(;u--;)i=nU(i.times(i),a);return l.precision=s,null==e?(nO=!0,nU(i,s)):i}i=o}}function nN(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function nB(t,e,r){if(e>t.LN10.sd())throw nO=!0,r&&(t.precision=r),Error(nw+"LN10 precision limit exceeded");return nU(new t(t.LN10),e)}function nR(t){for(var e="";t--;)e+="0";return e}function nL(t,e){var r,n,i,o,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(nw+(p.s?"NaN":"-Infinity"));if(p.eq(r9))return new d(0);if(null==e?(nO=!1,l=y):l=e,p.eq(10))return null==e&&(nO=!0),nB(d,l);if(d.precision=l+=10,n=(r=nC(h)).charAt(0),!(15e14>Math.abs(o=nN(p))))return u=nB(d,l+2,y).times(o+""),p=nL(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(nO=!0,nU(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=nC((p=p.times(t)).d)).charAt(0),f++;for(o=nN(p),n>1?(p=new d("0."+r),o++):p=new d(n+"."+r.slice(1)),c=a=p=nI(p.minus(r9),p.plus(r9),l),s=nU(p.times(p),l),i=3;;){if(a=nU(a.times(s),l),nC((u=c.plus(nI(a,new d(i),l))).d).slice(0,l)===nC(c.d).slice(0,l))return c=c.times(2),0!==o&&(c=c.plus(nB(d,l+2,y).times(o+""))),c=nI(c,new d(f),l),d.precision=y,null==e?(nO=!0,nU(c,y)):c;c=u,i+=2}}function nz(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=nS((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),nO&&(t.e>nk||t.e<-nk))throw Error(nA+r)}else t.s=0,t.e=0,t.d=[0];return t}function nU(t,e,r){var n,i,o,a,c,u,l,s,f=t.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(o=f.length))return t;for(a=1,l=o=f[s];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=l/(o=nP(10,a-i-1))%10|0,u=e<0||void 0!==f[s+1]||l%o,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?l/nP(10,a-i):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(o=nN(t),f.length=1,e=e-o-1,f[0]=nP(10,(7-e%7)%7),t.e=nS(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,o=1,s--):(f.length=s+1,o=nP(10,7-n),f[s]=i>0?(l/nP(10,a-i)%nP(10,i)|0)*o:0),u)for(;;)if(0==s){1e7==(f[0]+=o)&&(f[0]=1,++t.e);break}else{if(f[s]+=o,1e7!=f[s])break;f[s--]=0,o=1}for(n=f.length;0===f[--n];)f.pop();if(nO&&(t.e>nk||t.e<-nk))throw Error(nA+nN(t));return t}function nF(t,e){var r,n,i,o,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),nO?nU(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(i=Math.max(Math.ceil(h/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((s=(i=u.length)<(c=f.length))&&(c=i),i=0;i<c;i++)if(u[i]!=f[i]){s=u[i]<f[i];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,i=f.length-c;i>0;--i)u[c++]=0;for(i=f.length;i>a;){if(u[--i]<f[i]){for(o=i;o&&0===u[--o];)u[o]=1e7-1;--u[o],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,nO?nU(e,h):e):new p(0)}function nW(t,e,r){var n,i=nN(t),o=nC(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+nR(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+nR(-i-1)+o,r&&(n=r-a)>0&&(o+=nR(n))):i>=a?(o+=nR(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+nR(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=nR(n))),t.s<0?"-"+o:o}function n$(t,e){if(t.length>e)return t.length=e,!0}function nq(t){if(!t||"object"!=typeof t)throw Error(nw+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(nS(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(nj+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(nj+r+": "+n);return this}var r7=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(nj+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return nz(this,t.toString())}if("string"!=typeof t)throw Error(nj+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,nE.test(t))nz(this,t);else throw Error(nj+t)}if(o.prototype=nM,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=nq,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});r9=new r7(1);let nX=r7;function nH(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nV=function(t){return t},nK={},nG=function(t){return t===nK},nY=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nG(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nJ=function(t){return function t(e,r){return 1===e?r:nY(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==nK}).length;return a>=e?r.apply(void 0,i):t(e-a,nY(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return nG(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nH(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return nH(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nH(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nZ=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nQ=nJ(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),n0=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nV;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},n1=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},n2=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};nJ(function(t,e,r){var n=+t;return n+r*(e-n)}),nJ(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),nJ(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let n3={rangeStep:function(t,e,r){for(var n=new nX(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new nX(t).abs().log(10).toNumber())+1}};function n5(t){return function(t){if(Array.isArray(t))return n7(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||n6(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n8(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||n6(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n6(t,e){if(t){if("string"==typeof t)return n7(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n7(t,e)}}function n7(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n9(t){var e=n8(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function n4(t,e,r){if(t.lte(0))return new nX(0);var n=n3.getDigitCount(t.toNumber()),i=new nX(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new nX(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new nX(Math.ceil(c))}function it(t,e,r){var n=1,i=new nX(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new nX(10).pow(n3.getDigitCount(t)-1),i=new nX(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new nX(Math.floor(t)))}else 0===t?i=new nX(Math.floor((e-1)/2)):r||(i=new nX(Math.floor(t)));var a=Math.floor((e-1)/2);return n0(nQ(function(t){return i.add(new nX(t-a).mul(n)).toNumber()}),nZ)(0,e)}var ie=n2(function(t){var e=n8(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=n8(n9([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(n5(nZ(0,i-1).map(function(){return 1/0}))):[].concat(n5(nZ(0,i-1).map(function(){return-1/0})),[l]);return r>n?n1(s):s}if(u===l)return it(u,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new nX(0),tickMin:new nX(0),tickMax:new nX(0)};var c=n4(new nX(r).sub(e).div(n-1),i,a),u=Math.ceil((o=e<=0&&r>=0?new nX(0):(o=new nX(e).add(r).div(2)).sub(new nX(o).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new nX(r).sub(o).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,i,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:o.sub(new nX(u).mul(c)),tickMax:o.add(new nX(l).mul(c))})}(u,l,a,o),p=f.step,h=f.tickMin,d=f.tickMax,y=n3.rangeStep(h,d.add(new nX(.1).mul(p)),p);return r>n?n1(y):y});n2(function(t){var e=n8(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=n8(n9([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return it(u,i,o);var s=n4(new nX(l).sub(u).div(a-1),o,0),f=n0(nQ(function(t){return new nX(u).add(new nX(t).mul(s)).toNumber()}),nZ)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?n1(f):f});var ir=n2(function(t,e){var r=n8(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=n8(n9([n,i]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var l=Math.max(e,2),s=n4(new nX(u).sub(c).div(l-1),o,0),f=[].concat(n5(n3.rangeStep(new nX(c),new nX(u).sub(new nX(.99).mul(s)),s)),[u]);return n>i?n1(f):f}),ii=r(13405),io=r(1511),ia=r(23872),ic=r(58909);function iu(t){return(iu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function il(t){return function(t){if(Array.isArray(t))return is(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return is(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return is(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function is(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ip(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ih(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ip(Object(r),!0).forEach(function(e){id(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ip(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function id(t,e,r){var n;return(n=function(t,e){if("object"!=iu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=iu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==iu(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function iy(t,e,r){return ni()(t)||ni()(e)?r:(0,io.vh)(e)?ns()(t,e,r):na()(e)?e(t):r}function iv(t,e,r,n){var i=np()(t,function(t){return iy(t,e)});if("number"===r){var o=i.filter(function(t){return(0,io.Et)(t)||parseFloat(t)});return o.length?[nr()(o),nt()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!ni()(t)}):i).map(function(t){return(0,io.vh)(t)||t instanceof Date?t:""})}var im=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if((0,io.sA)(s-l)!==(0,io.sA)(f-s)){var h=[];if((0,io.sA)(f-s)===(0,io.sA)(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},ib=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?ih(ih({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},ig=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var l=i[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,ia.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?ih(ih({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var O=ni()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:ni()(O)?void 0:(0,io.F4)(O,r,0)})}}return o},ix=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=(0,io.F4)(r,i,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=i&&(h-=(u-1)*l,l=0),h>=i&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((i-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(il(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,io.F4)(n,i,0,!0);i-2*y-(u-1)*l<=0&&(l=0);var v=(i-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(il(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},iO=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=o-(a.left||0)-(a.right||0),u=(0,ic.g)({children:i,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,d=u.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,io.Et)(t[p]))return ih(ih({},t),{},id({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,io.Et)(t[h]))return ih(ih({},t),{},id({},h,t[h]+(f||0)))}return t},iw=function(t,e,r,n,i){var o=e.props.children,a=(0,ia.aS)(o,ii.u).filter(function(t){var e;return e=t.props.direction,!!ni()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(a&&a.length){var c=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=iy(e,r);if(ni()(n))return t;var i=Array.isArray(n)?[nr()(n),nt()(n)]:[n,n],o=c.reduce(function(t,r){var n=iy(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},ij=function(t,e,r,n,i){var o=e.map(function(e){return iw(t,e,r,i,n)}).filter(function(t){return!ni()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},iA=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&iw(t,e,o,n)||iv(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},iS=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},iP=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},iE=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,io.sA)(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!nd()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}})},ik=new WeakMap,iM=function(t,e){if("function"!=typeof e)return t;ik.has(t)||ik.set(t,new WeakMap);var r=ik.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},iT=function(t,e,r){var o=t.scale,a=t.type,c=t.layout,u=t.axisType;if("auto"===o)return"radial"===c&&"radiusAxis"===u?{scale:i.A(),realScaleType:"band"}:"radial"===c&&"angleAxis"===u?{scale:tM(),realScaleType:"linear"}:"category"===a&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:i.z(),realScaleType:"point"}:"category"===a?{scale:i.A(),realScaleType:"band"}:{scale:tM(),realScaleType:"linear"};if(nu()(o)){var l="scale".concat(nv()(o));return{scale:(n[l]||i.z)(),realScaleType:n[l]?l:"point"}}return na()(o)?{scale:o}:{scale:i.z(),realScaleType:"point"}},i_=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},iC=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},iI=function(t,e){if(!e||2!==e.length||!(0,io.Et)(e[0])||!(0,io.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!(0,io.Et)(t[0])||t[0]<r)&&(i[0]=r),(!(0,io.Et)(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},iD={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=nd()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}rK(t,e)}},none:rK,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}rK(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<i;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=l/u)}r[a-1][1]+=r[a-1][0]=o,rK(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=nd()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},iN=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=iD[r];return(function(){var t=(0,rY.A)([]),e=rJ,r=rK,n=rZ;function i(i){var o,a,c=Array.from(t.apply(this,arguments),rQ),u=c.length,l=-1;for(let t of i)for(o=0,++l;o<u;++o)(c[o][l]=[0,+n(t,c[o].key,l,i)]).data=t;for(o=0,a=(0,rG.A)(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,rY.A)(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,rY.A)(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?rJ:"function"==typeof t?t:(0,rY.A)(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?rK:t,i):r},i})().keys(n).value(function(t,e){return+iy(t,e,0)}).order(rJ).offset(i)(t)},iB=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?ih(ih({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],u=t[c]||{hasStack:!1,stackGroups:{}};if((0,io.vh)(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[(0,io.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return ih(ih({},t),{},id({},c,u))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return ih(ih({},e),{},id({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:iN(t,a.items,i)}))},{})),ih(ih({},e),{},id({},o,c))},{})},iR=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var l=ie(u,i,a);return t.domain([nr()(l),nt()(l)]),{niceTicks:l}}return i&&"number"===n?{niceTicks:ir(t.domain(),i,a)}:null};function iL(t){var e=t.axis,r=t.ticks,n=t.bandSize,i=t.entry,o=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!ni()(i[e.dataKey])){var c=(0,io.eP)(r,"value",i[e.dataKey]);if(c)return c.coordinate+n/2}return r[o]?r[o].coordinate+n/2:null}var u=iy(i,ni()(a)?e.dataKey:a);return ni()(u)?null:e.scale(u)}var iz=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=iy(o,e.dataKey,e.domain[a]);return ni()(c)?null:e.scale(c)-i/2+n},iU=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},iF=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?ih(ih({},t.type.defaultProps),t.props):t.props).stackId;if((0,io.vh)(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},iW=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[nr()(e.concat([t[0]]).filter(io.Et)),nt()(e.concat([t[1]]).filter(io.Et))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},i$=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,iq=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,iX=function(t,e,r){if(na()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,io.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(i$.test(t[0])){var i=+i$.exec(t[0])[1];n[0]=e[0]-i}else na()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,io.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(iq.test(t[1])){var o=+iq.exec(t[1])[1];n[1]=e[1]+o}else na()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},iH=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=nx()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],l=i[a-1];o=Math.min((u.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},iV=function(t,e,r){return!t||!t.length||nb()(t,ns()(r,"type.defaultProps.domain"))?e:t},iK=function(t,e){var r=t.type.defaultProps?ih(ih({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return ih(ih({},(0,ia.J9)(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:ib(t),value:iy(e,n),type:c,payload:e,chartType:u,hide:l})}},13405:(t,e,r)=>{"use strict";r.d(e,{u:()=>m});var n=r(60222),i=r.n(n),o=r(61498),a=r(42956),c=r(23872),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(p=function(){return!!t})()}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function d(t,e){return(d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}var m=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=h(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,p()?Reflect.construct(t,e||[],h(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&d(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,l=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,u),m=(0,c.J9)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,o.A)(!1);var b=p.map(function(t){var o,c,u=h(t,l),p=u.x,v=u.y,b=u.value,g=u.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(g,2)||function(t,e){if(t){if("string"==typeof t)return f(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=O[0],c=O[1]}else o=c=g;if("vertical"===r){var w=d.scale,j=v+e,A=j+n,S=j-n,P=w(b-o),E=w(b+c);x.push({x1:E,y1:A,x2:E,y2:S}),x.push({x1:P,y1:j,x2:E,y2:j}),x.push({x1:P,y1:A,x2:P,y2:S})}else if("horizontal"===r){var k=y.scale,M=p+e,T=M-n,_=M+n,C=k(b-o),I=k(b+c);x.push({x1:T,y1:I,x2:_,y2:I}),x.push({x1:M,y1:C,x2:M,y2:I}),x.push({x1:T,y1:C,x2:_,y2:C})}return i().createElement(a.W,s({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return i().createElement("line",s({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return i().createElement(a.W,{className:"recharts-errorBars"},b)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i().Component);y(m,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),y(m,"displayName","ErrorBar")},14870:(t,e,r)=>{var n=r(97657),i=r(82278),o=r(96598);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},15305:(t,e,r)=>{var n=r(56097),i=r(69584),o=r(83470),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,o=i();if(g(o))return O(o);p=setTimeout(x,(t=o-h,r=o-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=i(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(o(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(i())},w}},17234:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},17552:(t,e,r)=>{"use strict";r.d(e,{A:()=>o,z:()=>a});var n=r(45656),i=r(805);function o(){var t,e,r=(0,i.A)().unknown(void 0),a=r.domain,c=r.range,u=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<u,i=n?l:u,o=n?u:l;t=(o-i)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),i+=(o-i-t*(r-f))*h,e=t*(1-f),s&&(i=Math.round(i),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return i+t*e});return c(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([u,l]=t,u*=1,l*=1,d()):[u,l]},r.rangeRound=function(t){return[u,l]=t,u*=1,l*=1,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return o(a(),[u,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.C.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(o.apply(null,arguments).paddingInner(1))}},19009:(t,e,r)=>{var n=r(81005),i=r(70486),o=r(79263);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},19096:(t,e,r)=>{var n=r(25748),i=r(14634);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},19919:t=>{t.exports=function(t){return t!=t}},23872:(t,e,r)=>{"use strict";r.d(e,{AW:()=>L,BU:()=>k,J9:()=>I,Me:()=>M,Mn:()=>j,OV:()=>D,X_:()=>R,aS:()=>E,ee:()=>B,sT:()=>_});var n=r(88336),i=r.n(n),o=r(8875),a=r.n(o),c=r(77719),u=r.n(c),l=r(2802),s=r.n(l),f=r(56097),p=r.n(f),h=r(60222),d=r(25048),y=r(1511),v=r(4854),m=r(43891),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},A=null,S=null,P=function t(e){if(e===A&&Array.isArray(S))return S;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),S=r,A=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],P(t).forEach(function(t){var e=i()(t,"type.displayName")||i()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function k(t,e){var r=E(t,e);return r&&r[0]}var M=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.Et)(r)&&!(r<=0)&&!!(0,y.Et)(n)&&!(n<=0)},T=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_=function(t){return t&&"object"===O(t)&&"clipDot"in t},C=function(t,e,r,n){var i,o=null!=(i=null===m.VU||void 0===m.VU?void 0:m.VU[n])?i:[];return!s()(t)&&(n&&o.includes(e)||m.QQ.includes(e))||r&&m.j2.includes(e)},I=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;C(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},D=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var o=e[i],a=r[i];if(Array.isArray(o)||Array.isArray(a)){if(!t(o,a))return!1}else if(!N(o,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,i=x(r,b),o=e.props||{},c=o.children,u=x(o,g);if(n&&c)return(0,v.b)(i,u)&&D(n,c);if(!n&&!c)return(0,v.b)(i,u)}return!1},B=function(t,e){var r=[],n={};return P(t).forEach(function(t,i){var o;if((o=t)&&o.type&&u()(o.type)&&T.indexOf(o.type)>=0)r.push(t);else if(t){var a=j(t.type),c=e[a]||{},l=c.handler,s=c.once;if(l&&(!s||!n[a])){var f=l(t,a,i);r.push(f),n[a]=!0}}}),r},R=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},L=function(t,e){return P(e).indexOf(t)}},24035:(t,e,r)=>{"use strict";r.d(e,{gu:()=>eD});var n=r(60222),i=r.n(n),o=r(8875),a=r.n(o),c=r(2802),u=r.n(c),l=r(96390),s=r.n(l),f=r(88336),p=r.n(f),h=r(76111),d=r.n(h),y=r(2438),v=r.n(y),m=r(70991),b=r(61498),g=r(75974),x=r(42956),O=r(5398),w=r(91110),j=r(3380),A=r(60954),S=r(23872),P=r(17552),E=r(87158),k=r(12810),M=r(1511);function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function C(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach(function(e){I(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function I(t,e,r){var n;return(n=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==T(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var D=["Webkit","Moz","O","ms"],N=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=D.reduce(function(t,n){return C(C({},t),{},I({},n+r,e))},{});return n[t]=e,n};function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(){return(R=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function L(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?L(Object(r),!0).forEach(function(e){q(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function U(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,X(n.key),n)}}function F(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(F=function(){return!!t})()}function W(t){return(W=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function $(t,e){return($=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function q(t,e,r){return(e=X(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function X(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}var H=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=(0,P.z)().domain(s()(0,c)).range([i,i+o-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},V=function(t){return t.changedTouches&&!!t.changedTouches.length},K=function(t){var e,r;function o(t){var e,r,n;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return r=o,n=[t],r=W(r),q(e=function(t,e){if(e&&("object"===B(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,F()?Reflect.construct(r,n||[],W(this).constructor):r.apply(this,n)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),q(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),q(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),q(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),q(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),q(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),q(e,"handleSlideDragStart",function(t){var r=V(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&$(o,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=o.getIndexInRange(n,u),f=o.getIndexInRange(n,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=(0,k.kr)(r[t],i,t);return u()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=V(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(q(q({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(q({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,a=t.fill,c=t.stroke;return i().createElement("rect",{stroke:c,fill:a,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,a=t.height,c=t.data,u=t.children,l=t.padding,s=n.Children.only(u);return s?i().cloneElement(s,{x:e,y:r,width:o,height:a,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,n,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=z(z({},(0,S.J9)(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(n=h[y])?void 0:n.name);return i().createElement(x.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},o.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return i().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:n,width:l,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return i().createElement(x.W,{className:"recharts-brush-texts"},i().createElement(E.E,R({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),i().createElement(E.E,R({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,a=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!(0,M.Et)(o)||!(0,M.Et)(a)||!(0,M.Et)(c)||!(0,M.Et)(u)||c<=0||u<=0)return null;var b=(0,m.A)("recharts-brush",r),g=1===i().Children.count(n),O=N("userSelect","none");return i().createElement(x.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:O},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,a=t.stroke,c=Math.floor(r+o/2)-1;return i().createElement(i().Fragment,null,i().createElement("rect",{x:e,y:r,width:n,height:o,fill:a,stroke:"none"}),i().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),i().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return i().isValidElement(t)?i().cloneElement(t,e):u()(t)?t(e):o.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return z({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?H({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&U(o.prototype,e),r&&U(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);q(K,"displayName","Brush"),q(K,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var G=r(73149),Y=r(58909),J=r(31347),Z=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},Q=r(40643),tt=r(28511);function te(){return(te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tr(t){return(tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ti(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(r),!0).forEach(function(e){tu(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(to=function(){return!!t})()}function ta(t){return(ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tc(t,e){return(tc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tu(t,e,r){return(e=tl(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tl(t){var e=function(t,e){if("object"!=tr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tr(e)?e:e+""}var ts=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=(0,Q.P2)({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return Z(t,"discard")&&!o.isInRange(a)?null:a},tf=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ta(t),function(t,e){if(e&&("object"===tr(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,to()?Reflect.construct(t,e||[],ta(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tc(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,a=t.alwaysShow,c=t.clipPathId,u=(0,M.vh)(e),l=(0,M.vh)(n);if((0,tt.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=ts(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=ti(ti({clipPath:Z(this.props,"hidden")?"url(#".concat(c,")"):void 0},(0,S.J9)(this.props,!0)),{},{cx:f,cy:p});return i().createElement(x.W,{className:(0,m.A)("recharts-reference-dot",y)},r.renderDot(d,v),J.J.renderCallByParent(this.props,{x:f-o,y:p-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tl(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i().Component);tu(tf,"displayName","ReferenceDot"),tu(tf,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tu(tf,"renderDot",function(t,e){var r;return i().isValidElement(t)?i().cloneElement(t,e):u()(t)?t(e):i().createElement(j.c,te({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var tp=r(60794),th=r.n(tp),td=r(66957);function ty(t){return(ty="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tv(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tv=function(){return!!t})()}function tm(t){return(tm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tb(t,e){return(tb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tx(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tg(Object(r),!0).forEach(function(e){tO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tO(t,e,r){return(e=tw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tw(t){var e=function(t,e){if("object"!=ty(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ty(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ty(e)?e:e+""}function tj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tA(){return(tA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tS=function(t,e){var r;return i().isValidElement(t)?i().cloneElement(t,e):u()(t)?t(e):i().createElement("line",tA({},e,{className:"recharts-reference-line-line"}))},tP=function(t,e,r,n,i,o,a,c,u){var l=i.x,s=i.y,f=i.width,p=i.height;if(r){var h=u.y,d=t.y.apply(h,{position:o});if(Z(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:o});if(Z(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:o})});return Z(u,"discard")&&th()(g,function(e){return!t.isInRange(e)})?null:g}return null};function tE(t){var e=t.x,r=t.y,n=t.segment,o=t.xAxisId,a=t.yAxisId,c=t.shape,u=t.className,l=t.alwaysShow,s=(0,td.Yp)(),f=(0,td.AF)(o),p=(0,td.Nk)(a),h=(0,td.sk)();if(!s||!h)return null;(0,tt.R)(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=tP((0,Q.P2)({x:f.scale,y:p.scale}),(0,M.vh)(e),(0,M.vh)(r),n&&2===n.length,h,t.position,f.orientation,p.orientation,t);if(!d)return null;var y=function(t){if(Array.isArray(t))return t}(d)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(d,2)||function(t,e){if(t){if("string"==typeof t)return tj(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tj(t,e)}}(d,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),v=y[0],b=v.x,g=v.y,O=y[1],w=O.x,j=O.y,A=tx(tx({clipPath:Z(t,"hidden")?"url(#".concat(s,")"):void 0},(0,S.J9)(t,!0)),{},{x1:b,y1:g,x2:w,y2:j});return i().createElement(x.W,{className:(0,m.A)("recharts-reference-line",u)},tS(c,A),J.J.renderCallByParent(t,(0,Q.vh)({x1:b,y1:g,x2:w,y2:j})))}var tk=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tm(t),function(t,e){if(e&&("object"===ty(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tv()?Reflect.construct(t,e||[],tm(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tb(r,t),e=[{key:"render",value:function(){return i().createElement(tE,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tw(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i().Component);function tM(){return(tM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tT(t){return(tT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t_(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t_(Object(r),!0).forEach(function(e){tB(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}tO(tk,"displayName","ReferenceLine"),tO(tk,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function tI(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tI=function(){return!!t})()}function tD(t){return(tD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tN(t,e){return(tN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tB(t,e,r){return(e=tR(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tR(t){var e=function(t,e){if("object"!=tT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tT(e)?e:e+""}var tL=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,l=i.xAxis,s=i.yAxis;if(!l||!s)return null;var f=(0,Q.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!Z(i,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,Q.sl)(p,h):null},tz=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=tD(t),function(t,e){if(e&&("object"===tT(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,tI()?Reflect.construct(t,e||[],tD(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&tN(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,a=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;(0,tt.R)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,M.vh)(e),f=(0,M.vh)(n),p=(0,M.vh)(o),h=(0,M.vh)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=tL(s,f,p,h,this.props);if(!y&&!d)return null;var v=Z(this.props,"hidden")?"url(#".concat(l,")"):void 0;return i().createElement(x.W,{className:(0,m.A)("recharts-reference-area",c)},r.renderRect(d,tC(tC({clipPath:v},(0,S.J9)(this.props,!0)),y)),J.J.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tR(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i().Component);function tU(t){return function(t){if(Array.isArray(t))return tF(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tF(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tF(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tF(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}tB(tz,"displayName","ReferenceArea"),tB(tz,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),tB(tz,"renderRect",function(t,e){var r;return i().isValidElement(t)?i().cloneElement(t,e):u()(t)?t(e):i().createElement(A.M,tM({},e,{className:"recharts-reference-area-rect"}))});var tW=function(t,e,r,n,i){var o=(0,S.aS)(t,tk),a=(0,S.aS)(t,tf),c=[].concat(tU(o),tU(a)),u=(0,S.aS)(t,tz),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&Z(e.props,"extendDomain")&&(0,M.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&Z(e.props,"extendDomain")&&(0,M.Et)(e.props[p])&&(0,M.Et)(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return(0,M.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},t$=r(57013),tq=r(4854),tX=r(91352),tH=new(r.n(tX)()),tV="recharts.syncMouseEvents",tK=r(43891);function tG(t){return(tG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tY(t,e,r){return(e=tJ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tJ(t){var e=function(t,e){if("object"!=tG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tG(e)?e:e+""}var tZ=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");tY(this,"activeIndex",0),tY(this,"coordinateList",[]),tY(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tJ(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),tQ=r(91554),t0=r(28118);function t1(t){return(t1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var t2=["x","y","top","left","width","height","className"];function t3(){return(t3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var t8=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,a=t.top,c=void 0===a?0:a,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t5(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=t1(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t1(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,t2));return(0,M.Et)(r)&&(0,M.Et)(o)&&(0,M.Et)(f)&&(0,M.Et)(h)&&(0,M.Et)(c)&&(0,M.Et)(l)?i().createElement("path",t3({},(0,S.J9)(y,!0),{className:(0,m.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(o,"h").concat(f)})):null};function t6(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[(0,t$.IZ)(e,r,n,i),(0,t$.IZ)(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}var t7=r(97173);function t9(t){return(t9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function et(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t4(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=t9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t9(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ee(t){var e,r,i,o,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(i=a.type.defaultProps)?void 0:i.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var b=t0.I;if("ScatterChart"===y)o=l,b=t8;else if("BarChart"===y)e=h/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},b=A.M;else if("radial"===d){var g=t6(l),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=t7.h}else o={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return t6(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,t$.IZ)(c,u,l,f),h=(0,t$.IZ)(c,u,s,f);n=p.x,i=p.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(d,l,f)},b=t0.I;var j=et(et(et(et({stroke:"#ccc",pointerEvents:"none"},f),o),(0,S.J9)(v,!1)),{},{payload:s,payloadIndex:p,className:(0,m.A)("recharts-tooltip-cursor",v.className)});return(0,n.isValidElement)(v)?(0,n.cloneElement)(v,j):(0,n.createElement)(b,j)}var er=["item"],en=["children","className","width","height","style","compact","title","desc"];function ei(t){return(ei="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eo(){return(eo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ea(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||ep(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ec(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function eu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eu=function(){return!!t})()}function el(t){return(el=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function es(t,e){return(es=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ef(t){return function(t){if(Array.isArray(t))return eh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ep(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ep(t,e){if(t){if("string"==typeof t)return eh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eh(t,e)}}function eh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ed(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ey(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ed(Object(r),!0).forEach(function(e){ev(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ed(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ev(t,e,r){return(e=em(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function em(t){var e=function(t,e){if("object"!=ei(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ei(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ei(e)?e:e+""}var eb={xAxis:["bottom","top"],yAxis:["left","right"]},eg={width:"100%",height:"100%"},ex={x:0,y:0};function eO(t){return t}var ew=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return ey(ey(ey({},n),(0,t$.IZ)(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return ey(ey(ey({},n),(0,t$.IZ)(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return ex},ej=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(ef(t),ef(r)):t},[]);return o.length>0?o:t&&t.length&&(0,M.Et)(n)&&(0,M.Et)(i)?t.slice(n,i+1):[]};function eA(t){return"number"===t?[0,"auto"]:void 0}var eS=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=ej(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var u,l,s=null!=(u=c.props.data)?u:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),o.dataKey&&!o.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,M.eP)(f,o.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(ef(i),[(0,k.zb)(c,l)]):i},[])},eP=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=(0,k.gH)(o,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=eS(t,e,l,s),p=ew(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eE=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,c=e.stackGroups,u=e.dataStartIndex,l=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,k._L)(f,i);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?ey(ey({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,O=y.ticks,w=y.includeHidden,j=y[o];if(e[j])return e;var A=ej(t.data,{graphicalItems:n.filter(function(t){var e;return(o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o])===j}),dataStartIndex:u,dataEndIndex:l}),S=A.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&(0,M.Et)(n)&&(0,M.Et)(i))return!0}return!1})(y.domain,b,v)&&(T=(0,k.AQ)(y.domain,null,b),d&&("number"===v||"auto"!==x)&&(C=(0,k.Ay)(A,m,"category")));var P=eA(v);if(!T||0===T.length){var E,T,_,C,I,D=null!=(I=y.domain)?I:P;if(m){if(T=(0,k.Ay)(A,m,v),"category"===v&&d){var N=(0,M.CG)(T);g&&N?(_=T,T=s()(0,S)):g||(T=(0,k.KC)(D,T,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(ef(t),[e])},[]))}else if("category"===v)T=g?T.filter(function(t){return""!==t&&!a()(t)}):(0,k.KC)(D,T,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||a()(e)?t:[].concat(ef(t),[e])},[]);else if("number"===v){var B=(0,k.A1)(A,n.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===j&&(w||!i)}),m,i,f);B&&(T=B)}d&&("number"===v||"auto"!==x)&&(C=(0,k.Ay)(A,m,"category"))}else T=d?s()(0,S):c&&c[j]&&c[j].hasStack&&"number"===v?"expand"===h?[0,1]:(0,k.Mk)(c[j].stackGroups,u,l):(0,k.vf)(A,n.filter(function(t){var e=o in t.props?t.props[o]:t.type.defaultProps[o],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(w||!r)}),v,f,!0);"number"===v?(T=tW(p,T,j,i,O),D&&(T=(0,k.AQ)(D,T,b))):"category"===v&&D&&T.every(function(t){return D.indexOf(t)>=0})&&(T=D)}return ey(ey({},e),{},ev({},j,ey(ey({},y),{},{axisType:i,domain:T,categoricalDomain:C,duplicateDomain:_,originalDomain:null!=(E=y.domain)?E:P,isCategorical:d,layout:f})))},{})},ek=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,f=t.children,h=ej(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),d=h.length,y=(0,k._L)(l,i),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?ey(ey({},e.type.defaultProps),e.props):e.props)[o],g=eA("number");return t[b]?t:(v++,m=y?s()(0,d):a&&a[b]&&a[b].hasStack?tW(f,m=(0,k.Mk)(a[b].stackGroups,c,u),b,i):tW(f,m=(0,k.AQ)(g,(0,k.vf)(h,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===b&&!i}),"number",l),n.defaultProps.allowDataOverflow),b,i),ey(ey({},t),{},ev({},b,ey(ey({axisType:i},n.defaultProps),{},{hide:!0,orientation:p()(eb,"".concat(i,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:l}))))},{})},eM=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,S.aS)(l,i),p={};return f&&f.length?p=eE(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=ek(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},eT=function(t){var e=(0,M.lX)(t),r=(0,k.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:d()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,k.Hj)(e,r)}},e_=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,S.BU)(e,K),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},eC=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},eI=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},h=(0,S.BU)(s,K),d=(0,S.BU)(s,w.s),y=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:ey(ey({},t),{},ev({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),v=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:ey(ey({},t),{},ev({},n,p()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=ey(ey({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||K.defaultProps.height),d&&e&&(m=(0,k.s0)(m,n,r,e));var g=u-m.left-m.right,x=l-m.top-m.bottom;return ey(ey({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},eD=function(t){var e=t.chartName,r=t.GraphicalChild,o=t.defaultTooltipEventType,c=void 0===o?"axis":o,l=t.validateTooltipEventTypes,s=void 0===l?["axis"]:l,f=t.axisComponents,h=t.legendContent,d=t.formatAxisMap,y=t.defaultProps,w=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,c=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,s=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=eC(s),v=y.numericAxisName,m=y.cateAxisName,g=!!r&&!!r.length&&r.some(function(t){var e=(0,S.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var O=ej(t.data,{graphicalItems:[r],dataStartIndex:c,dataEndIndex:u}),w=void 0!==r.type.defaultProps?ey(ey({},r.type.defaultProps),r.props):r.props,j=w.dataKey,A=w.maxBarSize,P=w["".concat(v,"Id")],E=w["".concat(m,"Id")],M=f.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=w["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||(0,b.A)(!1);var o=n[i];return ey(ey({},t),{},ev(ev({},r.axisType,o),"".concat(r.axisType,"Ticks"),(0,k.Rh)(o)))},{}),T=M[m],_=M["".concat(m,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,k.kA)(r,n[P].stackGroups),I=(0,S.Mn)(r.type).indexOf("Bar")>=0,D=(0,k.Hj)(T,_),N=[],B=g&&(0,k.tA)({barSize:l,stackGroups:n,totalSize:"xAxis"===m?M[m].width:"yAxis"===m?M[m].height:void 0});if(I){var R,L,z=a()(A)?d:A,U=null!=(R=null!=(L=(0,k.Hj)(T,_,!0))?L:z)?R:0;N=(0,k.BX)({barGap:p,barCategoryGap:h,bandSize:U!==D?U:D,sizeList:B[E],maxBarSize:z}),U!==D&&(N=N.map(function(t){return ey(ey({},t),{},{position:ey(ey({},t.position),{},{offset:t.position.offset-U/2})})}))}var F=r&&r.type&&r.type.getComposedData;F&&x.push({props:ey(ey({},F(ey(ey({},M),{},{displayedData:O,props:t,dataKey:j,item:r,bandSize:D,barPosition:N,offset:i,stackedData:C,layout:s,dataStartIndex:c,dataEndIndex:u}))),{},ev(ev(ev({key:r.key||"item-".concat(y)},v,M[v]),m,M[m]),"animationId",o)),childIndex:(0,S.AW)(r,t.children),item:r})}),x},P=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!(0,S.Me)({props:i}))return null;var u=i.children,l=i.layout,s=i.stackOffset,p=i.data,h=i.reverseStackOrder,y=eC(l),v=y.numericAxisName,m=y.cateAxisName,b=(0,S.aS)(u,r),g=(0,k.Mn)(p,b,"".concat(v,"Id"),"".concat(m,"Id"),s,h),x=f.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ey(ey({},t),{},ev({},r,eM(i,ey(ey({},e),{},{graphicalItems:b,stackGroups:e.axisType===v&&g,dataStartIndex:o,dataEndIndex:a}))))},{}),O=eI(ey(ey({},x),{},{props:i,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(t){x[t]=d(i,x[t],O,t.replace("Map",""),e)});var j=eT(x["".concat(m,"Map")]),A=w(i,ey(ey({},x),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:b,stackGroups:g,offset:O}));return ey(ey({formattedGraphicalItems:A,graphicalItems:b,offset:O,stackGroups:g},j),x)},E=function(t){var r;function o(t){var r,c,l,s,f;if(!(this instanceof o))throw TypeError("Cannot call a class as a function");return s=o,f=[t],s=el(s),ev(l=function(t,e){if(e&&("object"===ei(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eu()?Reflect.construct(s,f||[],el(this).constructor):s.apply(this,f)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ev(l,"accessibilityManager",new tZ),ev(l,"handleLegendBBoxUpdate",function(t){if(t){var e=l.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;l.setState(ey({legendBBox:t},P({props:l.props,dataStartIndex:r,dataEndIndex:n,updateId:i},ey(ey({},l.state),{},{legendBBox:t}))))}}),ev(l,"handleReceiveSyncEvent",function(t,e,r){l.props.syncId===t&&(r!==l.eventEmitterSymbol||"function"==typeof l.props.syncMethod)&&l.applySyncEvent(e)}),ev(l,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==l.state.dataStartIndex||r!==l.state.dataEndIndex){var n=l.state.updateId;l.setState(function(){return ey({dataStartIndex:e,dataEndIndex:r},P({props:l.props,dataStartIndex:e,dataEndIndex:r,updateId:n},l.state))}),l.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),ev(l,"handleMouseEnter",function(t){var e=l.getMouseInfo(t);if(e){var r=ey(ey({},e),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseEnter;u()(n)&&n(r,t)}}),ev(l,"triggeredAfterMouseMove",function(t){var e=l.getMouseInfo(t),r=e?ey(ey({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseMove;u()(n)&&n(r,t)}),ev(l,"handleItemMouseEnter",function(t){l.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),ev(l,"handleItemMouseLeave",function(){l.setState(function(){return{isTooltipActive:!1}})}),ev(l,"handleMouseMove",function(t){t.persist(),l.throttleTriggeredAfterMouseMove(t)}),ev(l,"handleMouseLeave",function(t){l.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};l.setState(e),l.triggerSyncEvent(e);var r=l.props.onMouseLeave;u()(r)&&r(e,t)}),ev(l,"handleOuterEvent",function(t){var e,r,n=(0,S.X_)(t),i=p()(l.props,"".concat(n));n&&u()(i)&&i(null!=(e=/.*touch.*/i.test(n)?l.getMouseInfo(t.changedTouches[0]):l.getMouseInfo(t))?e:{},t)}),ev(l,"handleClick",function(t){var e=l.getMouseInfo(t);if(e){var r=ey(ey({},e),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onClick;u()(n)&&n(r,t)}}),ev(l,"handleMouseDown",function(t){var e=l.props.onMouseDown;u()(e)&&e(l.getMouseInfo(t),t)}),ev(l,"handleMouseUp",function(t){var e=l.props.onMouseUp;u()(e)&&e(l.getMouseInfo(t),t)}),ev(l,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),ev(l,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.handleMouseDown(t.changedTouches[0])}),ev(l,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.handleMouseUp(t.changedTouches[0])}),ev(l,"handleDoubleClick",function(t){var e=l.props.onDoubleClick;u()(e)&&e(l.getMouseInfo(t),t)}),ev(l,"handleContextMenu",function(t){var e=l.props.onContextMenu;u()(e)&&e(l.getMouseInfo(t),t)}),ev(l,"triggerSyncEvent",function(t){void 0!==l.props.syncId&&tH.emit(tV,l.props.syncId,t,l.eventEmitterSymbol)}),ev(l,"applySyncEvent",function(t){var e=l.props,r=e.layout,n=e.syncMethod,i=l.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)l.setState(ey({dataStartIndex:o,dataEndIndex:a},P({props:l.props,dataStartIndex:o,dataEndIndex:a,updateId:i},l.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,u=t.chartY,s=t.activeTooltipIndex,f=l.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ey(ey({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),m=Math.min(u,y.y+y.height),b=h[s]&&h[s].value,g=eS(l.state,l.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:ex;l.setState(ey(ey({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else l.setState(t)}),ev(l,"renderCursor",function(t){var r,n=l.state,o=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,u=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=l.getTooltipEventType(),h=null!=(r=t.props.active)?r:o,d=l.props.layout,y=t.key||"_recharts-cursor";return i().createElement(ee,{key:y,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:u,tooltipAxisBandSize:f,tooltipEventType:p})}),ev(l,"renderPolarAxis",function(t,e,r){var i=p()(t,"type.axisType"),o=p()(l.state,"".concat(i,"Map")),a=t.type.defaultProps,c=void 0!==a?ey(ey({},a),t.props):t.props,u=o&&o[c["".concat(i,"Id")]];return(0,n.cloneElement)(t,ey(ey({},u),{},{className:(0,m.A)(i,u.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,k.Rh)(u,!0)}))}),ev(l,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,i=e.polarAngles,o=e.polarRadius,a=l.state,c=a.radiusAxisMap,u=a.angleAxisMap,s=(0,M.lX)(c),f=(0,M.lX)(u),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(i)?i:(0,k.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:(0,k.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),ev(l,"renderLegend",function(){var t=l.state.formattedGraphicalItems,e=l.props,r=e.children,i=e.width,o=e.height,a=l.props.margin||{},c=i-(a.left||0)-(a.right||0),u=(0,Y.g)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:h});if(!u)return null;var s=u.item,f=ec(u,er);return(0,n.cloneElement)(s,ey(ey({},f),{},{chartWidth:i,chartHeight:o,margin:a,onBBoxUpdate:l.handleLegendBBoxUpdate}))}),ev(l,"renderTooltip",function(){var t,e=l.props,r=e.children,i=e.accessibilityLayer,o=(0,S.BU)(r,O.m);if(!o)return null;var a=l.state,c=a.isTooltipActive,u=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=o.props.active)?t:c;return(0,n.cloneElement)(o,{viewBox:ey(ey({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:u,accessibilityLayer:i})}),ev(l,"renderBrush",function(t){var e=l.props,r=e.margin,i=e.data,o=l.state,a=o.offset,c=o.dataStartIndex,u=o.dataEndIndex,s=o.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,k.HQ)(l.handleBrushChange,t.props.onChange),data:i,x:(0,M.Et)(t.props.x)?t.props.x:a.left,y:(0,M.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,M.Et)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:u,updateId:"brush-".concat(s)})}),ev(l,"renderReferenceElement",function(t,e,r){if(!t)return null;var i=l.clipPathId,o=l.state,a=o.xAxisMap,c=o.yAxisMap,u=o.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:c[y],viewBox:{x:u.left,y:u.top,width:u.width,height:u.height},clipPathId:i})}),ev(l,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?ey(ey({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ey(ey({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,k.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,S.J9)(s,!1)),(0,tK._U)(s));return c.push(o.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),n?c.push(o.renderActiveDot(s,ey(ey({},f),{},{cx:n.x,cy:n.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),ev(l,"renderGraphicChild",function(t,e,r){var i=l.filterFormatItem(t,e,r);if(!i)return null;var o=l.getTooltipEventType(),c=l.state,u=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,h=l.props.children,d=(0,S.BU)(h,O.m),y=i.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==i.item.type.defaultProps?ey(ey({},i.item.type.defaultProps),i.item.props):i.item.props,x=g.activeDot,w=g.hide,j=g.activeBar,A=g.activeShape,P=!!(!w&&u&&d&&(x||j||A)),E={};"axis"!==o&&d&&"click"===d.props.trigger?E={onClick:(0,k.HQ)(l.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(E={onMouseLeave:(0,k.HQ)(l.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,k.HQ)(l.handleItemMouseEnter,t.props.onMouseEnter)});var T=(0,n.cloneElement)(t,ey(ey({},i.props),E));if(P)if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var _="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());I=(0,M.eP)(v,_,p),D=m&&b&&(0,M.eP)(b,_,p)}else I=null==v?void 0:v[f],D=m&&b&&b[f];if(A||j){var C=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,ey(ey(ey({},i.props),E),{},{activeIndex:C})),null,null]}if(!a()(I))return[T].concat(ef(l.renderActivePoints({item:i,activePoint:I,basePoint:D,childIndex:f,isRange:m})))}else{var I,D,N,B=(null!=(N=l.getItemByXY(l.state.activeCoordinate))?N:{graphicalItem:T}).graphicalItem,R=B.item,L=void 0===R?t:R,z=B.childIndex,U=ey(ey(ey({},i.props),E),{},{activeIndex:z});return[(0,n.cloneElement)(L,U),null,null]}return m?[T,null,null]:[T,null]}),ev(l,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,ey(ey({key:"recharts-customized-".concat(r)},l.props),l.state))}),ev(l,"renderMap",{CartesianGrid:{handler:eO,once:!0},ReferenceArea:{handler:l.renderReferenceElement},ReferenceLine:{handler:eO},ReferenceDot:{handler:l.renderReferenceElement},XAxis:{handler:eO},YAxis:{handler:eO},Brush:{handler:l.renderBrush,once:!0},Bar:{handler:l.renderGraphicChild},Line:{handler:l.renderGraphicChild},Area:{handler:l.renderGraphicChild},Radar:{handler:l.renderGraphicChild},RadialBar:{handler:l.renderGraphicChild},Scatter:{handler:l.renderGraphicChild},Pie:{handler:l.renderGraphicChild},Funnel:{handler:l.renderGraphicChild},Tooltip:{handler:l.renderCursor,once:!0},PolarGrid:{handler:l.renderPolarGrid,once:!0},PolarAngleAxis:{handler:l.renderPolarAxis},PolarRadiusAxis:{handler:l.renderPolarAxis},Customized:{handler:l.renderCustomized}}),l.clipPathId="".concat(null!=(r=t.id)?r:(0,M.NF)("recharts"),"-clip"),l.throttleTriggeredAfterMouseMove=v()(l.triggeredAfterMouseMove,null!=(c=t.throttleDelay)?c:1e3/60),l.state={},l}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return o.prototype=Object.create(t&&t.prototype,{constructor:{value:o,writable:!0,configurable:!0}}),Object.defineProperty(o,"prototype",{writable:!1}),t&&es(o,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=(0,S.BU)(e,O.m);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=eS(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ey(ey({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,S.OV)([(0,S.BU)(t.children,O.m)],[(0,S.BU)(this.props.children,O.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,S.BU)(this.props.children,O.m);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return s.indexOf(e)>=0?e:c}return c}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,G.A3)(r),i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap;if("axis"!==this.getTooltipEventType()&&u&&l){var s=(0,M.lX)(u).scale,f=(0,M.lX)(l).scale,p=s&&s.invert?s.invert(i.chartX):null,h=f&&f.invert?f.invert(i.chartY):null;return ey(ey({},i),{},{xValue:p,yValue:h})}var d=eP(this.state,this.props.data,this.props.layout,a);return d?ey(ey({},i),d):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=(0,M.lX)(u);return(0,t$.yy)({x:i,y:o},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,S.BU)(t,O.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ey(ey({},(0,tK._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){tH.on(tV,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){tH.removeListener(tV,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===(0,S.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,a=e.width;return i().createElement("defs",null,i().createElement("clipPath",{id:t},i().createElement("rect",{x:r,y:n,height:o,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ea(e,2),n=r[0],i=r[1];return ey(ey({},t),{},ev({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ea(e,2),n=r[0],i=r[1];return ey(ey({},t),{},ev({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?ey(ey({},u.type.defaultProps),u.props):u.props,s=(0,S.Mn)(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return(0,A.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return(0,t$.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,tQ.NE)(a,n)||(0,tQ.nZ)(a,n)||(0,tQ.xQ)(a,n)){var h=(0,tQ.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ey(ey({},a),{},{childIndex:d}),payload:(0,tQ.xQ)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,S.Me)(this))return null;var n=this.props,o=n.children,a=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=ec(n,en),d=(0,S.J9)(h,!1);if(s)return i().createElement(td.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},i().createElement(g.u,eo({},d,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),(0,S.ee)(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!=(t=this.props.tabIndex)?t:0,d.role=null!=(e=this.props.role)?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return i().createElement(td.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},i().createElement("div",eo({className:(0,m.A)("recharts-wrapper",a),style:ey({position:"relative",cursor:"default",width:c,height:u},l)},y,{ref:function(t){r.container=t}}),i().createElement(g.u,eo({},d,{width:c,height:u,title:f,desc:p,style:eg}),this.renderClipPath(),(0,S.ee)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,em(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.Component);ev(E,"displayName",e),ev(E,"defaultProps",ey({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y)),ev(E,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,o=t.width,c=t.height,u=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=e_(t);return ey(ey(ey({},h),{},{updateId:0},P(ey(ey({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||o!==e.prevWidth||c!==e.prevHeight||u!==e.prevLayout||l!==e.prevStackOffset||!(0,tq.b)(s,e.prevMargin)){var d=e_(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ey(ey({},eP(e,n,u)),{},{updateId:e.updateId+1}),m=ey(ey(ey({},d),y),v);return ey(ey(ey({},m),P(ey({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:i})}if(!(0,S.OV)(i,e.prevChildren)){var b,g,x,O,w=(0,S.BU)(i,K),j=w&&null!=(b=null==(g=w.props)?void 0:g.startIndex)?b:f,A=w&&null!=(x=null==(O=w.props)?void 0:O.endIndex)?x:p,E=a()(n)||j!==f||A!==p?e.updateId+1:e.updateId;return ey(ey({updateId:E},P(ey(ey({props:t},e),{},{updateId:E,dataStartIndex:j,dataEndIndex:A}),e)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:A})}return null}),ev(E,"renderActiveDot",function(t,e,r){var o;return o=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):u()(t)?t(e):i().createElement(j.c,e),i().createElement(x.W,{className:"recharts-active-dot",key:r},o)});var T=(0,n.forwardRef)(function(t,e){return i().createElement(E,eo({},t,{ref:e}))});return T.displayName=E.displayName,T}},25048:(t,e,r)=>{"use strict";t.exports=r(63836)},27085:(t,e,r)=>{"use strict";r.d(e,{Q:()=>u});var n=r(24035),i=r(97803),o=r(55041),a=r(70822),c=r(40643),u=(0,n.gu)({chartName:"AreaChart",GraphicalChild:i.G,axisComponents:[{axisType:"xAxis",AxisComp:o.W},{axisType:"yAxis",AxisComp:a.h}],formatAxisMap:c.pr})},27547:(t,e,r)=>{"use strict";r.d(e,{i:()=>D});var n=r(60222),i=r.n(n),o=r(39276),a=r.n(o);let c=Math.cos,u=Math.sin,l=Math.sqrt,s=Math.PI,f=2*s,p={draw(t,e){let r=l(e/s);t.moveTo(r,0),t.arc(0,0,r,0,f)}},h=l(1/3),d=2*h,y=u(s/10)/u(7*s/10),v=u(f/10)*y,m=-c(f/10)*y,b=l(3),g=l(3)/2,x=1/l(12),O=(x/2+1)*3;var w=r(42369),j=r(71858);l(3),l(3);var A=r(70991),S=r(23872);function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var E=["type","size","sizeType"];function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var _={symbolCircle:p,symbolCross:{draw(t,e){let r=l(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=l(e/d),n=r*h;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=l(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=l(.8908130915292852*e),n=v*r,i=m*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=f*e/5,a=c(o),l=u(o);t.lineTo(l*r,-a*r),t.lineTo(a*n-l*i,l*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-l(e/(3*b));t.moveTo(0,2*r),t.lineTo(-b*r,-r),t.lineTo(b*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=l(e/O),n=r/2,i=r*x,o=r*x+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-g*i,g*n+-.5*i),t.lineTo(-.5*n-g*o,g*n+-.5*o),t.lineTo(-.5*a-g*o,g*a+-.5*o),t.lineTo(-.5*n+g*i,-.5*i-g*n),t.lineTo(-.5*n+g*o,-.5*o-g*n),t.lineTo(-.5*a+g*o,-.5*o-g*a),t.closePath()}}},C=Math.PI/180,I=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*C;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},D=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,c=void 0===o?64:o,u=t.sizeType,l=void 0===u?"area":u,s=T(T({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,E)),{},{type:n,size:c,sizeType:l}),f=s.className,h=s.cx,d=s.cy,y=(0,S.J9)(s,!0);return h===+h&&d===+d&&c===+c?i().createElement("path",k({},y,{className:(0,A.A)("recharts-symbols",f),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=_["symbol".concat(a()(n))]||p,(function(t,e){let r=null,n=(0,j.i)(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:(0,w.A)(t||p),e="function"==typeof e?e:(0,w.A)(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,w.A)(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,w.A)(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(I(c,l,n))())})):null};D.registerSymbol=function(t,e){_["symbol".concat(a()(t))]=e}},28118:(t,e,r)=>{"use strict";r.d(e,{I:()=>K});var n=r(60222),i=r.n(n);function o(){}function a(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function c(t){this._context=t}function u(t){this._context=t}function l(t){this._context=t}c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},l.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class s{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function f(t){this._context=t}function p(t){this._context=t}function h(t){return new p(t)}f.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function d(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function y(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function v(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function m(t){this._context=t}function b(t){this._context=new g(t)}function g(t){this._context=t}function x(t){this._context=t}function O(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function w(t,e){this._context=t,this._t=e}p.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},m.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v(this,this._t0,y(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,v(this,y(this,r=d(this,t,e)),r);break;default:v(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(b.prototype=Object.create(m.prototype)).point=function(t,e){m.prototype.point.call(this,e,t)},g.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=O(t),i=O(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var j=r(78068),A=r(42369),S=r(71858);function P(t){return t[0]}function E(t){return t[1]}function k(t,e){var r=(0,A.A)(!0),n=null,i=h,o=null,a=(0,S.i)(c);function c(c){var u,l,s,f=(c=(0,j.A)(c)).length,p=!1;for(null==n&&(o=i(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(l,u,c),+e(l,u,c));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?P:(0,A.A)(t),e="function"==typeof e?e:void 0===e?E:(0,A.A)(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,A.A)(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,A.A)(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,A.A)(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function M(t,e,r){var n=null,i=(0,A.A)(!0),o=null,a=h,c=null,u=(0,S.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,j.A)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&i(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return k().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?P:(0,A.A)(+t),e="function"==typeof e?e:void 0===e?(0,A.A)(0):(0,A.A)(+e),r="function"==typeof r?r:void 0===r?E:(0,A.A)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,A.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,A.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,A.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,A.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,A.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,A.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:(0,A.A)(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),l):o},l}var T=r(39276),_=r.n(T),C=r(2802),I=r.n(C),D=r(70991),N=r(43891),B=r(23872),R=r(1511);function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(){return(z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var W={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new l(t)},curveBasis:function(t){return new c(t)},curveBumpX:function(t){return new s(t,!0)},curveBumpY:function(t){return new s(t,!1)},curveLinearClosed:function(t){return new f(t)},curveLinear:h,curveMonotoneX:function(t){return new m(t)},curveMonotoneY:function(t){return new b(t)},curveNatural:function(t){return new x(t)},curveStep:function(t){return new w(t,.5)},curveStepAfter:function(t){return new w(t,1)},curveStepBefore:function(t){return new w(t,0)}},$=function(t){return t.x===+t.x&&t.y===+t.y},q=function(t){return t.x},X=function(t){return t.y},H=function(t,e){if(I()(t))return t;var r="curve".concat(_()(t));return("curveMonotone"===r||"curveBump"===r)&&e?W["".concat(r).concat("vertical"===e?"Y":"X")]:W[r]||h},V=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=H(void 0===r?"linear":r,a),s=u?i.filter(function(t){return $(t)}):i;if(Array.isArray(o)){var f=u?o.filter(function(t){return $(t)}):o,p=s.map(function(t,e){return F(F({},t),{},{base:f[e]})});return(e="vertical"===a?M().y(X).x1(q).x0(function(t){return t.base.x}):M().x(q).y1(X).y0(function(t){return t.base.y})).defined($).curve(l),e(p)}return(e="vertical"===a&&(0,R.Et)(o)?M().y(X).x1(q).x0(o):(0,R.Et)(o)?M().x(q).y1(X).y0(o):k().x(q).y(X)).defined($).curve(l),e(s)},K=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var a=r&&r.length?V(t):n;return i().createElement("path",z({},(0,B.J9)(t,!1),(0,N._U)(t),{className:(0,D.A)("recharts-curve",e),d:a,ref:o}))}},28511:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},31347:(t,e,r)=>{"use strict";r.d(e,{J:()=>P});var n=r(60222),i=r.n(n),o=r(8875),a=r.n(o),c=r(2802),u=r.n(c),l=r(56097),s=r.n(l),f=r(70991),p=r(87158),h=r(23872),d=r(1511),y=r(57013);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["offset"];function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var w=function(t){var e=t.value,r=t.formatter,n=a()(t.children)?e:t.children;return u()(r)?r(n):n},j=function(t,e,r){var n,o,c=t.position,u=t.viewBox,l=t.offset,s=t.className,p=u.cx,h=u.cy,v=u.innerRadius,m=u.outerRadius,b=u.startAngle,g=u.endAngle,x=u.clockWise,w=(v+m)/2,j=(0,d.sA)(g-b)*Math.min(Math.abs(g-b),360),A=j>=0?1:-1;"insideStart"===c?(n=b+A*l,o=x):"insideEnd"===c?(n=g-A*l,o=!x):"end"===c&&(n=g+A*l,o=x),o=j<=0?o:!o;var S=(0,y.IZ)(p,h,w,n),P=(0,y.IZ)(p,h,w,n+(o?1:-1)*359),E="M".concat(S.x,",").concat(S.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!o,",\n    ").concat(P.x,",").concat(P.y),k=a()(t.id)?(0,d.NF)("recharts-radial-line-"):t.id;return i().createElement("text",O({},r,{dominantBaseline:"central",className:(0,f.A)("recharts-radial-bar-label",s)}),i().createElement("defs",null,i().createElement("path",{id:k,d:E})),i().createElement("textPath",{xlinkHref:"#".concat(k)},e))},A=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,y.IZ)(i,o,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=(0,y.IZ)(i,o,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},S=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,f=l*n,p=l>0?"end":"start",h=l>0?"start":"end",y=c>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===i)return x(x({},{x:o+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return x(x({},{x:o+c/2,y:a+u+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===i){var g={x:o-v,y:a+u/2,textAnchor:m,verticalAnchor:"middle"};return x(x({},g),r?{width:Math.max(g.x-r.x,0),height:u}:{})}if("right"===i){var O={x:o+c+v,y:a+u/2,textAnchor:b,verticalAnchor:"middle"};return x(x({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?x({x:o+v,y:a+u/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===i?x({x:o+c-v,y:a+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===i?x({x:o+c/2,y:a+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===i?x({x:o+c/2,y:a+u-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===i?x({x:o+v,y:a+f,textAnchor:b,verticalAnchor:h},w):"insideTopRight"===i?x({x:o+c-v,y:a+f,textAnchor:m,verticalAnchor:h},w):"insideBottomLeft"===i?x({x:o+v,y:a+u-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===i?x({x:o+c-v,y:a+u-f,textAnchor:m,verticalAnchor:p},w):s()(i)&&((0,d.Et)(i.x)||(0,d._3)(i.x))&&((0,d.Et)(i.y)||(0,d._3)(i.y))?x({x:o+(0,d.F4)(i.x,c),y:a+(0,d.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):x({x:o+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function P(t){var e,r=t.offset,o=x({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,m)),c=o.viewBox,l=o.position,s=o.value,y=o.children,v=o.content,b=o.className,g=o.textBreakAll;if(!c||a()(s)&&a()(y)&&!(0,n.isValidElement)(v)&&!u()(v))return null;if((0,n.isValidElement)(v))return(0,n.cloneElement)(v,o);if(u()(v)){if(e=(0,n.createElement)(v,o),(0,n.isValidElement)(e))return e}else e=w(o);var P="cx"in c&&(0,d.Et)(c.cx),E=(0,h.J9)(o,!0);if(P&&("insideStart"===l||"insideEnd"===l||"end"===l))return j(o,e,E);var k=P?A(o):S(o);return i().createElement(p.E,O({className:(0,f.A)("recharts-label",void 0===b?"":b)},E,k,{breakAll:g}),e)}P.displayName="Label";var E=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,d.Et)(y)&&(0,d.Et)(v)){if((0,d.Et)(s)&&(0,d.Et)(f))return{x:s,y:f,width:y,height:v};if((0,d.Et)(p)&&(0,d.Et)(h))return{x:p,y:h,width:y,height:v}}return(0,d.Et)(s)&&(0,d.Et)(f)?{x:s,y:f,width:0,height:0}:(0,d.Et)(e)&&(0,d.Et)(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};P.parseViewBox=E,P.renderCallByParent=function(t,e){var r,o,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&a&&!t.label)return null;var c=t.children,l=E(t),f=(0,h.aS)(c,P).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||l,key:"label-".concat(r)})});if(!a)return f;return[(r=t.label,o=e||l,!r?null:!0===r?i().createElement(P,{key:"label-implicit",viewBox:o}):(0,d.vh)(r)?i().createElement(P,{key:"label-implicit",viewBox:o,value:r}):(0,n.isValidElement)(r)?r.type===P?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:o}):i().createElement(P,{key:"label-implicit",content:r,viewBox:o}):u()(r)?i().createElement(P,{key:"label-implicit",content:r,viewBox:o}):s()(r)?i().createElement(P,O({viewBox:o},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return b(t)}(f)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(f)||function(t,e){if(t){if("string"==typeof t)return b(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}}(f)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())}},32178:(t,e,r)=>{var n=r(50527),i=r(54919),o=r(80212);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},33612:(t,e,r)=>{"use strict";r.d(e,{y:()=>F});var n=r(60222),i=r.n(n),o=r(70991),a=r(86704),c=r(29736),u=r.n(c),l=r(8875),s=r.n(l),f=r(42956),p=r(13405),h=r(89389),d=r(43371),y=r(1511),v=r(23872),m=r(10427),b=r(12810),g=r(43891),x=r(61498),O=r(91554),w=["x","y"];function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function A(){return(A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function P(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function E(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,w),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),u=parseInt("".concat(e.width||i.width),10);return P(P(P(P(P({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function k(t){return i().createElement(O.yp,A({shapeType:"rectangle",propTransformer:E,activeClassName:"recharts-active-bar"},t))}var M=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||(0,x.A)(!1),e)}},T=["value","background"];function _(t){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function C(){return(C=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){z(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function N(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,U(n.key),n)}}function B(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(B=function(){return!!t})()}function R(t){return(R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function L(t,e){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function z(t,e,r){return(e=U(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function U(t){var e=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_(e)?e:e+""}var F=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=R(e),z(t=function(t,e){if(e&&("object"===_(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,B()?Reflect.construct(e,r||[],R(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),z(t,"id",(0,y.NF)("recharts-bar-")),z(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),z(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&L(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,a=r.activeIndex,c=r.activeBar,u=(0,v.J9)(this.props,!1);return t&&t.map(function(t,r){var l=r===a,s=D(D(D({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return i().createElement(f.W,C({className:"recharts-bar-rectangle"},(0,g.XC)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),i().createElement(k,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,c=e.animationBegin,u=e.animationDuration,l=e.animationEasing,s=e.animationId,p=this.state.prevData;return i().createElement(a.Ay,{begin:c,duration:u,isActive:o,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var i=(0,y.Dj)(r.x,t.x),a=(0,y.Dj)(r.y,t.y),c=(0,y.Dj)(r.width,t.width),u=(0,y.Dj)(r.height,t.height);return D(D({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var l=(0,y.Dj)(0,t.height)(o);return D(D({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,y.Dj)(0,t.width)(o);return D(D({},t),{},{width:s})});return i().createElement(f.W,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!u()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,a=(0,v.J9)(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,T);if(!c)return null;var l=D(D(D(D(D({},u),{},{fill:"#eee"},c),a),(0,g.XC)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return i().createElement(k,C({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,v.aS)(u,p.u);if(!l)return null;var s="vertical"===c?n[0].height/2:n[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,b.kr)(t,e)}};return i().createElement(f.W,{clipPath:t?"url(#clipPath-".concat(e,")"):null},l.map(function(t){return i().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:a,layout:c,offset:s,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,a=t.xAxis,c=t.yAxis,u=t.left,l=t.top,p=t.width,h=t.height,y=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,o.A)("recharts-bar",n),x=a&&a.allowDataOverflow,O=c&&c.allowDataOverflow,w=x||O,j=s()(m)?this.id:m;return i().createElement(f.W,{className:g},x||O?i().createElement("defs",null,i().createElement("clipPath",{id:"clipPath-".concat(j)},i().createElement("rect",{x:x?u:u-p/2,y:O?l:l-h/2,width:x?p:2*p,height:O?h:2*h}))):null,i().createElement(f.W,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,j),(!y||b)&&d.Z.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&N(n.prototype,e),r&&N(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);z(F,"displayName","Bar"),z(F,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!m.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),z(F,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,d=(0,b.xi)(n,r);if(!d)return null;var m=e.layout,g=r.type.defaultProps,x=void 0!==g?D(D({},g),r.props):r.props,O=x.dataKey,w=x.children,j=x.minPointSize,A="horizontal"===m?a:o,S=l?A.scale.domain():null,P=(0,b.DW)({numericAxis:A}),E=(0,v.aS)(w,h.f),k=f.map(function(t,e){l?f=(0,b._f)(l[s+e],S):Array.isArray(f=(0,b.kr)(t,O))||(f=[P,f]);var n=M(j,F.defaultProps.minPointSize)(f[1],e);if("horizontal"===m){var f,p,h,v,g,x,w,A=[a.scale(f[0]),a.scale(f[1])],k=A[0],T=A[1];p=(0,b.y2)({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:t,index:e}),h=null!=(w=null!=T?T:k)?w:void 0,v=d.size;var _=k-T;if(g=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var C=(0,y.sA)(g||n)*(Math.abs(n)-Math.abs(g));h-=C,g+=C}}else{var I=[o.scale(f[0]),o.scale(f[1])],N=I[0],B=I[1];if(p=N,h=(0,b.y2)({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:t,index:e}),v=B-N,g=d.size,x={x:o.x,y:h,width:o.width,height:g},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var R=(0,y.sA)(v||n)*(Math.abs(n)-Math.abs(v));v+=R}}return D(D(D({},t),{},{x:p,y:h,width:v,height:g,value:l?f:f[1],payload:t,background:x},E&&E[e]&&E[e].props),{},{tooltipPayload:[(0,b.zb)(r,t)],tooltipPosition:{x:p+v/2,y:h+g/2}})});return D({data:k,layout:m},p)})},35035:(t,e,r)=>{"use strict";r.d(e,{r:()=>tr});var n=r(24035),i=r(60222),o=r.n(i),a=r(2802),c=r.n(a),u=r(70991),l=r(42956),s=r(3380),f=r(23872),p=["points","className","baseLinePoints","connectNulls"];function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return y(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var v=function(t){return t&&t.x===+t.x&&t.y===+t.y},m=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){v(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),v(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},b=function(t,e){var r=m(t);e&&(r=[r.reduce(function(t,e){return[].concat(d(t),d(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},g=function(t,e,r){var n=b(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(b(e.reverse(),r).slice(1))},x=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,i=t.connectNulls,a=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,p);if(!e||!e.length)return null;var c=(0,u.A)("recharts-polygon",r);if(n&&n.length){var l=a.stroke&&"none"!==a.stroke,s=g(e,n,i);return o().createElement("g",{className:c},o().createElement("path",h({},(0,f.J9)(a,!0),{fill:"Z"===s.slice(-1)?a.fill:"none",stroke:"none",d:s})),l?o().createElement("path",h({},(0,f.J9)(a,!0),{fill:"none",d:b(e,i)})):null,l?o().createElement("path",h({},(0,f.J9)(a,!0),{fill:"none",d:b(n,i)})):null)}var d=b(e,i);return o().createElement("path",h({},(0,f.J9)(a,!0),{fill:"Z"===d.slice(-1)?a.fill:"none",className:c,d:d}))},O=r(87158),w=r(43891),j=r(57013);function A(t){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(){return(S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function E(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,I(n.key),n)}}function M(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(M=function(){return!!t})()}function T(t){return(T=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function _(t,e){return(_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function C(t,e,r){return(e=I(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function I(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}var D=Math.PI/180,N=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=T(t),function(t,e){if(e&&("object"===A(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,M()?Reflect.construct(t,e||[],T(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&_(n,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,i=e.radius,o=e.orientation,a=e.tickSize,c=(0,j.IZ)(r,n,i,t.coordinate),u=(0,j.IZ)(r,n,i+("inner"===o?-1:1)*(a||8),t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*D);return r>1e-5?"outer"===e?"start":"end":r<-1e-5?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,i=t.axisLine,a=t.axisLineType,c=E(E({},(0,f.J9)(this.props,!1)),{},{fill:"none"},(0,f.J9)(i,!1));if("circle"===a)return o().createElement(s.c,S({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:n}));var u=this.props.ticks.map(function(t){return(0,j.IZ)(e,r,n,t.coordinate)});return o().createElement(x,S({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,i=e.tick,a=e.tickLine,c=e.tickFormatter,s=e.stroke,p=(0,f.J9)(this.props,!1),h=(0,f.J9)(i,!1),d=E(E({},p),{},{fill:"none"},(0,f.J9)(a,!1)),y=r.map(function(e,r){var f=t.getTickLineCoord(e),y=E(E(E({textAnchor:t.getTickTextAnchor(e)},p),{},{stroke:"none",fill:s},h),{},{index:r,payload:e,x:f.x2,y:f.y2});return o().createElement(l.W,S({className:(0,u.A)("recharts-polar-angle-axis-tick",(0,j.Zk)(i)),key:"tick-".concat(e.coordinate)},(0,w.XC)(t.props,e,r)),a&&o().createElement("line",S({className:"recharts-polar-angle-axis-tick-line"},d,f)),i&&n.renderTickItem(i,y,c?c(e.value,r):e.value))});return o().createElement(l.W,{className:"recharts-polar-angle-axis-ticks"},y)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return!(r<=0)&&e&&e.length?o().createElement(l.W,{className:(0,u.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement(O.E,S({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&k(n.prototype,e),r&&k(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);C(N,"displayName","PolarAngleAxis"),C(N,"axisType","angleAxis"),C(N,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var B=r(46775),R=r.n(B),L=r(97865),z=r.n(L),U=r(31347),F=["cx","cy","angle","ticks","axisLine"],W=["ticks","tick","angle","tickFormatter","stroke"];function $(t){return($="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function q(){return(q=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function X(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function H(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?X(Object(r),!0).forEach(function(e){Z(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function V(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function K(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Q(n.key),n)}}function G(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(G=function(){return!!t})()}function Y(t){return(Y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function J(t,e){return(J=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function Z(t,e,r){return(e=Q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Q(t){var e=function(t,e){if("object"!=$(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==$(e)?e:e+""}var tt=function(t){var e,r;function n(){var t,e;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return t=n,e=arguments,t=Y(t),function(t,e){if(e&&("object"===$(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,G()?Reflect.construct(t,e||[],Y(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&J(n,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,i=r.cx,o=r.cy;return(0,j.IZ)(i,o,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,i=t.ticks,o=R()(i,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:z()(i,function(t){return t.coordinate||0}).coordinate||0,outerRadius:o.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,i=t.ticks,a=t.axisLine,c=V(t,F),u=i.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=(0,j.IZ)(e,r,u[0],n),s=(0,j.IZ)(e,r,u[1],n),p=H(H(H({},(0,f.J9)(c,!1)),{},{fill:"none"},(0,f.J9)(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return o().createElement("line",q({className:"recharts-polar-radius-axis-line"},p))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,i=e.tick,a=e.angle,c=e.tickFormatter,s=e.stroke,p=V(e,W),h=this.getTickTextAnchor(),d=(0,f.J9)(p,!1),y=(0,f.J9)(i,!1),v=r.map(function(e,r){var f=t.getTickValueCoord(e),p=H(H(H(H({textAnchor:h,transform:"rotate(".concat(90-a,", ").concat(f.x,", ").concat(f.y,")")},d),{},{stroke:"none",fill:s},y),{},{index:r},f),{},{payload:e});return o().createElement(l.W,q({className:(0,u.A)("recharts-polar-radius-axis-tick",(0,j.Zk)(i)),key:"tick-".concat(e.coordinate)},(0,w.XC)(t.props,e,r)),n.renderTickItem(i,p,c?c(e.value,r):e.value))});return o().createElement(l.W,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?o().createElement(l.W,{className:(0,u.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),U.J.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement(O.E,q({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&K(n.prototype,e),r&&K(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);Z(tt,"displayName","PolarRadiusAxis"),Z(tt,"axisType","radiusAxis"),Z(tt,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var te=r(1024),tr=(0,n.gu)({chartName:"PieChart",GraphicalChild:te.F,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:N},{axisType:"radiusAxis",AxisComp:tt}],formatAxisMap:j.pr,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},38506:(t,e,r)=>{"use strict";r.d(e,{s:()=>c});var n=r(2398),i=r.n(n),o=r(2802),a=r.n(o);function c(t,e,r){return!0===e?i()(t,r):a()(e)?i()(t,e):t}},39276:(t,e,r)=>{t.exports=r(89107)("toUpperCase")},40643:(t,e,r)=>{"use strict";r.d(e,{P2:()=>O,bx:()=>w,pr:()=>m,sl:()=>b,vh:()=>g});var n=r(94288),i=r.n(n),o=r(5283),a=r.n(o),c=r(12810),u=r(23872),l=r(1511),s=r(33612);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,i){var o=t.width,a=t.height,f=t.layout,p=t.children,h=Object.keys(e),v={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,u.BU)(p,s.y);return h.reduce(function(o,a){var u,s,p,h,b,g=e[a],x=g.orientation,O=g.domain,w=g.padding,j=void 0===w?{}:w,A=g.mirror,S=g.reversed,P="".concat(x).concat(A?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=O[1]-O[0],k=1/0,M=g.categoricalDomain.sort();if(M.forEach(function(t,e){e>0&&(k=Math.min((t||0)-(M[e-1]||0),k))}),Number.isFinite(k)){var T=k/E,_="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(u=T*_/2),"no-gap"===g.padding){var C=(0,l.F4)(t.barCategoryGap,T*_),I=T*_/2;u=I-C-(I-C)/_*C}}}s="xAxis"===n?[r.left+(j.left||0)+(u||0),r.left+r.width-(j.right||0)-(u||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(u||0),r.top+r.height-(j.bottom||0)-(u||0)]:g.range,S&&(s=[s[1],s[0]]);var D=(0,c.W7)(g,i,m),N=D.scale,B=D.realScaleType;N.domain(O).range(s),(0,c.YB)(N);var R=(0,c.w7)(N,d(d({},g),{},{realScaleType:B}));"xAxis"===n?(b="top"===x&&!A||"bottom"===x&&A,p=r.left,h=v[P]-b*g.height):"yAxis"===n&&(b="left"===x&&!A||"right"===x&&A,p=v[P]-b*g.width,h=r.top);var L=d(d(d({},g),R),{},{realScaleType:B,x:p,y:h,scale:N,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return L.bandSize=(0,c.Hj)(L,R),g.hide||"xAxis"!==n?g.hide||(v[P]+=(b?-1:1)*L.width):v[P]+=(b?-1:1)*L.height,d(d({},o),{},y({},a,L))},{})},b=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},g=function(t){return b({x:t.x1,y:t.y1},{x:t.x2,y:t.y2})},x=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&p(r.prototype,t),e&&p(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();y(x,"EPS",1e-4);var O=function(t){var e=Object.keys(t).reduce(function(e,r){return d(d({},e),{},y({},r,x.create(t[r])))},{});return d(d({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return i()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})},w=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))}},42369:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},42857:(t,e,r)=>{var n=r(82699);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},42956:(t,e,r)=>{"use strict";r.d(e,{W:()=>l});var n=r(60222),i=r.n(n),o=r(70991),a=r(23872),c=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l=i().forwardRef(function(t,e){var r=t.children,n=t.className,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,c),s=(0,o.A)("recharts-layer",n);return i().createElement("g",u({className:s},(0,a.J9)(l,!0),{ref:e}),r)})},43371:(t,e,r)=>{"use strict";r.d(e,{Z:()=>P});var n=r(60222),i=r.n(n),o=r(8875),a=r.n(o),c=r(56097),u=r.n(c),l=r(2802),s=r.n(l),f=r(84946),p=r.n(f),h=r(31347),d=r(42956),y=r(23872),v=r(12810);function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var b=["valueAccessor"],g=["data","dataKey","clockWise","id","textBreakAll"];function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==m(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var S=function(t){return Array.isArray(t.value)?p()(t.value):t.value};function P(t){var e=t.valueAccessor,r=void 0===e?S:e,n=A(t,b),o=n.data,c=n.dataKey,u=n.clockWise,l=n.id,s=n.textBreakAll,f=A(n,g);return o&&o.length?i().createElement(d.W,{className:"recharts-label-list"},o.map(function(t,e){var n=a()(c)?r(t,e):(0,v.kr)(t&&t.payload,c),o=a()(l)?{}:{id:"".concat(l,"-").concat(e)};return i().createElement(h.J,O({},(0,y.J9)(t,!0),f,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:s,viewBox:h.J.parseViewBox(a()(u)?t:j(j({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}P.displayName="LabelList",P.renderCallByParent=function(t,e){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var a=t.children,c=(0,y.aS)(a,P).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return o?[(r=t.label,!r?null:!0===r?i().createElement(P,{key:"labelList-implicit",data:e}):i().isValidElement(r)||s()(r)?i().createElement(P,{key:"labelList-implicit",data:e,content:r}):u()(r)?i().createElement(P,O({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return x(t)}(c)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(c)||function(t,e){if(t){if("string"==typeof t)return x(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(t,e)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):c}},43891:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>c,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(60222),i=r(56097),o=r.n(i);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var c=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],u=["points","pathLength"],l={svg:["viewBox","children"],polygon:u,polyline:u},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!o()(r))return null;var i={};return Object.keys(r).forEach(function(t){s.includes(t)&&(i[t]=e||function(e){return r[t](r,e)})}),i},p=function(t,e,r){if(!o()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];s.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n}},45162:(t,e,r)=>{var n=r(38868);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},45656:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function i(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>i})},45908:(t,e,r)=>{"use strict";r.d(e,{u:()=>y});var n=r(70991),i=r(60222),o=r.n(i),a=r(2438),c=r.n(a),u=r(1511),l=r(28511),s=r(23872);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=(0,i.forwardRef)(function(t,e){var r,a=t.aspect,f=t.initialDimension,p=void 0===f?{width:-1,height:-1}:f,y=t.width,v=void 0===y?"100%":y,m=t.height,b=void 0===m?"100%":m,g=t.minWidth,x=void 0===g?0:g,O=t.minHeight,w=t.maxHeight,j=t.children,A=t.debounce,S=void 0===A?0:A,P=t.id,E=t.className,k=t.onResize,M=t.style,T=(0,i.useRef)(null),_=(0,i.useRef)();_.current=k,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(T.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),T.current},configurable:!0})});var C=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:p.width,containerHeight:p.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),I=C[0],D=C[1],N=(0,i.useCallback)(function(t,e){D(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;N(n,i),null==(e=_.current)||e.call(_,n,i)};S>0&&(t=c()(t,S,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=T.current.getBoundingClientRect();return N(r.width,r.height),e.observe(T.current),function(){e.disconnect()}},[N,S]);var B=(0,i.useMemo)(function(){var t=I.containerWidth,e=I.containerHeight;if(t<0||e<0)return null;(0,l.R)((0,u._3)(v)||(0,u._3)(b),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",v,b),(0,l.R)(!a||a>0,"The aspect(%s) must be greater than zero.",a);var r=(0,u._3)(v)?t:v,n=(0,u._3)(b)?e:b;a&&a>0&&(r?n=r/a:n&&(r=n*a),w&&n>w&&(n=w)),(0,l.R)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,v,b,x,O,a);var c=!Array.isArray(j)&&(0,s.Mn)(j.type).endsWith("Chart");return o().Children.map(j,function(t){return o().isValidElement(t)?(0,i.cloneElement)(t,h({width:r,height:n},c?{style:h({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[a,j,b,w,O,x,I,v]);return o().createElement("div",{id:P?"".concat(P):void 0,className:(0,n.A)("recharts-responsive-container",E),style:h(h({},void 0===M?{}:M),{},{width:v,height:b,minWidth:x,minHeight:O,maxHeight:w}),ref:T},B)})},46567:(t,e,r)=>{"use strict";r.d(e,{E:()=>u});var n=r(24035),i=r(33612),o=r(55041),a=r(70822),c=r(40643),u=(0,n.gu)({chartName:"BarChart",GraphicalChild:i.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:o.W},{axisType:"yAxis",AxisComp:a.h}],formatAxisMap:c.pr})},46775:(t,e,r)=>{var n=r(50527),i=r(54919),o=r(97657);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},50383:(t,e,r)=>{var n=r(25748),i=r(14634);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},50527:(t,e,r)=>{var n=r(17418);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},51086:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},51092:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(46244).A)("outline","alert-circle","IconAlertCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 8v4",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},52084:(t,e,r)=>{var n=r(54186),i=r(17234),o=r(80190);t.exports=function(t){return i(t)?o(t):n(t)}},54186:t=>{t.exports=function(t){return t.split("")}},54919:t=>{t.exports=function(t,e){return t>e}},55041:(t,e,r)=>{"use strict";r.d(e,{W:()=>m});var n=r(60222),i=r.n(n),o=r(70991),a=r(66957),c=r(85171),u=r(12810);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(s=function(){return!!t})()}function f(t){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function p(t,e){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function v(t){var e=t.xAxisId,r=(0,a.yi)(),n=(0,a.rY)(),l=(0,a.AF)(e);return null==l?null:i().createElement(c.u,y({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return(0,u.Rh)(t,!0)}}))}var m=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,s()?Reflect.construct(t,e||[],f(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&p(r,t),e=[{key:"render",value:function(){return i().createElement(v,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i().Component);h(m,"displayName","XAxis"),h(m,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},57013:(t,e,r)=>{"use strict";r.d(e,{IZ:()=>v,Kg:()=>y,Zk:()=>j,lY:()=>m,pr:()=>b,yy:()=>w});var n=r(8875),i=r.n(n),o=r(60222),a=r(2802),c=r.n(a),u=r(1511),l=r(12810);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){var n;return(n=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=Math.PI/180,v=function(t,e,r,n){return{x:t+Math.cos(-y*n)*r,y:e+Math.sin(-y*n)*r}},m=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},b=function(t,e,r,n,o){var a=t.width,c=t.height,s=t.startAngle,f=t.endAngle,y=(0,u.F4)(t.cx,a,a/2),v=(0,u.F4)(t.cy,c,c/2),b=m(a,c,r),g=(0,u.F4)(t.innerRadius,b,0),x=(0,u.F4)(t.outerRadius,b,.8*b);return Object.keys(e).reduce(function(t,r){var a,c=e[r],u=c.domain,m=c.reversed;if(i()(c.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[g,x]),m&&(a=[a[1],a[0]]);else{var b,O=function(t){if(Array.isArray(t))return t}(b=a=c.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(b,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=O[0],f=O[1]}var w=(0,l.W7)(c,o),j=w.realScaleType,A=w.scale;A.domain(u).range(a),(0,l.YB)(A);var S=(0,l.w7)(A,p(p({},c),{},{realScaleType:j})),P=p(p(p({},c),S),{},{range:a,radius:x,realScaleType:j,scale:A,cx:y,cy:v,innerRadius:g,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},h({},r,P))},{})},g=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},x=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=g({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},O=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},w=function(t,e){var r,n=x({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,c=e.outerRadius;if(i<a||i>c)return!1;if(0===i)return!0;var u=O(e),l=u.startAngle,s=u.endAngle,f=o;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?p(p({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},j=function(t){return(0,o.isValidElement)(t)||c()(t)||"boolean"==typeof t?"":t.className}},57365:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},58909:(t,e,r)=>{"use strict";r.d(e,{g:()=>l});var n=r(91110),i=r(12810),o=r(23872);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,c=t.legendWidth,l=t.legendContent,s=(0,o.BU)(r,n.s);if(!s)return null;var f=n.s.defaultProps,p=void 0!==f?u(u({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u(u({},r),e.props):{},o=n.dataKey,a=n.name,c=n.legendType;return{inactive:n.hide,dataKey:o,type:p.iconType||c||"square",color:(0,i.Ps)(e),value:a||o,payload:n}}),u(u(u({},p),n.s.getWithHeight(s,c)),{},{payload:e,item:s})}},60794:(t,e,r)=>{var n=r(4596),i=r(97657),o=r(97208),a=r(5517),c=r(7348);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},60954:(t,e,r)=>{"use strict";r.d(e,{J:()=>d,M:()=>v});var n=r(60222),i=r.n(n),o=r(70991),a=r(86704),c=r(23872);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var h=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),o+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),o+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),o+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},d=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},y={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},v=function(t){var e,r=p(p({},y),t),u=(0,n.useRef)(),f=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return s(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),d=f[0],v=f[1];(0,n.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var t=u.current.getTotalLength();t&&v(t)}catch(t){}},[]);var m=r.x,b=r.y,g=r.width,x=r.height,O=r.radius,w=r.className,j=r.animationEasing,A=r.animationDuration,S=r.animationBegin,P=r.isAnimationActive,E=r.isUpdateAnimationActive;if(m!==+m||b!==+b||g!==+g||x!==+x||0===g||0===x)return null;var k=(0,o.A)("recharts-rectangle",w);return E?i().createElement(a.Ay,{canBegin:d>0,from:{width:g,height:x,x:m,y:b},to:{width:g,height:x,x:m,y:b},duration:A,animationEasing:j,isActive:E},function(t){var e=t.width,n=t.height,o=t.x,s=t.y;return i().createElement(a.Ay,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:A,isActive:P,easing:j},i().createElement("path",l({},(0,c.J9)(r,!0),{className:k,d:h(o,s,e,n,O),ref:u})))}):i().createElement("path",l({},(0,c.J9)(r,!0),{className:k,d:h(m,b,g,x,O)}))}},61498:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});function n(t,e){if(!t)throw Error("Invariant failed")}},63305:(t,e,r)=>{var n=r(84595),i=r(42857),o=r(57365),a=r(65275),c=r(19009),u=r(79263);t.exports=function(t,e,r){var l=-1,s=i,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=o;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},63836:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case i:return e}}}(t)===o}},65799:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},66957:(t,e,r)=>{"use strict";r.d(e,{DR:()=>x,pj:()=>j,rY:()=>M,yi:()=>k,Yp:()=>O,hj:()=>E,sk:()=>P,AF:()=>w,Nk:()=>S,$G:()=>A});var n=r(60222),i=r.n(n),o=r(61498),a=r(95217),c=r.n(a),u=r(5283),l=r.n(u),s=r(82756),f=r.n(s)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),p=r(1511),h=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)(void 0),v=(0,n.createContext)({}),m=(0,n.createContext)(void 0),b=(0,n.createContext)(0),g=(0,n.createContext)(0),x=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,s=f(o);return i().createElement(h.Provider,{value:r},i().createElement(d.Provider,{value:n},i().createElement(v.Provider,{value:o},i().createElement(y.Provider,{value:s},i().createElement(m.Provider,{value:a},i().createElement(b.Provider,{value:l},i().createElement(g.Provider,{value:u},c)))))))},O=function(){return(0,n.useContext)(m)},w=function(t){var e=(0,n.useContext)(h);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},j=function(){var t=(0,n.useContext)(h);return(0,p.lX)(t)},A=function(){var t=(0,n.useContext)(d);return c()(t,function(t){return l()(t.domain,Number.isFinite)})||(0,p.lX)(t)},S=function(t){var e=(0,n.useContext)(d);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},P=function(){return(0,n.useContext)(y)},E=function(){return(0,n.useContext)(v)},k=function(){return(0,n.useContext)(g)},M=function(){return(0,n.useContext)(b)}},68954:(t,e,r)=>{var n=r(65936),i=r(97657),o=r(93068),a=r(5517);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},69584:(t,e,r)=>{var n=r(60753);t.exports=function(){return n.Date.now()}},70486:t=>{t.exports=function(){}},70822:(t,e,r)=>{"use strict";r.d(e,{h:()=>m});var n=r(60222),i=r.n(n),o=r(70991),a=r(66957),c=r(85171),u=r(12810);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(s=function(){return!!t})()}function f(t){return(f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function p(t,e){return(p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}function y(){return(y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var v=function(t){var e=t.yAxisId,r=(0,a.yi)(),n=(0,a.rY)(),l=(0,a.Nk)(e);return null==l?null:i().createElement(c.u,y({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return(0,u.Rh)(t,!0)}}))},m=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,s()?Reflect.construct(t,e||[],f(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&p(r,t),e=[{key:"render",value:function(){return i().createElement(v,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(i().Component);h(m,"displayName","YAxis"),h(m,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},71491:(t,e,r)=>{var n=r(89676),i=r(68954);t.exports=function(t,e){return n(i(t,e),1)}},71858:(t,e,r)=>{"use strict";r.d(e,{i:()=>u});let n=Math.PI,i=2*n,o=i-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class c{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,i,o){if(t*=1,e*=1,r*=1,i*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let a=this._x1,c=this._y1,u=r-t,l=i-e,s=a-t,f=c-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6)if(Math.abs(f*u-l*s)>1e-6&&o){let h=r-a,d=i-c,y=u*u+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=o*Math.tan((n-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*s},${e+g*f}`,this._append`A${o},${o},0,0,${+(f*h>s*d)},${this._x1=t+x*u},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,a,c,u){if(t*=1,e*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,p=e+s,h=1^u,d=u?a-c:c-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(d<0&&(d=d%i+i),d>o?this._append`A${r},${r},0,1,${h},${t-l},${e-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${h},${this._x1=t+r*Math.cos(c)},${this._y1=e+r*Math.sin(c)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new c(e)}c.prototype},72698:(t,e,r)=>{"use strict";r.d(e,{d:()=>T});var n=r(60222),i=r.n(n),o=r(2802),a=r.n(o),c=r(28511),u=r(1511),l=r(23872),s=r(12810),f=r(83333),p=r(85171),h=r(66957),d=["x1","y1","x2","y2","key"],y=["offset"];function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function b(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?m(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function g(){return(g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function x(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var O=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,a=t.width,c=t.height,u=t.ry;return i().createElement("rect",{x:n,y:o,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function w(t,e){var r;if(i().isValidElement(t))r=i().cloneElement(t,e);else if(a()(t))r=t(e);else{var n=e.x1,o=e.y1,c=e.x2,u=e.y2,s=e.key,f=x(e,d),p=(0,l.J9)(f,!1),h=(p.offset,x(p,y));r=i().createElement("line",g({},h,{x1:n,y1:o,x2:c,y2:u,fill:"none",key:s}))}return r}function j(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,a=t.horizontalPoints;if(!o||!a||!a.length)return null;var c=a.map(function(n,i){return w(o,b(b({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return i().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function A(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,a=t.verticalPoints;if(!o||!a||!a.length)return null;var c=a.map(function(n,i){return w(o,b(b({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return i().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function S(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:o+c-t;if(l<=0)return null;var f=u%e.length;return i().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return i().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,a=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:o+c-t;if(l<=0)return null;var f=e%r.length;return i().createElement("rect",{key:"react-".concat(e),x:t,y:a,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return i().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var E=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return(0,s.PW)((0,f.f)(b(b(b({},p.u.defaultProps),r),{},{ticks:(0,s.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},k=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return(0,s.PW)((0,f.f)(b(b(b({},p.u.defaultProps),r),{},{ticks:(0,s.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},M={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function T(t){var e,r,n,o,l,s,f=(0,h.yi)(),p=(0,h.rY)(),d=(0,h.hj)(),y=b(b({},t),{},{stroke:null!=(e=t.stroke)?e:M.stroke,fill:null!=(r=t.fill)?r:M.fill,horizontal:null!=(n=t.horizontal)?n:M.horizontal,horizontalFill:null!=(o=t.horizontalFill)?o:M.horizontalFill,vertical:null!=(l=t.vertical)?l:M.vertical,verticalFill:null!=(s=t.verticalFill)?s:M.verticalFill,x:(0,u.Et)(t.x)?t.x:d.left,y:(0,u.Et)(t.y)?t.y:d.top,width:(0,u.Et)(t.width)?t.width:d.width,height:(0,u.Et)(t.height)?t.height:d.height}),m=y.x,x=y.y,w=y.width,T=y.height,_=y.syncWithTicks,C=y.horizontalValues,I=y.verticalValues,D=(0,h.pj)(),N=(0,h.$G)();if(!(0,u.Et)(w)||w<=0||!(0,u.Et)(T)||T<=0||!(0,u.Et)(m)||m!==+m||!(0,u.Et)(x)||x!==+x)return null;var B=y.verticalCoordinatesGenerator||E,R=y.horizontalCoordinatesGenerator||k,L=y.horizontalPoints,z=y.verticalPoints;if((!L||!L.length)&&a()(R)){var U=C&&C.length,F=R({yAxis:N?b(b({},N),{},{ticks:U?C:N.ticks}):void 0,width:f,height:p,offset:d},!!U||_);(0,c.R)(Array.isArray(F),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(v(F),"]")),Array.isArray(F)&&(L=F)}if((!z||!z.length)&&a()(B)){var W=I&&I.length,$=B({xAxis:D?b(b({},D),{},{ticks:W?I:D.ticks}):void 0,width:f,height:p,offset:d},!!W||_);(0,c.R)(Array.isArray($),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(v($),"]")),Array.isArray($)&&(z=$)}return i().createElement("g",{className:"recharts-cartesian-grid"},i().createElement(O,{fill:y.fill,fillOpacity:y.fillOpacity,x:y.x,y:y.y,width:y.width,height:y.height,ry:y.ry}),i().createElement(j,g({},y,{offset:d,horizontalPoints:L,xAxis:D,yAxis:N})),i().createElement(A,g({},y,{offset:d,verticalPoints:z,xAxis:D,yAxis:N})),i().createElement(S,g({},y,{horizontalPoints:L})),i().createElement(P,g({},y,{verticalPoints:z})))}T.displayName="CartesianGrid"},73149:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(10427);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var n,o,a;n=t,o=e,a=r[e],(o=function(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var u={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",f=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:i});if(u.widthCache[o])return u.widthCache[o];try{var c=document.getElementById(s);c||((c=document.createElement("span")).setAttribute("id",s),c.setAttribute("aria-hidden","true"),document.body.appendChild(c));var f=a(a({},l),i);Object.assign(c.style,f),c.textContent="".concat(t);var p=c.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[o]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},75974:(t,e,r)=>{"use strict";r.d(e,{u:()=>l});var n=r(60222),i=r.n(n),o=r(70991),a=r(23872),c=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t){var e=t.children,r=t.width,n=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,c),y=l||{width:r,height:n,x:0,y:0},v=(0,o.A)("recharts-surface",s);return i().createElement("svg",u({},(0,a.J9)(d,!0,"svg"),{className:v,width:r,height:n,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),i().createElement("title",null,p),i().createElement("desc",null,h),e)}},77719:(t,e,r)=>{var n=r(25748),i=r(5517),o=r(14634);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},78068:(t,e,r)=>{"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{A:()=>n}),Array.prototype.slice},80190:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[i,o,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},82699:(t,e,r)=>{var n=r(87291),i=r(19919),o=r(65799);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},83201:(t,e,r)=>{var n=r(50383);t.exports=function(t){return n(t)&&t!=+t}},83333:(t,e,r)=>{"use strict";r.d(e,{f:()=>d});var n=r(2802),i=r.n(n),o=r(1511),a=r(73149),c=r(10427),u=r(40643);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function s(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e,r){var n,f,p,d,y,v=t.tick,m=t.ticks,b=t.viewBox,g=t.minTickGap,x=t.orientation,O=t.interval,w=t.tickFormatter,j=t.unit,A=t.angle;if(!m||!m.length||!v)return[];if((0,o.Et)(O)||c.m.isSsr)return l(m,("number"==typeof O&&(0,o.Et)(O)?O:0)+1);var S=[],P="top"===x||"bottom"===x?"width":"height",E=j&&"width"===P?(0,a.Pu)(j,{fontSize:e,letterSpacing:r}):{width:0,height:0},k=function(t,n){var o,c,l=i()(w)?w(t.value,n):t.value;return"width"===P?(o=(0,a.Pu)(l,{fontSize:e,letterSpacing:r}),c={width:o.width+E.width,height:o.height+E.height},(0,u.bx)(c,A)):(0,a.Pu)(l,{fontSize:e,letterSpacing:r})[P]},M=m.length>=2?(0,o.sA)(m[1].coordinate-m[0].coordinate):1,T=(n="width"===P,f=b.x,p=b.y,d=b.width,y=b.height,1===M?{start:n?f:p,end:n?f+d:p+y}:{start:n?f+d:p+y,end:n?f:p});return"equidistantPreserveStart"===O?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,f=0,p=1,h=c;p<=a.length;)if(o=function(){var e,o=null==n?void 0:n[f];if(void 0===o)return{v:l(n,p)};var a=f,d=function(){return void 0===e&&(e=r(o,a)),e},y=o.coordinate,v=0===f||s(t,y,d,h,u);v||(f=0,h=c,p+=1),v&&(h=y+t*(d()/2+i),f+=p)}())return o.v;return[]}(M,T,k,m,g):("preserveStart"===O||"preserveStartEnd"===O?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(o){var f=n[c-1],p=r(f,c-1),d=t*(f.coordinate+t*p/2-l);a[c-1]=f=h(h({},f),{},{tickCoord:d>0?f.coordinate-d*t:f.coordinate}),s(t,f.tickCoord,function(){return p},u,l)&&(l=f.tickCoord-t*(p/2+i),a[c-1]=h(h({},f),{},{isShow:!0}))}for(var y=o?c-1:c,v=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var f=t*(o.coordinate-t*c()/2-u);a[e]=o=h(h({},o),{},{tickCoord:f<0?o.coordinate-f*t:o.coordinate})}else a[e]=o=h(h({},o),{},{tickCoord:o.coordinate});s(t,o.tickCoord,c,u,l)&&(u=o.tickCoord+t*(c()/2+i),a[e]=h(h({},o),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(M,T,k,m,g,"preserveStartEnd"===O):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,l=function(e){var n,l=o[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-u);o[e]=l=h(h({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else o[e]=l=h(h({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,c,u)&&(u=l.tickCoord-t*(f()/2+i),o[e]=h(h({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return o}(M,T,k,m,g)).filter(function(t){return t.isShow})}},85171:(t,e,r)=>{"use strict";r.d(e,{u:()=>C});var n=r(60222),i=r.n(n),o=r(2802),a=r.n(o),c=r(88336),u=r.n(c),l=r(70991),s=r(4854),f=r(42956),p=r(87158),h=r(31347),d=r(1511),y=r(43891),v=r(23872),m=r(83333),b=["viewBox"],g=["viewBox"],x=["ticks"];function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function w(){return(w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){T(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function T(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var C=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=k(r),(e=function(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,E()?Reflect.construct(r,i||[],k(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&M(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=S(t,b),i=this.props,o=i.viewBox,a=S(i,g);return!(0,s.b)(r,o)||!(0,s.b)(n,a)||!(0,s.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,y=c.mirror,v=c.tickMargin,m=y?-1:1,b=t.tickSize||h,g=(0,d.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=l+!y*f)-m*b)-m*v,o=g;break;case"left":n=i=t.coordinate,o=(e=(r=u+!y*s)-m*b)-m*v,a=g;break;case"right":n=i=t.coordinate,o=(e=(r=u+y*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(i=l+y*f)+m*b)+m*v,o=g}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,a=t.orientation,c=t.mirror,s=t.axisLine,f=A(A(A({},(0,v.J9)(this.props,!1)),(0,v.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!c||"bottom"===a&&c);f=A(A({},f),{},{x1:e,y1:r+p*o,x2:e+n,y2:r+p*o})}else{var h=+("left"===a&&!c||"right"===a&&c);f=A(A({},f),{},{x1:e+h*n,y1:r,x2:e+h*n,y2:r+o})}return i().createElement("line",w({},f,{className:(0,l.A)("recharts-cartesian-axis-line",u()(s,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,c=this.props,s=c.tickLine,p=c.stroke,h=c.tick,d=c.tickFormatter,b=c.unit,g=(0,m.f)(A(A({},this.props),{},{ticks:t}),e,r),x=this.getTickTextAnchor(),O=this.getTickVerticalAnchor(),j=(0,v.J9)(this.props,!1),S=(0,v.J9)(h,!1),P=A(A({},j),{},{fill:"none"},(0,v.J9)(s,!1)),E=g.map(function(t,e){var r=o.getTickLineCoord(t),c=r.line,v=r.tick,m=A(A(A(A({textAnchor:x,verticalAnchor:O},j),{},{stroke:"none",fill:p},S),v),{},{index:e,payload:t,visibleTicksCount:g.length,tickFormatter:d});return i().createElement(f.W,w({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,y.XC)(o.props,t,e)),s&&i().createElement("line",w({},P,c,{className:(0,l.A)("recharts-cartesian-axis-tick-line",u()(s,"className"))})),h&&n.renderTickItem(h,m,"".concat(a()(d)?d(t.value,e):t.value).concat(b||"")))});return i().createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,c=e.ticksGenerator,u=e.className;if(e.hide)return null;var s=this.props,p=s.ticks,d=S(s,x),y=p;return(a()(c)&&(y=c(p&&p.length>0?this.props:d)),n<=0||o<=0||!y||!y.length)?null:i().createElement(f.W,{className:(0,l.A)("recharts-cartesian-axis",u),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),h.J.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return i().isValidElement(t)?i().cloneElement(t,e):a()(t)?t(e):i().createElement(p.E,w({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&P(n.prototype,e),r&&P(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.Component);T(C,"displayName","CartesianAxis"),T(C,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},85487:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});var n=(0,r(19e3).A)("outline","trending-down","IconTrendingDown",[["path",{d:"M3 7l6 6l4 -4l8 8",key:"svg-0"}],["path",{d:"M21 10l0 7l-7 0",key:"svg-1"}]])},86704:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>tS});var n=r(60222),i=r.n(n),o=r(71876),a=r.n(o),c=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty;function s(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function f(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function p(t){return c(t).concat(u(t))}var h=Object.hasOwn||function(t,e){return l.call(t,e)};function d(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var y=Object.getOwnPropertyDescriptor,v=Object.keys;function m(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function b(t,e){return d(t.getTime(),e.getTime())}function g(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function x(t,e){return t===e}function O(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function w(t,e,r){var n=v(t),i=n.length;if(v(e).length!==i)return!1;for(;i-- >0;)if(!M(t,e,r,n[i]))return!1;return!0}function j(t,e,r){var n,i,o,a=p(t),c=a.length;if(p(e).length!==c)return!1;for(;c-- >0;)if(!M(t,e,r,n=a[c])||(i=y(t,n),o=y(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function A(t,e){return d(t.valueOf(),e.valueOf())}function S(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function E(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function k(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function M(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||h(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var T=Array.isArray,_="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,C=Object.assign,I=Object.prototype.toString.call.bind(Object.prototype.toString),D=N();function N(t){void 0===t&&(t={});var e,r,n,i,o,a,c,u,l,p,h,y,v,M=t.circular,D=t.createInternalComparator,N=t.createState,B=t.strict,R=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?j:m,areDatesEqual:b,areErrorsEqual:g,areFunctionsEqual:x,areMapsEqual:n?s(O,j):O,areNumbersEqual:d,areObjectsEqual:n?j:w,arePrimitiveWrappersEqual:A,areRegExpsEqual:S,areSetsEqual:n?s(P,j):P,areTypedArraysEqual:n?j:E,areUrlsEqual:k};if(r&&(i=C({},i,r(i))),e){var o=f(i.areArraysEqual),a=f(i.areMapsEqual),c=f(i.areObjectsEqual),u=f(i.areSetsEqual);i=C({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,h=e.areSetsEqual,y=e.areTypedArraysEqual,v=e.areUrlsEqual,function(t,e,s){if(t===e)return!0;if(null==t||null==e)return!1;var f=typeof t;if(f!==typeof e)return!1;if("object"!==f)return"number"===f?c(t,e,s):"function"===f&&o(t,e,s);var d=t.constructor;if(d!==e.constructor)return!1;if(d===Object)return u(t,e,s);if(T(t))return r(t,e,s);if(null!=_&&_(t))return y(t,e,s);if(d===Date)return n(t,e,s);if(d===RegExp)return p(t,e,s);if(d===Map)return a(t,e,s);if(d===Set)return h(t,e,s);var m=I(t);return"[object Date]"===m?n(t,e,s):"[object RegExp]"===m?p(t,e,s):"[object Map]"===m?a(t,e,s):"[object Set]"===m?h(t,e,s):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,s):"[object URL]"===m?v(t,e,s):"[object Error]"===m?i(t,e,s):"[object Arguments]"===m?u(t,e,s):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,s)}),L=D?D(R):function(t,e,r,n,i,o,a){return R(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==M&&M,comparator:R,createState:N,equals:L,strict:void 0!==B&&B})}function B(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function z(t){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function W(t,e,r){var n;return(n=function(t,e){if("object"!==z(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==z(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===z(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}N({strict:!0}),N({circular:!0}),N({circular:!0,strict:!0}),N({createInternalComparator:function(){return d}}),N({strict:!0,createInternalComparator:function(){return d}}),N({circular:!0,createInternalComparator:function(){return d}}),N({circular:!0,createInternalComparator:function(){return d},strict:!0});var $=function(t){return t},q=function(t,e){return Object.keys(e).reduce(function(r,n){return F(F({},r),{},W({},n,t(n,e[n])))},{})},X=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},H=function(t,e,r,n,i,o,a,c){};function V(t,e){if(t){if("string"==typeof t)return K(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return K(t,e)}}function K(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var G=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Y=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},J=function(t,e){return function(r){return Y(G(t,e),r)}},Z=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,u=1;break;case"ease":o=.25,a=.1,c=.25,u=1;break;case"ease-in":o=.42,a=0,c=1,u=1;break;case"ease-out":o=.42,a=0,c=.58,u=1;break;case"ease-in-out":o=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(s,4)||V(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],u=f[3]}else H(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}H([o,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=J(o,c),h=J(a,u),d=(t=o,e=c,function(r){var n;return Y([].concat(function(t){if(Array.isArray(t))return K(t)}(n=G(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||V(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=d(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return h(r)};return y.isStepper=!1,y},Q=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},tt=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Z(n);case"spring":return Q();default:if("cubic-bezier"===n.split("(")[0])return Z(n);H(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(H(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function te(t){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tr(t){return function(t){if(Array.isArray(t))return tc(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ta(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ti(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(r),!0).forEach(function(e){to(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(t,e,r){var n;return(n=function(t,e){if("object"!==te(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==te(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===te(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ta(t,e){if(t){if("string"==typeof t)return tc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tc(t,e)}}function tc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tu=function(t,e,r){return t+(e-t)*r},tl=function(t){return t.from!==t.to},ts=function t(e,r,n){var i=q(function(t,r){if(tl(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(n,2)||ta(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return ti(ti({},r),{},{from:o,velocity:a})}return r},r);return n<1?q(function(t,e){return tl(e)?ti(ti({},e),{},{velocity:tu(e.velocity,i[t].velocity,n),from:tu(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let tf=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return ti(ti({},r),{},to({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return ti(ti({},r),{},to({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;l=ts(r,l,a),i(ti(ti(ti({},t),e),q(function(t,e){return e.from},l))),o=n,Object.values(l).filter(tl).length&&(s=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,l=q(function(t,e){return tu.apply(void 0,tr(e).concat([r(c)]))},u);if(i(ti(ti(ti({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=q(function(t,e){return tu.apply(void 0,tr(e).concat([r(1)]))},u);i(ti(ti(ti({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tp(t){return(tp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var th=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function td(t){return function(t){if(Array.isArray(t))return ty(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ty(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ty(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ty(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tv(Object(r),!0).forEach(function(e){tb(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tb(t,e,r){return(e=tg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tg(t){var e=function(t,e){if("object"!==tp(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tp(e)?e:String(e)}function tx(t,e){return(tx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tO(t,e){if(e&&("object"===tp(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tw(t)}function tw(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tj(t){return(tj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tA=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");a.prototype=Object.create(t&&t.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),t&&tx(a,t);var e,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tj(a);return t=e?Reflect.construct(r,arguments,tj(this).constructor):r.apply(this,arguments),tO(this,t)});function a(t,e){if(!(this instanceof a))throw TypeError("Cannot call a class as a function");var r=o.call(this,t,e),n=r.props,i=n.isActive,c=n.attributeName,u=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tw(r)),r.changeStyle=r.changeStyle.bind(tw(r)),!i||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tO(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},tO(r);r.state={style:c?tb({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:i?tb({},i,a):a};this.state&&u&&(i&&u[i]!==a||!i&&u!==a)&&this.setState(l);return}if(!D(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||o?c:t.to;if(this.state&&u){var p={style:i?tb({},i,f):f};(i&&u[i]!==f||!i&&u!==f)&&this.setState(p)}this.runAnimation(tm(tm({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=tf(r,n,tt(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(td(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=i>0?r[i-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(td(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var h=X(p,o,c),d=tm(tm(tm({},f.style),u),{},{transition:h});return[].concat(td(t),[d,o,s]).filter($)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return L(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return L(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void B(t.bind(null,a),o):(t(o),void B(t.bind(null,a)))}"object"===R(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?tb({},c,u):u,v=X(Object.keys(y),a,l);d.start([s,o,tm(tm({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),a=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,th)),c=n.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!o||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,i=e.className;return(0,n.cloneElement)(t,tm(tm({},a),{},{style:tm(tm({},void 0===r?{}:r),u),className:i}))};return 1===c?l(n.Children.only(e)):i().createElement("div",null,n.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tg(n.key),n)}}(a.prototype,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);tA.displayName="Animate",tA.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tA.propTypes={from:a().oneOfType([a().object,a().string]),to:a().oneOfType([a().object,a().string]),attributeName:a().string,duration:a().number,begin:a().number,easing:a().oneOfType([a().string,a().func]),steps:a().arrayOf(a().shape({duration:a().number.isRequired,style:a().object.isRequired,easing:a().oneOfType([a().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),a().func]),properties:a().arrayOf("string"),onAnimationEnd:a().func})),children:a().oneOfType([a().node,a().func]),isActive:a().bool,canBegin:a().bool,onAnimationEnd:a().func,shouldReAnimate:a().bool,onAnimationStart:a().func,onAnimationReStart:a().func};let tS=tA},87158:(t,e,r)=>{"use strict";r.d(e,{E:()=>L});var n=r(60222),i=r.n(n),o=r(8875),a=r.n(o),c=r(70991),u=r(1511),l=r(10427),s=r(23872),f=r(73149);function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:e+""}(n.key),n)}}var v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,x={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},O=Object.keys(x),w=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||b.test(e)||(this.num=NaN,this.unit=""),O.includes(e)&&(this.num=t*x[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=h(null!=(e=g.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&y(r.prototype,t),e&&y(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function j(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=h(null!=(r=v.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],c=w.parse(null!=i?i:""),u=w.parse(null!=a?a:""),l="*"===o?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(v,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=h(null!=(s=m.exec(e))?s:[],4),p=f[1],d=f[2],y=f[3],b=w.parse(null!=p?p:""),g=w.parse(null!=y?y:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(m,x.toString())}return e}var A=/\(([^()]*)\)/;function S(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=h(A.exec(e),2)[1];e=e.replace(A,j(r))}return e}(e),e=j(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var P=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],E=["dx","dy","angle","className","breakAll"];function k(){return(k=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function M(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function T(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var C=/[ \f\n\r\t\v\u2028\u2029]+/,I=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];a()(e)||(i=r?e.toString().split(""):e.toString().split(C));var o=i.map(function(t){return{word:t,width:(0,f.Pu)(t,n).width}}),c=r?0:(0,f.Pu)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:c}}catch(t){return null}},D=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,l=t.style,s=t.breakAll,f=(0,u.Et)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(I({breakAll:s,style:l,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var b=Math.floor((y+v)/2),g=T(d(b-1),2),x=g[0],O=g[1],w=T(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){o=O;break}m++}return o||h},N=function(t){return[{words:a()(t)?[]:t.toString().split(C)}]},B=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!l.m.isSsr){var c=I({breakAll:o,children:n,style:i});if(!c)return N(n);var u=c.wordsWithComputedWidth,s=c.spaceWidth;return D({breakAll:o,children:n,maxLines:a,style:i},u,s,e,r)}return N(n)},R="#808080",L=function(t){var e,r=t.x,o=void 0===r?0:r,a=t.y,l=void 0===a?0:a,f=t.lineHeight,p=void 0===f?"1em":f,h=t.capHeight,d=void 0===h?"0.71em":h,y=t.scaleToFit,v=void 0!==y&&y,m=t.textAnchor,b=t.verticalAnchor,g=t.fill,x=void 0===g?R:g,O=M(t,P),w=(0,n.useMemo)(function(){return B({breakAll:O.breakAll,children:O.children,maxLines:O.maxLines,scaleToFit:v,style:O.style,width:O.width})},[O.breakAll,O.children,O.maxLines,v,O.style,O.width]),j=O.dx,A=O.dy,T=O.angle,_=O.className,C=O.breakAll,I=M(O,E);if(!(0,u.vh)(o)||!(0,u.vh)(l))return null;var D=o+((0,u.Et)(j)?j:0),N=l+((0,u.Et)(A)?A:0);switch(void 0===b?"end":b){case"start":e=S("calc(".concat(d,")"));break;case"middle":e=S("calc(".concat((w.length-1)/2," * -").concat(p," + (").concat(d," / 2))"));break;default:e=S("calc(".concat(w.length-1," * -").concat(p,")"))}var L=[];if(v){var z=w[0].width,U=O.width;L.push("scale(".concat(((0,u.Et)(U)?U/z:1)/z,")"))}return T&&L.push("rotate(".concat(T,", ").concat(D,", ").concat(N,")")),L.length&&(I.transform=L.join(" ")),i().createElement("text",k({},(0,s.J9)(I,!0),{x:D,y:N,className:(0,c.A)("recharts-text",_),textAnchor:void 0===m?"start":m,fill:x.includes("url")?R:x}),w.map(function(t,r){var n=t.words.join(C?"":" ");return i().createElement("tspan",{x:D,dy:0===r?e:p,key:"".concat(n,"-").concat(r)},n)}))}},89069:(t,e,r)=>{var n=r(67161);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},89107:(t,e,r)=>{var n=r(45162),i=r(17234),o=r(52084),a=r(45342);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},89389:(t,e,r)=>{"use strict";r.d(e,{f:()=>n});var n=function(t){return null};n.displayName="Cell"},91110:(t,e,r)=>{"use strict";r.d(e,{s:()=>D});var n=r(60222),i=r.n(n),o=r(2802),a=r.n(o),c=r(70991),u=r(28511),l=r(75974),s=r(27547),f=r(43891);function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(y=function(){return!!t})()}function v(t){return(v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function m(t,e){return(m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function b(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:e+""}var x=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=v(t),function(t,e){if(e&&("object"===p(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,y()?Reflect.construct(t,e||[],v(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&m(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return i().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return i().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return i().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(i().isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,i().cloneElement(t.legendIcon,a)}return i().createElement(s.i,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,s=e.formatter,p=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===o?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||s,m=(0,c.A)(b(b({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=a()(e.value)?null:e.value;(0,u.R)(!a()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?p:e.color;return i().createElement("li",h({className:m,style:y,key:"legend-item-".concat(r)},(0,f.XC)(t.props,e,r)),i().createElement(l.u,{width:n,height:n,viewBox:d,style:v},t.renderIcon(e)),i().createElement("span",{className:"recharts-legend-item-text",style:{color:x}},o?o(g,e,r):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?i().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,g(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);b(x,"displayName","Legend"),b(x,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var O=r(1511),w=r(38506);function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var A=["ref"];function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function P(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function E(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function k(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(k=function(){return!!t})()}function M(t){return(M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function T(t,e){return(T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function _(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}function I(t){return t.value}var D=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=M(e),_(t=function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,k()?Reflect.construct(e,r||[],M(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&T(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?P({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),P(P({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=P(P({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(a)),a);return i().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(i().isValidElement(t))return i().cloneElement(t,e);if("function"==typeof t)return i().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,A);return i().createElement(x,r)}(r,P(P({},this.props),{},{payload:(0,w.s)(u,c,I)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=P(P({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,O.Et)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&E(n.prototype,e),r&&E(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);_(D,"displayName","Legend"),_(D,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},91352:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},91554:(t,e,r)=>{"use strict";r.d(e,{yp:()=>I,GG:()=>U,NE:()=>D,nZ:()=>N,xQ:()=>B});var n=r(60222),i=r.n(n),o=r(2802),a=r.n(o),c=r(13747),u=r.n(c),l=r(19096),s=r.n(l),f=r(29736),p=r.n(f),h=r(60954),d=r(70991),y=r(86704),v=r(23872);function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(){return(b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==m(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var w=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},j={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},A=function(t){var e,r=O(O({},j),t),o=(0,n.useRef)(),a=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return g(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=a[0],u=a[1];(0,n.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,m=r.className,x=r.animationEasing,A=r.animationDuration,S=r.animationBegin,P=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var E=(0,d.A)("recharts-trapezoid",m);return P?i().createElement(y.Ay,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:A,animationEasing:x,isActive:P},function(t){var e=t.upperWidth,n=t.lowerWidth,a=t.height,u=t.x,l=t.y;return i().createElement(y.Ay,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:A,easing:x},i().createElement("path",b({},(0,v.J9)(r,!0),{className:E,d:w(u,l,e,n,a),ref:o})))}):i().createElement("g",null,i().createElement("path",b({},(0,v.J9)(r,!0),{className:E,d:w(l,s,f,p,h)})))},S=r(97173),P=r(42956),E=r(27547),k=["option","shapeType","propTransformer","activeClassName","isActive"];function M(t){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return i().createElement(h.M,r);case"trapezoid":return i().createElement(A,r);case"sector":return i().createElement(S.h,r);case"symbols":if("symbols"===e)return i().createElement(E.i,r);break;default:return null}}function I(t){var e,r=t.option,o=t.shapeType,c=t.propTransformer,l=t.activeClassName,f=t.isActive,p=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,k);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,_(_({},p),(0,n.isValidElement)(r)?r.props:r));else if(a()(r))e=r(p);else if(u()(r)&&!s()(r)){var h=(void 0===c?function(t,e){return _(_({},e),t)}:c)(r,p);e=i().createElement(C,{shapeType:o,elementProps:h})}else e=i().createElement(C,{shapeType:o,elementProps:p});return f?i().createElement(P.W,{className:void 0===l?"recharts-active-shape":l},e):e}function D(t,e){return null!=e&&"trapezoids"in t.props}function N(t,e){return null!=e&&"sectors"in t.props}function B(t,e){return null!=e&&"points"in t.props}function R(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function L(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function z(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}function U(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(D(o,i)?e="trapezoids":N(o,i)?e="sectors":B(o,i)&&(e="points"),e),u=D(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:N(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:B(o,i)?i.payload:{},l=a.filter(function(t,e){var r=p()(u,t),n=o.props[c].filter(function(t){var e;return(D(o,i)?e=R:N(o,i)?e=L:B(o,i)&&(e=z),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},94580:t=>{t.exports=function(t,e){return t<e}},94653:(t,e,r)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"notFound",{enumerable:!0,get:function(){return i}});let n=""+r(12848).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function i(){let t=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw t.digest=n,t}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},95217:(t,e,r)=>{t.exports=r(14870)(r(86389))},95280:(t,e,r)=>{var n=r(50527),i=r(94580),o=r(80212);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},97173:(t,e,r)=>{"use strict";r.d(e,{h:()=>m});var n=r(60222),i=r.n(n),o=r(70991),a=r(23872),c=r(57013),u=r(1511);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var h=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,u=t.cornerRadius,l=t.cornerIsExternal,s=u*(a?1:-1)+n,f=Math.asin(u/s)/c.Kg,p=l?i:i+o*f;return{center:(0,c.IZ)(e,r,s,p),circleTangency:(0,c.IZ)(e,r,n,p),lineTangency:(0,c.IZ)(e,r,s*Math.cos(f*c.Kg),l?i-o*f:i),theta:f}},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=t.endAngle,l=(0,u.sA)(a-o)*Math.min(Math.abs(a-o),359.999),s=o+l,f=(0,c.IZ)(e,r,i,o),p=(0,c.IZ)(e,r,i,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(o>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(n>0){var d=(0,c.IZ)(e,r,n,o),y=(0,c.IZ)(e,r,n,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(o<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(e,",").concat(r," Z");return h},y=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,u.sA)(s-l),p=h({cx:e,cy:r,radius:i,angle:l,sign:f,cornerRadius:o,cornerIsExternal:c}),y=p.circleTangency,v=p.lineTangency,m=p.theta,b=h({cx:e,cy:r,radius:i,angle:s,sign:-f,cornerRadius:o,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=c?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):d({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var A=h({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),S=A.circleTangency,P=A.lineTangency,E=A.theta,k=h({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),M=k.circleTangency,T=k.lineTangency,_=k.theta,C=c?Math.abs(l-s):Math.abs(l-s)-E-_;if(C<0&&0===o)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(T.x,",").concat(T.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(S.x,",").concat(S.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j},v={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},m=function(t){var e,r=p(p({},v),t),n=r.cx,c=r.cy,l=r.innerRadius,f=r.outerRadius,h=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(f<l||g===x)return null;var w=(0,o.A)("recharts-sector",O),j=f-l,A=(0,u.F4)(h,j,0,!0);return e=A>0&&360>Math.abs(g-x)?y({cx:n,cy:c,innerRadius:l,outerRadius:f,cornerRadius:Math.min(A,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):d({cx:n,cy:c,innerRadius:l,outerRadius:f,startAngle:g,endAngle:x}),i().createElement("path",s({},(0,a.J9)(r,!0),{className:w,d:e,role:"img"}))}},97208:(t,e,r)=>{var n=r(67161);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},97803:(t,e,r)=>{"use strict";r.d(e,{G:()=>L});var n=r(60222),i=r.n(n),o=r(70991),a=r(86704),c=r(2802),u=r.n(c),l=r(32178),s=r.n(l),f=r(8875),p=r.n(f),h=r(83201),d=r.n(h),y=r(29736),v=r.n(y),m=r(28118),b=r(3380),g=r(42956),x=r(43371),O=r(10427),w=r(1511),j=r(12810),A=r(23872),S=["layout","type","stroke","connectNulls","isRange","ref"],P=["key"];function E(t){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function k(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){B(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,R(n.key),n)}}function I(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(I=function(){return!!t})()}function D(t){return(D=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function N(t,e){return(N=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function B(t,e,r){return(e=R(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function R(t){var e=function(t,e){if("object"!=E(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=E(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==E(e)?e:e+""}var L=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=D(e),B(t=function(t,e){if(e&&("object"===E(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,I()?Reflect.construct(e,r||[],D(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0}),B(t,"id",(0,w.NF)("recharts-area-")),B(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),u()(e)&&e()}),B(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),u()(e)&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&N(n,t),e=[{key:"renderDots",value:function(t,e,r){var o=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(o&&!a)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=(0,A.J9)(this.props,!1),p=(0,A.J9)(u,!0),h=l.map(function(t,e){var r=_(_(_({key:"dot-".concat(e),r:3},f),p),{},{index:e,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return n.renderDotItem(u,r)}),d={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return i().createElement(g.W,M({className:"recharts-area-dots"},d),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,o=e.strokeWidth,a=n[0].x,c=n[n.length-1].x,u=t*Math.abs(a-c),l=s()(n.map(function(t){return t.y||0}));return((0,w.Et)(r)&&"number"==typeof r?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(s()(r.map(function(t){return t.y||0})),l)),(0,w.Et)(l))?i().createElement("rect",{x:a<c?a:a-u,y:0,width:u,height:Math.floor(l+(o?parseInt("".concat(o),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,o=e.strokeWidth,a=n[0].y,c=n[n.length-1].y,u=t*Math.abs(a-c),l=s()(n.map(function(t){return t.x||0}));return((0,w.Et)(r)&&"number"==typeof r?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(s()(r.map(function(t){return t.x||0})),l)),(0,w.Et)(l))?i().createElement("rect",{x:0,y:a<c?a:a-u,width:l+(o?parseInt("".concat(o),10):1),height:Math.floor(u)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var o=this.props,a=o.layout,c=o.type,u=o.stroke,l=o.connectNulls,s=o.isRange,f=(o.ref,k(o,S));return i().createElement(g.W,{clipPath:r?"url(#clipPath-".concat(n,")"):null},i().createElement(m.I,M({},(0,A.J9)(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&i().createElement(m.I,M({},(0,A.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&i().createElement(m.I,M({},(0,A.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,c=n.baseLine,u=n.isAnimationActive,l=n.animationBegin,s=n.animationDuration,f=n.animationEasing,h=n.animationId,y=this.state,v=y.prevPoints,m=y.prevBaseLine;return i().createElement(a.Ay,{begin:l,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"area-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a=n.t;if(v){var u,l=v.length/o.length,s=o.map(function(t,e){var r=Math.floor(e*l);if(v[r]){var n=v[r],i=(0,w.Dj)(n.x,t.x),o=(0,w.Dj)(n.y,t.y);return _(_({},t),{},{x:i(a),y:o(a)})}return t});return u=(0,w.Et)(c)&&"number"==typeof c?(0,w.Dj)(m,c)(a):p()(c)||d()(c)?(0,w.Dj)(m,0)(a):c.map(function(t,e){var r=Math.floor(e*l);if(m[r]){var n=m[r],i=(0,w.Dj)(n.x,t.x),o=(0,w.Dj)(n.y,t.y);return _(_({},t),{},{x:i(a),y:o(a)})}return t}),r.renderAreaStatically(s,u,t,e)}return i().createElement(g.W,null,i().createElement("defs",null,i().createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(a))),i().createElement(g.W,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(o,c,t,e)))})}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,i=r.baseLine,o=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return o&&n&&n.length&&(!c&&l>0||!v()(c,n)||!v()(u,i))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,i,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,a=e.points,c=e.className,u=e.top,l=e.left,s=e.xAxis,f=e.yAxis,h=e.width,d=e.height,y=e.isAnimationActive,v=e.id;if(r||!a||!a.length)return null;var m=this.state.isAnimationFinished,b=1===a.length,O=(0,o.A)("recharts-area",c),w=s&&s.allowDataOverflow,j=f&&f.allowDataOverflow,S=w||j,P=p()(v)?this.id:v,E=null!=(t=(0,A.J9)(n,!1))?t:{r:3,strokeWidth:2},k=E.r,M=E.strokeWidth,T=((0,A.sT)(n)?n:{}).clipDot,_=void 0===T||T,C=2*(void 0===k?3:k)+(void 0===M?2:M);return i().createElement(g.W,{className:O},w||j?i().createElement("defs",null,i().createElement("clipPath",{id:"clipPath-".concat(P)},i().createElement("rect",{x:w?l:l-h/2,y:j?u:u-d/2,width:w?h:2*h,height:j?d:2*d})),!_&&i().createElement("clipPath",{id:"clipPath-dots-".concat(P)},i().createElement("rect",{x:l-C/2,y:u-C/2,width:h+C,height:d+C}))):null,b?null:this.renderArea(S,P),(n||b)&&this.renderDots(S,_,P),(!y||m)&&x.Z.renderCallByParent(this.props,a))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],e&&C(n.prototype,e),r&&C(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);B(L,"displayName","Area"),B(L,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!O.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),B(L,"getBaseValue",function(t,e,r,n){var i=t.layout,o=t.baseValue,a=e.props.baseValue,c=null!=a?a:o;if((0,w.Et)(c)&&"number"==typeof c)return c;var u="horizontal"===i?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]}),B(L,"getComposedData",function(t){var e,r=t.props,n=t.item,i=t.xAxis,o=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,h=t.offset,d=r.layout,y=s&&s.length,v=L.getBaseValue(r,n,i,o),m="horizontal"===d,b=!1,g=p.map(function(t,e){y?r=s[f+e]:Array.isArray(r=(0,j.kr)(t,l))?b=!0:r=[v,r];var r,n=null==r[1]||y&&null==(0,j.kr)(t,l);return m?{x:(0,j.nb)({axis:i,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:o.scale(r[1]),value:r,payload:t}:{x:n?null:i.scale(r[1]),y:(0,j.nb)({axis:o,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}});return e=y||b?g.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?o.scale(e):null}:{x:null!=e?i.scale(e):null,y:t.y}}):m?o.scale(v):i.scale(v),_({points:g,baseLine:e,layout:d,isRange:b},h)}),B(L,"renderDotItem",function(t,e){var r;if(i().isValidElement(t))r=i().cloneElement(t,e);else if(u()(t))r=t(e);else{var n=(0,o.A)("recharts-area-dot","boolean"!=typeof t?t.className:""),a=e.key,c=k(e,P);r=i().createElement(b.c,M({},c,{key:a,className:n}))}return r})},97865:(t,e,r)=>{var n=r(50527),i=r(97657),o=r(94580);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}}};
//# sourceMappingURL=8721.js.map