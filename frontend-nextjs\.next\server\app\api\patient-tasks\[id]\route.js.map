{"version": 3, "file": "../app/api/patient-tasks/[id]/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,iDCAA,sDCAA,6FCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,oWCKO,IAAMA,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAChI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCK,CADoCL,CAAAA,MACvBG,EAAcG,WAAdH,GAA4B,CAACD,EAAOK,EAAE,EAGzD,GAAkB,IAAdP,IAAwB,KAAnBQ,IAAI,CAAe,CAE1B,IAAMC,EAAe,iBAAOJ,EAAKK,EAAAA,QAAU,CAAgBL,EAAKK,EAAAA,QAAU,CAACH,EAAE,CAAGF,EAAKK,EAALL,QAAe,CACzFM,EAAc,iBAAON,EAAKO,EAAAA,OAAS,CAAgBP,EAAKO,EAAAA,OAAS,CAACL,EAAE,CAAGF,EAAKO,EAALP,OAAc,CAC3F,GAAII,IAAiBT,EAAKa,EAALb,IAAAA,OAAkB,EAAIW,IAAgBX,EAAKa,EAAAA,WAAa,EAAI,CAACC,CAH5D,WAGyEC,UAHnD,wBAAyB,yBAAyB,CAGCA,QAAQ,CAACV,EAAKW,EAALX,MAAa,CAAG,CACtH,MAAOY,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,mBAAqB,KAEpD,MAAO,GAAkB,YAAc,GAA5BjB,EAAKQ,EAALR,EAAS,EAEG,YACjBS,MADwBJ,EAAKK,EAAAA,QAAU,CAAgBL,EAAKK,EAAAA,QAAU,CAACH,EAAE,CAAGF,EAAKK,EAALL,QAAKK,IAChEV,EAAKa,EAAAA,WAAa,EAAI,CAACC,CAFtB,iBAAkB,yBAA0B,oBAAoB,CAE7BC,QAAQ,CAACV,EAAKW,EAAAA,MAAQ,CAAG,CAChF,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,mBAAqB,MAIpD,MAAOC,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBb,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBc,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,8BAAgCA,CAAAA,GACvCF,CAAAA,CADuCE,CAAAA,EACvCF,EAAAA,CAAoB,gCAC7B,CACF,CAAG,EAEUI,EAAQtB,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAClI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCsB,CADoCtB,CAAAA,MACjBC,EAAnBqB,IAA+B,CAAZrB,EAGnBsB,EAAe,MAAMpB,EAAcG,WAAdH,GAA4B,CAACD,EAAOK,EAAE,EAG3DE,EAAe,iBAAOc,EAAab,UAAAA,CAA0Ba,EAAab,UAAAA,CAAWH,EAAE,CAAGgB,EAAab,UAAba,CAC1FZ,EAAc,iBAAOY,EAAaX,SAAS,CAATA,EAAsCA,SAAS,CAATA,EAAY,CAAGW,EAAaX,SAAS,CAAtBW,GAC3E,OAAWd,GAAzBT,EAAKQ,EAALR,EAAS,EAAgBS,IAAiBT,EAAKa,EAAAA,IAAtBJ,OAAmC,EAAIE,IAAgBX,EAAKa,EAAAA,GAArBF,QAAkC,CACpG,CADsG,KAC/FM,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,6DAA+D,KAI5F,QAAOK,EAAWV,SAAS,CAGD,cAAtBU,EAAWE,MAAM,EAAoBD,WAAqC,CAAxBC,IAAAA,MAAM,GAC1DF,EAAWG,QAAAA,GAAW,CAAG,IAAIC,IAAAA,CAAAA,CAIL,cAAtBJ,EAAWE,MAAM,EAA4C,WAAa,GAArCD,EAAaC,MAAM,GAC1DF,CADoDE,CACzCC,QAAXH,GAAsB,CAAG,KACzBA,EAAWK,QAAXL,OAA0B,CAAG,MAG/B,IAAMjB,EAAO,MAAMF,EAAcyB,WAAdzB,MAA+B,CAACD,EAAOK,EAAE,CAAEe,CAAXpB,EAEnD,MAAOgB,CAFuDI,CAAAA,CAEvDJ,EAAAA,EAAAA,CAAsBb,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBc,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,8BAAgCA,CAAAA,GACvCF,CAAAA,CADuCE,CAAAA,EACvCF,EAAAA,CAAoB,iCAC7B,CACF,CAAG,EAEUY,EAAS9B,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IACnI,GAAI,CAEF,GAAIF,IAAAA,GAAuB,KAAlBQ,IAAI,CACX,MAAOS,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,sCAAwC,MAGrE,IAAMd,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GAG1C,CAH0CA,CAAAA,KAC1C,MAAMG,EAAc2B,WAAAA,MAAiB,CAAC5B,EAAOK,EAAE,EAATL,CAE/BgB,EAAAA,EAAAA,EAAAA,CAAsB,EAAEa,OAAS,oCAAoC,EAC9E,CAAE,MAAOZ,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,8BAAgCA,CAAAA,GACvCF,CAAAA,CADuCE,CAAAA,EACvCF,EAAAA,CAAoB,iCAC7B,CACF,CAAG,EC7EG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,MAAQ,CAAC,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,yBAAyB,SAC7C,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAee,EAA4B,GAAH,EAAQ,EAEnD,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,EAAYC,CAAf,CAA6C,KAAH,EAA5B,EAEnB,EAAS,EAAYC,EAA+B,MAAH,CAA7B,CAAwC,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,EAAYC,GAAf,IAA+C,EAAjC,OAA0C,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,qCACA,mCACA,iBACA,6CACA,CAAK,CACL,wHACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patient-tasks/[id]/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/?9db8", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\nimport { PatientTask } from '@/types/clinic';\n\nexport const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const data = await payloadClient.getPatientTask(params.id) as PatientTask;\n\n    // Check if user has permission to view this task\n    if (user.role === 'doctor') {\n      const allowedTypes = ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'];\n      const assignedToId = typeof data.assignedTo === 'object' ? data.assignedTo.id : data.assignedTo;\n      const createdById = typeof data.createdBy === 'object' ? data.createdBy.id : data.createdBy;\n      if (assignedToId !== user.payloadUserId && createdById !== user.payloadUserId && !allowedTypes.includes(data.taskType)) {\n        return createErrorResponse('Permission denied', 403);\n      }\n    } else if (user.role === 'front-desk') {\n      const allowedTypes = ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'];\n      const assignedToId = typeof data.assignedTo === 'object' ? data.assignedTo.id : data.assignedTo;\n      if (assignedToId !== user.payloadUserId && !allowedTypes.includes(data.taskType)) {\n        return createErrorResponse('Permission denied', 403);\n      }\n    }\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error fetching patient task:', error);\n    return createErrorResponse('Failed to fetch patient task');\n  }\n});\n\nexport const PATCH = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const updateData = await request.json();\n    \n    // Get the existing task to check permissions\n    const existingTask = await payloadClient.getPatientTask(params.id) as PatientTask;\n\n    // Check if user can update this task\n    const assignedToId = typeof existingTask.assignedTo === 'object' ? existingTask.assignedTo.id : existingTask.assignedTo;\n    const createdById = typeof existingTask.createdBy === 'object' ? existingTask.createdBy.id : existingTask.createdBy;\n    if (user.role !== 'admin' && assignedToId !== user.payloadUserId && createdById !== user.payloadUserId) {\n      return createErrorResponse('You can only update tasks assigned to you or created by you', 403);\n    }\n    \n    // Prevent changing the creator\n    delete updateData.createdBy;\n    \n    // If status is being changed to completed, set completion time\n    if (updateData.status === 'completed' && existingTask.status !== 'completed') {\n      updateData.completedAt = new Date();\n    }\n    \n    // If status is being changed from completed to something else, clear completion data\n    if (updateData.status !== 'completed' && existingTask.status === 'completed') {\n      updateData.completedAt = null;\n      updateData.completionNotes = null;\n    }\n    \n    const data = await payloadClient.updatePatientTask(params.id, updateData);\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error updating patient task:', error);\n    return createErrorResponse('Failed to update patient task');\n  }\n});\n\nexport const DELETE = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    // Only admin can delete tasks\n    if (user.role !== 'admin') {\n      return createErrorResponse('Only administrators can delete tasks', 403);\n    }\n    \n    const payloadClient = createPayloadClient(user);\n    await payloadClient.deletePatientTask(params.id);\n    \n    return createSuccessResponse({ message: 'Patient task deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting patient task:', error);\n    return createErrorResponse('Failed to delete patient task');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patient-tasks/[id]',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-tasks\\\\[id]\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patient-tasks/[id]/route\",\n        pathname: \"/api/patient-tasks/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/patient-tasks/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-tasks\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "params", "payloadClient", "createPayloadClient", "data", "getPatientTask", "id", "role", "assignedToId", "assignedTo", "createdById", "created<PERSON>y", "payloadUserId", "allowedTypes", "includes", "taskType", "createErrorResponse", "createSuccessResponse", "error", "console", "PATCH", "updateData", "existingTask", "status", "completedAt", "Date", "completionNotes", "updatePatientTask", "DELETE", "deletePatientTask", "message", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}