try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="170edc14-bd31-488c-965e-071e61b21bb1",e._sentryDebugIdIdentifier="sentry-dbid-170edc14-bd31-488c-965e-071e61b21bb1")}catch(e){}(()=>{var e={};e.id=4050,e.ids=[4050],e.modules={1378:(e,t,s)=>{"use strict";s.d(t,{V:()=>c});var a=s(24443);s(60222);var r=s(49913),n=s(72595),l=s(33284),i=s(39255);let o=()=>(0,a.jsx)(i.YJP,{className:"size-4","data-sentry-element":"ChevronLeftIcon","data-sentry-component":"LeftIcon","data-sentry-source-file":"calendar.tsx"}),d=()=>(0,a.jsx)(i.vKP,{className:"size-4","data-sentry-element":"ChevronRightIcon","data-sentry-component":"RightIcon","data-sentry-source-file":"calendar.tsx"});function c({className:e,classNames:t,showOutsideDays:s=!0,...i}){return(0,a.jsx)(r.hv,{showOutsideDays:s,className:(0,n.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,n.cn)((0,l.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,n.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===i.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,n.cn)((0,l.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:o,IconRight:d},...i,"data-sentry-element":"DayPicker","data-sentry-component":"Calendar","data-sentry-source-file":"calendar.tsx"})}},1996:(e,t,s)=>{"use strict";s.d(t,{default:()=>et});var a=s(24443),r=s(60222),n=s(34769),l=s(32218),i=s(33284),o=s(10531),d=s(57416),c=s(9343),m=s(12099),u=s(19342),x=s(23032),p=s(67529),g=s(72595);let h={"phone-call":{label:"电话通话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},email:{label:"邮件沟通",icon:m.jQ,color:"bg-green-100 text-green-800 border-green-200"},"consultation-note":{label:"咨询记录",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"in-person-visit":{label:"到院就诊",icon:m.vg,color:"bg-orange-100 text-orange-800 border-orange-200"},"treatment-discussion":{label:"治疗讨论",icon:m.bY,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"billing-inquiry":{label:"账单咨询",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"}},y={open:{label:"开放",color:"bg-gray-100 text-gray-800"},"in-progress":{label:"进行中",color:"bg-blue-100 text-blue-800"},resolved:{label:"已解决",color:"bg-green-100 text-green-800"},closed:{label:"已关闭",color:"bg-gray-100 text-gray-600"}},f={low:{label:"低",color:"bg-gray-100 text-gray-600"},medium:{label:"中",color:"bg-yellow-100 text-yellow-800"},high:{label:"高",color:"bg-red-100 text-red-800"}};function j({patientId:e,interactions:t,loading:s=!1,onCreateInteraction:n,onInteractionClick:d,className:c}){let[j,v]=(0,r.useState)(""),[b,N]=(0,r.useState)("all"),[w,k]=(0,r.useState)("all"),[C,I]=(0,r.useState)(t),S=e=>{let t=h[e]?.icon||m.bY;return(0,a.jsx)(t,{className:"size-4","data-sentry-element":"IconComponent","data-sentry-component":"getInteractionIcon","data-sentry-source-file":"interaction-timeline.tsx"})},z=e=>"string"==typeof e?"未知工作人员":`${e.firstName||""} ${e.lastName||""}`.trim()||e.email;return s?(0,a.jsxs)(l.Zp,{className:c,children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m._L,{className:"size-5"}),"互动时间线"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"size-8 bg-gray-200 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},t))})})]}):(0,a.jsxs)(l.Zp,{className:c,"data-sentry-element":"Card","data-sentry-component":"InteractionTimeline","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsx)(m._L,{className:"size-5","data-sentry-element":"IconClock","data-sentry-source-file":"interaction-timeline.tsx"}),"互动时间线",(0,a.jsx)(o.E,{variant:"secondary",className:"ml-2","data-sentry-element":"Badge","data-sentry-source-file":"interaction-timeline.tsx",children:C.length})]}),n&&(0,a.jsxs)(i.$,{onClick:n,size:"sm",children:[(0,a.jsx)(m.uI,{className:"size-4 mr-2"}),"添加互动"]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(m.C0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"interaction-timeline.tsx"}),(0,a.jsx)(u.p,{placeholder:"搜索互动记录...",value:j,onChange:e=>v(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"interaction-timeline.tsx"})]}),(0,a.jsxs)(x.l6,{value:b,onValueChange:N,"data-sentry-element":"Select","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsxs)(x.bq,{className:"w-full sm:w-[140px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsx)(m.uJ,{className:"size-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"interaction-timeline.tsx"}),(0,a.jsx)(x.yv,{placeholder:"类型","data-sentry-element":"SelectValue","data-sentry-source-file":"interaction-timeline.tsx"})]}),(0,a.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"interaction-timeline.tsx",children:"所有类型"}),Object.entries(h).map(([e,t])=>(0,a.jsx)(x.eb,{value:e,children:t.label},e))]})]}),(0,a.jsxs)(x.l6,{value:w,onValueChange:k,"data-sentry-element":"Select","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsx)(x.bq,{className:"w-full sm:w-[120px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"interaction-timeline.tsx",children:(0,a.jsx)(x.yv,{placeholder:"状态","data-sentry-element":"SelectValue","data-sentry-source-file":"interaction-timeline.tsx"})}),(0,a.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,a.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"interaction-timeline.tsx",children:"所有状态"}),Object.entries(y).map(([e,t])=>(0,a.jsx)(x.eb,{value:e,children:t.label},e))]})]})]})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"interaction-timeline.tsx",children:(0,a.jsx)(p.ScrollArea,{className:"h-[600px]","data-sentry-element":"ScrollArea","data-sentry-source-file":"interaction-timeline.tsx",children:0===C.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(m.bY,{className:"size-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"暂无互动记录"}),(0,a.jsx)("p",{className:"text-sm",children:"开始记录与患者的沟通互动"})]}):(0,a.jsx)("div",{className:"space-y-4",children:C.map((e,t)=>{let s=h[e.interactionType],r=y[e.status],n=f[e.priority];return(0,a.jsxs)("div",{className:"relative",children:[t<C.length-1&&(0,a.jsx)("div",{className:"absolute left-4 top-12 bottom-0 w-px bg-border"}),(0,a.jsxs)("div",{className:(0,g.cn)("flex items-start gap-3 p-4 rounded-lg border transition-colors",d&&"cursor-pointer hover:bg-muted/50"),onClick:()=>d?.(e),children:[(0,a.jsx)("div",{className:(0,g.cn)("flex items-center justify-center size-8 rounded-full border-2",s?.color||"bg-gray-100 text-gray-600 border-gray-200"),children:S(e.interactionType)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-sm leading-tight",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,a.jsx)(o.E,{variant:"outline",className:r.color,children:r.label}),(0,a.jsx)(o.E,{variant:"outline",className:n.color,children:n.label})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground mb-2",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.vg,{className:"size-3"}),z(e.staffMember)]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(m._L,{className:"size-3"}),(0,g.r6)(new Date(e.timestamp))]})]}),e.outcome&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-2",children:e.outcome}),e.followUpRequired&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded",children:[(0,a.jsx)(m.hI,{className:"size-3"}),"需要跟进",e.followUpDate&&(0,a.jsxs)("span",{children:["- ",(0,g.r6)(new Date(e.followUpDate))]})]})]})]})]},e.id)})})})})]})}let v={"follow-up-call":{label:"跟进电话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},"appointment-scheduling":{label:"预约安排",icon:m._v,color:"bg-green-100 text-green-800 border-green-200"},"treatment-reminder":{label:"治疗提醒",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"billing-follow-up":{label:"账单跟进",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"},"medical-record-update":{label:"病历更新",icon:m.nR,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"consultation-follow-up":{label:"咨询跟进",icon:m.bY,color:"bg-orange-100 text-orange-800 border-orange-200"}},b={pending:{label:"待处理",color:"bg-gray-100 text-gray-800",icon:m._L},"in-progress":{label:"进行中",color:"bg-blue-100 text-blue-800",icon:m.hI},completed:{label:"已完成",color:"bg-green-100 text-green-800",icon:m.iW},cancelled:{label:"已取消",color:"bg-red-100 text-red-800",icon:m.MR}},N={low:{label:"低",color:"bg-gray-100 text-gray-600"},medium:{label:"中",color:"bg-yellow-100 text-yellow-800"},high:{label:"高",color:"bg-orange-100 text-orange-800"},urgent:{label:"紧急",color:"bg-red-100 text-red-800"}};function w({patientId:e,tasks:t,loading:s=!1,onCreateTask:n,onTaskClick:c,onTaskStatusChange:h,className:y,viewMode:f="list"}){let[j,w]=(0,r.useState)(""),[k,C]=(0,r.useState)("all"),[I,S]=(0,r.useState)("all"),[z,T]=(0,r.useState)("all"),[q,D]=(0,r.useState)(t),P=e=>{let t=v[e]?.icon||m.nR;return(0,a.jsx)(t,{className:"size-4","data-sentry-element":"IconComponent","data-sentry-component":"getTaskIcon","data-sentry-source-file":"task-manager.tsx"})},R=e=>"string"==typeof e?"未分配":`${e.firstName||""} ${e.lastName||""}`.trim()||e.email,$=e=>q.filter(t=>t.status===e),_=(e,t)=>{h&&h(e.id,t)};if(s)return(0,a.jsxs)(l.Zp,{className:y,children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.nR,{className:"size-5"}),"任务管理"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"size-8 bg-gray-200 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},t))})})]});let E=e=>{let t=v[e.taskType],s=b[e.status],r=N[e.priority],n=(0,g.Jv)(e.dueDate)&&"completed"!==e.status;return(0,a.jsxs)("div",{className:(0,g.cn)("p-4 rounded-lg border transition-colors",c&&"cursor-pointer hover:bg-muted/50",n&&"border-red-200 bg-red-50"),onClick:()=>c?.(e),"data-sentry-component":"renderTaskCard","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:(0,g.cn)("flex items-center justify-center size-6 rounded border",t?.color||"bg-gray-100 text-gray-600 border-gray-200"),children:P(e.taskType)}),(0,a.jsx)("h4",{className:"font-medium text-sm leading-tight",children:e.title})]}),n&&(0,a.jsx)(m.QO,{className:"size-4 text-red-500 flex-shrink-0"})]}),e.description&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2 mb-3",children:e.description}),(0,a.jsx)("div",{className:"flex items-center justify-between gap-2 mb-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(o.E,{variant:"outline",className:r.color,"data-sentry-element":"Badge","data-sentry-source-file":"task-manager.tsx",children:r.label}),(0,a.jsx)(o.E,{variant:"outline",className:s.color,"data-sentry-element":"Badge","data-sentry-source-file":"task-manager.tsx",children:s.label})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.vg,{className:"size-3","data-sentry-element":"IconUser","data-sentry-source-file":"task-manager.tsx"}),R(e.assignedTo)]}),(0,a.jsxs)("span",{className:(0,g.cn)("flex items-center gap-1",n&&"text-red-600 font-medium"),children:[(0,a.jsx)(m._v,{className:"size-3","data-sentry-element":"IconCalendar","data-sentry-source-file":"task-manager.tsx"}),(0,g.r6)(new Date(e.dueDate))]})]}),h&&"completed"!==e.status&&(0,a.jsxs)("div",{className:"flex gap-1 mt-3",children:["pending"===e.status&&(0,a.jsx)(i.$,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),_(e,"in-progress")},className:"h-6 px-2 text-xs",children:"开始"}),"in-progress"===e.status&&(0,a.jsx)(i.$,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),_(e,"completed")},className:"h-6 px-2 text-xs",children:"完成"})]})]},e.id)};return(0,a.jsxs)(l.Zp,{className:y,"data-sentry-element":"Card","data-sentry-component":"TaskManager","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsx)(m.nR,{className:"size-5","data-sentry-element":"IconFileText","data-sentry-source-file":"task-manager.tsx"}),"任务管理",(0,a.jsx)(o.E,{variant:"secondary",className:"ml-2","data-sentry-element":"Badge","data-sentry-source-file":"task-manager.tsx",children:q.length})]}),n&&(0,a.jsxs)(i.$,{onClick:n,size:"sm",children:[(0,a.jsx)(m.uI,{className:"size-4 mr-2"}),"创建任务"]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(m.C0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"task-manager.tsx"}),(0,a.jsx)(u.p,{placeholder:"搜索任务...",value:j,onChange:e=>w(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"task-manager.tsx"})]}),(0,a.jsxs)(x.l6,{value:k,onValueChange:C,"data-sentry-element":"Select","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsxs)(x.bq,{className:"w-full sm:w-[140px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsx)(m.uJ,{className:"size-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"task-manager.tsx"}),(0,a.jsx)(x.yv,{placeholder:"类型","data-sentry-element":"SelectValue","data-sentry-source-file":"task-manager.tsx"})]}),(0,a.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"task-manager.tsx",children:"所有类型"}),Object.entries(v).map(([e,t])=>(0,a.jsx)(x.eb,{value:e,children:t.label},e))]})]}),(0,a.jsxs)(x.l6,{value:z,onValueChange:T,"data-sentry-element":"Select","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsx)(x.bq,{className:"w-full sm:w-[120px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"task-manager.tsx",children:(0,a.jsx)(x.yv,{placeholder:"优先级","data-sentry-element":"SelectValue","data-sentry-source-file":"task-manager.tsx"})}),(0,a.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"task-manager.tsx",children:[(0,a.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"task-manager.tsx",children:"所有优先级"}),Object.entries(N).map(([e,t])=>(0,a.jsx)(x.eb,{value:e,children:t.label},e))]})]})]})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"task-manager.tsx",children:"kanban"===f?(0,a.jsxs)(d.tU,{defaultValue:"pending",className:"w-full",children:[(0,a.jsx)(d.j7,{className:"grid w-full grid-cols-4",children:Object.entries(b).map(([e,t])=>(0,a.jsxs)(d.Xi,{value:e,className:"text-xs",children:[t.label," (",$(e).length,")"]},e))}),Object.entries(b).map(([e,t])=>(0,a.jsx)(d.av,{value:e,children:(0,a.jsx)(p.ScrollArea,{className:"h-[500px]",children:(0,a.jsxs)("div",{className:"space-y-3",children:[$(e).map(E),0===$(e).length&&(0,a.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:(0,a.jsxs)("p",{children:["暂无",t.label,"任务"]})})]})})},e))]}):(0,a.jsx)(p.ScrollArea,{className:"h-[600px]",children:0===q.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(m.nR,{className:"size-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"暂无任务"}),(0,a.jsx)("p",{className:"text-sm",children:"创建任务来跟进患者事务"})]}):(0,a.jsx)("div",{className:"space-y-3",children:q.map(E)})})})]})}var k=s(11035);let C=[{type:"phone-call",label:"记录电话",description:"记录与患者的电话沟通",icon:m.st,color:"text-blue-600 hover:text-blue-700",bgColor:"hover:bg-blue-50"},{type:"email",label:"记录邮件",description:"记录邮件沟通内容",icon:m.jQ,color:"text-green-600 hover:text-green-700",bgColor:"hover:bg-green-50"},{type:"consultation-note",label:"咨询记录",description:"添加咨询或诊疗记录",icon:m.VS,color:"text-purple-600 hover:text-purple-700",bgColor:"hover:bg-purple-50"},{type:"in-person-visit",label:"到院记录",description:"记录患者到院就诊",icon:m.vg,color:"text-orange-600 hover:text-orange-700",bgColor:"hover:bg-orange-50"},{type:"treatment-discussion",label:"治疗讨论",description:"记录治疗方案讨论",icon:m.bY,color:"text-indigo-600 hover:text-indigo-700",bgColor:"hover:bg-indigo-50"},{type:"billing-inquiry",label:"账单咨询",description:"记录账单相关咨询",icon:m.Bl,color:"text-yellow-600 hover:text-yellow-700",bgColor:"hover:bg-yellow-50"}],I=[{type:"follow-up-call",label:"跟进电话",description:"安排跟进电话任务",icon:m.st,color:"text-blue-600 hover:text-blue-700",bgColor:"hover:bg-blue-50"},{type:"appointment-scheduling",label:"预约安排",description:"安排预约相关任务",icon:m._v,color:"text-green-600 hover:text-green-700",bgColor:"hover:bg-green-50"},{type:"treatment-reminder",label:"治疗提醒",description:"创建治疗提醒任务",icon:m.VS,color:"text-purple-600 hover:text-purple-700",bgColor:"hover:bg-purple-50"},{type:"billing-follow-up",label:"账单跟进",description:"创建账单跟进任务",icon:m.Bl,color:"text-yellow-600 hover:text-yellow-700",bgColor:"hover:bg-yellow-50"},{type:"medical-record-update",label:"病历更新",description:"安排病历更新任务",icon:m.nR,color:"text-indigo-600 hover:text-indigo-700",bgColor:"hover:bg-indigo-50"},{type:"consultation-follow-up",label:"咨询跟进",description:"创建咨询跟进任务",icon:m.bY,color:"text-orange-600 hover:text-orange-700",bgColor:"hover:bg-orange-50"}];function S({patient:e,onCreateInteraction:t,onCreateTask:s,onScheduleAppointment:n,onViewBilling:o,className:d,compact:c=!1}){let[u,x]=(0,r.useState)(!1),[p,h]=(0,r.useState)(!1);return c?(0,a.jsxs)("div",{className:(0,g.cn)("flex items-center gap-2",d),children:[(0,a.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>t?.("phone-call"),className:"text-blue-600 hover:text-blue-700 hover:bg-blue-50",children:[(0,a.jsx)(m.st,{className:"size-4 mr-1"}),"电话"]}),(0,a.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>s?.("follow-up-call"),className:"text-green-600 hover:text-green-700 hover:bg-green-50",children:[(0,a.jsx)(m._L,{className:"size-4 mr-1"}),"任务"]}),n&&(0,a.jsxs)(i.$,{size:"sm",variant:"outline",onClick:n,className:"text-purple-600 hover:text-purple-700 hover:bg-purple-50",children:[(0,a.jsx)(m._v,{className:"size-4 mr-1"}),"预约"]}),(0,a.jsxs)(k.rI,{children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsx)(i.$,{size:"sm",variant:"outline",children:(0,a.jsx)(m.uI,{className:"size-4"})})}),(0,a.jsxs)(k.SQ,{align:"end",className:"w-56",children:[(0,a.jsx)(k.lp,{children:"快速操作"}),(0,a.jsx)(k.mB,{}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.lp,{className:"text-xs font-normal text-muted-foreground",children:"添加互动记录"}),C.slice(0,3).map(e=>{let s=e.icon;return(0,a.jsxs)(k._2,{onClick:()=>t(e.type),className:(0,g.cn)("cursor-pointer",e.bgColor),children:[(0,a.jsx)(s,{className:(0,g.cn)("size-4 mr-2",e.color)}),e.label]},e.type)}),(0,a.jsx)(k.mB,{})]}),s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.lp,{className:"text-xs font-normal text-muted-foreground",children:"创建任务"}),I.slice(0,3).map(e=>{let t=e.icon;return(0,a.jsxs)(k._2,{onClick:()=>s(e.type),className:(0,g.cn)("cursor-pointer",e.bgColor),children:[(0,a.jsx)(t,{className:(0,g.cn)("size-4 mr-2",e.color)}),e.label]},e.type)})]}),o&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k.mB,{}),(0,a.jsxs)(k._2,{onClick:o,className:"cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)(m.Bl,{className:"size-4 mr-2 text-gray-600"}),"查看账单"]})]})]})]})]}):(0,a.jsxs)(l.Zp,{className:d,"data-sentry-element":"Card","data-sentry-component":"QuickActions","data-sentry-source-file":"quick-actions.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"quick-actions.tsx",children:[(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"quick-actions.tsx",children:[(0,a.jsx)(m.uI,{className:"size-5","data-sentry-element":"IconPlus","data-sentry-source-file":"quick-actions.tsx"}),"快速操作"]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["为 ",e.fullName," 执行常用操作"]})]}),(0,a.jsxs)(l.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"quick-actions.tsx",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>t?.("phone-call"),className:"h-auto p-4 text-left justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200","data-sentry-element":"Button","data-sentry-source-file":"quick-actions.tsx",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m.st,{className:"size-5","data-sentry-element":"IconPhone","data-sentry-source-file":"quick-actions.tsx"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"联系患者"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"记录电话沟通"})]})]})}),n&&(0,a.jsx)(i.$,{variant:"outline",onClick:n,className:"h-auto p-4 text-left justify-start text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m._v,{className:"size-5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"安排预约"}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:"预约治疗或咨询"})]})]})})]}),t&&(0,a.jsx)("div",{children:(0,a.jsxs)(k.rI,{open:u,onOpenChange:x,children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full justify-between",children:[(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.bY,{className:"size-4"}),"添加互动记录"]}),(0,a.jsx)(m.rI,{className:"size-4"})]})}),(0,a.jsxs)(k.SQ,{className:"w-full min-w-[300px]",children:[(0,a.jsx)(k.lp,{children:"选择互动类型"}),(0,a.jsx)(k.mB,{}),C.map(e=>{let s=e.icon;return(0,a.jsx)(k._2,{onClick:()=>{t(e.type),x(!1)},className:(0,g.cn)("cursor-pointer p-3",e.bgColor),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(s,{className:(0,g.cn)("size-4 mt-0.5",e.color)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.type)})]})]})}),s&&(0,a.jsx)("div",{children:(0,a.jsxs)(k.rI,{open:p,onOpenChange:h,children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",className:"w-full justify-between",children:[(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(m._L,{className:"size-4"}),"创建跟进任务"]}),(0,a.jsx)(m.rI,{className:"size-4"})]})}),(0,a.jsxs)(k.SQ,{className:"w-full min-w-[300px]",children:[(0,a.jsx)(k.lp,{children:"选择任务类型"}),(0,a.jsx)(k.mB,{}),I.map(e=>{let t=e.icon;return(0,a.jsx)(k._2,{onClick:()=>{s(e.type),h(!1)},className:(0,g.cn)("cursor-pointer p-3",e.bgColor),children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)(t,{className:(0,g.cn)("size-4 mt-0.5",e.color)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.type)})]})]})}),o&&(0,a.jsxs)(i.$,{variant:"outline",onClick:o,className:"w-full justify-start text-gray-600 hover:text-gray-700 hover:bg-gray-50",children:[(0,a.jsx)(m.Bl,{className:"size-4 mr-2"}),"查看账单记录"]})]})]})}var z=s(75895);let T={"phone-call":{label:"电话通话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},email:{label:"邮件沟通",icon:m.jQ,color:"bg-green-100 text-green-800 border-green-200"},"consultation-note":{label:"咨询记录",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"in-person-visit":{label:"到院就诊",icon:m.vg,color:"bg-orange-100 text-orange-800 border-orange-200"},"treatment-discussion":{label:"治疗讨论",icon:m.bY,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"billing-inquiry":{label:"账单咨询",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"}},q={"follow-up-call":{label:"跟进电话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},"appointment-scheduling":{label:"预约安排",icon:m._v,color:"bg-green-100 text-green-800 border-green-200"},"treatment-reminder":{label:"治疗提醒",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"billing-follow-up":{label:"账单跟进",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"},"medical-record-update":{label:"病历更新",icon:m.vg,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"consultation-follow-up":{label:"咨询跟进",icon:m.bY,color:"bg-orange-100 text-orange-800 border-orange-200"}};function D({patientId:e,timeline:t,loading:s=!1,onItemClick:n,onEditItem:c,className:h}){let[y,f]=(0,r.useState)(""),[j,v]=(0,r.useState)("all"),[b,N]=(0,r.useState)("all"),[w,k]=(0,r.useState)(t),C=e=>{if("interaction"===e.type){let t=T[e.data.interactionType],s=t?.icon||m.bY;return(0,a.jsx)(s,{className:"size-4"})}{let t=q[e.data.taskType],s=t?.icon||m._L;return(0,a.jsx)(s,{className:"size-4"})}},I=e=>{if("interaction"===e.type){let t=e.data;return T[t.interactionType]?.color||"bg-gray-100 text-gray-800 border-gray-200"}{let t=e.data;return q[t.taskType]?.color||"bg-gray-100 text-gray-800 border-gray-200"}},S=e=>e?"string"==typeof e?"未知工作人员":`${e.firstName||""} ${e.lastName||""}`.trim()||e.email:"未知";if(s)return(0,a.jsxs)(l.Zp,{className:h,children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.bY,{className:"size-5"}),"沟通记录"]})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,a.jsx)("div",{className:"animate-pulse",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"size-8 bg-gray-200 rounded-full"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},t))})})]});let D=t.filter(e=>"interaction"===e.type).length,P=t.filter(e=>"task"===e.type).length;return(0,a.jsxs)(l.Zp,{className:h,"data-sentry-element":"Card","data-sentry-component":"CommunicationLog","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsx)(m.bY,{className:"size-5","data-sentry-element":"IconMessageCircle","data-sentry-source-file":"communication-log.tsx"}),"沟通记录",(0,a.jsx)(o.E,{variant:"secondary",className:"ml-2","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:w.length})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(m.C0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"communication-log.tsx"}),(0,a.jsx)(u.p,{placeholder:"搜索沟通记录...",value:y,onChange:e=>f(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"communication-log.tsx"})]}),(0,a.jsxs)(x.l6,{value:j,onValueChange:v,"data-sentry-element":"Select","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsxs)(x.bq,{className:"w-full sm:w-[140px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsx)(m.uJ,{className:"size-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"communication-log.tsx"}),(0,a.jsx)(x.yv,{placeholder:"类型","data-sentry-element":"SelectValue","data-sentry-source-file":"communication-log.tsx"})]}),(0,a.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"所有类型"}),(0,a.jsx)(z.Separator,{className:"my-1","data-sentry-element":"Separator","data-sentry-source-file":"communication-log.tsx"}),(0,a.jsx)(x.eb,{value:"phone-call","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"电话通话"}),(0,a.jsx)(x.eb,{value:"email","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"邮件沟通"}),(0,a.jsx)(x.eb,{value:"consultation-note","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"咨询记录"}),(0,a.jsx)(x.eb,{value:"in-person-visit","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"到院就诊"}),(0,a.jsx)(x.eb,{value:"treatment-discussion","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"治疗讨论"}),(0,a.jsx)(x.eb,{value:"billing-inquiry","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"账单咨询"}),(0,a.jsx)(z.Separator,{className:"my-1","data-sentry-element":"Separator","data-sentry-source-file":"communication-log.tsx"}),(0,a.jsx)(x.eb,{value:"follow-up-call","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"跟进电话"}),(0,a.jsx)(x.eb,{value:"appointment-scheduling","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"预约安排"}),(0,a.jsx)(x.eb,{value:"treatment-reminder","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"治疗提醒"}),(0,a.jsx)(x.eb,{value:"billing-follow-up","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"账单跟进"}),(0,a.jsx)(x.eb,{value:"medical-record-update","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"病历更新"}),(0,a.jsx)(x.eb,{value:"consultation-follow-up","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"咨询跟进"})]})]})]})]}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"communication-log.tsx",children:(0,a.jsxs)(d.tU,{value:b,onValueChange:N,className:"w-full","data-sentry-element":"Tabs","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-3","data-sentry-element":"TabsList","data-sentry-source-file":"communication-log.tsx",children:[(0,a.jsxs)(d.Xi,{value:"all","data-sentry-element":"TabsTrigger","data-sentry-source-file":"communication-log.tsx",children:["全部 (",t.length,")"]}),(0,a.jsxs)(d.Xi,{value:"interaction","data-sentry-element":"TabsTrigger","data-sentry-source-file":"communication-log.tsx",children:["互动 (",D,")"]}),(0,a.jsxs)(d.Xi,{value:"task","data-sentry-element":"TabsTrigger","data-sentry-source-file":"communication-log.tsx",children:["任务 (",P,")"]})]}),(0,a.jsx)(d.av,{value:b,className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"communication-log.tsx",children:(0,a.jsx)(p.ScrollArea,{className:"h-[600px]","data-sentry-element":"ScrollArea","data-sentry-source-file":"communication-log.tsx",children:0===w.length?(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(m.bY,{className:"size-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"暂无沟通记录"}),(0,a.jsx)("p",{className:"text-sm",children:"开始记录与患者的互动和任务"})]}):(0,a.jsx)("div",{className:"space-y-4",children:w.map((e,t)=>{let s="interaction"===e.type,r=e.data,l=I(e);return(0,a.jsxs)("div",{className:"relative","data-sentry-component":"renderTimelineItem","data-sentry-source-file":"communication-log.tsx",children:[t<w.length-1&&(0,a.jsx)("div",{className:"absolute left-4 top-12 bottom-0 w-px bg-border"}),(0,a.jsxs)("div",{className:(0,g.cn)("flex items-start gap-3 p-4 rounded-lg border transition-colors",n&&"cursor-pointer hover:bg-muted/50"),onClick:()=>n?.(e),children:[(0,a.jsx)("div",{className:(0,g.cn)("flex items-center justify-center size-8 rounded-full border-2 flex-shrink-0",l),children:C(e)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-sm leading-tight mb-1",children:e.title}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,a.jsx)(o.E,{variant:"outline",className:"text-xs","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:s?"互动":"任务"}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(m.vg,{className:"size-3","data-sentry-element":"IconUser","data-sentry-source-file":"communication-log.tsx"}),s?S(e.staffMember):`分配给: ${S(e.assignedTo)}`]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,a.jsxs)(o.E,{variant:"outline",className:"completed"===e.status||"resolved"===e.status||"closed"===e.status?"bg-green-100 text-green-800":"in-progress"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:["open"===e.status&&"开放","in-progress"===e.status&&"进行中","resolved"===e.status&&"已解决","closed"===e.status&&"已关闭","pending"===e.status&&"待处理","completed"===e.status&&"已完成","cancelled"===e.status&&"已取消"]}),(0,a.jsxs)(o.E,{variant:"outline",className:"urgent"===e.priority||"high"===e.priority?"bg-red-100 text-red-800":"medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-600","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:["low"===e.priority&&"低","medium"===e.priority&&"中","high"===e.priority&&"高","urgent"===e.priority&&"紧急"]})]})]}),s?(0,a.jsxs)("div",{children:[r.outcome&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-2",children:r.outcome}),r.followUpRequired&&(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded mb-2",children:[(0,a.jsx)(m.hI,{className:"size-3"}),"需要跟进",r.followUpDate&&(0,a.jsxs)("span",{children:["- ",(0,g.r6)(new Date(r.followUpDate))]})]})]}):(0,a.jsxs)("div",{children:[r.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-2",children:r.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(m._v,{className:"size-3"}),"截止: ",(0,g.r6)(new Date(r.dueDate))]}),r.completedAt&&(0,a.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,a.jsx)(m._L,{className:"size-3"}),"完成: ",r.completedAt?(0,g.r6)(new Date(r.completedAt)):"未完成"]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-3",children:[(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[(0,g.fw)(new Date(e.timestamp))," • ",(0,g.r6)(new Date(e.timestamp))]}),(n||c)&&(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[n&&(0,a.jsxs)(i.$,{size:"sm",variant:"ghost",onClick:t=>{t.stopPropagation(),n(e)},className:"h-6 px-2 text-xs",children:[(0,a.jsx)(m.Hv,{className:"size-3 mr-1"}),"查看"]}),c&&(0,a.jsxs)(i.$,{size:"sm",variant:"ghost",onClick:t=>{t.stopPropagation(),c(e)},className:"h-6 px-2 text-xs",children:[(0,a.jsx)(m.GI,{className:"size-3 mr-1"}),"编辑"]})]})]})]})]})]},e.id)})})})})]})})]})}var P=s(95550),R=s(80395),$=s(13875),_=s(61780),E=s(26882),A=s(81646),B=s(63184),F=s(1378),V=s(5149);let U=$.z.object({interactionType:$.z.enum(["phone-call","email","consultation-note","in-person-visit","treatment-discussion","billing-inquiry"]),title:$.z.string().min(1,"请输入互动标题").max(200,"标题不能超过200个字符"),notes:$.z.string().min(1,"请输入详细记录"),outcome:$.z.string().optional(),followUpRequired:$.z.boolean().default(!1),followUpDate:$.z.string().optional(),priority:$.z.enum(["low","medium","high"]).default("medium"),status:$.z.enum(["open","in-progress","resolved","closed"]).default("open"),relatedAppointment:$.z.string().optional(),relatedBill:$.z.string().optional()}),L=[{value:"phone-call",label:"电话通话",description:"记录与患者的电话沟通"},{value:"email",label:"邮件沟通",description:"记录邮件往来内容"},{value:"consultation-note",label:"咨询记录",description:"记录咨询或诊疗过程"},{value:"in-person-visit",label:"到院就诊",description:"记录患者到院就诊情况"},{value:"treatment-discussion",label:"治疗讨论",description:"记录治疗方案讨论"},{value:"billing-inquiry",label:"账单咨询",description:"记录账单相关咨询"}],O=[{value:"low",label:"低",description:"一般重要性"},{value:"medium",label:"中",description:"中等重要性"},{value:"high",label:"高",description:"高重要性，需要优先处理"}],M=[{value:"open",label:"开放",description:"新创建的互动记录"},{value:"in-progress",label:"进行中",description:"正在处理中"},{value:"resolved",label:"已解决",description:"问题已解决"},{value:"closed",label:"已关闭",description:"互动已结束"}];function J({open:e,onOpenChange:t,patientId:s,patientName:n,interaction:l,defaultType:o,onSubmit:d,loading:c=!1}){let[p,h]=(0,r.useState)(),[y,f]=(0,r.useState)("09:00"),j=!!l,v=(0,P.mN)({resolver:(0,R.u)(U),defaultValues:{interactionType:o||"phone-call",title:"",notes:"",outcome:"",followUpRequired:!1,followUpDate:"",priority:"medium",status:"open",relatedAppointment:"",relatedBill:""}}),b=async e=>{try{if(e.followUpRequired&&p){let[t,s]=y.split(":"),a=new Date(p);a.setHours(parseInt(t),parseInt(s)),e.followUpDate=a.toISOString()}let a={...e,patient:s};await d(a),t(!1)}catch(e){console.error("Error submitting interaction:",e)}},N=v.watch("followUpRequired");return(0,a.jsx)(_.lG,{open:e,onOpenChange:t,"data-sentry-element":"Dialog","data-sentry-component":"InteractionFormDialog","data-sentry-source-file":"interaction-form-dialog.tsx",children:(0,a.jsxs)(_.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"interaction-form-dialog.tsx",children:[(0,a.jsxs)(_.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"interaction-form-dialog.tsx",children:[(0,a.jsx)(_.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"interaction-form-dialog.tsx",children:j?"编辑互动记录":"添加互动记录"}),(0,a.jsxs)(_.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"interaction-form-dialog.tsx",children:["为患者 ",(0,a.jsx)("strong",{children:n})," ",j?"编辑":"创建","互动记录"]})]}),(0,a.jsx)(E.lV,{...v,"data-sentry-element":"Form","data-sentry-source-file":"interaction-form-dialog.tsx",children:(0,a.jsxs)("form",{onSubmit:v.handleSubmit(b),className:"space-y-6",children:[(0,a.jsx)(E.zB,{control:v.control,name:"interactionType",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"互动类型 *"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{placeholder:"选择互动类型"})})}),(0,a.jsx)(x.gC,{children:L.map(e=>(0,a.jsx)(x.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:v.control,name:"title",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"互动标题 *"}),(0,a.jsx)(E.MJ,{children:(0,a.jsx)(u.p,{placeholder:"简要描述此次互动的主题",...e})}),(0,a.jsx)(E.Rr,{children:"请输入简洁明了的标题，方便后续查找"}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:v.control,name:"notes",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"详细记录 *"}),(0,a.jsx)(E.MJ,{children:(0,a.jsx)(A.T,{placeholder:"详细记录互动内容、讨论要点、患者反馈等...",className:"min-h-[120px]",...e})}),(0,a.jsx)(E.Rr,{children:"请详细记录互动的具体内容和重要信息"}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:v.control,name:"outcome",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"互动结果"}),(0,a.jsx)(E.MJ,{children:(0,a.jsx)(A.T,{placeholder:"记录互动的结果、解决方案或后续安排...",className:"min-h-[80px]",...e})}),(0,a.jsx)(E.Rr,{children:"记录此次互动达成的结果或解决方案"}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(E.zB,{control:v.control,name:"priority",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"优先级"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})})}),(0,a.jsx)(x.gC,{children:O.map(e=>(0,a.jsx)(x.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:v.control,name:"status",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"状态"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})})}),(0,a.jsx)(x.gC,{children:M.map(e=>(0,a.jsx)(x.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(E.zB,{control:v.control,name:"followUpRequired",render:({field:e})=>(0,a.jsxs)(E.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(B.S,{checked:e.value,onCheckedChange:e.onChange})}),(0,a.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,a.jsx)(E.lR,{children:"需要跟进"}),(0,a.jsx)(E.Rr,{children:"勾选此项将自动创建跟进任务"})]})]}),"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),N&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(E.lR,{children:"跟进日期"}),(0,a.jsxs)(V.AM,{children:[(0,a.jsx)(V.Wv,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",className:(0,g.cn)("w-full justify-start text-left font-normal",!p&&"text-muted-foreground"),children:[(0,a.jsx)(m._v,{className:"mr-2 h-4 w-4"}),p?(0,g.r6)(p):"选择日期"]})}),(0,a.jsx)(V.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(F.V,{mode:"single",selected:p,onSelect:h,disabled:e=>e<new Date,initialFocus:!0})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(E.lR,{children:"跟进时间"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m._L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(u.p,{type:"time",value:y,onChange:e=>f(e.target.value),className:"pl-10"})]})]})]})]}),(0,a.jsxs)(_.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"interaction-form-dialog.tsx",children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:c,"data-sentry-element":"Button","data-sentry-source-file":"interaction-form-dialog.tsx",children:"取消"}),(0,a.jsx)(i.$,{type:"submit",disabled:c,"data-sentry-element":"Button","data-sentry-source-file":"interaction-form-dialog.tsx",children:c?"保存中...":j?"更新":"创建"})]})]})})]})})}let Z=$.z.object({taskType:$.z.enum(["follow-up-call","appointment-scheduling","treatment-reminder","billing-follow-up","medical-record-update","consultation-follow-up"]),title:$.z.string().min(1,"请输入任务标题").max(200,"标题不能超过200个字符"),description:$.z.string().optional(),assignedTo:$.z.string().min(1,"请选择负责人"),dueDate:$.z.string().min(1,"请选择截止日期"),priority:$.z.enum(["low","medium","high","urgent"]).default("medium"),status:$.z.enum(["pending","in-progress","completed","cancelled"]).default("pending"),relatedInteraction:$.z.string().optional(),completionNotes:$.z.string().optional()}),H=[{value:"follow-up-call",label:"跟进电话",description:"安排跟进电话任务"},{value:"appointment-scheduling",label:"预约安排",description:"安排预约相关任务"},{value:"treatment-reminder",label:"治疗提醒",description:"创建治疗提醒任务"},{value:"billing-follow-up",label:"账单跟进",description:"创建账单跟进任务"},{value:"medical-record-update",label:"病历更新",description:"安排病历更新任务"},{value:"consultation-follow-up",label:"咨询跟进",description:"创建咨询跟进任务"}],G=[{value:"low",label:"低",description:"一般重要性",color:"text-gray-600"},{value:"medium",label:"中",description:"中等重要性",color:"text-yellow-600"},{value:"high",label:"高",description:"高重要性，需要优先处理",color:"text-orange-600"},{value:"urgent",label:"紧急",description:"紧急任务，需要立即处理",color:"text-red-600"}],Y=[{value:"pending",label:"待处理",description:"新创建的任务"},{value:"in-progress",label:"进行中",description:"正在处理中"},{value:"completed",label:"已完成",description:"任务已完成"},{value:"cancelled",label:"已取消",description:"任务已取消"}];function W({open:e,onOpenChange:t,patientId:s,patientName:n,task:l,defaultType:o,relatedInteractionId:d,availableStaff:c,onSubmit:p,loading:h=!1}){let[y,f]=(0,r.useState)(),[j,v]=(0,r.useState)("09:00"),b=!!l,N=(0,P.mN)({resolver:(0,R.u)(Z),defaultValues:{taskType:o||"follow-up-call",title:"",description:"",assignedTo:"",dueDate:"",priority:"medium",status:"pending",relatedInteraction:d||"",completionNotes:""}}),w=async e=>{try{if(y){let[t,s]=j.split(":"),a=new Date(y);a.setHours(parseInt(t),parseInt(s)),e.dueDate=a.toISOString()}let a={...e,patient:s};await p(a),t(!1)}catch(e){console.error("Error submitting task:",e)}},k=N.watch("status");return N.watch("taskType"),(0,a.jsx)(_.lG,{open:e,onOpenChange:t,"data-sentry-element":"Dialog","data-sentry-component":"TaskFormDialog","data-sentry-source-file":"task-form-dialog.tsx",children:(0,a.jsxs)(_.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,a.jsxs)(_.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,a.jsx)(_.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"task-form-dialog.tsx",children:b?"编辑任务":"创建任务"}),(0,a.jsxs)(_.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"task-form-dialog.tsx",children:["为患者 ",(0,a.jsx)("strong",{children:n})," ",b?"编辑":"创建","跟进任务"]})]}),(0,a.jsx)(E.lV,{...N,"data-sentry-element":"Form","data-sentry-source-file":"task-form-dialog.tsx",children:(0,a.jsxs)("form",{onSubmit:N.handleSubmit(w),className:"space-y-6",children:[(0,a.jsx)(E.zB,{control:N.control,name:"taskType",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"任务类型 *"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{placeholder:"选择任务类型"})})}),(0,a.jsx)(x.gC,{children:H.map(e=>(0,a.jsx)(x.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:N.control,name:"title",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"任务标题 *"}),(0,a.jsx)(E.MJ,{children:(0,a.jsx)(u.p,{placeholder:"简要描述任务内容",...e})}),(0,a.jsx)(E.Rr,{children:"请输入简洁明了的任务标题"}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:N.control,name:"description",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"任务描述"}),(0,a.jsx)(E.MJ,{children:(0,a.jsx)(A.T,{placeholder:"详细描述任务要求和注意事项...",className:"min-h-[100px]",...e})}),(0,a.jsx)(E.Rr,{children:"详细描述任务的具体要求和执行步骤"}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:N.control,name:"assignedTo",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"分配给 *"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{placeholder:"选择负责人",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.vg,{className:"size-4"}),(0,a.jsx)("span",{children:"选择负责人"})]})})})}),(0,a.jsx)(x.gC,{children:c.map(e=>(0,a.jsx)(x.eb,{value:e.id,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.vg,{className:"size-4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:`${e.firstName||""} ${e.lastName||""}`.trim()||e.email}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["admin"===e.role&&"管理员","doctor"===e.role&&"医生","front-desk"===e.role&&"前台"]})]})]})},e.id))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(E.lR,{"data-sentry-element":"FormLabel","data-sentry-source-file":"task-form-dialog.tsx",children:"截止日期 *"}),(0,a.jsxs)(V.AM,{"data-sentry-element":"Popover","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,a.jsx)(V.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"task-form-dialog.tsx",children:(0,a.jsxs)(i.$,{variant:"outline",className:(0,g.cn)("w-full justify-start text-left font-normal",!y&&"text-muted-foreground"),"data-sentry-element":"Button","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,a.jsx)(m._v,{className:"mr-2 h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"task-form-dialog.tsx"}),y?(0,g.r6)(y):"选择日期"]})}),(0,a.jsx)(V.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"task-form-dialog.tsx",children:(0,a.jsx)(F.V,{mode:"single",selected:y,onSelect:f,disabled:e=>e<new Date,initialFocus:!0,"data-sentry-element":"Calendar","data-sentry-source-file":"task-form-dialog.tsx"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(E.lR,{"data-sentry-element":"FormLabel","data-sentry-source-file":"task-form-dialog.tsx",children:"截止时间"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m._L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground","data-sentry-element":"IconClock","data-sentry-source-file":"task-form-dialog.tsx"}),(0,a.jsx)(u.p,{type:"time",value:j,onChange:e=>v(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"task-form-dialog.tsx"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(E.zB,{control:N.control,name:"priority",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"优先级"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})})}),(0,a.jsx)(x.gC,{children:G.map(e=>(0,a.jsx)(x.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:(0,g.cn)("font-medium",e.color),children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,a.jsx)(E.zB,{control:N.control,name:"status",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"状态"}),(0,a.jsxs)(x.l6,{onValueChange:e.onChange,defaultValue:e.value,children:[(0,a.jsx)(E.MJ,{children:(0,a.jsx)(x.bq,{children:(0,a.jsx)(x.yv,{})})}),(0,a.jsx)(x.gC,{children:Y.map(e=>(0,a.jsx)(x.eb,{value:e.value,children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,a.jsx)(E.C5,{})]}),"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"})]}),"completed"===k&&(0,a.jsx)(E.zB,{control:N.control,name:"completionNotes",render:({field:e})=>(0,a.jsxs)(E.eI,{children:[(0,a.jsx)(E.lR,{children:"完成备注"}),(0,a.jsx)(E.MJ,{children:(0,a.jsx)(A.T,{placeholder:"记录任务完成情况和总结...",className:"min-h-[80px]",...e})}),(0,a.jsx)(E.Rr,{children:"记录任务完成的具体情况和总结"}),(0,a.jsx)(E.C5,{})]})}),(0,a.jsxs)(_.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,a.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>t(!1),disabled:h,"data-sentry-element":"Button","data-sentry-source-file":"task-form-dialog.tsx",children:"取消"}),(0,a.jsx)(i.$,{type:"submit",disabled:h,"data-sentry-element":"Button","data-sentry-source-file":"task-form-dialog.tsx",children:h?"保存中...":b?"更新":"创建"})]})]})})]})})}var X=s(95826),Q=s(22371),K=s(85001);let ee={interaction:{created:e=>{let t={"phone-call":"电话通话",email:"邮件沟通","consultation-note":"咨询记录","in-person-visit":"到院就诊","treatment-discussion":"治疗讨论","billing-inquiry":"账单咨询"}[e.interactionType]||"互动记录";K.toast.success(`${t}创建成功！`,{description:`标题: ${e.title}`,duration:4e3})},updated:e=>{K.toast.success(`互动记录更新成功！`,{description:`标题: ${e.title}`,duration:4e3})},deleted:e=>{K.toast.success(`互动记录删除成功！`,{description:`已删除: ${e}`,duration:4e3})},followUpCreated:e=>{K.toast.info(`跟进任务已自动创建`,{description:`基于互动: ${e.title}`,duration:5e3})},statusChanged:(e,t,s)=>{let a={open:"开放","in-progress":"进行中",resolved:"已解决",closed:"已关闭"};K.toast.success(`互动状态已更新`,{description:`${e.title}: ${a[t]} → ${a[s]}`,duration:4e3})}},task:{created:e=>{let t={"follow-up-call":"跟进电话","appointment-scheduling":"预约安排","treatment-reminder":"治疗提醒","billing-follow-up":"账单跟进","medical-record-update":"病历更新","consultation-follow-up":"咨询跟进"}[e.taskType]||"任务";K.toast.success(`${t}任务创建成功！`,{description:`标题: ${e.title}`,duration:4e3})},updated:e=>{K.toast.success(`任务更新成功！`,{description:`标题: ${e.title}`,duration:4e3})},deleted:e=>{K.toast.success(`任务删除成功！`,{description:`已删除: ${e}`,duration:4e3})},assigned:(e,t)=>{let s=`${t.firstName||""} ${t.lastName||""}`.trim()||t.email;K.toast.info(`任务已分配`,{description:`${e.title} → ${s}`,duration:4e3})},statusChanged:(e,t,s)=>{let a={pending:"待处理","in-progress":"进行中",completed:"已完成",cancelled:"已取消"};K.toast.success(`任务状态已更新`,{description:`${{pending:"⏳","in-progress":"\uD83D\uDD04",completed:"✅",cancelled:"❌"}[s]} ${e.title}: ${a[t]} → ${a[s]}`,duration:4e3})},completed:e=>{K.toast.success(`🎉 任务完成！`,{description:`${e.title} 已标记为完成`,duration:5e3})},overdue:e=>{K.toast.warning(`⚠️ 任务已逾期`,{description:`${e.title} - 截止时间: ${(0,g.r6)(new Date(e.dueDate))}`,duration:8e3})},reminder:(e,t)=>{let s=t<60?`${t} 分钟后`:`${Math.floor(t/60)} 小时后`;K.toast.info(`📅 任务提醒`,{description:`${e.title} 将在 ${s} 到期`,duration:6e3})}},error:{interactionCreateFailed:e=>{K.toast.error(`互动记录创建失败`,{description:e||"请检查网络连接后重试",duration:5e3})},taskCreateFailed:e=>{K.toast.error(`任务创建失败`,{description:e||"请检查网络连接后重试",duration:5e3})},updateFailed:(e,t)=>{K.toast.error(`${{interaction:"互动记录",task:"任务"}[e]||"项目"}更新失败`,{description:t||"请检查网络连接后重试",duration:5e3})},deleteFailed:(e,t)=>{K.toast.error(`${{interaction:"互动记录",task:"任务"}[e]||"项目"}删除失败`,{description:t||"请检查网络连接后重试",duration:5e3})},loadFailed:(e,t)=>{K.toast.error(`${{interactions:"互动记录",tasks:"任务",timeline:"时间线"}[e]||"数据"}加载失败`,{description:t||"请检查网络连接后重试",duration:5e3})},permissionDenied:e=>{K.toast.error(`权限不足`,{description:`您没有权限执行: ${e}`,duration:5e3})},networkError:()=>{K.toast.error(`网络连接失败`,{description:"请检查网络连接后重试",duration:5e3})}}};function et({patientId:e}){let t=(0,n.useRouter)(),{user:s}=(0,Q.Jd)(),[u,x]=(0,r.useState)(null),[p,h]=(0,r.useState)([]),[y,f]=(0,r.useState)([]),[v,b]=(0,r.useState)([]),[N,k]=(0,r.useState)([]),[C,I]=(0,r.useState)(!0),[z,T]=(0,r.useState)("overview"),[q,P]=(0,r.useState)(!1),[R,$]=(0,r.useState)(!1),[_,E]=(0,r.useState)(""),[A,B]=(0,r.useState)(""),[F,V]=(0,r.useState)(),[U,L]=(0,r.useState)(),[O,M]=(0,r.useState)(!1),Z=e=>{E(e||""),V(void 0),P(!0)},H=e=>{B(e||""),L(void 0),$(!0)},G=async t=>{M(!0);try{let a=(0,X.o)({clerkId:s.id,email:s.emailAddresses[0]?.emailAddress||"",firstName:s.firstName||"",lastName:s.lastName||""});if(F){let e=await a.updatePatientInteraction(F.id,t);h(t=>t.map(t=>t.id===F.id?e:t)),ee.interaction.updated(e)}else{let e=await a.createPatientInteraction(t);h(t=>[e,...t]),ee.interaction.created(e)}let r=await a.getPatientTimeline(e,{limit:100});b(r.docs||[])}catch(e){console.error("Error submitting interaction:",e),ee.error.interactionCreateFailed(e instanceof Error?e.message:void 0)}finally{M(!1)}},Y=async t=>{M(!0);try{let a=(0,X.o)({clerkId:s.id,email:s.emailAddresses[0]?.emailAddress||"",firstName:s.firstName||"",lastName:s.lastName||""});if(U){let e=await a.updatePatientTask(U.id,t);f(t=>t.map(t=>t.id===U.id?e:t)),ee.task.updated(e)}else{let e=await a.createPatientTask(t);f(t=>[e,...t]),ee.task.created(e)}let r=await a.getPatientTimeline(e,{limit:100});b(r.docs||[])}catch(e){console.error("Error submitting task:",e),ee.error.taskCreateFailed(e instanceof Error?e.message:void 0)}finally{M(!1)}},K=()=>{t.push(`/dashboard/appointments/new?patientId=${e}`)},et=()=>{t.push(`/dashboard/billing?patientId=${e}`)},es=async(e,t)=>{try{let a=(0,X.o)({clerkId:s.id,email:s.emailAddresses[0]?.emailAddress||"",firstName:s.firstName||"",lastName:s.lastName||""});await a.updatePatientTask(e,{status:t}),f(s=>s.map(s=>s.id===e?{...s,status:t}:s));let r=y.find(t=>t.id===e);r&&ee.task.statusChanged(r,r.status,t)}catch(e){console.error("Error updating task status:",e),ee.error.updateFailed("task",e instanceof Error?e.message:void 0)}};if(C)return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,a.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-96 bg-gray-200 rounded"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"h-48 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]})});if(!u)return(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(m.vg,{className:"size-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"患者未找到"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:"请检查患者ID是否正确"}),(0,a.jsxs)(i.$,{onClick:()=>t.back(),children:[(0,a.jsx)(m.Gk,{className:"size-4 mr-2"}),"返回"]})]});let ea=p.length,er=y.filter(e=>"pending"===e.status).length,en=y.filter(e=>"completed"!==e.status&&new Date(e.dueDate)<new Date).length,el=p[0];return(0,a.jsxs)("div",{className:"space-y-6","data-sentry-component":"PatientDetailView","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>t.back(),"data-sentry-element":"Button","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(m.Gk,{className:"size-4 mr-2","data-sentry-element":"IconArrowLeft","data-sentry-source-file":"patient-detail-view.tsx"}),"返回"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:u.fullName}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"患者详细信息"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(S,{patient:u,onCreateInteraction:Z,onCreateTask:H,onScheduleAppointment:K,onViewBilling:et,compact:!0,"data-sentry-element":"QuickActions","data-sentry-source-file":"patient-detail-view.tsx"}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(m.GI,{className:"size-4 mr-2","data-sentry-element":"IconEdit","data-sentry-source-file":"patient-detail-view.tsx"}),"编辑"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(d.tU,{value:z,onValueChange:T,className:"w-full","data-sentry-element":"Tabs","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-4","data-sentry-element":"TabsList","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(d.Xi,{value:"overview","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:"概览"}),(0,a.jsxs)(d.Xi,{value:"interactions","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:["互动记录 (",ea,")"]}),(0,a.jsxs)(d.Xi,{value:"tasks","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:["任务管理 (",y.length,")"]}),(0,a.jsx)(d.Xi,{value:"timeline","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:"时间线"})]}),(0,a.jsxs)(d.av,{value:"overview",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(m.vg,{className:"size-5","data-sentry-element":"IconUser","data-sentry-source-file":"patient-detail-view.tsx"}),"基本信息"]})}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsxs)(c.Avatar,{className:"size-16","data-sentry-element":"Avatar","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(c.AvatarImage,{src:"object"==typeof u.photo?u.photo?.url:void 0,alt:u.fullName,"data-sentry-element":"AvatarImage","data-sentry-source-file":"patient-detail-view.tsx"}),(0,a.jsx)(c.AvatarFallback,{className:"text-lg","data-sentry-element":"AvatarFallback","data-sentry-source-file":"patient-detail-view.tsx",children:u.fullName.slice(0,2).toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.st,{className:"size-4 text-muted-foreground","data-sentry-element":"IconPhone","data-sentry-source-file":"patient-detail-view.tsx"}),(0,a.jsx)("span",{className:"font-medium",children:u.phone})]}),u.email&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.jQ,{className:"size-4 text-muted-foreground"}),(0,a.jsx)("span",{children:u.email})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m._v,{className:"size-4 text-muted-foreground","data-sentry-element":"IconCalendar","data-sentry-source-file":"patient-detail-view.tsx"}),(0,a.jsxs)("span",{children:["创建时间: ",(0,g.r6)(new Date(u.createdAt))]})]}),u.lastVisit&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m._L,{className:"size-4 text-muted-foreground"}),(0,a.jsxs)("span",{children:["最后就诊: ",(0,g.r6)(new Date(u.lastVisit))]})]})]}),u.userType&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.E,{variant:"patient"===u.userType?"default":"secondary",children:"patient"===u.userType?"正式患者":"咨询用户"}),u.status&&(0,a.jsxs)(o.E,{variant:"outline",children:["active"===u.status&&"活跃","inactive"===u.status&&"非活跃","converted"===u.status&&"已转换"]})]})]})]})})]}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(m.$7,{className:"size-5","data-sentry-element":"IconActivity","data-sentry-source-file":"patient-detail-view.tsx"}),"活动概要"]})}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:ea}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"总互动次数"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:er}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"待处理任务"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:en}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"逾期任务"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:el?(0,g.fw)(new Date(el.timestamp)):"无"}),(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"最后联系"})]})]})})]})]}),(0,a.jsx)(d.av,{value:"interactions","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsx)(j,{patientId:e,interactions:p,onCreateInteraction:Z,onInteractionClick:e=>console.log("View interaction:",e),"data-sentry-element":"InteractionTimeline","data-sentry-source-file":"patient-detail-view.tsx"})}),(0,a.jsx)(d.av,{value:"tasks","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsx)(w,{patientId:e,tasks:y,onCreateTask:H,onTaskClick:e=>console.log("View task:",e),onTaskStatusChange:es,viewMode:"list","data-sentry-element":"TaskManager","data-sentry-source-file":"patient-detail-view.tsx"})}),(0,a.jsx)(d.av,{value:"timeline","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsx)(D,{patientId:e,timeline:v,onItemClick:e=>console.log("View timeline item:",e),onEditItem:e=>console.log("Edit timeline item:",e),"data-sentry-element":"CommunicationLog","data-sentry-source-file":"patient-detail-view.tsx"})})]})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(S,{patient:u,onCreateInteraction:Z,onCreateTask:H,onScheduleAppointment:K,onViewBilling:et,"data-sentry-element":"QuickActions","data-sentry-source-file":"patient-detail-view.tsx"}),(0,a.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,a.jsx)(m.as,{className:"size-5","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"patient-detail-view.tsx"}),"最近活动"]})}),(0,a.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,a.jsxs)("div",{className:"space-y-3",children:[v.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-start gap-3 text-sm",children:[(0,a.jsx)("div",{className:"size-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-muted-foreground text-xs",children:(0,g.fw)(new Date(e.timestamp))})]})]},e.id)),0===v.length&&(0,a.jsx)("p",{className:"text-muted-foreground text-sm text-center py-4",children:"暂无活动记录"})]})})]})]})]}),(0,a.jsx)(J,{open:q,onOpenChange:P,patientId:e,patientName:u.fullName,interaction:F,defaultType:_,onSubmit:G,loading:O,"data-sentry-element":"InteractionFormDialog","data-sentry-source-file":"patient-detail-view.tsx"}),(0,a.jsx)(W,{open:R,onOpenChange:$,patientId:e,patientName:u.fullName,task:U,defaultType:A,availableStaff:N,onSubmit:Y,loading:O,"data-sentry-element":"TaskFormDialog","data-sentry-source-file":"patient-detail-view.tsx"})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},61780:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>x,L3:()=>p,c7:()=>u,lG:()=>i,rr:()=>g,zM:()=>o});var a=s(24443);s(60222);var r=s(99873),n=s(20422),l=s(72595);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,a.jsx)(r.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,a.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function m({className:e,children:t,...s}){return(0,a.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,a.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,a.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,a.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function p({className:e,...t}){return(0,a.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function g({className:e,...t}){return(0,a.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},61883:(e,t,s)=>{"use strict";let a;s.r(t),s.d(t,{default:()=>j,generateImageMetadata:()=>y,generateMetadata:()=>h,generateViewport:()=>f,metadata:()=>u});var r=s(63033),n=s(78869),l=s(22576),i=s(44508),o=s(83829),d=s(18124),c=s(62243),m=s(19761);let u={title:"Dashboard : Patient Detail"};async function x(e){let t=await e.params;return t.id&&"new"!==t.id||(0,i.notFound)(),(0,n.jsx)(o.A,{scrollable:!0,"data-sentry-element":"PageContainer","data-sentry-component":"PatientDetailPage","data-sentry-source-file":"page.tsx",children:(0,n.jsx)("div",{className:"flex-1 space-y-4",children:(0,n.jsx)(l.Suspense,{fallback:(0,n.jsx)(d.A,{}),"data-sentry-element":"Suspense","data-sentry-source-file":"page.tsx",children:(0,n.jsx)(c.default,{patientId:t.id,"data-sentry-element":"PatientDetailView","data-sentry-source-file":"page.tsx"})})})})}let p={...r},g="workUnitAsyncStorage"in p?p.workUnitAsyncStorage:"requestAsyncStorage"in p?p.requestAsyncStorage:void 0;a=new Proxy(x,{apply:(e,t,s)=>{let a,r,n;try{let e=g?.getStore();a=e?.headers.get("sentry-trace")??void 0,r=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return m.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/patients/[id]",componentType:"Page",sentryTraceHeader:a,baggageHeader:r,headers:n}).apply(t,s)}});let h=void 0,y=void 0,f=void 0,j=a},62243:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\patients\\\\patient-detail-view.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\patients\\patient-detail-view.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63184:(e,t,s)=>{"use strict";s.d(t,{S:()=>I});var a=s(24443),r=s(60222),n=s(24368),l=s(4684),i=s(12772),o=s(36612),d=s(87483),c=s(42354),m=s(49258),u=s(24582),x="Checkbox",[p,g]=(0,l.A)(x),[h,y]=p(x),f=r.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:l,checked:d,defaultChecked:c,required:m,disabled:x,value:p="on",onCheckedChange:g,form:y,...f}=e,[j,v]=r.useState(null),k=(0,n.s)(t,e=>v(e)),C=r.useRef(!1),I=!j||y||!!j.closest("form"),[S=!1,z]=(0,o.i)({prop:d,defaultProp:c,onChange:g}),T=r.useRef(S);return r.useEffect(()=>{let e=j?.form;if(e){let t=()=>z(T.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[j,z]),(0,a.jsxs)(h,{scope:s,state:S,disabled:x,children:[(0,a.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":N(S)?"mixed":S,"aria-required":m,"data-state":w(S),"data-disabled":x?"":void 0,disabled:x,value:p,...f,ref:k,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(e.onClick,e=>{z(e=>!!N(e)||!e),I&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),I&&(0,a.jsx)(b,{control:j,bubbles:!C.current,name:l,value:p,checked:S,required:m,disabled:x,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!N(c)&&c})]})});f.displayName=x;var j="CheckboxIndicator",v=r.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:r,...n}=e,l=y(j,s);return(0,a.jsx)(m.C,{present:r||N(l.state)||!0===l.state,children:(0,a.jsx)(u.sG.span,{"data-state":w(l.state),"data-disabled":l.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});v.displayName=j;var b=e=>{let{control:t,checked:s,bubbles:n=!0,defaultChecked:l,...i}=e,o=r.useRef(null),m=(0,d.Z)(s),u=(0,c.X)(t);r.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&t){let a=new Event("click",{bubbles:n});e.indeterminate=N(s),t.call(e,!N(s)&&s),e.dispatchEvent(a)}},[m,s,n]);let x=r.useRef(!N(s)&&s);return(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:l??x.current,...i,tabIndex:-1,ref:o,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return"indeterminate"===e}function w(e){return N(e)?"indeterminate":e?"checked":"unchecked"}var k=s(84338),C=s(72595);function I({className:e,...t}){return(0,a.jsx)(f,{"data-slot":"checkbox",className:(0,C.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(v,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,a.jsx)(k.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},63235:(e,t,s)=>{Promise.resolve().then(s.bind(s,1996)),Promise.resolve().then(s.bind(s,67529))},65358:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>o});var a=s(29703),r=s(85544),n=s(62458),l=s(77821),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(t,i);let o={children:["",{children:["dashboard",{children:["patients",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61883)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\patients\\[id]\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/dashboard/patients/[id]/page",pathname:"/dashboard/patients/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},87211:(e,t,s)=>{Promise.resolve().then(s.bind(s,62243)),Promise.resolve().then(s.bind(s,89371))},94735:e=>{"use strict";e.exports=require("events")},95826:(e,t,s)=>{"use strict";s.d(t,{o:()=>r});class a{constructor(e){this.user=e}async makeRequest(e,t={}){let s,a,{method:r="GET",body:n,params:l}=t;if(s=`http://localhost:8002/api${e}`,l){let e=new URLSearchParams;Object.entries(l).forEach(([t,s])=>{null!=s&&e.append(t,s.toString())}),e.toString()&&(s+=`?${e.toString()}`)}a={method:r,headers:{"Content-Type":"application/json","X-Clerk-User-Id":this.user.clerkId,"X-User-Email":this.user.email}},n&&"GET"!==r&&(a.body=JSON.stringify(n));try{let e=await fetch(s,a);if(!e.ok){let t=await e.text();throw Error(`API request failed: ${e.status} ${e.statusText} - ${t}`)}return await e.json()}catch(t){throw console.error(`API request failed for ${e}:`,t),t}}async getAppointments(e){return this.makeRequest("/appointments",{params:{depth:"2",...e}})}async getAppointment(e){return this.makeRequest(`/appointments/${e}`,{params:{depth:"2"}})}async createAppointment(e){return this.makeRequest("/appointments",{method:"POST",body:e})}async updateAppointment(e,t){return this.makeRequest(`/appointments/${e}`,{method:"PATCH",body:t})}async deleteAppointment(e){return this.makeRequest(`/appointments/${e}`,{method:"DELETE"})}async getPatients(e){let t={depth:"1",...e};return e?.search&&(t["where[or][0][fullName][contains]"]=e.search,t["where[or][1][phone][contains]"]=e.search,t["where[or][2][email][contains]"]=e.search,delete t.search),this.makeRequest("/patients",{params:t})}async getPatient(e){return this.makeRequest(`/patients/${e}`,{params:{depth:"1"}})}async createPatient(e){return this.makeRequest("/patients",{method:"POST",body:e})}async updatePatient(e,t){return this.makeRequest(`/patients/${e}`,{method:"PATCH",body:t})}async deletePatient(e){return this.makeRequest(`/patients/${e}`,{method:"DELETE"})}async getTreatments(e){return this.makeRequest("/treatments",{params:{depth:"1",...e}})}async getTreatment(e){return this.makeRequest(`/treatments/${e}`)}async createTreatment(e){return this.makeRequest("/treatments",{method:"POST",body:e})}async updateTreatment(e,t){return this.makeRequest(`/treatments/${e}`,{method:"PATCH",body:t})}async deleteTreatment(e){return this.makeRequest(`/treatments/${e}`,{method:"DELETE"})}async getUsers(e){return this.makeRequest("/users",{params:{depth:"1",...e}})}async updateUser(e,t){return this.makeRequest(`/users/${e}`,{method:"PATCH",body:t})}async syncCurrentUser(){return this.makeRequest("/users/sync",{method:"POST",body:{clerkId:this.user.clerkId,email:this.user.email,firstName:this.user.firstName,lastName:this.user.lastName}})}async syncUser(e){try{let t=await this.makeRequest("/users",{params:{where:JSON.stringify({clerkId:{equals:e.clerkId}}),limit:1}});if(!t.docs||!(t.docs.length>0))return await this.makeRequest("/users",{method:"POST",body:{email:e.email,clerkId:e.clerkId,firstName:e.firstName,lastName:e.lastName,role:"front-desk",lastLogin:new Date().toISOString()}});{let s=t.docs[0];return await this.makeRequest(`/users/${s.id}`,{method:"PATCH",body:{email:e.email,firstName:e.firstName,lastName:e.lastName,lastLogin:new Date().toISOString()}})}}catch(t){return console.error("Error syncing user with Payload:",t),{id:"temp-id",email:e.email,clerkId:e.clerkId,role:"front-desk",firstName:e.firstName,lastName:e.lastName}}}async getPatientInteractions(e){return this.makeRequest("/patient-interactions",{params:{depth:"2",...e}})}async getPatientInteraction(e){return this.makeRequest(`/patient-interactions/${e}`,{params:{depth:"2"}})}async createPatientInteraction(e){return this.makeRequest("/patient-interactions",{method:"POST",body:e})}async updatePatientInteraction(e,t){return this.makeRequest(`/patient-interactions/${e}`,{method:"PATCH",body:t})}async deletePatientInteraction(e){return this.makeRequest(`/patient-interactions/${e}`,{method:"DELETE"})}async getPatientTasks(e){return this.makeRequest("/patient-tasks",{params:{depth:"2",...e}})}async getPatientTask(e){return this.makeRequest(`/patient-tasks/${e}`,{params:{depth:"2"}})}async createPatientTask(e){return this.makeRequest("/patient-tasks",{method:"POST",body:e})}async updatePatientTask(e,t){return this.makeRequest(`/patient-tasks/${e}`,{method:"PATCH",body:t})}async deletePatientTask(e){return this.makeRequest(`/patient-tasks/${e}`,{method:"DELETE"})}async getPatientInteractionsByPatient(e,t){return this.makeRequest(`/patients/${e}/interactions`,{params:{depth:"2",...t}})}async getPatientTasksByPatient(e,t){return this.makeRequest(`/patients/${e}/tasks`,{params:{depth:"2",...t}})}async getPatientTimeline(e,t){return this.makeRequest(`/patients/${e}/timeline`,{params:{depth:"2",...t}})}}function r(e){return new a(e)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,3875,395,9255,5903,8774,7494,5392,3566],()=>s(65358));module.exports=a})();
//# sourceMappingURL=page.js.map