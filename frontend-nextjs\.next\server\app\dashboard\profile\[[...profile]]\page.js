try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="1f42771d-3093-436d-96d0-b98ba8211626",e._sentryDebugIdIdentifier="sentry-dbid-1f42771d-3093-436d-96d0-b98ba8211626")}catch(e){}(()=>{var e={};e.id=3411,e.ids=[3411],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21692:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f7b45347fd50452ee6e2850ded1018991a7b086f0":()=>s.at,"7f909588461cb83e855875f4939d6f26e4ae81b49e":()=>s.ot,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261":()=>s.ai});var s=t(64965)},21820:e=>{"use strict";e.exports=require("os")},22483:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f22efd92a3b59d43d3d12fe480e87910640e1db9e":()=>s.y});var s=t(41372)},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29809:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.default,__next_app__:()=>u,pages:()=>p,routeModule:()=>l,tree:()=>a});var s=t(29703),o=t(85544),i=t(62458),n=t(77821),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let a={children:["",{children:["dashboard",{children:["profile",{children:["[[...profile]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,51296)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\profile\\[[...profile]]\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/dashboard/profile/[[...profile]]/page",pathname:"/dashboard/profile/[[...profile]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:a}})},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},46711:(e,r,t)=>{Promise.resolve().then(t.bind(t,21830)),Promise.resolve().then(t.bind(t,55439)),Promise.resolve().then(t.bind(t,40112)),Promise.resolve().then(t.bind(t,15349)),Promise.resolve().then(t.bind(t,90339)),Promise.resolve().then(t.bind(t,98451))},48161:e=>{"use strict";e.exports=require("node:os")},51296:(e,r,t)=>{"use strict";let s;t.r(r),t.d(r,{default:()=>m,generateImageMetadata:()=>x,generateMetadata:()=>f,generateViewport:()=>h,metadata:()=>p});var o=t(63033),i=t(78869),n=t(98451);function d(){return(0,i.jsx)("div",{className:"flex w-full flex-col p-4","data-sentry-component":"ProfileViewPage","data-sentry-source-file":"profile-view-page.tsx",children:(0,i.jsx)(n.UserProfile,{"data-sentry-element":"UserProfile","data-sentry-source-file":"profile-view-page.tsx"})})}var a=t(19761);let p={title:"Dashboard : Profile"};async function u(){return(0,i.jsx)(d,{"data-sentry-element":"ProfileViewPage","data-sentry-component":"Page","data-sentry-source-file":"page.tsx"})}let l={...o},c="workUnitAsyncStorage"in l?l.workUnitAsyncStorage:"requestAsyncStorage"in l?l.requestAsyncStorage:void 0;s=new Proxy(u,{apply:(e,r,t)=>{let s,o,i;try{let e=c?.getStore();s=e?.headers.get("sentry-trace")??void 0,o=e?.headers.get("baggage")??void 0,i=e?.headers}catch(e){}return a.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/profile/[[...profile]]",componentType:"Page",sentryTraceHeader:s,baggageHeader:o,headers:i}).apply(r,t)}});let f=void 0,x=void 0,h=void 0,m=s},51439:(e,r,t)=>{Promise.resolve().then(t.bind(t,52943)),Promise.resolve().then(t.bind(t,17893)),Promise.resolve().then(t.bind(t,8074)),Promise.resolve().then(t.bind(t,27144)),Promise.resolve().then(t.bind(t,79153)),Promise.resolve().then(t.bind(t,95614))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[55,3738,1950,7927,6451,5618,2584,4144,7988,8774,7494],()=>t(29809));module.exports=s})();
//# sourceMappingURL=page.js.map