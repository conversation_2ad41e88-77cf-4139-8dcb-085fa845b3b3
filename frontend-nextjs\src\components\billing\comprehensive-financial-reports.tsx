'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';
import { 
  IconChartBar, 
  IconTrendingUp, 
  IconTrendingDown,
  IconDownload,
  IconRefresh,
  IconCalendar,
  IconCash,
  IconCreditCard,
  IconPigMoney,
  IconUsers,
  IconReceipt,
  IconAlertCircle,
  IconTarget,
  IconActivity
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { useRole, PermissionGate } from '@/lib/role-context';

interface RevenueData {
  period: string;
  revenue: number;
  transactions: number;
  averageTransaction: number;
  growth: number;
}

interface PaymentMethodData {
  method: string;
  amount: number;
  count: number;
  percentage: number;
}

interface TreatmentRevenueData {
  treatment: string;
  revenue: number;
  count: number;
  averagePrice: number;
}

interface OutstandingData {
  totalOutstanding: number;
  overdueAmount: number;
  billsCount: number;
  overdueBillsCount: number;
  averageDaysOverdue: number;
  bills: Array<{
    billNumber: string;
    patient: string;
    amount: number;
    daysOverdue: number;
    dueDate: string;
  }>;
}

interface FinancialReportData {
  summary: {
    totalRevenue: number;
    totalBills: number;
    totalOutstanding: number;
    collectionRate: number;
    averageTransactionValue: number;
  };
  revenueByPeriod: RevenueData[];
  paymentMethods: PaymentMethodData[];
  treatmentRevenue: TreatmentRevenueData[];
  outstanding: OutstandingData;
  trends: {
    revenueGrowth: number;
    transactionGrowth: number;
    collectionRateChange: number;
  };
}

export function ComprehensiveFinancialReports() {
  const { hasPermission } = useRole();
  const [reportData, setReportData] = useState<FinancialReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [reportType, setReportType] = useState('monthly');
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Simulate comprehensive financial report data
      const mockData: FinancialReportData = {
        summary: {
          totalRevenue: 125000,
          totalBills: 450,
          totalOutstanding: 25000,
          collectionRate: 80.0,
          averageTransactionValue: 277.78,
        },
        revenueByPeriod: [
          { period: '2024-01', revenue: 45000, transactions: 180, averageTransaction: 250, growth: 15.2 },
          { period: '2024-02', revenue: 52000, transactions: 195, averageTransaction: 267, growth: 15.6 },
          { period: '2024-03', revenue: 48000, transactions: 175, averageTransaction: 274, growth: -7.7 },
          { period: '2024-04', revenue: 55000, transactions: 200, averageTransaction: 275, growth: 14.6 },
        ],
        paymentMethods: [
          { method: 'cash', amount: 50000, count: 200, percentage: 40.0 },
          { method: 'card', amount: 37500, count: 150, percentage: 30.0 },
          { method: 'wechat', amount: 25000, count: 100, percentage: 20.0 },
          { method: 'alipay', amount: 12500, count: 50, percentage: 10.0 },
        ],
        treatmentRevenue: [
          { treatment: '面部护理', revenue: 45000, count: 150, averagePrice: 300 },
          { treatment: '身体护理', revenue: 35000, count: 100, averagePrice: 350 },
          { treatment: '激光治疗', revenue: 30000, count: 60, averagePrice: 500 },
          { treatment: '注射美容', revenue: 15000, count: 30, averagePrice: 500 },
        ],
        outstanding: {
          totalOutstanding: 25000,
          overdueAmount: 8000,
          billsCount: 85,
          overdueBillsCount: 25,
          averageDaysOverdue: 15,
          bills: [
            { billNumber: 'BILL-20240101-001', patient: '张三', amount: 1500, daysOverdue: 30, dueDate: '2024-01-15' },
            { billNumber: 'BILL-20240102-002', patient: '李四', amount: 2000, daysOverdue: 25, dueDate: '2024-01-20' },
            { billNumber: 'BILL-20240103-003', patient: '王五', amount: 1200, daysOverdue: 20, dueDate: '2024-01-25' },
          ],
        },
        trends: {
          revenueGrowth: 12.5,
          transactionGrowth: 8.3,
          collectionRateChange: -2.1,
        },
      };

      setReportData(mockData);
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('获取报告数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReportData();
  }, [reportType, dateRange]);

  // Check permissions after all hooks
  if (!hasPermission('canAccessAdvancedReports')) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconAlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">权限不足</h3>
          <p className="text-muted-foreground">
            您没有权限查看高级财务报告
          </p>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const exportReport = async () => {
    try {
      const exportData = {
        reportData,
        reportType,
        dateRange,
        generatedAt: new Date().toISOString(),
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `financial-report-${reportType}-${dateRange.startDate}-to-${dateRange.endDate}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('报告导出成功');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('导出报告失败');
    }
  };

  const getTrendIcon = (value: number) => {
    return value >= 0 ? IconTrendingUp : IconTrendingDown;
  };

  const getTrendColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">综合财务报告</h2>
          <Button disabled>
            <IconRefresh className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">加载中...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">--</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!reportData) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">无法加载报告数据</p>
        <Button onClick={fetchReportData} className="mt-4">
          <IconRefresh className="mr-2 h-4 w-4" />
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <IconChartBar className="h-6 w-6" />
            综合财务报告
          </h2>
          <p className="text-muted-foreground">
            {dateRange.startDate} 至 {dateRange.endDate}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">日报</SelectItem>
              <SelectItem value="weekly">周报</SelectItem>
              <SelectItem value="monthly">月报</SelectItem>
              <SelectItem value="yearly">年报</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex items-center gap-2">
            <Input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-auto"
            />
            <span>至</span>
            <Input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-auto"
            />
          </div>
          <Button onClick={fetchReportData} size="sm">
            <IconRefresh className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button onClick={exportReport} size="sm" variant="outline">
            <IconDownload className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <IconCash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalRevenue)}</div>
            <div className="flex items-center gap-1 text-xs">
              {(() => {
                const TrendIcon = getTrendIcon(reportData.trends.revenueGrowth);
                return (
                  <>
                    <TrendIcon className={`h-3 w-3 ${getTrendColor(reportData.trends.revenueGrowth)}`} />
                    <span className={getTrendColor(reportData.trends.revenueGrowth)}>
                      {formatPercentage(Math.abs(reportData.trends.revenueGrowth))}
                    </span>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">账单总数</CardTitle>
            <IconReceipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.summary.totalBills}</div>
            <div className="flex items-center gap-1 text-xs">
              {(() => {
                const TrendIcon = getTrendIcon(reportData.trends.transactionGrowth);
                return (
                  <>
                    <TrendIcon className={`h-3 w-3 ${getTrendColor(reportData.trends.transactionGrowth)}`} />
                    <span className={getTrendColor(reportData.trends.transactionGrowth)}>
                      {formatPercentage(Math.abs(reportData.trends.transactionGrowth))}
                    </span>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">未收款</CardTitle>
            <IconAlertCircle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalOutstanding)}</div>
            <p className="text-xs text-muted-foreground">
              逾期: {formatCurrency(reportData.outstanding.overdueAmount)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">回收率</CardTitle>
            <IconTarget className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(reportData.summary.collectionRate)}</div>
            <div className="flex items-center gap-1 text-xs">
              {(() => {
                const TrendIcon = getTrendIcon(reportData.trends.collectionRateChange);
                return (
                  <>
                    <TrendIcon className={`h-3 w-3 ${getTrendColor(reportData.trends.collectionRateChange)}`} />
                    <span className={getTrendColor(reportData.trends.collectionRateChange)}>
                      {formatPercentage(Math.abs(reportData.trends.collectionRateChange))}
                    </span>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均交易</CardTitle>
            <IconActivity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.averageTransactionValue)}</div>
            <p className="text-xs text-muted-foreground">
              每笔交易
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports Tabs */}
      <Tabs defaultValue="revenue" className="space-y-4">
        <TabsList>
          <TabsTrigger value="revenue">收入分析</TabsTrigger>
          <TabsTrigger value="payments">支付方式</TabsTrigger>
          <TabsTrigger value="treatments">治疗收入</TabsTrigger>
          <TabsTrigger value="outstanding">未付款分析</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>收入趋势</CardTitle>
              <CardDescription>按时间段的收入变化</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.revenueByPeriod.map((period, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <div className="font-medium">{period.period}</div>
                      <div className="text-sm text-muted-foreground">
                        {period.transactions} 笔交易
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">{formatCurrency(period.revenue)}</div>
                      <div className="flex items-center gap-1 text-sm">
                        {(() => {
                          const TrendIcon = getTrendIcon(period.growth);
                          return (
                            <>
                              <TrendIcon className={`h-3 w-3 ${getTrendColor(period.growth)}`} />
                              <span className={getTrendColor(period.growth)}>
                                {formatPercentage(Math.abs(period.growth))}
                              </span>
                            </>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>支付方式分析</CardTitle>
              <CardDescription>各种支付方式的使用情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.paymentMethods.map((method, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {method.method === 'cash' && <IconCash className="h-4 w-4" />}
                        {method.method === 'card' && <IconCreditCard className="h-4 w-4" />}
                        <span className="font-medium capitalize">{method.method}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{formatCurrency(method.amount)}</div>
                        <div className="text-sm text-muted-foreground">{method.count} 笔</div>
                      </div>
                    </div>
                    <Progress value={method.percentage} className="h-2" />
                    <div className="text-xs text-muted-foreground text-right">
                      {formatPercentage(method.percentage)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="treatments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>治疗项目收入</CardTitle>
              <CardDescription>各治疗项目的收入贡献</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>治疗项目</TableHead>
                    <TableHead className="text-right">收入</TableHead>
                    <TableHead className="text-right">次数</TableHead>
                    <TableHead className="text-right">平均价格</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportData.treatmentRevenue.map((treatment, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{treatment.treatment}</TableCell>
                      <TableCell className="text-right">{formatCurrency(treatment.revenue)}</TableCell>
                      <TableCell className="text-right">{treatment.count}</TableCell>
                      <TableCell className="text-right">{formatCurrency(treatment.averagePrice)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="outstanding" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>未付款分析</CardTitle>
              <CardDescription>逾期账单和收款情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 mb-6">
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">总未付款</div>
                  <div className="text-2xl font-bold">{formatCurrency(reportData.outstanding.totalOutstanding)}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">逾期金额</div>
                  <div className="text-2xl font-bold text-red-600">{formatCurrency(reportData.outstanding.overdueAmount)}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">未付账单数</div>
                  <div className="text-2xl font-bold">{reportData.outstanding.billsCount}</div>
                </div>
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">逾期账单数</div>
                  <div className="text-2xl font-bold text-red-600">{reportData.outstanding.overdueBillsCount}</div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-4">逾期账单明细</h4>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>账单编号</TableHead>
                      <TableHead>患者</TableHead>
                      <TableHead className="text-right">金额</TableHead>
                      <TableHead className="text-right">逾期天数</TableHead>
                      <TableHead>到期日期</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reportData.outstanding.bills.map((bill, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{bill.billNumber}</TableCell>
                        <TableCell>{bill.patient}</TableCell>
                        <TableCell className="text-right">{formatCurrency(bill.amount)}</TableCell>
                        <TableCell className="text-right">
                          <Badge variant="destructive">{bill.daysOverdue} 天</Badge>
                        </TableCell>
                        <TableCell>{bill.dueDate}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
