try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="1d1bbe18-d3b6-4879-83e8-aaed69d55287",e._sentryDebugIdIdentifier="sentry-dbid-1d1bbe18-d3b6-4879-83e8-aaed69d55287")}catch(e){}(()=>{var e={};e.id=1833,e.ids=[1833],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{"use strict";e.exports=require("module")},10531:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(24443);r(60222);var a=r(16586),n=r(29693),i=r(72595);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...n}){let d=r?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12110:(e,t,r)=>{Promise.resolve().then(r.bind(r,34774))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},31548:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>p,generateImageMetadata:()=>u,generateMetadata:()=>c,generateViewport:()=>m});var a=r(63033),n=r(91611),i=r(19761),o=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\admin\\page.tsx","default");let d={...a},l="workUnitAsyncStorage"in d?d.workUnitAsyncStorage:"requestAsyncStorage"in d?d.requestAsyncStorage:void 0;s="function"==typeof o?new Proxy(o,{apply:(e,t,r)=>{let s,a,n;try{let e=l?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return i.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/admin",componentType:"Page",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}}):o;let c=void 0,u=void 0,m=void 0,p=s},32218:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,X9:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>u});var s=r(24443);r(60222);var a=r(72595);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},32300:(e,t,r)=>{"use strict";r.d(t,{t:()=>a});let s={nav:{dashboard:"仪表板",appointments:"预约管理",patients:"患者管理",treatments:"治疗项目",admin:"系统管理",account:"账户",profile:"个人资料",login:"登录",overview:"概览"},dashboard:{title:"诊所控制台 \uD83C\uDFE5",subtitle:"欢迎使用您的诊所管理系统",metrics:{todayAppointments:"今日预约",recentPatients:"近期患者",totalPatients:"患者总数",activetreatments:"可用治疗",scheduledForToday:"今日安排",appointmentsScheduledForToday:"今日安排的预约",newPatientsThisWeek:"本周新患者",patientsRegisteredInLast7Days:"过去7天注册的患者",totalRegisteredPatients:"注册患者总数",completePatientDatabase:"完整患者数据库",treatmentOptionsAvailable:"可用治疗选项",fullServiceCatalog:"完整服务目录",active:"活跃",last7Days:"过去7天",allTime:"全部时间",available:"可用"},errors:{loadingDashboard:"加载仪表板时出错",failedToLoadMetrics:"无法加载仪表板数据"}},appointments:{title:"预约管理",subtitle:"管理患者预约和排程",newAppointment:"新建预约",editAppointment:"编辑预约",appointmentDetails:"预约详情",appointmentsCount:"个预约",loadingAppointments:"加载预约中...",noAppointments:"暂无预约",filters:{all:"全部",today:"今天",thisWeek:"本周",thisMonth:"本月",status:"状态",dateRange:"日期范围"},status:{scheduled:"已安排",confirmed:"已确认",inProgress:"进行中",completed:"已完成",cancelled:"已取消",noShow:"未到场"},form:{patient:"患者",selectPatient:"选择患者",treatment:"治疗项目",selectTreatment:"选择治疗项目",date:"日期",time:"时间",notes:"备注",notesPlaceholder:"预约备注（可选）",status:"状态"}},patients:{title:"患者管理",subtitle:"管理患者信息和病历",newPatient:"新建患者",editPatient:"编辑患者",patientDetails:"患者详情",patientsCount:"位患者",loadingPatients:"加载患者中...",noPatients:"暂无患者",searchPlaceholder:"按姓名、电话或邮箱搜索患者",form:{fullName:"姓名",fullNamePlaceholder:"请输入患者姓名",phone:"电话",phonePlaceholder:"请输入电话号码",email:"邮箱",emailPlaceholder:"请输入邮箱地址（可选）",medicalNotes:"病历备注",medicalNotesPlaceholder:"请输入病历备注（可选）"}},treatments:{title:"治疗项目",subtitle:"管理诊所治疗服务",newTreatment:"新建治疗",editTreatment:"编辑治疗",treatmentDetails:"治疗详情",treatmentsCount:"个治疗项目",loadingTreatments:"加载治疗项目中...",noTreatments:"暂无治疗项目",form:{name:"治疗名称",namePlaceholder:"请输入治疗名称",description:"治疗描述",descriptionPlaceholder:"请输入治疗描述",duration:"治疗时长",durationPlaceholder:"请输入治疗时长（分钟）",price:"价格",pricePlaceholder:"请输入价格"}},admin:{title:"系统管理",subtitle:"管理用户权限和系统设置",userManagement:"用户管理",roleManagement:"角色管理",systemSettings:"系统设置",users:"用户",roles:{admin:"管理员",doctor:"医生",frontDesk:"前台"}},common:{actions:{save:"保存",cancel:"取消",edit:"编辑",delete:"删除",view:"查看",search:"搜索",filter:"筛选",reset:"重置",submit:"提交",close:"关闭",confirm:"确认",back:"返回",next:"下一步",previous:"上一步",add:"添加",remove:"移除",update:"更新",create:"创建"},status:{loading:"加载中...",success:"成功",error:"错误",warning:"警告",info:"信息",pending:"待处理",active:"活跃",inactive:"非活跃",enabled:"已启用",disabled:"已禁用"},time:{today:"今天",yesterday:"昨天",tomorrow:"明天",thisWeek:"本周",lastWeek:"上周",nextWeek:"下周",thisMonth:"本月",lastMonth:"上月",nextMonth:"下月",thisYear:"今年",lastYear:"去年",nextYear:"明年"},confirmDialog:{title:"确认操作",deleteTitle:"确认删除",deleteMessage:"您确定要删除这个项目吗？此操作无法撤销。",cancelTitle:"确认取消",cancelMessage:"您确定要取消吗？未保存的更改将丢失。",saveTitle:"确认保存",saveMessage:"您确定要保存这些更改吗？"}},validation:{required:"此字段为必填项",email:"请输入有效的邮箱地址",phone:"请输入有效的电话号码",minLength:"至少需要 {min} 个字符",maxLength:"最多允许 {max} 个字符",number:"请输入有效的数字",positive:"请输入正数",date:"请选择有效的日期",time:"请选择有效的时间"},errors:{general:"发生了未知错误，请稍后重试",network:"网络连接错误，请检查您的网络连接",unauthorized:"您没有权限执行此操作",notFound:"请求的资源未找到",serverError:"服务器错误，请稍后重试",validationError:"输入数据验证失败",loadFailed:"加载数据失败",saveFailed:"保存数据失败",deleteFailed:"删除数据失败",updateFailed:"更新数据失败",createFailed:"创建数据失败"},success:{saved:"保存成功",deleted:"删除成功",updated:"更新成功",created:"创建成功",sent:"发送成功",uploaded:"上传成功",downloaded:"下载成功"}};function a(e,t){let r=e.split("."),a=s;for(let t of r)if(!a||"object"!=typeof a||!(t in a))return console.warn(`Translation key not found: ${e}`),e;else a=a[t];return"string"!=typeof a?(console.warn(`Translation value is not a string: ${e}`),e):t?a.replace(/\{(\w+)\}/g,(e,r)=>t[r]?.toString()||e):a}},33873:e=>{"use strict";e.exports=require("path")},34774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(24443),a=r(60222),n=r(79153),i=r(74482),o=r(34769),d=r(72007),l=r(33284),c=r(10531),u=r(32218),m=r(23032),p=r(46244),h=(0,p.A)("outline","shield","IconShield",[["path",{d:"M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3",key:"svg-0"}]]),x=r(24658),f=(0,p.A)("outline","user-check","IconUserCheck",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h4",key:"svg-1"}],["path",{d:"M15 19l2 2l4 -4",key:"svg-2"}]]),y=r(95826),g=r(31232),b=r(85001),v=r(32300);function j(){let{userId:e,isLoaded:t}=(0,n.d)(),{user:r,refreshUser:p}=(0,i.It)(),[j,k]=(0,a.useState)([]),[P,q]=(0,a.useState)(!0),[N,w]=(0,a.useState)(null),[T,A]=(0,a.useState)(null);t&&!e&&(0,o.redirect)("/auth/sign-in");let C=async()=>{if(r)try{q(!0);let e=(0,y.o)(r),t=await e.getUsers({limit:100});k(t.docs||[]),w(null)}catch(e){console.error("Failed to fetch users:",e),w("Failed to load users. Please try again later.")}finally{q(!1)}},R=async(e,t)=>{if(r)try{A(e);let s=(0,y.o)(r);await s.updateUser(e,{role:t}),k(j.map(r=>r.id===e?{...r,role:t}:r)),b.toast.success("User role updated successfully"),r.payloadUserId===e&&await p()}catch(e){console.error("Failed to update user role:",e),b.toast.error("Failed to update user role")}finally{A(null)}};return!t||P?(0,s.jsx)(d.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"加载管理面板中..."})]})})}):(0,s.jsx)(i.Y0,{roles:"admin",fallback:(0,s.jsx)(d.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(h,{className:"size-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"访问被拒绝"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"您需要管理员权限才能访问此页面。"})]})})}),"data-sentry-element":"RoleGate","data-sentry-component":"AdminPage","data-sentry-source-file":"page.tsx",children:(0,s.jsx)(d.A,{"data-sentry-element":"PageContainer","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-2xl font-bold tracking-tight flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"size-6","data-sentry-element":"IconUsers","data-sentry-source-file":"page.tsx"}),(0,v.t)("admin.title")]}),(0,s.jsx)("p",{className:"text-muted-foreground",children:(0,v.t)("admin.subtitle")})]})}),(0,s.jsxs)(u.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(u.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"page.tsx",children:[(0,s.jsxs)(u.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"page.tsx",children:[(0,s.jsx)(f,{className:"size-5","data-sentry-element":"IconUserCheck","data-sentry-source-file":"page.tsx"}),"当前用户"]}),(0,s.jsx)(u.BT,{"data-sentry-element":"CardDescription","data-sentry-source-file":"page.tsx",children:"您当前的角色和权限"})]}),(0,s.jsx)(u.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"page.tsx",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:r?.email}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[r?.firstName," ",r?.lastName]})]}),(0,s.jsx)(c.E,{className:(0,g.Pj)(r?.role||"front-desk"),"data-sentry-element":"Badge","data-sentry-source-file":"page.tsx",children:(0,g.cb)(r?.role||"front-desk")})]})})]}),N?(0,s.jsx)(u.Zp,{children:(0,s.jsx)(u.Wu,{className:"pt-6",children:(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:N}),(0,s.jsx)(l.$,{onClick:C,children:"Try Again"})]})})}):(0,s.jsxs)(u.Zp,{children:[(0,s.jsxs)(u.aR,{children:[(0,s.jsxs)(u.ZB,{children:["All Users (",j.length,")"]}),(0,s.jsx)(u.BT,{children:"Manage roles for all system users"})]}),(0,s.jsx)(u.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[j.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.email}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.firstName," ",e.lastName]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Joined: ",new Date(e.createdAt).toLocaleDateString()]})]})})}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(c.E,{className:(0,g.Pj)(e.role),children:(0,g.cb)(e.role)}),(0,s.jsxs)(m.l6,{value:e.role,onValueChange:t=>R(e.id,t),disabled:T===e.id,children:[(0,s.jsx)(m.bq,{className:"w-32",children:(0,s.jsx)(m.yv,{})}),(0,s.jsxs)(m.gC,{children:[(0,s.jsx)(m.eb,{value:"admin",children:"Administrator"}),(0,s.jsx)(m.eb,{value:"doctor",children:"Doctor"}),(0,s.jsx)(m.eb,{value:"front-desk",children:"Front Desk"})]})]})]})]},e.id)),0===j.length&&(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.A,{className:"size-12 mx-auto text-muted-foreground mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"No users found"})]})]})})]})]})})})}},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},48161:e=>{"use strict";e.exports=require("node:os")},51430:(e,t,r)=>{Promise.resolve().then(r.bind(r,31548))},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67529:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,ScrollArea:()=>i});var s=r(24443);r(60222);var a=r(54889),n=r(72595);function i({className:e,children:t,...r}){return(0,s.jsxs)(a.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",e),...r,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,s.jsx)(a.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,s.jsx)(o,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,s.jsx)(a.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function o({className:e,orientation:t="vertical",...r}){return(0,s.jsx)(a.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,s.jsx)(a.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},68829:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f22efd92a3b59d43d3d12fe480e87910640e1db9e":()=>a.y,"7f7b45347fd50452ee6e2850ded1018991a7b086f0":()=>s.at,"7f909588461cb83e855875f4939d6f26e4ae81b49e":()=>s.ot,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261":()=>s.ai});var s=r(27235),a=r(41372)},72007:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(24443);r(60222);var a=r(67529);function n({children:e,scrollable:t=!0}){return(0,s.jsx)(s.Fragment,{children:t?(0,s.jsx)(a.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79153:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>l,d:()=>c});var s=r(22371),a=r(95505),n=r(26393),i=r(60222),o=r.n(i);let d=o().createContext(null);function l({authPromise:e,children:t}){return o().createElement(d.Provider,{value:e},t)}function c(){let e=(0,n.useRouter)(),t=o().useContext(d),r=t;return(t&&"then"in t&&(r=o().use(t)),e)?(0,s.As)():(0,a.hP)(r)}},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},94735:e=>{"use strict";e.exports=require("events")},95334:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(29703),a=r(85544),n=r(62458),i=r(77821),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["dashboard",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,31548)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\admin\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/admin/page",pathname:"/dashboard/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},95826:(e,t,r)=>{"use strict";r.d(t,{o:()=>a});class s{constructor(e){this.user=e}async makeRequest(e,t={}){let r,s,{method:a="GET",body:n,params:i}=t;if(r=`http://localhost:8002/api${e}`,i){let e=new URLSearchParams;Object.entries(i).forEach(([t,r])=>{null!=r&&e.append(t,r.toString())}),e.toString()&&(r+=`?${e.toString()}`)}s={method:a,headers:{"Content-Type":"application/json","X-Clerk-User-Id":this.user.clerkId,"X-User-Email":this.user.email}},n&&"GET"!==a&&(s.body=JSON.stringify(n));try{let e=await fetch(r,s);if(!e.ok){let t=await e.text();throw Error(`API request failed: ${e.status} ${e.statusText} - ${t}`)}return await e.json()}catch(t){throw console.error(`API request failed for ${e}:`,t),t}}async getAppointments(e){return this.makeRequest("/appointments",{params:{depth:"2",...e}})}async getAppointment(e){return this.makeRequest(`/appointments/${e}`,{params:{depth:"2"}})}async createAppointment(e){return this.makeRequest("/appointments",{method:"POST",body:e})}async updateAppointment(e,t){return this.makeRequest(`/appointments/${e}`,{method:"PATCH",body:t})}async deleteAppointment(e){return this.makeRequest(`/appointments/${e}`,{method:"DELETE"})}async getPatients(e){let t={depth:"1",...e};return e?.search&&(t["where[or][0][fullName][contains]"]=e.search,t["where[or][1][phone][contains]"]=e.search,t["where[or][2][email][contains]"]=e.search,delete t.search),this.makeRequest("/patients",{params:t})}async getPatient(e){return this.makeRequest(`/patients/${e}`,{params:{depth:"1"}})}async createPatient(e){return this.makeRequest("/patients",{method:"POST",body:e})}async updatePatient(e,t){return this.makeRequest(`/patients/${e}`,{method:"PATCH",body:t})}async deletePatient(e){return this.makeRequest(`/patients/${e}`,{method:"DELETE"})}async getTreatments(e){return this.makeRequest("/treatments",{params:{depth:"1",...e}})}async getTreatment(e){return this.makeRequest(`/treatments/${e}`)}async createTreatment(e){return this.makeRequest("/treatments",{method:"POST",body:e})}async updateTreatment(e,t){return this.makeRequest(`/treatments/${e}`,{method:"PATCH",body:t})}async deleteTreatment(e){return this.makeRequest(`/treatments/${e}`,{method:"DELETE"})}async getUsers(e){return this.makeRequest("/users",{params:{depth:"1",...e}})}async updateUser(e,t){return this.makeRequest(`/users/${e}`,{method:"PATCH",body:t})}async syncCurrentUser(){return this.makeRequest("/users/sync",{method:"POST",body:{clerkId:this.user.clerkId,email:this.user.email,firstName:this.user.firstName,lastName:this.user.lastName}})}async syncUser(e){try{let t=await this.makeRequest("/users",{params:{where:JSON.stringify({clerkId:{equals:e.clerkId}}),limit:1}});if(!t.docs||!(t.docs.length>0))return await this.makeRequest("/users",{method:"POST",body:{email:e.email,clerkId:e.clerkId,firstName:e.firstName,lastName:e.lastName,role:"front-desk",lastLogin:new Date().toISOString()}});{let r=t.docs[0];return await this.makeRequest(`/users/${r.id}`,{method:"PATCH",body:{email:e.email,firstName:e.firstName,lastName:e.lastName,lastLogin:new Date().toISOString()}})}}catch(t){return console.error("Error syncing user with Payload:",t),{id:"temp-id",email:e.email,clerkId:e.clerkId,role:"front-desk",firstName:e.firstName,lastName:e.lastName}}}async getPatientInteractions(e){return this.makeRequest("/patient-interactions",{params:{depth:"2",...e}})}async getPatientInteraction(e){return this.makeRequest(`/patient-interactions/${e}`,{params:{depth:"2"}})}async createPatientInteraction(e){return this.makeRequest("/patient-interactions",{method:"POST",body:e})}async updatePatientInteraction(e,t){return this.makeRequest(`/patient-interactions/${e}`,{method:"PATCH",body:t})}async deletePatientInteraction(e){return this.makeRequest(`/patient-interactions/${e}`,{method:"DELETE"})}async getPatientTasks(e){return this.makeRequest("/patient-tasks",{params:{depth:"2",...e}})}async getPatientTask(e){return this.makeRequest(`/patient-tasks/${e}`,{params:{depth:"2"}})}async createPatientTask(e){return this.makeRequest("/patient-tasks",{method:"POST",body:e})}async updatePatientTask(e,t){return this.makeRequest(`/patient-tasks/${e}`,{method:"PATCH",body:t})}async deletePatientTask(e){return this.makeRequest(`/patient-tasks/${e}`,{method:"DELETE"})}async getPatientInteractionsByPatient(e,t){return this.makeRequest(`/patients/${e}/interactions`,{params:{depth:"2",...t}})}async getPatientTasksByPatient(e,t){return this.makeRequest(`/patients/${e}/tasks`,{params:{depth:"2",...t}})}async getPatientTimeline(e,t){return this.makeRequest(`/patients/${e}/timeline`,{params:{depth:"2",...t}})}}function a(e){return new s(e)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,8774,7494],()=>r(95334));module.exports=s})();
//# sourceMappingURL=page.js.map