{"version": 3, "file": "../app/dashboard/page.js", "mappings": "ubAAA,sGCAA,uCAA2K,CAE3K,uCAAiK,wBCFjK,wICIA,IAAMA,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACbC,CAAS,SACTP,CAAO,CACPQ,UAAU,EAAK,CACf,GAAGC,EAGJ,EACC,IAAMC,EAAOF,EAAUG,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,QAAQL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAChB,EAAc,SACzDG,CACF,GAAIO,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,qGCAA,mECAA,0GCAA,sMCSe,SAASC,IACtB,GAAM,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAmB,CACvDC,kBAAmB,EACnBC,eAAgB,EAChBC,cAAe,EACfC,iBAAkB,CACpB,GACM,CAACC,EAASC,EAAW,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACO,EAAOC,EAAS,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,aAiBlD,EACS,OADI,CACJ,EAACS,MAAAA,CAAItB,UAAU,gEACjB,6BAAa,CAACuB,GAAG,CAAC,CAACC,EAAGC,IAAM,UAACC,EAAAA,EAAIA,CAAAA,CAAS1B,UAAU,2BACjD,WAAC2B,EAAAA,EAAUA,CAAAA,WACT,WAACC,EAAAA,EAAeA,CAAAA,CAAC5B,UAAU,oCACzB,UAACsB,MAAAA,CAAItB,UAAU,2CACf,UAACsB,MAAAA,CAAItB,UAAU,+CAEjB,UAAC6B,EAAAA,EAASA,CAAAA,CAAC7B,UAAU,sEACnB,UAACsB,MAAAA,CAAItB,UAAU,kDAPiByB,MAa1CL,EACK,KADE,GACF,EAACE,MAAAA,CAAItB,UAAU,gEAClB,UAAC0B,EAAAA,EAAIA,CAAAA,CAAC1B,UAAU,yBACd,WAAC2B,EAAAA,EAAUA,CAAAA,WACT,UAACE,EAAAA,EAASA,CAAAA,CAAC7B,UAAU,4BAAoB8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAC3C,UAACF,EAAAA,EAAeA,CAAAA,UAAER,WAKrB,WAACE,MAAAA,CAAItB,UAAU,uDAAuDQ,wBAAsB,mBAAmBC,0BAAwB,kCAE1I,WAACiB,EAAAA,EAAIA,CAAAA,CAAC1B,UAAU,kBAAkBO,sBAAoB,OAAOE,0BAAwB,kCACnF,WAACkB,EAAAA,EAAUA,CAAAA,CAACpB,sBAAoB,aAAaE,0BAAwB,kCACnE,WAACmB,EAAAA,EAAeA,CAAAA,CAAC5B,UAAU,0BAA0BO,sBAAoB,kBAAkBE,0BAAwB,kCACjH,UAACsB,EAAAA,CAAYA,CAAAA,CAAC/B,UAAU,SAASO,sBAAoB,eAAeE,0BAAwB,0BAC3FqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0CAEL,UAACD,EAAAA,EAASA,CAAAA,CAAC7B,UAAU,6DAA6DO,sBAAoB,YAAYE,0BAAwB,iCACvIE,EAAQG,iBAAiB,GAE5B,UAACkB,EAAAA,EAAUA,CAAAA,CAACzB,sBAAoB,aAAaE,0BAAwB,iCACnE,WAACV,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUc,sBAAoB,QAAQE,0BAAwB,kCAC3E,UAACwB,EAAAA,CAAcA,CAAAA,CAAC1B,sBAAoB,iBAAiBE,0BAAwB,0BAC5EqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,oCAIT,WAACI,EAAAA,EAAUA,CAAAA,CAAClC,UAAU,uCAAuCO,sBAAoB,aAAaE,0BAAwB,kCACpH,WAACa,MAAAA,CAAItB,UAAU,gDACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAAuC,IAAC,UAACC,EAAAA,CAAYA,CAAAA,CAAC/B,UAAU,SAASO,sBAAoB,eAAeE,0BAAwB,6BAEzI,UAACa,MAAAA,CAAItB,UAAU,iCACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,2DAMT,WAACJ,EAAAA,EAAIA,CAAAA,CAAC1B,UAAU,kBAAkBO,sBAAoB,OAAOE,0BAAwB,kCACnF,WAACkB,EAAAA,EAAUA,CAAAA,CAACpB,sBAAoB,aAAaE,0BAAwB,kCACnE,WAACmB,EAAAA,EAAeA,CAAAA,CAAC5B,UAAU,0BAA0BO,sBAAoB,kBAAkBE,0BAAwB,kCACjH,UAAC0B,EAAAA,CAASA,CAAAA,CAACnC,UAAU,SAASO,sBAAoB,YAAYE,0BAAwB,0BACrFqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAEL,UAACD,EAAAA,EAASA,CAAAA,CAAC7B,UAAU,6DAA6DO,sBAAoB,YAAYE,0BAAwB,iCACvIE,EAAQI,cAAc,GAEzB,UAACiB,EAAAA,EAAUA,CAAAA,CAACzB,sBAAoB,aAAaE,0BAAwB,iCACnE,WAACV,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUc,sBAAoB,QAAQE,0BAAwB,kCAC3E,UAACwB,EAAAA,CAAcA,CAAAA,CAAC1B,sBAAoB,iBAAiBE,0BAAwB,0BAC5EqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAIT,WAACI,EAAAA,EAAUA,CAAAA,CAAClC,UAAU,uCAAuCO,sBAAoB,aAAaE,0BAAwB,kCACpH,WAACa,MAAAA,CAAItB,UAAU,gDACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yCAAyC,IAAC,UAACK,EAAAA,CAASA,CAAAA,CAACnC,UAAU,SAASO,sBAAoB,YAAYE,0BAAwB,6BAErI,UAACa,MAAAA,CAAItB,UAAU,iCACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,2DAMT,WAACJ,EAAAA,EAAIA,CAAAA,CAAC1B,UAAU,kBAAkBO,sBAAoB,OAAOE,0BAAwB,kCACnF,WAACkB,EAAAA,EAAUA,CAAAA,CAACpB,sBAAoB,aAAaE,0BAAwB,kCACnE,WAACmB,EAAAA,EAAeA,CAAAA,CAAC5B,UAAU,0BAA0BO,sBAAoB,kBAAkBE,0BAAwB,kCACjH,UAAC0B,EAAAA,CAASA,CAAAA,CAACnC,UAAU,SAASO,sBAAoB,YAAYE,0BAAwB,0BACrFqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,sCAEL,UAACD,EAAAA,EAASA,CAAAA,CAAC7B,UAAU,6DAA6DO,sBAAoB,YAAYE,0BAAwB,iCACvIE,EAAQK,aAAa,GAExB,UAACgB,EAAAA,EAAUA,CAAAA,CAACzB,sBAAoB,aAAaE,0BAAwB,iCACnE,WAACV,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUc,sBAAoB,QAAQE,0BAAwB,kCAC3E,UAACwB,EAAAA,CAAcA,CAAAA,CAAC1B,sBAAoB,iBAAiBE,0BAAwB,0BAC5EqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qCAIT,WAACI,EAAAA,EAAUA,CAAAA,CAAClC,UAAU,uCAAuCO,sBAAoB,aAAaE,0BAAwB,kCACpH,WAACa,MAAAA,CAAItB,UAAU,gDACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,6CAA6C,IAAC,UAACK,EAAAA,CAASA,CAAAA,CAACnC,UAAU,SAASO,sBAAoB,YAAYE,0BAAwB,6BAEzI,UAACa,MAAAA,CAAItB,UAAU,iCACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qDAMT,WAACJ,EAAAA,EAAIA,CAAAA,CAAC1B,UAAU,kBAAkBO,sBAAoB,OAAOE,0BAAwB,kCACnF,WAACkB,EAAAA,EAAUA,CAAAA,CAACpB,sBAAoB,aAAaE,0BAAwB,kCACnE,WAACmB,EAAAA,EAAeA,CAAAA,CAAC5B,UAAU,0BAA0BO,sBAAoB,kBAAkBE,0BAAwB,kCACjH,UAAC2B,EAAAA,CAAeA,CAAAA,CAACpC,UAAU,SAASO,sBAAoB,kBAAkBE,0BAAwB,0BACjGqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yCAEL,UAACD,EAAAA,EAASA,CAAAA,CAAC7B,UAAU,6DAA6DO,sBAAoB,YAAYE,0BAAwB,iCACvIE,EAAQM,gBAAgB,GAE3B,UAACe,EAAAA,EAAUA,CAAAA,CAACzB,sBAAoB,aAAaE,0BAAwB,iCACnE,WAACV,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUc,sBAAoB,QAAQE,0BAAwB,kCAC3E,UAACwB,EAAAA,CAAcA,CAAAA,CAAC1B,sBAAoB,iBAAiBE,0BAAwB,0BAC5EqB,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uCAIT,WAACI,EAAAA,EAAUA,CAAAA,CAAClC,UAAU,uCAAuCO,sBAAoB,aAAaE,0BAAwB,kCACpH,WAACa,MAAAA,CAAItB,UAAU,gDACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,+CAA+C,IAAC,UAACM,EAAAA,CAAeA,CAAAA,CAACpC,UAAU,SAASO,sBAAoB,kBAAkBE,0BAAwB,6BAEvJ,UAACa,MAAAA,CAAItB,UAAU,iCACZ8B,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,kDAKf,0BCxKA,gDCAA,oDCAA,kDCAA,gDCAA,wGCAA,qLCEA,SAASJ,EAAK,WACZ1B,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,OAAOL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,oFAAqFN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,OAAOC,0BAAwB,YAC9M,CACA,SAASkB,EAAW,CAClB3B,WAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6JAA8JN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACpS,CACA,SAASoB,EAAU,WACjB7B,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,aAAaL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,6BAA8BN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,YAAYC,0BAAwB,YAClK,CACA,SAASmB,EAAgB,WACvB5B,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,mBAAmBL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,kBAAkBC,0BAAwB,YACjL,CACA,SAASuB,EAAW,WAClBhC,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,iEAAkEN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACxM,CACA,SAAS4B,EAAY,WACnBrC,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,eAAeL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,OAAQN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,cAAcC,0BAAwB,YAChJ,CACA,SAASyB,EAAW,WAClBlC,CAAS,CACT,GAAGE,EACyB,EAC5B,MAAO,UAACoB,MAAAA,CAAIjB,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,0CAA2CN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,aAAaC,0BAAwB,YACjL,0BC3CA,kDCAA,gECAA,kEEmBI,sBAAsB,gLDdX,eAAe6B,IAC5B,GAAM,CACJC,CAF0BD,GAAAA,IAEpB,CACP,CAAG,MAAME,CAAAA,EAAAA,EAAAA,CAAAA,CAAAA,EAAAA,QACLD,EAGEE,CAAAA,CAHH,CAGGA,CAHM,CAGNA,GAAAA,CAACC,CAAAA,EAAAA,CAAAA,CAAAA,CAAcnC,qBAAoB,iBAAgBC,uBAAsB,aAAYC,yBAAwB,YAChH,SAAAkC,CAAAA,EAAAA,EAAAA,IAAAA,CAACrB,CAAAA,KAAAA,CAAAA,CAAItB,SAAU,4CACb2C,CAAAA,EAAAA,EAAAA,IAAAA,CAACrB,CAAAA,KAAAA,CAAAA,CAAItB,SAAU,yDACbyC,CAAAA,EAAAA,EAAAA,GAAAA,CAACG,CAAAA,IAAAA,CAAAA,CAAG5C,SAAU,8CACX8B,CAAAA,EAAAA,EAAAA,CAAAA,CAAE,sBAELW,CAAAA,EAAAA,EAAAA,GAAAA,CAACI,CAAAA,GAAAA,CAAAA,CAAE7C,SAAU,kCACV8B,CAAAA,EAAAA,EAAAA,CAAAA,CAAE,4BAIPW,CAAAA,EAAAA,EAAAA,GAAAA,CAAC/B,CAAAA,EAAAA,OAAAA,CAAAA,CAAiBH,qBAAoB,oBAAmBE,yBAAwB,mBAb9EqC,CAAAA,EAAAA,EAAAA,QAAAA,CAAS,iBAgBpB,CClBA,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OASN,EATe,IASc,KAAK,CAPZC,EAO8B,CAClD,KAAK,CAAE,CADa,EACM,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EAK8B,CACzD,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,OAAI,EAC7D,EAAU,GAAmB,EAAtB,KACf,CAAO,MAAQ,CAAC,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,YAAY,CAC5B,aAAa,CAAE,MAAM,mBACrB,gBACA,CADiB,SAEjB,CACR,CAAO,CAFc,CAEZ,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CACF,CAF0B,CAOxB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAOF,EAEE,EAA2B,CAlBN,IASL,iBASQ,IChF9B,0oBCAA,sDCAA,wDCAA,qDCAA,sECAA,oDCAA,kECAA,yDCAA,uDCAA,uMCKA,SAASC,EAAW,WAClBhD,CAAS,UACTiD,CAAQ,CACR,GAAG/C,EACmD,EACtD,MAAO,WAACgD,EAAAA,EAAwB,EAAC7C,YAAU,cAAcL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,WAAYN,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,2BAA2BC,wBAAsB,aAAaC,0BAAwB,4BAChN,UAACyC,EAAAA,EAA4B,EAAC7C,YAAU,uBAAuBL,UAAU,qJAAqJO,sBAAoB,+BAA+BE,0BAAwB,2BACtSwC,IAEH,UAACE,EAAAA,CAAU5C,sBAAoB,YAAYE,0BAAwB,oBACnE,UAACyC,EAAAA,EAA0B,EAAC3C,sBAAoB,6BAA6BE,0BAAwB,sBAE3G,CACA,SAAS0C,EAAU,WACjBnD,CAAS,aACToD,EAAc,UAAU,CACxB,GAAGlD,EACkE,EACrE,MAAO,UAACgD,EAAAA,EAAuC,EAAC7C,YAAU,wBAAwB+C,YAAaA,EAAapD,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qDAAsE,aAAhB8C,GAA8B,6CAA8D,eAAhBA,GAAgC,+CAAgDpD,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,0CAA0CC,wBAAsB,YAAYC,0BAAwB,2BACvd,UAACyC,EAAAA,EAAmC,EAAC7C,YAAU,oBAAoBL,UAAU,yCAAyCO,sBAAoB,sCAAsCE,0BAAwB,qBAE9M,kTE1BA,uCAA2K,CAE3K,uCAAiK,yBCFjK,qDCAA,4DCAA,kDCAA,wDCAA,iECAA,uDCAA,sDCAA,yDCAA,iDCAA,2DCAA,wVCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,uBAAiC,EACjC,MAjBA,IAAoB,uCAA0H,CAiB9I,yFAES,EACF,CACP,CACA,QAvBA,IAAsB,uCAA4H,CAuBlJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAxCA,IAAsB,uCAAiH,CAwCvI,gFACA,gBAxCA,IAAsB,uCAAuH,CAwC7I,sFACA,aAxCA,IAAsB,sCAAoH,CAwC1I,mFACA,WAxCA,IAAsB,4CAAgF,CAwCtG,+CACA,cAxCA,IAAsB,4CAAmF,CAwCzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,4FAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,uBACA,sBAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,yBCnFD,kDCAA,yDCAA,4DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/?dc91", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/badge.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/./src/components/dashboard/dashboard-metrics.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/card.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/src/app/dashboard/page.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/scroll-area.tsx", "webpack://next-shadcn-dashboard-starter/?b8d0", "webpack://next-shadcn-dashboard-starter/?7d1e", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/?934f", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\dashboard\\\\dashboard-metrics.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardHeader, CardTitle, CardDescription, CardAction, CardFooter } from '@/components/ui/card';\nimport { IconTrendingUp, IconCalendar, IconUsers, IconStethoscope } from '@tabler/icons-react';\nimport { getDashboardMetrics } from '@/lib/api';\nimport type { DashboardMetrics } from '@/types/clinic';\nimport { t } from '@/lib/translations';\nexport default function DashboardMetrics() {\n  const [metrics, setMetrics] = useState<DashboardMetrics>({\n    todayAppointments: 0,\n    recentPatients: 0,\n    totalPatients: 0,\n    activetreatments: 0\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  useEffect(() => {\n    async function fetchMetrics() {\n      try {\n        setLoading(true);\n        const data = await getDashboardMetrics();\n        setMetrics(data);\n        setError(null);\n      } catch (err) {\n        console.error('Failed to fetch dashboard metrics:', err);\n        setError(t('dashboard.errors.failedToLoadMetrics'));\n      } finally {\n        setLoading(false);\n      }\n    }\n    fetchMetrics();\n  }, []);\n  if (loading) {\n    return <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>\n        {[...Array(4)].map((_, i) => <Card key={i} className='@container/card'>\n            <CardHeader>\n              <CardDescription className='flex items-center gap-2'>\n                <div className='h-4 w-4 bg-muted animate-pulse rounded' />\n                <div className='h-4 w-24 bg-muted animate-pulse rounded' />\n              </CardDescription>\n              <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>\n                <div className='h-8 w-16 bg-muted animate-pulse rounded' />\n              </CardTitle>\n            </CardHeader>\n          </Card>)}\n      </div>;\n  }\n  if (error) {\n    return <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4'>\n        <Card className='col-span-full'>\n          <CardHeader>\n            <CardTitle className='text-destructive'>{t('dashboard.errors.loadingDashboard')}</CardTitle>\n            <CardDescription>{error}</CardDescription>\n          </CardHeader>\n        </Card>\n      </div>;\n  }\n  return <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4' data-sentry-component=\"DashboardMetrics\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n      {/* Today's Appointments Card */}\n      <Card className='@container/card' data-sentry-element=\"Card\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <CardDescription className='flex items-center gap-2' data-sentry-element=\"CardDescription\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <IconCalendar className='size-4' data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n            {t('dashboard.metrics.todayAppointments')}\n          </CardDescription>\n          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            {metrics.todayAppointments}\n          </CardTitle>\n          <CardAction data-sentry-element=\"CardAction\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <Badge variant='outline' data-sentry-element=\"Badge\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n              <IconTrendingUp data-sentry-element=\"IconTrendingUp\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n              {t('dashboard.metrics.active')}\n            </Badge>\n          </CardAction>\n        </CardHeader>\n        <CardFooter className='flex-col items-start gap-1.5 text-sm' data-sentry-element=\"CardFooter\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <div className='line-clamp-1 flex gap-2 font-medium'>\n            {t('dashboard.metrics.scheduledForToday')} <IconCalendar className='size-4' data-sentry-element=\"IconCalendar\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n          </div>\n          <div className='text-muted-foreground'>\n            {t('dashboard.metrics.appointmentsScheduledForToday')}\n          </div>\n        </CardFooter>\n      </Card>\n\n      {/* Recent Patients Card */}\n      <Card className='@container/card' data-sentry-element=\"Card\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <CardDescription className='flex items-center gap-2' data-sentry-element=\"CardDescription\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <IconUsers className='size-4' data-sentry-element=\"IconUsers\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n            {t('dashboard.metrics.recentPatients')}\n          </CardDescription>\n          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            {metrics.recentPatients}\n          </CardTitle>\n          <CardAction data-sentry-element=\"CardAction\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <Badge variant='outline' data-sentry-element=\"Badge\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n              <IconTrendingUp data-sentry-element=\"IconTrendingUp\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n              {t('dashboard.metrics.last7Days')}\n            </Badge>\n          </CardAction>\n        </CardHeader>\n        <CardFooter className='flex-col items-start gap-1.5 text-sm' data-sentry-element=\"CardFooter\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <div className='line-clamp-1 flex gap-2 font-medium'>\n            {t('dashboard.metrics.newPatientsThisWeek')} <IconUsers className='size-4' data-sentry-element=\"IconUsers\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n          </div>\n          <div className='text-muted-foreground'>\n            {t('dashboard.metrics.patientsRegisteredInLast7Days')}\n          </div>\n        </CardFooter>\n      </Card>\n\n      {/* Total Patients Card */}\n      <Card className='@container/card' data-sentry-element=\"Card\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <CardDescription className='flex items-center gap-2' data-sentry-element=\"CardDescription\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <IconUsers className='size-4' data-sentry-element=\"IconUsers\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n            {t('dashboard.metrics.totalPatients')}\n          </CardDescription>\n          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            {metrics.totalPatients}\n          </CardTitle>\n          <CardAction data-sentry-element=\"CardAction\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <Badge variant='outline' data-sentry-element=\"Badge\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n              <IconTrendingUp data-sentry-element=\"IconTrendingUp\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n              {t('dashboard.metrics.allTime')}\n            </Badge>\n          </CardAction>\n        </CardHeader>\n        <CardFooter className='flex-col items-start gap-1.5 text-sm' data-sentry-element=\"CardFooter\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <div className='line-clamp-1 flex gap-2 font-medium'>\n            {t('dashboard.metrics.totalRegisteredPatients')} <IconUsers className='size-4' data-sentry-element=\"IconUsers\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n          </div>\n          <div className='text-muted-foreground'>\n            {t('dashboard.metrics.completePatientDatabase')}\n          </div>\n        </CardFooter>\n      </Card>\n\n      {/* Active Treatments Card */}\n      <Card className='@container/card' data-sentry-element=\"Card\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n        <CardHeader data-sentry-element=\"CardHeader\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <CardDescription className='flex items-center gap-2' data-sentry-element=\"CardDescription\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <IconStethoscope className='size-4' data-sentry-element=\"IconStethoscope\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n            {t('dashboard.metrics.activetreatments')}\n          </CardDescription>\n          <CardTitle className='text-2xl font-semibold tabular-nums @[250px]/card:text-3xl' data-sentry-element=\"CardTitle\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            {metrics.activetreatments}\n          </CardTitle>\n          <CardAction data-sentry-element=\"CardAction\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n            <Badge variant='outline' data-sentry-element=\"Badge\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n              <IconTrendingUp data-sentry-element=\"IconTrendingUp\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n              {t('dashboard.metrics.available')}\n            </Badge>\n          </CardAction>\n        </CardHeader>\n        <CardFooter className='flex-col items-start gap-1.5 text-sm' data-sentry-element=\"CardFooter\" data-sentry-source-file=\"dashboard-metrics.tsx\">\n          <div className='line-clamp-1 flex gap-2 font-medium'>\n            {t('dashboard.metrics.treatmentOptionsAvailable')} <IconStethoscope className='size-4' data-sentry-element=\"IconStethoscope\" data-sentry-source-file=\"dashboard-metrics.tsx\" />\n          </div>\n          <div className='text-muted-foreground'>\n            {t('dashboard.metrics.fullServiceCatalog')}\n          </div>\n        </CardFooter>\n      </Card>\n    </div>;\n}", "module.exports = require(\"os\");", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Card({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card' className={cn('bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm', className)} {...props} data-sentry-component=\"Card\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardHeader({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-header' className={cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6', className)} {...props} data-sentry-component=\"CardHeader\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardTitle({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-title' className={cn('leading-none font-semibold', className)} {...props} data-sentry-component=\"CardTitle\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardDescription({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-description' className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"CardDescription\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardAction({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-action' className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)} {...props} data-sentry-component=\"CardAction\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardContent({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-content' className={cn('px-6', className)} {...props} data-sentry-component=\"CardContent\" data-sentry-source-file=\"card.tsx\" />;\n}\nfunction CardFooter({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  return <div data-slot='card-footer' className={cn('flex items-center px-6 [.border-t]:pt-6', className)} {...props} data-sentry-component=\"CardFooter\" data-sentry-source-file=\"card.tsx\" />;\n}\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "import { auth } from '@clerk/nextjs/server';\nimport { redirect } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport DashboardMetrics from '@/components/dashboard/dashboard-metrics';\nimport { t } from '@/lib/translations';\nexport default async function Dashboard() {\n  const {\n    userId\n  } = await auth();\n  if (!userId) {\n    return redirect('/auth/sign-in');\n  }\n  return <PageContainer data-sentry-element=\"PageContainer\" data-sentry-component=\"Dashboard\" data-sentry-source-file=\"page.tsx\">\r\n      <div className='flex flex-1 flex-col space-y-2'>\r\n        <div className='flex items-center justify-between space-y-2'>\r\n          <h2 className='text-2xl font-bold tracking-tight'>\r\n            {t('dashboard.title')}\r\n          </h2>\r\n          <p className='text-muted-foreground'>\r\n            {t('dashboard.subtitle')}\r\n          </p>\r\n        </div>\r\n\r\n        <DashboardMetrics data-sentry-element=\"DashboardMetrics\" data-sentry-source-file=\"page.tsx\" />\r\n      </div>\r\n    </PageContainer>;\n}", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "'use client';\n\nimport * as React from 'react';\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\nimport { cn } from '@/lib/utils';\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return <ScrollAreaPrimitive.Root data-slot='scroll-area' className={cn('relative', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.Root\" data-sentry-component=\"ScrollArea\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.Viewport data-slot='scroll-area-viewport' className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1' data-sentry-element=\"ScrollAreaPrimitive.Viewport\" data-sentry-source-file=\"scroll-area.tsx\">\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar data-sentry-element=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n      <ScrollAreaPrimitive.Corner data-sentry-element=\"ScrollAreaPrimitive.Corner\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.Root>;\n}\nfunction ScrollBar({\n  className,\n  orientation = 'vertical',\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return <ScrollAreaPrimitive.ScrollAreaScrollbar data-slot='scroll-area-scrollbar' orientation={orientation} className={cn('flex touch-none p-px transition-colors select-none', orientation === 'vertical' && 'h-full w-2.5 border-l border-l-transparent', orientation === 'horizontal' && 'h-2.5 flex-col border-t border-t-transparent', className)} {...props} data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaScrollbar\" data-sentry-component=\"ScrollBar\" data-sentry-source-file=\"scroll-area.tsx\">\r\n      <ScrollAreaPrimitive.ScrollAreaThumb data-slot='scroll-area-thumb' className='bg-border relative flex-1 rounded-full' data-sentry-element=\"ScrollAreaPrimitive.ScrollAreaThumb\" data-sentry-source-file=\"scroll-area.tsx\" />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>;\n}\nexport { ScrollArea, ScrollBar };", "\nexport { deleteKeylessAction as \"7f7b45347fd50452ee6e2850ded1018991a7b086f0\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { syncKeylessConfigAction as \"7f909588461cb83e855875f4939d6f26e4ae81b49e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { createOrReadKeylessAction as \"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\keyless-actions.js\"\nexport { invalidateCacheAction as \"7f22efd92a3b59d43d3d12fe480e87910640e1db9e\" } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\node_modules\\\\.pnpm\\\\@clerk+nextjs@6.12.12_next@_f2abd70610af0db25eb1f4c2a7e1d3bb\\\\node_modules\\\\@clerk\\\\nextjs\\\\dist\\\\esm\\\\app-router\\\\server-actions.js\"\n", "import(/* webpackMode: \"eager\", webpackExports: [\"default\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\dashboard\\\\dashboard-metrics.tsx\");\n;\nimport(/* webpackMode: \"eager\", webpackExports: [\"ScrollArea\"] */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\components\\\\ui\\\\scroll-area.tsx\");\n", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "Comp", "Slot", "data-slot", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "DashboardMetrics", "metrics", "setMetrics", "useState", "todayAppointments", "recentPatients", "totalPatients", "activetreatments", "loading", "setLoading", "error", "setError", "div", "map", "_", "i", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardDescription", "CardTitle", "t", "IconCalendar", "CardAction", "IconTrendingUp", "<PERSON><PERSON><PERSON>er", "IconUsers", "IconStethoscope", "<PERSON><PERSON><PERSON><PERSON>", "Dashboard", "userId", "auth", "_jsx", "<PERSON><PERSON><PERSON><PERSON>", "_jsxs", "h2", "p", "redirect", "serverComponentModule.default", "ScrollArea", "children", "ScrollAreaPrimitive", "<PERSON><PERSON>Bar", "orientation"], "sourceRoot": ""}