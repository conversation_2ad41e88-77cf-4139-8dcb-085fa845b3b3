try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="4f962512-1c9e-4329-9af8-1e551ee012ab",e._sentryDebugIdIdentifier="sentry-dbid-4f962512-1c9e-4329-9af8-1e551ee012ab")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4050],{14218:(e,t,a)=>{"use strict";a.d(t,{C1:()=>C,bL:()=>k});var s=a(99004),n=a(39552),r=a(38774),l=a(84732),i=a(18608),o=a(10751),d=a(66042),c=a(22474),m=a(51452),u=a(52880),x="Checkbox",[p,g]=(0,r.A)(x),[f,h]=p(x),y=s.forwardRef((e,t)=>{let{__scopeCheckbox:a,name:r,checked:o,defaultChecked:d,required:c,disabled:x,value:p="on",onCheckedChange:g,form:h,...y}=e,[v,j]=s.useState(null),k=(0,n.s)(t,e=>j(e)),C=s.useRef(!1),I=!v||h||!!v.closest("form"),[T=!1,z]=(0,i.i)({prop:o,defaultProp:d,onChange:g}),S=s.useRef(T);return s.useEffect(()=>{let e=null==v?void 0:v.form;if(e){let t=()=>z(S.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[v,z]),(0,u.jsxs)(f,{scope:a,state:T,disabled:x,children:[(0,u.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":N(T)?"mixed":T,"aria-required":c,"data-state":w(T),"data-disabled":x?"":void 0,disabled:x,value:p,...y,ref:k,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(e.onClick,e=>{z(e=>!!N(e)||!e),I&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),I&&(0,u.jsx)(b,{control:v,bubbles:!C.current,name:r,value:p,checked:T,required:c,disabled:x,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!N(d)&&d})]})});y.displayName=x;var v="CheckboxIndicator",j=s.forwardRef((e,t)=>{let{__scopeCheckbox:a,forceMount:s,...n}=e,r=h(v,a);return(0,u.jsx)(c.C,{present:s||N(r.state)||!0===r.state,children:(0,u.jsx)(m.sG.span,{"data-state":w(r.state),"data-disabled":r.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});j.displayName=v;var b=e=>{let{control:t,checked:a,bubbles:n=!0,defaultChecked:r,...l}=e,i=s.useRef(null),c=(0,o.Z)(a),m=(0,d.X)(t);s.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(c!==a&&t){let s=new Event("click",{bubbles:n});e.indeterminate=N(a),t.call(e,!N(a)&&a),e.dispatchEvent(s)}},[c,a,n]);let x=s.useRef(!N(a)&&a);return(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=r?r:x.current,...l,tabIndex:-1,ref:i,style:{...e.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function N(e){return"indeterminate"===e}function w(e){return N(e)?"indeterminate":e?"checked":"unchecked"}var k=y,C=j},18580:(e,t,a)=>{"use strict";a.d(t,{o:()=>n});class s{async makeRequest(e){let t,a,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{method:n="GET",body:r,params:l}=s;if(t="/api".concat(e),l){let e=new URLSearchParams;Object.entries(l).forEach(t=>{let[a,s]=t;null!=s&&e.append(a,s.toString())}),e.toString()&&(t+="?".concat(e.toString()))}a={method:n,headers:{"Content-Type":"application/json"}},r&&"GET"!==n&&(a.body=JSON.stringify(r));try{let e=await fetch(t,a);if(!e.ok){let t=await e.text();throw Error("API request failed: ".concat(e.status," ").concat(e.statusText," - ").concat(t))}return await e.json()}catch(t){throw console.error("API request failed for ".concat(e,":"),t),t}}async getAppointments(e){return this.makeRequest("/appointments",{params:{depth:"2",...e}})}async getAppointment(e){return this.makeRequest("/appointments/".concat(e),{params:{depth:"2"}})}async createAppointment(e){return this.makeRequest("/appointments",{method:"POST",body:e})}async updateAppointment(e,t){return this.makeRequest("/appointments/".concat(e),{method:"PATCH",body:t})}async deleteAppointment(e){return this.makeRequest("/appointments/".concat(e),{method:"DELETE"})}async getPatients(e){let t={depth:"1",...e};return(null==e?void 0:e.search)&&(t["where[or][0][fullName][contains]"]=e.search,t["where[or][1][phone][contains]"]=e.search,t["where[or][2][email][contains]"]=e.search,delete t.search),this.makeRequest("/patients",{params:t})}async getPatient(e){return this.makeRequest("/patients/".concat(e),{params:{depth:"1"}})}async createPatient(e){return this.makeRequest("/patients",{method:"POST",body:e})}async updatePatient(e,t){return this.makeRequest("/patients/".concat(e),{method:"PATCH",body:t})}async deletePatient(e){return this.makeRequest("/patients/".concat(e),{method:"DELETE"})}async getTreatments(e){return this.makeRequest("/treatments",{params:{depth:"1",...e}})}async getTreatment(e){return this.makeRequest("/treatments/".concat(e))}async createTreatment(e){return this.makeRequest("/treatments",{method:"POST",body:e})}async updateTreatment(e,t){return this.makeRequest("/treatments/".concat(e),{method:"PATCH",body:t})}async deleteTreatment(e){return this.makeRequest("/treatments/".concat(e),{method:"DELETE"})}async getUsers(e){return this.makeRequest("/users",{params:{depth:"1",...e}})}async updateUser(e,t){return this.makeRequest("/users/".concat(e),{method:"PATCH",body:t})}async syncCurrentUser(){return this.makeRequest("/users/sync",{method:"POST",body:{clerkId:this.user.clerkId,email:this.user.email,firstName:this.user.firstName,lastName:this.user.lastName}})}async syncUser(e){try{let t=await this.makeRequest("/users",{params:{where:JSON.stringify({clerkId:{equals:e.clerkId}}),limit:1}});if(!t.docs||!(t.docs.length>0))return await this.makeRequest("/users",{method:"POST",body:{email:e.email,clerkId:e.clerkId,firstName:e.firstName,lastName:e.lastName,role:"front-desk",lastLogin:new Date().toISOString()}});{let a=t.docs[0];return await this.makeRequest("/users/".concat(a.id),{method:"PATCH",body:{email:e.email,firstName:e.firstName,lastName:e.lastName,lastLogin:new Date().toISOString()}})}}catch(t){return console.error("Error syncing user with Payload:",t),{id:"temp-id",email:e.email,clerkId:e.clerkId,role:"front-desk",firstName:e.firstName,lastName:e.lastName}}}async getPatientInteractions(e){return this.makeRequest("/patient-interactions",{params:{depth:"2",...e}})}async getPatientInteraction(e){return this.makeRequest("/patient-interactions/".concat(e),{params:{depth:"2"}})}async createPatientInteraction(e){return this.makeRequest("/patient-interactions",{method:"POST",body:e})}async updatePatientInteraction(e,t){return this.makeRequest("/patient-interactions/".concat(e),{method:"PATCH",body:t})}async deletePatientInteraction(e){return this.makeRequest("/patient-interactions/".concat(e),{method:"DELETE"})}async getPatientTasks(e){return this.makeRequest("/patient-tasks",{params:{depth:"2",...e}})}async getPatientTask(e){return this.makeRequest("/patient-tasks/".concat(e),{params:{depth:"2"}})}async createPatientTask(e){return this.makeRequest("/patient-tasks",{method:"POST",body:e})}async updatePatientTask(e,t){return this.makeRequest("/patient-tasks/".concat(e),{method:"PATCH",body:t})}async deletePatientTask(e){return this.makeRequest("/patient-tasks/".concat(e),{method:"DELETE"})}async getPatientInteractionsByPatient(e,t){return this.makeRequest("/patients/".concat(e,"/interactions"),{params:{depth:"2",...t}})}async getPatientTasksByPatient(e,t){return this.makeRequest("/patients/".concat(e,"/tasks"),{params:{depth:"2",...t}})}async getPatientTimeline(e,t){return this.makeRequest("/patients/".concat(e,"/timeline"),{params:{depth:"2",...t}})}constructor(e){this.user=e}}function n(e){return new s(e)}},21747:(e,t,a)=>{"use strict";a.d(t,{B8:()=>S,UC:()=>D,bL:()=>z,l9:()=>A});var s=a(99004),n=a(84732),r=a(38774),l=a(59949),i=a(22474),o=a(51452),d=a(51825),c=a(18608),m=a(29548),u=a(52880),x="Tabs",[p,g]=(0,r.A)(x,[l.RG]),f=(0,l.RG)(),[h,y]=p(x),v=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,onValueChange:n,defaultValue:r,orientation:l="horizontal",dir:i,activationMode:x="automatic",...p}=e,g=(0,d.jH)(i),[f,y]=(0,c.i)({prop:s,onChange:n,defaultProp:r});return(0,u.jsx)(h,{scope:a,baseId:(0,m.B)(),value:f,onValueChange:y,orientation:l,dir:g,activationMode:x,children:(0,u.jsx)(o.sG.div,{dir:g,"data-orientation":l,...p,ref:t})})});v.displayName=x;var j="TabsList",b=s.forwardRef((e,t)=>{let{__scopeTabs:a,loop:s=!0,...n}=e,r=y(j,a),i=f(a);return(0,u.jsx)(l.bL,{asChild:!0,...i,orientation:r.orientation,dir:r.dir,loop:s,children:(0,u.jsx)(o.sG.div,{role:"tablist","aria-orientation":r.orientation,...n,ref:t})})});b.displayName=j;var N="TabsTrigger",w=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,disabled:r=!1,...i}=e,d=y(N,a),c=f(a),m=I(d.baseId,s),x=T(d.baseId,s),p=s===d.value;return(0,u.jsx)(l.q7,{asChild:!0,...c,focusable:!r,active:p,children:(0,u.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:m,...i,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{r||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(s)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(s)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||r||!e||d.onValueChange(s)})})})});w.displayName=N;var k="TabsContent",C=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:n,forceMount:r,children:l,...d}=e,c=y(k,a),m=I(c.baseId,n),x=T(c.baseId,n),p=n===c.value,g=s.useRef(p);return s.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(i.C,{present:r||p,children:a=>{let{present:s}=a;return(0,u.jsx)(o.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":m,hidden:!s,id:x,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:s&&l})}})});function I(e,t){return"".concat(e,"-trigger-").concat(t)}function T(e,t){return"".concat(e,"-content-").concat(t)}C.displayName=k;var z=v,S=b,A=w,D=C},25192:(e,t,a)=>{"use strict";a.d(t,{T:()=>r});var s=a(52880);a(99004);var n=a(54651);function r(e){let{className:t,...a}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},39372:(e,t,a)=>{"use strict";a.d(t,{V:()=>c});var s=a(52880);a(99004);var n=a(25782),r=a(54651),l=a(62054),i=a(86183);let o=()=>(0,s.jsx)(i.YJP,{className:"size-4","data-sentry-element":"ChevronLeftIcon","data-sentry-component":"LeftIcon","data-sentry-source-file":"calendar.tsx"}),d=()=>(0,s.jsx)(i.vKP,{className:"size-4","data-sentry-element":"ChevronRightIcon","data-sentry-component":"RightIcon","data-sentry-source-file":"calendar.tsx"});function c(e){let{className:t,classNames:a,showOutsideDays:i=!0,...c}=e;return(0,s.jsx)(n.hv,{showOutsideDays:i,className:(0,r.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,r.cn)((0,l.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,r.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===c.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,r.cn)((0,l.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:o,IconRight:d},...c,"data-sentry-element":"DayPicker","data-sentry-component":"Calendar","data-sentry-source-file":"calendar.tsx"})}},40153:(e,t,a)=>{"use strict";a.d(t,{S:()=>i});var s=a(52880);a(99004);var n=a(14218),r=a(90502),l=a(54651);function i(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,"data-sentry-element":"CheckboxPrimitive.Root","data-sentry-component":"Checkbox","data-sentry-source-file":"checkbox.tsx",children:(0,s.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none","data-sentry-element":"CheckboxPrimitive.Indicator","data-sentry-source-file":"checkbox.tsx",children:(0,s.jsx)(r.A,{className:"size-3.5","data-sentry-element":"CheckIcon","data-sentry-source-file":"checkbox.tsx"})})})}},42278:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>o,av:()=>d,j7:()=>i,tU:()=>l});var s=a(52880);a(99004);var n=a(21747),r=a(54651);function l(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"tabs",className:(0,r.cn)("flex flex-col gap-2",t),...a,"data-sentry-element":"TabsPrimitive.Root","data-sentry-component":"Tabs","data-sentry-source-file":"tabs.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)(n.B8,{"data-slot":"tabs-list",className:(0,r.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...a,"data-sentry-element":"TabsPrimitive.List","data-sentry-component":"TabsList","data-sentry-source-file":"tabs.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(n.l9,{"data-slot":"tabs-trigger",className:(0,r.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a,"data-sentry-element":"TabsPrimitive.Trigger","data-sentry-component":"TabsTrigger","data-sentry-source-file":"tabs.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(n.UC,{"data-slot":"tabs-content",className:(0,r.cn)("flex-1 outline-none",t),...a,"data-sentry-element":"TabsPrimitive.Content","data-sentry-component":"TabsContent","data-sentry-source-file":"tabs.tsx"})}},54651:(e,t,a)=>{"use strict";a.d(t,{Jv:()=>d,cn:()=>r,fw:()=>o,r6:()=>i,z3:()=>l});var s=a(97921),n=a(56309);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,s.$)(t))}function l(e){var t,a;let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:n=0,sizeType:r="normal"}=s;if(0===e)return"0 Byte";let l=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,l)).toFixed(n)," ").concat("accurate"===r?null!=(t=["Bytes","KiB","MiB","GiB","TiB"][l])?t:"Bytest":null!=(a=["Bytes","KB","MB","GB","TB"][l])?a:"Bytes")}function i(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function o(e){let t=Math.floor((new Date().getTime()-e.getTime())/1e3);if(t<60)return"刚刚";let a=Math.floor(t/60);if(a<60)return"".concat(a,"分钟前");let s=Math.floor(a/60);if(s<24)return"".concat(s,"小时前");let n=Math.floor(s/24);if(n<7)return"".concat(n,"天前");let r=Math.floor(n/7);if(r<4)return"".concat(r,"周前");let l=Math.floor(n/30);if(l<12)return"".concat(l,"个月前");let i=Math.floor(n/365);return"".concat(i,"年前")}function d(e){return("string"==typeof e?new Date(e):e)<new Date}},56420:(e,t,a)=>{"use strict";a.d(t,{C5:()=>y,MJ:()=>f,Rr:()=>h,eI:()=>p,lR:()=>g,lV:()=>d,zB:()=>m});var s=a(52880),n=a(99004),r=a(50516),l=a(38406),i=a(54651),o=a(84692);let d=l.Op,c=n.createContext({}),m=e=>{let{...t}=e;return(0,s.jsx)(c.Provider,{value:{name:t.name},"data-sentry-element":"FormFieldContext.Provider","data-sentry-component":"FormField","data-sentry-source-file":"form.tsx",children:(0,s.jsx)(l.xI,{...t,"data-sentry-element":"Controller","data-sentry-source-file":"form.tsx"})})},u=()=>{let e=n.useContext(c),t=n.useContext(x),{getFieldState:a}=(0,l.xW)(),s=(0,l.lN)({name:e.name}),r=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...r}},x=n.createContext({});function p(e){let{className:t,...a}=e,r=n.useId();return(0,s.jsx)(x.Provider,{value:{id:r},"data-sentry-element":"FormItemContext.Provider","data-sentry-component":"FormItem","data-sentry-source-file":"form.tsx",children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...a})})}function g(e){let{className:t,...a}=e,{error:n,formItemId:r}=u();return(0,s.jsx)(o.J,{"data-slot":"form-label","data-error":!!n,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:r,...a,"data-sentry-element":"Label","data-sentry-component":"FormLabel","data-sentry-source-file":"form.tsx"})}function f(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:l,formMessageId:i}=u();return(0,s.jsx)(r.DX,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!a,...t,"data-sentry-element":"Slot","data-sentry-component":"FormControl","data-sentry-source-file":"form.tsx"})}function h(e){let{className:t,...a}=e,{formDescriptionId:n}=u();return(0,s.jsx)("p",{"data-slot":"form-description",id:n,className:(0,i.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"FormDescription","data-sentry-source-file":"form.tsx"})}function y(e){var t;let{className:a,...n}=e,{error:r,formMessageId:l}=u(),o=r?String(null!=(t=null==r?void 0:r.message)?t:""):n.children;return o?(0,s.jsx)("p",{"data-slot":"form-message",id:l,className:(0,i.cn)("text-destructive text-sm",a),...n,"data-sentry-component":"FormMessage","data-sentry-source-file":"form.tsx",children:o}):null}},60171:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>d,_2:()=>m,hO:()=>u,lp:()=>x,mB:()=>p,rI:()=>i,ty:()=>o});var s=a(52880);a(99004);var n=a(83028),r=a(90502),l=a(54651);function i(e){let{...t}=e;return(0,s.jsx)(n.bL,{"data-slot":"dropdown-menu",...t,"data-sentry-element":"DropdownMenuPrimitive.Root","data-sentry-component":"DropdownMenu","data-sentry-source-file":"dropdown-menu.tsx"})}function o(e){let{...t}=e;return(0,s.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t,"data-sentry-element":"DropdownMenuPrimitive.Trigger","data-sentry-component":"DropdownMenuTrigger","data-sentry-source-file":"dropdown-menu.tsx"})}function d(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(n.ZL,{"data-sentry-element":"DropdownMenuPrimitive.Portal","data-sentry-component":"DropdownMenuContent","data-sentry-source-file":"dropdown-menu.tsx",children:(0,s.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r,"data-sentry-element":"DropdownMenuPrimitive.Content","data-sentry-source-file":"dropdown-menu.tsx"})})}function c(e){let{...t}=e;return(0,s.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t,"data-sentry-element":"DropdownMenuPrimitive.Group","data-sentry-component":"DropdownMenuGroup","data-sentry-source-file":"dropdown-menu.tsx"})}function m(e){let{className:t,inset:a,variant:r="default",...i}=e;return(0,s.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,"data-sentry-element":"DropdownMenuPrimitive.Item","data-sentry-component":"DropdownMenuItem","data-sentry-source-file":"dropdown-menu.tsx"})}function u(e){let{className:t,children:a,checked:i,...o}=e;return(0,s.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:i,...o,"data-sentry-element":"DropdownMenuPrimitive.CheckboxItem","data-sentry-component":"DropdownMenuCheckboxItem","data-sentry-source-file":"dropdown-menu.tsx",children:[(0,s.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{"data-sentry-element":"DropdownMenuPrimitive.ItemIndicator","data-sentry-source-file":"dropdown-menu.tsx",children:(0,s.jsx)(r.A,{className:"size-4","data-sentry-element":"CheckIcon","data-sentry-source-file":"dropdown-menu.tsx"})})}),a]})}function x(e){let{className:t,inset:a,...r}=e;return(0,s.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,l.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r,"data-sentry-element":"DropdownMenuPrimitive.Label","data-sentry-component":"DropdownMenuLabel","data-sentry-source-file":"dropdown-menu.tsx"})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,l.cn)("bg-border -mx-1 my-1 h-px",t),...a,"data-sentry-element":"DropdownMenuPrimitive.Separator","data-sentry-component":"DropdownMenuSeparator","data-sentry-source-file":"dropdown-menu.tsx"})}},62054:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>i});var s=a(52880);a(99004);var n=a(50516),r=a(85017),l=a(54651);let i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...d}=e,c=o?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:a,size:r,className:t})),...d,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}},73261:(e,t,a)=>{"use strict";a.d(t,{$7:()=>Z.A,Bl:()=>j.A,C0:()=>O.A,FI:()=>W,GI:()=>H.A,Gk:()=>_.A,Hv:()=>$.A,MR:()=>l.A,QO:()=>w.A,VS:()=>q.A,_L:()=>U.A,_v:()=>B.A,as:()=>G.A,bY:()=>V.A,hI:()=>C.A,iW:()=>P.A,jQ:()=>M.A,nR:()=>p.A,rI:()=>E.A,st:()=>L.A,uI:()=>N.A,uJ:()=>J.A,vg:()=>k.A});var s=a(77832),n=a(27997),r=a(85309),l=a(68046),i=a(40360),o=a(1083),d=a(58635),c=a(97648),m=a(12685),u=a(93900),x=a(98721),p=a(92775),g=a(29026),f=a(68369),h=a(12005),y=a(99906),v=a(29776),j=a(40773),b=a(59238),N=a(66444),w=a(46976),k=a(57165),C=a(2031),I=a(11661),T=a(45900),z=a(30912),S=a(36891),A=a(17142),D=a(68860),R=a(60680),P=a(60382),B=a(72486),F=a(68290),q=a(47889),_=a(98066),E=a(3775),L=a(79254),M=a(68065),V=a(82963),U=a(37028),O=a(60664),J=a(29118),$=a(71861),H=a(26368),Z=a(83919),G=a(7964);let W={dashboard:s.A,logo:n.A,login:r.A,close:l.A,product:i.A,spinner:o.A,kanban:d.A,chevronLeft:c.A,chevronRight:m.A,trash:u.A,employee:x.A,post:p.A,page:g.A,userPen:f.A,user2:h.A,media:y.A,settings:v.A,billing:j.A,ellipsis:b.A,add:N.A,warning:w.A,user:k.A,arrowRight:C.A,help:I.A,pizza:T.A,sun:z.A,moon:S.A,laptop:A.A,github:D.A,twitter:R.A,check:P.A,calendar:B.A,users:F.A,medical:q.A}},86540:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,X9:()=>d,ZB:()=>i,Zp:()=>r,aR:()=>l,wL:()=>m});var s=a(52880);a(99004);var n=a(54651);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-action",className:(0,n.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",t),...a,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},95165:(e,t,a)=>{Promise.resolve().then(a.bind(a,99843)),Promise.resolve().then(a.bind(a,90917))},96113:(e,t,a)=>{"use strict";a.d(t,{Avatar:()=>l,AvatarFallback:()=>o,AvatarImage:()=>i});var s=a(52880);a(99004);var n=a(55955),r=a(54651);function l(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"avatar",className:(0,r.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a,"data-sentry-element":"AvatarPrimitive.Root","data-sentry-component":"Avatar","data-sentry-source-file":"avatar.tsx"})}function i(e){let{className:t,...a}=e;return(0,s.jsx)(n._V,{"data-slot":"avatar-image",className:(0,r.cn)("aspect-square size-full",t),...a,"data-sentry-element":"AvatarPrimitive.Image","data-sentry-component":"AvatarImage","data-sentry-source-file":"avatar.tsx"})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,r.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a,"data-sentry-element":"AvatarPrimitive.Fallback","data-sentry-component":"AvatarFallback","data-sentry-source-file":"avatar.tsx"})}},99843:(e,t,a)=>{"use strict";a.d(t,{default:()=>ea});var s=a(52880),n=a(99004),r=a(95181),l=a(86540),i=a(62054),o=a(88151),d=a(42278),c=a(96113),m=a(73261),u=a(42094),x=a(45450),p=a(90917),g=a(54651);let f={"phone-call":{label:"电话通话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},email:{label:"邮件沟通",icon:m.jQ,color:"bg-green-100 text-green-800 border-green-200"},"consultation-note":{label:"咨询记录",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"in-person-visit":{label:"到院就诊",icon:m.vg,color:"bg-orange-100 text-orange-800 border-orange-200"},"treatment-discussion":{label:"治疗讨论",icon:m.bY,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"billing-inquiry":{label:"账单咨询",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"}},h={open:{label:"开放",color:"bg-gray-100 text-gray-800"},"in-progress":{label:"进行中",color:"bg-blue-100 text-blue-800"},resolved:{label:"已解决",color:"bg-green-100 text-green-800"},closed:{label:"已关闭",color:"bg-gray-100 text-gray-600"}},y={low:{label:"低",color:"bg-gray-100 text-gray-600"},medium:{label:"中",color:"bg-yellow-100 text-yellow-800"},high:{label:"高",color:"bg-red-100 text-red-800"}};function v(e){let{patientId:t,interactions:a,loading:r=!1,onCreateInteraction:d,onInteractionClick:c,className:v}=e,[j,b]=(0,n.useState)(""),[N,w]=(0,n.useState)("all"),[k,C]=(0,n.useState)("all"),[I,T]=(0,n.useState)(a);(0,n.useEffect)(()=>{let e=a;j&&(e=e.filter(e=>{var t;return e.title.toLowerCase().includes(j.toLowerCase())||(null==(t=e.outcome)?void 0:t.toLowerCase().includes(j.toLowerCase()))||"object"==typeof e.staffMember&&"".concat(e.staffMember.firstName," ").concat(e.staffMember.lastName).toLowerCase().includes(j.toLowerCase())})),"all"!==N&&(e=e.filter(e=>e.interactionType===N)),"all"!==k&&(e=e.filter(e=>e.status===k)),T(e)},[a,j,N,k]);let z=e=>{var t;let a=(null==(t=f[e])?void 0:t.icon)||m.bY;return(0,s.jsx)(a,{className:"size-4","data-sentry-element":"IconComponent","data-sentry-component":"getInteractionIcon","data-sentry-source-file":"interaction-timeline.tsx"})},S=e=>"string"==typeof e?"未知工作人员":"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||e.email;return r?(0,s.jsxs)(l.Zp,{className:v,children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(m._L,{className:"size-5"}),"互动时间线"]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,s.jsx)("div",{className:"animate-pulse",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"size-8 bg-gray-200 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},t))})})]}):(0,s.jsxs)(l.Zp,{className:v,"data-sentry-element":"Card","data-sentry-component":"InteractionTimeline","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsx)(m._L,{className:"size-5","data-sentry-element":"IconClock","data-sentry-source-file":"interaction-timeline.tsx"}),"互动时间线",(0,s.jsx)(o.E,{variant:"secondary",className:"ml-2","data-sentry-element":"Badge","data-sentry-source-file":"interaction-timeline.tsx",children:I.length})]}),d&&(0,s.jsxs)(i.$,{onClick:d,size:"sm",children:[(0,s.jsx)(m.uI,{className:"size-4 mr-2"}),"添加互动"]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(m.C0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"interaction-timeline.tsx"}),(0,s.jsx)(u.p,{placeholder:"搜索互动记录...",value:j,onChange:e=>b(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"interaction-timeline.tsx"})]}),(0,s.jsxs)(x.l6,{value:N,onValueChange:w,"data-sentry-element":"Select","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsxs)(x.bq,{className:"w-full sm:w-[140px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsx)(m.uJ,{className:"size-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"interaction-timeline.tsx"}),(0,s.jsx)(x.yv,{placeholder:"类型","data-sentry-element":"SelectValue","data-sentry-source-file":"interaction-timeline.tsx"})]}),(0,s.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"interaction-timeline.tsx",children:"所有类型"}),Object.entries(f).map(e=>{let[t,a]=e;return(0,s.jsx)(x.eb,{value:t,children:a.label},t)})]})]}),(0,s.jsxs)(x.l6,{value:k,onValueChange:C,"data-sentry-element":"Select","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsx)(x.bq,{className:"w-full sm:w-[120px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"interaction-timeline.tsx",children:(0,s.jsx)(x.yv,{placeholder:"状态","data-sentry-element":"SelectValue","data-sentry-source-file":"interaction-timeline.tsx"})}),(0,s.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"interaction-timeline.tsx",children:[(0,s.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"interaction-timeline.tsx",children:"所有状态"}),Object.entries(h).map(e=>{let[t,a]=e;return(0,s.jsx)(x.eb,{value:t,children:a.label},t)})]})]})]})]}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"interaction-timeline.tsx",children:(0,s.jsx)(p.ScrollArea,{className:"h-[600px]","data-sentry-element":"ScrollArea","data-sentry-source-file":"interaction-timeline.tsx",children:0===I.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(m.bY,{className:"size-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"暂无互动记录"}),(0,s.jsx)("p",{className:"text-sm",children:"开始记录与患者的沟通互动"})]}):(0,s.jsx)("div",{className:"space-y-4",children:I.map((e,t)=>{let a=f[e.interactionType],n=h[e.status],r=y[e.priority];return(0,s.jsxs)("div",{className:"relative",children:[t<I.length-1&&(0,s.jsx)("div",{className:"absolute left-4 top-12 bottom-0 w-px bg-border"}),(0,s.jsxs)("div",{className:(0,g.cn)("flex items-start gap-3 p-4 rounded-lg border transition-colors",c&&"cursor-pointer hover:bg-muted/50"),onClick:()=>null==c?void 0:c(e),children:[(0,s.jsx)("div",{className:(0,g.cn)("flex items-center justify-center size-8 rounded-full border-2",(null==a?void 0:a.color)||"bg-gray-100 text-gray-600 border-gray-200"),children:z(e.interactionType)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,s.jsx)("h4",{className:"font-medium text-sm leading-tight",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,s.jsx)(o.E,{variant:"outline",className:n.color,children:n.label}),(0,s.jsx)(o.E,{variant:"outline",className:r.color,children:r.label})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground mb-2",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.vg,{className:"size-3"}),S(e.staffMember)]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(m._L,{className:"size-3"}),(0,g.r6)(new Date(e.timestamp))]})]}),e.outcome&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-2",children:e.outcome}),e.followUpRequired&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded",children:[(0,s.jsx)(m.hI,{className:"size-3"}),"需要跟进",e.followUpDate&&(0,s.jsxs)("span",{children:["- ",(0,g.r6)(new Date(e.followUpDate))]})]})]})]})]},e.id)})})})})]})}let j={"follow-up-call":{label:"跟进电话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},"appointment-scheduling":{label:"预约安排",icon:m._v,color:"bg-green-100 text-green-800 border-green-200"},"treatment-reminder":{label:"治疗提醒",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"billing-follow-up":{label:"账单跟进",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"},"medical-record-update":{label:"病历更新",icon:m.nR,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"consultation-follow-up":{label:"咨询跟进",icon:m.bY,color:"bg-orange-100 text-orange-800 border-orange-200"}},b={pending:{label:"待处理",color:"bg-gray-100 text-gray-800",icon:m._L},"in-progress":{label:"进行中",color:"bg-blue-100 text-blue-800",icon:m.hI},completed:{label:"已完成",color:"bg-green-100 text-green-800",icon:m.iW},cancelled:{label:"已取消",color:"bg-red-100 text-red-800",icon:m.MR}},N={low:{label:"低",color:"bg-gray-100 text-gray-600"},medium:{label:"中",color:"bg-yellow-100 text-yellow-800"},high:{label:"高",color:"bg-orange-100 text-orange-800"},urgent:{label:"紧急",color:"bg-red-100 text-red-800"}};function w(e){let{patientId:t,tasks:a,loading:r=!1,onCreateTask:c,onTaskClick:f,onTaskStatusChange:h,className:y,viewMode:v="list"}=e,[w,k]=(0,n.useState)(""),[C,I]=(0,n.useState)("all"),[T,z]=(0,n.useState)("all"),[S,A]=(0,n.useState)("all"),[D,R]=(0,n.useState)(a);(0,n.useEffect)(()=>{let e=a;w&&(e=e.filter(e=>{var t;return e.title.toLowerCase().includes(w.toLowerCase())||(null==(t=e.description)?void 0:t.toLowerCase().includes(w.toLowerCase()))})),"all"!==C&&(e=e.filter(e=>e.taskType===C)),"all"!==T&&(e=e.filter(e=>e.status===T)),"all"!==S&&(e=e.filter(e=>e.priority===S)),R(e)},[a,w,C,T,S]);let P=e=>{var t;let a=(null==(t=j[e])?void 0:t.icon)||m.nR;return(0,s.jsx)(a,{className:"size-4","data-sentry-element":"IconComponent","data-sentry-component":"getTaskIcon","data-sentry-source-file":"task-manager.tsx"})},B=e=>"string"==typeof e?"未分配":"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||e.email,F=e=>D.filter(t=>t.status===e),q=(e,t)=>{h&&h(e.id,t)};if(r)return(0,s.jsxs)(l.Zp,{className:y,children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(m.nR,{className:"size-5"}),"任务管理"]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,s.jsx)("div",{className:"animate-pulse",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"size-8 bg-gray-200 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},t))})})]});let _=e=>{let t=j[e.taskType],a=b[e.status],n=N[e.priority],r=(0,g.Jv)(e.dueDate)&&"completed"!==e.status;return(0,s.jsxs)("div",{className:(0,g.cn)("p-4 rounded-lg border transition-colors",f&&"cursor-pointer hover:bg-muted/50",r&&"border-red-200 bg-red-50"),onClick:()=>null==f?void 0:f(e),"data-sentry-component":"renderTaskCard","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:(0,g.cn)("flex items-center justify-center size-6 rounded border",(null==t?void 0:t.color)||"bg-gray-100 text-gray-600 border-gray-200"),children:P(e.taskType)}),(0,s.jsx)("h4",{className:"font-medium text-sm leading-tight",children:e.title})]}),r&&(0,s.jsx)(m.QO,{className:"size-4 text-red-500 flex-shrink-0"})]}),e.description&&(0,s.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2 mb-3",children:e.description}),(0,s.jsx)("div",{className:"flex items-center justify-between gap-2 mb-3",children:(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(o.E,{variant:"outline",className:n.color,"data-sentry-element":"Badge","data-sentry-source-file":"task-manager.tsx",children:n.label}),(0,s.jsx)(o.E,{variant:"outline",className:a.color,"data-sentry-element":"Badge","data-sentry-source-file":"task-manager.tsx",children:a.label})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.vg,{className:"size-3","data-sentry-element":"IconUser","data-sentry-source-file":"task-manager.tsx"}),B(e.assignedTo)]}),(0,s.jsxs)("span",{className:(0,g.cn)("flex items-center gap-1",r&&"text-red-600 font-medium"),children:[(0,s.jsx)(m._v,{className:"size-3","data-sentry-element":"IconCalendar","data-sentry-source-file":"task-manager.tsx"}),(0,g.r6)(new Date(e.dueDate))]})]}),h&&"completed"!==e.status&&(0,s.jsxs)("div",{className:"flex gap-1 mt-3",children:["pending"===e.status&&(0,s.jsx)(i.$,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),q(e,"in-progress")},className:"h-6 px-2 text-xs",children:"开始"}),"in-progress"===e.status&&(0,s.jsx)(i.$,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),q(e,"completed")},className:"h-6 px-2 text-xs",children:"完成"})]})]},e.id)};return(0,s.jsxs)(l.Zp,{className:y,"data-sentry-element":"Card","data-sentry-component":"TaskManager","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsx)(m.nR,{className:"size-5","data-sentry-element":"IconFileText","data-sentry-source-file":"task-manager.tsx"}),"任务管理",(0,s.jsx)(o.E,{variant:"secondary",className:"ml-2","data-sentry-element":"Badge","data-sentry-source-file":"task-manager.tsx",children:D.length})]}),c&&(0,s.jsxs)(i.$,{onClick:c,size:"sm",children:[(0,s.jsx)(m.uI,{className:"size-4 mr-2"}),"创建任务"]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(m.C0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"task-manager.tsx"}),(0,s.jsx)(u.p,{placeholder:"搜索任务...",value:w,onChange:e=>k(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"task-manager.tsx"})]}),(0,s.jsxs)(x.l6,{value:C,onValueChange:I,"data-sentry-element":"Select","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsxs)(x.bq,{className:"w-full sm:w-[140px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsx)(m.uJ,{className:"size-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"task-manager.tsx"}),(0,s.jsx)(x.yv,{placeholder:"类型","data-sentry-element":"SelectValue","data-sentry-source-file":"task-manager.tsx"})]}),(0,s.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"task-manager.tsx",children:"所有类型"}),Object.entries(j).map(e=>{let[t,a]=e;return(0,s.jsx)(x.eb,{value:t,children:a.label},t)})]})]}),(0,s.jsxs)(x.l6,{value:S,onValueChange:A,"data-sentry-element":"Select","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsx)(x.bq,{className:"w-full sm:w-[120px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"task-manager.tsx",children:(0,s.jsx)(x.yv,{placeholder:"优先级","data-sentry-element":"SelectValue","data-sentry-source-file":"task-manager.tsx"})}),(0,s.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"task-manager.tsx",children:[(0,s.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"task-manager.tsx",children:"所有优先级"}),Object.entries(N).map(e=>{let[t,a]=e;return(0,s.jsx)(x.eb,{value:t,children:a.label},t)})]})]})]})]}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"task-manager.tsx",children:"kanban"===v?(0,s.jsxs)(d.tU,{defaultValue:"pending",className:"w-full",children:[(0,s.jsx)(d.j7,{className:"grid w-full grid-cols-4",children:Object.entries(b).map(e=>{let[t,a]=e;return(0,s.jsxs)(d.Xi,{value:t,className:"text-xs",children:[a.label," (",F(t).length,")"]},t)})}),Object.entries(b).map(e=>{let[t,a]=e;return(0,s.jsx)(d.av,{value:t,children:(0,s.jsx)(p.ScrollArea,{className:"h-[500px]",children:(0,s.jsxs)("div",{className:"space-y-3",children:[F(t).map(_),0===F(t).length&&(0,s.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:(0,s.jsxs)("p",{children:["暂无",a.label,"任务"]})})]})})},t)})]}):(0,s.jsx)(p.ScrollArea,{className:"h-[600px]",children:0===D.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(m.nR,{className:"size-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"暂无任务"}),(0,s.jsx)("p",{className:"text-sm",children:"创建任务来跟进患者事务"})]}):(0,s.jsx)("div",{className:"space-y-3",children:D.map(_)})})})]})}var k=a(60171);let C=[{type:"phone-call",label:"记录电话",description:"记录与患者的电话沟通",icon:m.st,color:"text-blue-600 hover:text-blue-700",bgColor:"hover:bg-blue-50"},{type:"email",label:"记录邮件",description:"记录邮件沟通内容",icon:m.jQ,color:"text-green-600 hover:text-green-700",bgColor:"hover:bg-green-50"},{type:"consultation-note",label:"咨询记录",description:"添加咨询或诊疗记录",icon:m.VS,color:"text-purple-600 hover:text-purple-700",bgColor:"hover:bg-purple-50"},{type:"in-person-visit",label:"到院记录",description:"记录患者到院就诊",icon:m.vg,color:"text-orange-600 hover:text-orange-700",bgColor:"hover:bg-orange-50"},{type:"treatment-discussion",label:"治疗讨论",description:"记录治疗方案讨论",icon:m.bY,color:"text-indigo-600 hover:text-indigo-700",bgColor:"hover:bg-indigo-50"},{type:"billing-inquiry",label:"账单咨询",description:"记录账单相关咨询",icon:m.Bl,color:"text-yellow-600 hover:text-yellow-700",bgColor:"hover:bg-yellow-50"}],I=[{type:"follow-up-call",label:"跟进电话",description:"安排跟进电话任务",icon:m.st,color:"text-blue-600 hover:text-blue-700",bgColor:"hover:bg-blue-50"},{type:"appointment-scheduling",label:"预约安排",description:"安排预约相关任务",icon:m._v,color:"text-green-600 hover:text-green-700",bgColor:"hover:bg-green-50"},{type:"treatment-reminder",label:"治疗提醒",description:"创建治疗提醒任务",icon:m.VS,color:"text-purple-600 hover:text-purple-700",bgColor:"hover:bg-purple-50"},{type:"billing-follow-up",label:"账单跟进",description:"创建账单跟进任务",icon:m.Bl,color:"text-yellow-600 hover:text-yellow-700",bgColor:"hover:bg-yellow-50"},{type:"medical-record-update",label:"病历更新",description:"安排病历更新任务",icon:m.nR,color:"text-indigo-600 hover:text-indigo-700",bgColor:"hover:bg-indigo-50"},{type:"consultation-follow-up",label:"咨询跟进",description:"创建咨询跟进任务",icon:m.bY,color:"text-orange-600 hover:text-orange-700",bgColor:"hover:bg-orange-50"}];function T(e){let{patient:t,onCreateInteraction:a,onCreateTask:r,onScheduleAppointment:o,onViewBilling:d,className:c,compact:u=!1}=e,[x,p]=(0,n.useState)(!1),[f,h]=(0,n.useState)(!1);return u?(0,s.jsxs)("div",{className:(0,g.cn)("flex items-center gap-2",c),children:[(0,s.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>null==a?void 0:a("phone-call"),className:"text-blue-600 hover:text-blue-700 hover:bg-blue-50",children:[(0,s.jsx)(m.st,{className:"size-4 mr-1"}),"电话"]}),(0,s.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>null==r?void 0:r("follow-up-call"),className:"text-green-600 hover:text-green-700 hover:bg-green-50",children:[(0,s.jsx)(m._L,{className:"size-4 mr-1"}),"任务"]}),o&&(0,s.jsxs)(i.$,{size:"sm",variant:"outline",onClick:o,className:"text-purple-600 hover:text-purple-700 hover:bg-purple-50",children:[(0,s.jsx)(m._v,{className:"size-4 mr-1"}),"预约"]}),(0,s.jsxs)(k.rI,{children:[(0,s.jsx)(k.ty,{asChild:!0,children:(0,s.jsx)(i.$,{size:"sm",variant:"outline",children:(0,s.jsx)(m.uI,{className:"size-4"})})}),(0,s.jsxs)(k.SQ,{align:"end",className:"w-56",children:[(0,s.jsx)(k.lp,{children:"快速操作"}),(0,s.jsx)(k.mB,{}),a&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.lp,{className:"text-xs font-normal text-muted-foreground",children:"添加互动记录"}),C.slice(0,3).map(e=>{let t=e.icon;return(0,s.jsxs)(k._2,{onClick:()=>a(e.type),className:(0,g.cn)("cursor-pointer",e.bgColor),children:[(0,s.jsx)(t,{className:(0,g.cn)("size-4 mr-2",e.color)}),e.label]},e.type)}),(0,s.jsx)(k.mB,{})]}),r&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.lp,{className:"text-xs font-normal text-muted-foreground",children:"创建任务"}),I.slice(0,3).map(e=>{let t=e.icon;return(0,s.jsxs)(k._2,{onClick:()=>r(e.type),className:(0,g.cn)("cursor-pointer",e.bgColor),children:[(0,s.jsx)(t,{className:(0,g.cn)("size-4 mr-2",e.color)}),e.label]},e.type)})]}),d&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.mB,{}),(0,s.jsxs)(k._2,{onClick:d,className:"cursor-pointer hover:bg-gray-50",children:[(0,s.jsx)(m.Bl,{className:"size-4 mr-2 text-gray-600"}),"查看账单"]})]})]})]})]}):(0,s.jsxs)(l.Zp,{className:c,"data-sentry-element":"Card","data-sentry-component":"QuickActions","data-sentry-source-file":"quick-actions.tsx",children:[(0,s.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"quick-actions.tsx",children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"quick-actions.tsx",children:[(0,s.jsx)(m.uI,{className:"size-5","data-sentry-element":"IconPlus","data-sentry-source-file":"quick-actions.tsx"}),"快速操作"]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["为 ",t.fullName," 执行常用操作"]})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4","data-sentry-element":"CardContent","data-sentry-source-file":"quick-actions.tsx",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>null==a?void 0:a("phone-call"),className:"h-auto p-4 text-left justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200","data-sentry-element":"Button","data-sentry-source-file":"quick-actions.tsx",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(m.st,{className:"size-5","data-sentry-element":"IconPhone","data-sentry-source-file":"quick-actions.tsx"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"联系患者"}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"记录电话沟通"})]})]})}),o&&(0,s.jsx)(i.$,{variant:"outline",onClick:o,className:"h-auto p-4 text-left justify-start text-green-600 hover:text-green-700 hover:bg-green-50 border-green-200",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(m._v,{className:"size-5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"安排预约"}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"预约治疗或咨询"})]})]})})]}),a&&(0,s.jsx)("div",{children:(0,s.jsxs)(k.rI,{open:x,onOpenChange:p,children:[(0,s.jsx)(k.ty,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"outline",className:"w-full justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.bY,{className:"size-4"}),"添加互动记录"]}),(0,s.jsx)(m.rI,{className:"size-4"})]})}),(0,s.jsxs)(k.SQ,{className:"w-full min-w-[300px]",children:[(0,s.jsx)(k.lp,{children:"选择互动类型"}),(0,s.jsx)(k.mB,{}),C.map(e=>{let t=e.icon;return(0,s.jsx)(k._2,{onClick:()=>{a(e.type),p(!1)},className:(0,g.cn)("cursor-pointer p-3",e.bgColor),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(t,{className:(0,g.cn)("size-4 mt-0.5",e.color)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.type)})]})]})}),r&&(0,s.jsx)("div",{children:(0,s.jsxs)(k.rI,{open:f,onOpenChange:h,children:[(0,s.jsx)(k.ty,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"outline",className:"w-full justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)(m._L,{className:"size-4"}),"创建跟进任务"]}),(0,s.jsx)(m.rI,{className:"size-4"})]})}),(0,s.jsxs)(k.SQ,{className:"w-full min-w-[300px]",children:[(0,s.jsx)(k.lp,{children:"选择任务类型"}),(0,s.jsx)(k.mB,{}),I.map(e=>{let t=e.icon;return(0,s.jsx)(k._2,{onClick:()=>{r(e.type),h(!1)},className:(0,g.cn)("cursor-pointer p-3",e.bgColor),children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(t,{className:(0,g.cn)("size-4 mt-0.5",e.color)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})},e.type)})]})]})}),d&&(0,s.jsxs)(i.$,{variant:"outline",onClick:d,className:"w-full justify-start text-gray-600 hover:text-gray-700 hover:bg-gray-50",children:[(0,s.jsx)(m.Bl,{className:"size-4 mr-2"}),"查看账单记录"]})]})]})}var z=a(35355);let S={"phone-call":{label:"电话通话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},email:{label:"邮件沟通",icon:m.jQ,color:"bg-green-100 text-green-800 border-green-200"},"consultation-note":{label:"咨询记录",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"in-person-visit":{label:"到院就诊",icon:m.vg,color:"bg-orange-100 text-orange-800 border-orange-200"},"treatment-discussion":{label:"治疗讨论",icon:m.bY,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"billing-inquiry":{label:"账单咨询",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"}},A={"follow-up-call":{label:"跟进电话",icon:m.st,color:"bg-blue-100 text-blue-800 border-blue-200"},"appointment-scheduling":{label:"预约安排",icon:m._v,color:"bg-green-100 text-green-800 border-green-200"},"treatment-reminder":{label:"治疗提醒",icon:m.VS,color:"bg-purple-100 text-purple-800 border-purple-200"},"billing-follow-up":{label:"账单跟进",icon:m.Bl,color:"bg-yellow-100 text-yellow-800 border-yellow-200"},"medical-record-update":{label:"病历更新",icon:m.vg,color:"bg-indigo-100 text-indigo-800 border-indigo-200"},"consultation-follow-up":{label:"咨询跟进",icon:m.bY,color:"bg-orange-100 text-orange-800 border-orange-200"}};function D(e){let{patientId:t,timeline:a,loading:r=!1,onItemClick:c,onEditItem:f,className:h}=e,[y,v]=(0,n.useState)(""),[j,b]=(0,n.useState)("all"),[N,w]=(0,n.useState)("all"),[k,C]=(0,n.useState)(a);(0,n.useEffect)(()=>{let e=a;"all"!==N&&(e=e.filter(e=>e.type===N)),y&&(e=e.filter(e=>{var t,a;return e.title.toLowerCase().includes(y.toLowerCase())||"interaction"===e.type&&(null==(t=e.data.outcome)?void 0:t.toLowerCase().includes(y.toLowerCase()))||"task"===e.type&&(null==(a=e.data.description)?void 0:a.toLowerCase().includes(y.toLowerCase()))})),"all"!==j&&(e=e.filter(e=>"interaction"===e.type?e.data.interactionType===j:e.data.taskType===j)),C(e)},[a,y,j,N]);let I=e=>{if("interaction"===e.type){let t=S[e.data.interactionType],a=(null==t?void 0:t.icon)||m.bY;return(0,s.jsx)(a,{className:"size-4"})}{let t=A[e.data.taskType],a=(null==t?void 0:t.icon)||m._L;return(0,s.jsx)(a,{className:"size-4"})}},T=e=>{var t,a;return"interaction"===e.type?(null==(t=S[e.data.interactionType])?void 0:t.color)||"bg-gray-100 text-gray-800 border-gray-200":(null==(a=A[e.data.taskType])?void 0:a.color)||"bg-gray-100 text-gray-800 border-gray-200"},D=e=>e?"string"==typeof e?"未知工作人员":"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||e.email:"未知";if(r)return(0,s.jsxs)(l.Zp,{className:h,children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(m.bY,{className:"size-5"}),"沟通记录"]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,s.jsx)("div",{className:"animate-pulse",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"size-8 bg-gray-200 rounded-full"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]})},t))})})]});let R=a.filter(e=>"interaction"===e.type).length,P=a.filter(e=>"task"===e.type).length;return(0,s.jsxs)(l.Zp,{className:h,"data-sentry-element":"Card","data-sentry-component":"CommunicationLog","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsxs)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsx)(m.bY,{className:"size-5","data-sentry-element":"IconMessageCircle","data-sentry-source-file":"communication-log.tsx"}),"沟通记录",(0,s.jsx)(o.E,{variant:"secondary",className:"ml-2","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:k.length})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(m.C0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground","data-sentry-element":"IconSearch","data-sentry-source-file":"communication-log.tsx"}),(0,s.jsx)(u.p,{placeholder:"搜索沟通记录...",value:y,onChange:e=>v(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"communication-log.tsx"})]}),(0,s.jsxs)(x.l6,{value:j,onValueChange:b,"data-sentry-element":"Select","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsxs)(x.bq,{className:"w-full sm:w-[140px]","data-sentry-element":"SelectTrigger","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsx)(m.uJ,{className:"size-4 mr-2","data-sentry-element":"IconFilter","data-sentry-source-file":"communication-log.tsx"}),(0,s.jsx)(x.yv,{placeholder:"类型","data-sentry-element":"SelectValue","data-sentry-source-file":"communication-log.tsx"})]}),(0,s.jsxs)(x.gC,{"data-sentry-element":"SelectContent","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsx)(x.eb,{value:"all","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"所有类型"}),(0,s.jsx)(z.Separator,{className:"my-1","data-sentry-element":"Separator","data-sentry-source-file":"communication-log.tsx"}),(0,s.jsx)(x.eb,{value:"phone-call","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"电话通话"}),(0,s.jsx)(x.eb,{value:"email","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"邮件沟通"}),(0,s.jsx)(x.eb,{value:"consultation-note","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"咨询记录"}),(0,s.jsx)(x.eb,{value:"in-person-visit","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"到院就诊"}),(0,s.jsx)(x.eb,{value:"treatment-discussion","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"治疗讨论"}),(0,s.jsx)(x.eb,{value:"billing-inquiry","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"账单咨询"}),(0,s.jsx)(z.Separator,{className:"my-1","data-sentry-element":"Separator","data-sentry-source-file":"communication-log.tsx"}),(0,s.jsx)(x.eb,{value:"follow-up-call","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"跟进电话"}),(0,s.jsx)(x.eb,{value:"appointment-scheduling","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"预约安排"}),(0,s.jsx)(x.eb,{value:"treatment-reminder","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"治疗提醒"}),(0,s.jsx)(x.eb,{value:"billing-follow-up","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"账单跟进"}),(0,s.jsx)(x.eb,{value:"medical-record-update","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"病历更新"}),(0,s.jsx)(x.eb,{value:"consultation-follow-up","data-sentry-element":"SelectItem","data-sentry-source-file":"communication-log.tsx",children:"咨询跟进"})]})]})]})]}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"communication-log.tsx",children:(0,s.jsxs)(d.tU,{value:N,onValueChange:w,className:"w-full","data-sentry-element":"Tabs","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsxs)(d.j7,{className:"grid w-full grid-cols-3","data-sentry-element":"TabsList","data-sentry-source-file":"communication-log.tsx",children:[(0,s.jsxs)(d.Xi,{value:"all","data-sentry-element":"TabsTrigger","data-sentry-source-file":"communication-log.tsx",children:["全部 (",a.length,")"]}),(0,s.jsxs)(d.Xi,{value:"interaction","data-sentry-element":"TabsTrigger","data-sentry-source-file":"communication-log.tsx",children:["互动 (",R,")"]}),(0,s.jsxs)(d.Xi,{value:"task","data-sentry-element":"TabsTrigger","data-sentry-source-file":"communication-log.tsx",children:["任务 (",P,")"]})]}),(0,s.jsx)(d.av,{value:N,className:"mt-4","data-sentry-element":"TabsContent","data-sentry-source-file":"communication-log.tsx",children:(0,s.jsx)(p.ScrollArea,{className:"h-[600px]","data-sentry-element":"ScrollArea","data-sentry-source-file":"communication-log.tsx",children:0===k.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(m.bY,{className:"size-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"暂无沟通记录"}),(0,s.jsx)("p",{className:"text-sm",children:"开始记录与患者的互动和任务"})]}):(0,s.jsx)("div",{className:"space-y-4",children:k.map((e,t)=>{let a="interaction"===e.type,n=e.data,r=T(e);return(0,s.jsxs)("div",{className:"relative","data-sentry-component":"renderTimelineItem","data-sentry-source-file":"communication-log.tsx",children:[t<k.length-1&&(0,s.jsx)("div",{className:"absolute left-4 top-12 bottom-0 w-px bg-border"}),(0,s.jsxs)("div",{className:(0,g.cn)("flex items-start gap-3 p-4 rounded-lg border transition-colors",c&&"cursor-pointer hover:bg-muted/50"),onClick:()=>null==c?void 0:c(e),children:[(0,s.jsx)("div",{className:(0,g.cn)("flex items-center justify-center size-8 rounded-full border-2 flex-shrink-0",r),children:I(e)}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between gap-2 mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-sm leading-tight mb-1",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,s.jsx)(o.E,{variant:"outline",className:"text-xs","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:a?"互动":"任务"}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.vg,{className:"size-3","data-sentry-element":"IconUser","data-sentry-source-file":"communication-log.tsx"}),a?D(e.staffMember):"分配给: ".concat(D(e.assignedTo))]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,s.jsxs)(o.E,{variant:"outline",className:"completed"===e.status||"resolved"===e.status||"closed"===e.status?"bg-green-100 text-green-800":"in-progress"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:["open"===e.status&&"开放","in-progress"===e.status&&"进行中","resolved"===e.status&&"已解决","closed"===e.status&&"已关闭","pending"===e.status&&"待处理","completed"===e.status&&"已完成","cancelled"===e.status&&"已取消"]}),(0,s.jsxs)(o.E,{variant:"outline",className:"urgent"===e.priority||"high"===e.priority?"bg-red-100 text-red-800":"medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-600","data-sentry-element":"Badge","data-sentry-source-file":"communication-log.tsx",children:["low"===e.priority&&"低","medium"===e.priority&&"中","high"===e.priority&&"高","urgent"===e.priority&&"紧急"]})]})]}),a?(0,s.jsxs)("div",{children:[n.outcome&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-2",children:n.outcome}),n.followUpRequired&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded mb-2",children:[(0,s.jsx)(m.hI,{className:"size-3"}),"需要跟进",n.followUpDate&&(0,s.jsxs)("span",{children:["- ",(0,g.r6)(new Date(n.followUpDate))]})]})]}):(0,s.jsxs)("div",{children:[n.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2 mb-2",children:n.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(m._v,{className:"size-3"}),"截止: ",(0,g.r6)(new Date(n.dueDate))]}),n.completedAt&&(0,s.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,s.jsx)(m._L,{className:"size-3"}),"完成: ",n.completedAt?(0,g.r6)(new Date(n.completedAt)):"未完成"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between mt-3",children:[(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[(0,g.fw)(new Date(e.timestamp))," • ",(0,g.r6)(new Date(e.timestamp))]}),(c||f)&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[c&&(0,s.jsxs)(i.$,{size:"sm",variant:"ghost",onClick:t=>{t.stopPropagation(),c(e)},className:"h-6 px-2 text-xs",children:[(0,s.jsx)(m.Hv,{className:"size-3 mr-1"}),"查看"]}),f&&(0,s.jsxs)(i.$,{size:"sm",variant:"ghost",onClick:t=>{t.stopPropagation(),f(e)},className:"h-6 px-2 text-xs",children:[(0,s.jsx)(m.GI,{className:"size-3 mr-1"}),"编辑"]})]})]})]})]})]},e.id)})})})})]})})]})}var R=a(38406),P=a(90290),B=a(73259),F=a(77362),q=a(56420),_=a(25192),E=a(40153),L=a(39372),M=a(34901);let V=B.z.object({interactionType:B.z.enum(["phone-call","email","consultation-note","in-person-visit","treatment-discussion","billing-inquiry"]),title:B.z.string().min(1,"请输入互动标题").max(200,"标题不能超过200个字符"),notes:B.z.string().min(1,"请输入详细记录"),outcome:B.z.string().optional(),followUpRequired:B.z.boolean().default(!1),followUpDate:B.z.string().optional(),priority:B.z.enum(["low","medium","high"]).default("medium"),status:B.z.enum(["open","in-progress","resolved","closed"]).default("open"),relatedAppointment:B.z.string().optional(),relatedBill:B.z.string().optional()}),U=[{value:"phone-call",label:"电话通话",description:"记录与患者的电话沟通"},{value:"email",label:"邮件沟通",description:"记录邮件往来内容"},{value:"consultation-note",label:"咨询记录",description:"记录咨询或诊疗过程"},{value:"in-person-visit",label:"到院就诊",description:"记录患者到院就诊情况"},{value:"treatment-discussion",label:"治疗讨论",description:"记录治疗方案讨论"},{value:"billing-inquiry",label:"账单咨询",description:"记录账单相关咨询"}],O=[{value:"low",label:"低",description:"一般重要性"},{value:"medium",label:"中",description:"中等重要性"},{value:"high",label:"高",description:"高重要性，需要优先处理"}],J=[{value:"open",label:"开放",description:"新创建的互动记录"},{value:"in-progress",label:"进行中",description:"正在处理中"},{value:"resolved",label:"已解决",description:"问题已解决"},{value:"closed",label:"已关闭",description:"互动已结束"}];function $(e){let{open:t,onOpenChange:a,patientId:r,patientName:l,interaction:o,defaultType:d,onSubmit:c,loading:p=!1}=e,[f,h]=(0,n.useState)(),[y,v]=(0,n.useState)("09:00"),j=!!o,b=(0,R.mN)({resolver:(0,P.u)(V),defaultValues:{interactionType:d||"phone-call",title:"",notes:"",outcome:"",followUpRequired:!1,followUpDate:"",priority:"medium",status:"open",relatedAppointment:"",relatedBill:""}});(0,n.useEffect)(()=>{if(t)if(o){if(b.reset({interactionType:o.interactionType,title:o.title,notes:o.notes,outcome:o.outcome||"",followUpRequired:o.followUpRequired,followUpDate:o.followUpDate||"",priority:o.priority,status:o.status,relatedAppointment:"string"==typeof o.relatedAppointment?o.relatedAppointment:"",relatedBill:"string"==typeof o.relatedBill?o.relatedBill:""}),o.followUpDate){let e=new Date(o.followUpDate);h(e),v(e.toTimeString().slice(0,5))}}else b.reset({interactionType:d||"phone-call",title:"",notes:"",outcome:"",followUpRequired:!1,followUpDate:"",priority:"medium",status:"open",relatedAppointment:"",relatedBill:""}),h(void 0),v("09:00")},[t,o,d,b]);let N=async e=>{try{if(e.followUpRequired&&f){let[t,a]=y.split(":"),s=new Date(f);s.setHours(parseInt(t),parseInt(a)),e.followUpDate=s.toISOString()}let t={...e,patient:r};await c(t),a(!1)}catch(e){console.error("Error submitting interaction:",e)}},w=b.watch("followUpRequired");return(0,s.jsx)(F.lG,{open:t,onOpenChange:a,"data-sentry-element":"Dialog","data-sentry-component":"InteractionFormDialog","data-sentry-source-file":"interaction-form-dialog.tsx",children:(0,s.jsxs)(F.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"interaction-form-dialog.tsx",children:[(0,s.jsxs)(F.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"interaction-form-dialog.tsx",children:[(0,s.jsx)(F.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"interaction-form-dialog.tsx",children:j?"编辑互动记录":"添加互动记录"}),(0,s.jsxs)(F.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"interaction-form-dialog.tsx",children:["为患者 ",(0,s.jsx)("strong",{children:l})," ",j?"编辑":"创建","互动记录"]})]}),(0,s.jsx)(q.lV,{...b,"data-sentry-element":"Form","data-sentry-source-file":"interaction-form-dialog.tsx",children:(0,s.jsxs)("form",{onSubmit:b.handleSubmit(N),className:"space-y-6",children:[(0,s.jsx)(q.zB,{control:b.control,name:"interactionType",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"互动类型 *"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"选择互动类型"})})}),(0,s.jsx)(x.gC,{children:U.map(e=>(0,s.jsx)(x.eb,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:b.control,name:"title",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"互动标题 *"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)(u.p,{placeholder:"简要描述此次互动的主题",...t})}),(0,s.jsx)(q.Rr,{children:"请输入简洁明了的标题，方便后续查找"}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:b.control,name:"notes",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"详细记录 *"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)(_.T,{placeholder:"详细记录互动内容、讨论要点、患者反馈等...",className:"min-h-[120px]",...t})}),(0,s.jsx)(q.Rr,{children:"请详细记录互动的具体内容和重要信息"}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:b.control,name:"outcome",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"互动结果"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)(_.T,{placeholder:"记录互动的结果、解决方案或后续安排...",className:"min-h-[80px]",...t})}),(0,s.jsx)(q.Rr,{children:"记录此次互动达成的结果或解决方案"}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(q.zB,{control:b.control,name:"priority",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"优先级"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{})})}),(0,s.jsx)(x.gC,{children:O.map(e=>(0,s.jsx)(x.eb,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:b.control,name:"status",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"状态"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{})})}),(0,s.jsx)(x.gC,{children:J.map(e=>(0,s.jsx)(x.eb,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(q.zB,{control:b.control,name:"followUpRequired",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(E.S,{checked:t.value,onCheckedChange:t.onChange})}),(0,s.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,s.jsx)(q.lR,{children:"需要跟进"}),(0,s.jsx)(q.Rr,{children:"勾选此项将自动创建跟进任务"})]})]})},"data-sentry-element":"FormField","data-sentry-source-file":"interaction-form-dialog.tsx"}),w&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(q.lR,{children:"跟进日期"}),(0,s.jsxs)(M.AM,{children:[(0,s.jsx)(M.Wv,{asChild:!0,children:(0,s.jsxs)(i.$,{variant:"outline",className:(0,g.cn)("w-full justify-start text-left font-normal",!f&&"text-muted-foreground"),children:[(0,s.jsx)(m._v,{className:"mr-2 h-4 w-4"}),f?(0,g.r6)(f):"选择日期"]})}),(0,s.jsx)(M.hl,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(L.V,{mode:"single",selected:f,onSelect:h,disabled:e=>e<new Date,initialFocus:!0})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(q.lR,{children:"跟进时间"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m._L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(u.p,{type:"time",value:y,onChange:e=>v(e.target.value),className:"pl-10"})]})]})]})]}),(0,s.jsxs)(F.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"interaction-form-dialog.tsx",children:[(0,s.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:p,"data-sentry-element":"Button","data-sentry-source-file":"interaction-form-dialog.tsx",children:"取消"}),(0,s.jsx)(i.$,{type:"submit",disabled:p,"data-sentry-element":"Button","data-sentry-source-file":"interaction-form-dialog.tsx",children:p?"保存中...":j?"更新":"创建"})]})]})})]})})}let H=B.z.object({taskType:B.z.enum(["follow-up-call","appointment-scheduling","treatment-reminder","billing-follow-up","medical-record-update","consultation-follow-up"]),title:B.z.string().min(1,"请输入任务标题").max(200,"标题不能超过200个字符"),description:B.z.string().optional(),assignedTo:B.z.string().min(1,"请选择负责人"),dueDate:B.z.string().min(1,"请选择截止日期"),priority:B.z.enum(["low","medium","high","urgent"]).default("medium"),status:B.z.enum(["pending","in-progress","completed","cancelled"]).default("pending"),relatedInteraction:B.z.string().optional(),completionNotes:B.z.string().optional()}),Z=[{value:"follow-up-call",label:"跟进电话",description:"安排跟进电话任务"},{value:"appointment-scheduling",label:"预约安排",description:"安排预约相关任务"},{value:"treatment-reminder",label:"治疗提醒",description:"创建治疗提醒任务"},{value:"billing-follow-up",label:"账单跟进",description:"创建账单跟进任务"},{value:"medical-record-update",label:"病历更新",description:"安排病历更新任务"},{value:"consultation-follow-up",label:"咨询跟进",description:"创建咨询跟进任务"}],G=[{value:"low",label:"低",description:"一般重要性",color:"text-gray-600"},{value:"medium",label:"中",description:"中等重要性",color:"text-yellow-600"},{value:"high",label:"高",description:"高重要性，需要优先处理",color:"text-orange-600"},{value:"urgent",label:"紧急",description:"紧急任务，需要立即处理",color:"text-red-600"}],W=[{value:"pending",label:"待处理",description:"新创建的任务"},{value:"in-progress",label:"进行中",description:"正在处理中"},{value:"completed",label:"已完成",description:"任务已完成"},{value:"cancelled",label:"已取消",description:"任务已取消"}];function Y(e){let{open:t,onOpenChange:a,patientId:r,patientName:l,task:o,defaultType:d,relatedInteractionId:c,availableStaff:p,onSubmit:f,loading:h=!1}=e,[y,v]=(0,n.useState)(),[j,b]=(0,n.useState)("09:00"),N=!!o,w=(0,R.mN)({resolver:(0,P.u)(H),defaultValues:{taskType:d||"follow-up-call",title:"",description:"",assignedTo:"",dueDate:"",priority:"medium",status:"pending",relatedInteraction:c||"",completionNotes:""}});(0,n.useEffect)(()=>{if(t)if(o){var e;w.reset({taskType:o.taskType,title:o.title,description:o.description||"",assignedTo:"string"==typeof o.assignedTo?o.assignedTo:o.assignedTo.id,dueDate:o.dueDate,priority:o.priority,status:o.status,relatedInteraction:"string"==typeof o.relatedInteraction?o.relatedInteraction:(null==(e=o.relatedInteraction)?void 0:e.id)||"",completionNotes:o.completionNotes||""});let t=new Date(o.dueDate);v(t),b(t.toTimeString().slice(0,5))}else{let e=new Date;e.setDate(e.getDate()+1),v(e),w.reset({taskType:d||"follow-up-call",title:"",description:"",assignedTo:"",dueDate:"",priority:"medium",status:"pending",relatedInteraction:c||"",completionNotes:""}),b("09:00")}},[t,o,d,c,w]);let k=async e=>{try{if(y){let[t,a]=j.split(":"),s=new Date(y);s.setHours(parseInt(t),parseInt(a)),e.dueDate=s.toISOString()}let t={...e,patient:r};await f(t),a(!1)}catch(e){console.error("Error submitting task:",e)}},C=w.watch("status"),I=w.watch("taskType");return(0,n.useEffect)(()=>{if(!N&&I){let e=Z.find(e=>e.value===I);e&&w.setValue("title","".concat(e.label," - ").concat(l))}},[I,l,N,w]),(0,s.jsx)(F.lG,{open:t,onOpenChange:a,"data-sentry-element":"Dialog","data-sentry-component":"TaskFormDialog","data-sentry-source-file":"task-form-dialog.tsx",children:(0,s.jsxs)(F.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto","data-sentry-element":"DialogContent","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,s.jsxs)(F.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,s.jsx)(F.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"task-form-dialog.tsx",children:N?"编辑任务":"创建任务"}),(0,s.jsxs)(F.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"task-form-dialog.tsx",children:["为患者 ",(0,s.jsx)("strong",{children:l})," ",N?"编辑":"创建","跟进任务"]})]}),(0,s.jsx)(q.lV,{...w,"data-sentry-element":"Form","data-sentry-source-file":"task-form-dialog.tsx",children:(0,s.jsxs)("form",{onSubmit:w.handleSubmit(k),className:"space-y-6",children:[(0,s.jsx)(q.zB,{control:w.control,name:"taskType",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"任务类型 *"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"选择任务类型"})})}),(0,s.jsx)(x.gC,{children:Z.map(e=>(0,s.jsx)(x.eb,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:w.control,name:"title",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"任务标题 *"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)(u.p,{placeholder:"简要描述任务内容",...t})}),(0,s.jsx)(q.Rr,{children:"请输入简洁明了的任务标题"}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:w.control,name:"description",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"任务描述"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)(_.T,{placeholder:"详细描述任务要求和注意事项...",className:"min-h-[100px]",...t})}),(0,s.jsx)(q.Rr,{children:"详细描述任务的具体要求和执行步骤"}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:w.control,name:"assignedTo",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"分配给 *"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"选择负责人",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.vg,{className:"size-4"}),(0,s.jsx)("span",{children:"选择负责人"})]})})})}),(0,s.jsx)(x.gC,{children:p.map(e=>(0,s.jsx)(x.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.vg,{className:"size-4"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:"".concat(e.firstName||""," ").concat(e.lastName||"").trim()||e.email}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground",children:["admin"===e.role&&"管理员","doctor"===e.role&&"医生","front-desk"===e.role&&"前台"]})]})]})},e.id))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(q.lR,{"data-sentry-element":"FormLabel","data-sentry-source-file":"task-form-dialog.tsx",children:"截止日期 *"}),(0,s.jsxs)(M.AM,{"data-sentry-element":"Popover","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,s.jsx)(M.Wv,{asChild:!0,"data-sentry-element":"PopoverTrigger","data-sentry-source-file":"task-form-dialog.tsx",children:(0,s.jsxs)(i.$,{variant:"outline",className:(0,g.cn)("w-full justify-start text-left font-normal",!y&&"text-muted-foreground"),"data-sentry-element":"Button","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,s.jsx)(m._v,{className:"mr-2 h-4 w-4","data-sentry-element":"IconCalendar","data-sentry-source-file":"task-form-dialog.tsx"}),y?(0,g.r6)(y):"选择日期"]})}),(0,s.jsx)(M.hl,{className:"w-auto p-0",align:"start","data-sentry-element":"PopoverContent","data-sentry-source-file":"task-form-dialog.tsx",children:(0,s.jsx)(L.V,{mode:"single",selected:y,onSelect:v,disabled:e=>e<new Date,initialFocus:!0,"data-sentry-element":"Calendar","data-sentry-source-file":"task-form-dialog.tsx"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(q.lR,{"data-sentry-element":"FormLabel","data-sentry-source-file":"task-form-dialog.tsx",children:"截止时间"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(m._L,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground","data-sentry-element":"IconClock","data-sentry-source-file":"task-form-dialog.tsx"}),(0,s.jsx)(u.p,{type:"time",value:j,onChange:e=>b(e.target.value),className:"pl-10","data-sentry-element":"Input","data-sentry-source-file":"task-form-dialog.tsx"})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(q.zB,{control:w.control,name:"priority",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"优先级"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{})})}),(0,s.jsx)(x.gC,{children:G.map(e=>(0,s.jsx)(x.eb,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:(0,g.cn)("font-medium",e.color),children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"}),(0,s.jsx)(q.zB,{control:w.control,name:"status",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"状态"}),(0,s.jsxs)(x.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,s.jsx)(q.MJ,{children:(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{})})}),(0,s.jsx)(x.gC,{children:W.map(e=>(0,s.jsx)(x.eb,{value:e.value,children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.label}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})},e.value))})]}),(0,s.jsx)(q.C5,{})]})},"data-sentry-element":"FormField","data-sentry-source-file":"task-form-dialog.tsx"})]}),"completed"===C&&(0,s.jsx)(q.zB,{control:w.control,name:"completionNotes",render:e=>{let{field:t}=e;return(0,s.jsxs)(q.eI,{children:[(0,s.jsx)(q.lR,{children:"完成备注"}),(0,s.jsx)(q.MJ,{children:(0,s.jsx)(_.T,{placeholder:"记录任务完成情况和总结...",className:"min-h-[80px]",...t})}),(0,s.jsx)(q.Rr,{children:"记录任务完成的具体情况和总结"}),(0,s.jsx)(q.C5,{})]})}}),(0,s.jsxs)(F.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"task-form-dialog.tsx",children:[(0,s.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>a(!1),disabled:h,"data-sentry-element":"Button","data-sentry-source-file":"task-form-dialog.tsx",children:"取消"}),(0,s.jsx)(i.$,{type:"submit",disabled:h,"data-sentry-element":"Button","data-sentry-source-file":"task-form-dialog.tsx",children:h?"保存中...":N?"更新":"创建"})]})]})})]})})}var Q=a(18580),X=a(87905),K=a(4629);let ee={interaction:{created:e=>{let t={"phone-call":"电话通话",email:"邮件沟通","consultation-note":"咨询记录","in-person-visit":"到院就诊","treatment-discussion":"治疗讨论","billing-inquiry":"账单咨询"}[e.interactionType]||"互动记录";K.toast.success("".concat(t,"创建成功！"),{description:"标题: ".concat(e.title),duration:4e3})},updated:e=>{K.toast.success("互动记录更新成功！",{description:"标题: ".concat(e.title),duration:4e3})},deleted:e=>{K.toast.success("互动记录删除成功！",{description:"已删除: ".concat(e),duration:4e3})},followUpCreated:e=>{K.toast.info("跟进任务已自动创建",{description:"基于互动: ".concat(e.title),duration:5e3})},statusChanged:(e,t,a)=>{let s={open:"开放","in-progress":"进行中",resolved:"已解决",closed:"已关闭"};K.toast.success("互动状态已更新",{description:"".concat(e.title,": ").concat(s[t]," → ").concat(s[a]),duration:4e3})}},task:{created:e=>{let t={"follow-up-call":"跟进电话","appointment-scheduling":"预约安排","treatment-reminder":"治疗提醒","billing-follow-up":"账单跟进","medical-record-update":"病历更新","consultation-follow-up":"咨询跟进"}[e.taskType]||"任务";K.toast.success("".concat(t,"任务创建成功！"),{description:"标题: ".concat(e.title),duration:4e3})},updated:e=>{K.toast.success("任务更新成功！",{description:"标题: ".concat(e.title),duration:4e3})},deleted:e=>{K.toast.success("任务删除成功！",{description:"已删除: ".concat(e),duration:4e3})},assigned:(e,t)=>{let a="".concat(t.firstName||""," ").concat(t.lastName||"").trim()||t.email;K.toast.info("任务已分配",{description:"".concat(e.title," → ").concat(a),duration:4e3})},statusChanged:(e,t,a)=>{let s={pending:"待处理","in-progress":"进行中",completed:"已完成",cancelled:"已取消"};K.toast.success("任务状态已更新",{description:"".concat({pending:"⏳","in-progress":"\uD83D\uDD04",completed:"✅",cancelled:"❌"}[a]," ").concat(e.title,": ").concat(s[t]," → ").concat(s[a]),duration:4e3})},completed:e=>{K.toast.success("\uD83C\uDF89 任务完成！",{description:"".concat(e.title," 已标记为完成"),duration:5e3})},overdue:e=>{K.toast.warning("⚠️ 任务已逾期",{description:"".concat(e.title," - 截止时间: ").concat((0,g.r6)(new Date(e.dueDate))),duration:8e3})},reminder:(e,t)=>{let a=t<60?"".concat(t," 分钟后"):"".concat(Math.floor(t/60)," 小时后");K.toast.info("\uD83D\uDCC5 任务提醒",{description:"".concat(e.title," 将在 ").concat(a," 到期"),duration:6e3})}},error:{interactionCreateFailed:e=>{K.toast.error("互动记录创建失败",{description:e||"请检查网络连接后重试",duration:5e3})},taskCreateFailed:e=>{K.toast.error("任务创建失败",{description:e||"请检查网络连接后重试",duration:5e3})},updateFailed:(e,t)=>{K.toast.error("".concat({interaction:"互动记录",task:"任务"}[e]||"项目","更新失败"),{description:t||"请检查网络连接后重试",duration:5e3})},deleteFailed:(e,t)=>{K.toast.error("".concat({interaction:"互动记录",task:"任务"}[e]||"项目","删除失败"),{description:t||"请检查网络连接后重试",duration:5e3})},loadFailed:(e,t)=>{K.toast.error("".concat({interactions:"互动记录",tasks:"任务",timeline:"时间线"}[e]||"数据","加载失败"),{description:t||"请检查网络连接后重试",duration:5e3})},permissionDenied:e=>{K.toast.error("权限不足",{description:"您没有权限执行: ".concat(e),duration:5e3})},networkError:()=>{K.toast.error("网络连接失败",{description:"请检查网络连接后重试",duration:5e3})}},success:{dataRefreshed:e=>{K.toast.success("".concat({interactions:"互动记录",tasks:"任务",timeline:"时间线"}[e]||"数据","刷新成功"),{duration:2e3})},syncCompleted:()=>{K.toast.success("数据同步完成",{description:"所有CRM数据已同步到最新状态",duration:3e3})}}},et=e=>K.toast.loading(e,{duration:1/0});function ea(e){var t;let{patientId:a}=e,u=(0,r.useRouter)(),{user:x}=(0,X.Jd)(),[p,f]=(0,n.useState)(null),[h,y]=(0,n.useState)([]),[j,b]=(0,n.useState)([]),[N,k]=(0,n.useState)([]),[C,I]=(0,n.useState)([]),[z,S]=(0,n.useState)(!0),[A,R]=(0,n.useState)("overview"),[P,B]=(0,n.useState)(!1),[F,q]=(0,n.useState)(!1),[_,E]=(0,n.useState)(""),[L,M]=(0,n.useState)(""),[V,U]=(0,n.useState)(),[O,J]=(0,n.useState)(),[H,Z]=(0,n.useState)(!1);(0,n.useEffect)(()=>{(async()=>{if(!x)return;let e=et("加载患者信息...");try{var t;let e=(0,Q.o)({clerkId:x.id,email:(null==(t=x.emailAddresses[0])?void 0:t.emailAddress)||"",firstName:x.firstName||"",lastName:x.lastName||""}),s=await e.getPatient(a);f(s);let n=await e.getPatientInteractionsByPatient(a,{limit:50});y(n.docs||[]);let r=await e.getPatientTasksByPatient(a,{limit:50});b(r.docs||[]);let l=await e.getPatientTimeline(a,{limit:100});k(l.docs||[]);let i=await e.getUsers({limit:50});I(i.docs||[]),ee.success.dataRefreshed("患者信息")}catch(e){console.error("Error loading patient data:",e),ee.error.loadFailed("患者信息",e instanceof Error?e.message:void 0)}finally{S(!1),K.toast.dismiss(e)}})()},[a,x]);let G=e=>{E(e||""),U(void 0),B(!0)},W=e=>{M(e||""),J(void 0),q(!0)},ea=async e=>{Z(!0);try{var t;let s=(0,Q.o)({clerkId:x.id,email:(null==(t=x.emailAddresses[0])?void 0:t.emailAddress)||"",firstName:x.firstName||"",lastName:x.lastName||""});if(V){let t=await s.updatePatientInteraction(V.id,e);y(e=>e.map(e=>e.id===V.id?t:e)),ee.interaction.updated(t)}else{let t=await s.createPatientInteraction(e);y(e=>[t,...e]),ee.interaction.created(t)}let n=await s.getPatientTimeline(a,{limit:100});k(n.docs||[])}catch(e){console.error("Error submitting interaction:",e),ee.error.interactionCreateFailed(e instanceof Error?e.message:void 0)}finally{Z(!1)}},es=async e=>{Z(!0);try{var t;let s=(0,Q.o)({clerkId:x.id,email:(null==(t=x.emailAddresses[0])?void 0:t.emailAddress)||"",firstName:x.firstName||"",lastName:x.lastName||""});if(O){let t=await s.updatePatientTask(O.id,e);b(e=>e.map(e=>e.id===O.id?t:e)),ee.task.updated(t)}else{let t=await s.createPatientTask(e);b(e=>[t,...e]),ee.task.created(t)}let n=await s.getPatientTimeline(a,{limit:100});k(n.docs||[])}catch(e){console.error("Error submitting task:",e),ee.error.taskCreateFailed(e instanceof Error?e.message:void 0)}finally{Z(!1)}},en=()=>{u.push("/dashboard/appointments/new?patientId=".concat(a))},er=()=>{u.push("/dashboard/billing?patientId=".concat(a))},el=async(e,t)=>{try{var a;let s=(0,Q.o)({clerkId:x.id,email:(null==(a=x.emailAddresses[0])?void 0:a.emailAddress)||"",firstName:x.firstName||"",lastName:x.lastName||""});await s.updatePatientTask(e,{status:t}),b(a=>a.map(a=>a.id===e?{...a,status:t}:a));let n=j.find(t=>t.id===e);n&&ee.task.statusChanged(n,n.status,t)}catch(e){console.error("Error updating task status:",e),ee.error.updateFailed("task",e instanceof Error?e.message:void 0)}};if(z)return(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-4"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-96 bg-gray-200 rounded"})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-48 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]})});if(!p)return(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(m.vg,{className:"size-12 mx-auto mb-4 text-muted-foreground"}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"患者未找到"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"请检查患者ID是否正确"}),(0,s.jsxs)(i.$,{onClick:()=>u.back(),children:[(0,s.jsx)(m.Gk,{className:"size-4 mr-2"}),"返回"]})]});let ei=h.length,eo=j.filter(e=>"pending"===e.status).length,ed=j.filter(e=>"completed"!==e.status&&new Date(e.dueDate)<new Date).length,ec=h[0];return(0,s.jsxs)("div",{className:"space-y-6","data-sentry-component":"PatientDetailView","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(i.$,{variant:"ghost",size:"sm",onClick:()=>u.back(),"data-sentry-element":"Button","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(m.Gk,{className:"size-4 mr-2","data-sentry-element":"IconArrowLeft","data-sentry-source-file":"patient-detail-view.tsx"}),"返回"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:p.fullName}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"患者详细信息"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(T,{patient:p,onCreateInteraction:G,onCreateTask:W,onScheduleAppointment:en,onViewBilling:er,compact:!0,"data-sentry-element":"QuickActions","data-sentry-source-file":"patient-detail-view.tsx"}),(0,s.jsxs)(i.$,{variant:"outline",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(m.GI,{className:"size-4 mr-2","data-sentry-element":"IconEdit","data-sentry-source-file":"patient-detail-view.tsx"}),"编辑"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(d.tU,{value:A,onValueChange:R,className:"w-full","data-sentry-element":"Tabs","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsxs)(d.j7,{className:"grid w-full grid-cols-4","data-sentry-element":"TabsList","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(d.Xi,{value:"overview","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:"概览"}),(0,s.jsxs)(d.Xi,{value:"interactions","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:["互动记录 (",ei,")"]}),(0,s.jsxs)(d.Xi,{value:"tasks","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:["任务管理 (",j.length,")"]}),(0,s.jsx)(d.Xi,{value:"timeline","data-sentry-element":"TabsTrigger","data-sentry-source-file":"patient-detail-view.tsx",children:"时间线"})]}),(0,s.jsxs)(d.av,{value:"overview",className:"space-y-6","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(m.vg,{className:"size-5","data-sentry-element":"IconUser","data-sentry-source-file":"patient-detail-view.tsx"}),"基本信息"]})}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsxs)(c.Avatar,{className:"size-16","data-sentry-element":"Avatar","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(c.AvatarImage,{src:"object"==typeof p.photo?null==(t=p.photo)?void 0:t.url:void 0,alt:p.fullName,"data-sentry-element":"AvatarImage","data-sentry-source-file":"patient-detail-view.tsx"}),(0,s.jsx)(c.AvatarFallback,{className:"text-lg","data-sentry-element":"AvatarFallback","data-sentry-source-file":"patient-detail-view.tsx",children:p.fullName.slice(0,2).toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.st,{className:"size-4 text-muted-foreground","data-sentry-element":"IconPhone","data-sentry-source-file":"patient-detail-view.tsx"}),(0,s.jsx)("span",{className:"font-medium",children:p.phone})]}),p.email&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.jQ,{className:"size-4 text-muted-foreground"}),(0,s.jsx)("span",{children:p.email})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m._v,{className:"size-4 text-muted-foreground","data-sentry-element":"IconCalendar","data-sentry-source-file":"patient-detail-view.tsx"}),(0,s.jsxs)("span",{children:["创建时间: ",(0,g.r6)(new Date(p.createdAt))]})]}),p.lastVisit&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m._L,{className:"size-4 text-muted-foreground"}),(0,s.jsxs)("span",{children:["最后就诊: ",(0,g.r6)(new Date(p.lastVisit))]})]})]}),p.userType&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(o.E,{variant:"patient"===p.userType?"default":"secondary",children:"patient"===p.userType?"正式患者":"咨询用户"}),p.status&&(0,s.jsxs)(o.E,{variant:"outline",children:["active"===p.status&&"活跃","inactive"===p.status&&"非活跃","converted"===p.status&&"已转换"]})]})]})]})})]}),(0,s.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(m.$7,{className:"size-5","data-sentry-element":"IconActivity","data-sentry-source-file":"patient-detail-view.tsx"}),"活动概要"]})}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:ei}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"总互动次数"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-yellow-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:eo}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"待处理任务"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-red-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-red-600",children:ed}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"逾期任务"})]}),(0,s.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600",children:ec?(0,g.fw)(new Date(ec.timestamp)):"无"}),(0,s.jsx)("div",{className:"text-sm text-muted-foreground",children:"最后联系"})]})]})})]})]}),(0,s.jsx)(d.av,{value:"interactions","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsx)(v,{patientId:a,interactions:h,onCreateInteraction:G,onInteractionClick:e=>console.log("View interaction:",e),"data-sentry-element":"InteractionTimeline","data-sentry-source-file":"patient-detail-view.tsx"})}),(0,s.jsx)(d.av,{value:"tasks","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsx)(w,{patientId:a,tasks:j,onCreateTask:W,onTaskClick:e=>console.log("View task:",e),onTaskStatusChange:el,viewMode:"list","data-sentry-element":"TaskManager","data-sentry-source-file":"patient-detail-view.tsx"})}),(0,s.jsx)(d.av,{value:"timeline","data-sentry-element":"TabsContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsx)(D,{patientId:a,timeline:N,onItemClick:e=>console.log("View timeline item:",e),onEditItem:e=>console.log("Edit timeline item:",e),"data-sentry-element":"CommunicationLog","data-sentry-source-file":"patient-detail-view.tsx"})})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(T,{patient:p,onCreateInteraction:G,onCreateTask:W,onScheduleAppointment:en,onViewBilling:er,"data-sentry-element":"QuickActions","data-sentry-source-file":"patient-detail-view.tsx"}),(0,s.jsxs)(l.Zp,{"data-sentry-element":"Card","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(l.aR,{"data-sentry-element":"CardHeader","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2","data-sentry-element":"CardTitle","data-sentry-source-file":"patient-detail-view.tsx",children:[(0,s.jsx)(m.as,{className:"size-5","data-sentry-element":"IconTrendingUp","data-sentry-source-file":"patient-detail-view.tsx"}),"最近活动"]})}),(0,s.jsx)(l.Wu,{"data-sentry-element":"CardContent","data-sentry-source-file":"patient-detail-view.tsx",children:(0,s.jsxs)("div",{className:"space-y-3",children:[N.slice(0,5).map(e=>(0,s.jsxs)("div",{className:"flex items-start gap-3 text-sm",children:[(0,s.jsx)("div",{className:"size-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"font-medium",children:e.title}),(0,s.jsx)("p",{className:"text-muted-foreground text-xs",children:(0,g.fw)(new Date(e.timestamp))})]})]},e.id)),0===N.length&&(0,s.jsx)("p",{className:"text-muted-foreground text-sm text-center py-4",children:"暂无活动记录"})]})})]})]})]}),(0,s.jsx)($,{open:P,onOpenChange:B,patientId:a,patientName:p.fullName,interaction:V,defaultType:_,onSubmit:ea,loading:H,"data-sentry-element":"InteractionFormDialog","data-sentry-source-file":"patient-detail-view.tsx"}),(0,s.jsx)(Y,{open:F,onOpenChange:q,patientId:a,patientName:p.fullName,task:O,defaultType:L,availableStaff:C,onSubmit:es,loading:H,"data-sentry-element":"TaskFormDialog","data-sentry-source-file":"patient-detail-view.tsx"})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[6550,6677,7905,1359,4089,4629,7131,2090,3530,2350,290,3028,8284,7630,6821,9442,4579,9253,7358],()=>t(95165)),_N_E=e.O()}]);