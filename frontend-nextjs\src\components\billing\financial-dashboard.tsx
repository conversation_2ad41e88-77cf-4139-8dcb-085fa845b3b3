'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  IconTrendingUp, 
  IconTrendingDown,
  IconCash,
  IconCreditCard,
  IconDeviceMobile,
  IconBuildingBank,
  IconAlertTriangle,
  IconRefresh,
  IconCalendar,
  IconChartBar
} from '@tabler/icons-react';
import { reportsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { useRole, PermissionGate } from '@/lib/role-context';
import { toast } from 'sonner';

interface FinancialMetrics {
  dailyRevenue: {
    date: string;
    totalRevenue: number;
    paymentCount: number;
    paymentMethods: Record<string, { amount: number; count: number }>;
  } | null;
  monthlyRevenue: {
    year: number;
    month: number;
    totalRevenue: number;
    dailyBreakdown: Array<{ date: string; revenue: number }>;
  } | null;
  outstandingBalances: {
    totalOutstanding: number;
    overdueAmount: number;
    billsCount: number;
    overdueBillsCount: number;
    bills: Array<{
      id: string;
      billNumber: string;
      patient: string;
      amount: number;
      dueDate: string;
      daysOverdue: number;
    }>;
  } | null;
}

export function FinancialDashboard() {
  const { hasPermission } = useRole();
  const [metrics, setMetrics] = useState<FinancialMetrics>({
    dailyRevenue: null,
    monthlyRevenue: null,
    outstandingBalances: null,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedMonth, setSelectedMonth] = useState({
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
  });
  const [refreshing, setRefreshing] = useState(false);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      const [dailyRevenue, monthlyRevenue, outstandingBalances] = await Promise.all([
        reportsAPI.getDailyRevenue(selectedDate).catch(() => null),
        reportsAPI.getMonthlyRevenue(selectedMonth.year, selectedMonth.month).catch(() => null),
        reportsAPI.getOutstandingBalances().catch(() => null),
      ]);

      setMetrics({
        dailyRevenue,
        monthlyRevenue,
        outstandingBalances,
      });
    } catch (err) {
      console.error('Failed to fetch financial metrics:', err);
      const errorMessage = err instanceof BillingAPIError 
        ? err.message 
        : '加载财务数据失败，请稍后重试';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, [selectedDate, selectedMonth]);

  // Check permissions after all hooks
  if (!hasPermission('canViewDetailedFinancials')) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconAlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">权限不足</h3>
          <p className="text-muted-foreground">
            您没有权限查看详细的财务报表
          </p>
        </div>
      </div>
    );
  }

  const handleRefresh = () => {
    setRefreshing(true);
    fetchMetrics();
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash':
        return IconCash;
      case 'card':
        return IconCreditCard;
      case 'wechat':
      case 'alipay':
        return IconDeviceMobile;
      case 'transfer':
        return IconBuildingBank;
      default:
        return IconCash;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载财务数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <IconChartBar className="size-6" />
            财务报表
          </h2>
          <p className="text-muted-foreground">
            查看收入统计、支付分析和应收账款
          </p>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleRefresh}
          disabled={refreshing}
        >
          <IconRefresh className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          刷新数据
        </Button>
      </div>

      {/* Date Controls */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <IconCalendar className="h-4 w-4" />
          <span className="text-sm font-medium">日期选择:</span>
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          />
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">月份选择:</span>
          <Select 
            value={`${selectedMonth.year}-${selectedMonth.month}`}
            onValueChange={(value) => {
              const [year, month] = value.split('-').map(Number);
              setSelectedMonth({ year, month });
            }}
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: 12 }, (_, i) => {
                const month = i + 1;
                const year = new Date().getFullYear();
                return (
                  <SelectItem key={`${year}-${month}`} value={`${year}-${month}`}>
                    {year}年{month}月
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex items-center">
            <IconAlertTriangle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Daily Revenue Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconTrendingUp className="h-5 w-5" />
            日收入统计
          </CardTitle>
          <CardDescription>
            {new Date(selectedDate).toLocaleDateString('zh-CN')} 的收入详情
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.dailyRevenue ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {billingUtils.formatCurrency(metrics.dailyRevenue.totalRevenue)}
                  </div>
                  <div className="text-sm text-muted-foreground">总收入</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {metrics.dailyRevenue.paymentCount}
                  </div>
                  <div className="text-sm text-muted-foreground">支付笔数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {metrics.dailyRevenue.paymentCount > 0 
                      ? billingUtils.formatCurrency(metrics.dailyRevenue.totalRevenue / metrics.dailyRevenue.paymentCount)
                      : billingUtils.formatCurrency(0)
                    }
                  </div>
                  <div className="text-sm text-muted-foreground">平均金额</div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-3">支付方式分布</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {Object.entries(metrics.dailyRevenue.paymentMethods).map(([method, data]) => {
                    const Icon = getPaymentMethodIcon(method);
                    return (
                      <div key={method} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            {billingUtils.getPaymentMethodName(method)}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-semibold">
                            {billingUtils.formatCurrency(data.amount)}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {data.count} 笔
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无当日收入数据
            </div>
          )}
        </CardContent>
      </Card>

      {/* Monthly Revenue Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconChartBar className="h-5 w-5" />
            月度收入统计
          </CardTitle>
          <CardDescription>
            {selectedMonth.year}年{selectedMonth.month}月的收入趋势
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.monthlyRevenue ? (
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">
                  {billingUtils.formatCurrency(metrics.monthlyRevenue.totalRevenue)}
                </div>
                <div className="text-sm text-muted-foreground">月度总收入</div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-3">每日收入明细</h4>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {metrics.monthlyRevenue.dailyBreakdown.map((day) => (
                    <div key={day.date} className="flex justify-between items-center p-2 hover:bg-muted/50 rounded">
                      <span className="text-sm">
                        {new Date(day.date).toLocaleDateString('zh-CN')}
                      </span>
                      <span className="font-medium">
                        {billingUtils.formatCurrency(day.revenue)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无月度收入数据
            </div>
          )}
        </CardContent>
      </Card>

      {/* Outstanding Balances Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconAlertTriangle className="h-5 w-5" />
            应收账款
          </CardTitle>
          <CardDescription>
            待收款项和逾期账单统计
          </CardDescription>
        </CardHeader>
        <CardContent>
          {metrics.outstandingBalances ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {billingUtils.formatCurrency(metrics.outstandingBalances.totalOutstanding)}
                  </div>
                  <div className="text-sm text-muted-foreground">总待收金额</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {billingUtils.formatCurrency(metrics.outstandingBalances.overdueAmount)}
                  </div>
                  <div className="text-sm text-muted-foreground">逾期金额</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {metrics.outstandingBalances.billsCount}
                  </div>
                  <div className="text-sm text-muted-foreground">待收账单</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {metrics.outstandingBalances.overdueBillsCount}
                  </div>
                  <div className="text-sm text-muted-foreground">逾期账单</div>
                </div>
              </div>

              {metrics.outstandingBalances.bills.length > 0 && (
                <>
                  <Separator />
                  <div>
                    <h4 className="font-medium mb-3">逾期账单详情</h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {metrics.outstandingBalances.bills
                        .filter(bill => bill.daysOverdue > 0)
                        .map((bill) => (
                        <div key={bill.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="font-medium text-sm">{bill.billNumber}</div>
                            <div className="text-xs text-muted-foreground">{bill.patient}</div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold text-red-600">
                              {billingUtils.formatCurrency(bill.amount)}
                            </div>
                            <Badge variant="destructive" className="text-xs">
                              逾期 {bill.daysOverdue} 天
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无应收账款数据
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
