try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="8fa51910-1186-427c-94e0-8a0efd3c1b39",e._sentryDebugIdIdentifier="sentry-dbid-8fa51910-1186-427c-94e0-8a0efd3c1b39")}catch(e){}exports.id=6166,exports.ids=[6166],exports.modules={13446:(e,t,r)=>{"use strict";r.d(t,{SR:()=>l,_O:()=>c,xC:()=>d});var n=r(55511),i=r.n(n);let a={encryption:{algorithm:"aes-256-gcm",keyLength:32,ivLength:16,tagLength:16},audit:{maxLogSize:1e4,sensitiveFields:["cardNumber","cvv","bankAccount","transactionId","ssn","idNumber"],retentionDays:365},rateLimit:{maxRequestsPerMinute:60,maxPaymentRequestsPerHour:10,maxFailedAttemptsPerHour:5,blockDurationMinutes:30}};class o{static getEncryptionKey(){let e=process.env.BILLING_ENCRYPTION_KEY;if(!e)throw Error("BILLING_ENCRYPTION_KEY environment variable is required");return e}static encrypt(e){try{let t=Buffer.from(this.getEncryptionKey(),"hex"),r=i().randomBytes(a.encryption.ivLength),n=i().createCipheriv(a.encryption.algorithm,t,r);n.setAAD(Buffer.from("billing-data"));let o=n.update(e,"utf8","hex");o+=n.final("hex");let s=n.getAuthTag();return{encrypted:o,iv:r.toString("hex"),tag:s.toString("hex")}}catch(e){throw console.error("Encryption error:",e),Error("Failed to encrypt sensitive data")}}static decrypt(e){try{let t=Buffer.from(this.getEncryptionKey(),"hex"),r=Buffer.from(e.iv,"hex"),n=Buffer.from(e.tag,"hex"),o=i().createDecipheriv(a.encryption.algorithm,t,r);o.setAAD(Buffer.from("billing-data")),o.setAuthTag(n);let s=o.update(e.encrypted,"hex","utf8");return s+=o.final("utf8")}catch(e){throw console.error("Decryption error:",e),Error("Failed to decrypt sensitive data")}}static hash(e){return i().createHash("sha256").update(e).digest("hex")}static generateSecureToken(e=32){return i().randomBytes(e).toString("hex")}}class s{constructor(){this.auditLog=[]}static getInstance(){return s.instance||(s.instance=new s),s.instance}logFinancialOperation(e,t,r,n,i,s=!0,u,l){let c={id:o.generateSecureToken(16),timestamp:new Date,userId:e,userEmail:t,action:r,resource:n,resourceId:i.id||i.billId||i.paymentId,details:this.sanitizeDetails(i),ipAddress:this.getClientIP(l),userAgent:l?.headers.get("user-agent")||void 0,success:s,errorMessage:u};this.auditLog.push(c),this.auditLog.length>a.audit.maxLogSize&&this.auditLog.shift(),console.log(`[AUDIT] ${c.timestamp.toISOString()} - ${c.action} on ${c.resource} by ${c.userEmail} - ${c.success?"SUCCESS":"FAILED"}`)}getAuditLog(e,t=100){let r=this.auditLog;return e&&(r=r.filter(t=>t.userId===e)),r.slice(-t).map(e=>({...e,details:this.sanitizeDetails(e.details)}))}sanitizeDetails(e){if(!e||"object"!=typeof e)return e;let t={...e};return a.audit.sensitiveFields.forEach(e=>{t[e]&&(t[e]=this.maskSensitiveData(t[e]))}),t}maskSensitiveData(e){return e.length<=4?"****":e.substring(0,2)+"*".repeat(e.length-4)+e.substring(e.length-2)}getClientIP(e){if(!e)return;let t=e.headers.get("x-forwarded-for");if(t)return t.split(",")[0].trim();let r=e.headers.get("x-real-ip");return r||"unknown"}}class u{constructor(){this.requestCounts=new Map,this.paymentCounts=new Map}static getInstance(){return u.instance||(u.instance=new u),u.instance}checkRateLimit(e,t=!1){let r=Date.now(),n=t?{max:a.rateLimit.maxPaymentRequestsPerHour,window:36e5}:{max:a.rateLimit.maxRequestsPerMinute,window:6e4},i=t?this.paymentCounts:this.requestCounts,o=i.get(e);return!o||r>o.resetTime?(i.set(e,{count:1,resetTime:r+n.window}),{allowed:!0}):o.count>=n.max?{allowed:!1,resetTime:o.resetTime}:(o.count++,{allowed:!0})}cleanup(){let e=Date.now(),t=[];this.requestCounts.forEach((r,n)=>{e>r.resetTime&&t.push(n)}),t.forEach(e=>this.requestCounts.delete(e));let r=[];this.paymentCounts.forEach((t,n)=>{e>t.resetTime&&r.push(n)}),r.forEach(e=>this.paymentCounts.delete(e))}}class l{static sanitizeAmount(e){if("number"==typeof e){if(!isFinite(e)||e<0)throw Error("Invalid amount: must be a positive finite number");return Math.round(100*e)/100}if("string"==typeof e){let t=parseFloat(e.replace(/[^\d.-]/g,""));if(isNaN(t)||t<0)throw Error("Invalid amount: must be a positive number");return Math.round(100*t)/100}throw Error("Invalid amount: must be a number or numeric string")}static sanitizeText(e,t=1e3){if("string"!=typeof e)throw Error("Input must be a string");let r=e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/<[^>]*>/g,"").replace(/javascript:/gi,"").replace(/on\w+\s*=/gi,"").trim();if(r.length>t)throw Error(`Input too long: maximum ${t} characters allowed`);return r}static sanitizePaymentMethod(e){if(!["cash","card","wechat","alipay","transfer","installment"].includes(e))throw Error("Invalid payment method");return e}static sanitizeTransactionId(e){if("string"!=typeof e)throw Error("Transaction ID must be a string");let t=e.replace(/[^a-zA-Z0-9\-_]/g,"");if(t.length<3||t.length>100)throw Error("Transaction ID must be between 3 and 100 characters");return t}}let c=s.getInstance(),d=u.getInstance()},45962:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=45962,e.exports=t},87920:(e,t,r)=>{"use strict";r.d(t,{Yw:()=>c,aU:()=>l,nt:()=>u});var n=r(36449);n.ai().min(0,"金额不能为负数");let i=n.Yj().min(1,"此字段为必填项"),a=n.Yj(),o=e=>!(e<0)&&(e.toString().split(".")[1]||"").length<=2,s=n.Ik({itemType:n.k5(["treatment","consultation","material","service"],{required_error:"请选择项目类型",invalid_type_error:"无效的项目类型"}),itemName:i.max(100,"项目名称不能超过100个字符"),description:a.max(500,"描述不能超过500个字符").optional(),quantity:n.ai().min(.01,"数量必须大于0").max(9999,"数量不能超过9999").refine(e=>(e.toString().split(".")[1]||"").length<=3,"数量最多支持3位小数"),unitPrice:n.ai().min(0,"单价不能为负数").max(999999.99,"单价不能超过999,999.99").refine(o,"单价格式无效，最多支持2位小数"),discountRate:n.ai().min(0,"折扣率不能为负数").max(100,"折扣率不能超过100%").optional()}).refine(e=>!e.discountRate||!(e.discountRate>0)||0!==e.unitPrice,{message:"单价为0时不能设置折扣",path:["discountRate"]}),u=n.Ik({patient:i.uuid("请选择有效的患者"),appointment:a.uuid("请选择有效的预约").optional().or(n.eu("")),treatment:a.uuid("请选择有效的治疗项目").optional().or(n.eu("")),billType:n.k5(["treatment","consultation","deposit","additional"],{required_error:"请选择账单类型",invalid_type_error:"无效的账单类型"}),description:i.min(2,"账单描述至少需要2个字符").max(200,"账单描述不能超过200个字符"),notes:a.max(1e3,"备注不能超过1000个字符").optional(),dueDate:n.Yj().min(1,"请选择到期日期").refine(e=>{try{return!0}catch{return!1}},"请输入有效的日期").refine(e=>{let t=new Date(e),r=new Date;return r.setHours(0,0,0,0),t>=r},"到期日期不能是过去的日期").refine(e=>{let t=new Date(e),r=new Date;return r.setFullYear(r.getFullYear()+2),t<=r},"到期日期不能超过2年"),discountAmount:n.ai().min(0,"折扣金额不能为负数").max(999999.99,"折扣金额不能超过999,999.99").refine(o,"折扣金额格式无效").optional(),taxAmount:n.ai().min(0,"税费金额不能为负数").max(999999.99,"税费金额不能超过999,999.99").refine(o,"税费金额格式无效").optional(),items:n.YO(s).min(1,"至少需要一个账单项目").max(50,"账单项目不能超过50个")}).refine(e=>{let t=e.items.reduce((e,t)=>{let r=t.quantity*t.unitPrice,n=r*((t.discountRate||0)/100);return e+(r-n)},0),r=e.discountAmount||0;return t+(e.taxAmount||0)-r>=0},{message:"账单总金额不能为负数",path:["discountAmount"]}).refine(e=>{let t=e.items.reduce((e,t)=>{let r=t.quantity*t.unitPrice,n=r*((t.discountRate||0)/100);return e+(r-n)},0);return(e.discountAmount||0)<=t},{message:"折扣金额不能超过项目小计",path:["discountAmount"]}),l=n.Ik({amount:n.ai().min(.01,"支付金额必须大于0").max(999999.99,"支付金额不能超过999,999.99").refine(o,"支付金额格式无效，最多支持2位小数"),paymentMethod:n.k5(["cash","card","wechat","alipay","transfer","installment"],{required_error:"请选择支付方式",invalid_type_error:"无效的支付方式"}),transactionId:n.Yj().max(100,"交易ID不能超过100个字符").optional().or(n.eu("")),notes:a.max(500,"备注不能超过500个字符").optional()}).refine(e=>!["card","wechat","alipay","transfer"].includes(e.paymentMethod)||e.transactionId&&e.transactionId.trim().length>0,{message:"此支付方式需要提供交易ID",path:["transactionId"]});n.Ik({fullName:i.min(2,"姓名至少需要2个字符").max(50,"姓名不能超过50个字符").regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/,"姓名只能包含中文、英文和空格"),phone:i.regex(/^1[3-9]\d{9}$/,"请输入有效的手机号码"),email:n.Yj().email("请输入有效的邮箱地址").max(100,"邮箱地址不能超过100个字符").optional().or(n.eu("")),medicalNotes:a.max(2e3,"医疗备注不能超过2000个字符").optional()}),n.Ik({status:n.k5(["draft","sent","confirmed","paid","cancelled"],{required_error:"请选择账单状态",invalid_type_error:"无效的账单状态"}),notes:a.max(500,"状态更新备注不能超过500个字符").optional()}),n.Ik({search:a.max(100,"搜索关键词不能超过100个字符").optional(),status:n.k5(["draft","sent","confirmed","paid","cancelled"]).optional(),billType:n.k5(["treatment","consultation","deposit","additional"]).optional(),patientId:a.uuid("请选择有效的患者").optional().or(n.eu("")),dateFrom:n.Yj().optional().refine(e=>{if(!e)return!0;try{return!0}catch{return!1}},"请输入有效的开始日期"),dateTo:n.Yj().optional().refine(e=>{if(!e)return!0;try{return!0}catch{return!1}},"请输入有效的结束日期"),amountMin:n.ai().min(0,"最小金额不能为负数").max(999999.99,"最小金额不能超过999,999.99").optional(),amountMax:n.ai().min(0,"最大金额不能为负数").max(999999.99,"最大金额不能超过999,999.99").optional()}).refine(e=>!e.dateFrom||!e.dateTo||new Date(e.dateFrom)<=new Date(e.dateTo),{message:"开始日期不能晚于结束日期",path:["dateTo"]}).refine(e=>void 0===e.amountMin||void 0===e.amountMax||e.amountMin<=e.amountMax,{message:"最小金额不能大于最大金额",path:["amountMax"]});let c=e=>e.errors.map(e=>({field:e.path.join("."),message:e.message,code:e.code}))}};
//# sourceMappingURL=6166.js.map