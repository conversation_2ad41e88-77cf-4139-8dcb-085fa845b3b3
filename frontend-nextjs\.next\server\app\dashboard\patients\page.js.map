{"version": 3, "file": "../app/dashboard/patients/page.js", "mappings": "ubAAA,6GCAA,wICIA,IAAMA,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAGA,CAAC,iZAAkZ,CAC1aC,SAAU,CACRC,QAAS,CACPC,QAAS,iFACTC,UAAW,uFACXC,YAAa,4KACbC,QAAS,wEACX,CACF,EACAC,gBAAiB,CACfL,QAAS,SACX,CACF,GACA,SAASM,EAAM,WACbC,CAAS,SACTP,CAAO,SACPQ,GAAU,CAAK,CACf,GAAGC,EAGJ,EACC,IAAMC,EAAOF,EAAUG,EAAAA,EAAIA,CAAG,OAC9B,MAAO,UAACD,EAAAA,CAAKE,YAAU,QAAQL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAChB,EAAc,SACzDG,CACF,GAAIO,GAAa,GAAGE,CAAK,CAAEK,sBAAoB,OAAOC,wBAAsB,QAAQC,0BAAwB,aAC9G,0BC7BA,8FCAA,uCAAoI,yBCApI,kECAA,2GCAA,qDCAA,6UCeA,OACA,UACA,GACA,CACA,UACA,YACA,CACA,UACA,WACA,CACA,uBAAiC,EACjC,MApBA,IAAoB,uCAAoI,CAoBxJ,mGAES,EACF,CACP,CAGA,EACA,CACO,CACP,CACA,QAhCA,IAAsB,uCAA4H,CAgClJ,2FACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,CACP,CACA,QAjDA,IAAsB,uCAAiH,CAiDvI,gFACA,gBAjDA,IAAsB,uCAAuH,CAiD7I,sFACA,aAjDA,IAAsB,sCAAoH,CAiD1I,mFACA,WAjDA,IAAsB,4CAAgF,CAiDtG,+CACA,cAjDA,IAAsB,4CAAmF,CAiDzG,kDACA,UACA,sBAAoC,uCAAyQ,aAC7S,SACA,aACA,WACA,eACA,CACA,EACA,CACO,UACP,sGAOO,GACP,QAH6B,EAI7B,UAHA,OADgD,KAChD,SAIA,EAKO,MAAwB,oBAAkB,EACjD,YACA,KAAc,WAAS,UACvB,gCACA,+BAEA,cACA,YACA,YACK,CACL,UACA,YACA,CACA,CAAC,8KCpFD,IAAMC,EAAOC,EAAAA,EAAYA,CAInBC,EAAmBC,EAAAA,aAAmB,CAAwB,CAAC,GAC/DC,EAAY,CAAkH,CAClI,GAAGZ,EACkC,GAC9B,UAACU,EAAiBG,QAAQ,EAACC,MAAO,CACvCC,KAAMf,EAAMe,IAAI,EACfV,sBAAoB,4BAA4BC,wBAAsB,YAAYC,0BAAwB,oBACzG,UAACS,EAAAA,EAAUA,CAAAA,CAAE,GAAGhB,CAAK,CAAEK,sBAAoB,aAAaE,0BAAwB,eAGhFU,EAAe,KACnB,IAAMC,EAAeP,EAAAA,UAAgB,CAACD,GAChCS,EAAcR,EAAAA,UAAgB,CAACS,GAC/B,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,GACZC,EAAYC,CAAAA,EAAAA,EAAAA,EAAAA,CAAYA,CAAC,CAC7BT,KAAMG,EAAaH,IAAI,GAEnBU,EAAaJ,EAAcH,EAAaH,IAAI,CAAEQ,GACpD,GAAI,CAACL,EACH,MAAM,MADW,kDAGnB,GAAM,IACJQ,CAAE,CACH,CAAGP,EACJ,MAAO,IACLO,EACAX,KAAMG,EAAaH,IAAI,CACvBY,WAAY,GAAGD,EAAG,UAAU,CAAC,CAC7BE,kBAAmB,GAAGF,EAAG,sBAAsB,CAAC,CAChDG,cAAe,GAAGH,EAAG,kBAAkB,CAAC,CACxC,GAAGD,CACL,CACF,EAIML,EAAkBT,EAAAA,aAAmB,CAAuB,CAAC,GACnE,SAASmB,EAAS,WAChBhC,CAAS,CACT,GAAGE,EACyB,EAC5B,IAAM0B,EAAKf,EAAAA,KAAW,GACtB,MAAO,UAACS,EAAgBP,QAAQ,EAACC,MAAO,IACtCY,CACF,EAAGrB,sBAAoB,2BAA2BC,wBAAsB,WAAWC,0BAAwB,oBACvG,UAACwB,MAAAA,CAAI5B,YAAU,YAAYL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,aAAcN,GAAa,GAAGE,CAAK,IAElF,CACA,SAASgC,EAAU,WACjBlC,CAAS,CACT,GAAGE,EAC8C,EACjD,GAAM,OACJiC,CAAK,YACLN,CAAU,CACX,CAAGV,IACJ,MAAO,UAACiB,EAAAA,CAAKA,CAAAA,CAAC/B,YAAU,aAAagC,aAAY,CAAC,CAACF,EAAOnC,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,qCAAsCN,GAAYsC,QAAST,EAAa,GAAG3B,CAAK,CAAEK,sBAAoB,QAAQC,wBAAsB,YAAYC,0BAAwB,YAClP,CACA,SAAS8B,EAAY,CACnB,GAAGrC,EAC+B,EAClC,GAAM,OACJiC,CAAK,YACLN,CAAU,mBACVC,CAAiB,eACjBC,CAAa,CACd,CAAGZ,IACJ,MAAO,UAACf,EAAAA,EAAIA,CAAAA,CAACC,YAAU,eAAeuB,GAAIC,EAAYW,mBAAkB,EAAkC,GAAGV,EAAkB,CAAC,EAAEC,EAAAA,CAAe,CAAhE,GAAGD,EAAAA,CAAmB,CAA4CW,eAAc,CAAC,CAACN,EAAQ,GAAGjC,CAAK,CAAEK,sBAAoB,OAAOC,wBAAsB,cAAcC,0BAAwB,YAC9Q,CACA,SAASiC,EAAgB,CACvB1C,WAAS,CACT,GAAGE,EACuB,EAC1B,GAAM,mBACJ4B,CAAiB,CAClB,CAAGX,IACJ,MAAO,UAACwB,IAAAA,CAAEtC,YAAU,mBAAmBuB,GAAIE,EAAmB9B,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,gCAAiCN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,kBAAkBC,0BAAwB,YACtM,CACA,SAASmC,EAAY,WACnB5C,CAAS,CACT,GAAGE,EACuB,EAC1B,GAAM,OACJiC,CAAK,eACLJ,CAAa,CACd,CAAGZ,IACE0B,EAAOV,EAAQW,OAAOX,GAAOY,SAAW,IAAM7C,EAAM8C,QAAQ,QAC7DH,EAGE,EAHH,CAGG,CAHI,CAGJ,KAACF,IAAAA,CAAEtC,YAAU,eAAeuB,GAAIG,EAAe/B,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,2BAA4BN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,cAAcC,0BAAwB,oBAC9KoC,IAHI,IAKX,0BC3GA,oDCAA,kDCAA,gDCAA,wGCAA,uFCCO,IAAMI,EAAe,CAE1BC,IAAK,CACHC,UAAW,MACXC,aAAc,OACdC,SAAU,OACVC,WAAY,OACZC,MAAO,OACPC,QAAS,KACTC,QAAS,OACTC,MAAO,KACPC,SAAU,IACZ,EAGAR,UAAW,CACTS,MAAO,qBACPC,SAAU,eACVC,QAAS,CACPC,kBAAmB,OACnBC,eAAgB,OAChBC,cAAe,OACfC,iBAAkB,OAClBC,kBAAmB,OACnBC,8BAA+B,UAC/BC,oBAAqB,QACrBC,8BAA+B,YAC/BC,wBAAyB,SACzBC,wBAAyB,UACzBC,0BAA2B,SAC3BC,mBAAoB,SACpBC,OAAQ,KACRC,UAAW,OACXC,QAAS,OACTC,UAAW,IACb,EACAC,OAAQ,CACNC,iBAAkB,WAClBC,oBAAqB,WACvB,CACF,EAGA7B,aAAc,CACZQ,MAAO,OACPC,SAAU,YACVqB,eAAgB,OAChBC,gBAAiB,OACjBC,mBAAoB,OACpBC,kBAAmB,MACnBC,oBAAqB,WACrBC,eAAgB,OAChBC,QAAS,CACPC,IAAK,KACLC,MAAO,KACPC,SAAU,KACVC,UAAW,KACXC,OAAQ,KACRC,UAAW,MACb,EACAD,OAAQ,CACNE,UAAW,MACXC,UAAW,MACXC,WAAY,MACZC,UAAW,MACXC,UAAW,MACXC,OAAQ,KACV,EACAC,KAAM,CACJC,QAAS,KACTC,cAAe,OACfC,UAAW,OACXC,gBAAiB,SACjBC,KAAM,KACNC,KAAM,KACNC,MAAO,KACPC,iBAAkB,WAClBhB,OAAQ,IACV,CACF,EAGAxC,SAAU,CACRO,MAAO,OACPC,SAAU,YACViD,WAAY,OACZC,YAAa,OACbC,eAAgB,OAChBC,cAAe,MACfC,gBAAiB,WACjBC,WAAY,OACZC,kBAAmB,gBACnBf,KAAM,CACJgB,SAAU,KACVC,oBAAqB,UACrBC,MAAO,KACPC,iBAAkB,UAClBC,MAAO,KACPC,iBAAkB,cAClBC,aAAc,OACdC,wBAAyB,aAC3B,CACF,EAGAtE,WAAY,CACVM,MAAO,OACPC,SAAU,WACVgE,aAAc,OACdC,cAAe,OACfC,iBAAkB,OAClBC,gBAAiB,QACjBC,kBAAmB,aACnBC,aAAc,SACd7B,KAAM,CACJpF,KAAM,OACNkH,gBAAiB,UACjBC,YAAa,OACbC,uBAAwB,UACxBC,SAAU,OACVC,oBAAqB,cACrBC,MAAO,KACPC,iBAAkB,OACpB,CACF,EAGAlF,MAAO,CACLK,MAAO,OACPC,SAAU,cACV6E,eAAgB,OAChBC,eAAgB,OAChBC,eAAgB,OAChBC,MAAO,KACPC,MAAO,CACLvF,MAAO,MACPwF,OAAQ,KACRC,UAAW,IACb,CACF,EAGAC,OAAQ,CAENC,QAAS,CACPC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,KAAM,KACNC,OAAQ,KACRC,OAAQ,KACRC,MAAO,KACPC,OAAQ,KACRC,MAAO,KACPC,QAAS,KACTC,KAAM,KACNC,KAAM,MACNC,SAAU,MACVC,IAAK,KACLC,OAAQ,KACRC,OAAQ,KACRC,OAAQ,IACV,EAGAvE,OAAQ,CACNwE,QAAS,SACTC,QAAS,KACTnI,MAAO,KACPoI,QAAS,KACTC,KAAM,KACNC,QAAS,MACT9F,OAAQ,KACR+F,SAAU,MACVC,QAAS,MACTC,SAAU,KACZ,EAGAjE,KAAM,CACJjB,MAAO,KACPmF,UAAW,KACXC,SAAU,KACVnF,SAAU,KACVoF,SAAU,KACVC,SAAU,KACVpF,UAAW,KACXqF,UAAW,KACXC,UAAW,KACXC,SAAU,KACVC,SAAU,KACVC,SAAU,IACZ,EAGAC,cAAe,CACb1H,MAAO,OACP2H,YAAa,OACbC,cAAe,uBACfC,YAAa,OACbC,cAAe,qBACfC,UAAW,OACXC,YAAa,cACf,CACF,EAGAC,WAAY,CACVC,SAAU,UACVrE,MAAO,aACPF,MAAO,aACPwE,UAAW,iBACXC,UAAW,iBACXC,OAAQ,WACRC,SAAU,QACVxF,KAAM,WACNC,KAAM,UACR,EAGA5B,OAAQ,CACNoH,QAAS,gBACTC,QAAS,mBACTC,aAAc,aACdC,SAAU,WACVC,YAAa,cACbC,gBAAiB,WACjBC,WAAY,SACZC,WAAY,SACZC,aAAc,SACdC,aAAc,SACdC,aAAc,QAChB,EAGAvC,QAAS,CACPwC,MAAO,OACPC,QAAS,OACTC,QAAS,OACTC,QAAS,OACTC,KAAM,OACNC,SAAU,OACVC,WAAY,MACd,CACF,EAAW,SAGKC,EAAEC,CAAW,CAAEC,CAAwC,EACrE,IAAMC,EAAOF,EAAIG,KAAK,CAAC,KACnBzM,EAAaiC,EAEjB,IAAK,IAAMyK,KAAKF,EACd,GADoB,CAChBxM,GAAS,iBAAOA,KAAsB0M,KAAK1M,CAAAA,EAI7C,CAJoD,MAGpD2M,QAAQC,IAAI,CAAC,CAAC,2BAA2B,EAAEN,EAAAA,CAAK,EACzCA,KAAK,EAHZtM,EAAQA,CAAK,CAAC0M,EAAE,OAOpB,KAJoC,KAIL,OAApB1M,GACT2M,QAAQC,IAAI,CAAC,CAAC,mCAAmC,EAAEN,EAAAA,CAAK,EACjDA,GAILC,EACKvM,EAAM6M,IADH,GACU,CAAC,aAAc,CAACC,EAAOC,IAClCR,CAAM,CAACQ,EAAS,EAAEC,YAAcF,GAIpC9M,CACT,0BCnRA,kDCAA,iECAA,uDCAA,wGCcO,SAASiN,EAAmB,MACjCC,CAAI,cACJC,CAAY,OACZvK,CAAK,aACLwE,CAAW,aACXgG,EAAc,IAAI,YAClBC,EAAa,IAAI,SACjB5O,EAAU,SAAS,WACnB6O,CAAS,SACTjE,GAAU,CAAK,CACS,EACxB,MAAO,UAACkE,EAAAA,EAAWA,CAAAA,CAACL,KAAMA,EAAMC,aAAcA,EAAc5N,sBAAoB,cAAcC,wBAAsB,qBAAqBC,0BAAwB,mCAC7J,WAAC+N,EAAAA,EAAkBA,CAAAA,CAACjO,sBAAoB,qBAAqBE,0BAAwB,oCACnF,WAACgO,EAAAA,EAAiBA,CAAAA,CAAClO,sBAAoB,oBAAoBE,0BAAwB,oCACjF,UAACiO,EAAAA,EAAgBA,CAAAA,CAACnO,sBAAoB,mBAAmBE,0BAAwB,mCAA2BmD,IAC5G,UAAC+K,EAAAA,EAAsBA,CAAAA,CAACpO,sBAAoB,yBAAyBE,0BAAwB,mCAA2B2H,OAE1H,WAACwG,EAAAA,EAAiBA,CAAAA,CAACrO,sBAAoB,oBAAoBE,0BAAwB,oCACjF,UAACoO,EAAAA,EAAiBA,CAAAA,CAACjE,SAAUP,EAAS9J,sBAAoB,oBAAoBE,0BAAwB,mCAA2B4N,IACjI,UAACS,EAAAA,EAAiBA,CAAAA,CAACC,QAAST,EAAW1D,SAAUP,EAASrK,UAAuB,gBAAZP,EAA4B,qEAAuE,GAAIc,sBAAoB,oBAAoBE,0BAAwB,mCACzO4J,EAAU,SAAW+D,WAKlC,0BCvCA,sDCAA,wDCAA,qDCAA,sECAA,oDCAA,kECAA,+DCgBA,eAAeY,EAAcC,CAAgB,CAAEC,CAAqB,EAClE,IAAMC,EAAM,CAAC,IAAI,EAAEF,EAAAA,CAAU,CAE7B,GAAI,CACF,IAAMG,EAAW,MAAMC,MAAMF,EAAK,CAChCG,QAAS,CACP,eAAgB,mBAChB,GAAGJ,GAASI,OAAO,EAErB,GAAGJ,CACL,GAEMK,EAAe,MAAMH,EAASI,IAAI,GAExC,GAAI,CAACJ,EAASK,EAAE,CAAE,CAEhB,GAAIF,EAAapN,KAAK,CACpB,CADsB,KACZuN,MAAMH,EAAapN,KAAK,CAEpC,OAAM,MAAU,CAAC,oBAAoB,EAAEiN,EAASvJ,MAAM,CAAC,CAAC,EAAEuJ,EAASO,UAAU,EAAE,CACjF,CAEA,OAAOJ,CACT,CAAE,MAAOpN,EAAO,CAEd,MADAwL,QAAQxL,KAAK,CAAC,CAAC,eAAe,EAAEgN,EAAI,QAAQ,CAAC,CAAEhN,GACzCA,CACR,CACF,8CAUO,IAAMyN,EAAkB,CAC7BC,OAAQ,MAAOtC,IACb,IAAMuC,EAAe,IAAIC,eACrBxC,IAAQyC,OAAOF,EAAaG,MAAM,CAAC,QAAS1C,EAAOyC,KAAK,CAAChC,QAAQ,IACjET,GAAQ2C,MAAMJ,EAAaG,MAAM,CAAC,OAAQ1C,EAAO2C,IAAI,CAAClC,QAAQ,IAG9DT,GAAQ4C,OAAO,EAGNA,KAAK,CAAC7J,OAAO,EAAE8J,QACxBN,EAAaG,MAAM,CAAC,yBAA0B1C,EAAO4C,KAAK,CAAC7J,OAAO,CAAC8J,MAAM,EAI7E,IAAMC,EAAQP,EAAa9B,QAAQ,GAAK,CAAC,CAAC,EAAE8B,EAAa9B,QAAQ,IAAI,CAAG,GACxE,OAAOgB,EAAyC,CAAC,aAAa,EAAEqB,EAAAA,CAAO,CACzE,EAEAC,QAAS,MAAO1O,GACPoN,EAAwB,CAAC,cAAc,EAAEpN,EAAAA,CAAI,EAGtDwI,OAAQ,MAAOmG,GACNvB,EAAwB,gBAAiB,CAC9CwB,OAAQ,OACR3N,KAAM4N,KAAKC,SAAS,CAACH,EACvB,GAGFpG,OAAQ,MAAOvI,EAAY2O,IAClBvB,EAAwB,CAAC,cAAc,EAAEpN,EAAAA,CAAI,CAAE,CACpD4O,OAAQ,QACR3N,KAAM4N,KAAKC,SAAS,CAACH,EACvB,GAGFjH,OAAQ,MAAO1H,GACNoN,EAAiB,CAAC,cAAc,EAAEpN,EAAAA,CAAI,CAAE,CAC7C4O,OAAQ,QACV,EAEJ,EAAE,EAGyB,CACzBX,OAAQ,MAAOtC,IACb,IAAMuC,EAAe,IAAIC,gBACrBxC,GAAQyC,OAAOF,EAAaG,MAAM,CAAC,QAAS1C,EAAOyC,KAAK,CAAChC,QAAQ,IACjET,GAAQ2C,MAAMJ,EAAaG,MAAM,CAAC,OAAQ1C,EAAO2C,IAAI,CAAClC,QAAQ,IAC9DT,GAAQ/D,QAAQsG,EAAaG,MAAM,CAAC,mCAAoC1C,EAAO/D,MAAM,EAEzF,IAAM6G,EAAQP,EAAa9B,QAAQ,GAAK,CAAC,CAAC,EAAE8B,EAAa9B,QAAQ,IAAI,CAAG,GACxE,OAAOgB,EAAqC,CAAC,SAAS,EAAEqB,EAAAA,CAAO,CACjE,EAEAC,QAAS,MAAO1O,GACPoN,EAAoB,CAAC,UAAU,EAAEpN,EAAAA,CAAI,EAG9CwI,OAAQ,MAAOmG,GACNvB,EAAoB,YAAa,CACtCwB,OAAQ,OACR3N,KAAM4N,KAAKC,SAAS,CAACH,EACvB,GAGFpG,OAAQ,MAAOvI,EAAY2O,IAClBvB,EAAoB,CAAC,UAAU,EAAEpN,EAAAA,CAAI,CAAE,CAC5C4O,OAAQ,QACR3N,KAAM4N,KAAKC,SAAS,CAACH,EACvB,GAGFjH,OAAQ,MAAO1H,GACNoN,EAAiB,CAAC,UAAU,EAAEpN,EAAAA,CAAI,CAAE,CACzC4O,OAAQ,QACV,EAEJ,EAAE,EAG2B,CAC3BX,OAAQ,MAAOtC,IACb,IAAMuC,EAAe,IAAIC,gBACrBxC,GAAQyC,OAAOF,EAAaG,MAAM,CAAC,QAAS1C,EAAOyC,KAAK,CAAChC,QAAQ,IACjET,GAAQ2C,MAAMJ,EAAaG,MAAM,CAAC,OAAQ1C,EAAO2C,IAAI,CAAClC,QAAQ,IAElE,IAAMqC,EAAQP,EAAa9B,QAAQ,GAAK,CAAC,CAAC,EAAE8B,EAAa9B,QAAQ,IAAI,CAAG,GACxE,OAAOgB,EAAuC,CAAC,WAAW,EAAEqB,EAAAA,CAAO,CACrE,EAEAC,QAAS,MAAO1O,GACPoN,EAAsB,CAAC,YAAY,EAAEpN,EAAAA,CAAI,EAGlDwI,OAAQ,MAAOmG,GACNvB,EAAsB,cAAe,CAC1CwB,OAAQ,OACR3N,KAAM4N,KAAKC,SAAS,CAACH,EACvB,GAGFpG,OAAQ,MAAOvI,EAAY2O,IAClBvB,EAAsB,CAAC,YAAY,EAAEpN,EAAAA,CAAI,CAAE,CAChD4O,OAAQ,QACR3N,KAAM4N,KAAKC,SAAS,CAACH,EACvB,GAGFjH,OAAQ,MAAO1H,GACNoN,EAAiB,CAAC,YAAY,EAAEpN,EAAAA,CAAI,CAAE,CAC3C4O,OAAQ,QACV,EAEJ,EAAE,EAGiC,UACjC,GAAI,CAEF,IAAM9K,EAAQ,IAAIiL,OAAOC,WAAW,GAAGnD,KAAK,CAAC,IAAI,CAAC,EAAE,CAM9CoD,EAAa9M,CALO,MAAM6L,EAAgBC,MAAM,CAAC,CACrDG,MAAO,GACT,IAGqCc,IAAI,CAACrH,MAAM,CAACsH,GAC/CA,EAAIC,eAAe,CAACC,UAAU,CAACvL,IAC/BwL,MAAM,CAGFC,EAAU,IAAIR,KACpBQ,EAAQC,OAAO,CAACD,EAAQE,OAAO,GAAK,GACpC,IAAMC,EAAc,MAAMC,EAAY1B,MAAM,CAAC,CAAEG,MAAO,GAAK,GACrDhM,EAAiBsN,EAAYR,IAAI,CAACrH,MAAM,CAACnD,GAC7C,IAAIqK,KAAKrK,EAAQkL,SAAS,GAAKL,GAC/BD,MAAM,CAGFO,EAAgB,MAAMC,EAAc7B,MAAM,CAAC,CAAEG,MAAO,GAAK,GAE/D,MAAO,CACLjM,kBAAmB8M,iBACnB7M,EACAC,cAAeqN,EAAYK,SAAS,CACpCzN,iBAAkBuN,EAAcE,SAAS,CAE7C,CAAE,MAAOxP,EAAO,CAGd,OAFAwL,QAAQxL,KAAK,CAAC,qCAAsCA,GAE7C,CACL4B,kBAAmB,EACnBC,eAAgB,EAChBC,cAAe,EACfC,iBAAkB,CACpB,CACF,CACF,EAAE,wBCnNF,uDCAA,4ZC8HO,SAAS0N,EAAqBC,CAAiB,CAAEC,CAAc,EAQpE,IAAMC,EAPoC,CACxC,QAAW,KACX,YAAe,KACf,UAAa,OACb,KAAQ,IACV,CAE4B,CAACD,EAAOE,WAAW,GAAG,EAAIF,EAEtD,OAAQD,GACN,IAAK,SACH,MAAO,GAAGE,EAAW,KAAK,CAAC,KACxB,SACH,MAAO,GAAGA,EAAW,KAAK,CAAC,KACxB,SACH,MAAO,GAAGA,EAAW,KAAK,CAC5B,KAAK,WACH,MAAO,GAAGA,EAAW,OAAO,CAAC,KAC1B,SACH,MAAO,GAAGA,EAAW,KAAK,CAAC,KACxB,OACH,MAAO,GAAGA,EAAW,KAAK,CAAC,SAE3B,MAAO,GAAGA,EAAAA,EAAaF,EAAU,GAAG,CAAC,CAE3C,gBCvIA,IAAMI,EAAgBC,EAAAA,EAAQ,CAAC,CAC7B7K,SAAU6K,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,2CAA2CC,GAAG,CAAC,IAAK,0CAA0CC,KAAK,CAAC,kBAAmB,wEACnJ9K,MAAO2K,EAAAA,EAAQ,GAAGC,GAAG,CAAC,GAAI,2CAA2CC,GAAG,CAAC,GAAI,wCAAwCC,KAAK,CAAC,yBAA0B,uEACrJ5K,MAAOyK,EAAAA,EAAQ,GAAGzK,KAAK,CAAC,kEAAkE6K,QAAQ,GAAGC,EAAE,CAACL,EAAAA,EAAS,CAAC,KAClHvK,aAAcuK,EAAAA,EAAQ,GAAGE,GAAG,CAAC,IAAM,+CAA+CE,QAAQ,EAC5F,GAQO,SAASE,EAAkB,MAChCtE,CAAI,cACJC,CAAY,SACZ7H,CAAO,WACPmM,CAAS,CACc,EACvB,GAAM,CAACpI,EAASqI,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjCC,EAAY,CAAC,CAACtM,EACdD,EAAOwM,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,CAAkB,CACpCC,SAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACd,GACtBe,cAAe,CACb3L,SAAUf,GAASe,UAAY,GAC/BE,MAAOjB,GAASiB,OAAS,GACzBE,MAAOnB,GAASmB,OAAS,GACzBE,aAAcrB,GAASqB,cAAgB,EACzC,CACF,GAaMsL,EAAW,MAAO1C,IACtBmC,GAAW,GAGX,IAAMQ,EAAeC,EAAAA,KAAKA,CAAC9I,OAAO,CAAC+I,SD8FvBA,CAAsC,CAAEtB,CAAc,EAQpE,IAAMC,EAAasB,CANjB,QAAW,KACX,YAAe,KACf,UAAa,OACb,KAAQ,IACV,CAE4B,CAACvB,EAAOE,WAAW,GAAG,EAAIF,EAEtD,OAAQD,GACN,IAAK,SACH,MAAO,CAAC,IAAI,EAAEE,EAAW,GAAG,CAAC,KAC1B,SACH,MAAO,CAAC,IAAI,EAAEA,EAAW,GAAG,CAAC,KAC1B,SACH,MAAO,CAAC,IAAI,EAAEA,EAAW,GAAG,CAAC,KAC1B,OACH,MAAO,CAAC,IAAI,EAAEA,EAAW,GAAG,CAAC,KAC1B,OACH,MAAO,CAAC,IAAI,EAAEA,EAAW,GAAG,CAC9B,KAAK,OACH,MAAO,CAAC,IAAI,EAAEA,EAAW,GAAG,CAAC,SAE7B,MAAO,CAAC,IAAI,EAAEA,EAAW,GAAG,CAChC,CACF,ECxH4Da,EAAY,SAAW,SAAU,YACzF,GAAI,CACF,IAAMU,EAAc,CAClBjM,SAAUkJ,EAAKlJ,QAAQ,CACvBE,MAAOgJ,EAAKhJ,KAAK,CACjBE,MAAO8I,EAAK9I,KAAK,OAAI8L,EACrB5L,aAAc4I,EAAK5I,YAAY,OAAI4L,CACrC,EACIX,GACF,MAAMrB,EADO,EACIA,CAACpH,MAAM,CAAC7D,EAAQ1E,EAAE,CAAE0R,GACrCH,EAAAA,KAAKA,CAAC7I,OAAO,CAACsH,EAAqB,SAAU,SAAXA,EAAuB,CACvDhQ,GAAIsR,CACN,KAEA,MAAM3B,EAAAA,EAAWA,CAACnH,MAAM,CAACkJ,GACzBH,EAAAA,KAAKA,CAAC7I,OAAO,CAACsH,EAAqB,SAAU,SAAXA,EAAuB,CACvDhQ,GAAIsR,CACN,IAEFT,MACAtE,GAAa,GACb9H,EAAKqD,KAAK,EACZ,CAAE,MAAOvH,EAAO,CACdwL,QAAQxL,KAAK,CAAC,0BAA2BA,GACzC,IAAMqR,EDtEL,SAASC,CAAyB,EAEvC,ECoEuCA,CDpEnC,CAACC,UAAUC,MAAM,CACnB,CADqB,KACd,sBAIT,GAAmB,eAAfxR,EAAMlB,IAAI,EAAqBkB,EAAMY,OAAO,EAAE6Q,SAAS,WACzD,CADqE,KAC9D,YAIT,GAAIzR,EAAMA,KAAK,EAAIA,EAAMY,OAAO,CAAE,CAChC,IAAMyQ,EAAerR,EAAMA,KAAK,EAAIA,EAAMY,OAAO,CAGjD,GAAIyQ,EAAaI,QAAQ,CAAC,kBAAoBJ,EAAaI,QAAQ,CAAC,sBAAsB,MACxF,EAAiBA,QAAQ,CAAC,SACjB,CAD2B,uBAGhCJ,EAAaI,QAAQ,CAAC,SACjB,CAD2B,uBAGhCJ,EAAaI,QAAQ,CAAC,QACjB,CAD0B,mBAG5B,sBAIT,GAAIJ,EAAaI,QAAQ,CAAC,eAAiBJ,EAAaI,QAAQ,CAAC,WAC/D,CAD2E,KACpE,yBAIT,GAAIJ,EAAaI,QAAQ,CAAC,eAAiBJ,EAAaI,QAAQ,CAAC,iBAAmBJ,EAAaI,QAAQ,CAAC,aACxG,CADsH,KAC/G,uBAIT,GAAIJ,EAAaI,QAAQ,CAAC,cAAgBJ,EAAaI,QAAQ,CAAC,kBAC9D,CADiF,KAC1E,uBAIT,GAAIJ,EAAaI,QAAQ,CAAC,eAAgB,CACxC,GAAIJ,EAAaI,QAAQ,CAAC,aAAeJ,EAAaI,QAAQ,CAAC,WAC7D,CADyE,KAClE,yBAET,GAAIJ,EAAaI,QAAQ,CAAC,QACxB,CADiC,KAC1B,wBAEX,CAGA,GAAIJ,EAAaI,QAAQ,CAAC,YACpBJ,EAAaI,QAAQ,CAAC,gBACxB,CADyC,KAClC,4BAKX,GAAIJ,EAAaI,QAAQ,CAAC,cAAc,EACrBA,QAAQ,CAAC,gBACxB,CADyC,KAClC,8BAKX,GAAIJ,EAAatC,MAAM,CAAG,KAAO,CAACsC,EAAaI,QAAQ,CAAC,WAAa,CAACJ,EAAaI,QAAQ,CAAC,UAC1F,CADqG,MAC9FJ,CAEX,CAGA,GAAIrR,EAAM0D,MAAM,CACd,CADgB,MACR1D,EAAM0D,MAAM,EAClB,KAAK,IACH,MAAO,kBACT,MAAK,IACH,MAAO,oBACT,MAAK,IACH,MAAO,aACT,MAAK,IACH,MAAO,WACT,MAAK,IACH,MAAO,qBACT,MAAK,IACH,MAAO,qBACT,MAAK,IACH,MAAO,kBACT,MAAK,IACH,MAAO,cACT,MAAK,IACL,KAAK,IACL,KAAK,IACH,MAAO,gBACT,SACE,MAAO,cACX,CAIF,MAAO,+BACT,EClC0C1D,GACpCgR,EAAAA,KAAKA,CAAChR,KAAK,CAACqR,EAAc,CACxB5R,GAAIsR,CACN,EACF,QAAU,CACRR,GAAW,EACb,CACF,EACA,MAAO,UAACmB,EAAAA,EAAMA,CAAAA,CAAC3F,KAAMA,EAAMC,aAAcA,EAAc5N,sBAAoB,SAASC,wBAAsB,oBAAoBC,0BAAwB,mCAClJ,WAACqT,EAAAA,EAAaA,CAAAA,CAAC9T,UAAU,mBAAmBO,sBAAoB,gBAAgBE,0BAAwB,oCACtG,WAACsT,EAAAA,EAAYA,CAAAA,CAACxT,sBAAoB,eAAeE,0BAAwB,oCACvE,UAACuT,EAAAA,EAAWA,CAAAA,CAACzT,sBAAoB,cAAcE,0BAAwB,mCACpEmS,EAAYvF,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,wBAA0BA,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yBAE7C,UAAC4G,EAAAA,EAAiBA,CAAAA,CAAC1T,sBAAoB,oBAAoBE,0BAAwB,mCAChFmS,EAAY,aAAe,uBAIhC,UAAClS,EAAAA,EAAIA,CAAAA,CAAE,GAAG2F,CAAI,CAAE9F,sBAAoB,OAAOE,0BAAwB,mCACjE,WAAC4F,OAAAA,CAAK4M,SAAU5M,EAAK6N,YAAY,CAACjB,GAAWjT,UAAU,sBAErD,UAACc,EAAAA,EAASA,CAAAA,CAACqT,QAAS9N,EAAK8N,OAAO,CAAElT,KAAK,WAAWmT,OAAQ,CAAC,CAC3DC,OAAK,CACN,GAAK,WAACrS,EAAAA,EAAQA,CAAAA,WACP,WAACE,EAAAA,EAASA,CAAAA,WAAEmL,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0BAA0B,QACxC,UAAC9K,EAAAA,EAAWA,CAAAA,UACV,UAAC+R,EAAAA,CAAKA,CAAAA,CAACC,YAAalH,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qCAAuC,GAAGgH,CAAK,KAEvE,UAAC1R,IAAAA,CAAE3C,UAAU,yCAAgC,6BAG7C,UAAC4C,EAAAA,EAAWA,CAAAA,CAAAA,MACDrC,sBAAoB,YAAYE,0BAAwB,4BAGzE,UAACK,EAAAA,EAASA,CAAAA,CAACqT,QAAS9N,EAAK8N,OAAO,CAAElT,KAAK,QAAQmT,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,WAACrS,EAAAA,EAAQA,CAAAA,WACP,WAACE,EAAAA,EAASA,CAAAA,WAAEmL,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uBAAuB,QACrC,UAAC9K,EAAAA,EAAWA,CAAAA,UACV,UAAC+R,EAAAA,CAAKA,CAAAA,CAACC,YAAalH,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,kCAAmCmH,KAAK,MAAO,GAAGH,CAAK,KAE/E,UAAC1R,IAAAA,CAAE3C,UAAU,yCAAgC,qBAG7C,UAAC4C,EAAAA,EAAWA,CAAAA,CAAAA,MACDrC,sBAAoB,YAAYE,0BAAwB,4BAGzE,UAACK,EAAAA,EAASA,CAAAA,CAACqT,QAAS9N,EAAK8N,OAAO,CAAElT,KAAK,QAAQmT,OAAQ,CAAC,OACxDC,CAAK,CACN,GAAK,WAACrS,EAAAA,EAAQA,CAAAA,WACP,WAACE,EAAAA,EAASA,CAAAA,WAAEmL,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,uBAAuB,WACrC,UAAC9K,EAAAA,EAAWA,CAAAA,UACV,UAAC+R,EAAAA,CAAKA,CAAAA,CAACC,YAAalH,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,kCAAmCmH,KAAK,QAAS,GAAGH,CAAK,KAEjF,UAAC1R,IAAAA,CAAE3C,UAAU,yCAAgC,gBAG7C,UAAC4C,EAAAA,EAAWA,CAAAA,CAAAA,MACDrC,sBAAoB,YAAYE,0BAAwB,4BAGzE,UAACgU,EAAAA,EAAcA,CAAAA,CAACC,WAAW,sBAAsBnU,sBAAoB,iBAAiBE,0BAAwB,mCAC5G,UAACK,EAAAA,EAASA,CAAAA,CAACqT,QAAS9N,EAAK8N,OAAO,CAAElT,KAAK,eAAemT,OAAQ,CAAC,OAC/DC,CAAK,CACN,GAAK,WAACrS,EAAAA,EAAQA,CAAAA,WACP,UAACE,EAAAA,EAASA,CAAAA,UAAEmL,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,gCACd,UAAC9K,EAAAA,EAAWA,CAAAA,UACV,UAACoS,EAAAA,CAAQA,CAAAA,CAACJ,YAAalH,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,yCAA0CrN,UAAU,gBAAiB,GAAGqU,CAAK,KAExG,UAACzR,EAAAA,EAAWA,CAAAA,CAAAA,GACZ,UAACD,IAAAA,CAAE3C,UAAU,yCAAgC,yBAGlCO,sBAAoB,YAAYE,0BAAwB,8BAG3E,WAACmU,EAAAA,EAAYA,CAAAA,CAACrU,sBAAoB,eAAeE,0BAAwB,oCACvE,UAACoU,EAAAA,CAAMA,CAAAA,CAACL,KAAK,SAAS/U,QAAQ,UAAUsP,QAAS,IAAMZ,GAAa,GAAQvD,SAAUP,EAAS9J,sBAAoB,SAASE,0BAAwB,mCACjJ4M,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,2BAEL,UAACwH,EAAAA,CAAMA,CAAAA,CAACL,KAAK,SAAS5J,SAAUP,EAAS9J,sBAAoB,SAASE,0BAAwB,mCAC3F4J,EAAU,SAAWuI,EAAY,OAAS,qBAO3D,gBCjKe,SAASkC,IACtB,GAAM,CACJC,QAAM,UACNC,CAAQ,CACT,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,GACL,eACJC,CAAa,CACd,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAOA,GACL,CAAC9R,EAAU+R,EAAY,CAAGzC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EAChD,CAACtI,EAASqI,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACjC,CAACxQ,EAAOkT,EAAS,CAAG1C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAgB,MAC5C,CAAC2C,EAAYC,EAAc,CAAG5C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACvC,CAAC6C,EAAkBC,EAAoB,CAAG9C,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAY,EAAE,EAChE,CAAC+C,EAAgBC,EAAkB,CAAGhD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC/C,CAACiD,EAAgBC,EAAkB,CAAGlD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAC9C,CAACmD,EAAkBC,EAAoB,CAAGpD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnD,CAACqD,EAAiBC,EAAmB,CAAGtD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,GAChD,CAACuD,EAAeC,EAAiB,CAAGxD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IAG/CqC,GAAY,CAACD,GACfqB,CAAAA,EAAAA,EADuB,QACvBA,CAAQA,CAAC,iBAIX,IAAMC,EAAgB,UACpB,GAAI,CACF3D,GAAW,GACX,IAAMtD,EAAW,MAAMmC,EAAAA,EAAWA,CAAC1B,MAAM,CAAC,CACxCG,MAAO,GACT,GACAoF,EAAYhG,EAAS0B,IAAI,EACzB2E,EAAoBrG,EAAS0B,IAAI,EACjCuE,EAAS,KACX,CAAE,MAAOiB,EAAK,CACZ3I,QAAQxL,KAAK,CAAC,4BAA6BmU,GAC3CjB,EAAShI,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,4BACb,QAAU,CACRqF,GAAW,EACb,CACF,EAQM6D,EAAmB,KACvBV,OAAkBtC,GAClBoC,GAAkB,EACpB,EACMa,EAAoB,IACxBX,EAAkBvP,GAClBqP,EAAkB,GACpB,EACMc,EAAsB,MAAOnQ,IAEjC,GAAI,CAUF,GATA6P,GAAiB,GASbO,CARyB,MAAM9G,EAAAA,EAAeA,CAACC,MAAM,CAAC,CACxDG,MAAO,EACPG,MAAO,CACL7J,QAAS,CACP8J,OAAQ9J,EAAQ1E,EAAE,CAEtB,CACF,IACyBkP,IAAI,CAACI,MAAM,CAAG,EAAG,YACxCiC,EAAAA,KAAKA,CAAChR,KAAK,CAAC,6BAGd8T,EAAmB3P,GACnByP,GAAoB,EACtB,CAAE,MAAO5T,EAAO,CACdwL,QAAQxL,KAAK,CAAC,wCAAyCA,GACvDgR,EAAAA,KAAKA,CAAChR,KAAK,CAAC,WACd,QAAU,CACRgU,GAAiB,EACnB,CACF,EACMQ,EAAsB,UAC1B,GAAKX,CAAD,EACJG,EAAiB,IACjB,GAAI,CACF,IAHoB,EAGd5E,EAAAA,EAAWA,CAACjI,MAAM,CAAC0M,EAAgBpU,EAAE,EAC3CuR,EAAAA,KAAKA,CAAC7I,OAAO,CAAC,UACdyL,GAAoB,GACpBE,OAAmB1C,GACnB8C,GACF,CAAE,MAAOlU,EAAO,CACdwL,IAFiB,IAETxL,KAAK,CAAC,SAFsB,mBAEOA,GAC3CgR,EAAAA,KAAKA,CAAChR,KAAK,CAAC,SACd,QAAU,CACRgU,GAAiB,EACnB,EACF,QAgBA,CAAKnB,GAAY3K,EACR,OADiB,CACjB,EAACuM,EAAAA,CAAaA,CAAAA,UACjB,UAAC3U,MAAAA,CAAIjC,UAAU,iDACb,WAACiC,MAAAA,CAAIjC,UAAU,wBACb,UAACiC,MAAAA,CAAIjC,UAAU,6EACf,UAAC2C,IAAAA,CAAE3C,UAAU,iCAAyBqN,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qCAK3C,WAACuJ,EAAAA,CAAaA,CAAAA,CAACrW,sBAAoB,gBAAgBC,wBAAsB,eAAeC,0BAAwB,qBACnH,WAACwB,MAAAA,CAAIjC,UAAU,2CAEb,WAACiC,MAAAA,CAAIjC,UAAU,8CACb,WAACiC,MAAAA,WACC,WAAC4U,KAAAA,CAAG7W,UAAU,sEACZ,UAAC8W,EAAAA,CAASA,CAAAA,CAAC9W,UAAU,SAASO,sBAAoB,YAAYE,0BAAwB,aACrF4M,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,qBAEL,UAAC1K,IAAAA,CAAE3C,UAAU,iCACVqN,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,0BAGP,UAACoH,EAAAA,EAAcA,CAAAA,CAACC,WAAW,oBAAoBnU,sBAAoB,iBAAiBE,0BAAwB,oBAC1G,WAACoU,EAAAA,CAAMA,CAAAA,CAAC7U,UAAU,0BAA0B+O,QAASwH,EAAkBhW,sBAAoB,SAASE,0BAAwB,qBAC1H,UAACsW,EAAAA,CAAQA,CAAAA,CAAC/W,UAAU,SAASO,sBAAoB,WAAWE,0BAAwB,aACnF4M,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,+BAMT,WAACpL,MAAAA,CAAIjC,UAAU,wCACb,WAACiC,MAAAA,CAAIjC,UAAU,qCACb,UAACgX,EAAAA,CAAUA,CAAAA,CAAChX,UAAU,kFAAkFO,sBAAoB,aAAaE,0BAAwB,aACjK,UAAC6T,EAAAA,CAAKA,CAAAA,CAACC,YAAalH,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,8BAA+BrM,MAAOsU,EAAY2B,SAAUC,GAAK3B,EAAc2B,EAAEC,MAAM,CAACnW,KAAK,EAAGhB,UAAU,QAAQO,sBAAoB,QAAQE,0BAAwB,gBAE9L,WAACV,EAAAA,CAAKA,CAAAA,CAACN,QAAQ,UAAUc,sBAAoB,QAAQE,0BAAwB,qBAC1E+U,EAAiBtE,MAAM,CAAC,MAAI7N,EAAS6N,MAAM,CAAC,IAAE7D,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,gCAKpDlL,GAAS,UAACF,MAAAA,CAAIjC,UAAU,0DACrB,UAAC2C,IAAAA,CAAE3C,UAAU,wBAAgBmC,MAIhC,CAACA,GAAS,UAACF,MAAAA,CAAIjC,UAAU,6BACtB,UAACiC,MAAAA,CAAIjC,UAAU,eACgB,IAA5BwV,EAAiBtE,MAAM,CAAS,WAACjP,MAAAA,CAAIjC,UAAU,6BAC5C,UAAC8W,EAAAA,CAASA,CAAAA,CAAC9W,UAAU,+CACrB,UAACoX,KAAAA,CAAGpX,UAAU,sCACXsV,EAAa,QAAU,WAE1B,UAAC3S,IAAAA,CAAE3C,UAAU,sCACVsV,EAAa,aAAe,iBAE9B,CAACA,GAAc,UAACb,EAAAA,EAAcA,CAAAA,CAACC,WAAW,6BACvC,WAACG,EAAAA,CAAMA,CAAAA,CAAC9F,QAASwH,YACf,UAACQ,EAAAA,CAAQA,CAAAA,CAAC/W,UAAU,gBACnBqN,CAAAA,EAAAA,EAAAA,CAAAA,CAACA,CAAC,+BAGF,WAACpL,MAAAA,CAAIjC,UAAU,sBACtB,UAACiC,MAAAA,CAAIjC,UAAU,yCACZsV,GAAc,CAAC,CAAC,EAAEA,EAAW,OAAO,CAAC,GAIxC,UAACrT,MAAAA,CAAIjC,UAAU,oDACZwV,EAAiB6B,GAAG,CAAC/Q,GAAW,UAACrE,MAAAA,CAAqBjC,UAAU,mEAC7D,WAACiC,MAAAA,CAAIjC,UAAU,sBAEb,WAACiC,MAAAA,CAAIjC,UAAU,6CACb,WAACiC,MAAAA,WACC,UAACmV,KAAAA,CAAGpX,UAAU,iCAAyBsG,EAAQe,QAAQ,GACvD,WAAC1E,IAAAA,CAAE3C,UAAU,0CAAgC,SACd,UAAtB,OAAOsG,EAAQ1E,EAAE,CAAgB0E,EAAQ1E,EAAE,CAAC0V,KAAK,CAAC,CAAC,GAAKhR,EAAQ1E,EAAE,OAK7E,WAACK,MAAAA,CAAIjC,UAAU,oCACb,UAAC6U,EAAAA,CAAMA,CAAAA,CAACpV,QAAQ,QAAQ8X,KAAK,KAAKxI,QAAS,IAAMyH,EAAkBlQ,GAAUtG,UAAU,uBACrF,UAACwX,EAAAA,CAAQA,CAAAA,CAACxX,UAAU,cAEtB,UAAC6U,EAAAA,CAAMA,CAAAA,CAACpV,QAAQ,QAAQ8X,KAAK,KAAKxI,QAAS,IAAM0H,EAAoBnQ,GAAUtG,UAAU,8CAA8C4K,SAAUsL,WAC/I,UAACuB,EAAAA,CAASA,CAAAA,CAACzX,UAAU,oBAM3B,WAACiC,MAAAA,CAAIjC,UAAU,sBACb,WAACiC,MAAAA,CAAIjC,UAAU,4CACb,UAAC0X,EAAAA,CAASA,CAAAA,CAAC1X,UAAU,iCACrB,UAAC2X,OAAAA,CAAK3X,UAAU,uBAAesG,EAAQiB,KAAK,MAG7CjB,EAAQmB,KAAK,EAAI,WAACxF,MAAAA,CAAIjC,UAAU,4CAC7B,UAAC4X,EAAAA,CAAQA,CAAAA,CAAC5X,UAAU,iCACpB,UAAC2X,OAAAA,UAAMrR,EAAQmB,KAAK,SAK1B,UAACgN,EAAAA,EAAcA,CAAAA,CAACC,WAAW,+BACxBpO,EAAQqB,YAAY,EAAI,WAAC1F,MAAAA,CAAIjC,UAAU,sBACpC,UAAC2C,IAAAA,CAAE3C,UAAU,qDAA4C,UACzD,UAAC2C,IAAAA,CAAE3C,UAAU,2EACVsG,EAAQqB,YAAY,QAM7B,UAAC1F,MAAAA,CAAIjC,UAAU,yBACb,WAAC2C,IAAAA,CAAE3C,UAAU,0CAAgC,SACpC,IAAI2Q,KAAKrK,EAAQkL,SAAS,EAAEqG,kBAAkB,YAhDpBvR,EAAQ1E,EAAE,eA4DnE,UAAC4Q,EAAiBA,CAACtE,KAAMwH,EAAgBvH,OAAvBqE,MAAqCmD,EAAmBrP,QAASsP,EAAgBnD,UAlJ7E,CAkJwFqF,IAjJhHnC,GAAkB,GAClBE,OAAkBtC,GAClB8C,GACF,EA8IqI9V,YA/IlH,UA+IsI,SA/InH,WA+IuIE,0BAAwB,aAEjM,UAACwN,EAAAA,CAAkBA,CAAAA,CAACC,KAAM4H,EAAkB3H,aAAc4H,EAAqBnS,MAAM,OAAOwE,YAAa,CAAC,SAAS,EAAE4N,GAAiB3O,SAAS,WAAW,CAAC,CAAE+G,YAAY,KAAK3O,QAAQ,cAAc6O,UAAWqI,EAAqBtM,QAAS6L,EAAe3V,sBAAoB,qBAAqBE,0BAAwB,eAEnU,wFCtQe,SAASmW,EAAc,CACpC5T,UAAQ,CACR+U,cAAa,CAAI,CAIlB,EACC,MAAO,+BACFA,EAAa,UAACC,EAAAA,UAAUA,CAAAA,CAAChY,UAAU,iCAChC,UAACiC,MAAAA,CAAIjC,UAAU,mCAA2BgD,MAC5B,UAACf,MAAAA,CAAIjC,UAAU,mCAA2BgD,KAElE,0BCdA,qDCAA,4DCAA,2CCAA,uCAAoI,wBCApI,yDCAA,iECAA,uDCAA,sDCAA,uKCQA,IAAM,EAAyB,iBAAM,CAA2D,IAAI,EAE7F,SAAS,EAAwB,aACtC,WACA,EACF,EAIE,OAAO,kBAAC,EAAuB,SAAvB,CAAgC,MAAO,GAAc,EAC/D,CAsCO,KAvCiE,IAuCxD,IACd,IAAM,EAAgB,WADa,GACb,CAAS,CAAC,EAC1B,EAAmB,cAAM,CAAW,GAEtC,EAAe,SACf,GAAoB,KAHwC,IAG9B,IAChC,EAAe,OAAM,CAAI,EAAgB,EADS,GASzC,QAAO,CAAC,EAIV,QAAc,CAAC,EAI1B,UAJsC,gBC1EtC,iDCAA,2DCAA,yHCEA,SAAS2R,EAAS,WAChB3U,CAAS,CACT,GAAGE,EAC8B,EACjC,MAAO,UAAC+X,WAAAA,CAAS5X,YAAU,WAAWL,UAAWM,CAAAA,EAAAA,EAAAA,EAAAA,CAAEA,CAAC,scAAucN,GAAa,GAAGE,CAAK,CAAEM,wBAAsB,WAAWC,0BAAwB,gBAC7kB,oCCYI,sBAAsB,0nBAX1B,IAAM,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EASN,EAJ6B,KALd,KAKwB,EAArC,OAAO,CAIa,CAAG,IAAI,KAAK,CAAC,EAAiB,CAJ5B,KAKjB,CAAE,CAAC,EAAkB,EAAS,CADa,GACT,CAAN,IAAW,EAEtC,CAFkB,CAGlB,EAGJ,GAAI,CACF,CAJS,GAAG,CADG,CAKW,CANP,CACD,CAK6B,CANzB,GAED,IADM,EADI,CAO/B,CADuB,CACH,GADyB,OACC,CAAC,GAAG,CAAjC,EAAoB,YAA4B,CAAC,OAAI,EACtE,EAAgB,GAAmB,EAD4C,KACrC,CAA7B,GAAiC,CAAC,EAAd,OAAuB,CAAC,EAAI,OAC7D,EADsE,GACzC,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAEM,OAAO,+BAAoC,CAAC,EAAkB,CAC5D,aAD0D,CAC5C,CAAE,qBAAqB,CACrC,aAAa,CAAE,MAAM,CACrB,iBAAiB,GACjB,aAAa,WACb,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CA7BEyX,EAoCnB,IAAC,OAOF,EAEE,OATgB,EAkBhB,OAtD+C,EA+D/C,EAA2B,CAlBN,IASL,iBASQ,IChF9B,iDCAA,yDCAA,4DCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/badge.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/?456f", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/?08f5", "webpack://next-shadcn-dashboard-starter/./src/components/ui/form.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"stream\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/./src/lib/translations.ts", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/confirmation-dialog.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/./src/lib/api.ts", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/./src/lib/error-utils.ts", "webpack://next-shadcn-dashboard-starter/./src/components/patients/patient-form-dialog.tsx", "webpack://next-shadcn-dashboard-starter/./src/app/dashboard/patients/page.tsx", "webpack://next-shadcn-dashboard-starter/./src/components/layout/page-container.tsx", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"zlib\"", "webpack://next-shadcn-dashboard-starter/?4b54", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/../../../src/client-boundary/PromisifiedAuthProvider.tsx", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/./src/components/ui/textarea.tsx", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "import * as React from 'react';\nimport { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\nconst badgeVariants = cva('inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden', {\n  variants: {\n    variant: {\n      default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\n      secondary: 'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\n      destructive: 'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\n      outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\n    }\n  },\n  defaultVariants: {\n    variant: 'default'\n  }\n});\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & {\n  asChild?: boolean;\n}) {\n  const Comp = asChild ? Slot : 'span';\n  return <Comp data-slot='badge' className={cn(badgeVariants({\n    variant\n  }), className)} {...props} data-sentry-element=\"Comp\" data-sentry-component=\"Badge\" data-sentry-source-file=\"badge.tsx\" />;\n}\nexport { Badge, badgeVariants };", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\page.tsx\");\n", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "const module0 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\");\nconst module1 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\");\nconst module2 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\");\nconst module3 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/forbidden-error\");\nconst module4 = () => import(/* webpackMode: \"eager\" */ \"next/dist/client/components/unauthorized-error\");\nconst module5 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\");\nconst page6 = () => import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\page.tsx\");\nimport { AppPageRouteModule } from \"next/dist/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'patients',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\layout.tsx\"],\n'global-error': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\"],\n'not-found': [module2, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await import(/* webpackMode: \"eager\" */ \"next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\favicon.ico?__next_metadata__\")).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\page.tsx\"];\nexport { tree, pages };\nexport { default as GlobalError } from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\global-error.tsx\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nexport * from \"next/dist/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/dashboard/patients/page\",\n        pathname: \"/dashboard/patients\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map", "'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport { Controller, FormProvider, useFormContext, useFormState, type ControllerProps, type FieldPath, type FieldValues } from 'react-hook-form';\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/label';\nconst Form = FormProvider;\ntype FormFieldContextValue<TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>> = {\n  name: TName;\n};\nconst FormFieldContext = React.createContext<FormFieldContextValue>({} as FormFieldContextValue);\nconst FormField = <TFieldValues extends FieldValues = FieldValues, TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return <FormFieldContext.Provider value={{\n    name: props.name\n  }} data-sentry-element=\"FormFieldContext.Provider\" data-sentry-component=\"FormField\" data-sentry-source-file=\"form.tsx\">\r\n      <Controller {...props} data-sentry-element=\"Controller\" data-sentry-source-file=\"form.tsx\" />\r\n    </FormFieldContext.Provider>;\n};\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const {\n    getFieldState\n  } = useFormContext();\n  const formState = useFormState({\n    name: fieldContext.name\n  });\n  const fieldState = getFieldState(fieldContext.name, formState);\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n  const {\n    id\n  } = itemContext;\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState\n  };\n};\ntype FormItemContextValue = {\n  id: string;\n};\nconst FormItemContext = React.createContext<FormItemContextValue>({} as FormItemContextValue);\nfunction FormItem({\n  className,\n  ...props\n}: React.ComponentProps<'div'>) {\n  const id = React.useId();\n  return <FormItemContext.Provider value={{\n    id\n  }} data-sentry-element=\"FormItemContext.Provider\" data-sentry-component=\"FormItem\" data-sentry-source-file=\"form.tsx\">\r\n      <div data-slot='form-item' className={cn('grid gap-2', className)} {...props} />\r\n    </FormItemContext.Provider>;\n}\nfunction FormLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  const {\n    error,\n    formItemId\n  } = useFormField();\n  return <Label data-slot='form-label' data-error={!!error} className={cn('data-[error=true]:text-destructive', className)} htmlFor={formItemId} {...props} data-sentry-element=\"Label\" data-sentry-component=\"FormLabel\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormControl({\n  ...props\n}: React.ComponentProps<typeof Slot>) {\n  const {\n    error,\n    formItemId,\n    formDescriptionId,\n    formMessageId\n  } = useFormField();\n  return <Slot data-slot='form-control' id={formItemId} aria-describedby={!error ? `${formDescriptionId}` : `${formDescriptionId} ${formMessageId}`} aria-invalid={!!error} {...props} data-sentry-element=\"Slot\" data-sentry-component=\"FormControl\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormDescription({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    formDescriptionId\n  } = useFormField();\n  return <p data-slot='form-description' id={formDescriptionId} className={cn('text-muted-foreground text-sm', className)} {...props} data-sentry-component=\"FormDescription\" data-sentry-source-file=\"form.tsx\" />;\n}\nfunction FormMessage({\n  className,\n  ...props\n}: React.ComponentProps<'p'>) {\n  const {\n    error,\n    formMessageId\n  } = useFormField();\n  const body = error ? String(error?.message ?? '') : props.children;\n  if (!body) {\n    return null;\n  }\n  return <p data-slot='form-message' id={formMessageId} className={cn('text-destructive text-sm', className)} {...props} data-sentry-component=\"FormMessage\" data-sentry-source-file=\"form.tsx\">\r\n      {body}\r\n    </p>;\n}\nexport { useFormField, Form, FormItem, FormLabel, FormControl, FormDescription, FormMessage, FormField };", "module.exports = require(\"stream\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "// 中文翻译文件\nexport const translations = {\n  // 导航菜单\n  nav: {\n    dashboard: '仪表板',\n    appointments: '预约管理',\n    patients: '患者管理',\n    treatments: '治疗项目',\n    admin: '系统管理',\n    account: '账户',\n    profile: '个人资料',\n    login: '登录',\n    overview: '概览'\n  },\n\n  // 仪表板\n  dashboard: {\n    title: '诊所控制台 🏥',\n    subtitle: '欢迎使用您的诊所管理系统',\n    metrics: {\n      todayAppointments: '今日预约',\n      recentPatients: '近期患者',\n      totalPatients: '患者总数',\n      activetreatments: '可用治疗',\n      scheduledForToday: '今日安排',\n      appointmentsScheduledForToday: '今日安排的预约',\n      newPatientsThisWeek: '本周新患者',\n      patientsRegisteredInLast7Days: '过去7天注册的患者',\n      totalRegisteredPatients: '注册患者总数',\n      completePatientDatabase: '完整患者数据库',\n      treatmentOptionsAvailable: '可用治疗选项',\n      fullServiceCatalog: '完整服务目录',\n      active: '活跃',\n      last7Days: '过去7天',\n      allTime: '全部时间',\n      available: '可用'\n    },\n    errors: {\n      loadingDashboard: '加载仪表板时出错',\n      failedToLoadMetrics: '无法加载仪表板数据'\n    }\n  },\n\n  // 预约管理\n  appointments: {\n    title: '预约管理',\n    subtitle: '管理患者预约和排程',\n    newAppointment: '新建预约',\n    editAppointment: '编辑预约',\n    appointmentDetails: '预约详情',\n    appointmentsCount: '个预约',\n    loadingAppointments: '加载预约中...',\n    noAppointments: '暂无预约',\n    filters: {\n      all: '全部',\n      today: '今天',\n      thisWeek: '本周',\n      thisMonth: '本月',\n      status: '状态',\n      dateRange: '日期范围'\n    },\n    status: {\n      scheduled: '已安排',\n      confirmed: '已确认',\n      inProgress: '进行中',\n      completed: '已完成',\n      cancelled: '已取消',\n      noShow: '未到场'\n    },\n    form: {\n      patient: '患者',\n      selectPatient: '选择患者',\n      treatment: '治疗项目',\n      selectTreatment: '选择治疗项目',\n      date: '日期',\n      time: '时间',\n      notes: '备注',\n      notesPlaceholder: '预约备注（可选）',\n      status: '状态'\n    }\n  },\n\n  // 患者管理\n  patients: {\n    title: '患者管理',\n    subtitle: '管理患者信息和病历',\n    newPatient: '新建患者',\n    editPatient: '编辑患者',\n    patientDetails: '患者详情',\n    patientsCount: '位患者',\n    loadingPatients: '加载患者中...',\n    noPatients: '暂无患者',\n    searchPlaceholder: '按姓名、电话或邮箱搜索患者',\n    form: {\n      fullName: '姓名',\n      fullNamePlaceholder: '请输入患者姓名',\n      phone: '电话',\n      phonePlaceholder: '请输入电话号码',\n      email: '邮箱',\n      emailPlaceholder: '请输入邮箱地址（可选）',\n      medicalNotes: '病历备注',\n      medicalNotesPlaceholder: '请输入病历备注（可选）'\n    }\n  },\n\n  // 治疗项目\n  treatments: {\n    title: '治疗项目',\n    subtitle: '管理诊所治疗服务',\n    newTreatment: '新建治疗',\n    editTreatment: '编辑治疗',\n    treatmentDetails: '治疗详情',\n    treatmentsCount: '个治疗项目',\n    loadingTreatments: '加载治疗项目中...',\n    noTreatments: '暂无治疗项目',\n    form: {\n      name: '治疗名称',\n      namePlaceholder: '请输入治疗名称',\n      description: '治疗描述',\n      descriptionPlaceholder: '请输入治疗描述',\n      duration: '治疗时长',\n      durationPlaceholder: '请输入治疗时长（分钟）',\n      price: '价格',\n      pricePlaceholder: '请输入价格'\n    }\n  },\n\n  // 系统管理\n  admin: {\n    title: '系统管理',\n    subtitle: '管理用户权限和系统设置',\n    userManagement: '用户管理',\n    roleManagement: '角色管理',\n    systemSettings: '系统设置',\n    users: '用户',\n    roles: {\n      admin: '管理员',\n      doctor: '医生',\n      frontDesk: '前台'\n    }\n  },\n\n  // 通用文本\n  common: {\n    // 操作按钮\n    actions: {\n      save: '保存',\n      cancel: '取消',\n      edit: '编辑',\n      delete: '删除',\n      view: '查看',\n      search: '搜索',\n      filter: '筛选',\n      reset: '重置',\n      submit: '提交',\n      close: '关闭',\n      confirm: '确认',\n      back: '返回',\n      next: '下一步',\n      previous: '上一步',\n      add: '添加',\n      remove: '移除',\n      update: '更新',\n      create: '创建'\n    },\n\n    // 状态\n    status: {\n      loading: '加载中...',\n      success: '成功',\n      error: '错误',\n      warning: '警告',\n      info: '信息',\n      pending: '待处理',\n      active: '活跃',\n      inactive: '非活跃',\n      enabled: '已启用',\n      disabled: '已禁用'\n    },\n\n    // 时间相关\n    time: {\n      today: '今天',\n      yesterday: '昨天',\n      tomorrow: '明天',\n      thisWeek: '本周',\n      lastWeek: '上周',\n      nextWeek: '下周',\n      thisMonth: '本月',\n      lastMonth: '上月',\n      nextMonth: '下月',\n      thisYear: '今年',\n      lastYear: '去年',\n      nextYear: '明年'\n    },\n\n    // 确认对话框\n    confirmDialog: {\n      title: '确认操作',\n      deleteTitle: '确认删除',\n      deleteMessage: '您确定要删除这个项目吗？此操作无法撤销。',\n      cancelTitle: '确认取消',\n      cancelMessage: '您确定要取消吗？未保存的更改将丢失。',\n      saveTitle: '确认保存',\n      saveMessage: '您确定要保存这些更改吗？'\n    }\n  },\n\n  // 表单验证\n  validation: {\n    required: '此字段为必填项',\n    email: '请输入有效的邮箱地址',\n    phone: '请输入有效的电话号码',\n    minLength: '至少需要 {min} 个字符',\n    maxLength: '最多允许 {max} 个字符',\n    number: '请输入有效的数字',\n    positive: '请输入正数',\n    date: '请选择有效的日期',\n    time: '请选择有效的时间'\n  },\n\n  // 错误消息\n  errors: {\n    general: '发生了未知错误，请稍后重试',\n    network: '网络连接错误，请检查您的网络连接',\n    unauthorized: '您没有权限执行此操作',\n    notFound: '请求的资源未找到',\n    serverError: '服务器错误，请稍后重试',\n    validationError: '输入数据验证失败',\n    loadFailed: '加载数据失败',\n    saveFailed: '保存数据失败',\n    deleteFailed: '删除数据失败',\n    updateFailed: '更新数据失败',\n    createFailed: '创建数据失败'\n  },\n\n  // 成功消息\n  success: {\n    saved: '保存成功',\n    deleted: '删除成功',\n    updated: '更新成功',\n    created: '创建成功',\n    sent: '发送成功',\n    uploaded: '上传成功',\n    downloaded: '下载成功'\n  }\n} as const;\n\n// 翻译工具函数\nexport function t(key: string, params?: Record<string, string | number>): string {\n  const keys = key.split('.');\n  let value: any = translations;\n  \n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      console.warn(`Translation key not found: ${key}`);\n      return key; // 返回原始key作为fallback\n    }\n  }\n  \n  if (typeof value !== 'string') {\n    console.warn(`Translation value is not a string: ${key}`);\n    return key;\n  }\n  \n  // 处理参数替换\n  if (params) {\n    return value.replace(/\\{(\\w+)\\}/g, (match, paramKey) => {\n      return params[paramKey]?.toString() || match;\n    });\n  }\n  \n  return value;\n}\n\n// 类型定义\nexport type TranslationKey = keyof typeof translations;\n", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "'use client';\n\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\ninterface ConfirmationDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  title: string;\n  description: string;\n  confirmText?: string;\n  cancelText?: string;\n  variant?: 'default' | 'destructive';\n  onConfirm: () => void;\n  loading?: boolean;\n}\nexport function ConfirmationDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = '确认',\n  cancelText = '取消',\n  variant = 'default',\n  onConfirm,\n  loading = false\n}: ConfirmationDialogProps) {\n  return <AlertDialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"AlertDialog\" data-sentry-component=\"ConfirmationDialog\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n      <AlertDialogContent data-sentry-element=\"AlertDialogContent\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n        <AlertDialogHeader data-sentry-element=\"AlertDialogHeader\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n          <AlertDialogTitle data-sentry-element=\"AlertDialogTitle\" data-sentry-source-file=\"confirmation-dialog.tsx\">{title}</AlertDialogTitle>\n          <AlertDialogDescription data-sentry-element=\"AlertDialogDescription\" data-sentry-source-file=\"confirmation-dialog.tsx\">{description}</AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter data-sentry-element=\"AlertDialogFooter\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n          <AlertDialogCancel disabled={loading} data-sentry-element=\"AlertDialogCancel\" data-sentry-source-file=\"confirmation-dialog.tsx\">{cancelText}</AlertDialogCancel>\n          <AlertDialogAction onClick={onConfirm} disabled={loading} className={variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''} data-sentry-element=\"AlertDialogAction\" data-sentry-source-file=\"confirmation-dialog.tsx\">\n            {loading ? '加载中...' : confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>;\n}", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "// Enhanced API utilities with best-practice authentication\nimport { Appointment, Patient, Treatment, PayloadResponse, DashboardMetrics, AppointmentCreateData, AppointmentUpdateData } from '@/types/clinic';\n\nexport interface ApiError {\n  error: string;\n  timestamp: string;\n  details?: any;\n}\n\nexport interface ApiResponse<T> {\n  data?: T;\n  error?: ApiError;\n  success: boolean;\n}\n\n// Enhanced API fetch function with proper error handling\nasync function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {\n  const url = `/api${endpoint}`;\n\n  try {\n    const response = await fetch(url, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    });\n\n    const responseData = await response.json();\n\n    if (!response.ok) {\n      // Handle structured error responses\n      if (responseData.error) {\n        throw new Error(responseData.error);\n      }\n      throw new Error(`API request failed: ${response.status} ${response.statusText}`);\n    }\n\n    return responseData;\n  } catch (error) {\n    console.error(`API request to ${url} failed:`, error);\n    throw error;\n  }\n}\n\n// User sync utility\nexport const authApi = {\n  syncUser: async (): Promise<any> => {\n    return apiRequest('/auth/sync', { method: 'POST' });\n  },\n};\n\n// Appointments API\nexport const appointmentsApi = {\n  getAll: async (params?: { limit?: number; page?: number; where?: any }): Promise<PayloadResponse<Appointment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n\n    // Handle where clause for filtering\n    if (params?.where) {\n      // Convert where object to query parameters\n      // This is a simplified implementation - you might need to expand this based on your needs\n      if (params.where.patient?.equals) {\n        searchParams.append('where[patient][equals]', params.where.patient.equals);\n      }\n    }\n\n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Appointment>>(`/appointments${query}`);\n  },\n\n  getById: async (id: string): Promise<Appointment> => {\n    return apiRequest<Appointment>(`/appointments/${id}`);\n  },\n\n  create: async (data: AppointmentCreateData): Promise<Appointment> => {\n    return apiRequest<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: AppointmentUpdateData): Promise<Appointment> => {\n    return apiRequest<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Patients API\nexport const patientsApi = {\n  getAll: async (params?: { limit?: number; page?: number; search?: string }): Promise<PayloadResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.search) searchParams.append('where[or][0][fullName][contains]', params.search);\n    \n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Patient>>(`/patients${query}`);\n  },\n\n  getById: async (id: string): Promise<Patient> => {\n    return apiRequest<Patient>(`/patients/${id}`);\n  },\n\n  create: async (data: Partial<Patient>): Promise<Patient> => {\n    return apiRequest<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: Partial<Patient>): Promise<Patient> => {\n    return apiRequest<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Treatments API\nexport const treatmentsApi = {\n  getAll: async (params?: { limit?: number; page?: number }): Promise<PayloadResponse<Treatment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n    \n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Treatment>>(`/treatments${query}`);\n  },\n\n  getById: async (id: string): Promise<Treatment> => {\n    return apiRequest<Treatment>(`/treatments/${id}`);\n  },\n\n  create: async (data: Partial<Treatment>): Promise<Treatment> => {\n    return apiRequest<Treatment>('/treatments', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: Partial<Treatment>): Promise<Treatment> => {\n    return apiRequest<Treatment>(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Dashboard metrics helper\nexport const getDashboardMetrics = async (): Promise<DashboardMetrics> => {\n  try {\n    // Get today's appointments\n    const today = new Date().toISOString().split('T')[0];\n    const todayAppointments = await appointmentsApi.getAll({\n      limit: 1000, // Get all to count\n    });\n    \n    // Filter for today's appointments\n    const todayCount = todayAppointments.docs.filter(apt => \n      apt.appointmentDate.startsWith(today)\n    ).length;\n\n    // Get recent patients (last 7 days)\n    const weekAgo = new Date();\n    weekAgo.setDate(weekAgo.getDate() - 7);\n    const allPatients = await patientsApi.getAll({ limit: 1000 });\n    const recentPatients = allPatients.docs.filter(patient => \n      new Date(patient.createdAt) >= weekAgo\n    ).length;\n\n    // Get all treatments\n    const allTreatments = await treatmentsApi.getAll({ limit: 1000 });\n\n    return {\n      todayAppointments: todayCount,\n      recentPatients,\n      totalPatients: allPatients.totalDocs,\n      activetreatments: allTreatments.totalDocs,\n    };\n  } catch (error) {\n    console.error('Failed to fetch dashboard metrics:', error);\n    // Return default values on error\n    return {\n      todayAppointments: 0,\n      recentPatients: 0,\n      totalPatients: 0,\n      activetreatments: 0,\n    };\n  }\n};\n", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "// Enhanced error handling utilities for better user experience\n\nexport interface ApiErrorDetails {\n  field?: string;\n  code?: string;\n  constraint?: string;\n}\n\nexport interface ApiError {\n  error: string;\n  timestamp: string;\n  details?: ApiErrorDetails;\n}\n\n/**\n * Convert API errors to user-friendly messages\n */\nexport function formatApiError(error: any): string {\n  // Handle network errors\n  if (!navigator.onLine) {\n    return '无网络连接。请检查您的网络连接后重试。';\n  }\n\n  // Handle timeout errors\n  if (error.name === 'AbortError' || error.message?.includes('timeout')) {\n    return '请求超时。请重试。';\n  }\n\n  // Handle structured API errors\n  if (error.error || error.message) {\n    const errorMessage = error.error || error.message;\n    \n    // Handle specific database constraint errors\n    if (errorMessage.includes('duplicate key') || errorMessage.includes('unique constraint')) {\n      if (errorMessage.includes('phone')) {\n        return '此电话号码已被注册。请使用不同的电话号码。';\n      }\n      if (errorMessage.includes('email')) {\n        return '此邮箱地址已被注册。请使用不同的邮箱地址。';\n      }\n      if (errorMessage.includes('name')) {\n        return '此名称已被使用。请选择不同的名称。';\n      }\n      return '此信息已被使用。请检查您的输入后重试。';\n    }\n\n    // Handle validation errors\n    if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {\n      return '请检查您的输入，确保所有必填字段都正确填写。';\n    }\n\n    // Handle permission errors\n    if (errorMessage.includes('permission') || errorMessage.includes('unauthorized') || errorMessage.includes('forbidden')) {\n      return '您没有权限执行此操作。请联系您的管理员。';\n    }\n\n    // Handle not found errors\n    if (errorMessage.includes('not found') || errorMessage.includes('does not exist')) {\n      return '找不到请求的项目。它可能已被删除或移动。';\n    }\n\n    // Handle appointment-specific errors\n    if (errorMessage.includes('appointment')) {\n      if (errorMessage.includes('conflict') || errorMessage.includes('overlap')) {\n        return '此预约时间与其他预约冲突。请选择不同的时间。';\n      }\n      if (errorMessage.includes('past')) {\n        return '无法安排过去的预约。请选择未来的日期和时间。';\n      }\n    }\n\n    // Handle patient-specific errors\n    if (errorMessage.includes('patient')) {\n      if (errorMessage.includes('appointments')) {\n        return '无法删除有预约记录的患者。请先取消或完成所有预约。';\n      }\n    }\n\n    // Handle treatment-specific errors\n    if (errorMessage.includes('treatment')) {\n      if (errorMessage.includes('appointments')) {\n        return '无法删除有预约记录的治疗项目。请先取消或完成所有预约。';\n      }\n    }\n\n    // Return the original error message if it's already user-friendly\n    if (errorMessage.length < 100 && !errorMessage.includes('Error:') && !errorMessage.includes('failed')) {\n      return errorMessage;\n    }\n  }\n\n  // Handle HTTP status codes\n  if (error.status) {\n    switch (error.status) {\n      case 400:\n        return '请求无效。请检查您的输入后重试。';\n      case 401:\n        return '您没有权限执行此操作。请登录后重试。';\n      case 403:\n        return '您没有权限执行此操作。';\n      case 404:\n        return '找不到请求的项目。';\n      case 409:\n        return '此操作与现有数据冲突。请检查您的输入。';\n      case 422:\n        return '提供的数据无效。请检查您的输入后重试。';\n      case 429:\n        return '请求过于频繁。请稍等片刻后重试。';\n      case 500:\n        return '服务器错误。请稍后重试。';\n      case 502:\n      case 503:\n      case 504:\n        return '服务暂时不可用。请稍后重试。';\n      default:\n        return '发生了意外错误。请重试。';\n    }\n  }\n\n  // Fallback for unknown errors\n  return '发生了意外错误。请重试，如果问题持续存在，请联系技术支持。';\n}\n\n/**\n * Format success messages for different operations\n */\nexport function formatSuccessMessage(operation: string, entity: string): string {\n  const entityMap: Record<string, string> = {\n    'patient': '患者',\n    'appointment': '预约',\n    'treatment': '治疗项目',\n    'user': '用户'\n  };\n\n  const entityName = entityMap[entity.toLowerCase()] || entity;\n\n  switch (operation) {\n    case 'create':\n      return `${entityName}创建成功！`;\n    case 'update':\n      return `${entityName}更新成功！`;\n    case 'delete':\n      return `${entityName}删除成功！`;\n    case 'complete':\n      return `${entityName}已标记为完成！`;\n    case 'cancel':\n      return `${entityName}取消成功！`;\n    case 'sync':\n      return `${entityName}同步成功！`;\n    default:\n      return `${entityName}${operation}完成！`;\n  }\n}\n\n/**\n * Format loading messages for different operations\n */\nexport function formatLoadingMessage(operation: string, entity: string): string {\n  const entityMap: Record<string, string> = {\n    'patient': '患者',\n    'appointment': '预约',\n    'treatment': '治疗项目',\n    'user': '用户'\n  };\n\n  const entityName = entityMap[entity.toLowerCase()] || entity;\n\n  switch (operation) {\n    case 'create':\n      return `正在创建${entityName}...`;\n    case 'update':\n      return `正在更新${entityName}...`;\n    case 'delete':\n      return `正在删除${entityName}...`;\n    case 'load':\n      return `正在加载${entityName}...`;\n    case 'save':\n      return `正在保存${entityName}...`;\n    case 'sync':\n      return `正在同步${entityName}...`;\n    default:\n      return `正在处理${entityName}...`;\n  }\n}\n\n/**\n * Validation error formatter for form fields\n */\nexport function formatValidationError(field: string, error: string): string {\n  const fieldMap: Record<string, string> = {\n    'fullName': '姓名',\n    'phone': '电话',\n    'email': '邮箱',\n    'medicalNotes': '病历备注',\n    'appointmentDate': '预约日期',\n    'treatment': '治疗项目',\n    'patient': '患者',\n    'price': '价格',\n    'duration': '时长',\n    'name': '名称',\n    'description': '描述'\n  };\n\n  const fieldName = fieldMap[field] || field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1');\n\n  // Common validation patterns\n  if (error.includes('required')) {\n    return `${fieldName}为必填项。`;\n  }\n  if (error.includes('email')) {\n    return `请输入有效的邮箱地址。`;\n  }\n  if (error.includes('phone')) {\n    return `请输入有效的电话号码。`;\n  }\n  if (error.includes('min')) {\n    const match = error.match(/(\\d+)/);\n    const min = match ? match[1] : '';\n    return `${fieldName}至少需要${min}个字符。`;\n  }\n  if (error.includes('max')) {\n    const match = error.match(/(\\d+)/);\n    const max = match ? match[1] : '';\n    return `${fieldName}不能超过${max}个字符。`;\n  }\n\n  return error;\n}\n\n/**\n * Check if error is a network error\n */\nexport function isNetworkError(error: any): boolean {\n  return !navigator.onLine || \n         error.name === 'NetworkError' || \n         error.message?.includes('fetch') ||\n         error.message?.includes('network');\n}\n\n/**\n * Check if error is a permission error\n */\nexport function isPermissionError(error: any): boolean {\n  const message = (error.error || error.message || '').toLowerCase();\n  return message.includes('permission') || \n         message.includes('unauthorized') || \n         message.includes('forbidden') ||\n         error.status === 401 ||\n         error.status === 403;\n}\n\n/**\n * Check if error is a validation error\n */\nexport function isValidationError(error: any): boolean {\n  const message = (error.error || error.message || '').toLowerCase();\n  return message.includes('validation') || \n         message.includes('invalid') ||\n         message.includes('required') ||\n         error.status === 400 ||\n         error.status === 422;\n}\n\n/**\n * Get appropriate toast duration based on error severity\n */\nexport function getToastDuration(error: any): number {\n  if (isNetworkError(error)) {\n    return 8000; // Longer for network issues\n  }\n  if (isPermissionError(error)) {\n    return 6000; // Medium for permission issues\n  }\n  if (isValidationError(error)) {\n    return 4000; // Shorter for validation issues\n  }\n  return 5000; // Default duration\n}\n", "'use client';\n\nimport React, { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport * as z from 'zod';\nimport { PermissionGate } from '@/lib/role-context';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { patientsApi } from '@/lib/api';\nimport { Patient } from '@/types/clinic';\nimport { toast } from 'sonner';\nimport { formatApiError, formatSuccessMessage, formatLoadingMessage } from '@/lib/error-utils';\nimport { t } from '@/lib/translations';\nconst patientSchema = z.object({\n  fullName: z.string().min(2, 'Full name must be at least 2 characters').max(100, 'Full name cannot exceed 100 characters').regex(/^[a-zA-Z\\s'-]+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes'),\n  phone: z.string().min(10, 'Phone number must be at least 10 digits').max(15, 'Phone number cannot exceed 15 digits').regex(/^[\\+]?[1-9][\\d]{0,15}$/, 'Please enter a valid phone number (e.g., +********** or **********)'),\n  email: z.string().email('Please enter a valid email address (e.g., <EMAIL>)').optional().or(z.literal('')),\n  medicalNotes: z.string().max(2000, 'Medical notes cannot exceed 2000 characters').optional()\n});\ntype PatientFormData = z.infer<typeof patientSchema>;\ninterface PatientFormDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  patient?: Patient;\n  onSuccess?: () => void;\n}\nexport function PatientFormDialog({\n  open,\n  onOpenChange,\n  patient,\n  onSuccess\n}: PatientFormDialogProps) {\n  const [loading, setLoading] = useState(false);\n  const isEditing = !!patient;\n  const form = useForm<PatientFormData>({\n    resolver: zodResolver(patientSchema),\n    defaultValues: {\n      fullName: patient?.fullName || '',\n      phone: patient?.phone || '',\n      email: patient?.email || '',\n      medicalNotes: patient?.medicalNotes || ''\n    }\n  });\n\n  // Reset form when patient changes or dialog opens\n  React.useEffect(() => {\n    if (open) {\n      form.reset({\n        fullName: patient?.fullName || '',\n        phone: patient?.phone || '',\n        email: patient?.email || '',\n        medicalNotes: patient?.medicalNotes || ''\n      });\n    }\n  }, [open, patient, form]);\n  const onSubmit = async (data: PatientFormData) => {\n    setLoading(true);\n\n    // Show loading toast\n    const loadingToast = toast.loading(formatLoadingMessage(isEditing ? 'update' : 'create', 'patient'));\n    try {\n      const patientData = {\n        fullName: data.fullName,\n        phone: data.phone,\n        email: data.email || undefined,\n        medicalNotes: data.medicalNotes || undefined\n      };\n      if (isEditing) {\n        await patientsApi.update(patient.id, patientData);\n        toast.success(formatSuccessMessage('update', 'patient'), {\n          id: loadingToast\n        });\n      } else {\n        await patientsApi.create(patientData);\n        toast.success(formatSuccessMessage('create', 'patient'), {\n          id: loadingToast\n        });\n      }\n      onSuccess?.();\n      onOpenChange(false);\n      form.reset();\n    } catch (error) {\n      console.error('Failed to save patient:', error);\n      const errorMessage = formatApiError(error);\n      toast.error(errorMessage, {\n        id: loadingToast\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return <Dialog open={open} onOpenChange={onOpenChange} data-sentry-element=\"Dialog\" data-sentry-component=\"PatientFormDialog\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n      <DialogContent className=\"sm:max-w-[500px]\" data-sentry-element=\"DialogContent\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n        <DialogHeader data-sentry-element=\"DialogHeader\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n          <DialogTitle data-sentry-element=\"DialogTitle\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n            {isEditing ? t('patients.editPatient') : t('patients.newPatient')}\n          </DialogTitle>\n          <DialogDescription data-sentry-element=\"DialogDescription\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n            {isEditing ? '更新下方的患者信息。' : '填写患者详细信息以创建新记录。'}\n          </DialogDescription>\n        </DialogHeader>\n\n        <Form {...form} data-sentry-element=\"Form\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            {/* Full Name */}\n            <FormField control={form.control} name=\"fullName\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('patients.form.fullName')} *</FormLabel>\n                  <FormControl>\n                    <Input placeholder={t('patients.form.fullNamePlaceholder')} {...field} />\n                  </FormControl>\n                  <p className=\"text-xs text-muted-foreground\">\n                    请输入患者的完整法定姓名，与身份证件上的姓名一致\n                  </p>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"patient-form-dialog.tsx\" />\n\n            {/* Phone */}\n            <FormField control={form.control} name=\"phone\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('patients.form.phone')} *</FormLabel>\n                  <FormControl>\n                    <Input placeholder={t('patients.form.phonePlaceholder')} type=\"tel\" {...field} />\n                  </FormControl>\n                  <p className=\"text-xs text-muted-foreground\">\n                    用于预约提醒和更新的主要联系电话\n                  </p>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"patient-form-dialog.tsx\" />\n\n            {/* Email */}\n            <FormField control={form.control} name=\"email\" render={({\n            field\n          }) => <FormItem>\n                  <FormLabel>{t('patients.form.email')} (可选)</FormLabel>\n                  <FormControl>\n                    <Input placeholder={t('patients.form.emailPlaceholder')} type=\"email\" {...field} />\n                  </FormControl>\n                  <p className=\"text-xs text-muted-foreground\">\n                    用于预约确认和电子收据\n                  </p>\n                  <FormMessage />\n                </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"patient-form-dialog.tsx\" />\n\n            {/* Medical Notes - Only visible to Admin and Doctor */}\n            <PermissionGate permission=\"canEditMedicalNotes\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n              <FormField control={form.control} name=\"medicalNotes\" render={({\n              field\n            }) => <FormItem>\n                    <FormLabel>{t('patients.form.medicalNotes')}</FormLabel>\n                    <FormControl>\n                      <Textarea placeholder={t('patients.form.medicalNotesPlaceholder')} className=\"min-h-[100px]\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                    <p className=\"text-xs text-muted-foreground\">\n                      机密医疗信息 - 仅限医务人员查看\n                    </p>\n                  </FormItem>} data-sentry-element=\"FormField\" data-sentry-source-file=\"patient-form-dialog.tsx\" />\n            </PermissionGate>\n\n            <DialogFooter data-sentry-element=\"DialogFooter\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n              <Button type=\"button\" variant=\"outline\" onClick={() => onOpenChange(false)} disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n                {t('common.actions.cancel')}\n              </Button>\n              <Button type=\"submit\" disabled={loading} data-sentry-element=\"Button\" data-sentry-source-file=\"patient-form-dialog.tsx\">\n                {loading ? '保存中...' : isEditing ? '更新患者' : '创建患者'}\n              </Button>\n            </DialogFooter>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>;\n}", "'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '@clerk/nextjs';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { redirect } from 'next/navigation';\nimport PageContainer from '@/components/layout/page-container';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { IconPlus, IconUsers, IconSearch, IconPhone, IconMail, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';\nimport { patientsApi, appointmentsApi } from '@/lib/api';\nimport { Patient } from '@/types/clinic';\nimport { PatientFormDialog } from '@/components/patients/patient-form-dialog';\nimport { ConfirmationDialog } from '@/components/ui/confirmation-dialog';\nimport { toast } from 'sonner';\nimport { t } from '@/lib/translations';\nexport default function PatientsPage() {\n  const {\n    userId,\n    isLoaded\n  } = useAuth();\n  const {\n    hasPermission\n  } = useRole();\n  const [patients, setPatients] = useState<Patient[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);\n  const [formDialogOpen, setFormDialogOpen] = useState(false);\n  const [editingPatient, setEditingPatient] = useState<Patient | undefined>();\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [patientToDelete, setPatientToDelete] = useState<Patient | undefined>();\n  const [actionLoading, setActionLoading] = useState(false);\n\n  // Redirect if not authenticated\n  if (isLoaded && !userId) {\n    redirect('/auth/sign-in');\n  }\n\n  // Fetch patients data\n  const fetchPatients = async () => {\n    try {\n      setLoading(true);\n      const response = await patientsApi.getAll({\n        limit: 100\n      });\n      setPatients(response.docs);\n      setFilteredPatients(response.docs);\n      setError(null);\n    } catch (err) {\n      console.error('Failed to fetch patients:', err);\n      setError(t('patients.loadingPatients'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (userId) {\n      fetchPatients();\n    }\n  }, [userId]);\n\n  // Handler functions\n  const handleNewPatient = () => {\n    setEditingPatient(undefined);\n    setFormDialogOpen(true);\n  };\n  const handleEditPatient = (patient: Patient) => {\n    setEditingPatient(patient);\n    setFormDialogOpen(true);\n  };\n  const handleDeletePatient = async (patient: Patient) => {\n    // Check if patient has any appointments\n    try {\n      setActionLoading(true);\n      const appointmentsResponse = await appointmentsApi.getAll({\n        limit: 1,\n        where: {\n          patient: {\n            equals: patient.id\n          }\n        }\n      });\n      if (appointmentsResponse.docs.length > 0) {\n        toast.error('无法删除有预约记录的患者。请先取消或完成所有预约。');\n        return;\n      }\n      setPatientToDelete(patient);\n      setDeleteDialogOpen(true);\n    } catch (error) {\n      console.error('Failed to check patient appointments:', error);\n      toast.error('验证患者预约失败');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleDeleteConfirm = async () => {\n    if (!patientToDelete) return;\n    setActionLoading(true);\n    try {\n      await patientsApi.delete(patientToDelete.id);\n      toast.success('患者删除成功');\n      setDeleteDialogOpen(false);\n      setPatientToDelete(undefined);\n      fetchPatients(); // Refresh the list\n    } catch (error) {\n      console.error('Failed to delete patient:', error);\n      toast.error('删除患者失败');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n  const handleFormSuccess = () => {\n    setFormDialogOpen(false);\n    setEditingPatient(undefined);\n    fetchPatients(); // Refresh the list\n  };\n\n  // Filter patients based on search term\n  useEffect(() => {\n    if (!searchTerm) {\n      setFilteredPatients(patients);\n    } else {\n      const filtered = patients.filter(patient => patient.fullName.toLowerCase().includes(searchTerm.toLowerCase()) || patient.phone.includes(searchTerm) || patient.email && patient.email.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredPatients(filtered);\n    }\n  }, [searchTerm, patients]);\n  if (!isLoaded || loading) {\n    return <PageContainer>\n        <div className='flex items-center justify-center h-64'>\n          <div className='text-center'>\n            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4'></div>\n            <p className='text-muted-foreground'>{t('patients.loadingPatients')}</p>\n          </div>\n        </div>\n      </PageContainer>;\n  }\n  return <PageContainer data-sentry-element=\"PageContainer\" data-sentry-component=\"PatientsPage\" data-sentry-source-file=\"page.tsx\">\n      <div className='flex flex-1 flex-col space-y-4'>\n        {/* Header */}\n        <div className='flex items-center justify-between'>\n          <div>\n            <h2 className='text-2xl font-bold tracking-tight flex items-center gap-2'>\n              <IconUsers className='size-6' data-sentry-element=\"IconUsers\" data-sentry-source-file=\"page.tsx\" />\n              {t('patients.title')}\n            </h2>\n            <p className='text-muted-foreground'>\n              {t('patients.subtitle')}\n            </p>\n          </div>\n          <PermissionGate permission=\"canCreatePatients\" data-sentry-element=\"PermissionGate\" data-sentry-source-file=\"page.tsx\">\n            <Button className='flex items-center gap-2' onClick={handleNewPatient} data-sentry-element=\"Button\" data-sentry-source-file=\"page.tsx\">\n              <IconPlus className='size-4' data-sentry-element=\"IconPlus\" data-sentry-source-file=\"page.tsx\" />\n              {t('patients.newPatient')}\n            </Button>\n          </PermissionGate>\n        </div>\n\n        {/* Search Bar */}\n        <div className='flex items-center space-x-2'>\n          <div className='relative flex-1 max-w-sm'>\n            <IconSearch className='absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground' data-sentry-element=\"IconSearch\" data-sentry-source-file=\"page.tsx\" />\n            <Input placeholder={t('patients.searchPlaceholder')} value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className='pl-10' data-sentry-element=\"Input\" data-sentry-source-file=\"page.tsx\" />\n          </div>\n          <Badge variant='outline' data-sentry-element=\"Badge\" data-sentry-source-file=\"page.tsx\">\n            {filteredPatients.length} / {patients.length} {t('patients.patientsCount')}\n          </Badge>\n        </div>\n\n        {/* Error State */}\n        {error && <div className='bg-red-50 border border-red-200 rounded-lg p-4'>\n            <p className='text-red-800'>{error}</p>\n          </div>}\n\n        {/* Patients List */}\n        {!error && <div className='rounded-lg border'>\n            <div className='p-4'>\n              {filteredPatients.length === 0 ? <div className='text-center py-8'>\n                  <IconUsers className='size-12 mx-auto text-muted-foreground mb-4' />\n                  <h3 className='text-lg font-semibold mb-2'>\n                    {searchTerm ? '未找到患者' : '暂无患者注册'}\n                  </h3>\n                  <p className='text-muted-foreground mb-4'>\n                    {searchTerm ? '请尝试调整搜索条件。' : '开始注册您的第一位患者。'}\n                  </p>\n                  {!searchTerm && <PermissionGate permission=\"canCreatePatients\">\n                      <Button onClick={handleNewPatient}>\n                        <IconPlus className='size-4 mr-2' />\n                        {t('patients.newPatient')}\n                      </Button>\n                    </PermissionGate>}\n                </div> : <div className='space-y-4'>\n                  <div className='text-sm text-muted-foreground'>\n                    {searchTerm && `\"${searchTerm}\" 的搜索结果`}\n                  </div>\n                  \n                  {/* Patients Grid */}\n                  <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>\n                    {filteredPatients.map(patient => <div key={patient.id} className='border rounded-lg p-4 hover:shadow-md transition-shadow'>\n                        <div className='space-y-3'>\n                          {/* Patient Name */}\n                          <div className='flex items-start justify-between'>\n                            <div>\n                              <h3 className='font-semibold text-lg'>{patient.fullName}</h3>\n                              <p className='text-sm text-muted-foreground'>\n                                患者编号: {typeof patient.id === 'string' ? patient.id.slice(-8) : patient.id}\n                              </p>\n                            </div>\n\n                            {/* Action Buttons */}\n                            <div className='flex items-center gap-1'>\n                              <Button variant=\"ghost\" size=\"sm\" onClick={() => handleEditPatient(patient)} className=\"h-8 w-8 p-0\">\n                                <IconEdit className=\"h-4 w-4\" />\n                              </Button>\n                              <Button variant=\"ghost\" size=\"sm\" onClick={() => handleDeletePatient(patient)} className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700\" disabled={actionLoading}>\n                                <IconTrash className=\"h-4 w-4\" />\n                              </Button>\n                            </div>\n                          </div>\n\n                          {/* Contact Information */}\n                          <div className='space-y-2'>\n                            <div className='flex items-center gap-2 text-sm'>\n                              <IconPhone className='size-4 text-muted-foreground' />\n                              <span className='font-medium'>{patient.phone}</span>\n                            </div>\n\n                            {patient.email && <div className='flex items-center gap-2 text-sm'>\n                                <IconMail className='size-4 text-muted-foreground' />\n                                <span>{patient.email}</span>\n                              </div>}\n                          </div>\n\n                          {/* Medical Notes - Only visible to Admin and Doctor */}\n                          <PermissionGate permission=\"canViewMedicalNotes\">\n                            {patient.medicalNotes && <div className='space-y-2'>\n                                <p className='text-sm font-medium text-muted-foreground'>病历备注:</p>\n                                <p className='text-sm bg-muted p-2 rounded text-muted-foreground line-clamp-2'>\n                                  {patient.medicalNotes}\n                                </p>\n                              </div>}\n                          </PermissionGate>\n\n                          {/* Registration Date */}\n                          <div className='pt-2 border-t'>\n                            <p className='text-xs text-muted-foreground'>\n                              注册时间: {new Date(patient.createdAt).toLocaleDateString()}\n                            </p>\n                          </div>\n                        </div>\n                      </div>)}\n                  </div>\n                </div>}\n            </div>\n          </div>}\n      </div>\n\n      {/* Dialogs */}\n      <PatientFormDialog open={formDialogOpen} onOpenChange={setFormDialogOpen} patient={editingPatient} onSuccess={handleFormSuccess} data-sentry-element=\"PatientFormDialog\" data-sentry-source-file=\"page.tsx\" />\n\n      <ConfirmationDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen} title=\"删除患者\" description={`您确定要删除患者 ${patientToDelete?.fullName} 吗？此操作无法撤销。`} confirmText=\"删除\" variant=\"destructive\" onConfirm={handleDeleteConfirm} loading={actionLoading} data-sentry-element=\"ConfirmationDialog\" data-sentry-source-file=\"page.tsx\" />\n    </PageContainer>;\n}", "import React from 'react';\nimport { ScrollArea } from '@/components/ui/scroll-area';\nexport default function PageContainer({\n  children,\n  scrollable = true\n}: {\n  children: React.ReactNode;\n  scrollable?: boolean;\n}) {\n  return <>\r\n      {scrollable ? <ScrollArea className='h-[calc(100dvh-52px)]'>\r\n          <div className='flex flex-1 p-4 md:px-6'>{children}</div>\r\n        </ScrollArea> : <div className='flex flex-1 p-4 md:px-6'>{children}</div>}\r\n    </>;\n}", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"zlib\");", "import(/* webpackMode: \"eager\" */ \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\dashboard\\\\patients\\\\page.tsx\");\n", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "'use client';\n\nimport { useAuth } from '@clerk/clerk-react';\nimport { useDerivedAuth } from '@clerk/clerk-react/internal';\nimport type { InitialState } from '@clerk/types';\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nconst PromisifiedAuthContext = React.createContext<Promise<InitialState> | InitialState | null>(null);\n\nexport function PromisifiedAuthProvider({\n  authPromise,\n  children,\n}: {\n  authPromise: Promise<InitialState> | InitialState;\n  children: React.ReactNode;\n}) {\n  return <PromisifiedAuthContext.Provider value={authPromise}>{children}</PromisifiedAuthContext.Provider>;\n}\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data during SSR\n * simply by using the `ClerkProvider`.\n *\n * @example\n * import { useAuth } from '@clerk/nextjs'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * This page will be fully rendered during SSR.\n *\n * ```tsx\n * import { useAuth } from '@clerk/nextjs'\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n * ```\n */\nexport function usePromisifiedAuth() {\n  const isPagesRouter = useRouter();\n  const valueFromContext = React.useContext(PromisifiedAuthContext);\n\n  let resolvedData = valueFromContext;\n  if (valueFromContext && 'then' in valueFromContext) {\n    resolvedData = React.use(valueFromContext);\n  }\n\n  // At this point we should have a usable auth object\n\n  if (typeof window === 'undefined') {\n    // Pages router should always use useAuth as it is able to grab initial auth state from context during SSR.\n    if (isPagesRouter) {\n      return useAuth();\n    }\n\n    // We don't need to deal with Clerk being loaded here\n    return useDerivedAuth(resolvedData);\n  } else {\n    return useAuth(resolvedData);\n  }\n}\n", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "import * as React from 'react';\nimport { cn } from '@/lib/utils';\nfunction Textarea({\n  className,\n  ...props\n}: React.ComponentProps<'textarea'>) {\n  return <textarea data-slot='textarea' className={cn('border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm', className)} {...props} data-sentry-component=\"Textarea\" data-sentry-source-file=\"textarea.tsx\" />;\n}\nexport { Textarea };", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = serverComponentModule.default;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        sentryTraceHeader = requestAsyncStore?.headers.get('sentry-trace') ?? undefined;\n        baggageHeader = requestAsyncStore?.headers.get('baggage') ?? undefined;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      return Sentry.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/dashboard/patients',\n        componentType: 'Page',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = serverComponentModule.generateMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateMetadata, {\n      componentRoute: '/dashboard/patients',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateImageMetadata = serverComponentModule.generateImageMetadata\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateImageMetadata, {\n      componentRoute: '/dashboard/patients',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateImageMetadata',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst generateViewport = serverComponentModule.generateViewport\n  ? Sentry.wrapGenerationFunctionWithSentry(serverComponentModule.generateViewport, {\n      componentRoute: '/dashboard/patients',\n      componentType: 'Page',\n      generationFunctionIdentifier: 'generateViewport',\n      requestAsyncStorage,\n    })\n  : undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\nexport { wrappedServerComponent$1 as default, generateImageMetadata, generateMetadata, generateViewport };\n", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["badgeVariants", "cva", "variants", "variant", "default", "secondary", "destructive", "outline", "defaultVariants", "Badge", "className", "<PERSON><PERSON><PERSON><PERSON>", "props", "Comp", "Slot", "data-slot", "cn", "data-sentry-element", "data-sentry-component", "data-sentry-source-file", "Form", "FormProvider", "FormFieldContext", "React", "FormField", "Provider", "value", "name", "Controller", "useFormField", "fieldContext", "itemContext", "FormItemContext", "getFieldState", "useFormContext", "formState", "useFormState", "fieldState", "id", "formItemId", "formDescriptionId", "formMessageId", "FormItem", "div", "FormLabel", "error", "Label", "data-error", "htmlFor", "FormControl", "aria-<PERSON><PERSON>", "aria-invalid", "FormDescription", "p", "FormMessage", "body", "String", "message", "children", "translations", "nav", "dashboard", "appointments", "patients", "treatments", "admin", "account", "profile", "login", "overview", "title", "subtitle", "metrics", "todayAppointments", "recentPatients", "totalPatients", "activetreatments", "scheduledForToday", "appointmentsScheduledForToday", "newPatientsThisWeek", "patientsRegisteredInLast7Days", "totalRegisteredPatients", "completePatientDatabase", "treatmentOptionsAvailable", "fullServiceCatalog", "active", "last7Days", "allTime", "available", "errors", "loadingDashboard", "failedToLoadMetrics", "newAppointment", "editAppointment", "appointmentDetails", "appointmentsCount", "loadingAppointments", "noAppointments", "filters", "all", "today", "thisWeek", "thisMonth", "status", "date<PERSON><PERSON><PERSON>", "scheduled", "confirmed", "inProgress", "completed", "cancelled", "noShow", "form", "patient", "selectPatient", "treatment", "selectTreatment", "date", "time", "notes", "notesPlaceholder", "newPatient", "editPatient", "patientDetails", "patientsCount", "loadingPatients", "noPatients", "searchPlaceholder", "fullName", "fullNamePlaceholder", "phone", "phonePlaceholder", "email", "emailPlaceholder", "medicalNotes", "medicalNotesPlaceholder", "newTreatment", "editTreatment", "treatmentDetails", "treatmentsCount", "loadingTreatments", "noTreatments", "namePlaceholder", "description", "descriptionPlaceholder", "duration", "durationPlaceholder", "price", "pricePlaceholder", "userManagement", "roleManagement", "systemSettings", "users", "roles", "doctor", "frontDesk", "common", "actions", "save", "cancel", "edit", "delete", "view", "search", "filter", "reset", "submit", "close", "confirm", "back", "next", "previous", "add", "remove", "update", "create", "loading", "success", "warning", "info", "pending", "inactive", "enabled", "disabled", "yesterday", "tomorrow", "lastWeek", "nextWeek", "lastM<PERSON>h", "nextMonth", "thisYear", "lastYear", "nextYear", "confirmDialog", "deleteTitle", "deleteMessage", "cancelTitle", "cancelMessage", "saveTitle", "saveMessage", "validation", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "number", "positive", "general", "network", "unauthorized", "notFound", "serverError", "validationError", "loadFailed", "saveFailed", "deleteFailed", "updateFailed", "createFailed", "saved", "deleted", "updated", "created", "sent", "uploaded", "downloaded", "t", "key", "params", "keys", "split", "k", "console", "warn", "replace", "match", "<PERSON><PERSON><PERSON><PERSON>", "toString", "ConfirmationDialog", "open", "onOpenChange", "confirmText", "cancelText", "onConfirm", "AlertDialog", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alert<PERSON><PERSON><PERSON>H<PERSON>er", "AlertDialogTitle", "AlertDialogDescription", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AlertDialogCancel", "AlertDialogAction", "onClick", "apiRequest", "endpoint", "options", "url", "response", "fetch", "headers", "responseData", "json", "ok", "Error", "statusText", "appointmentsApi", "getAll", "searchParams", "URLSearchParams", "limit", "append", "page", "where", "equals", "query", "getById", "data", "method", "JSON", "stringify", "Date", "toISOString", "todayCount", "docs", "apt", "appointmentDate", "startsWith", "length", "weekAgo", "setDate", "getDate", "allPatients", "patientsApi", "createdAt", "allTreatments", "treatmentsApi", "totalDocs", "formatSuccessMessage", "operation", "entity", "entityName", "toLowerCase", "patientSchema", "z", "min", "max", "regex", "optional", "or", "PatientFormDialog", "onSuccess", "setLoading", "useState", "isEditing", "useForm", "resolver", "zodResolver", "defaultValues", "onSubmit", "loadingToast", "toast", "formatLoadingMessage", "entityMap", "patientData", "undefined", "errorMessage", "formatApiError", "navigator", "onLine", "includes", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "DialogTitle", "DialogDescription", "handleSubmit", "control", "render", "field", "Input", "placeholder", "type", "PermissionGate", "permission", "Textarea", "<PERSON><PERSON><PERSON><PERSON>er", "<PERSON><PERSON>", "PatientsPage", "userId", "isLoaded", "useAuth", "hasPermission", "useRole", "setPatients", "setError", "searchTerm", "setSearchTerm", "filteredPatients", "setFilteredPatients", "formDialogOpen", "setFormDialogOpen", "editingPatient", "setEditingPatient", "deleteDialogOpen", "setDeleteDialogOpen", "patientToDelete", "setPatientToDelete", "actionLoading", "setActionLoading", "redirect", "fetchPatients", "err", "handleNewPatient", "handleEditPatient", "handleDeletePatient", "appointmentsResponse", "handleDeleteConfirm", "<PERSON><PERSON><PERSON><PERSON>", "h2", "IconUsers", "IconPlus", "IconSearch", "onChange", "e", "target", "h3", "map", "slice", "size", "IconEdit", "IconTrash", "IconPhone", "span", "IconMail", "toLocaleDateString", "handleFormSuccess", "scrollable", "ScrollArea", "textarea", "serverComponentModule.default"], "sourceRoot": ""}