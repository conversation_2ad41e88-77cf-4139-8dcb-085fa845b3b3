{"version": 3, "file": "6166.js", "mappings": "6fAMA,IAAMA,EAAkB,CACtBC,WAAY,CACVC,UAAW,cACXC,UAAW,GACXC,SAAU,GACVC,UAAW,EACb,EACAC,MAAO,CACLC,WAAY,IACZC,gBAAiB,CAAC,aAAc,MAAO,cAAe,gBAAiB,MAAO,WAAW,CACzFC,cAAe,GACjB,EACAC,UAAW,CACTC,qBAAsB,GACtBC,0BAA2B,GAC3BC,yBAA0B,EAC1BC,qBAAsB,EACxB,CAoBF,CAGO,OAAMC,EACX,OAAeC,kBAA2B,CACxC,IAAMC,EAAMC,QAAQC,GAAG,CAACC,sBAAsB,CAC9C,GAAI,CAACH,EACH,GADQ,GACF,MAAU,2DAElB,OAAOA,CACT,CAKA,OAAOI,QAAQC,CAAY,CAAkD,CAC3E,GAAI,CACF,IAAML,EAAMM,OAAOC,IAAI,CAAC,IAAI,CAACR,gBAAgB,GAAI,OAC3CS,EAAKC,IAAAA,WAAkB,CAAC1B,EAAgBC,UAAU,CAACG,QAAQ,EAC3DuB,EAASD,IAAAA,cAAqB,CAAC1B,EAAgBC,UAAU,CAACC,SAAS,CAAEe,EAAKQ,GAC/EE,EAAeC,MAAM,CAACL,OAAOC,IAAI,CAAC,iBAEnC,IAAIK,EAAYF,EAAOG,MAAM,CAACR,EAAM,OAAQ,OAC5CO,GAAaF,EAAOI,KAAK,CAAC,OAE1B,IAAMC,EAAM,EAAgBC,UAAU,GAEtC,MAAO,WACLJ,EACAJ,GAAIA,EAAGS,QAAQ,CAAC,OAChBF,IAAKA,EAAIE,QAAQ,CAAC,MACpB,CACF,CAAE,MAAOC,EAAO,CAEd,MADAC,QAAQD,KAAK,CAAC,oBAAqBA,GAC7B,MAAU,mCAClB,CACF,CAKA,OAAOE,QAAQC,CAA6D,CAAU,CACpF,GAAI,CACF,IAAMrB,EAAMM,OAAOC,IAAI,CAAC,IAAI,CAACR,gBAAgB,GAAI,OAC3CS,EAAKF,OAAOC,IAAI,CAACc,EAAcb,EAAE,CAAE,OACnCO,EAAMT,OAAOC,IAAI,CAACc,EAAcN,GAAG,CAAE,OAErCO,EAAWb,IAAAA,gBAAuB,CAAC1B,EAAgBC,UAAU,CAACC,SAAS,CAAEe,EAAKQ,GACnFc,EAAiBX,MAAM,CAACL,OAAOC,IAAI,CAAC,iBACpCe,EAAiBC,UAAU,CAACR,GAE7B,IAAIS,EAAYF,EAAST,MAAM,CAACQ,EAAcT,SAAS,CAAE,MAAO,QAGhE,OAFAY,GAAaF,EAASR,KAAK,CAAC,OAG9B,CAAE,MAAOI,EAAO,CAEd,MADAC,QAAQD,KAAK,CAAC,oBAAqBA,GAC7B,MAAU,mCAClB,CACF,CAKA,OAAOO,KAAKpB,CAAY,CAAU,CAChC,OAAOI,IAAAA,UAAiB,CAAC,UAAUI,MAAM,CAACR,GAAMqB,MAAM,CAAC,MACzD,CAKA,OAAOC,oBAAoBC,EAAiB,EAAE,CAAU,CACtD,OAAOnB,IAAAA,WAAkB,CAACmB,GAAQX,QAAQ,CAAC,MAC7C,CACF,CAkBO,MAAMY,EAIX,aAAsB,MAFdC,QAAAA,CAA4B,EAAE,CAItC,OAAOC,aAA2B,CAIhC,OAHI,EAAaC,QAAQ,EAAE,CACzBH,EAAYG,QAAQ,CAAG,IAAIH,CAAAA,EAEtBA,EAAYG,QAAQ,CAM7BC,sBACEC,CAAc,CACdC,CAAiB,CACjBC,CAAc,CACdC,CAAgB,CAChBC,CAAY,CACZC,GAAmB,CAAI,CACvBC,CAAqB,CACrBC,CAAiB,CACX,CACN,IAAMC,EAAuB,CAC3BC,GAAI7C,EAAe6B,mBAAmB,CAAC,IACvCiB,UAAW,IAAIC,KACfX,mBACAC,SACAC,WACAC,EACAS,WAAYR,EAAQK,EAAE,EAAIL,EAAQS,MAAM,EAAIT,EAAQU,SAAS,CAC7DV,QAAS,IAAI,CAACW,eAAe,CAACX,GAC9BY,UAAW,IAAI,CAACC,WAAW,CAACV,GAC5BW,UAAWX,GAASY,QAAQC,IAAI,oBAAiBC,UACjDhB,EACAC,cACF,EAEA,IAAI,CAACV,QAAQ,CAAC0B,IAAI,CAACd,GAGf,IAAI,CAACZ,QAAQ,CAACF,MAAM,CAAG7C,EAAgBM,KAAK,CAACC,UAAU,EAAE,IACvD,CAACwC,QAAQ,CAAC2B,KAAK,GAIrBtC,QAAQuC,GAAG,CAAC,CAAC,QAAQ,EAAEhB,EAAME,SAAS,CAACe,WAAW,GAAG,GAAG,EAAEjB,EAAMN,MAAM,CAAC,IAAI,EAAEM,EAAML,QAAQ,CAAC,IAAI,EAAEK,EAAMP,SAAS,CAAC,GAAG,EAAEO,EAAMH,OAAO,CAAG,UAAY,UAAU,CAC/J,CAKAqB,YAAY1B,CAAe,CAAE2B,EAAgB,GAAG,CAAmB,CACjE,IAAIC,EAAc,IAAI,CAAChC,QAAQ,CAM/B,OAJII,IACF4B,EAAcA,EADJ,MACsB,CAACpB,GAASA,EAAMR,MAAM,GAAKA,EAAAA,EAGtD4B,EACJC,KAAK,CAAC,CAACF,GACPG,GAAG,CAACtB,GAAU,EACb,GADa,CACL,CACRJ,QAAS,IAAI,CAACW,eAAe,CAACP,EAAMJ,OAAO,EAC7C,EACJ,CAKA,gBAAwBA,CAAY,CAAO,CACzC,GAAI,CAACA,GAA8B,UAAnB,OAAOA,EACrB,OAAOA,EAGT,IAAM2B,EAAY,CAAE,GAAG3B,CAAO,EAQ9B,OANAvD,EAAgBM,KAAK,CAACE,eAAe,CAAC2E,OAAO,CAACC,IACxCF,CAAS,CAACE,EAAM,EAAE,CACpBF,CAAS,CAACE,EAAM,CAAG,IAAI,CAACC,iBAAiB,CAACH,CAAS,CAACE,GAAM,CAE9D,GAEOF,CACT,CAKA,kBAA0B5D,CAAY,CAAU,QAC1CA,EAAKuB,MAAM,EAAI,EACV,CADa,MAGfvB,EAAKgE,SAAS,CAAC,EAAG,GAAK,IAAIC,MAAM,CAACjE,EAAKuB,MAAM,CAAG,GAAKvB,EAAKgE,SAAS,CAAChE,EAAKuB,MAAM,CAAG,EAC3F,CAKA,YAAoBa,CAAiB,CAAsB,CACzD,GAAI,CAACA,EAAS,OAAOc,IAEfgB,EAAY9B,EAAQY,OAAO,CAACC,GAAG,CAAC,mBACtC,GAAIiB,EACF,OAAOA,EADM,KACS,CAAC,IAAI,CAAC,EAAE,CAACC,IAAI,GAGrC,IAAMC,EAAShC,EAAQY,OAAO,CAACC,GAAG,CAAC,oBACnC,GAIO,KAJK,IAKd,CACF,CAGO,MAAMoB,EAKX,aAAsB,MAHdC,aAAAA,CAAmE,IAAIC,SACvEC,aAAAA,CAAmE,IAAID,GAExD,CAEvB,OAAO7C,aAA2B,CAIhC,OAHI,EAAaC,QAAQ,EAAE,CACzB0C,EAAY1C,QAAQ,CAAG,IAAI0C,CAAAA,EAEtBA,EAAY1C,QAAQ,CAM7B8C,eAAe5C,CAAc,CAAE6C,EAA4B,EAAK,CAA4C,CAC1G,IAAMC,EAAMnC,KAAKmC,GAAG,GACdC,EAASF,EACX,CAAEG,IAAKnG,EAAgBU,SAAS,CAACE,yBAAyB,CAAEwF,OAAQ,IAAe,CAAV,CACzE,CAAED,GAD4E,CACvEnG,EAAgBU,SAAS,CAACC,oBAAoB,CAAEyF,OAAQ,GAAU,EAAL,EAEzDJ,EAAmB,IAAI,CAACF,aAAa,CAAG,IAAI,CAACF,aAAa,CACnES,EAAYC,EAAO/B,GAAG,CAACpB,SAE7B,CAAKkD,GAAaJ,EAAMI,EAAUE,SAAS,EAAE,EAEpCC,GAAG,CAACrD,EAAQ,CAAEsD,MAAO,EAAGF,UAAWN,EAAMC,EAAOE,MAAO,GACvD,CAAEM,SAAS,CAAK,GAGrBL,EAAUI,KAAK,EAAIP,EAAOC,GAAG,CACxB,CAD0B,SACf,EAAOI,UAAWF,EAAUE,SAAS,GAGzDF,EAAUI,KAAK,GACR,CAAEC,SAAS,CAAK,EACzB,CAKAC,SAAgB,CACd,IAAMV,EAAMnC,KAAKmC,GAAG,GAEdW,EAA0B,EAAE,CAClC,IAAI,CAAChB,aAAa,CAACT,OAAO,CAAC,CAAC7D,EAAM6B,KAC5B8C,EAAM3E,EAAKiF,SAAS,EAAE,EACV9B,IAAI,CAACtB,EAEvB,GACAyD,EAAczB,OAAO,CAAChC,GAAU,IAAI,CAACyC,aAAa,CAACiB,MAAM,CAAC1D,IAE1D,IAAM2D,EAAiC,EAAE,CACzC,IAAI,CAAChB,aAAa,CAACX,OAAO,CAAC,CAAC7D,EAAM6B,KAC5B8C,EAAM3E,EAAKiF,SAAS,EAAE,EACH9B,IAAI,CAACtB,EAE9B,GACA2D,EAAqB3B,OAAO,CAAChC,GAAU,IAAI,CAAC2C,aAAa,CAACe,MAAM,CAAC1D,GACnE,CACF,CAGO,MAAM4D,EAIX,OAAOC,eAAeC,CAAW,CAAU,CACzC,GAAsB,UAAlB,OAAOA,EAAqB,CAC9B,GAAI,CAACC,SAASD,IAAWA,EAAS,EAChC,CADmC,KAC7B,MAAU,oDAElB,OAAOE,KAAKC,KAAK,CAAU,IAATH,GAAgB,GACpC,CAEA,CAHyC,EAGnB,UAAlB,OAAOA,EAAqB,CAC9B,IAAMI,EAJ6D,WAIzCJ,EAAOK,OAAO,CAAC,WAAY,KACrD,GAAIC,MAAMF,IAAWA,EAAS,EAC5B,CAD+B,KACzB,MAAU,6CAElB,OAAOF,KAAKC,KAAK,CAAU,IAATC,GAAgB,GACpC,CAEA,MAAUG,MAAM,qDAClB,CAKA,OAAOC,aAAaC,CAAY,CAAEC,EAAoB,GAAI,CAAU,CAClE,GAAoB,UAAhB,OAAOD,EACT,MAAM,MAAU,0BAIlB,IAAMxC,EAAYwC,EACfJ,OAAO,CAAC,sDAAuD,IAC/DA,OAAO,CAAC,WAAY,IACpBA,OAAO,CAAC,gBAAiB,IACzBA,OAAO,CAAC,cAAe,IACvB7B,IAAI,GAEP,GAAIP,EAAUrC,MAAM,CAAG8E,EACrB,MAAM,GAD0B,GAChB,CAAC,wBAAwB,EAAEA,EAAU,mBAAmB,CAAC,EAG3E,OAAOzC,CACT,CAKA,OAAO0C,sBAAsBC,CAAc,CAAU,CAGnD,GAAI,CAFiB,CAAC,OAAQ,OAAQ,SAAU,SAAU,WAAY,cAAc,CAElEC,QAAQ,CAACD,GACzB,MADkC,MAClB,0BAGlB,OAAOA,CACT,CAKA,OAAOE,sBAAsBC,CAAqB,CAAU,CAC1D,GAA6B,UAAU,OAA5BA,EACT,MAAM,MAAU,mCAIlB,IAAM9C,EAAY8C,EAAcV,OAAO,CAAC,mBAAoB,IAE5D,GAAIpC,EAAUrC,MAAM,CAAG,GAAKqC,EAAUrC,MAAM,CAAG,IAC7C,CADkD,KAC5C,MAAU,uDAGlB,OAAOqC,CACT,CACF,CAGO,IAAM+C,EAAcnF,EAAYE,WAAW,GAAG,EAC1B2C,EAAY3C,WAAW,GAAG,WChZrD,cACA,yCAEA,OADA,0BACA,CACA,CACA,cACA,YACA,WACA,6FCFuBkF,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,WACzC,IAAMC,EAAiBF,EAAAA,EAAQ,GAAGC,GAAG,CAAC,EAAG,WACnCE,EAAiBH,EAAAA,EAAQ,GAKzBI,EAAmB,GACvB,EAAIC,GAAQ,GAEU,GAAOrG,IAFP,IAEe,GAAGsG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAI,GAAC,CAAG3F,MAAM,EAC3C,EAkBb4F,EAAiBP,EAAAA,EAAQ,CAAC,CACrCQ,SAAUR,EAAAA,EAAM,CAAC,CAAC,YAAa,eAAgB,WAAY,UAAU,CAAE,CACrES,eAAgB,UAChBC,mBAAoB,SACtB,GACAC,SAAUT,EAAejC,GAAG,CAAC,IAAK,kBAClC2C,YAAaT,EAAelC,GAAG,CAAC,IAAK,gBAAgB4C,QAAQ,GAC7DC,SAAUd,EAAAA,EAAQ,GACfC,GAAG,CAAC,IAAM,WACVhC,GAAG,CAAC,KAAM,cACV8C,MAAM,CAAC,GACgB,CAACC,EAAIhH,QAAQ,GAAGsG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAI,GAAC,CAAG3F,MAAM,EACzC,EACvB,cACLsG,UAAWjB,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,WACPhC,GAAG,CAAC,UAAW,oBACf8C,MAAM,CAACX,EAAkB,mBAC5Bc,aAAclB,EAAAA,EAAQ,GACnBC,GAAG,CAAC,EAAG,YACPhC,GAAG,CAAC,IAAK,eACT4C,QAAQ,EACb,GAAGE,MAAM,CAAC,IAEJ3H,EAAK8H,YAAY,IAAI9H,EAAK8H,YAAY,EAAG,GAAwB,GAAG,CAAtB9H,EAAK6H,SAAS,CAI/D,CACDE,QAAS,cACTC,KAAM,CAAC,eAAe,GACrB,EAG2BpB,EAAAA,EAAQ,CAAC,CACrCqB,QAASnB,EAAeoB,IAAI,CAAC,YAC7BC,YAAapB,EAAemB,IAAI,CAAC,YAAYT,QAAQ,GAAGW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KACrEyB,UAAWtB,EAAemB,IAAI,CAAC,cAAcT,QAAQ,GAAGW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KACrE0B,SAAU1B,EAAAA,EAAM,CAAC,CAAC,YAAa,eAAgB,UAAW,aAAa,CAAE,CACvES,eAAgB,UAChBC,mBAAoB,SACtB,GACAE,YAAaV,EACVD,GAAG,CAAC,EAAG,gBACPhC,GAAG,CAAC,IAAK,kBACZ0D,MAAOxB,EAAelC,GAAG,CAAC,IAAM,iBAAiB4C,QAAQ,GACzDe,QAAS5B,EAAAA,EAAQ,GACdC,GAAG,CAAC,EAAG,WACPc,MAAM,CAAC,IACN,GAAI,CAEF,OAAO,CACT,CAAE,KAAM,CACN,OAAO,CACT,CACF,EAAG,YACFA,MAAM,CAACc,IAtEV,IAAMC,EAAY,IAAIlG,KAAKmG,GACrBC,EAAQ,IAAIpG,KAElB,OADAoG,EAAMC,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBH,GAAaE,CACtB,EAkEmC,gBAC9BjB,MAAM,CAjE2B,IACpC,IAAMe,EAAY,IAAIlG,KAAKmG,GACrBG,EAAU,IAAItG,KAEpB,OADAsG,EAAQC,WAAW,CAACD,EAAQE,WAAW,GAAK,GACrCN,CADyC,EAC5BI,CACtB,EA4D2C,cACzCG,KA/DwE,UA+DxDrC,EAAAA,EAAQ,GACrBC,GAAG,CAAC,EAAG,aACPhC,GAAG,CAAC,UAAW,sBACf8C,MAAM,CAACX,EAAkB,YACzBS,QAAQ,GACXyB,UAAWtC,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,aACPhC,GAAG,CAAC,UAAW,sBACf8C,MAAM,CAACX,EAAkB,YACzBS,QAAQ,GACX0B,MAAOvC,EAAAA,EAAO,CAACO,GACZN,GAAG,CAAC,EAAG,cACPhC,GAAG,CAAC,GAAI,cACb,GAAG8C,MAAM,CAAC,IAER,IAAMyB,EAAapJ,EAAKmJ,KAAK,CAACE,MAAM,CAAC,CAACC,EAAKC,KACzC,IAAMC,EAAYD,EAAK7B,QAAQ,CAAG6B,EAAK1B,SAAS,CAC1C4B,EAAeD,EAAa,EAACD,EAAKzB,MAAN,MAAkB,GAAI,EAAK,IAAE,CAC/D,OAAOwB,EAAOE,GAAYC,CAAZD,CAAuB,EACpC,GAEGP,EAAiBjJ,EAAKiJ,cAAc,EAAI,EAI9C,OAFmBG,GADDpJ,EAAKkJ,QACSA,CADA,GAAI,EACQD,GAEvB,CACvB,EAAG,CACDlB,QAAS,aACTC,KAAM,CAAC,iBAAiB,GACvBL,MAAM,CAAC,IAER,IAAMyB,EAAapJ,EAAKmJ,KAAK,CAACE,MAAM,CAAC,CAACC,EAAKC,KACzC,IAAMC,EAAYD,EAAK7B,QAAQ,CAAG6B,EAAK1B,SAAS,CAC1C4B,EAAeD,EAAa,EAACD,EAAKzB,MAAN,MAAkB,GAAI,EAAK,IAAE,CAC/D,OAAOwB,GAAOE,EAAYC,CAAAA,CAAW,EACpC,GAGH,MAAOR,CADgBjJ,EAAKiJ,cAAc,GAAI,GACrBG,CAC3B,EAAG,CACDrB,QAAS,eACTC,KAAM,CAAC,iBAAiB,GACvB,EAG8BpB,EAAAA,EAAQ,CAAC,CACxCjB,OAAQiB,EAAAA,EAAQ,GACbC,GAAG,CAAC,IAAM,aACVhC,GAAG,CAAC,UAAW,sBACf8C,MAAM,CAACX,EAAkB,qBAC5B0C,cAAe9C,EAAAA,EAAM,CAAC,CAAC,OAAQ,OAAQ,SAAU,SAAU,WAAY,cAAc,CAAE,CACrFS,eAAgB,UAChBC,mBAAoB,SACtB,GACAZ,cAAeE,EAAAA,EAAQ,GACpB/B,GAAG,CAAC,IAAK,kBACT4C,QAAQ,GACRW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KAChB2B,MAAOxB,EAAelC,GAAG,CAAC,IAAK,gBAAgB4C,QAAQ,EACzD,GAAGE,MAAM,CAAC,GAGR,CADsC,CAAC,OAAQ,SAAU,SAAU,WAAW,CAC5CnB,QAAQ,CAACxG,EAAK0J,aAAa,GAAG,EAClDhD,aAAa,EAAI1G,EAAK0G,aAAa,CAACvC,IAAI,GAAG5C,MAAM,CAAG,EAGjE,CACDwG,QAAS,gBACTC,KAAM,CAAC,gBAAgB,GACtB,EAG8BpB,EAAQ,CAAC,CACxC+C,SAAU7C,EACPD,GAAG,CAAC,EAAG,cACPhC,GAAG,CAAC,GAAI,eACR+E,KAAK,CAAC,6BAA8B,kBACvCC,MAAO/C,EACJ8C,KAAK,CAACE,gBAAY,cACrBC,MAAOnD,EAAAA,EAAQ,GACZmD,KAAK,CAAC,cACNlF,GAAG,CAAC,IAAK,kBACT4C,QAAQ,GACRW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KAChBoD,aAAcjD,EAAelC,GAAG,CAAC,IAAM,mBAAmB4C,QAAQ,EACpE,GAAG,EAGmCb,EAAQ,CAAC,CAC7CqD,OAAQrD,EAAAA,EAAM,CAAC,CAAC,QAAS,OAAQ,YAAa,OAAQ,YAAY,CAAE,CAClES,eAAgB,UAChBC,mBAAoB,SACtB,GACAiB,MAAOxB,EAAelC,GAAG,CAAC,IAAK,oBAAoB4C,QAAQ,EAC7D,GAAG,EAG6Bb,EAAQ,CAAC,CACvCsD,OAAQnD,EAAelC,GAAG,CAAC,IAAK,mBAAmB4C,QAAQ,GAC3DwC,OAAQrD,EAAAA,EAAM,CAAC,CAAC,QAAS,OAAQ,YAAa,OAAQ,YAAY,EAAEa,QAAQ,GAC5Ea,SAAU1B,EAAAA,EAAM,CAAC,CAAC,YAAa,eAAgB,UAAW,aAAa,EAAEa,QAAQ,GACjF0C,UAAWpD,EAAemB,IAAI,CAAC,YAAYT,QAAQ,GAAGW,EAAE,CAACxB,EAAAA,EAAS,CAAC,KACnEwD,SAAUxD,EAAAA,EAAQ,GACfa,QAAQ,GACRE,MAAM,CAAC,IACN,GAAI,CAACgB,EAAM,OAAO,EAClB,GAAI,CAEF,OAAO,CACT,CAAE,KAAM,CACN,OAAO,CACT,CACF,EAAG,cACL0B,OAAQzD,EAAAA,EAAQ,GACba,QAAQ,GACRE,MAAM,CAAC,IACN,GAAI,CAACgB,EAAM,OAAO,EAClB,GAAI,CAEF,OAAO,CACT,CAAE,KAAM,CACN,OAAO,CACT,CACF,EAAG,cACL2B,UAAW1D,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,aACPhC,GAAG,CAAC,UAAW,sBACf4C,QAAQ,GACX8C,UAAW3D,EAAAA,EAAQ,GAChBC,GAAG,CAAC,EAAG,aACPhC,GAAG,CAAC,UAAW,sBACf4C,QAAQ,EACb,GAAGE,MAAM,CAAC,GAER,CAAI3H,EAAKoK,QAAQ,GAAIpK,EAAKqK,MAAM,EAAE,IACX7H,KAAKxC,EAAKoK,CAEZI,OAFoB,GACxB,IAAIhI,KAAKxC,EAAKqK,MAAM,EAIpC,CACDtC,QAAS,eACTC,KAAM,CAAC,SAAS,GACfL,MAAM,CAAC,GAER,KAAuBzE,IAAnBlD,EAAKsK,SAAS,OAAqCpH,IAAnBlD,EAAKuK,KAAyB,IAAhB,EACzCvK,EAAKsK,SAAS,EAAItK,EAAKuK,SAAS,CAGxC,CACDxC,QAAS,eACTC,KAAM,CAAC,YAAY,GAClB,IAoCUyC,EAAyB,GAC7BC,EAAOA,MAAM,CAAC/G,GAAG,CAAC9C,GAAU,EACjCiD,GADiC,GAC1BjD,EAAMmH,IAAI,CAAC2C,IAAI,CAAC,KACvB5C,QAASlH,EAAMkH,OAAO,CACtB6C,KAAM/J,EAAM+J,IAAI,CAClB", "sources": ["webpack://next-shadcn-dashboard-starter/./src/lib/billing-security.ts", "webpack://next-shadcn-dashboard-starter/./node_modules/.pnpm/@opentelemetry+instrumentat_04f370d515cee0be955272f826166073/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync", "webpack://next-shadcn-dashboard-starter/./src/lib/validation/billing-schemas.ts"], "sourcesContent": ["// Comprehensive security measures for billing system\n// Handles data encryption, audit logging, and secure payment processing\n\nimport crypto from 'crypto';\n\n// Security configuration\nconst SECURITY_CONFIG = {\n  encryption: {\n    algorithm: 'aes-256-gcm',\n    keyLength: 32,\n    ivLength: 16,\n    tagLength: 16,\n  },\n  audit: {\n    maxLogSize: 10000, // Maximum number of audit log entries\n    sensitiveFields: ['cardNumber', 'cvv', 'bankAccount', 'transactionId', 'ssn', 'idNumber'],\n    retentionDays: 365, // Keep audit logs for 1 year\n  },\n  rateLimit: {\n    maxRequestsPerMinute: 60,\n    maxPaymentRequestsPerHour: 10,\n    maxFailedAttemptsPerHour: 5,\n    blockDurationMinutes: 30,\n  },\n  validation: {\n    maxAmountPerTransaction: 50000, // Maximum transaction amount in USD\n    maxDailyAmountPerUser: 100000, // Maximum daily amount per user\n    suspiciousAmountThreshold: 10000, // Flag transactions above this amount\n  },\n  session: {\n    maxIdleTimeMinutes: 30,\n    maxSessionDurationHours: 8,\n    requireReauthForSensitiveOps: true,\n  },\n  monitoring: {\n    enableRealTimeAlerts: true,\n    alertThresholds: {\n      failedLoginAttempts: 3,\n      suspiciousTransactionAmount: 10000,\n      rapidTransactionCount: 5, // 5 transactions within 5 minutes\n      unusualAccessPattern: true,\n    },\n  },\n};\n\n// Encryption utilities for sensitive financial data\nexport class DataEncryption {\n  private static getEncryptionKey(): string {\n    const key = process.env.BILLING_ENCRYPTION_KEY;\n    if (!key) {\n      throw new Error('BILLING_ENCRYPTION_KEY environment variable is required');\n    }\n    return key;\n  }\n\n  /**\n   * Encrypt sensitive data\n   */\n  static encrypt(data: string): { encrypted: string; iv: string; tag: string } {\n    try {\n      const key = Buffer.from(this.getEncryptionKey(), 'hex');\n      const iv = crypto.randomBytes(SECURITY_CONFIG.encryption.ivLength);\n      const cipher = crypto.createCipheriv(SECURITY_CONFIG.encryption.algorithm, key, iv);\n      (cipher as any).setAAD(Buffer.from('billing-data'));\n\n      let encrypted = cipher.update(data, 'utf8', 'hex');\n      encrypted += cipher.final('hex');\n      \n      const tag = (cipher as any).getAuthTag();\n\n      return {\n        encrypted,\n        iv: iv.toString('hex'),\n        tag: tag.toString('hex'),\n      };\n    } catch (error) {\n      console.error('Encryption error:', error);\n      throw new Error('Failed to encrypt sensitive data');\n    }\n  }\n\n  /**\n   * Decrypt sensitive data\n   */\n  static decrypt(encryptedData: { encrypted: string; iv: string; tag: string }): string {\n    try {\n      const key = Buffer.from(this.getEncryptionKey(), 'hex');\n      const iv = Buffer.from(encryptedData.iv, 'hex');\n      const tag = Buffer.from(encryptedData.tag, 'hex');\n      \n      const decipher = crypto.createDecipheriv(SECURITY_CONFIG.encryption.algorithm, key, iv);\n      (decipher as any).setAAD(Buffer.from('billing-data'));\n      (decipher as any).setAuthTag(tag);\n\n      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n      decrypted += decipher.final('utf8');\n\n      return decrypted;\n    } catch (error) {\n      console.error('Decryption error:', error);\n      throw new Error('Failed to decrypt sensitive data');\n    }\n  }\n\n  /**\n   * Hash sensitive data for comparison (one-way)\n   */\n  static hash(data: string): string {\n    return crypto.createHash('sha256').update(data).digest('hex');\n  }\n\n  /**\n   * Generate secure random token\n   */\n  static generateSecureToken(length: number = 32): string {\n    return crypto.randomBytes(length).toString('hex');\n  }\n}\n\n// Audit logging for financial operations\nexport interface AuditLogEntry {\n  id: string;\n  timestamp: Date;\n  userId: string;\n  userEmail: string;\n  action: string;\n  resource: string;\n  resourceId?: string;\n  details: any;\n  ipAddress?: string;\n  userAgent?: string;\n  success: boolean;\n  errorMessage?: string;\n}\n\nexport class AuditLogger {\n  private static instance: AuditLogger;\n  private auditLog: AuditLogEntry[] = [];\n\n  private constructor() {}\n\n  static getInstance(): AuditLogger {\n    if (!AuditLogger.instance) {\n      AuditLogger.instance = new AuditLogger();\n    }\n    return AuditLogger.instance;\n  }\n\n  /**\n   * Log financial operation\n   */\n  logFinancialOperation(\n    userId: string,\n    userEmail: string,\n    action: string,\n    resource: string,\n    details: any,\n    success: boolean = true,\n    errorMessage?: string,\n    request?: Request\n  ): void {\n    const entry: AuditLogEntry = {\n      id: DataEncryption.generateSecureToken(16),\n      timestamp: new Date(),\n      userId,\n      userEmail,\n      action,\n      resource,\n      resourceId: details.id || details.billId || details.paymentId,\n      details: this.sanitizeDetails(details),\n      ipAddress: this.getClientIP(request),\n      userAgent: request?.headers.get('user-agent') || undefined,\n      success,\n      errorMessage,\n    };\n\n    this.auditLog.push(entry);\n\n    // Keep only the most recent entries\n    if (this.auditLog.length > SECURITY_CONFIG.audit.maxLogSize) {\n      this.auditLog.shift();\n    }\n\n    // Log to console for development (in production, this should go to a secure logging service)\n    console.log(`[AUDIT] ${entry.timestamp.toISOString()} - ${entry.action} on ${entry.resource} by ${entry.userEmail} - ${entry.success ? 'SUCCESS' : 'FAILED'}`);\n  }\n\n  /**\n   * Get audit log entries (filtered for security)\n   */\n  getAuditLog(userId?: string, limit: number = 100): AuditLogEntry[] {\n    let filteredLog = this.auditLog;\n\n    if (userId) {\n      filteredLog = filteredLog.filter(entry => entry.userId === userId);\n    }\n\n    return filteredLog\n      .slice(-limit)\n      .map(entry => ({\n        ...entry,\n        details: this.sanitizeDetails(entry.details),\n      }));\n  }\n\n  /**\n   * Remove sensitive information from audit details\n   */\n  private sanitizeDetails(details: any): any {\n    if (!details || typeof details !== 'object') {\n      return details;\n    }\n\n    const sanitized = { ...details };\n    \n    SECURITY_CONFIG.audit.sensitiveFields.forEach(field => {\n      if (sanitized[field]) {\n        sanitized[field] = this.maskSensitiveData(sanitized[field]);\n      }\n    });\n\n    return sanitized;\n  }\n\n  /**\n   * Mask sensitive data for logging\n   */\n  private maskSensitiveData(data: string): string {\n    if (data.length <= 4) {\n      return '****';\n    }\n    return data.substring(0, 2) + '*'.repeat(data.length - 4) + data.substring(data.length - 2);\n  }\n\n  /**\n   * Extract client IP address from request\n   */\n  private getClientIP(request?: Request): string | undefined {\n    if (!request) return undefined;\n\n    const forwarded = request.headers.get('x-forwarded-for');\n    if (forwarded) {\n      return forwarded.split(',')[0].trim();\n    }\n\n    const realIP = request.headers.get('x-real-ip');\n    if (realIP) {\n      return realIP;\n    }\n\n    return 'unknown';\n  }\n}\n\n// Rate limiting for API endpoints\nexport class RateLimiter {\n  private static instance: RateLimiter;\n  private requestCounts: Map<string, { count: number; resetTime: number }> = new Map();\n  private paymentCounts: Map<string, { count: number; resetTime: number }> = new Map();\n\n  private constructor() {}\n\n  static getInstance(): RateLimiter {\n    if (!RateLimiter.instance) {\n      RateLimiter.instance = new RateLimiter();\n    }\n    return RateLimiter.instance;\n  }\n\n  /**\n   * Check if request is within rate limit\n   */\n  checkRateLimit(userId: string, isPaymentRequest: boolean = false): { allowed: boolean; resetTime?: number } {\n    const now = Date.now();\n    const limits = isPaymentRequest \n      ? { max: SECURITY_CONFIG.rateLimit.maxPaymentRequestsPerHour, window: 60 * 60 * 1000 }\n      : { max: SECURITY_CONFIG.rateLimit.maxRequestsPerMinute, window: 60 * 1000 };\n\n    const counts = isPaymentRequest ? this.paymentCounts : this.requestCounts;\n    const userCount = counts.get(userId);\n\n    if (!userCount || now > userCount.resetTime) {\n      // Reset or initialize counter\n      counts.set(userId, { count: 1, resetTime: now + limits.window });\n      return { allowed: true };\n    }\n\n    if (userCount.count >= limits.max) {\n      return { allowed: false, resetTime: userCount.resetTime };\n    }\n\n    userCount.count++;\n    return { allowed: true };\n  }\n\n  /**\n   * Clear expired rate limit entries\n   */\n  cleanup(): void {\n    const now = Date.now();\n    \n    const usersToDelete: string[] = [];\n    this.requestCounts.forEach((data, userId) => {\n      if (now > data.resetTime) {\n        usersToDelete.push(userId);\n      }\n    });\n    usersToDelete.forEach(userId => this.requestCounts.delete(userId));\n\n    const paymentUsersToDelete: string[] = [];\n    this.paymentCounts.forEach((data, userId) => {\n      if (now > data.resetTime) {\n        paymentUsersToDelete.push(userId);\n      }\n    });\n    paymentUsersToDelete.forEach(userId => this.paymentCounts.delete(userId));\n  }\n}\n\n// Input sanitization for financial data\nexport class InputSanitizer {\n  /**\n   * Sanitize and validate monetary amounts\n   */\n  static sanitizeAmount(amount: any): number {\n    if (typeof amount === 'number') {\n      if (!isFinite(amount) || amount < 0) {\n        throw new Error('Invalid amount: must be a positive finite number');\n      }\n      return Math.round(amount * 100) / 100; // Round to 2 decimal places\n    }\n\n    if (typeof amount === 'string') {\n      const parsed = parseFloat(amount.replace(/[^\\d.-]/g, ''));\n      if (isNaN(parsed) || parsed < 0) {\n        throw new Error('Invalid amount: must be a positive number');\n      }\n      return Math.round(parsed * 100) / 100;\n    }\n\n    throw new Error('Invalid amount: must be a number or numeric string');\n  }\n\n  /**\n   * Sanitize text input to prevent XSS and injection attacks\n   */\n  static sanitizeText(text: string, maxLength: number = 1000): string {\n    if (typeof text !== 'string') {\n      throw new Error('Input must be a string');\n    }\n\n    // Remove potentially dangerous characters and HTML tags\n    const sanitized = text\n      .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n      .replace(/<[^>]*>/g, '')\n      .replace(/javascript:/gi, '')\n      .replace(/on\\w+\\s*=/gi, '')\n      .trim();\n\n    if (sanitized.length > maxLength) {\n      throw new Error(`Input too long: maximum ${maxLength} characters allowed`);\n    }\n\n    return sanitized;\n  }\n\n  /**\n   * Validate and sanitize payment method\n   */\n  static sanitizePaymentMethod(method: string): string {\n    const validMethods = ['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'];\n    \n    if (!validMethods.includes(method)) {\n      throw new Error('Invalid payment method');\n    }\n\n    return method;\n  }\n\n  /**\n   * Sanitize transaction ID\n   */\n  static sanitizeTransactionId(transactionId: string): string {\n    if (typeof transactionId !== 'string') {\n      throw new Error('Transaction ID must be a string');\n    }\n\n    // Allow only alphanumeric characters, hyphens, and underscores\n    const sanitized = transactionId.replace(/[^a-zA-Z0-9\\-_]/g, '');\n    \n    if (sanitized.length < 3 || sanitized.length > 100) {\n      throw new Error('Transaction ID must be between 3 and 100 characters');\n    }\n\n    return sanitized;\n  }\n}\n\n// Export singleton instances\nexport const auditLogger = AuditLogger.getInstance();\nexport const rateLimiter = RateLimiter.getInstance();\n\n// Cleanup function to be called periodically\nexport const cleanupSecurity = () => {\n  rateLimiter.cleanup();\n};\n", "function webpackEmptyContext(req) {\n\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\te.code = 'MODULE_NOT_FOUND';\n\tthrow e;\n}\nwebpackEmptyContext.keys = () => ([]);\nwebpackEmptyContext.resolve = webpackEmptyContext;\nwebpackEmptyContext.id = 45962;\nmodule.exports = webpackEmptyContext;", "// Comprehensive validation schemas for billing forms\n// Provides robust client-side validation with detailed error messages in Chinese\n\nimport * as z from 'zod';\n\n// Common validation patterns\nconst positiveNumber = z.number().min(0, '金额不能为负数');\nconst requiredString = z.string().min(1, '此字段为必填项');\nconst optionalString = z.string();\nconst phoneRegex = /^1[3-9]\\d{9}$/;\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n\n// Custom validation functions\nconst validateCurrency = (value: number) => {\n  if (value < 0) return false;\n  // Check for reasonable decimal places (max 2)\n  const decimalPlaces = (value.toString().split('.')[1] || '').length;\n  return decimalPlaces <= 2;\n};\n\nconst validateDateNotInPast = (date: string) => {\n  const inputDate = new Date(date);\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return inputDate >= today;\n};\n\nconst validateDateNotTooFarInFuture = (date: string) => {\n  const inputDate = new Date(date);\n  const maxDate = new Date();\n  maxDate.setFullYear(maxDate.getFullYear() + 2); // Max 2 years in future\n  return inputDate <= maxDate;\n};\n\n// Bill Item validation schema\nexport const billItemSchema = z.object({\n  itemType: z.enum(['treatment', 'consultation', 'material', 'service'], {\n    required_error: '请选择项目类型',\n    invalid_type_error: '无效的项目类型',\n  }),\n  itemName: requiredString.max(100, '项目名称不能超过100个字符'),\n  description: optionalString.max(500, '描述不能超过500个字符').optional(),\n  quantity: z.number()\n    .min(0.01, '数量必须大于0')\n    .max(9999, '数量不能超过9999')\n    .refine((val) => {\n      const decimalPlaces = (val.toString().split('.')[1] || '').length;\n      return decimalPlaces <= 3;\n    }, '数量最多支持3位小数'),\n  unitPrice: z.number()\n    .min(0, '单价不能为负数')\n    .max(999999.99, '单价不能超过999,999.99')\n    .refine(validateCurrency, '单价格式无效，最多支持2位小数'),\n  discountRate: z.number()\n    .min(0, '折扣率不能为负数')\n    .max(100, '折扣率不能超过100%')\n    .optional(),\n}).refine((data) => {\n  // Validate that discount rate makes sense\n  if (data.discountRate && data.discountRate > 0 && data.unitPrice === 0) {\n    return false;\n  }\n  return true;\n}, {\n  message: '单价为0时不能设置折扣',\n  path: ['discountRate'],\n});\n\n// Bill form validation schema\nexport const billFormSchema = z.object({\n  patient: requiredString.uuid('请选择有效的患者'),\n  appointment: optionalString.uuid('请选择有效的预约').optional().or(z.literal('')),\n  treatment: optionalString.uuid('请选择有效的治疗项目').optional().or(z.literal('')),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional'], {\n    required_error: '请选择账单类型',\n    invalid_type_error: '无效的账单类型',\n  }),\n  description: requiredString\n    .min(2, '账单描述至少需要2个字符')\n    .max(200, '账单描述不能超过200个字符'),\n  notes: optionalString.max(1000, '备注不能超过1000个字符').optional(),\n  dueDate: z.string()\n    .min(1, '请选择到期日期')\n    .refine((date) => {\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的日期')\n    .refine(validateDateNotInPast, '到期日期不能是过去的日期')\n    .refine(validateDateNotTooFarInFuture, '到期日期不能超过2年'),\n  discountAmount: z.number()\n    .min(0, '折扣金额不能为负数')\n    .max(999999.99, '折扣金额不能超过999,999.99')\n    .refine(validateCurrency, '折扣金额格式无效')\n    .optional(),\n  taxAmount: z.number()\n    .min(0, '税费金额不能为负数')\n    .max(999999.99, '税费金额不能超过999,999.99')\n    .refine(validateCurrency, '税费金额格式无效')\n    .optional(),\n  items: z.array(billItemSchema)\n    .min(1, '至少需要一个账单项目')\n    .max(50, '账单项目不能超过50个'),\n}).refine((data) => {\n  // Validate that bill has reasonable total\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  const taxAmount = data.taxAmount || 0;\n  const finalTotal = itemsTotal + taxAmount - discountAmount;\n  \n  return finalTotal >= 0;\n}, {\n  message: '账单总金额不能为负数',\n  path: ['discountAmount'],\n}).refine((data) => {\n  // Validate discount doesn't exceed subtotal\n  const itemsTotal = data.items.reduce((sum, item) => {\n    const itemTotal = item.quantity * item.unitPrice;\n    const itemDiscount = itemTotal * ((item.discountRate || 0) / 100);\n    return sum + (itemTotal - itemDiscount);\n  }, 0);\n  \n  const discountAmount = data.discountAmount || 0;\n  return discountAmount <= itemsTotal;\n}, {\n  message: '折扣金额不能超过项目小计',\n  path: ['discountAmount'],\n});\n\n// Payment form validation schema\nexport const paymentFormSchema = z.object({\n  amount: z.number()\n    .min(0.01, '支付金额必须大于0')\n    .max(999999.99, '支付金额不能超过999,999.99')\n    .refine(validateCurrency, '支付金额格式无效，最多支持2位小数'),\n  paymentMethod: z.enum(['cash', 'card', 'wechat', 'alipay', 'transfer', 'installment'], {\n    required_error: '请选择支付方式',\n    invalid_type_error: '无效的支付方式',\n  }),\n  transactionId: z.string()\n    .max(100, '交易ID不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  notes: optionalString.max(500, '备注不能超过500个字符').optional(),\n}).refine((data) => {\n  // Require transaction ID for certain payment methods\n  const methodsRequiringTransactionId = ['card', 'wechat', 'alipay', 'transfer'];\n  if (methodsRequiringTransactionId.includes(data.paymentMethod)) {\n    return data.transactionId && data.transactionId.trim().length > 0;\n  }\n  return true;\n}, {\n  message: '此支付方式需要提供交易ID',\n  path: ['transactionId'],\n});\n\n// Patient form validation schema (for billing context)\nexport const patientFormSchema = z.object({\n  fullName: requiredString\n    .min(2, '姓名至少需要2个字符')\n    .max(50, '姓名不能超过50个字符')\n    .regex(/^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, '姓名只能包含中文、英文和空格'),\n  phone: requiredString\n    .regex(phoneRegex, '请输入有效的手机号码'),\n  email: z.string()\n    .email('请输入有效的邮箱地址')\n    .max(100, '邮箱地址不能超过100个字符')\n    .optional()\n    .or(z.literal('')),\n  medicalNotes: optionalString.max(2000, '医疗备注不能超过2000个字符').optional(),\n});\n\n// Bill status update validation schema\nexport const billStatusUpdateSchema = z.object({\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled'], {\n    required_error: '请选择账单状态',\n    invalid_type_error: '无效的账单状态',\n  }),\n  notes: optionalString.max(500, '状态更新备注不能超过500个字符').optional(),\n});\n\n// Filter validation schema\nexport const billFilterSchema = z.object({\n  search: optionalString.max(100, '搜索关键词不能超过100个字符').optional(),\n  status: z.enum(['draft', 'sent', 'confirmed', 'paid', 'cancelled']).optional(),\n  billType: z.enum(['treatment', 'consultation', 'deposit', 'additional']).optional(),\n  patientId: optionalString.uuid('请选择有效的患者').optional().or(z.literal('')),\n  dateFrom: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的开始日期'),\n  dateTo: z.string()\n    .optional()\n    .refine((date) => {\n      if (!date) return true;\n      try {\n        new Date(date);\n        return true;\n      } catch {\n        return false;\n      }\n    }, '请输入有效的结束日期'),\n  amountMin: z.number()\n    .min(0, '最小金额不能为负数')\n    .max(999999.99, '最小金额不能超过999,999.99')\n    .optional(),\n  amountMax: z.number()\n    .min(0, '最大金额不能为负数')\n    .max(999999.99, '最大金额不能超过999,999.99')\n    .optional(),\n}).refine((data) => {\n  // Validate date range\n  if (data.dateFrom && data.dateTo) {\n    const fromDate = new Date(data.dateFrom);\n    const toDate = new Date(data.dateTo);\n    return fromDate <= toDate;\n  }\n  return true;\n}, {\n  message: '开始日期不能晚于结束日期',\n  path: ['dateTo'],\n}).refine((data) => {\n  // Validate amount range\n  if (data.amountMin !== undefined && data.amountMax !== undefined) {\n    return data.amountMin <= data.amountMax;\n  }\n  return true;\n}, {\n  message: '最小金额不能大于最大金额',\n  path: ['amountMax'],\n});\n\n// Export types for TypeScript\nexport type BillItemFormData = z.infer<typeof billItemSchema>;\nexport type BillFormData = z.infer<typeof billFormSchema>;\nexport type PaymentFormData = z.infer<typeof paymentFormSchema>;\nexport type PatientFormData = z.infer<typeof patientFormSchema>;\nexport type BillStatusUpdateData = z.infer<typeof billStatusUpdateSchema>;\nexport type BillFilterData = z.infer<typeof billFilterSchema>;\n\n// Validation helper functions\nexport const validateBillItem = (data: unknown) => {\n  return billItemSchema.safeParse(data);\n};\n\nexport const validateBillForm = (data: unknown) => {\n  return billFormSchema.safeParse(data);\n};\n\nexport const validatePaymentForm = (data: unknown) => {\n  return paymentFormSchema.safeParse(data);\n};\n\nexport const validatePatientForm = (data: unknown) => {\n  return patientFormSchema.safeParse(data);\n};\n\nexport const validateBillStatusUpdate = (data: unknown) => {\n  return billStatusUpdateSchema.safeParse(data);\n};\n\nexport const validateBillFilter = (data: unknown) => {\n  return billFilterSchema.safeParse(data);\n};\n\n// Custom validation error formatter\nexport const formatValidationErrors = (errors: z.ZodError) => {\n  return errors.errors.map(error => ({\n    field: error.path.join('.'),\n    message: error.message,\n    code: error.code,\n  }));\n};\n"], "names": ["SECURITY_CONFIG", "encryption", "algorithm", "<PERSON><PERSON><PERSON><PERSON>", "iv<PERSON><PERSON><PERSON>", "tag<PERSON><PERSON><PERSON>", "audit", "maxLogSize", "sensitiveFields", "retentionDays", "rateLimit", "maxRequestsPerMinute", "maxPaymentRequestsPerHour", "maxFailedAttemptsPerHour", "blockDurationMinutes", "DataEncryption", "getEncryptionKey", "key", "process", "env", "BILLING_ENCRYPTION_KEY", "encrypt", "data", "<PERSON><PERSON><PERSON>", "from", "iv", "crypto", "cipher", "setAAD", "encrypted", "update", "final", "tag", "getAuthTag", "toString", "error", "console", "decrypt", "encryptedData", "decipher", "setAuthTag", "decrypted", "hash", "digest", "generateSecureToken", "length", "AuditLogger", "auditLog", "getInstance", "instance", "logFinancialOperation", "userId", "userEmail", "action", "resource", "details", "success", "errorMessage", "request", "entry", "id", "timestamp", "Date", "resourceId", "billId", "paymentId", "sanitizeDetails", "ip<PERSON><PERSON><PERSON>", "getClientIP", "userAgent", "headers", "get", "undefined", "push", "shift", "log", "toISOString", "getAuditLog", "limit", "filteredLog", "slice", "map", "sanitized", "for<PERSON>ach", "field", "maskSensitiveData", "substring", "repeat", "forwarded", "trim", "realIP", "RateLimiter", "requestCounts", "Map", "paymentCounts", "checkRateLimit", "isPaymentRequest", "now", "limits", "max", "window", "userCount", "counts", "resetTime", "set", "count", "allowed", "cleanup", "usersToDelete", "delete", "paymentUsersToDelete", "InputSanitizer", "sanitizeAmount", "amount", "isFinite", "Math", "round", "parsed", "replace", "isNaN", "Error", "sanitizeText", "text", "max<PERSON><PERSON><PERSON>", "sanitizePaymentMethod", "method", "includes", "sanitizeTransactionId", "transactionId", "auditLogger", "z", "min", "requiredString", "optionalString", "validateCurrency", "value", "split", "billItemSchema", "itemType", "required_error", "invalid_type_error", "itemName", "description", "optional", "quantity", "refine", "val", "unitPrice", "discountRate", "message", "path", "patient", "uuid", "appointment", "or", "treatment", "billType", "notes", "dueDate", "validateDateNotInPast", "inputDate", "date", "today", "setHours", "maxDate", "setFullYear", "getFullYear", "discountAmount", "taxAmount", "items", "itemsTotal", "reduce", "sum", "item", "itemTotal", "itemDiscount", "paymentMethod", "fullName", "regex", "phone", "phoneRegex", "email", "medicalNotes", "status", "search", "patientId", "dateFrom", "dateTo", "amountMin", "amountMax", "toDate", "formatValidationErrors", "errors", "join", "code"], "sourceRoot": ""}