{"version": 3, "file": "../app/api/patients/[id]/timeline/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,iDCAA,sDCAA,6FCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,0CCAA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,uWCiBO,IAAMA,EAAMC,CAAAA,EAAAA,EAAAA,EAAAA,CAAmB,OAAOC,EAAyBC,EAAsB,QAAEC,CAAM,CAA8B,IAChI,GAAI,CACF,IAAMC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBJ,CAAAA,GACpCK,CADoCL,CAAAA,CAC9B,GAAIM,GAAIL,CAAAA,EAAQI,GAAG,EAAXJ,EAGNM,QAASF,CAAAA,EAAIG,CAAJH,WAAgB,CAACI,GAAG,CAAC,OAAY,SAClDC,EAAOH,QAASF,CAAAA,EAAIG,CAAJH,WAAgB,CAACI,GAAG,CAAC,MAAW,QAChDE,EAAON,EAAIG,CAAAA,WAAY,CAACC,GAAG,CAAC,QAE5BG,EAA2B,EAAE,CAGnC,GAAI,CAACD,GAAiB,CAATA,YAAwB,GAAxBA,EAAwB,CACnC,CADWA,GACPE,EAAwB,CAC1BC,OAAS,EAAEC,IADe,EACfA,CAAQb,EAAOc,EAAAA,CAC5B,CAGIhB,CAAc,GAAdA,KAAwB,KAAnBiB,IAAI,CACXJ,EAAiBK,GAAG,CAAG,CACrBL,EACA,CACEM,EAAI,EACF,CACEC,CALRP,OACEA,GAImB,EACXE,MAAAA,CAAQf,EAAKqB,aAAAA,CAEjB,EACA,CACEC,eAAiB,EACfC,EAAI,EAAC,oBAAqB,uBAAwB,kBAAkB,CAExE,EAEJ,EACD,CACsB,YAAc,GAA5BvB,EAAKiB,EAALjB,EAAS,GAClBa,EAAiBK,GAAG,CAAG,CACrBL,EACA,CACES,MAHJT,OACEA,EAEmB,EACfU,EAAI,EAAC,aAAc,QAAS,kBAAkB,CAElD,EACD,EAUHC,CAPqB,MAAMrB,EAAcsB,GAOzCD,QAPyCC,WAAsB,CAAC,CAC9DC,KAAO,KACPhB,IAAM,GACNiB,KAAOd,CAAAA,EACPe,IAAM,UADCf,GAET,IAEagB,IAAI,CAACC,OAAO,CAAC,IACxBlB,EAASmB,IAAI,CADYC,CACzBpB,EACEI,CAAIgB,EAAYhB,EAAE,CAClBL,IAAM,eACNsB,SAAAA,CAAWD,EAAYC,SAAS,CAChCC,KAAAA,CAAOF,EAAYE,KAAK,CACxBC,MAAAA,CAAQH,EAAYG,MAAM,CAC1BC,QAAAA,CAAUJ,EAAYI,QAAQ,CAC9BhB,WAAAA,CAAaY,EAAYZ,WAAW,CACpCiB,IAAML,CAAAA,CACR,EACF,EACF,CAGA,GAAI,CAACrB,GAAiB,CAATA,KAAiB,GAAjBA,EAAiB,CAC5B,CADWA,GACP2B,EAAiB,CACnBxB,MADmB,CACV,EAAEC,MAAAA,CAAQb,EAAOc,EAAAA,CAC5B,EAGkB,QAAU,GAAxBhB,EAAKiB,EAALjB,EAAS,CACXsC,EAAUpB,GAAG,CAAG,CACdoB,EADFA,CAGInB,EAAI,EACF,CACEoB,CAJND,SAIkB,EACVvB,MAAAA,CAAQf,EAAKqB,aAAAA,CAEjB,EACA,CACEmB,SAAW,EACTzB,MAAAA,CAAQf,EAAKqB,aAAAA,CAEjB,EACA,CACEoB,QAAU,EACRlB,EAAI,EAAC,qBAAsB,wBAAyB,yBAAyB,CAEjF,EACD,EAEJ,CACQvB,IAAAA,QAA4B,KAAvBiB,IAAI,GAClBqB,EAAUpB,GAAG,CAAG,CACdoB,EADFA,CAGInB,EAAI,EACF,CACEoB,CAJND,SAIkB,EACVvB,MAAAA,CAAQf,EAAKqB,aAAAA,CAEjB,EACA,CACEoB,QAAU,EACRlB,EAAI,EAAC,iBAAkB,yBAA0B,oBAAoB,CAEzE,EACD,EAEJ,EAGW,KAOdmB,EAPoBvC,EAAcwC,WAAAA,IAAe,CAAC,CAChDjB,KAAO,KACPhB,IAAM,GACNiB,KAAOW,CAAAA,EACPV,IAAM,GADCU,QAET,IAEMT,IAAI,CAACC,OAAO,CAAC,IAACc,EACTb,CADSa,GACL,CAAC,CACZ5B,EAAAA,CAAI4B,EAAK5B,EAAE,CACXL,IAAM,QACNsB,SAAAA,CAAWW,EAAKC,OAAO,CACvBX,KAAAA,CAAOU,EAAKV,KAAK,CACjBC,MAAAA,CAAQS,EAAKT,MAAM,CACnBC,QAAAA,CAAUQ,EAAKR,QAAQ,CACvBG,UAAAA,CAAYK,EAAKL,UAAU,CAC3BC,SAAAA,CAAWI,EAAKJ,SAAS,CACzBH,IAAMO,CAAAA,CACR,EACF,EACF,CAGAhC,EAASgB,IAAI,CAAC,CAAdhB,CAAkBkC,CAAAA,CAAAA,GAAM,IAAIC,IAAKD,CAAAA,CAAAA,CAAEb,SAAS,EAAEe,OAAO,EAAK,KAAID,KAAKE,CAAEhB,CAAAA,SAAS,EAAEe,OAAO,IAGvF,IAAME,EAAcxC,CAAAA,GAAO,CAAPA,CAAYgB,EAC1ByB,EAAWD,CADexB,CACFA,EAGxB0B,EAAW,CAHa1B,GAAAA,CAItB2B,CAHkBzC,CAGlByC,CAH2BC,KAAK,CAAd1C,EAA2BuC,GAInDI,KAJmDJ,CAAAA,GAInDI,CAAW3C,EAAS4C,MAAM,OAC1B9B,KAAAA,EACAhB,EACA+C,EADA/C,QACA+C,CAAYC,IAAKC,CAAAA,IAAI,CAAC/C,EAAS4C,MAAT5C,CAAkBc,GACxCkC,EADwClC,CAAAA,QAC3ByB,CAAAA,EAAWvC,EAAS4C,IAApBL,EAA0B,CACvCU,WAAAA,CAAanD,EAAO,EACpBoD,QAAAA,CAAUX,EAAWvC,EAAS4C,IAAT5C,EAAAA,CAAkBF,EAAO,CAAI,MAClDqD,QAAUrD,CAAAA,EAAO,CAAIA,CAAXA,EAAkB,CAAI,CAAXA,IACvB,EAEA,MAAOsD,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBZ,CAAAA,EAC/B,CAAE,KAD6BA,CAAAA,EACf,CAEd,EAFc,KACda,OAAQC,CAAAA,KAAK,CAAC,kCAAoCA,CAAAA,GAC3CC,CAAAA,CAD2CD,CAAAA,EAC3CC,EAAAA,CAAoB,oCAC7B,CACF,CAAG,EC9KG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,CACnB,OAER,EAFiB,OAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GAAmB,EAAtB,KAA6B,CACrC,MAD4B,CACnB,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,6BAA6B,SACjD,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CADuB,CAAN,CAMjB,IAAC,EAAM,CAAH,CAAeC,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAM,CAAH,MAAeC,EAA4B,EAA7B,GAAkC,EAAR,EAEnC,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,KAAeC,EAA6B,EAA9B,IAAoC,CAAT,CAE7C,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,yCACA,uCACA,iBACA,iDACA,CAAK,CACL,6HACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patients/[id]/timeline/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessResponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\n\ninterface TimelineItem {\n  id: string;\n  type: 'interaction' | 'task';\n  timestamp: string;\n  title: string;\n  status: string;\n  priority: string;\n  staffMember?: any;\n  assignedTo?: any;\n  createdBy?: any;\n  data: any;\n}\n\nexport const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest, { params }: { params: { id: string } }) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const url = new URL(request.url);\n    \n    // Extract query parameters\n    const limit = parseInt(url.searchParams.get('limit') || '50');\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const type = url.searchParams.get('type'); // 'interaction', 'task', or null for both\n    \n    const timeline: TimelineItem[] = [];\n    \n    // Fetch interactions if requested\n    if (!type || type === 'interaction') {\n      let interactionWhere: any = {\n        patient: { equals: params.id },\n      };\n      \n      // Apply role-based filtering for interactions\n      if (user.role === 'doctor') {\n        interactionWhere.and = [\n          interactionWhere,\n          {\n            or: [\n              {\n                staffMember: {\n                  equals: user.payloadUserId,\n                },\n              },\n              {\n                interactionType: {\n                  in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],\n                },\n              },\n            ],\n          },\n        ];\n      } else if (user.role === 'front-desk') {\n        interactionWhere.and = [\n          interactionWhere,\n          {\n            interactionType: {\n              in: ['phone-call', 'email', 'billing-inquiry'],\n            },\n          },\n        ];\n      }\n      \n      const interactions = await payloadClient.getPatientInteractions({\n        limit: 100, // Get more items to merge properly\n        page: 1,\n        where: interactionWhere,\n        sort: '-timestamp',\n      }) as any;\n\n      interactions.docs.forEach((interaction: any) => {\n        timeline.push({\n          id: interaction.id,\n          type: 'interaction',\n          timestamp: interaction.timestamp,\n          title: interaction.title,\n          status: interaction.status,\n          priority: interaction.priority,\n          staffMember: interaction.staffMember,\n          data: interaction,\n        });\n      });\n    }\n    \n    // Fetch tasks if requested\n    if (!type || type === 'task') {\n      let taskWhere: any = {\n        patient: { equals: params.id },\n      };\n      \n      // Apply role-based filtering for tasks\n      if (user.role === 'doctor') {\n        taskWhere.and = [\n          taskWhere,\n          {\n            or: [\n              {\n                assignedTo: {\n                  equals: user.payloadUserId,\n                },\n              },\n              {\n                createdBy: {\n                  equals: user.payloadUserId,\n                },\n              },\n              {\n                taskType: {\n                  in: ['treatment-reminder', 'medical-record-update', 'consultation-follow-up'],\n                },\n              },\n            ],\n          },\n        ];\n      } else if (user.role === 'front-desk') {\n        taskWhere.and = [\n          taskWhere,\n          {\n            or: [\n              {\n                assignedTo: {\n                  equals: user.payloadUserId,\n                },\n              },\n              {\n                taskType: {\n                  in: ['follow-up-call', 'appointment-scheduling', 'billing-follow-up'],\n                },\n              },\n            ],\n          },\n        ];\n      }\n      \n      const tasks = await payloadClient.getPatientTasks({\n        limit: 100, // Get more items to merge properly\n        page: 1,\n        where: taskWhere,\n        sort: '-dueDate',\n      }) as any;\n\n      tasks.docs.forEach((task: any) => {\n        timeline.push({\n          id: task.id,\n          type: 'task',\n          timestamp: task.dueDate,\n          title: task.title,\n          status: task.status,\n          priority: task.priority,\n          assignedTo: task.assignedTo,\n          createdBy: task.createdBy,\n          data: task,\n        });\n      });\n    }\n    \n    // Sort timeline by timestamp descending (newest first)\n    timeline.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n    \n    // Apply pagination to the merged timeline\n    const startIndex = (page - 1) * limit;\n    const endIndex = startIndex + limit;\n    const paginatedTimeline = timeline.slice(startIndex, endIndex);\n    \n    const response = {\n      docs: paginatedTimeline,\n      totalDocs: timeline.length,\n      limit,\n      page,\n      totalPages: Math.ceil(timeline.length / limit),\n      hasNextPage: endIndex < timeline.length,\n      hasPrevPage: page > 1,\n      nextPage: endIndex < timeline.length ? page + 1 : null,\n      prevPage: page > 1 ? page - 1 : null,\n    };\n    \n    return createSuccessResponse(response);\n  } catch (error) {\n    console.error('Error fetching patient timeline:', error);\n    return createErrorResponse('Failed to fetch patient timeline');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patients/[id]/timeline',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patients\\\\[id]\\\\timeline\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patients/[id]/timeline/route\",\n        pathname: \"/api/patients/[id]/timeline\",\n        filename: \"route\",\n        bundlePath: \"app/api/patients/[id]/timeline/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patients\\\\[id]\\\\timeline\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "params", "payloadClient", "createPayloadClient", "url", "URL", "parseInt", "searchParams", "get", "page", "type", "timeline", "interactionWhere", "patient", "equals", "id", "role", "and", "or", "staffMember", "payloadUserId", "interactionType", "in", "interactions", "getPatientInteractions", "limit", "where", "sort", "docs", "for<PERSON>ach", "push", "interaction", "timestamp", "title", "status", "priority", "data", "taskWhere", "assignedTo", "created<PERSON>y", "taskType", "tasks", "getPatientTasks", "task", "dueDate", "b", "Date", "getTime", "a", "startIndex", "endIndex", "response", "paginatedTimeline", "slice", "totalDocs", "length", "totalPages", "Math", "ceil", "hasNextPage", "hasPrevPage", "nextPage", "prevPage", "createSuccessResponse", "console", "error", "createErrorResponse", "serverComponentModule.GET", "serverComponentModule.POST", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.HEAD", "serverComponentModule.OPTIONS"], "sourceRoot": ""}