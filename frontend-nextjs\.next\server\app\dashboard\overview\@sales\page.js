try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="770ab951-5ffb-423b-96f7-420292c57b5c",e._sentryDebugIdIdentifier="sentry-dbid-770ab951-5ffb-423b-96f7-420292c57b5c")}catch(e){}"use strict";(()=>{var e={};e.id=2718,e.ids=[2718],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},43687:(e,r,s)=>{s.r(r),s.d(r,{GlobalError:()=>n.default,__next_app__:()=>l,pages:()=>p,routeModule:()=>x,tree:()=>d});var o=s(29703),t=s(85544),n=s(62458),a=s(77821),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);s.d(r,i);let d={children:["",{children:["dashboard",{children:["overview",{area_stats:["__PAGE__",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,55334)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\page.tsx"]}]},{error:[()=>Promise.resolve().then(s.bind(s,60901)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,37235)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\loading.tsx"]}],bar_stats:["__PAGE__",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71626)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\page.tsx"]}]},{error:[()=>Promise.resolve().then(s.bind(s,97057)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,62160)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\loading.tsx"]}],pie_stats:["__PAGE__",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,72921)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\page.tsx"]}]},{error:[()=>Promise.resolve().then(s.bind(s,59792)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,44589)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\loading.tsx"]}],sales:["__PAGE__",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36226)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\page.tsx"]}]},{error:[()=>Promise.resolve().then(s.bind(s,48662)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,63971)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\loading.tsx"]}],children:["__DEFAULT__",{},{defaultPage:[()=>Promise.resolve().then(s.t.bind(s,11634,23)),"next/dist/client/components/parallel-route-default.js"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,93888)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,35691)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\error.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@area_stats\\page.tsx","C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@bar_stats\\page.tsx","C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@pie_stats\\page.tsx","C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\overview\\@sales\\page.tsx"],l={require:s,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/dashboard/overview/@sales/page",pathname:"/dashboard/overview",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},44708:e=>{e.exports=require("node:https")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74075:e=>{e.exports=require("zlib")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),o=r.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,3408,5215,8721,8774,7494,8968],()=>s(43687));module.exports=o})();
//# sourceMappingURL=page.js.map