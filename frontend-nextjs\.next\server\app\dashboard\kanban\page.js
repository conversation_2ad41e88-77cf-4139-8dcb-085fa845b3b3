try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="3d1e0619-cad1-4d15-b4fe-a4b5bb962e46",e._sentryDebugIdIdentifier="sentry-dbid-3d1e0619-cad1-4d15-b4fe-a4b5bb962e46")}catch(e){}(()=>{var e={};e.id=3793,e.ids=[3793],e.modules={1793:(e,t,r)=>{Promise.resolve().then(r.bind(r,89371)),Promise.resolve().then(r.bind(r,3317)),Promise.resolve().then(r.bind(r,71698))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3317:(e,t,r)=>{"use strict";r.d(t,{KanbanBoard:()=>n});let n=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call KanbanBoard() from the server but KanbanBoard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\kanban-board.tsx","KanbanBoard")},8086:e=>{"use strict";e.exports=require("module")},10531:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(24443);r(60222);var a=r(16586),i=r(29693),l=r(72595);let s=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:r=!1,...i}){let o=r?a.DX:"span";return(0,n.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(s({variant:t}),e),...i,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14945:(e,t,r)=>{Promise.resolve().then(r.bind(r,67529)),Promise.resolve().then(r.bind(r,91778)),Promise.resolve().then(r.bind(r,54152))},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},21965:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>y,generateImageMetadata:()=>m,generateMetadata:()=>g,generateViewport:()=>v,metadata:()=>f});var a=r(63033),i=r(78869),l=r(83829),s=r(37862),o=r(3317),d=r(71698);function u(){return(0,i.jsx)(l.A,{"data-sentry-element":"PageContainer","data-sentry-component":"KanbanViewPage","data-sentry-source-file":"kanban-view-page.tsx",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsx)(s.D,{title:"Kanban",description:"Manage tasks by dnd","data-sentry-element":"Heading","data-sentry-source-file":"kanban-view-page.tsx"}),(0,i.jsx)(d.default,{"data-sentry-element":"NewTaskDialog","data-sentry-source-file":"kanban-view-page.tsx"})]}),(0,i.jsx)(o.KanbanBoard,{"data-sentry-element":"KanbanBoard","data-sentry-source-file":"kanban-view-page.tsx"})]})})}var c=r(19761);let f={title:"Dashboard : Kanban view"},h={...a},p="workUnitAsyncStorage"in h?h.workUnitAsyncStorage:"requestAsyncStorage"in h?h.requestAsyncStorage:void 0;n=new Proxy(function(){return(0,i.jsx)(u,{"data-sentry-element":"KanbanViewPage","data-sentry-component":"page","data-sentry-source-file":"page.tsx"})},{apply:(e,t,r)=>{let n,a,i;try{let e=p?.getStore();n=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,i=e?.headers}catch(e){}return c.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/kanban",componentType:"Page",sentryTraceHeader:n,baggageHeader:a,headers:i}).apply(t,r)}});let g=void 0,m=void 0,v=void 0,y=n},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},32218:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>u,X9:()=>d,ZB:()=>s,Zp:()=>i,aR:()=>l,wL:()=>c});var n=r(24443);r(60222);var a=r(72595);function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t,"data-sentry-component":"Card","data-sentry-source-file":"card.tsx"})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t,"data-sentry-component":"CardHeader","data-sentry-source-file":"card.tsx"})}function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t,"data-sentry-component":"CardTitle","data-sentry-source-file":"card.tsx"})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-component":"CardDescription","data-sentry-source-file":"card.tsx"})}function d({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-action",className:(0,a.cn)("col-start-2 row-span-2 row-start-1 self-start justify-self-end",e),...t,"data-sentry-component":"CardAction","data-sentry-source-file":"card.tsx"})}function u({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t,"data-sentry-component":"CardContent","data-sentry-source-file":"card.tsx"})}function c({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t,"data-sentry-component":"CardFooter","data-sentry-source-file":"card.tsx"})}},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},37862:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var n=r(78869);let a=({title:e,description:t})=>(0,n.jsxs)("div",{"data-sentry-component":"Heading","data-sentry-source-file":"heading.tsx",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:e}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:t})]})},38522:e=>{"use strict";e.exports=require("node:zlib")},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},45442:(e,t,r)=>{"use strict";r.d(t,{O:()=>v});var n=r(60222);let a=e=>{let t,r=new Set,n=(e,n)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=n?n:"object"!=typeof a||null===a)?a:Object.assign({},t,a),r.forEach(r=>r(t,e))}},a=()=>t,i={setState:n,getState:a,getInitialState:()=>l,subscribe:e=>(r.add(e),()=>r.delete(e))},l=t=e(n,a,i);return i},i=e=>e?a(e):a,l=e=>e,s=e=>{let t=i(e),r=e=>(function(e,t=l){let r=n.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r};var o=r(55511);let d={randomUUID:o.randomUUID},u=new Uint8Array(256),c=u.length,f=[];for(let e=0;e<256;++e)f.push((e+256).toString(16).slice(1));let h=function(e,t,r){if(d.randomUUID&&!t&&!e)return d.randomUUID();let n=(e=e||{}).random??e.rng?.()??(c>u.length-16&&((0,o.randomFillSync)(u),c=0),u.slice(c,c+=16));if(n.length<16)throw Error("Random bytes length must be >= 16");if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return(f[e[t+0]]+f[e[t+1]]+f[e[t+2]]+f[e[t+3]]+"-"+f[e[t+4]]+f[e[t+5]]+"-"+f[e[t+6]]+f[e[t+7]]+"-"+f[e[t+8]]+f[e[t+9]]+"-"+f[e[t+10]]+f[e[t+11]]+f[e[t+12]]+f[e[t+13]]+f[e[t+14]]+f[e[t+15]]).toLowerCase()}(n)},p=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>p(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>p(t)(e)}}},g=[{id:"TODO",title:"Todo"}],m=[{id:"task1",status:"TODO",title:"Project initiation and planning"},{id:"task2",status:"TODO",title:"Gather requirements from stakeholders"}],v=(e=>e?s(e):s)()(((e,t)=>(r,n,a)=>{let i,l={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),a=null!=(t=r.getItem(e))?t:null;return a instanceof Promise?a.then(n):n(a)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1,o=new Set,d=new Set,u=l.storage;if(!u)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),r(...e)},n,a);let c=()=>{let e=l.partialize({...n()});return u.setItem(l.name,{state:e,version:l.version})},f=a.setState;a.setState=(e,t)=>{f(e,t),c()};let h=e((...e)=>{r(...e),c()},n,a);a.getInitialState=()=>h;let g=()=>{var e,t;if(!u)return;s=!1,o.forEach(e=>{var t;return e(null!=(t=n())?t:h)});let a=(null==(t=l.onRehydrateStorage)?void 0:t.call(l,null!=(e=n())?e:h))||void 0;return p(u.getItem.bind(u))(l.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===l.version)return[!1,e.state];else{if(l.migrate){let t=l.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[a,s]=e;if(r(i=l.merge(s,null!=(t=n())?t:h),!0),a)return c()}).then(()=>{null==a||a(i,void 0),i=n(),s=!0,d.forEach(e=>e(i))}).catch(e=>{null==a||a(void 0,e)})};return a.persist={setOptions:e=>{l={...l,...e},e.storage&&(u=e.storage)},clearStorage:()=>{null==u||u.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>g(),hasHydrated:()=>s,onHydrate:e=>(o.add(e),()=>{o.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},l.skipHydration||g(),i||h})(e=>({tasks:m,columns:g,draggedTask:null,addTask:(t,r)=>e(e=>({tasks:[...e.tasks,{id:h(),title:t,description:r,status:"TODO"}]})),updateCol:(t,r)=>e(e=>({columns:e.columns.map(e=>e.id===t?{...e,title:r}:e)})),addCol:t=>e(e=>({columns:[...e.columns,{title:t,id:e.columns.length?t.toUpperCase():"TODO"}]})),dragTask:t=>e({draggedTask:t}),removeTask:t=>e(e=>({tasks:e.tasks.filter(e=>e.id!==t)})),removeCol:t=>e(e=>({columns:e.columns.filter(e=>e.id!==t)})),setTasks:t=>e({tasks:t}),setCols:t=>e({columns:t})}),{name:"task-store",skipHydration:!0}))},48161:e=>{"use strict";e.exports=require("node:os")},48448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>u,pages:()=>d,routeModule:()=>c,tree:()=>o});var n=r(29703),a=r(85544),i=r(62458),l=r(77821),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);r.d(t,s);let o={children:["",{children:["dashboard",{children:["kanban",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21965)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\kanban\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\kanban\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/kanban/page",pathname:"/dashboard/kanban",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},54152:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var n=r(24443),a=r(33284),i=r(61780),l=r(19342),s=r(81646),o=r(45442);function d(){let e=(0,o.O)(e=>e.addTask);return(0,n.jsxs)(i.lG,{"data-sentry-element":"Dialog","data-sentry-component":"NewTaskDialog","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsx)(i.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(a.$,{variant:"secondary",size:"sm","data-sentry-element":"Button","data-sentry-source-file":"new-task-dialog.tsx",children:"＋ Add New Todo"})}),(0,n.jsxs)(i.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsxs)(i.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"new-task-dialog.tsx",children:[(0,n.jsx)(i.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"new-task-dialog.tsx",children:"Add New Todo"}),(0,n.jsx)(i.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"new-task-dialog.tsx",children:"What do you want to get done today?"})]}),(0,n.jsxs)("form",{id:"todo-form",className:"grid gap-4 py-4",onSubmit:t=>{t.preventDefault();let{title:r,description:n}=Object.fromEntries(new FormData(t.currentTarget));"string"==typeof r&&"string"==typeof n&&e(r,n)},children:[(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(l.p,{id:"title",name:"title",placeholder:"Todo title...",className:"col-span-4","data-sentry-element":"Input","data-sentry-source-file":"new-task-dialog.tsx"})}),(0,n.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,n.jsx)(s.T,{id:"description",name:"description",placeholder:"Description...",className:"col-span-4","data-sentry-element":"Textarea","data-sentry-source-file":"new-task-dialog.tsx"})})]}),(0,n.jsx)(i.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(i.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-task-dialog.tsx",children:(0,n.jsx)(a.$,{type:"submit",size:"sm",form:"todo-form","data-sentry-element":"Button","data-sentry-source-file":"new-task-dialog.tsx",children:"Add Todo"})})})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71698:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\features\\\\kanban\\\\components\\\\new-task-dialog.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\kanban\\components\\new-task-dialog.tsx","default")},73024:e=>{"use strict";e.exports=require("node:fs")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},81646:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var n=r(24443);r(60222);var a=r(72595);function i({className:e,...t}){return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t,"data-sentry-component":"Textarea","data-sentry-source-file":"textarea.tsx"})}},83829:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(78869);r(22576);var a=r(89371);function i({children:e,scrollable:t=!0}){return(0,n.jsx)(n.Fragment,{children:t?(0,n.jsx)(a.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,n.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,n.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89371:(e,t,r)=>{"use strict";r.d(t,{ScrollArea:()=>a});var n=r(91611);let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollArea");(0,n.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollBar")},91778:(e,t,r)=>{"use strict";let n;r.d(t,{KanbanBoard:()=>tU});var a,i,l,s,o,d,u,c,f,h,p=r(24443),g=r(60222),m=r.n(g),v=r(89859),y=r(45442);function x(e){if(!e)return!1;let t=e.data.current;return t?.type==="Column"||t?.type==="Task"}let b="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function w(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function D(e){return"nodeType"in e}function C(e){var t,r;return e?w(e)?e:D(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function k(e){let{Document:t}=C(e);return e instanceof t}function j(e){return!w(e)&&e instanceof C(e).HTMLElement}function S(e){return e instanceof C(e).SVGElement}function E(e){return e?w(e)?e.document:D(e)?k(e)?e:j(e)||S(e)?e.ownerDocument:document:document:document}let T=b?g.useLayoutEffect:g.useEffect;function M(e){let t=(0,g.useRef)(e);return T(()=>{t.current=e}),(0,g.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function O(e,t){void 0===t&&(t=[e]);let r=(0,g.useRef)(e);return T(()=>{r.current!==e&&(r.current=e)},t),r}function N(e,t){let r=(0,g.useRef)();return(0,g.useMemo)(()=>{let t=e(r.current);return r.current=t,t},[...t])}function R(e){let t=M(e),r=(0,g.useRef)(null),n=(0,g.useCallback)(e=>{e!==r.current&&(null==t||t(e,r.current)),r.current=e},[]);return[r,n]}function I(e){let t=(0,g.useRef)();return(0,g.useEffect)(()=>{t.current=e},[e]),t.current}let A={};function P(e,t){return(0,g.useMemo)(()=>{if(t)return t;let r=null==A[e]?0:A[e]+1;return A[e]=r,e+"-"+r},[e,t])}function B(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>{for(let[n,a]of Object.entries(r)){let r=t[n];null!=r&&(t[n]=r+e*a)}return t},{...t})}}let L=B(1),q=B(-1);function $(e){if(!e)return!1;let{KeyboardEvent:t}=C(e.target);return t&&e instanceof t}function U(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=C(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let z=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:r}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(r?Math.round(r):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:r}=e;return"scaleX("+t+") scaleY("+r+")"}},Transform:{toString(e){if(e)return[z.Translate.toString(e),z.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:r,easing:n}=e;return t+" "+r+"ms "+n}}}),F="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",_={display:"none"};function K(e){let{id:t,value:r}=e;return m().createElement("div",{id:t,style:_},r)}function H(e){let{id:t,announcement:r,ariaLiveType:n="assertive"}=e;return m().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},r)}let X=(0,g.createContext)(null),W={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},Y={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was moved over droppable area "+r.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was dropped over droppable area "+r.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function V(e){let{announcements:t=Y,container:r,hiddenTextDescribedById:n,screenReaderInstructions:a=W}=e,{announce:i,announcement:l}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),s=P("DndLiveRegion"),[o,d]=(0,g.useState)(!1);(0,g.useEffect)(()=>{d(!0)},[]);var u=(0,g.useMemo)(()=>({onDragStart(e){let{active:r}=e;i(t.onDragStart({active:r}))},onDragMove(e){let{active:r,over:n}=e;t.onDragMove&&i(t.onDragMove({active:r,over:n}))},onDragOver(e){let{active:r,over:n}=e;i(t.onDragOver({active:r,over:n}))},onDragEnd(e){let{active:r,over:n}=e;i(t.onDragEnd({active:r,over:n}))},onDragCancel(e){let{active:r,over:n}=e;i(t.onDragCancel({active:r,over:n}))}}),[i,t]);let c=(0,g.useContext)(X);if((0,g.useEffect)(()=>{if(!c)throw Error("useDndMonitor must be used within a children of <DndContext>");return c(u)},[u,c]),!o)return null;let f=m().createElement(m().Fragment,null,m().createElement(K,{id:n,value:a.draggable}),m().createElement(H,{id:s,announcement:l}));return r?(0,v.createPortal)(f,r):f}function G(){}function J(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let Z=Object.freeze({x:0,y:0});function Q(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return n-r}let ee=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=[];for(let e of n){let{id:n}=e,i=r.get(n);if(i){let r=function(e,t){let r=Math.max(t.top,e.top),n=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(n<a&&r<i){let l=t.width*t.height,s=e.width*e.height,o=(a-n)*(i-r);return Number((o/(l+s-o)).toFixed(4))}return 0}(i,t);r>0&&a.push({id:n,data:{droppableContainer:e,value:r}})}}return a.sort(Q)};function et(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:Z}let er=function(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>({...t,top:t.top+e*r.y,bottom:t.bottom+e*r.y,left:t.left+e*r.x,right:t.right+e*r.x}),{...t})}}(1);function en(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let ea={ignoreTransform:!1};function ei(e,t){void 0===t&&(t=ea);let r=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:n}=C(e).getComputedStyle(e);t&&(r=function(e,t,r){let n=en(t);if(!n)return e;let{scaleX:a,scaleY:i,x:l,y:s}=n,o=e.left-l-(1-a)*parseFloat(r),d=e.top-s-(1-i)*parseFloat(r.slice(r.indexOf(" ")+1)),u=a?e.width/a:e.width,c=i?e.height/i:e.height;return{width:u,height:c,top:d,right:o+u,bottom:d+c,left:o}}(r,t,n))}let{top:n,left:a,width:i,height:l,bottom:s,right:o}=r;return{top:n,left:a,width:i,height:l,bottom:s,right:o}}function el(e){return ei(e,{ignoreTransform:!0})}function es(e,t){let r=[];return e?function n(a){var i;if(null!=t&&r.length>=t||!a)return r;if(k(a)&&null!=a.scrollingElement&&!r.includes(a.scrollingElement))return r.push(a.scrollingElement),r;if(!j(a)||S(a)||r.includes(a))return r;let l=C(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=C(e).getComputedStyle(e));let r=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let n=t[e];return"string"==typeof n&&r.test(n)})}(a,l)&&r.push(a),void 0===(i=l)&&(i=C(a).getComputedStyle(a)),"fixed"===i.position)?r:n(a.parentNode)}(e):r}function eo(e){let[t]=es(e,1);return null!=t?t:null}function ed(e){return b&&e?w(e)?e:D(e)?k(e)||e===E(e).scrollingElement?window:j(e)?e:null:null:null}function eu(e){return w(e)?e.scrollX:e.scrollLeft}function ec(e){return w(e)?e.scrollY:e.scrollTop}function ef(e){return{x:eu(e),y:ec(e)}}function eh(e){return!!b&&!!e&&e===document.scrollingElement}function ep(e){let t={x:0,y:0},r=eh(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-r.width,y:e.scrollHeight-r.height},a=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:a,isLeft:i,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let eg={x:.2,y:.2};function em(e){return e.reduce((e,t)=>L(e,ef(t)),Z)}function ev(e,t){if(void 0===t&&(t=ei),!e)return;let{top:r,left:n,bottom:a,right:i}=t(e);eo(e)&&(a<=0||i<=0||r>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let ey=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+eu(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+ec(t),0)}]];class ex{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=es(t),n=em(r);for(let[t,a,i]of(this.rect={...e},this.width=e.width,this.height=e.height,ey))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=i(r),l=n[t]-a;return this.rect[e]+l},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class eb{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,r){var n;null==(n=this.target)||n.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function ew(e,t){let r=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+n**2)>t:"x"in t&&"y"in t?r>t.x&&n>t.y:"x"in t?r>t.x:"y"in t&&n>t.y}function eD(e){e.preventDefault()}function eC(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(l||(l={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(s||(s={}));let ek={start:[s.Space,s.Enter],cancel:[s.Esc],end:[s.Space,s.Enter,s.Tab]},ej=(e,t)=>{let{currentCoordinates:r}=t;switch(e.code){case s.Right:return{...r,x:r.x+25};case s.Left:return{...r,x:r.x-25};case s.Down:return{...r,y:r.y+25};case s.Up:return{...r,y:r.y-25}}};class eS{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new eb(E(t)),this.windowListeners=new eb(C(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(l.Resize,this.handleCancel),this.windowListeners.add(l.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(l.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,r=e.node.current;r&&ev(r),t(Z)}handleKeyDown(e){if($(e)){let{active:t,context:r,options:n}=this.props,{keyboardCodes:a=ek,coordinateGetter:i=ej,scrollBehavior:l="smooth"}=n,{code:o}=e;if(a.end.includes(o))return void this.handleEnd(e);if(a.cancel.includes(o))return void this.handleCancel(e);let{collisionRect:d}=r.current,u=d?{x:d.left,y:d.top}:Z;this.referenceCoordinates||(this.referenceCoordinates=u);let c=i(e,{active:t,context:r.current,currentCoordinates:u});if(c){let t=q(c,u),n={x:0,y:0},{scrollableAncestors:a}=r.current;for(let r of a){let a=e.code,{isTop:i,isRight:o,isLeft:d,isBottom:u,maxScroll:f,minScroll:h}=ep(r),p=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:r,right:n,bottom:a}=e.getBoundingClientRect();return{top:t,left:r,right:n,bottom:a,width:e.clientWidth,height:e.clientHeight}}(r),g={x:Math.min(a===s.Right?p.right-p.width/2:p.right,Math.max(a===s.Right?p.left:p.left+p.width/2,c.x)),y:Math.min(a===s.Down?p.bottom-p.height/2:p.bottom,Math.max(a===s.Down?p.top:p.top+p.height/2,c.y))},m=a===s.Right&&!o||a===s.Left&&!d,v=a===s.Down&&!u||a===s.Up&&!i;if(m&&g.x!==c.x){let e=r.scrollLeft+t.x,i=a===s.Right&&e<=f.x||a===s.Left&&e>=h.x;if(i&&!t.y)return void r.scrollTo({left:e,behavior:l});i?n.x=r.scrollLeft-e:n.x=a===s.Right?r.scrollLeft-f.x:r.scrollLeft-h.x,n.x&&r.scrollBy({left:-n.x,behavior:l});break}if(v&&g.y!==c.y){let e=r.scrollTop+t.y,i=a===s.Down&&e<=f.y||a===s.Up&&e>=h.y;if(i&&!t.x)return void r.scrollTo({top:e,behavior:l});i?n.y=r.scrollTop-e:n.y=a===s.Down?r.scrollTop-f.y:r.scrollTop-h.y,n.y&&r.scrollBy({top:-n.y,behavior:l});break}}this.handleMove(e,L(q(c,this.referenceCoordinates),n))}}}handleMove(e,t){let{onMove:r}=this.props;e.preventDefault(),r(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eE(e){return!!(e&&"distance"in e)}function eT(e){return!!(e&&"delay"in e)}eS.activators=[{eventName:"onKeyDown",handler:(e,t,r)=>{let{keyboardCodes:n=ek,onActivation:a}=t,{active:i}=r,{code:l}=e.nativeEvent;if(n.start.includes(l)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class eM{constructor(e,t,r){var n;void 0===r&&(r=function(e){let{EventTarget:t}=C(e);return e instanceof t?e:E(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:i}=a;this.props=e,this.events=t,this.document=E(i),this.documentListeners=new eb(this.document),this.listeners=new eb(r),this.windowListeners=new eb(C(i)),this.initialCoordinates=null!=(n=U(a))?n:Z,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(l.Resize,this.handleCancel),this.windowListeners.add(l.DragStart,eD),this.windowListeners.add(l.VisibilityChange,this.handleCancel),this.windowListeners.add(l.ContextMenu,eD),this.documentListeners.add(l.Keydown,this.handleKeydown),t){if(null!=r&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eT(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eE(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:r,onPending:n}=this.props;n(r,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(l.Click,eC,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(l.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:r,initialCoordinates:n,props:a}=this,{onMove:i,options:{activationConstraint:l}}=a;if(!n)return;let s=null!=(t=U(e))?t:Z,o=q(n,s);if(!r&&l){if(eE(l)){if(null!=l.tolerance&&ew(o,l.tolerance))return this.handleCancel();if(ew(o,l.distance))return this.handleStart()}return eT(l)&&ew(o,l.tolerance)?this.handleCancel():void this.handlePending(l,o)}e.cancelable&&e.preventDefault(),i(s)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===s.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let eO={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eN extends eM{constructor(e){let{event:t}=e;super(e,eO,E(t.target))}}eN.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return!!r.isPrimary&&0===r.button&&(null==n||n({event:r}),!0)}}];let eR={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(o||(o={}));class eI extends eM{constructor(e){super(e,eR,E(e.event.target))}}eI.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return r.button!==o.RightClick&&(null==n||n({event:r}),!0)}}];let eA={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eP extends eM{constructor(e){super(e,eA)}static setup(){return window.addEventListener(eA.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(eA.move.name,e)};function e(){}}}eP.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t,{touches:a}=r;return!(a.length>1)&&(null==n||n({event:r}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(d||(d={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(u||(u={}));let eB={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(c||(c={})),(f||(f={})).Optimized="optimized";let eL=new Map;function eq(e,t){return N(r=>e?r||("function"==typeof t?t(e):e):null,[t,e])}function e$(e){let{callback:t,disabled:r}=e,n=M(t),a=(0,g.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(n)},[r]);return(0,g.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function eU(e){return new ex(ei(e),e)}function ez(e,t,r){void 0===t&&(t=eU);let[n,a]=(0,g.useState)(null);function i(){a(n=>{if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=n?n:r)?a:null}let i=t(e);return JSON.stringify(n)===JSON.stringify(i)?n:i})}let l=function(e){let{callback:t,disabled:r}=e,n=M(t),a=(0,g.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(n)},[n,r]);return(0,g.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let r of t){let{type:t,target:n}=r;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){i();break}}}}),s=e$({callback:i});return T(()=>{i(),e?(null==s||s.observe(e),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())},[e]),n}let eF=[];function e_(e,t){void 0===t&&(t=[]);let r=(0,g.useRef)(null);return(0,g.useEffect)(()=>{r.current=null},t),(0,g.useEffect)(()=>{let t=e!==Z;t&&!r.current&&(r.current=e),!t&&r.current&&(r.current=null)},[e]),r.current?q(e,r.current):Z}function eK(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,r=e.innerHeight;return{top:0,left:0,right:t,bottom:r,width:t,height:r}}(e):null,[e])}let eH=[];function eX(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return j(t)?t:e}let eW=[{sensor:eN,options:{}},{sensor:eS,options:{}}],eY={current:{}},eV={draggable:{measure:el},droppable:{measure:el,strategy:c.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:ei}};class eG extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,r;return null!=(t=null==(r=this.get(e))?void 0:r.node.current)?t:void 0}}let eJ={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eG,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:G},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eV,measureDroppableContainers:G,windowRect:null,measuringScheduled:!1},eZ={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:G,draggableNodes:new Map,over:null,measureDroppableContainers:G},eQ=(0,g.createContext)(eZ),e0=(0,g.createContext)(eJ);function e1(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eG}}}function e2(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:r}=t,{id:n}=r,a=new eG(e.droppable.containers);return a.set(n,r),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:r,key:n,disabled:a}=t,i=e.droppable.containers.get(r);if(!i||n!==i.key)return e;let l=new eG(e.droppable.containers);return l.set(r,{...i,disabled:a}),{...e,droppable:{...e.droppable,containers:l}}}case a.UnregisterDroppable:{let{id:r,key:n}=t,a=e.droppable.containers.get(r);if(!a||n!==a.key)return e;let i=new eG(e.droppable.containers);return i.delete(r),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function e4(e){let{disabled:t}=e,{active:r,activatorEvent:n,draggableNodes:a}=(0,g.useContext)(eQ),i=I(n),l=I(null==r?void 0:r.id);return(0,g.useEffect)(()=>{if(!t&&!n&&i&&null!=l){if(!$(i)||document.activeElement===i.target)return;let e=a.get(l);if(!e)return;let{activatorNode:t,node:r}=e;(t.current||r.current)&&requestAnimationFrame(()=>{for(let e of[t.current,r.current]){if(!e)continue;let t=e.matches(F)?e:e.querySelector(F);if(t){t.focus();break}}})}},[n,t,a,l,i]),null}function e5(e,t){let{transform:r,...n}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...n}),r):r}let e3=(0,g.createContext)({...Z,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(h||(h={}));let e6=(0,g.memo)(function(e){var t,r,n,l,s,o;let{id:f,accessibility:p,autoScroll:y=!0,children:x,sensors:w=eW,collisionDetection:D=ee,measuring:k,modifiers:S,...E}=e,[M,A]=(0,g.useReducer)(e2,void 0,e1),[B,q]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:r,event:n}=t;e.forEach(e=>{var t;return null==(t=e[r])?void 0:t.call(e,n)})},[e]),t]}(),[$,z]=(0,g.useState)(h.Uninitialized),F=$===h.Initialized,{draggable:{active:_,nodes:K,translate:H},droppable:{containers:W}}=M,Y=null!=_?K.get(_):null,G=(0,g.useRef)({initial:null,translated:null}),J=(0,g.useMemo)(()=>{var e;return null!=_?{id:_,data:null!=(e=null==Y?void 0:Y.data)?e:eY,rect:G}:null},[_,Y]),Q=(0,g.useRef)(null),[en,ea]=(0,g.useState)(null),[el,eu]=(0,g.useState)(null),ec=O(E,Object.values(E)),ev=P("DndDescribedBy",f),ey=(0,g.useMemo)(()=>W.getEnabled(),[W]),eb=(0,g.useMemo)(()=>({draggable:{...eV.draggable,...null==k?void 0:k.draggable},droppable:{...eV.droppable,...null==k?void 0:k.droppable},dragOverlay:{...eV.dragOverlay,...null==k?void 0:k.dragOverlay}}),[null==k?void 0:k.draggable,null==k?void 0:k.droppable,null==k?void 0:k.dragOverlay]),{droppableRects:ew,measureDroppableContainers:eD,measuringScheduled:eC}=function(e,t){let{dragging:r,dependencies:n,config:a}=t,[i,l]=(0,g.useState)(null),{frequency:s,measure:o,strategy:d}=a,u=(0,g.useRef)(e),f=function(){switch(d){case c.Always:return!1;case c.BeforeDragging:return r;default:return!r}}(),h=O(f),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),h.current||l(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[h]),m=(0,g.useRef)(null),v=N(t=>{if(f&&!r)return eL;if(!t||t===eL||u.current!==e||null!=i){let t=new Map;for(let r of e){if(!r)continue;if(i&&i.length>0&&!i.includes(r.id)&&r.rect.current){t.set(r.id,r.rect.current);continue}let e=r.node.current,n=e?new ex(o(e),e):null;r.rect.current=n,n&&t.set(r.id,n)}return t}return t},[e,i,r,f,o]);return(0,g.useEffect)(()=>{u.current=e},[e]),(0,g.useEffect)(()=>{f||p()},[r,f]),(0,g.useEffect)(()=>{i&&i.length>0&&l(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{f||"number"!=typeof s||null!==m.current||(m.current=setTimeout(()=>{p(),m.current=null},s))},[s,f,p,...n]),{droppableRects:v,measureDroppableContainers:p,measuringScheduled:null!=i}}(ey,{dragging:F,dependencies:[H.x,H.y],config:eb.droppable}),ek=function(e,t){let r=null!=t?e.get(t):void 0,n=r?r.node.current:null;return N(e=>{var r;return null==t?null:null!=(r=null!=n?n:e)?r:null},[n,t])}(K,_),ej=(0,g.useMemo)(()=>el?U(el):null,[el]),eS=function(){let e=(null==en?void 0:en.autoScrollEnabled)===!1,t="object"==typeof y?!1===y.enabled:!1===y,r=F&&!e&&!t;return"object"==typeof y?{...y,enabled:r}:{enabled:r}}(),eE=eq(ek,eb.draggable.measure);!function(e){let{activeNode:t,measure:r,initialRect:n,config:a=!0}=e,i=(0,g.useRef)(!1),{x:l,y:s}="boolean"==typeof a?{x:a,y:a}:a;T(()=>{if(!l&&!s||!t){i.current=!1;return}if(i.current||!n)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=et(r(e),n);if(l||(a.x=0),s||(a.y=0),i.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=eo(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,l,s,n,r])}({activeNode:null!=_?K.get(_):null,config:eS.layoutShiftCompensation,initialRect:eE,measure:eb.draggable.measure});let eT=ez(ek,eb.draggable.measure,eE),eM=ez(ek?ek.parentElement:null),eO=(0,g.useRef)({activatorEvent:null,active:null,activeNode:ek,collisionRect:null,collisions:null,droppableRects:ew,draggableNodes:K,draggingNode:null,draggingNodeRect:null,droppableContainers:W,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eN=W.getNodeFor(null==(t=eO.current.over)?void 0:t.id),eR=function(e){let{measure:t}=e,[r,n]=(0,g.useState)(null),a=e$({callback:(0,g.useCallback)(e=>{for(let{target:r}of e)if(j(r)){n(e=>{let n=t(r);return e?{...e,width:n.width,height:n.height}:n});break}},[t])}),[i,l]=R((0,g.useCallback)(e=>{let r=eX(e);null==a||a.disconnect(),r&&(null==a||a.observe(r)),n(r?t(r):null)},[t,a]));return(0,g.useMemo)(()=>({nodeRef:i,rect:r,setRef:l}),[r,i,l])}({measure:eb.dragOverlay.measure}),eI=null!=(r=eR.nodeRef.current)?r:ek,eA=F?null!=(n=eR.rect)?n:eT:null,eP=!!(eR.nodeRef.current&&eR.rect),eU=function(e){let t=eq(e);return et(e,t)}(eP?null:eT),eG=eK(eI?C(eI):null),eJ=function(e){let t=(0,g.useRef)(e),r=N(r=>e?r&&r!==eF&&e&&t.current&&e.parentNode===t.current.parentNode?r:es(e):eF,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),r}(F?null!=eN?eN:ek:null),eZ=function(e,t){void 0===t&&(t=ei);let[r]=e,n=eK(r?C(r):null),[a,i]=(0,g.useState)(eH);function l(){i(()=>e.length?e.map(e=>eh(e)?n:new ex(t(e),e)):eH)}let s=e$({callback:l});return T(()=>{null==s||s.disconnect(),l(),e.forEach(e=>null==s?void 0:s.observe(e))},[e]),a}(eJ),e6=e5(S,{transform:{x:H.x-eU.x,y:H.y-eU.y,scaleX:1,scaleY:1},activatorEvent:el,active:J,activeNodeRect:eT,containerNodeRect:eM,draggingNodeRect:eA,over:eO.current.over,overlayNodeRect:eR.rect,scrollableAncestors:eJ,scrollableAncestorRects:eZ,windowRect:eG}),e9=ej?L(ej,H):null,e7=function(e){let[t,r]=(0,g.useState)(null),n=(0,g.useRef)(e),a=(0,g.useCallback)(e=>{let t=ed(e.target);t&&r(e=>e?(e.set(t,ef(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=n.current;if(e!==t){i(t);let l=e.map(e=>{let t=ed(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,ef(t)]):null}).filter(e=>null!=e);r(l.length?new Map(l):null),n.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=ed(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>L(e,t),Z):em(e):Z,[e,t])}(eJ),e8=e_(e7),te=e_(e7,[eT]),tt=L(e6,e8),tr=eA?er(eA,e6):null,tn=J&&tr?D({active:J,collisionRect:tr,droppableRects:ew,droppableContainers:ey,pointerCoordinates:e9}):null,ta=function(e,t){if(!e||0===e.length)return null;let[r]=e;return r.id}(tn,"id"),[ti,tl]=(0,g.useState)(null),ts=(s=eP?e6:L(e6,te),o=null!=(l=null==ti?void 0:ti.rect)?l:null,{...s,scaleX:o&&eT?o.width/eT.width:1,scaleY:o&&eT?o.height/eT.height:1}),to=(0,g.useRef)(null),td=(0,g.useCallback)((e,t)=>{let{sensor:r,options:n}=t;if(null==Q.current)return;let i=K.get(Q.current);if(!i)return;let l=e.nativeEvent,s=new r({active:Q.current,activeNode:i,event:l,options:n,context:eO,onAbort(e){if(!K.get(e))return;let{onDragAbort:t}=ec.current,r={id:e};null==t||t(r),B({type:"onDragAbort",event:r})},onPending(e,t,r,n){if(!K.get(e))return;let{onDragPending:a}=ec.current,i={id:e,constraint:t,initialCoordinates:r,offset:n};null==a||a(i),B({type:"onDragPending",event:i})},onStart(e){let t=Q.current;if(null==t)return;let r=K.get(t);if(!r)return;let{onDragStart:n}=ec.current,i={activatorEvent:l,active:{id:t,data:r.data,rect:G}};(0,v.unstable_batchedUpdates)(()=>{null==n||n(i),z(h.Initializing),A({type:a.DragStart,initialCoordinates:e,active:t}),B({type:"onDragStart",event:i}),ea(to.current),eu(l)})},onMove(e){A({type:a.DragMove,coordinates:e})},onEnd:o(a.DragEnd),onCancel:o(a.DragCancel)});function o(e){return async function(){let{active:t,collisions:r,over:n,scrollAdjustedTranslate:i}=eO.current,s=null;if(t&&i){let{cancelDrop:o}=ec.current;s={activatorEvent:l,active:t,collisions:r,delta:i,over:n},e===a.DragEnd&&"function"==typeof o&&await Promise.resolve(o(s))&&(e=a.DragCancel)}Q.current=null,(0,v.unstable_batchedUpdates)(()=>{A({type:e}),z(h.Uninitialized),tl(null),ea(null),eu(null),to.current=null;let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(s){let e=ec.current[t];null==e||e(s),B({type:t,event:s})}})}}to.current=s},[K]),tu=(0,g.useCallback)((e,t)=>(r,n)=>{let a=r.nativeEvent,i=K.get(n);null!==Q.current||!i||a.dndKit||a.defaultPrevented||!0===e(r,t.options,{active:i})&&(a.dndKit={capturedBy:t.sensor},Q.current=n,td(r,t))},[K,td]),tc=(0,g.useMemo)(()=>w.reduce((e,t)=>{let{sensor:r}=t;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:tu(e.handler,t)}))]},[]),[w,tu]);(0,g.useEffect)(()=>{if(!b)return;let e=w.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},w.map(e=>{let{sensor:t}=e;return t})),T(()=>{eT&&$===h.Initializing&&z(h.Initialized)},[eT,$]),(0,g.useEffect)(()=>{let{onDragMove:e}=ec.current,{active:t,activatorEvent:r,collisions:n,over:a}=eO.current;if(!t||!r)return;let i={active:t,activatorEvent:r,collisions:n,delta:{x:tt.x,y:tt.y},over:a};(0,v.unstable_batchedUpdates)(()=>{null==e||e(i),B({type:"onDragMove",event:i})})},[tt.x,tt.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:r,droppableContainers:n,scrollAdjustedTranslate:a}=eO.current;if(!e||null==Q.current||!t||!a)return;let{onDragOver:i}=ec.current,l=n.get(ta),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,o={active:e,activatorEvent:t,collisions:r,delta:{x:a.x,y:a.y},over:s};(0,v.unstable_batchedUpdates)(()=>{tl(s),null==i||i(o),B({type:"onDragOver",event:o})})},[ta]),T(()=>{eO.current={activatorEvent:el,active:J,activeNode:ek,collisionRect:tr,collisions:tn,droppableRects:ew,draggableNodes:K,draggingNode:eI,draggingNodeRect:eA,droppableContainers:W,over:ti,scrollableAncestors:eJ,scrollAdjustedTranslate:tt},G.current={initial:eA,translated:tr}},[J,ek,tn,tr,K,eI,eA,ew,W,ti,eJ,tt]),function(e){let{acceleration:t,activator:r=d.Pointer,canScroll:n,draggingRect:a,enabled:l,interval:s=5,order:o=u.TreeOrder,pointerCoordinates:c,scrollableAncestors:f,scrollableAncestorRects:h,delta:p,threshold:m}=e,v=function(e){let{delta:t,disabled:r}=e,n=I(t);return N(e=>{if(r||!n||!e)return eB;let a={x:Math.sign(t.x-n.x),y:Math.sign(t.y-n.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===a.x,[i.Forward]:e.x[i.Forward]||1===a.x},y:{[i.Backward]:e.y[i.Backward]||-1===a.y,[i.Forward]:e.y[i.Forward]||1===a.y}}},[r,t,n])}({delta:p,disabled:!l}),[y,x]=function(){let e=(0,g.useRef)(null);return[(0,g.useCallback)((t,r)=>{e.current=setInterval(t,r)},[]),(0,g.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),b=(0,g.useRef)({x:0,y:0}),w=(0,g.useRef)({x:0,y:0}),D=(0,g.useMemo)(()=>{switch(r){case d.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case d.DraggableRect:return a}},[r,a,c]),C=(0,g.useRef)(null),k=(0,g.useCallback)(()=>{let e=C.current;if(!e)return;let t=b.current.x*w.current.x,r=b.current.y*w.current.y;e.scrollBy(t,r)},[]),j=(0,g.useMemo)(()=>o===u.TreeOrder?[...f].reverse():f,[o,f]);(0,g.useEffect)(()=>{if(!l||!f.length||!D)return void x();for(let e of j){if((null==n?void 0:n(e))===!1)continue;let r=h[f.indexOf(e)];if(!r)continue;let{direction:a,speed:l}=function(e,t,r,n,a){let{top:l,left:s,right:o,bottom:d}=r;void 0===n&&(n=10),void 0===a&&(a=eg);let{isTop:u,isBottom:c,isLeft:f,isRight:h}=ep(e),p={x:0,y:0},g={x:0,y:0},m={height:t.height*a.y,width:t.width*a.x};return!u&&l<=t.top+m.height?(p.y=i.Backward,g.y=n*Math.abs((t.top+m.height-l)/m.height)):!c&&d>=t.bottom-m.height&&(p.y=i.Forward,g.y=n*Math.abs((t.bottom-m.height-d)/m.height)),!h&&o>=t.right-m.width?(p.x=i.Forward,g.x=n*Math.abs((t.right-m.width-o)/m.width)):!f&&s<=t.left+m.width&&(p.x=i.Backward,g.x=n*Math.abs((t.left+m.width-s)/m.width)),{direction:p,speed:g}}(e,r,D,t,m);for(let e of["x","y"])v[e][a[e]]||(l[e]=0,a[e]=0);if(l.x>0||l.y>0){x(),C.current=e,y(k,s),b.current=l,w.current=a;return}}b.current={x:0,y:0},w.current={x:0,y:0},x()},[t,k,n,x,l,s,JSON.stringify(D),JSON.stringify(v),y,f,j,h,JSON.stringify(m)])}({...eS,delta:H,draggingRect:tr,pointerCoordinates:e9,scrollableAncestors:eJ,scrollableAncestorRects:eZ});let tf=(0,g.useMemo)(()=>({active:J,activeNode:ek,activeNodeRect:eT,activatorEvent:el,collisions:tn,containerNodeRect:eM,dragOverlay:eR,draggableNodes:K,droppableContainers:W,droppableRects:ew,over:ti,measureDroppableContainers:eD,scrollableAncestors:eJ,scrollableAncestorRects:eZ,measuringConfiguration:eb,measuringScheduled:eC,windowRect:eG}),[J,ek,eT,el,tn,eM,eR,K,W,ew,ti,eD,eJ,eZ,eb,eC,eG]),th=(0,g.useMemo)(()=>({activatorEvent:el,activators:tc,active:J,activeNodeRect:eT,ariaDescribedById:{draggable:ev},dispatch:A,draggableNodes:K,over:ti,measureDroppableContainers:eD}),[el,tc,J,eT,A,ev,K,ti,eD]);return m().createElement(X.Provider,{value:q},m().createElement(eQ.Provider,{value:th},m().createElement(e0.Provider,{value:tf},m().createElement(e3.Provider,{value:ts},x)),m().createElement(e4,{disabled:(null==p?void 0:p.restoreFocus)===!1})),m().createElement(V,{...p,hiddenTextDescribedById:ev}))}),e9=(0,g.createContext)(null),e7="button";function e8(){return(0,g.useContext)(e0)}let te={timeout:25};function tt(e){let{animation:t,children:r}=e,[n,a]=(0,g.useState)(null),[i,l]=(0,g.useState)(null),s=I(r);return r||n||!s||a(s),T(()=>{if(!i)return;let e=null==n?void 0:n.key,r=null==n?void 0:n.props.id;if(null==e||null==r)return void a(null);Promise.resolve(t(r,i)).then(()=>{a(null)})},[t,n,i]),m().createElement(m().Fragment,null,r,n?(0,g.cloneElement)(n,{ref:l}):null)}let tr={x:0,y:0,scaleX:1,scaleY:1};function tn(e){let{children:t}=e;return m().createElement(eQ.Provider,{value:eZ},m().createElement(e3.Provider,{value:tr},t))}let ta={position:"fixed",touchAction:"none"},ti=e=>$(e)?"transform 250ms ease":void 0,tl=(0,g.forwardRef)((e,t)=>{let{as:r,activatorEvent:n,adjustScale:a,children:i,className:l,rect:s,style:o,transform:d,transition:u=ti}=e;if(!s)return null;let c=a?d:{...d,scaleX:1,scaleY:1},f={...ta,width:s.width,height:s.height,top:s.top,left:s.left,transform:z.Transform.toString(c),transformOrigin:a&&n?function(e,t){let r=U(e);if(!r)return"0 0";let n={x:(r.x-t.left)/t.width*100,y:(r.y-t.top)/t.height*100};return n.x+"% "+n.y+"%"}(n,s):void 0,transition:"function"==typeof u?u(n):u,...o};return m().createElement(r,{className:l,style:f,ref:t},i)}),ts={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:r}}=e;return[{transform:z.Transform.toString(t)},{transform:z.Transform.toString(r)}]},sideEffects:(n={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:r}=e,a={},{styles:i,className:l}=n;if(null!=i&&i.active)for(let[e,r]of Object.entries(i.active))void 0!==r&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,r));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&r.node.style.setProperty(e,t);return null!=l&&l.active&&t.node.classList.add(l.active),null!=l&&l.dragOverlay&&r.node.classList.add(l.dragOverlay),function(){for(let[e,r]of Object.entries(a))t.node.style.setProperty(e,r);null!=l&&l.active&&t.node.classList.remove(l.active)}})},to=0,td=m().memo(e=>{let{adjustScale:t=!1,children:r,dropAnimation:n,style:a,transition:i,modifiers:l,wrapperElement:s="div",className:o,zIndex:d=999}=e,{activatorEvent:u,active:c,activeNodeRect:f,containerNodeRect:h,draggableNodes:p,droppableContainers:v,dragOverlay:y,over:x,measuringConfiguration:b,scrollableAncestors:w,scrollableAncestorRects:D,windowRect:k}=e8(),j=(0,g.useContext)(e3),S=function(e){return(0,g.useMemo)(()=>{if(null!=e)return++to},[e])}(null==c?void 0:c.id),E=e5(l,{activatorEvent:u,active:c,activeNodeRect:f,containerNodeRect:h,draggingNodeRect:y.rect,over:x,overlayNodeRect:y.rect,scrollableAncestors:w,scrollableAncestorRects:D,transform:j,windowRect:k}),T=eq(f),O=function(e){let{config:t,draggableNodes:r,droppableContainers:n,measuringConfiguration:a}=e;return M((e,i)=>{if(null===t)return;let l=r.get(e);if(!l)return;let s=l.node.current;if(!s)return;let o=eX(i);if(!o)return;let{transform:d}=C(i).getComputedStyle(i),u=en(d);if(!u)return;let c="function"==typeof t?t:function(e){let{duration:t,easing:r,sideEffects:n,keyframes:a}={...ts,...e};return e=>{let{active:i,dragOverlay:l,transform:s,...o}=e;if(!t)return;let d={x:l.rect.left-i.rect.left,y:l.rect.top-i.rect.top},u={scaleX:1!==s.scaleX?i.rect.width*s.scaleX/l.rect.width:1,scaleY:1!==s.scaleY?i.rect.height*s.scaleY/l.rect.height:1},c={x:s.x-d.x,y:s.y-d.y,...u},f=a({...o,active:i,dragOverlay:l,transform:{initial:s,final:c}}),[h]=f,p=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(p))return;let g=null==n?void 0:n({active:i,dragOverlay:l,...o}),m=l.node.animate(f,{duration:t,easing:r,fill:"forwards"});return new Promise(e=>{m.onfinish=()=>{null==g||g(),e()}})}}(t);return ev(s,a.draggable.measure),c({active:{id:e,data:l.data,node:s,rect:a.draggable.measure(s)},draggableNodes:r,dragOverlay:{node:i,rect:a.dragOverlay.measure(o)},droppableContainers:n,measuringConfiguration:a,transform:u})})}({config:n,draggableNodes:p,droppableContainers:v,measuringConfiguration:b}),N=T?y.setRef:void 0;return m().createElement(tn,null,m().createElement(tt,{animation:O},c&&S?m().createElement(tl,{key:S,id:c.id,ref:N,as:s,activatorEvent:u,adjustScale:t,className:o,transition:i,rect:T,style:{zIndex:d,...a},transform:E},r):null))});function tu(e,t,r){let n=e.slice();return n.splice(r<0?n.length+r:r,0,n.splice(t,1)[0]),n}function tc(e){return null!==e&&e>=0}let tf=e=>{let{rects:t,activeIndex:r,overIndex:n,index:a}=e,i=tu(t,n,r),l=t[a],s=i[a];return s&&l?{x:s.left-l.left,y:s.top-l.top,scaleX:s.width/l.width,scaleY:s.height/l.height}:null},th="Sortable",tp=m().createContext({activeIndex:-1,containerId:th,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:tf,disabled:{draggable:!1,droppable:!1}});function tg(e){let{children:t,id:r,items:n,strategy:a=tf,disabled:i=!1}=e,{active:l,dragOverlay:s,droppableRects:o,over:d,measureDroppableContainers:u}=e8(),c=P(th,r),f=null!==s.rect,h=(0,g.useMemo)(()=>n.map(e=>"object"==typeof e&&"id"in e?e.id:e),[n]),p=null!=l,v=l?h.indexOf(l.id):-1,y=d?h.indexOf(d.id):-1,x=(0,g.useRef)(h),b=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(h,x.current),w=-1!==y&&-1===v||b,D="boolean"==typeof i?{draggable:i,droppable:i}:i;T(()=>{b&&p&&u(h)},[b,h,p,u]),(0,g.useEffect)(()=>{x.current=h},[h]);let C=(0,g.useMemo)(()=>({activeIndex:v,containerId:c,disabled:D,disableTransforms:w,items:h,overIndex:y,useDragOverlay:f,sortedRects:h.reduce((e,t,r)=>{let n=o.get(t);return n&&(e[r]=n),e},Array(h.length)),strategy:a}),[v,c,D.draggable,D.droppable,w,h,y,o,f,a]);return m().createElement(tp.Provider,{value:C},t)}let tm=e=>{let{id:t,items:r,activeIndex:n,overIndex:a}=e;return tu(r,n,a).indexOf(t)},tv=e=>{let{containerId:t,isSorting:r,wasDragging:n,index:a,items:i,newIndex:l,previousItems:s,previousContainerId:o,transition:d}=e;return!!d&&!!n&&(s===i||a!==l)&&(!!r||l!==a&&t===o)},ty={duration:200,easing:"ease"},tx="transform",tb=z.Transition.toString({property:tx,duration:0,easing:"linear"}),tw={roleDescription:"sortable"};function tD(e){var t,r,n,i;let{animateLayoutChanges:l=tv,attributes:s,disabled:o,data:d,getNewIndex:u=tm,id:c,strategy:f,resizeObserverConfig:h,transition:p=ty}=e,{items:m,containerId:v,activeIndex:y,disabled:x,disableTransforms:b,sortedRects:w,overIndex:D,useDragOverlay:C,strategy:k}=(0,g.useContext)(tp),j=(t=o,r=x,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(n=null==t?void 0:t.draggable)?n:r.draggable,droppable:null!=(i=null==t?void 0:t.droppable)?i:r.droppable}),S=m.indexOf(c),E=(0,g.useMemo)(()=>({sortable:{containerId:v,index:S,items:m},...d}),[v,d,S,m]),M=(0,g.useMemo)(()=>m.slice(m.indexOf(c)),[m,c]),{rect:N,node:I,isOver:A,setNodeRef:B}=function(e){let{data:t,disabled:r=!1,id:n,resizeObserverConfig:i}=e,l=P("Droppable"),{active:s,dispatch:o,over:d,measureDroppableContainers:u}=(0,g.useContext)(eQ),c=(0,g.useRef)({disabled:r}),f=(0,g.useRef)(!1),h=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:m,updateMeasurementsFor:v,timeout:y}={...te,...i},x=O(null!=v?v:n),b=e$({callback:(0,g.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{u(Array.isArray(x.current)?x.current:[x.current]),p.current=null},y)},[y]),disabled:m||!s}),[w,D]=R((0,g.useCallback)((e,t)=>{b&&(t&&(b.unobserve(t),f.current=!1),e&&b.observe(e))},[b])),C=O(t);return(0,g.useEffect)(()=>{b&&w.current&&(b.disconnect(),f.current=!1,b.observe(w.current))},[w,b]),(0,g.useEffect)(()=>(o({type:a.RegisterDroppable,element:{id:n,key:l,disabled:r,node:w,rect:h,data:C}}),()=>o({type:a.UnregisterDroppable,key:l,id:n})),[n]),(0,g.useEffect)(()=>{r!==c.current.disabled&&(o({type:a.SetDroppableDisabled,id:n,key:l,disabled:r}),c.current.disabled=r)},[n,l,r,o]),{active:s,rect:h,isOver:(null==d?void 0:d.id)===n,node:w,over:d,setNodeRef:D}}({id:c,data:E,disabled:j.droppable,resizeObserverConfig:{updateMeasurementsFor:M,...h}}),{active:L,activatorEvent:q,activeNodeRect:U,attributes:F,setNodeRef:_,listeners:K,isDragging:H,over:X,setActivatorNodeRef:W,transform:Y}=function(e){let{id:t,data:r,disabled:n=!1,attributes:a}=e,i=P("Draggable"),{activators:l,activatorEvent:s,active:o,activeNodeRect:d,ariaDescribedById:u,draggableNodes:c,over:f}=(0,g.useContext)(eQ),{role:h=e7,roleDescription:p="draggable",tabIndex:m=0}=null!=a?a:{},v=(null==o?void 0:o.id)===t,y=(0,g.useContext)(v?e3:e9),[x,b]=R(),[w,D]=R(),C=(0,g.useMemo)(()=>l.reduce((e,r)=>{let{eventName:n,handler:a}=r;return e[n]=e=>{a(e,t)},e},{}),[l,t]),k=O(r);return T(()=>(c.set(t,{id:t,key:i,node:x,activatorNode:w,data:k}),()=>{let e=c.get(t);e&&e.key===i&&c.delete(t)}),[c,t]),{active:o,activatorEvent:s,activeNodeRect:d,attributes:(0,g.useMemo)(()=>({role:h,tabIndex:m,"aria-disabled":n,"aria-pressed":!!v&&h===e7||void 0,"aria-roledescription":p,"aria-describedby":u.draggable}),[n,h,m,v,p,u.draggable]),isDragging:v,listeners:n?void 0:C,node:x,over:f,setNodeRef:b,setActivatorNodeRef:D,transform:y}}({id:c,data:E,attributes:{...tw,...s},disabled:j.draggable}),V=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,g.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(B,_),G=!!L,J=G&&!b&&tc(y)&&tc(D),Z=!C&&H,Q=Z&&J?Y:null,ee=J?null!=Q?Q:(null!=f?f:k)({rects:w,activeNodeRect:U,activeIndex:y,overIndex:D,index:S}):null,et=tc(y)&&tc(D)?u({id:c,items:m,activeIndex:y,overIndex:D}):S,er=null==L?void 0:L.id,en=(0,g.useRef)({activeId:er,items:m,newIndex:et,containerId:v}),ea=m!==en.current.items,el=l({active:L,containerId:v,isDragging:H,isSorting:G,id:c,index:S,items:m,newIndex:en.current.newIndex,previousItems:en.current.items,previousContainerId:en.current.containerId,transition:p,wasDragging:null!=en.current.activeId}),es=function(e){let{disabled:t,index:r,node:n,rect:a}=e,[i,l]=(0,g.useState)(null),s=(0,g.useRef)(r);return T(()=>{if(!t&&r!==s.current&&n.current){let e=a.current;if(e){let t=ei(n.current,{ignoreTransform:!0}),r={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(r.x||r.y)&&l(r)}}r!==s.current&&(s.current=r)},[t,r,n,a]),(0,g.useEffect)(()=>{i&&l(null)},[i]),i}({disabled:!el,index:S,node:I,rect:N});return(0,g.useEffect)(()=>{G&&en.current.newIndex!==et&&(en.current.newIndex=et),v!==en.current.containerId&&(en.current.containerId=v),m!==en.current.items&&(en.current.items=m)},[G,et,v,m]),(0,g.useEffect)(()=>{if(er===en.current.activeId)return;if(er&&!en.current.activeId){en.current.activeId=er;return}let e=setTimeout(()=>{en.current.activeId=er},50);return()=>clearTimeout(e)},[er]),{active:L,activeIndex:y,attributes:F,data:E,rect:N,index:S,newIndex:et,items:m,isOver:A,isSorting:G,isDragging:H,listeners:K,node:I,overIndex:D,over:X,setNodeRef:V,setActivatorNodeRef:W,setDroppableNodeRef:B,setDraggableNodeRef:_,transform:null!=es?es:ee,transition:es||ea&&en.current.newIndex===S?tb:(!Z||$(q))&&p&&(G||el)?z.Transition.toString({...p,property:tx}):void 0}}s.Down,s.Right,s.Up,s.Left;var tC=r(29693),tk=(0,r(46244).A)("outline","grip-vertical","IconGripVertical",[["path",{d:"M9 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M9 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M9 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}],["path",{d:"M15 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-3"}],["path",{d:"M15 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-4"}],["path",{d:"M15 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-5"}]]),tj=r(33284),tS=r(32218),tE=r(39255),tT=r(75922),tM=r(11035),tO=r(19342),tN=r(85001);function tR({title:e,id:t}){let[r,n]=g.useState(e),a=(0,y.O)(e=>e.updateCol),i=(0,y.O)(e=>e.removeCol),[l,s]=g.useState(!0),[o,d]=g.useState(!1),u=g.useRef(null);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("form",{onSubmit:n=>{n.preventDefault(),s(!l),a(t,r),(0,tN.toast)(`${e} updated to ${r}`)},children:(0,p.jsx)(tO.p,{value:r,onChange:e=>n(e.target.value),className:"mt-0! mr-auto text-base disabled:cursor-pointer disabled:border-none disabled:opacity-100",disabled:l,ref:u,"data-sentry-element":"Input","data-sentry-source-file":"column-action.tsx"})}),(0,p.jsxs)(tM.rI,{modal:!1,"data-sentry-element":"DropdownMenu","data-sentry-source-file":"column-action.tsx",children:[(0,p.jsx)(tM.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"column-action.tsx",children:(0,p.jsxs)(tj.$,{variant:"secondary",className:"ml-1","data-sentry-element":"Button","data-sentry-source-file":"column-action.tsx",children:[(0,p.jsx)("span",{className:"sr-only",children:"Actions"}),(0,p.jsx)(tE.Oer,{className:"h-4 w-4","data-sentry-element":"DotsHorizontalIcon","data-sentry-source-file":"column-action.tsx"})]})}),(0,p.jsxs)(tM.SQ,{align:"end","data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"column-action.tsx",children:[(0,p.jsx)(tM._2,{onSelect:()=>{s(!l),setTimeout(()=>{u.current&&u.current?.focus()},500)},"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"column-action.tsx",children:"Rename"}),(0,p.jsx)(tM.mB,{"data-sentry-element":"DropdownMenuSeparator","data-sentry-source-file":"column-action.tsx"}),(0,p.jsx)(tM._2,{onSelect:()=>d(!0),className:"text-red-600","data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"column-action.tsx",children:"Delete Section"})]})]}),(0,p.jsx)(tT.Lt,{open:o,onOpenChange:d,"data-sentry-element":"AlertDialog","data-sentry-source-file":"column-action.tsx",children:(0,p.jsxs)(tT.EO,{"data-sentry-element":"AlertDialogContent","data-sentry-source-file":"column-action.tsx",children:[(0,p.jsxs)(tT.wd,{"data-sentry-element":"AlertDialogHeader","data-sentry-source-file":"column-action.tsx",children:[(0,p.jsx)(tT.r7,{"data-sentry-element":"AlertDialogTitle","data-sentry-source-file":"column-action.tsx",children:"Are you sure want to delete column?"}),(0,p.jsx)(tT.$v,{"data-sentry-element":"AlertDialogDescription","data-sentry-source-file":"column-action.tsx",children:"NOTE: All tasks related to this category will also be deleted."})]}),(0,p.jsxs)(tT.ck,{"data-sentry-element":"AlertDialogFooter","data-sentry-source-file":"column-action.tsx",children:[(0,p.jsx)(tT.Zr,{"data-sentry-element":"AlertDialogCancel","data-sentry-source-file":"column-action.tsx",children:"Cancel"}),(0,p.jsx)(tj.$,{variant:"destructive",onClick:()=>{setTimeout(()=>document.body.style.pointerEvents="",100),d(!1),i(t),(0,tN.toast)("This column has been deleted.")},"data-sentry-element":"Button","data-sentry-source-file":"column-action.tsx",children:"Delete"})]})]})})]})}var tI=r(10531);function tA({task:e,isOverlay:t}){let{setNodeRef:r,attributes:n,listeners:a,transform:i,transition:l,isDragging:s}=tD({id:e.id,data:{type:"Task",task:e},attributes:{roleDescription:"Task"}}),o={transition:l,transform:z.Translate.toString(i)},d=(0,tC.F)("mb-2",{variants:{dragging:{over:"ring-2 opacity-30",overlay:"ring-2 ring-primary"}}});return(0,p.jsxs)(tS.Zp,{ref:r,style:o,className:d({dragging:t?"overlay":s?"over":void 0}),"data-sentry-element":"Card","data-sentry-component":"TaskCard","data-sentry-source-file":"task-card.tsx",children:[(0,p.jsxs)(tS.aR,{className:"space-between border-secondary relative flex flex-row border-b-2 px-3 py-3","data-sentry-element":"CardHeader","data-sentry-source-file":"task-card.tsx",children:[(0,p.jsxs)(tj.$,{variant:"ghost",...n,...a,className:"text-secondary-foreground/50 -ml-2 h-auto cursor-grab p-1","data-sentry-element":"Button","data-sentry-source-file":"task-card.tsx",children:[(0,p.jsx)("span",{className:"sr-only",children:"Move task"}),(0,p.jsx)(tk,{"data-sentry-element":"IconGripVertical","data-sentry-source-file":"task-card.tsx"})]}),(0,p.jsx)(tI.E,{variant:"outline",className:"ml-auto font-semibold","data-sentry-element":"Badge","data-sentry-source-file":"task-card.tsx",children:"Task"})]}),(0,p.jsx)(tS.Wu,{className:"px-3 pt-3 pb-6 text-left whitespace-pre-wrap","data-sentry-element":"CardContent","data-sentry-source-file":"task-card.tsx",children:e.title})]})}var tP=r(67529);function tB({column:e,tasks:t,isOverlay:r}){let n=(0,g.useMemo)(()=>t.map(e=>e.id),[t]),{setNodeRef:a,attributes:i,listeners:l,transform:s,transition:o,isDragging:d}=tD({id:e.id,data:{type:"Column",column:e},attributes:{roleDescription:`Column: ${e.title}`}}),u={transition:o,transform:z.Translate.toString(s)},c=(0,tC.F)("h-[75vh] max-h-[75vh] w-[350px] max-w-full bg-secondary flex flex-col shrink-0 snap-center",{variants:{dragging:{default:"border-2 border-transparent",over:"ring-2 opacity-30",overlay:"ring-2 ring-primary"}}});return(0,p.jsxs)(tS.Zp,{ref:a,style:u,className:c({dragging:r?"overlay":d?"over":void 0}),"data-sentry-element":"Card","data-sentry-component":"BoardColumn","data-sentry-source-file":"board-column.tsx",children:[(0,p.jsxs)(tS.aR,{className:"space-between flex flex-row items-center border-b-2 p-4 text-left font-semibold","data-sentry-element":"CardHeader","data-sentry-source-file":"board-column.tsx",children:[(0,p.jsxs)(tj.$,{variant:"ghost",...i,...l,className:"text-primary/50 relative -ml-2 h-auto cursor-grab p-1","data-sentry-element":"Button","data-sentry-source-file":"board-column.tsx",children:[(0,p.jsx)("span",{className:"sr-only",children:`Move column: ${e.title}`}),(0,p.jsx)(tk,{"data-sentry-element":"IconGripVertical","data-sentry-source-file":"board-column.tsx"})]}),(0,p.jsx)(tR,{id:e.id,title:e.title,"data-sentry-element":"ColumnActions","data-sentry-source-file":"board-column.tsx"})]}),(0,p.jsx)(tS.Wu,{className:"flex grow flex-col gap-4 overflow-x-hidden p-2","data-sentry-element":"CardContent","data-sentry-source-file":"board-column.tsx",children:(0,p.jsx)(tP.ScrollArea,{className:"h-full","data-sentry-element":"ScrollArea","data-sentry-source-file":"board-column.tsx",children:(0,p.jsx)(tg,{items:n,"data-sentry-element":"SortableContext","data-sentry-source-file":"board-column.tsx",children:t.map(e=>(0,p.jsx)(tA,{task:e},e.id))})})})]})}function tL({children:e}){let t=e8(),r=(0,tC.F)("px-2  pb-4 md:px-0 flex lg:justify-start",{variants:{dragging:{default:"",active:"snap-none"}}});return(0,p.jsxs)(tP.ScrollArea,{className:"w-full rounded-md whitespace-nowrap","data-sentry-element":"ScrollArea","data-sentry-component":"BoardContainer","data-sentry-source-file":"board-column.tsx",children:[(0,p.jsx)("div",{className:r({dragging:t.active?"active":"default"}),children:(0,p.jsx)("div",{className:"flex flex-row items-start justify-center gap-4",children:e})}),(0,p.jsx)(tP.$,{orientation:"horizontal","data-sentry-element":"ScrollBar","data-sentry-source-file":"board-column.tsx"})]})}var tq=r(61780);function t$(){let e=(0,y.O)(e=>e.addCol);return(0,p.jsxs)(tq.lG,{"data-sentry-element":"Dialog","data-sentry-component":"NewSectionDialog","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,p.jsx)(tq.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-section-dialog.tsx",children:(0,p.jsx)(tj.$,{variant:"secondary",size:"lg",className:"w-full","data-sentry-element":"Button","data-sentry-source-file":"new-section-dialog.tsx",children:"＋ Add New Section"})}),(0,p.jsxs)(tq.Cf,{className:"sm:max-w-[425px]","data-sentry-element":"DialogContent","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,p.jsxs)(tq.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"new-section-dialog.tsx",children:[(0,p.jsx)(tq.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"new-section-dialog.tsx",children:"Add New Section"}),(0,p.jsx)(tq.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"new-section-dialog.tsx",children:"What section you want to add today?"})]}),(0,p.jsx)("form",{id:"todo-form",className:"grid gap-4 py-4",onSubmit:t=>{t.preventDefault();let{title:r}=Object.fromEntries(new FormData(t.currentTarget));"string"==typeof r&&e(r)},children:(0,p.jsx)("div",{className:"grid grid-cols-4 items-center gap-4",children:(0,p.jsx)(tO.p,{id:"title",name:"title",placeholder:"Section title...",className:"col-span-4","data-sentry-element":"Input","data-sentry-source-file":"new-section-dialog.tsx"})})}),(0,p.jsx)(tq.Es,{"data-sentry-element":"DialogFooter","data-sentry-source-file":"new-section-dialog.tsx",children:(0,p.jsx)(tq.zM,{asChild:!0,"data-sentry-element":"DialogTrigger","data-sentry-source-file":"new-section-dialog.tsx",children:(0,p.jsx)(tj.$,{type:"submit",size:"sm",form:"todo-form","data-sentry-element":"Button","data-sentry-source-file":"new-section-dialog.tsx",children:"Add Section"})})})]})]})}function tU(){let e=(0,y.O)(e=>e.columns),t=(0,y.O)(e=>e.setCols),r=(0,g.useRef)("TODO"),n=(0,g.useMemo)(()=>e.map(e=>e.id),[e]),a=(0,y.O)(e=>e.tasks),i=(0,y.O)(e=>e.setTasks),[l,s]=(0,g.useState)(null),[o,d]=(0,g.useState)(!1),[u,c]=(0,g.useState)(null),f=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(J(eI),J(eP));if(o)return(0,p.jsxs)(e6,{accessibility:{announcements:{onDragStart({active:t}){if(x(t)){if(t.data.current?.type==="Column"){let r=n.findIndex(e=>e===t.id),a=e[r];return`Picked up Column ${a?.title} at position: ${r+1} of ${n.length}`}else if(t.data.current?.type==="Task"){r.current=t.data.current.task.status;let{tasksInColumn:e,taskPosition:n,column:a}=h(t.id,r.current);return`Picked up Task ${t.data.current.task.title} at position: ${n+1} of ${e.length} in column ${a?.title}`}}},onDragOver({active:e,over:t}){if(x(e)&&x(t)){if(e.data.current?.type==="Column"&&t.data.current?.type==="Column"){let r=n.findIndex(e=>e===t.id);return`Column ${e.data.current.column.title} was moved over ${t.data.current.column.title} at position ${r+1} of ${n.length}`}else if(e.data.current?.type==="Task"&&t.data.current?.type==="Task"){let{tasksInColumn:n,taskPosition:a,column:i}=h(t.id,t.data.current.task.status);return t.data.current.task.status!==r.current?`Task ${e.data.current.task.title} was moved over column ${i?.title} in position ${a+1} of ${n.length}`:`Task was moved over position ${a+1} of ${n.length} in column ${i?.title}`}}},onDragEnd({active:e,over:t}){if(!x(e)||!x(t)){r.current="TODO";return}if(e.data.current?.type==="Column"&&t.data.current?.type==="Column"){let r=n.findIndex(e=>e===t.id);return`Column ${e.data.current.column.title} was dropped into position ${r+1} of ${n.length}`}if(e.data.current?.type==="Task"&&t.data.current?.type==="Task"){let{tasksInColumn:e,taskPosition:n,column:a}=h(t.id,t.data.current.task.status);return t.data.current.task.status!==r.current?`Task was dropped into column ${a?.title} in position ${n+1} of ${e.length}`:`Task was dropped into position ${n+1} of ${e.length} in column ${a?.title}`}r.current="TODO"},onDragCancel({active:e}){if(r.current="TODO",x(e))return`Dragging ${e.data.current?.type} cancelled.`}}},sensors:f,onDragStart:function(e){if(!x(e.active))return;let t=e.active.data.current;return t?.type==="Column"?void s(t.column):t?.type==="Task"?void c(t.task):void 0},onDragEnd:function(r){s(null),c(null);let{active:n,over:a}=r;if(!a)return;let i=n.id,l=a.id;if(!x(n))return;let o=n.data.current;if(i===l||o?.type!=="Column")return;let d=e.findIndex(e=>e.id===i),u=e.findIndex(e=>e.id===l);t(tu(e,d,u))},onDragOver:function(e){let{active:t,over:r}=e;if(!r)return;let n=t.id,l=r.id;if(n===l||!x(t)||!x(r))return;let s=t.data.current,o=r.data.current,d=s?.type==="Task",u=s?.type==="Task";if(!d)return;if(d&&u){let e=a.findIndex(e=>e.id===n),t=a.findIndex(e=>e.id===l),r=a[e],s=a[t];r&&s&&r.status!==s.status&&(r.status=s.status,i(tu(a,e,t-1))),i(tu(a,e,t))}let c=o?.type==="Column";if(d&&c){let e=a.findIndex(e=>e.id===n),t=a[e];t&&(t.status=l,i(tu(a,e,e)))}},"data-sentry-element":"DndContext","data-sentry-component":"KanbanBoard","data-sentry-source-file":"kanban-board.tsx",children:[(0,p.jsx)(tL,{"data-sentry-element":"BoardContainer","data-sentry-source-file":"kanban-board.tsx",children:(0,p.jsxs)(tg,{items:n,"data-sentry-element":"SortableContext","data-sentry-source-file":"kanban-board.tsx",children:[e?.map((t,r)=>(0,p.jsxs)(g.Fragment,{children:[(0,p.jsx)(tB,{column:t,tasks:a.filter(e=>e.status===t.id)}),r===e?.length-1&&(0,p.jsx)("div",{className:"w-[300px]",children:(0,p.jsx)(t$,{})})]},t.id)),!e.length&&(0,p.jsx)(t$,{})]})}),"document"in window&&(0,v.createPortal)((0,p.jsxs)(td,{children:[l&&(0,p.jsx)(tB,{isOverlay:!0,column:l,tasks:a.filter(e=>e.status===l.id)}),u&&(0,p.jsx)(tA,{task:u,isOverlay:!0})]}),document.body)]});function h(t,r){let n=a.filter(e=>e.status===r),i=n.findIndex(e=>e.id===t);return{tasksInColumn:n,taskPosition:i,column:e.find(e=>e.id===r)}}}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,9255,8774,7494,6273],()=>r(48448));module.exports=n})();
//# sourceMappingURL=page.js.map