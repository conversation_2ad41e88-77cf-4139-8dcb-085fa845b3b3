try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="5cd9f02e-8de2-4122-8872-865ea9aaa4ab",e._sentryDebugIdIdentifier="sentry-dbid-5cd9f02e-8de2-4122-8872-865ea9aaa4ab")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[229],{29980:(e,n,t)=>{t.d(n,{Bk:()=>d,It:()=>l,RoleProvider:()=>o,Y0:()=>u});var a=t(52880),i=t(99004),s=t(87905),r=t(39426);let c=(0,i.createContext)(void 0);function o(e){let{children:n}=e,{user:t,isLoaded:o}=(0,s.Jd)(),[l,d]=(0,i.useState)(null),[u,p]=(0,i.useState)(!0),[f,m]=(0,i.useState)(null),y=async()=>{var e,n,a,i;if(!t){d(null),p(!1);return}try{p(!0),m(null);let i=await fetch("/api/auth/sync",{method:"POST",headers:{"Content-Type":"application/json"}});if(!i.ok)throw Error("Failed to sync user data");let s=await i.json(),r={clerkId:t.id,email:(null==(e=t.emailAddresses[0])?void 0:e.emailAddress)||"",firstName:t.firstName||void 0,lastName:t.lastName||void 0,role:(null==(n=s.user)?void 0:n.role)||"front-desk",payloadUserId:null==(a=s.user)?void 0:a.payloadUserId};d(r)}catch(e){console.error("Error fetching user role:",e),m(e instanceof Error?e.message:"Failed to fetch user role"),t&&d({clerkId:t.id,email:(null==(i=t.emailAddresses[0])?void 0:i.emailAddress)||"",firstName:t.firstName||void 0,lastName:t.lastName||void 0,role:"front-desk",payloadUserId:void 0})}finally{p(!1)}};(0,i.useEffect)(()=>{o&&y()},[null==t?void 0:t.id,o]);let g=(null==l?void 0:l.role)?(0,r.bn)(l.role):null;return(0,a.jsx)(c.Provider,{value:{user:l,loading:u,error:f,permissions:g,hasPermission:e=>(0,r._m)(l,e),hasRole:e=>(0,r.hf)(l,e),refreshUser:y},"data-sentry-element":"RoleContext.Provider","data-sentry-component":"RoleProvider","data-sentry-source-file":"role-context.tsx",children:n})}function l(){let e=(0,i.useContext)(c);if(void 0===e)throw Error("useRole must be used within a RoleProvider");return e}function d(e){let{permission:n,children:t,fallback:i=null}=e;return!function(e){let{hasPermission:n}=l();return n(e)}(n)?(0,a.jsx)(a.Fragment,{children:i}):(0,a.jsx)(a.Fragment,{children:t})}function u(e){let{roles:n,children:t,fallback:i=null}=e;return!function(e){let{hasRole:n}=l();return n(e)}(n)?(0,a.jsx)(a.Fragment,{children:i}):(0,a.jsx)(a.Fragment,{children:t})}},39426:(e,n,t)=>{function a(e){switch(e){case"admin":return{canCreatePatients:!0,canEditPatients:!0,canDeletePatients:!0,canViewMedicalNotes:!0,canEditMedicalNotes:!0,canCreateAppointments:!0,canEditAllAppointments:!0,canEditOwnAppointments:!0,canDeleteAppointments:!0,canCreateTreatments:!0,canEditTreatments:!0,canDeleteTreatments:!0,canManageUsers:!0,canViewAnalytics:!0,canViewFinancialData:!0,canCreateBills:!0,canEditBills:!0,canDeleteBills:!0,canViewAllBills:!0,canViewOwnBills:!0,canProcessPayments:!0,canViewPayments:!0,canGenerateReceipts:!0,canHandleRefunds:!0,canGenerateReports:!0,canViewDetailedFinancials:!0,canManagePaymentMethods:!0,canApplyDiscounts:!0,canApproveRefunds:!0,canViewSensitiveFinancials:!0,canExportFinancialData:!0,canBulkUpdateBills:!0,canOverridePaymentLimits:!0,canAccessAuditLogs:!0,canManageDeposits:!0,canProcessDepositRefunds:!0,canViewPatientFinancialHistory:!0,canModifyBillDueDates:!0,canWaiveFees:!0,canAccessAdvancedReports:!0,canManageBillingSettings:!0,canCreateDeposits:!0,canEditDeposits:!0,canDeleteDeposits:!0,canApplyDeposits:!0,canRefundDeposits:!0,canViewDepositHistory:!0,canViewDailyReports:!0,canViewMonthlyReports:!0,canViewYearlyReports:!0,canViewOutstandingReports:!0,canViewPaymentMethodReports:!0,canViewTreatmentRevenueReports:!0,canExportReports:!0,canScheduleReports:!0};case"doctor":return{canCreatePatients:!1,canEditPatients:!1,canDeletePatients:!1,canViewMedicalNotes:!0,canEditMedicalNotes:!0,canCreateAppointments:!1,canEditAllAppointments:!1,canEditOwnAppointments:!0,canDeleteAppointments:!1,canCreateTreatments:!1,canEditTreatments:!1,canDeleteTreatments:!1,canManageUsers:!1,canViewAnalytics:!1,canViewFinancialData:!1,canCreateBills:!1,canEditBills:!1,canDeleteBills:!1,canViewAllBills:!1,canViewOwnBills:!0,canProcessPayments:!1,canViewPayments:!0,canGenerateReceipts:!1,canHandleRefunds:!1,canGenerateReports:!1,canViewDetailedFinancials:!1,canManagePaymentMethods:!1,canApplyDiscounts:!1,canApproveRefunds:!1,canViewSensitiveFinancials:!1,canExportFinancialData:!1,canBulkUpdateBills:!1,canOverridePaymentLimits:!1,canAccessAuditLogs:!1,canManageDeposits:!1,canProcessDepositRefunds:!1,canViewPatientFinancialHistory:!0,canModifyBillDueDates:!1,canWaiveFees:!1,canAccessAdvancedReports:!1,canManageBillingSettings:!1,canCreateDeposits:!1,canEditDeposits:!1,canDeleteDeposits:!1,canApplyDeposits:!1,canRefundDeposits:!1,canViewDepositHistory:!1,canViewDailyReports:!1,canViewMonthlyReports:!1,canViewYearlyReports:!1,canViewOutstandingReports:!1,canViewPaymentMethodReports:!1,canViewTreatmentRevenueReports:!1,canExportReports:!1,canScheduleReports:!1};case"front-desk":return{canCreatePatients:!0,canEditPatients:!0,canDeletePatients:!0,canViewMedicalNotes:!1,canEditMedicalNotes:!1,canCreateAppointments:!0,canEditAllAppointments:!0,canEditOwnAppointments:!0,canDeleteAppointments:!0,canCreateTreatments:!1,canEditTreatments:!1,canDeleteTreatments:!1,canManageUsers:!1,canViewAnalytics:!1,canViewFinancialData:!1,canCreateBills:!0,canEditBills:!0,canDeleteBills:!1,canViewAllBills:!0,canViewOwnBills:!0,canProcessPayments:!0,canViewPayments:!0,canGenerateReceipts:!0,canHandleRefunds:!1,canGenerateReports:!1,canViewDetailedFinancials:!1,canManagePaymentMethods:!0,canApplyDiscounts:!0,canApproveRefunds:!1,canViewSensitiveFinancials:!1,canExportFinancialData:!1,canBulkUpdateBills:!0,canOverridePaymentLimits:!1,canAccessAuditLogs:!1,canManageDeposits:!0,canProcessDepositRefunds:!1,canViewPatientFinancialHistory:!0,canModifyBillDueDates:!0,canWaiveFees:!1,canAccessAdvancedReports:!1,canManageBillingSettings:!1,canCreateDeposits:!0,canEditDeposits:!0,canDeleteDeposits:!1,canApplyDeposits:!0,canRefundDeposits:!1,canViewDepositHistory:!0,canViewDailyReports:!0,canViewMonthlyReports:!0,canViewYearlyReports:!1,canViewOutstandingReports:!0,canViewPaymentMethodReports:!0,canViewTreatmentRevenueReports:!1,canExportReports:!1,canScheduleReports:!1};default:return{canCreatePatients:!1,canEditPatients:!1,canDeletePatients:!1,canViewMedicalNotes:!1,canEditMedicalNotes:!1,canCreateAppointments:!1,canEditAllAppointments:!1,canEditOwnAppointments:!1,canDeleteAppointments:!1,canCreateTreatments:!1,canEditTreatments:!1,canDeleteTreatments:!1,canManageUsers:!1,canViewAnalytics:!1,canViewFinancialData:!1,canCreateBills:!1,canEditBills:!1,canDeleteBills:!1,canViewAllBills:!1,canViewOwnBills:!1,canProcessPayments:!1,canViewPayments:!1,canGenerateReceipts:!1,canHandleRefunds:!1,canGenerateReports:!1,canViewDetailedFinancials:!1,canManagePaymentMethods:!1,canApplyDiscounts:!1,canApproveRefunds:!1,canViewSensitiveFinancials:!1,canExportFinancialData:!1,canBulkUpdateBills:!1,canOverridePaymentLimits:!1,canAccessAuditLogs:!1,canManageDeposits:!1,canProcessDepositRefunds:!1,canViewPatientFinancialHistory:!1,canModifyBillDueDates:!1,canWaiveFees:!1,canAccessAdvancedReports:!1,canManageBillingSettings:!1,canCreateDeposits:!1,canEditDeposits:!1,canDeleteDeposits:!1,canApplyDeposits:!1,canRefundDeposits:!1,canViewDepositHistory:!1,canViewDailyReports:!1,canViewMonthlyReports:!1,canViewYearlyReports:!1,canViewOutstandingReports:!1,canViewPaymentMethodReports:!1,canViewTreatmentRevenueReports:!1,canExportReports:!1,canScheduleReports:!1}}}function i(e,n){return!!e&&!!e.role&&a(e.role)[n]}function s(e,n){return!!e&&!!e.role&&(Array.isArray(n)?n:[n]).includes(e.role)}function r(e){switch(e){case"admin":return"管理员";case"front-desk":return"前台";case"doctor":return"医生";default:return"未知角色"}}function c(e){switch(e){case"admin":return"bg-red-100 text-red-800";case"doctor":return"bg-blue-100 text-blue-800";case"front-desk":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}}t.d(n,{Pj:()=>c,_m:()=>i,bn:()=>a,cb:()=>r,hf:()=>s})},54651:(e,n,t)=>{t.d(n,{Jv:()=>l,cn:()=>s,fw:()=>o,r6:()=>c,z3:()=>r});var a=t(97921),i=t(56309);function s(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];return(0,i.QP)((0,a.$)(n))}function r(e){var n,t;let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{decimals:i=0,sizeType:s="normal"}=a;if(0===e)return"0 Byte";let r=Math.floor(Math.log(e)/Math.log(1024));return"".concat((e/Math.pow(1024,r)).toFixed(i)," ").concat("accurate"===s?null!=(n=["Bytes","KiB","MiB","GiB","TiB"][r])?n:"Bytest":null!=(t=["Bytes","KB","MB","GB","TB"][r])?t:"Bytes")}function c(e){return new Intl.DateTimeFormat("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}).format(e)}function o(e){let n=Math.floor((new Date().getTime()-e.getTime())/1e3);if(n<60)return"刚刚";let t=Math.floor(n/60);if(t<60)return"".concat(t,"分钟前");let a=Math.floor(t/60);if(a<24)return"".concat(a,"小时前");let i=Math.floor(a/24);if(i<7)return"".concat(i,"天前");let s=Math.floor(i/7);if(s<4)return"".concat(s,"周前");let r=Math.floor(i/30);if(r<12)return"".concat(r,"个月前");let c=Math.floor(i/365);return"".concat(c,"年前")}function l(e){return("string"==typeof e?new Date(e):e)<new Date}},62054:(e,n,t)=>{t.d(n,{$:()=>o,r:()=>c});var a=t(52880);t(99004);var i=t(50516),s=t(85017),r=t(54651);let c=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:n,variant:t,size:s,asChild:o=!1,...l}=e,d=o?i.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,r.cn)(c({variant:t,size:s,className:n})),...l,"data-sentry-element":"Comp","data-sentry-component":"Button","data-sentry-source-file":"button.tsx"})}}}]);