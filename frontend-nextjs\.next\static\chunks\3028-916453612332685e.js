try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="31e50f34-0257-43c6-8649-f327a016c415",e._sentryDebugIdIdentifier="sentry-dbid-31e50f34-0257-43c6-8649-f327a016c415")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3028],{83028:(e,r,n)=>{n.d(r,{H_:()=>e0,UC:()=>eJ,YJ:()=>eW,q7:()=>e$,VF:()=>e1,JU:()=>eQ,ZL:()=>ez,bL:()=>eY,wv:()=>e5,l9:()=>eZ});var t=n(99004),o=n(84732),a=n(39552),l=n(38774),u=n(18608),i=n(51452),d=n(59694),s=n(51825),c=n(17430),p=n(6280),f=n(40201),v=n(29548),m=n(97677),h=n(55173),g=n(22474),w=n(59949),x=n(50516),y=n(36962),b=n(10144),C=n(92350),M=n(52880),D=["Enter"," "],R=["ArrowUp","PageDown","End"],j=["ArrowDown","PageUp","Home",...R],_={ltr:[...D,"ArrowRight"],rtl:[...D,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[P,E,N]=(0,d.N)(I),[T,O]=(0,l.A)(I,[N,m.Bk,w.RG]),S=(0,m.Bk)(),L=(0,w.RG)(),[F,A]=T(I),[K,G]=T(I),U=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=S(r),[d,c]=t.useState(null),p=t.useRef(!1),f=(0,y.c)(l),v=(0,s.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,M.jsx)(m.bL,{...i,children:(0,M.jsx)(F,{scope:r,open:n,onOpenChange:f,content:d,onContentChange:c,children:(0,M.jsx)(K,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};U.displayName=I;var B=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=S(n);return(0,M.jsx)(m.Mz,{...o,...t,ref:r})});B.displayName="MenuAnchor";var V="MenuPortal",[X,q]=T(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=A(V,r);return(0,M.jsx)(X,{scope:r,forceMount:n,children:(0,M.jsx)(g.C,{present:n||a.open,children:(0,M.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};H.displayName=V;var Y="MenuContent",[Z,z]=T(Y),J=t.forwardRef((e,r)=>{let n=q(Y,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=A(Y,e.__scopeMenu),l=G(Y,e.__scopeMenu);return(0,M.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:t||a.open,children:(0,M.jsx)(P.Slot,{scope:e.__scopeMenu,children:l.modal?(0,M.jsx)(W,{...o,ref:r}):(0,M.jsx)(Q,{...o,ref:r})})})})}),W=t.forwardRef((e,r)=>{let n=A(Y,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,M.jsx)($,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=A(Y,e.__scopeMenu);return(0,M.jsx)($,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:D,disableOutsideScroll:_,...k}=e,I=A(Y,n),P=G(Y,n),N=S(n),T=L(n),O=E(n),[F,K]=t.useState(null),U=t.useRef(null),B=(0,a.s)(r,U,I.onContentChange),V=t.useRef(0),X=t.useRef(""),q=t.useRef(0),H=t.useRef(null),z=t.useRef("right"),J=t.useRef(0),W=_?C.A:t.Fragment,Q=_?{as:x.DX,allowPinchZoom:!0}:void 0,$=e=>{var r,n;let t=X.current+e,o=O().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){X.current=r,window.clearTimeout(V.current),""!==r&&(V.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,p.Oh)();let ee=t.useCallback(e=>{var r,n;return z.current===(null==(r=H.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e].x,u=r[e].y,i=r[a].x,d=r[a].y;u>t!=d>t&&n<(i-l)*(t-u)/(d-u)+l&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=H.current)?void 0:n.area)},[]);return(0,M.jsx)(Z,{scope:n,searchRef:X,onItemEnter:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:t.useCallback(e=>{var r;ee(e)||(null==(r=U.current)||r.focus(),K(null))},[ee]),onTriggerLeave:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{H.current=e},[]),children:(0,M.jsx)(W,{...Q,children:(0,M.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=U.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,M.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:D,children:(0,M.jsx)(w.bL,{asChild:!0,...T,dir:P.dir,orientation:"vertical",loop:l,currentTabStopId:F,onCurrentTabStopIdChange:K,onEntryFocus:(0,o.m)(v,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eR(I.open),"data-radix-menu-content":"",dir:P.dir,...N,...k,ref:B,style:{outline:"none",...k.style},onKeyDown:(0,o.m)(k.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&$(e.key));let o=U.current;if(e.target!==o||!j.includes(e.key))return;e.preventDefault();let a=O().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),X.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{let r=e.target,n=J.current!==e.clientX;e.currentTarget.contains(r)&&n&&(z.current=e.clientX>J.current?"right":"left",J.current=e.clientX)}))})})})})})})});J.displayName=Y;var ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,M.jsx)(i.sG.div,{role:"group",...t,ref:r})});ee.displayName="MenuGroup";var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,M.jsx)(i.sG.div,{...t,ref:r})});er.displayName="MenuLabel";var en="MenuItem",et="menu.itemSelect",eo=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,d=t.useRef(null),s=G(en,e.__scopeMenu),c=z(en,e.__scopeMenu),p=(0,a.s)(r,d),f=t.useRef(!1);return(0,M.jsx)(ea,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!n&&e){let r=new CustomEvent(et,{bubbles:!0,cancelable:!0});e.addEventListener(et,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:s.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||D.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ea=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...d}=e,s=z(en,n),c=L(n),p=t.useRef(null),f=(0,a.s)(r,p),[v,m]=t.useState(!1),[h,g]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;g((null!=(r=e.textContent)?r:"").trim())}},[d.children]),(0,M.jsx)(P.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:h,children:(0,M.jsx)(w.q7,{asChild:!0,...c,focusable:!l,children:(0,M.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...d,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{l?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),el=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,M.jsx)(eo,{role:"menuitemcheckbox","aria-checked":ej(n)?"mixed":n,...a,ref:r,"data-state":e_(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!ej(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ei,ed]=T(eu,{value:void 0,onValueChange:()=>{}}),es=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,M.jsx)(ei,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,M.jsx)(ee,{...o,ref:r})})});es.displayName=eu;var ec="MenuRadioItem",ep=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ed(ec,e.__scopeMenu),l=n===a.value;return(0,M.jsx)(ev,{scope:e.__scopeMenu,checked:l,children:(0,M.jsx)(eo,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":e_(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ep.displayName=ec;var ef="MenuItemIndicator",[ev,em]=T(ef,{checked:!1}),eh=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=em(ef,n);return(0,M.jsx)(g.C,{present:t||ej(a.checked)||!0===a.checked,children:(0,M.jsx)(i.sG.span,{...o,ref:r,"data-state":e_(a.checked)})})});eh.displayName=ef;var eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,M.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});eg.displayName="MenuSeparator";var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=S(n);return(0,M.jsx)(m.i3,{...o,...t,ref:r})});ew.displayName="MenuArrow";var[ex,ey]=T("MenuSub"),eb="MenuSubTrigger",eC=t.forwardRef((e,r)=>{let n=A(eb,e.__scopeMenu),l=G(eb,e.__scopeMenu),u=ey(eb,e.__scopeMenu),i=z(eb,e.__scopeMenu),d=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,M.jsx)(B,{asChild:!0,...p,children:(0,M.jsx)(ea,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eR(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ek(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>{var r,t;f();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&_[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});eC.displayName=eb;var eM="MenuSubContent",eD=t.forwardRef((e,r)=>{let n=q(Y,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=A(Y,e.__scopeMenu),d=G(Y,e.__scopeMenu),s=ey(eM,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,M.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.C,{present:l||i.open,children:(0,M.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)($,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;d.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=k[d.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=s.trigger)||t.focus(),e.preventDefault()}})})})})})});function eR(e){return e?"open":"closed"}function ej(e){return"indeterminate"===e}function e_(e){return ej(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return r=>"mouse"===r.pointerType?e(r):void 0}eD.displayName=eM;var eI="DropdownMenu",[eP,eE]=(0,l.A)(eI,[O]),eN=O(),[eT,eO]=eP(eI),eS=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:d=!0}=e,s=eN(r),c=t.useRef(null),[p=!1,f]=(0,u.i)({prop:a,defaultProp:l,onChange:i});return(0,M.jsx)(eT,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:d,children:(0,M.jsx)(U,{...s,open:p,onOpenChange:f,dir:o,modal:d,children:n})})};eS.displayName=eI;var eL="DropdownMenuTrigger",eF=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eO(eL,n),d=eN(n);return(0,M.jsx)(B,{asChild:!0,...d,children:(0,M.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=eL;var eA=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eN(r);return(0,M.jsx)(H,{...t,...n})};eA.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eG=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eO(eK,n),u=eN(n),i=t.useRef(!1);return(0,M.jsx)(J,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eK;var eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(ee,{...o,...t,ref:r})});eU.displayName="DropdownMenuGroup";var eB=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(er,{...o,...t,ref:r})});eB.displayName="DropdownMenuLabel";var eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(eo,{...o,...t,ref:r})});eV.displayName="DropdownMenuItem";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(el,{...o,...t,ref:r})});eX.displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(es,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(ep,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem";var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(eh,{...o,...t,ref:r})});eq.displayName="DropdownMenuItemIndicator";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(eg,{...o,...t,ref:r})});eH.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(ew,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(eC,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eN(n);return(0,M.jsx)(eD,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eY=eS,eZ=eF,ez=eA,eJ=eG,eW=eU,eQ=eB,e$=eV,e0=eX,e1=eq,e5=eH}}]);