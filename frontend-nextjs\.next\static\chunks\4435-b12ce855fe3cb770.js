try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="a459466f-b53c-4f99-89a4-28e0b07ea06c",e._sentryDebugIdIdentifier="sentry-dbid-a459466f-b53c-4f99-89a4-28e0b07ea06c")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4435],{4853:(e,t,n)=>{n.d(t,{be:()=>a,gB:()=>d,gl:()=>b});var r=n(99004),l=n(48211),i=n(50414);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s="Sortable",c=r.createContext({activeIndex:-1,containerId:s,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function d(e){let{children:t,id:n,items:a,strategy:o=u,disabled:d=!1}=e,{active:f,dragOverlay:h,droppableRects:g,over:p,measureDroppableContainers:v}=(0,l.fF)(),m=(0,i.YG)(s,n),b=null!==h.rect,y=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=f,x=f?y.indexOf(f.id):-1,E=p?y.indexOf(p.id):-1,D=(0,r.useRef)(y),S=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(y,D.current),C=-1!==E&&-1===x||S,R="boolean"==typeof d?{draggable:d,droppable:d}:d;(0,i.Es)(()=>{S&&w&&v(y)},[S,y,w,v]),(0,r.useEffect)(()=>{D.current=y},[y]);let M=(0,r.useMemo)(()=>({activeIndex:x,containerId:m,disabled:R,disableTransforms:C,items:y,overIndex:E,useDragOverlay:b,sortedRects:y.reduce((e,t,n)=>{let r=g.get(t);return r&&(e[n]=r),e},Array(y.length)),strategy:o}),[x,m,R.draggable,R.droppable,C,y,E,g,b,o]);return r.createElement(c.Provider,{value:M},t)}let f=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},h=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},g={duration:200,easing:"ease"},p="transform",v=i.Ks.Transition.toString({property:p,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function b(e){var t,n,a,u;let{animateLayoutChanges:s=h,attributes:d,disabled:b,data:y,getNewIndex:w=f,id:x,strategy:E,resizeObserverConfig:D,transition:S=g}=e,{items:C,containerId:R,activeIndex:M,disabled:k,disableTransforms:O,sortedRects:T,overIndex:I,useDragOverlay:N,strategy:L}=(0,r.useContext)(c),A=(t=b,n=k,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),z=C.indexOf(x),P=(0,r.useMemo)(()=>({sortable:{containerId:R,index:z,items:C},...y}),[R,y,z,C]),j=(0,r.useMemo)(()=>C.slice(C.indexOf(x)),[C,x]),{rect:Y,node:U,isOver:B,setNodeRef:F}=(0,l.zM)({id:x,data:P,disabled:A.droppable,resizeObserverConfig:{updateMeasurementsFor:j,...D}}),{active:W,activatorEvent:K,activeNodeRect:_,attributes:X,setNodeRef:G,listeners:H,isDragging:V,over:J,setActivatorNodeRef:Z,transform:q}=(0,l.PM)({id:x,data:P,attributes:{...m,...d},disabled:A.draggable}),$=(0,i.jn)(F,G),Q=!!W,ee=Q&&!O&&o(M)&&o(I),et=!N&&V,en=et&&ee?q:null,er=ee?null!=en?en:(null!=E?E:L)({rects:T,activeNodeRect:_,activeIndex:M,overIndex:I,index:z}):null,el=o(M)&&o(I)?w({id:x,items:C,activeIndex:M,overIndex:I}):z,ei=null==W?void 0:W.id,ea=(0,r.useRef)({activeId:ei,items:C,newIndex:el,containerId:R}),eo=C!==ea.current.items,eu=s({active:W,containerId:R,isDragging:V,isSorting:Q,id:x,index:z,items:C,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:S,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:z,node:U,rect:Y});return(0,r.useEffect)(()=>{Q&&ea.current.newIndex!==el&&(ea.current.newIndex=el),R!==ea.current.containerId&&(ea.current.containerId=R),C!==ea.current.items&&(ea.current.items=C)},[Q,el,R,C]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(ei&&!ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:W,activeIndex:M,attributes:X,data:P,rect:Y,index:z,newIndex:el,items:C,isOver:B,isSorting:Q,isDragging:V,listeners:H,node:U,overIndex:I,over:J,setNodeRef:$,setActivatorNodeRef:Z,setDroppableNodeRef:F,setDraggableNodeRef:G,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===z?v:(!et||(0,i.kx)(K))&&S&&(Q||eu)?i.Ks.Transition.toString({...S,property:p}):void 0}}l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left},17879:(e,t,n)=>{let r;n.d(t,{A:()=>o});let l={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},i=new Uint8Array(16),a=[];for(let e=0;e<256;++e)a.push((e+256).toString(16).slice(1));let o=function(e,t,n){if(l.randomUUID&&!t&&!e)return l.randomUUID();let o=(e=e||{}).random??e.rng?.()??function(){if(!r){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(i)}();if(o.length<16)throw Error("Random bytes length must be >= 16");if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,t){if((n=n||0)<0||n+16>t.length)throw RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[n+e]=o[e];return t}return function(e,t=0){return(a[e[t+0]]+a[e[t+1]]+a[e[t+2]]+a[e[t+3]]+"-"+a[e[t+4]]+a[e[t+5]]+"-"+a[e[t+6]]+a[e[t+7]]+"-"+a[e[t+8]]+a[e[t+9]]+"-"+a[e[t+10]]+a[e[t+11]]+a[e[t+12]]+a[e[t+13]]+a[e[t+14]]+a[e[t+15]]).toLowerCase()}(o)}},34593:(e,t,n)=>{n.d(t,{v:()=>u});var r=n(99004);let l=e=>{let t,n=new Set,r=(e,r)=>{let l="function"==typeof e?e(t):e;if(!Object.is(l,t)){let e=t;t=(null!=r?r:"object"!=typeof l||null===l)?l:Object.assign({},t,l),n.forEach(n=>n(t,e))}},l=()=>t,i={setState:r,getState:l,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,l,i);return i},i=e=>e?l(e):l,a=e=>e,o=e=>{let t=i(e),n=e=>(function(e,t=a){let n=r.useSyncExternalStore(e.subscribe,()=>t(e.getState()),()=>t(e.getInitialState()));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},u=e=>e?o(e):o},42706:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(49202).A)("outline","grip-vertical","IconGripVertical",[["path",{d:"M9 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M9 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M9 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}],["path",{d:"M15 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-3"}],["path",{d:"M15 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-4"}],["path",{d:"M15 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-5"}]])},46937:(e,t,n)=>{n.d(t,{UC:()=>N,VY:()=>P,ZD:()=>A,ZL:()=>T,bL:()=>O,hE:()=>z,hJ:()=>I,rc:()=>L});var r=n(99004),l=n(38774),i=n(39552),a=n(88749),o=n(84732),u=n(50516),s=n(52880),c="AlertDialog",[d,f]=(0,l.A)(c,[a.Hs]),h=(0,a.Hs)(),g=e=>{let{__scopeAlertDialog:t,...n}=e,r=h(t);return(0,s.jsx)(a.bL,{...r,...n,modal:!0})};g.displayName=c,r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.l9,{...l,...r,ref:t})}).displayName="AlertDialogTrigger";var p=e=>{let{__scopeAlertDialog:t,...n}=e,r=h(t);return(0,s.jsx)(a.ZL,{...r,...n})};p.displayName="AlertDialogPortal";var v=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.hJ,{...l,...r,ref:t})});v.displayName="AlertDialogOverlay";var m="AlertDialogContent",[b,y]=d(m),w=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:l,...c}=e,d=h(n),f=r.useRef(null),g=(0,i.s)(t,f),p=r.useRef(null);return(0,s.jsx)(a.G$,{contentName:m,titleName:x,docsSlug:"alert-dialog",children:(0,s.jsx)(b,{scope:n,cancelRef:p,children:(0,s.jsxs)(a.UC,{role:"alertdialog",...d,...c,ref:g,onOpenAutoFocus:(0,o.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=p.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(u.xV,{children:l}),(0,s.jsx)(k,{contentRef:f})]})})})});w.displayName=m;var x="AlertDialogTitle",E=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.hE,{...l,...r,ref:t})});E.displayName=x;var D="AlertDialogDescription",S=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.VY,{...l,...r,ref:t})});S.displayName=D;var C=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=h(n);return(0,s.jsx)(a.bm,{...l,...r,ref:t})});C.displayName="AlertDialogAction";var R="AlertDialogCancel",M=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:l}=y(R,n),o=h(n),u=(0,i.s)(t,l);return(0,s.jsx)(a.bm,{...o,...r,ref:u})});M.displayName=R;var k=e=>{let{contentRef:t}=e,n="`".concat(m,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(m,"` by passing a `").concat(D,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(m,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},O=g,T=p,I=v,N=w,L=C,A=M,z=E,P=S},48211:(e,t,n)=>{let r;n.d(t,{Mp:()=>ez,Hd:()=>eZ,vL:()=>o,cA:()=>eu,IG:()=>ec,Sj:()=>A,fF:()=>eU,PM:()=>eY,zM:()=>eF,MS:()=>C,FR:()=>R});var l,i,a,o,u,s,c,d,f,h,g=n(99004),p=n(32909),v=n(50414);let m={display:"none"};function b(e){let{id:t,value:n}=e;return g.createElement("div",{id:t,style:m},n)}function y(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return g.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,g.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},E={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function D(e){let{announcements:t=E,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,g.useState)("");return{announce:(0,g.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,g.useState)(!1);(0,g.useEffect)(()=>{s(!0)},[]);var c=(0,g.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let d=(0,g.useContext)(w);if((0,g.useEffect)(()=>{if(!d)throw Error("useDndMonitor must be used within a children of <DndContext>");return d(c)},[c,d]),!u)return null;let f=g.createElement(g.Fragment,null,g.createElement(b,{id:r,value:l.draggable}),g.createElement(y,{id:o,announcement:a}));return n?(0,p.createPortal)(f,n):f}function S(){}function C(e,t){return(0,g.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function R(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,g.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let M=Object.freeze({x:0,y:0});function k(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}let O=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(k)};function T(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:M}let I=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1);function N(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}let L={ignoreTransform:!1};function A(e,t){void 0===t&&(t=L);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=N(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function z(e){return A(e,{ignoreTransform:!0})}function P(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function j(e){let[t]=P(e,1);return null!=t?t:null}function Y(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function U(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function B(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function F(e){return{x:U(e),y:B(e)}}function W(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function K(e){let t={x:0,y:0},n=W(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let _={x:.2,y:.2};function X(e){return e.reduce((e,t)=>(0,v.WQ)(e,F(t)),M)}function G(e,t){if(void 0===t&&(t=A),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);j(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}let H=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+U(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+B(t),0)}]];class V{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=P(t),r=X(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,H))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class J{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function Z(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function q(e){e.preventDefault()}function $(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let Q={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},ee=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class et{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new J((0,v.TW)(t)),this.windowListeners=new J((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&G(n),t(M)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=Q,coordinateGetter:i=ee,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u))return void this.handleEnd(e);if(l.cancel.includes(u))return void this.handleCancel(e);let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:M;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:f,minScroll:h}=K(n),g=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(l===o.Right?g.right-g.width/2:g.right,Math.max(l===o.Right?g.left:g.left+g.width/2,d.x)),y:Math.min(l===o.Down?g.bottom-g.height/2:g.bottom,Math.max(l===o.Down?g.top:g.top+g.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,m=l===o.Down&&!c||l===o.Up&&!i;if(v&&p.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=f.x||l===o.Left&&e>=h.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-f.x:n.scrollLeft-h.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(m&&p.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=f.y||l===o.Up&&e>=h.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-f.y:n.scrollTop-h.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function en(e){return!!(e&&"distance"in e)}function er(e){return!!(e&&"delay"in e)}et.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=Q,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class el{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new J(this.document),this.listeners=new J(n),this.windowListeners=new J((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:M,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,q),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,q),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(er(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(en(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,$,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:M,u=(0,v.Re)(r,o);if(!n&&a){if(en(a)){if(null!=a.tolerance&&Z(u,a.tolerance))return this.handleCancel();if(Z(u,a.distance))return this.handleStart()}return er(a)&&Z(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ei={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ea extends el{constructor(e){let{event:t}=e;super(e,ei,(0,v.TW)(t.target))}}ea.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eo={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class eu extends el{constructor(e){super(e,eo,(0,v.TW)(e.event.target))}}eu.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let es={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class ec extends el{constructor(e){super(e,es)}static setup(){return window.addEventListener(es.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(es.move.name,e)};function e(){}}}ec.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ed={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(f||(f={})).Optimized="optimized";let ef=new Map;function eh(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function eg(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ep(e){return new V(A(e),e)}function ev(e,t,n){void 0===t&&(t=ep);let[r,l]=(0,g.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,g.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,g.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=eg({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let em=[];function eb(e,t){void 0===t&&(t=[]);let n=(0,g.useRef)(null);return(0,g.useEffect)(()=>{n.current=null},t),(0,g.useEffect)(()=>{let t=e!==M;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):M}function ey(e){return(0,g.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let ew=[];function ex(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}let eE=[{sensor:ea,options:{}},{sensor:et,options:{}}],eD={current:{}},eS={draggable:{measure:z},droppable:{measure:z,strategy:d.WhileDragging,frequency:f.Optimized},dragOverlay:{measure:A}};class eC extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eR={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eC,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:S},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eS,measureDroppableContainers:S,windowRect:null,measuringScheduled:!1},eM={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:S,draggableNodes:new Map,over:null,measureDroppableContainers:S},ek=(0,g.createContext)(eM),eO=(0,g.createContext)(eR);function eT(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eC}}}function eI(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eC(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eC(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eC(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eN(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,g.useContext)(ek),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,g.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}function eL(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}let eA=(0,g.createContext)({...M,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(h||(h={}));let ez=(0,g.memo)(function(e){var t,n,r,a,o,u;let{id:f,accessibility:m,autoScroll:b=!0,children:y,sensors:x=eE,collisionDetection:E=O,measuring:S,modifiers:C,...R}=e,[k,N]=(0,g.useReducer)(eI,void 0,eT),[L,z]=function(){let[e]=(0,g.useState)(()=>new Set),t=(0,g.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,g.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[U,B]=(0,g.useState)(h.Uninitialized),G=U===h.Initialized,{draggable:{active:H,nodes:J,translate:Z},droppable:{containers:q}}=k,$=null!=H?J.get(H):null,Q=(0,g.useRef)({initial:null,translated:null}),ee=(0,g.useMemo)(()=>{var e;return null!=H?{id:H,data:null!=(e=null==$?void 0:$.data)?e:eD,rect:Q}:null},[H,$]),et=(0,g.useRef)(null),[en,er]=(0,g.useState)(null),[el,ei]=(0,g.useState)(null),ea=(0,v.YN)(R,Object.values(R)),eo=(0,v.YG)("DndDescribedBy",f),eu=(0,g.useMemo)(()=>q.getEnabled(),[q]),es=(0,g.useMemo)(()=>({draggable:{...eS.draggable,...null==S?void 0:S.draggable},droppable:{...eS.droppable,...null==S?void 0:S.droppable},dragOverlay:{...eS.dragOverlay,...null==S?void 0:S.dragOverlay}}),[null==S?void 0:S.draggable,null==S?void 0:S.droppable,null==S?void 0:S.dragOverlay]),{droppableRects:ec,measureDroppableContainers:ep,measuringScheduled:eC}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,g.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,g.useRef)(e),f=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),h=(0,v.YN)(f),p=(0,g.useCallback)(function(e){void 0===e&&(e=[]),h.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[h]),m=(0,g.useRef)(null),b=(0,v.KG)(t=>{if(f&&!n)return ef;if(!t||t===ef||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new V(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,f,u]);return(0,g.useEffect)(()=>{c.current=e},[e]),(0,g.useEffect)(()=>{f||p()},[n,f]),(0,g.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,g.useEffect)(()=>{f||"number"!=typeof o||null!==m.current||(m.current=setTimeout(()=>{p(),m.current=null},o))},[o,f,p,...r]),{droppableRects:b,measureDroppableContainers:p,measuringScheduled:null!=i}}(eu,{dragging:G,dependencies:[Z.x,Z.y],config:es.droppable}),eR=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(J,H),eM=(0,g.useMemo)(()=>el?(0,v.e_)(el):null,[el]),ez=function(){let e=(null==en?void 0:en.autoScrollEnabled)===!1,t="object"==typeof b?!1===b.enabled:!1===b,n=G&&!e&&!t;return"object"==typeof b?{...b,enabled:n}:{enabled:n}}(),eP=eh(eR,es.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,g.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=T(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=j(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=H?J.get(H):null,config:ez.layoutShiftCompensation,initialRect:eP,measure:es.draggable.measure});let ej=ev(eR,es.draggable.measure,eP),eY=ev(eR?eR.parentElement:null),eU=(0,g.useRef)({activatorEvent:null,active:null,activeNode:eR,collisionRect:null,collisions:null,droppableRects:ec,draggableNodes:J,draggingNode:null,draggingNodeRect:null,droppableContainers:q,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eB=q.getNodeFor(null==(t=eU.current.over)?void 0:t.id),eF=function(e){let{measure:t}=e,[n,r]=(0,g.useState)(null),l=eg({callback:(0,g.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,g.useCallback)(e=>{let n=ex(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,g.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:es.dragOverlay.measure}),eW=null!=(n=eF.nodeRef.current)?n:eR,eK=G?null!=(r=eF.rect)?r:ej:null,e_=!!(eF.nodeRef.current&&eF.rect),eX=function(e){let t=eh(e);return T(e,t)}(e_?null:ej),eG=ey(eW?(0,v.zk)(eW):null),eH=function(e){let t=(0,g.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==em&&e&&t.current&&e.parentNode===t.current.parentNode?n:P(e):em,[e]);return(0,g.useEffect)(()=>{t.current=e},[e]),n}(G?null!=eB?eB:eR:null),eV=function(e,t){void 0===t&&(t=A);let[n]=e,r=ey(n?(0,v.zk)(n):null),[l,i]=(0,g.useState)(ew);function a(){i(()=>e.length?e.map(e=>W(e)?r:new V(t(e),e)):ew)}let o=eg({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(eH),eJ=eL(C,{transform:{x:Z.x-eX.x,y:Z.y-eX.y,scaleX:1,scaleY:1},activatorEvent:el,active:ee,activeNodeRect:ej,containerNodeRect:eY,draggingNodeRect:eK,over:eU.current.over,overlayNodeRect:eF.rect,scrollableAncestors:eH,scrollableAncestorRects:eV,windowRect:eG}),eZ=eM?(0,v.WQ)(eM,Z):null,eq=function(e){let[t,n]=(0,g.useState)(null),r=(0,g.useRef)(e),l=(0,g.useCallback)(e=>{let t=Y(e.target);t&&n(e=>e?(e.set(t,F(t)),new Map(e)):null)},[]);return(0,g.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=Y(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,F(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=Y(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,g.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),M):X(e):M,[e,t])}(eH),e$=eb(eq),eQ=eb(eq,[ej]),e0=(0,v.WQ)(eJ,e$),e1=eK?I(eK,eJ):null,e2=ee&&e1?E({active:ee,collisionRect:e1,droppableRects:ec,droppableContainers:eu,pointerCoordinates:eZ}):null,e5=function(e,t){if(!e||0===e.length)return null;let[n]=e;return n.id}(e2,"id"),[e4,e9]=(0,g.useState)(null),e6=(o=e_?eJ:(0,v.WQ)(eJ,eQ),u=null!=(a=null==e4?void 0:e4.rect)?a:null,{...o,scaleX:u&&ej?u.width/ej.width:1,scaleY:u&&ej?u.height/ej.height:1}),e3=(0,g.useRef)(null),e8=(0,g.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==et.current)return;let i=J.get(et.current);if(!i)return;let a=e.nativeEvent,o=new n({active:et.current,activeNode:i,event:a,options:r,context:eU,onAbort(e){if(!J.get(e))return;let{onDragAbort:t}=ea.current,n={id:e};null==t||t(n),L({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!J.get(e))return;let{onDragPending:l}=ea.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),L({type:"onDragPending",event:i})},onStart(e){let t=et.current;if(null==t)return;let n=J.get(t);if(!n)return;let{onDragStart:r}=ea.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:Q}};(0,p.unstable_batchedUpdates)(()=>{null==r||r(i),B(h.Initializing),N({type:l.DragStart,initialCoordinates:e,active:t}),L({type:"onDragStart",event:i}),er(e3.current),ei(a)})},onMove(e){N({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eU.current,o=null;if(t&&i){let{cancelDrop:u}=ea.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}et.current=null,(0,p.unstable_batchedUpdates)(()=>{N({type:e}),B(h.Uninitialized),e9(null),er(null),ei(null),e3.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=ea.current[t];null==e||e(o),L({type:t,event:o})}})}}e3.current=o},[J]),e7=(0,g.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=J.get(r);null!==et.current||!i||l.dndKit||l.defaultPrevented||!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},et.current=r,e8(n,t))},[J,e8]),te=(0,g.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e7(e.handler,t)}))]},[]),[x,e7]);(0,g.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{ej&&U===h.Initializing&&B(h.Initialized)},[ej,U]),(0,g.useEffect)(()=>{let{onDragMove:e}=ea.current,{active:t,activatorEvent:n,collisions:r,over:l}=eU.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e0.x,y:e0.y},over:l};(0,p.unstable_batchedUpdates)(()=>{null==e||e(i),L({type:"onDragMove",event:i})})},[e0.x,e0.y]),(0,g.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eU.current;if(!e||null==et.current||!t||!l)return;let{onDragOver:i}=ea.current,a=r.get(e5),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,p.unstable_batchedUpdates)(()=>{e9(o),null==i||i(u),L({type:"onDragOver",event:u})})},[e5]),(0,v.Es)(()=>{eU.current={activatorEvent:el,active:ee,activeNode:eR,collisionRect:e1,collisions:e2,droppableRects:ec,draggableNodes:J,draggingNode:eW,draggingNodeRect:eK,droppableContainers:q,over:e4,scrollableAncestors:eH,scrollAdjustedTranslate:e0},Q.current={initial:eK,translated:e1}},[ee,eR,e2,e1,J,eW,eK,ec,q,e4,eH,e0]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:f,scrollableAncestorRects:h,delta:p,threshold:m}=e,b=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return ed;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:p,disabled:!a}),[y,w]=(0,v.$$)(),x=(0,g.useRef)({x:0,y:0}),E=(0,g.useRef)({x:0,y:0}),D=(0,g.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),S=(0,g.useRef)(null),C=(0,g.useCallback)(()=>{let e=S.current;if(!e)return;let t=x.current.x*E.current.x,n=x.current.y*E.current.y;e.scrollBy(t,n)},[]),R=(0,g.useMemo)(()=>u===c.TreeOrder?[...f].reverse():f,[u,f]);(0,g.useEffect)(()=>{if(!a||!f.length||!D)return void w();for(let e of R){if((null==r?void 0:r(e))===!1)continue;let n=h[f.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=_);let{isTop:c,isBottom:d,isLeft:f,isRight:h}=K(e),g={x:0,y:0},p={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(g.y=i.Backward,p.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(g.y=i.Forward,p.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!h&&u>=t.right-v.width?(g.x=i.Forward,p.x=r*Math.abs((t.right-v.width-u)/v.width)):!f&&o<=t.left+v.width&&(g.x=i.Backward,p.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:g,speed:p}}(e,n,D,t,m);for(let e of["x","y"])b[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),S.current=e,y(C,o),x.current=a,E.current=l;return}}x.current={x:0,y:0},E.current={x:0,y:0},w()},[t,C,r,w,a,o,JSON.stringify(D),JSON.stringify(b),y,f,R,h,JSON.stringify(m)])}({...ez,delta:Z,draggingRect:e1,pointerCoordinates:eZ,scrollableAncestors:eH,scrollableAncestorRects:eV});let tt=(0,g.useMemo)(()=>({active:ee,activeNode:eR,activeNodeRect:ej,activatorEvent:el,collisions:e2,containerNodeRect:eY,dragOverlay:eF,draggableNodes:J,droppableContainers:q,droppableRects:ec,over:e4,measureDroppableContainers:ep,scrollableAncestors:eH,scrollableAncestorRects:eV,measuringConfiguration:es,measuringScheduled:eC,windowRect:eG}),[ee,eR,ej,el,e2,eY,eF,J,q,ec,e4,ep,eH,eV,es,eC,eG]),tn=(0,g.useMemo)(()=>({activatorEvent:el,activators:te,active:ee,activeNodeRect:ej,ariaDescribedById:{draggable:eo},dispatch:N,draggableNodes:J,over:e4,measureDroppableContainers:ep}),[el,te,ee,ej,N,eo,J,e4,ep]);return g.createElement(w.Provider,{value:z},g.createElement(ek.Provider,{value:tn},g.createElement(eO.Provider,{value:tt},g.createElement(eA.Provider,{value:e6},y)),g.createElement(eN,{disabled:(null==m?void 0:m.restoreFocus)===!1})),g.createElement(D,{...m,hiddenTextDescribedById:eo}))}),eP=(0,g.createContext)(null),ej="button";function eY(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:f}=(0,g.useContext)(ek),{role:h=ej,roleDescription:p="draggable",tabIndex:m=0}=null!=l?l:{},b=(null==u?void 0:u.id)===t,y=(0,g.useContext)(b?eA:eP),[w,x]=(0,v.lk)(),[E,D]=(0,v.lk)(),S=(0,g.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),C=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:E,data:C}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,g.useMemo)(()=>({role:h,tabIndex:m,"aria-disabled":r,"aria-pressed":!!b&&h===ej||void 0,"aria-roledescription":p,"aria-describedby":c.draggable}),[r,h,m,b,p,c.draggable]),isDragging:b,listeners:r?void 0:S,node:w,over:f,setNodeRef:x,setActivatorNodeRef:D,transform:y}}function eU(){return(0,g.useContext)(eO)}let eB={timeout:25};function eF(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,g.useContext)(ek),d=(0,g.useRef)({disabled:n}),f=(0,g.useRef)(!1),h=(0,g.useRef)(null),p=(0,g.useRef)(null),{disabled:m,updateMeasurementsFor:b,timeout:y}={...eB,...i},w=(0,v.YN)(null!=b?b:r),x=eg({callback:(0,g.useCallback)(()=>{if(!f.current){f.current=!0;return}null!=p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),p.current=null},y)},[y]),disabled:m||!o}),E=(0,g.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),f.current=!1),e&&x.observe(e))},[x]),[D,S]=(0,v.lk)(E),C=(0,v.YN)(t);return(0,g.useEffect)(()=>{x&&D.current&&(x.disconnect(),f.current=!1,x.observe(D.current))},[D,x]),(0,g.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:D,rect:h,data:C}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,g.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:h,isOver:(null==s?void 0:s.id)===r,node:D,over:s,setNodeRef:S}}function eW(e){let{animation:t,children:n}=e,[r,l]=(0,g.useState)(null),[i,a]=(0,g.useState)(null),o=(0,v.ZC)(n);return n||r||!o||l(o),(0,v.Es)(()=>{if(!i)return;let e=null==r?void 0:r.key,n=null==r?void 0:r.props.id;if(null==e||null==n)return void l(null);Promise.resolve(t(n,i)).then(()=>{l(null)})},[t,r,i]),g.createElement(g.Fragment,null,n,r?(0,g.cloneElement)(r,{ref:a}):null)}let eK={x:0,y:0,scaleX:1,scaleY:1};function e_(e){let{children:t}=e;return g.createElement(ek.Provider,{value:eM},g.createElement(eA.Provider,{value:eK},t))}let eX={position:"fixed",touchAction:"none"},eG=e=>(0,v.kx)(e)?"transform 250ms ease":void 0,eH=(0,g.forwardRef)((e,t)=>{let{as:n,activatorEvent:r,adjustScale:l,children:i,className:a,rect:o,style:u,transform:s,transition:c=eG}=e;if(!o)return null;let d=l?s:{...s,scaleX:1,scaleY:1},f={...eX,width:o.width,height:o.height,top:o.top,left:o.left,transform:v.Ks.Transform.toString(d),transformOrigin:l&&r?function(e,t){let n=(0,v.e_)(e);if(!n)return"0 0";let r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}(r,o):void 0,transition:"function"==typeof c?c(r):c,...u};return g.createElement(n,{className:a,style:f,ref:t},i)}),eV={duration:250,easing:"ease",keyframes:e=>{let{transform:{initial:t,final:n}}=e;return[{transform:v.Ks.Transform.toString(t)},{transform:v.Ks.Transform.toString(n)}]},sideEffects:(r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}})},eJ=0,eZ=g.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:l,transition:i,modifiers:a,wrapperElement:o="div",className:u,zIndex:s=999}=e,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggableNodes:p,droppableContainers:m,dragOverlay:b,over:y,measuringConfiguration:w,scrollableAncestors:x,scrollableAncestorRects:E,windowRect:D}=eU(),S=(0,g.useContext)(eA),C=function(e){return(0,g.useMemo)(()=>{if(null!=e)return++eJ},[e])}(null==d?void 0:d.id),R=eL(a,{activatorEvent:c,active:d,activeNodeRect:f,containerNodeRect:h,draggingNodeRect:b.rect,over:y,overlayNodeRect:b.rect,scrollableAncestors:x,scrollableAncestorRects:E,transform:S,windowRect:D}),M=eh(f),k=function(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:l}=e;return(0,v._q)((e,i)=>{if(null===t)return;let a=n.get(e);if(!a)return;let o=a.node.current;if(!o)return;let u=ex(i);if(!u)return;let{transform:s}=(0,v.zk)(i).getComputedStyle(i),c=N(s);if(!c)return;let d="function"==typeof t?t:function(e){let{duration:t,easing:n,sideEffects:r,keyframes:l}={...eV,...e};return e=>{let{active:i,dragOverlay:a,transform:o,...u}=e;if(!t)return;let s={x:a.rect.left-i.rect.left,y:a.rect.top-i.rect.top},c={scaleX:1!==o.scaleX?i.rect.width*o.scaleX/a.rect.width:1,scaleY:1!==o.scaleY?i.rect.height*o.scaleY/a.rect.height:1},d={x:o.x-s.x,y:o.y-s.y,...c},f=l({...u,active:i,dragOverlay:a,transform:{initial:o,final:d}}),[h]=f,g=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(g))return;let p=null==r?void 0:r({active:i,dragOverlay:a,...u}),v=a.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise(e=>{v.onfinish=()=>{null==p||p(),e()}})}}(t);return G(o,l.draggable.measure),d({active:{id:e,data:a.data,node:o,rect:l.draggable.measure(o)},draggableNodes:n,dragOverlay:{node:i,rect:l.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:l,transform:c})})}({config:r,draggableNodes:p,droppableContainers:m,measuringConfiguration:w}),O=M?b.setRef:void 0;return g.createElement(e_,null,g.createElement(eW,{animation:k},d&&C?g.createElement(eH,{key:C,id:d.id,ref:O,as:o,activatorEvent:c,adjustScale:t,className:u,transition:i,rect:M,style:{zIndex:s,...l},transform:R},n):null))})},50414:(e,t,n)=>{n.d(t,{$$:()=>p,Es:()=>h,KG:()=>m,Ks:()=>M,Ll:()=>o,Re:()=>S,Sw:()=>i,TW:()=>f,WQ:()=>D,YG:()=>x,YN:()=>v,ZC:()=>y,_q:()=>g,ag:()=>O,e_:()=>R,jn:()=>l,kx:()=>C,l6:()=>a,lk:()=>b,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(99004);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function f(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let h=i?r.useLayoutEffect:r.useEffect;function g(e){let t=(0,r.useRef)(e);return h(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function p(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return h(()=>{n.current!==e&&(n.current=e)},t),n}function m(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function b(e){let t=g(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function y(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function E(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let D=E(1),S=E(-1);function C(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function R(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let M=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[M.Translate.toString(e),M.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),k="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function O(e){return e.matches(k)?e:e.querySelector(k)}},71476:(e,t,n)=>{n.d(t,{Zr:()=>l});let r=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>r(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>r(t)(e)}}},l=(e,t)=>(n,l,i)=>{let a,o={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var t;let r=e=>null===e?null:JSON.parse(e,void 0),l=null!=(t=n.getItem(e))?t:null;return l instanceof Promise?l.then(r):r(l)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,s=new Set,c=new Set,d=o.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...e)},l,i);let f=()=>{let e=o.partialize({...l()});return d.setItem(o.name,{state:e,version:o.version})},h=i.setState;i.setState=(e,t)=>{h(e,t),f()};let g=e((...e)=>{n(...e),f()},l,i);i.getInitialState=()=>g;let p=()=>{var e,t;if(!d)return;u=!1,s.forEach(e=>{var t;return e(null!=(t=l())?t:g)});let i=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=l())?e:g))||void 0;return r(d.getItem.bind(d))(o.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];else{if(o.migrate){let t=o.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[r,i]=e;if(n(a=o.merge(i,null!=(t=l())?t:g),!0),r)return f()}).then(()=>{null==i||i(a,void 0),a=l(),u=!0,c.forEach(e=>e(a))}).catch(e=>{null==i||i(void 0,e)})};return i.persist={setOptions:e=>{o={...o,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>p(),hasHydrated:()=>u,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||p(),a||g}}}]);