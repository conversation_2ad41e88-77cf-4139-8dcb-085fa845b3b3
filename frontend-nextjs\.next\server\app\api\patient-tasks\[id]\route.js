try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="06015a0a-a7d3-4be2-bb99-057d434ef279",e._sentryDebugIdIdentifier="sentry-dbid-06015a0a-a7d3-4be2-bb99-057d434ef279")}catch(e){}"use strict";(()=>{var e={};e.id=2118,e.ids=[2118],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57615:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>P,serverHooks:()=>E,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>A});var o={};t.r(o),t.d(o,{DELETE:()=>T,GET:()=>f,HEAD:()=>b,OPTIONS:()=>w,PATCH:()=>m,POST:()=>k,PUT:()=>h});var s=t(86047),i=t(85544),a=t(36135),n=t(63033),d=t(53547),p=t(54360),u=t(19761);let l=(0,d.ZA)(async(e,r,{params:t})=>{try{let r=(0,p.o)(e),o=await r.getPatientTask(t.id);if("doctor"===e.role){let r="object"==typeof o.assignedTo?o.assignedTo.id:o.assignedTo,t="object"==typeof o.createdBy?o.createdBy.id:o.createdBy;if(r!==e.payloadUserId&&t!==e.payloadUserId&&!["treatment-reminder","medical-record-update","consultation-follow-up"].includes(o.taskType))return(0,d.WX)("Permission denied",403)}else if("front-desk"===e.role&&("object"==typeof o.assignedTo?o.assignedTo.id:o.assignedTo)!==e.payloadUserId&&!["follow-up-call","appointment-scheduling","billing-follow-up"].includes(o.taskType))return(0,d.WX)("Permission denied",403);return(0,d.$y)(o)}catch(e){return console.error("Error fetching patient task:",e),(0,d.WX)("Failed to fetch patient task")}}),c=(0,d.ZA)(async(e,r,{params:t})=>{try{let o=(0,p.o)(e),s=await r.json(),i=await o.getPatientTask(t.id),a="object"==typeof i.assignedTo?i.assignedTo.id:i.assignedTo,n="object"==typeof i.createdBy?i.createdBy.id:i.createdBy;if("admin"!==e.role&&a!==e.payloadUserId&&n!==e.payloadUserId)return(0,d.WX)("You can only update tasks assigned to you or created by you",403);delete s.createdBy,"completed"===s.status&&"completed"!==i.status&&(s.completedAt=new Date),"completed"!==s.status&&"completed"===i.status&&(s.completedAt=null,s.completionNotes=null);let u=await o.updatePatientTask(t.id,s);return(0,d.$y)(u)}catch(e){return console.error("Error updating patient task:",e),(0,d.WX)("Failed to update patient task")}}),y=(0,d.ZA)(async(e,r,{params:t})=>{try{if("admin"!==e.role)return(0,d.WX)("Only administrators can delete tasks",403);let r=(0,p.o)(e);return await r.deletePatientTask(t.id),(0,d.$y)({message:"Patient task deleted successfully"})}catch(e){return console.error("Error deleting patient task:",e),(0,d.WX)("Failed to delete patient task")}}),x={...n},q="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;function g(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,o)=>{let s;try{let e=q?.getStore();s=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/patient-tasks/[id]",headers:s}).apply(t,o)}})}let f=g(l,"GET"),k=g(void 0,"POST"),h=g(void 0,"PUT"),m=g(c,"PATCH"),T=g(y,"DELETE"),b=g(void 0,"HEAD"),w=g(void 0,"OPTIONS"),P=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/patient-tasks/[id]/route",pathname:"/api/patient-tasks/[id]",filename:"route",bundlePath:"app/api/patient-tasks/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-tasks\\[id]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:v,workUnitAsyncStorage:A,serverHooks:E}=P;function j(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:A})}},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[55,3738,1950,5886,9615,125],()=>t(57615));module.exports=o})();
//# sourceMappingURL=route.js.map