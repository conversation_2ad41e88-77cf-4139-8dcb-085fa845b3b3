try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="69b03de4-a8f9-43fc-aa97-eb180bbe2d8e",e._sentryDebugIdIdentifier="sentry-dbid-69b03de4-a8f9-43fc-aa97-eb180bbe2d8e")}catch(e){}"use strict";exports.id=4144,exports.ids=[4144],exports.modules={277:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","moon","IconMoon",[["path",{d:"M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z",key:"svg-0"}]])},748:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},1675:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","user-circle","IconUserCircle",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 10m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0",key:"svg-1"}],["path",{d:"M6.168 18.849a4 4 0 0 1 3.832 -2.849h4a4 4 0 0 1 3.834 2.855",key:"svg-2"}]])},2048:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},l=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.useStore=void 0;var u=n(84265),s=a(n(60222)),c=l(n(94560)),d=n(64071),f=n(86292),p=n(45410);t.useStore=function(e){var t=s.useRef(r({animations:{enterMs:200,exitMs:100}},e.options)),n=s.useMemo(function(){return new d.ActionInterface(e.actions||[],{historyManager:t.current.enableHistory?f.history:void 0})},[]),o=s.useState({searchQuery:"",currentRootActionId:null,visualState:p.VisualState.hidden,actions:r({},n.actions),activeIndex:0,disabled:!1}),i=o[0],a=o[1],l=s.useRef(i);l.current=i;var u=s.useCallback(function(){return l.current},[]),v=s.useMemo(function(){return new h(u)},[u]);s.useEffect(function(){l.current=i,v.notify()},[i,v]);var m=s.useCallback(function(e){return a(function(t){return r(r({},t),{actions:n.add(e)})}),function(){a(function(t){return r(r({},t),{actions:n.remove(e)})})}},[n]),g=s.useRef(null);return s.useMemo(function(){return{getState:u,query:{setCurrentRootAction:function(e){a(function(t){return r(r({},t),{currentRootActionId:e})})},setVisualState:function(e){a(function(t){return r(r({},t),{visualState:"function"==typeof e?e(t.visualState):e})})},setSearch:function(e){return a(function(t){return r(r({},t),{searchQuery:e})})},registerActions:m,toggle:function(){return a(function(e){return r(r({},e),{visualState:[p.VisualState.animatingOut,p.VisualState.hidden].includes(e.visualState)?p.VisualState.animatingIn:p.VisualState.animatingOut})})},setActiveIndex:function(e){return a(function(t){return r(r({},t),{activeIndex:"number"==typeof e?e:e(t.activeIndex)})})},inputRefSetter:function(e){g.current=e},getInput:function(){return(0,c.default)(g.current,"Input ref is undefined, make sure you attach `query.inputRefSetter` to your search input."),g.current},disable:function(e){a(function(t){return r(r({},t),{disabled:e})})}},options:t.current,subscribe:function(e,t){return v.subscribe(e,t)}}},[u,v,m])};var h=function(){function e(e){this.subscribers=[],this.getState=e}return e.prototype.subscribe=function(e,t){var n=this,r=new v(function(){return e(n.getState())},t);return this.subscribers.push(r),this.unsubscribe.bind(this,r)},e.prototype.unsubscribe=function(e){if(this.subscribers.length){var t=this.subscribers.indexOf(e);if(t>-1)return this.subscribers.splice(t,1)}},e.prototype.notify=function(){this.subscribers.forEach(function(e){return e.collect()})},e}(),v=function(){function e(e,t){this.collector=e,this.onChange=t}return e.prototype.collect=function(){try{var e=this.collector();!(0,u.deepEqual)(e,this.collected)&&(this.collected=e,this.onChange&&this.onChange(this.collected))}catch(e){console.warn(e)}},e}()},2064:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},s=function(e,t,n,r){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],f=new Set,p=new Set(s),h=function(e){!e||f.has(e)||(f.add(e),h(e.parentNode))};s.forEach(h);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(c.get(e)||0)+1;o.set(e,l),c.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),s(o,i,n,"aria-hidden")):function(){return null}}},2212:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","clock","IconClock",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 7v5l3 3",key:"svg-1"}]])},3431:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","brightness","IconBrightness",[["path",{d:"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0",key:"svg-0"}],["path",{d:"M12 3l0 18",key:"svg-1"}],["path",{d:"M12 9l4.65 -4.65",key:"svg-2"}],["path",{d:"M12 14.3l7.37 -7.37",key:"svg-3"}],["path",{d:"M12 19.6l8.85 -8.85",key:"svg-4"}]])},4150:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),o(n(64071),t),o(n(22440),t)},4684:(e,t,n)=>{n.d(t,{A:()=>a,q:()=>i});var r=n(60222),o=n(24443);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[l]||a,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},4826:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","x","IconX",[["path",{d:"M18 6l-12 12",key:"svg-0"}],["path",{d:"M6 6l12 12",key:"svg-1"}]])},6517:(e,t,n)=>{var r=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,s=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))u.call(e,l)||l===n||o(e,l,{get:()=>t[l],enumerable:!(r=i(t,l))||r.enumerable});return e},c={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(c,{Root:()=>y,Slot:()=>h,Slottable:()=>m}),e.exports=s(o({},"__esModule",{value:!0}),c);var d=((e,t,n)=>(n=null!=e?r(l(e)):{},s(!t&&e&&e.__esModule?n:o(n,"default",{value:e,enumerable:!0}),e)))(n(60222)),f=n(13451),p=n(24443),h=d.forwardRef((e,t)=>{let{children:n,...r}=e,o=d.Children.toArray(n),i=o.find(g);if(i){let e=i.props.children,n=o.map(t=>t!==i?t:d.Children.count(e)>1?d.Children.only(null):d.isValidElement(e)?e.props.children:null);return(0,p.jsx)(v,{...r,ref:t,children:d.isValidElement(e)?d.cloneElement(e,void 0,n):null})}return(0,p.jsx)(v,{...r,ref:t,children:n})});h.displayName="Slot";var v=d.forwardRef((e,t)=>{let{children:n,...r}=e;if(d.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),o=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==d.Fragment&&(o.ref=t?(0,f.composeRefs)(t,e):e),d.cloneElement(n,o)}return d.Children.count(n)>1?d.Children.only(null):null});v.displayName="SlotClone";var m=({children:e})=>(0,p.jsx)(p.Fragment,{children:e});function g(e){return d.isValidElement(e)&&e.type===m}var y=h},6776:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","brand-github","IconBrandGithub",[["path",{d:"M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5",key:"svg-0"}]])},7584:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KBarAnimator=void 0;var l=a(n(60222)),u=n(45410),s=n(30526),c=n(67214),d=[{opacity:0,transform:"scale(.99)"},{opacity:1,transform:"scale(1.01)"},{opacity:1,transform:"scale(1)"}],f=[{transform:"scale(1)"},{transform:"scale(.98)"},{transform:"scale(1)"}];t.KBarAnimator=function(e){var t,n,o=e.children,i=e.style,a=e.className,p=e.disableCloseOnOuterClick,h=(0,s.useKBar)(function(e){return{visualState:e.visualState,currentRootActionId:e.currentRootActionId}}),v=h.visualState,m=h.currentRootActionId,g=h.query,y=h.options,b=l.useRef(null),w=l.useRef(null),x=(null==(t=null==y?void 0:y.animations)?void 0:t.enterMs)||0,M=(null==(n=null==y?void 0:y.animations)?void 0:n.exitMs)||0;l.useEffect(function(){if(v!==u.VisualState.showing){var e=v===u.VisualState.animatingIn?x:M,t=b.current;null==t||t.animate(d,{duration:e,easing:v===u.VisualState.animatingOut?"ease-in":"ease-out",direction:v===u.VisualState.animatingOut?"reverse":"normal",fill:"forwards"})}},[y,v,x,M]);var k=l.useRef();l.useEffect(function(){if(v===u.VisualState.showing){var e=b.current,t=w.current;if(e&&t){var n=new ResizeObserver(function(t){for(var n=0;n<t.length;n++){var r=t[n].contentRect;k.current||(k.current=r.height),e.animate([{height:k.current+"px"},{height:r.height+"px"}],{duration:x/2,easing:"ease-out",fill:"forwards"}),k.current=r.height}});return n.observe(t),function(){n.unobserve(t)}}}},[v,y,x,M]);var C=l.useRef(!0);return l.useEffect(function(){if(C.current){C.current=!1;return}var e=b.current;e&&e.animate(f,{duration:x,easing:"ease-out"})},[m,x]),(0,c.useOuterClick)(b,function(){var e,t;p||(g.setVisualState(u.VisualState.animatingOut),null==(t=null==(e=y.callbacks)?void 0:e.onClose)||t.call(e))}),l.createElement("div",{ref:b,style:r(r(r({},d[0]),i),{pointerEvents:"auto"}),className:a},l.createElement("div",{ref:w},o))}},9286:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(19e3).A)("outline","brand-github","IconBrandGithub",[["path",{d:"M9 19c-4.3 1.4 -4.3 -2.5 -6 -3m12 5v-3.5c0 -1 .1 -1.4 -.5 -2c2.8 -.3 5.5 -1.4 5.5 -6a4.6 4.6 0 0 0 -1.3 -3.2a4.2 4.2 0 0 0 -.1 -3.2s-1.1 -.3 -3.5 1.3a12.3 12.3 0 0 0 -6.2 0c-2.4 -1.6 -3.5 -1.3 -3.5 -1.3a4.2 4.2 0 0 0 -.1 3.2a4.6 4.6 0 0 0 -1.3 3.2c0 4.6 2.7 5.7 5.5 6c-.6 .6 -.6 1.2 -.5 2v3.5",key:"svg-0"}]])},9556:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","dots-vertical","IconDotsVertical",[["path",{d:"M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-0"}],["path",{d:"M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-1"}],["path",{d:"M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0",key:"svg-2"}]])},9719:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(60222);n(24443);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},9752:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","trash","IconTrash",[["path",{d:"M4 7l16 0",key:"svg-0"}],["path",{d:"M10 11l0 6",key:"svg-1"}],["path",{d:"M14 11l0 6",key:"svg-2"}],["path",{d:"M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12",key:"svg-3"}],["path",{d:"M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3",key:"svg-4"}]])},11348:(e,t,n)=>{n.d(t,{b:()=>l});var r=n(60222),o=n(24582),i=n(24443),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a},11473:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},l=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};Object.defineProperty(t,"__esModule",{value:!0}),t.KBarPositioner=void 0;var u=a(n(60222)),s={position:"fixed",display:"flex",alignItems:"flex-start",justifyContent:"center",width:"100%",inset:"0px",padding:"14vh 16px 16px"};t.KBarPositioner=u.forwardRef(function(e,t){var n=e.style,o=e.children,i=l(e,["style","children"]);return u.createElement("div",r({ref:t,style:n?r(r({},s),n):s},i),o)})},12305:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","settings","IconSettings",[["path",{d:"M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z",key:"svg-0"}],["path",{d:"M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0",key:"svg-1"}]])},12741:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","arrow-right","IconArrowRight",[["path",{d:"M5 12l14 0",key:"svg-0"}],["path",{d:"M13 18l6 -6",key:"svg-1"}],["path",{d:"M13 6l6 6",key:"svg-2"}]])},12772:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},12795:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(60222),i=n(12772),a=n(24582),l=n(24368),u=n(88818),s=n(24443),c="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:f,onPointerDownOutside:v,onFocusOutside:m,onInteractOutside:g,onDismiss:y,...b}=e,w=o.useContext(d),[x,M]=o.useState(null),k=x?.ownerDocument??globalThis?.document,[,C]=o.useState({}),E=(0,l.s)(t,e=>M(e)),S=Array.from(w.layers),[O]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),A=S.indexOf(O),j=x?S.indexOf(x):-1,R=w.layersWithOutsidePointerEventsDisabled.size>0,_=j>=A,P=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){h("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));_&&!n&&(v?.(e),g?.(e),e.defaultPrevented||y?.())},k),I=function(e,t=globalThis?.document){let n=(0,u.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&h("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...w.branches].some(e=>e.contains(t))&&(m?.(e),g?.(e),e.defaultPrevented||y?.())},k);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{j===w.layers.size-1&&(f?.(e),!e.defaultPrevented&&y&&(e.preventDefault(),y()))},k),o.useEffect(()=>{if(x)return n&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{n&&1===w.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=r)}},[x,k,n,w]),o.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(a.sG.div,{...b,ref:E,style:{pointerEvents:R?_?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,a.hO)(o,i):o.dispatchEvent(i)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},13451:(e,t,n)=>{var r=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,s=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))u.call(e,l)||l===n||o(e,l,{get:()=>t[l],enumerable:!(r=i(t,l))||r.enumerable});return e},c={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(c,{composeRefs:()=>p,useComposedRefs:()=>h}),e.exports=s(o({},"__esModule",{value:!0}),c);var d=((e,t,n)=>(n=null!=e?r(l(e)):{},s(!t&&e&&e.__esModule?n:o(n,"default",{value:e,enumerable:!0}),e)))(n(60222));function f(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function p(...e){return t=>{let n=!1,r=e.map(e=>{let r=f(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():f(e[t],null)}}}}function h(...e){return d.useCallback(p(...e),e)}},14178:(e,t,n)=>{n.d(t,{Ke:()=>M,R6:()=>w,bL:()=>E});var r=n(60222),o=n(12772),i=n(4684),a=n(36612),l=n(21382),u=n(24368),s=n(24582),c=n(49258),d=n(31354),f=n(24443),p="Collapsible",[h,v]=(0,i.A)(p),[m,g]=h(p),y=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:o,defaultOpen:i,disabled:l,onOpenChange:u,...c}=e,[p=!1,h]=(0,a.i)({prop:o,defaultProp:i,onChange:u});return(0,f.jsx)(m,{scope:n,disabled:l,contentId:(0,d.B)(),open:p,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),children:(0,f.jsx)(s.sG.div,{"data-state":C(p),"data-disabled":l?"":void 0,...c,ref:t})})});y.displayName=p;var b="CollapsibleTrigger",w=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,i=g(b,n);return(0,f.jsx)(s.sG.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":C(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});w.displayName=b;var x="CollapsibleContent",M=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=g(x,e.__scopeCollapsible);return(0,f.jsx)(c.C,{present:n||o.open,children:({present:e})=>(0,f.jsx)(k,{...r,ref:t,present:e})})});M.displayName=x;var k=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:o,children:i,...a}=e,c=g(x,n),[d,p]=r.useState(o),h=r.useRef(null),v=(0,u.s)(t,h),m=r.useRef(0),y=m.current,b=r.useRef(0),w=b.current,M=c.open||d,k=r.useRef(M),E=r.useRef(void 0);return r.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=h.current;if(e){E.current=E.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,b.current=t.width,k.current||(e.style.transitionDuration=E.current.transitionDuration,e.style.animationName=E.current.animationName),p(o)}},[c.open,o]),(0,f.jsx)(s.sG.div,{"data-state":C(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!M,...a,ref:v,style:{"--radix-collapsible-content-height":y?`${y}px`:void 0,"--radix-collapsible-content-width":w?`${w}px`:void 0,...e.style},children:M&&i})});function C(e){return e?"open":"closed"}var E=y},14736:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","device-laptop","IconDeviceLaptop",[["path",{d:"M3 19l18 0",key:"svg-0"}],["path",{d:"M5 6m0 1a1 1 0 0 1 1 -1h12a1 1 0 0 1 1 1v8a1 1 0 0 1 -1 1h-12a1 1 0 0 1 -1 -1z",key:"svg-1"}]])},15355:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","help-circle","IconHelpCircle",[["path",{d:"M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0",key:"svg-0"}],["path",{d:"M12 16v.01",key:"svg-1"}],["path",{d:"M12 13a2 2 0 0 0 .914 -3.782a1.98 1.98 0 0 0 -2.414 .483",key:"svg-2"}]])},15431:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","chevron-right","IconChevronRight",[["path",{d:"M9 6l6 6l-6 6",key:"svg-0"}]])},16701:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","command","IconCommand",[["path",{d:"M7 9a2 2 0 1 1 2 -2v10a2 2 0 1 1 -2 -2h10a2 2 0 1 1 -2 2v-10a2 2 0 1 1 2 2h-10",key:"svg-0"}]])},17838:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","search","IconSearch",[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0",key:"svg-0"}],["path",{d:"M21 21l-6 -6",key:"svg-1"}]])},20422:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},20742:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","photo","IconPhoto",[["path",{d:"M15 8h.01",key:"svg-0"}],["path",{d:"M3 6a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3v-12z",key:"svg-1"}],["path",{d:"M3 16l5 -5c.928 -.893 2.072 -.893 3 0l5 5",key:"svg-2"}],["path",{d:"M14 14l1 -1c.928 -.893 2.072 -.893 3 0l3 3",key:"svg-3"}]])},20866:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]])},21382:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(60222),o=globalThis?.document?r.useLayoutEffect:()=>{}},22031:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","chevron-down","IconChevronDown",[["path",{d:"M6 9l6 6l6 -6",key:"svg-0"}]])},22207:(e,t,n)=>{n.d(t,{Mz:()=>eY,i3:()=>eJ,UC:()=>eZ,bL:()=>eX,Bk:()=>eI});var r=n(60222);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,s=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>d[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function M(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=v(g(t)),u=m(l),s=p(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(s){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[l]-=y*(n&&c?-1:1);break;case"end":r[l]+=y*(n&&c?-1:1)}return r}let k=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),s=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=M(s,r,u),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:v}=l[n],{x:m,y:g,data:y,reset:b}=await v({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:s,platform:a,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(s=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:c,y:d}=M(s,f,u)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),v=w(h),m=l[p?"floating"===d?"reference":"floating":d],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),M=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},k=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:b,strategy:u}):y);return{top:(g.top-k.top+v.top)/M.y,bottom:(k.bottom-g.bottom+v.bottom)/M.y,left:(g.left-k.left+v.left)/M.x,right:(k.right-g.right+v.right)/M.x}}function E(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function S(e){return o.some(t=>e[t]>=0)}async function O(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=h(n),u="y"===g(n),s=["left","top"].includes(a)?-1:1,c=i&&u?-1:1,d=f(t,e),{mainAxis:v,crossAxis:m,alignmentAxis:y}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof y&&(m="end"===l?-1*y:y),u?{x:m*c,y:v*s}:{x:v*s,y:m*c}}function A(){return"undefined"!=typeof window}function j(e){return P(e)?(e.nodeName||"").toLowerCase():"#document"}function R(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function _(e){var t;return null==(t=(P(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function P(e){return!!A()&&(e instanceof Node||e instanceof R(e).Node)}function I(e){return!!A()&&(e instanceof Element||e instanceof R(e).Element)}function D(e){return!!A()&&(e instanceof HTMLElement||e instanceof R(e).HTMLElement)}function L(e){return!!A()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof R(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function T(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=B(),n=I(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function K(e){return["html","body","#document"].includes(j(e))}function z(e){return R(e).getComputedStyle(e)}function W(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function V(e){if("html"===j(e))return e;let t=e.assignedSlot||e.parentNode||L(e)&&e.host||_(e);return L(t)?t.host:t}function $(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=V(t);return K(n)?t.ownerDocument?t.ownerDocument.body:t.body:D(n)&&N(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=R(o);if(i){let e=H(a);return t.concat(a,a.visualViewport||[],N(o)?o:[],e&&n?$(e):[])}return t.concat(o,$(o,[],n))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function G(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=D(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function U(e){return I(e)?e:e.contextElement}function q(e){let t=U(e);if(!D(t))return s(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=G(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let X=s(0);function Y(e){let t=R(e);return B()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=U(e),l=s(1);t&&(r?I(r)&&(l=q(r)):l=q(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===R(a))&&o)?Y(a):s(0),c=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=R(a),t=r&&I(r)?R(r):r,n=e,o=H(n);for(;o&&r&&t!==n;){let e=q(o),t=o.getBoundingClientRect(),r=z(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=a,o=H(n=R(o))}}return x({width:f,height:p,x:c,y:d})}function J(e,t){let n=W(e).scrollLeft;return t?t.left+n:Z(_(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=R(e),r=_(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=B();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=_(e),n=W(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+J(e),u=-n.scrollTop;return"rtl"===z(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(_(e));else if(I(t))r=function(e,t){let n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=D(e)?q(e):s(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=Y(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===z(e).position}function en(e,t){if(!D(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return _(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=R(e);if(T(e))return n;if(!D(e)){let t=V(e);for(;t&&!K(t);){if(I(t)&&!et(t))return t;t=V(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(j(r))&&et(r);)r=en(r,t);return r&&K(r)&&et(r)&&!F(r)?n:r||function(e){let t=V(e);for(;D(t)&&!K(t);){if(F(t))return t;if(T(t))break;t=V(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=D(t),o=_(t),i="fixed"===n,a=Z(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=s(0);if(r||!r&&!i)if(("body"!==j(t)||N(o))&&(l=W(t)),r){let e=Z(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=J(o));let c=!o||r||i?s(0):Q(o,l);return{x:a.left+l.scrollLeft-u.x-c.x,y:a.top+l.scrollTop-u.y-c.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=_(r),l=!!t&&T(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},c=s(1),d=s(0),f=D(r);if((f||!f&&!i)&&(("body"!==j(r)||N(a))&&(u=W(r)),D(r))){let e=Z(r);c=q(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?s(0):Q(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:_,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?T(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=$(e,[],!1).filter(e=>I(e)&&"body"!==j(e)),o=null,i="fixed"===z(e).position,a=i?V(e):e;for(;I(a)&&!K(a);){let t=z(a),n=F(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||N(a)&&!n&&function e(t,n){let r=V(t);return!(r===n||!I(r)||K(r))&&("fixed"===z(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=V(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],s=l.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,u,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=G(e);return{width:t,height:n}},getScale:q,isElement:I,isRTL:function(e){return"rtl"===z(e).direction}};function ea(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let el=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:s,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let y=w(p),b={x:n,y:r},x=v(g(o)),M=m(x),k=await u.getDimensions(d),C="y"===x,E=C?"clientHeight":"clientWidth",S=l.reference[M]+l.reference[x]-b[x]-l.floating[M],O=b[x]-l.reference[x],A=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),j=A?A[E]:0;j&&await (null==u.isElement?void 0:u.isElement(A))||(j=s.floating[E]||l.floating[M]);let R=j/2-k[M]/2-1,_=i(y[C?"top":"left"],R),P=i(y[C?"bottom":"right"],R),I=j-k[M]-P,D=j/2-k[M]/2+(S/2-O/2),L=a(_,i(D,I)),N=!c.arrow&&null!=h(o)&&D!==L&&l.reference[M]/2-(D<_?_:P)-k[M]/2<0,T=N?D<_?D-_:D-I:0;return{[x]:b[x]+T,data:{[x]:L,centerOffset:D-L-T,...N&&{alignmentOffset:T}},reset:N}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return k(e,t,{...o,platform:i})};var es=n(89859),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ef(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let ev=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?el({element:n.current,padding:r}).fn(t):{}:n?el({element:n,padding:r}).fn(t):{}}}),em=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await O(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eg=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await C(t,c),m=g(p(o)),y=v(m),b=d[y],w=d[m];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}let x=s.fn({...t,[y]:b,[m]:w});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[y]:l,[m]:u}}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:s=!0}=f(e,t),c={x:n,y:r},d=g(o),h=v(d),m=c[h],y=c[d],b=f(l,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+w.mainAxis,n=i.reference[h]+i.reference[e]-w.mainAxis;m<t?m=t:m>n&&(m=n)}if(s){var x,M;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:w.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(M=a.offset)?void 0:M[d])||0)-(t?w.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:m,[d]:y}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:s,initialPlacement:c,platform:d,elements:w}=t,{mainAxis:x=!0,crossAxis:M=!0,fallbackPlacements:k,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:O=!0,...A}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let j=p(l),R=g(c),_=p(c)===c,P=await (null==d.isRTL?void 0:d.isRTL(w.floating)),I=k||(_||!O?[b(c)]:function(e){let t=b(e);return[y(e),t,y(t)]}(c)),D="none"!==S;!k&&D&&I.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(c,O,S,P));let L=[c,...I],N=await C(t,A),T=[],F=(null==(r=u.flip)?void 0:r.overflows)||[];if(x&&T.push(N[j]),M){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(g(e)),i=m(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=b(a)),[a,b(a)]}(l,s,P);T.push(N[e[0]],N[e[1]])}if(F=[...F,{placement:l,overflows:T}],!T.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=L[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(E){case"bestFit":{let e=null==(a=F.filter(e=>{if(D){let t=g(e.placement);return t===R||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:s,platform:c,elements:d}=t,{apply:v=()=>{},...m}=f(e,t),y=await C(t,m),b=p(u),w=h(u),x="y"===g(u),{width:M,height:k}=s.floating;"top"===b||"bottom"===b?(o=b,l=w===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(l=b,o="end"===w?"top":"bottom");let E=k-y.top-y.bottom,S=M-y.left-y.right,O=i(k-y[o],E),A=i(M-y[l],S),j=!t.middlewareData.shift,R=O,_=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(_=S),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=E),j&&!w){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?_=M-2*(0!==e||0!==t?e+t:a(y.left,y.right)):R=k-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await v({...t,availableWidth:_,availableHeight:R});let P=await c.getDimensions(d.floating);return M!==P.width||k!==P.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=E(await C(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:S(e)}}}case"escaped":{let e=E(await C(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:S(e)}}}default:return{}}}}}(e),options:[e,t]}),eM=(e,t)=>({...ev(e),options:[e,t]});var ek=n(24582),eC=n(24443),eE=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eC.jsx)(ek.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eE.displayName="Arrow";var eS=n(24368),eO=n(4684),eA=n(88818),ej=n(21382),eR=n(42354),e_="Popper",[eP,eI]=(0,eO.A)(e_),[eD,eL]=eP(e_),eN=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eC.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eN.displayName=e_;var eT="PopperAnchor",eF=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eL(eT,n),l=r.useRef(null),u=(0,eS.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eC.jsx)(ek.sG.div,{...i,ref:u})});eF.displayName=eT;var eB="PopperContent",[eK,ez]=eP(eB),eW=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:l=0,align:s="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:v="partial",hideWhenDetached:m=!1,updatePositionStrategy:g="optimized",onPlaced:y,...b}=e,w=eL(eB,n),[x,M]=r.useState(null),k=(0,eS.s)(t,e=>M(e)),[C,E]=r.useState(null),S=(0,eR.X)(C),O=S?.width??0,A=S?.height??0,j="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},R=Array.isArray(p)?p:[p],P=R.length>0,I={padding:j,boundary:R.filter(eG),altBoundary:P},{refs:D,floatingStyles:L,placement:N,isPositioned:T,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);let[v,m]=r.useState(null),[g,y]=r.useState(null),b=r.useCallback(e=>{e!==k.current&&(k.current=e,m(e))},[]),w=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=a||v,M=l||g,k=r.useRef(null),C=r.useRef(null),E=r.useRef(d),S=null!=s,O=eh(s),A=eh(i),j=eh(c),R=r.useCallback(()=>{if(!k.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};A.current&&(e.platform=A.current),eu(k.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};_.current&&!ed(E.current,t)&&(E.current=t,es.flushSync(()=>{f(t)}))})},[p,t,n,A,j]);ec(()=>{!1===c&&E.current.isPositioned&&(E.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let _=r.useRef(!1);ec(()=>(_.current=!0,()=>{_.current=!1}),[]),ec(()=>{if(x&&(k.current=x),M&&(C.current=M),x&&M){if(O.current)return O.current(x,M,R);R()}},[x,M,R,O,S]);let P=r.useMemo(()=>({reference:k,floating:C,setReference:b,setFloating:w}),[b,w]),I=r.useMemo(()=>({reference:x,floating:M}),[x,M]),D=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!I.floating)return e;let t=ep(I.floating,d.x),r=ep(I.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(I.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,I.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:R,refs:P,elements:I,floatingStyles:D}),[d,R,P,I,D])}({strategy:"fixed",placement:o+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=U(e),h=l||s?[...p?$(p):[],...$(t)]:[];h.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,r=null,o=_(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:h,width:v,height:m}=f;if(c||t(),!v||!m)return;let g=u(h),y=u(o.clientWidth-(p+v)),b={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(h+m))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!w)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ea(f,e.getBoundingClientRect())||s(),w=!1}try{r=new IntersectionObserver(x,{...b,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,b)}r.observe(e)}(!0),l}(p,n):null,m=-1,g=null;c&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?Z(e):null;return f&&function t(){let r=Z(e);y&&!ea(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{l&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:w.anchor},middleware:[em({mainAxis:l+A,alignmentAxis:c}),f&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===v?ey():void 0,...I}),f&&eb({...I}),ew({...I,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&eM({element:C,padding:d}),eU({arrowWidth:O,arrowHeight:A}),m&&ex({strategy:"referenceHidden",...I})]}),[B,K]=eq(N),z=(0,eA.c)(y);(0,ej.N)(()=>{T&&z?.()},[T,z]);let W=F.arrow?.x,V=F.arrow?.y,H=F.arrow?.centerOffset!==0,[G,q]=r.useState();return(0,ej.N)(()=>{x&&q(window.getComputedStyle(x).zIndex)},[x]),(0,eC.jsx)("div",{ref:D.setFloating,"data-radix-popper-content-wrapper":"",style:{...L,transform:T?L.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:G,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eK,{scope:n,placedSide:B,onArrowChange:E,arrowX:W,arrowY:V,shouldHideArrow:H,children:(0,eC.jsx)(ek.sG.div,{"data-side":B,"data-align":K,...b,ref:k,style:{...b.style,animation:T?void 0:"none"}})})})});eW.displayName=eB;var eV="PopperArrow",e$={top:"bottom",right:"left",bottom:"top",left:"right"},eH=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eV,n),i=e$[o.placedSide];return(0,eC.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eE,{...r,ref:t,style:{...r.style,display:"block"}})})});function eG(e){return null!==e}eH.displayName=eV;var eU=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,s]=eq(n),c={start:"0%",center:"50%",end:"100%"}[s],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===u?(p=i?c:`${d}px`,h=`${-l}px`):"top"===u?(p=i?c:`${d}px`,h=`${r.floating.height+l}px`):"right"===u?(p=`${-l}px`,h=i?c:`${f}px`):"left"===u&&(p=`${r.floating.width+l}px`,h=i?c:`${f}px`),{data:{x:p,y:h}}}});function eq(e){let[t,n="center"]=e.split("-");return[t,n]}var eX=eN,eY=eF,eZ=eW,eJ=eH},22440:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ActionImpl=void 0;var o=r(n(94560)),i=n(81587),a=n(67214),l=function(e){var t=e.keywords,n=e.section,r=void 0===n?"":n;return((void 0===t?"":t)+" "+("string"==typeof r?r:r.name)).trim()};t.ActionImpl=function(){function e(e,t){var n,r=this;this.priority=a.Priority.NORMAL,this.ancestors=[],this.children=[],Object.assign(this,e),this.id=e.id,this.name=e.name,this.keywords=l(e);var u=e.perform;if(this.command=u&&new i.Command({perform:function(){return u(r)}},{history:t.history}),this.perform=null==(n=this.command)?void 0:n.perform,e.parent){var s=t.store[e.parent];(0,o.default)(s,"attempted to create an action whos parent: "+e.parent+" does not exist in the store."),s.addChild(this)}}return e.prototype.addChild=function(e){e.ancestors.unshift(this);for(var t=this.parentActionImpl;t;)e.ancestors.unshift(t),t=t.parentActionImpl;this.children.push(e)},e.prototype.removeChild=function(e){var t=this,n=this.children.indexOf(e);-1!==n&&this.children.splice(n,1),e.children&&e.children.forEach(function(e){t.removeChild(e)})},Object.defineProperty(e.prototype,"parentActionImpl",{get:function(){return this.ancestors[this.ancestors.length-1]},enumerable:!1,configurable:!0}),e.create=function(t,n){return new e(t,n)},e}()},22750:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.useRegisterActions=void 0;var a=i(n(60222)),l=n(30526);t.useRegisterActions=function(e,t){void 0===t&&(t=[]);var n=(0,l.useKBar)().query,r=a.useMemo(function(){return e},t);a.useEffect(function(){if(r.length){var e=n.registerActions(r);return function(){e()}}},[n,r])}},24582:(e,t,n)=>{n.d(t,{hO:()=>u,sG:()=>l});var r=n(60222),o=n(89859),i=n(16586),a=n(24443),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,l=r?i.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(l,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},24658:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","users","IconUsers",[["path",{d:"M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"svg-2"}],["path",{d:"M21 21v-2a4 4 0 0 0 -3 -3.85",key:"svg-3"}]])},25804:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","sun","IconSun",[["path",{d:"M12 12m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0",key:"svg-0"}],["path",{d:"M3 12h1m8 -9v1m8 8h1m-9 8v1m-6.4 -15.4l.7 .7m12.1 -.7l-.7 .7m0 11.4l.7 .7m-12.1 -.7l-.7 .7",key:"svg-1"}]])},25816:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","shopping-bag","IconShoppingBag",[["path",{d:"M6.331 8h11.339a2 2 0 0 1 1.977 2.304l-1.255 8.152a3 3 0 0 1 -2.966 2.544h-6.852a3 3 0 0 1 -2.965 -2.544l-1.255 -8.152a2 2 0 0 1 1.977 -2.304z",key:"svg-0"}],["path",{d:"M9 11v-5a3 3 0 0 1 6 0v5",key:"svg-1"}]])},27926:(e,t,n)=>{n.d(t,{N:()=>u});var r=n(60222),o=n(4684),i=n(24368),a=n(16586),l=n(24443);function u(e){let t=e+"CollectionProvider",[n,u]=(0,o.A)(t),[s,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(s,{scope:t,itemMap:i,collectionRef:o,children:n})};d.displayName=t;let f=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=c(f,n),u=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(a.DX,{ref:u,children:r})});p.displayName=f;let h=e+"CollectionItemSlot",v="data-radix-collection-item",m=r.forwardRef((e,t)=>{let{scope:n,children:o,...u}=e,s=r.useRef(null),d=(0,i.s)(t,s),f=c(h,n);return r.useEffect(()=>(f.itemMap.set(s,{ref:s,...u}),()=>void f.itemMap.delete(s))),(0,l.jsx)(a.DX,{...{[v]:""},ref:d,children:o})});return m.displayName=h,[{Provider:d,Slot:p,ItemSlot:m},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},30413:(e,t,n)=>{n.d(t,{b:()=>l,s:()=>a});var r=n(60222),o=n(24582),i=n(24443),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var l=a},30526:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.useKBar=void 0;var l=a(n(60222)),u=n(71575);t.useKBar=function(e){var t=l.useContext(u.KBarContext),n=t.query,o=t.getState,i=t.subscribe,a=t.options,s=l.useRef(null==e?void 0:e(o())),c=l.useRef(e),d=l.useCallback(function(e){return r(r({},e),{query:n,options:a})},[n,a]),f=l.useState(d(s.current)),p=f[0],h=f[1];return l.useEffect(function(){var e;return c.current&&(e=i(function(e){return c.current(e)},function(e){return h(d(e))})),function(){e&&e()}},[d,i]),p}},30992:(e,t,n)=>{n.d(t,{A:()=>H});var r,o,i=n(57707),a=n(60222),l="right-scroll-bar-position",u="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=f),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,i.__assign)({async:!0,ssr:!1},e),a}(),h=function(){},v=a.forwardRef(function(e,t){var n,r,o,l,u=a.useRef(null),f=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=f[0],m=f[1],g=e.forwardProps,y=e.children,b=e.className,w=e.removeScrollBar,x=e.enabled,M=e.shards,k=e.sideCar,C=e.noIsolation,E=e.inert,S=e.allowPinchZoom,O=e.as,A=e.gapMode,j=(0,i.__rest)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(n=[u,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,l=o.facade,c(function(){var e=d.get(l);if(e){var t=new Set(e),r=new Set(n),o=l.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}d.set(l,n)},[n]),l),_=(0,i.__assign)((0,i.__assign)({},j),v);return a.createElement(a.Fragment,null,x&&a.createElement(k,{sideCar:p,removeScrollBar:w,shards:M,noIsolation:C,inert:E,setCallbacks:m,allowPinchZoom:!!S,lockRef:u,gapMode:A}),g?a.cloneElement(a.Children.only(y),(0,i.__assign)((0,i.__assign)({},_),{ref:R})):a.createElement(void 0===O?"div":O,(0,i.__assign)({},_,{className:b,ref:R}),y))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:u,zeroRight:l};var m=function(e){var t=e.sideCar,n=(0,i.__rest)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,(0,i.__assign)({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},M=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=M(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=b(),E="data-scroll-locked",S=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},A=function(){a.useEffect(function(){return document.body.setAttribute(E,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},j=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;A();var i=a.useMemo(function(){return k(o)},[o]);return a.createElement(C,{styles:S(i,!t,o,n?"":"!important")})},R=!1;if("undefined"!=typeof window)try{var _=Object.defineProperty({},"passive",{get:function(){return R=!0,!0}});window.addEventListener("test",_,_),window.removeEventListener("test",_,_)}catch(e){R=!1}var P=!!R&&{passive:!1},I=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},D=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),L(e,r)){var o=N(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},L=function(e,t){return"v"===e?I(t,"overflowY"):I(t,"overflowX")},N=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},T=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,s=t.contains(u),c=!1,d=l>0,f=0,p=0;do{var h=N(e,u),v=h[0],m=h[1]-h[2]-a*v;(v||m)&&L(e,u)&&(f+=m,p+=v),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},K=function(e){return e&&"current"in e?e.current:e},z=0,W=[];let V=(r=function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(z++)[0],l=a.useState(b)[0],u=a.useRef(e);a.useEffect(function(){u.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,i.__spreadArray)([e.lockRef.current],(e.shards||[]).map(K),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,i=F(e),a=n.current,l="deltaX"in e?e.deltaX:a[0]-i[0],s="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(l)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=D(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=D(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||s)&&(r.current=o),!o)return!0;var p=r.current||o;return T(p,t,e,"h"===p?l:s,!0)},[]),c=a.useCallback(function(e){if(W.length&&W[W.length-1]===l){var n="deltaY"in e?B(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(K).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),p=a.useCallback(function(t){d(t.type,B(t),t.target,s(t,e.lockRef.current))},[]),h=a.useCallback(function(t){d(t.type,F(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return W.push(l),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",c,P),document.addEventListener("touchmove",c,P),document.addEventListener("touchstart",f,P),function(){W=W.filter(function(e){return e!==l}),document.removeEventListener("wheel",c,P),document.removeEventListener("touchmove",c,P),document.removeEventListener("touchstart",f,P)}},[]);var v=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?a.createElement(j,{gapMode:e.gapMode}):null)},p.useMedium(r),m);var $=a.forwardRef(function(e,t){return a.createElement(v,(0,i.__assign)({},e,{ref:t,sideCar:V}))});$.classNames=v.classNames;let H=$},31354:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(60222),i=n(21382),a=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},32259:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.Priority=t.createAction=void 0;var i=n(67214);Object.defineProperty(t,"createAction",{enumerable:!0,get:function(){return i.createAction}}),Object.defineProperty(t,"Priority",{enumerable:!0,get:function(){return i.Priority}}),o(n(72295),t),o(n(37283),t),o(n(11473),t),o(n(94477),t),o(n(36995),t),o(n(30526),t),o(n(22750),t),o(n(71575),t),o(n(7584),t),o(n(45410),t),o(n(4150),t)},33005:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","bell","IconBell",[["path",{d:"M10 5a2 2 0 1 1 4 0a7 7 0 0 1 4 6v3a4 4 0 0 0 2 3h-16a4 4 0 0 0 2 -3v-3a7 7 0 0 1 4 -6",key:"svg-0"}],["path",{d:"M9 17v1a3 3 0 0 0 6 0v-1",key:"svg-1"}]])},33078:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","arrow-left","IconArrowLeft",[["path",{d:"M5 12l14 0",key:"svg-0"}],["path",{d:"M5 12l6 6",key:"svg-1"}],["path",{d:"M5 12l6 -6",key:"svg-2"}]])},33520:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","filter","IconFilter",[["path",{d:"M4 4h16v2.172a2 2 0 0 1 -.586 1.414l-4.414 4.414v7l-6 2v-8.5l-4.48 -4.928a2 2 0 0 1 -.52 -1.345v-2.227z",key:"svg-0"}]])},36612:(e,t,n)=>{n.d(t,{i:()=>i});var r=n(60222),o=n(88818);function i({prop:e,defaultProp:t,onChange:n=()=>{}}){let[i,a]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[i]=n,a=r.useRef(i),l=(0,o.c)(t);return r.useEffect(()=>{a.current!==i&&(l(i),a.current=i)},[i,a,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,u=l?e:i,s=(0,o.c)(n);return[u,r.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&s(n)}else a(t)},[l,e,a,s])]}},36995:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KBarResults=void 0;var l=a(n(60222)),u=n(64779),s=n(94477),c=n(30526),d=n(67214);t.KBarResults=function(e){var t=l.useRef(null),n=l.useRef(null),o=l.useRef(e.items);o.current=e.items;var i=(0,u.useVirtual)({size:o.current.length,parentRef:n}),a=(0,c.useKBar)(function(e){return{search:e.searchQuery,currentRootActionId:e.currentRootActionId,activeIndex:e.activeIndex}}),f=a.query,p=a.search,h=a.currentRootActionId,v=a.activeIndex,m=a.options;l.useEffect(function(){var e=function(e){var n;e.isComposing||("ArrowUp"===e.key||e.ctrlKey&&"p"===e.key?(e.preventDefault(),e.stopPropagation(),f.setActiveIndex(function(e){var t=e>0?e-1:e;if("string"==typeof o.current[t]){if(0===t)return e;t-=1}return t})):"ArrowDown"===e.key||e.ctrlKey&&"n"===e.key?(e.preventDefault(),e.stopPropagation(),f.setActiveIndex(function(e){var t=e<o.current.length-1?e+1:e;if("string"==typeof o.current[t]){if(t===o.current.length-1)return e;t+=1}return t})):"Enter"===e.key&&(e.preventDefault(),e.stopPropagation(),null==(n=t.current)||n.click()))};return window.addEventListener("keydown",e,{capture:!0}),function(){return window.removeEventListener("keydown",e,{capture:!0})}},[f]);var g=i.scrollToIndex;l.useEffect(function(){g(v,{align:v<=1?"end":"auto"})},[v,g]),l.useEffect(function(){f.setActiveIndex(+("string"==typeof e.items[0]))},[p,h,e.items,f]);var y=l.useCallback(function(e){var t,n;"string"!=typeof e&&(e.command?(e.command.perform(e),f.toggle()):(f.setSearch(""),f.setCurrentRootAction(e.id)),null==(n=null==(t=m.callbacks)?void 0:t.onSelectAction)||n.call(t,e))},[f,m]),b=(0,d.usePointerMovedSinceMount)();return l.createElement("div",{ref:n,style:{maxHeight:e.maxHeight||400,position:"relative",overflow:"auto"}},l.createElement("div",{role:"listbox",id:s.KBAR_LISTBOX,style:{height:i.totalSize+"px",width:"100%"}},i.virtualItems.map(function(n){var i=o.current[n.index],a="string"!=typeof i&&{onPointerMove:function(){return b&&v!==n.index&&f.setActiveIndex(n.index)},onPointerDown:function(){return f.setActiveIndex(n.index)},onClick:function(){return y(i)}},u=n.index===v;return l.createElement("div",r({ref:u?t:null,id:(0,s.getListboxItemId)(n.index),role:"option","aria-selected":u,key:n.index,style:{position:"absolute",top:0,left:0,width:"100%",transform:"translateY("+n.start+"px)"}},a),l.cloneElement(e.onRender({item:i,active:u}),{ref:n.measureRef}))})))}},37283:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KBarPortal=void 0;var a=n(87140),l=i(n(60222)),u=n(45410),s=n(30526);t.KBarPortal=function(e){var t=e.children,n=e.container;return(0,s.useKBar)(function(e){return{showing:e.visualState!==u.VisualState.hidden}}).showing?l.createElement(a.Portal,{container:n},t):null}},37375:(e,t,n)=>{n.d(t,{q:()=>r});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},41245:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","photo-up","IconPhotoUp",[["path",{d:"M15 8h.01",key:"svg-0"}],["path",{d:"M12.5 21h-6.5a3 3 0 0 1 -3 -3v-12a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v6.5",key:"svg-1"}],["path",{d:"M3 16l5 -5c.928 -.893 2.072 -.893 3 0l3.5 3.5",key:"svg-2"}],["path",{d:"M14 14l1 -1c.679 -.653 1.473 -.829 2.214 -.526",key:"svg-3"}],["path",{d:"M19 22v-6",key:"svg-4"}],["path",{d:"M22 19l-3 -3l-3 3",key:"svg-5"}]])},42354:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(60222),o=n(21382);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},42363:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","loader-2","IconLoader2",[["path",{d:"M12 3a9 9 0 1 0 9 9",key:"svg-0"}]])},43839:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","user-edit","IconUserEdit",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h3.5",key:"svg-1"}],["path",{d:"M18.42 15.61a2.1 2.1 0 0 1 2.97 2.97l-3.39 3.42h-3v-3l3.42 -3.39z",key:"svg-2"}]])},44537:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","file-text","IconFileText",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}],["path",{d:"M9 9l1 0",key:"svg-2"}],["path",{d:"M9 13l6 0",key:"svg-3"}],["path",{d:"M9 17l6 0",key:"svg-4"}]])},45410:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VisualState=void 0,function(e){e.animatingIn="animating-in",e.showing="showing",e.animatingOut="animating-out",e.hidden="hidden"}(t.VisualState||(t.VisualState={}))},46244:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(60222),o={outline:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},filled:{xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor",stroke:"none"}};let i=(e,t,n,i)=>{let a=(0,r.forwardRef)(({color:n="currentColor",size:a=24,stroke:l=2,title:u,className:s,children:c,...d},f)=>(0,r.createElement)("svg",{ref:f,...o[e],width:a,height:a,className:["tabler-icon",`tabler-icon-${t}`,s].join(" "),..."filled"===e?{fill:n}:{strokeWidth:l,stroke:n},...d},[u&&(0,r.createElement)("title",{key:"svg-title"},u),...i.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return a.displayName=`${n}`,a}},46643:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","user","IconUser",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2",key:"svg-1"}]])},47145:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","eye","IconEye",[["path",{d:"M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0",key:"svg-0"}],["path",{d:"M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6",key:"svg-1"}]])},48199:(e,t,n)=>{n.d(t,{H4:()=>x,_V:()=>w,bL:()=>b});var r=n(60222),o=n(4684),i=n(88818),a=n(21382),l=n(24582),u=n(24443),s="Avatar",[c,d]=(0,o.A)(s),[f,p]=c(s),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,u.jsx)(f,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,u.jsx)(l.sG.span,{...o,ref:t})})});h.displayName=s;var v="AvatarImage",m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:s=()=>{},...c}=e,d=p(v,n),f=function(e,t){let[n,o]=r.useState("idle");return(0,a.N)(()=>{if(!e)return void o("error");let n=!0,r=new window.Image,i=e=>()=>{n&&o(e)};return o("loading"),r.onload=i("loaded"),r.onerror=i("error"),r.src=e,t&&(r.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(o,c.referrerPolicy),h=(0,i.c)(e=>{s(e),d.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,u.jsx)(l.sG.img,{...c,ref:t,src:o}):null});m.displayName=v;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=p(g,n),[s,c]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==a.imageLoadingStatus?(0,u.jsx)(l.sG.span,{...i,ref:t}):null});y.displayName=g;var b=h,w=m,x=y},48353:(e,t,n)=>{n.d(t,{b:()=>s});var r=n(60222),o=n(24582),i=n(24443),a="horizontal",l=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=a,...s}=e,c=(n=u,l.includes(n))?u:a;return(0,i.jsx)(o.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...s,ref:t})});u.displayName="Separator";var s=u},49230:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","phone","IconPhone",[["path",{d:"M5 4h4l2 5l-2.5 1.5a11 11 0 0 0 5 5l1.5 -2.5l5 2v4a2 2 0 0 1 -2 2a16 16 0 0 1 -15 -15a2 2 0 0 1 2 -2",key:"svg-0"}]])},49258:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(60222),o=n(24368),i=n(21382),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e,t=o.ownerDocument.defaultView??window,n=n=>{let r=l(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!s.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return e?.animationName||"none"}a.displayName="Presence"},49958:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","edit","IconEdit",[["path",{d:"M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1",key:"svg-0"}],["path",{d:"M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z",key:"svg-1"}],["path",{d:"M16 5l3 3",key:"svg-2"}]])},50628:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","layout-dashboard","IconLayoutDashboard",[["path",{d:"M5 4h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1",key:"svg-0"}],["path",{d:"M5 16h4a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-2a1 1 0 0 1 1 -1",key:"svg-1"}],["path",{d:"M15 12h4a1 1 0 0 1 1 1v6a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-6a1 1 0 0 1 1 -1",key:"svg-2"}],["path",{d:"M15 4h4a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1v-2a1 1 0 0 1 1 -1",key:"svg-3"}]])},51524:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","logout","IconLogout",[["path",{d:"M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M9 12h12l-3 -3",key:"svg-1"}],["path",{d:"M18 15l3 -3",key:"svg-2"}]])},53503:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","credit-card","IconCreditCard",[["path",{d:"M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z",key:"svg-0"}],["path",{d:"M3 10l18 0",key:"svg-1"}],["path",{d:"M7 15l.01 0",key:"svg-2"}],["path",{d:"M11 15l2 0",key:"svg-3"}]])},58246:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","trending-up","IconTrendingUp",[["path",{d:"M3 17l6 -6l4 4l8 -8",key:"svg-0"}],["path",{d:"M14 7l7 0l0 7",key:"svg-1"}]])},59757:(e,t,n)=>{var r=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,s=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))u.call(e,l)||l===n||o(e,l,{get:()=>t[l],enumerable:!(r=i(t,l))||r.enumerable});return e},c={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(c,{useLayoutEffect:()=>f}),e.exports=s(o({},"__esModule",{value:!0}),c);var d=((e,t,n)=>(n=null!=e?r(l(e)):{},s(!t&&e&&e.__esModule?n:o(n,"default",{value:e,enumerable:!0}),e)))(n(60222)),f=globalThis?.document?d.useLayoutEffect:()=>{}},59844:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","file","IconFile",[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4",key:"svg-0"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z",key:"svg-1"}]])},60079:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.InternalEvents=void 0;var l=i(n(60222)),u=a(n(91953)),s=n(45410),c=n(30526),d=n(67214);t.InternalEvents=function(){var e,t,n,r,o,i,a,p,h,v,m,g,y,b,w,x,M,k,C,E,S,O,A,j;return r=(n=(0,c.useKBar)(function(e){return{visualState:e.visualState,showing:e.visualState!==s.VisualState.hidden,disabled:e.disabled}})).query,o=n.options,i=n.visualState,a=n.showing,p=n.disabled,l.useEffect(function(){var e,t=function(){r.setVisualState(function(e){return e===s.VisualState.hidden||e===s.VisualState.animatingOut?e:s.VisualState.animatingOut})};if(p)return void t();var n=o.toggleShortcut||"$mod+k",i=(0,u.default)(window,((e={})[n]=function(e){var t,n,i,l;e.defaultPrevented||(e.preventDefault(),r.toggle(),a?null==(n=null==(t=o.callbacks)?void 0:t.onClose)||n.call(t):null==(l=null==(i=o.callbacks)?void 0:i.onOpen)||l.call(i))},e.Escape=function(e){var n,r;a&&(e.stopPropagation(),e.preventDefault(),null==(r=null==(n=o.callbacks)?void 0:n.onClose)||r.call(n)),t()},e));return function(){i()}},[o.callbacks,o.toggleShortcut,r,a,p]),h=l.useRef(),v=l.useCallback(function(e){var t,n,i=0;e===s.VisualState.animatingIn&&(i=(null==(t=o.animations)?void 0:t.enterMs)||0),e===s.VisualState.animatingOut&&(i=(null==(n=o.animations)?void 0:n.exitMs)||0),clearTimeout(h.current),h.current=setTimeout(function(){var t=!1;r.setVisualState(function(){var n=e===s.VisualState.animatingIn?s.VisualState.showing:s.VisualState.hidden;return n===s.VisualState.hidden&&(t=!0),n}),t&&r.setCurrentRootAction(null)},i)},[null==(e=o.animations)?void 0:e.enterMs,null==(t=o.animations)?void 0:t.exitMs,r]),l.useEffect(function(){switch(i){case s.VisualState.animatingIn:case s.VisualState.animatingOut:v(i)}},[v,i]),g=(m=(0,c.useKBar)(function(e){return{visualState:e.visualState}})).visualState,y=m.options,l.useEffect(function(){if(!y.disableDocumentLock)if(g===s.VisualState.animatingIn){if(document.body.style.overflow="hidden",!y.disableScrollbarManagement){var e=(0,d.getScrollbarWidth)(),t=getComputedStyle(document.body)["margin-right"];t&&(e+=Number(t.replace(/\D/g,""))),document.body.style.marginRight=e+"px"}}else g===s.VisualState.hidden&&(document.body.style.removeProperty("overflow"),y.disableScrollbarManagement||document.body.style.removeProperty("margin-right"))},[y.disableDocumentLock,y.disableScrollbarManagement,g]),w=(b=(0,c.useKBar)(function(e){return{actions:e.actions,open:e.visualState===s.VisualState.showing,disabled:e.disabled}})).actions,x=b.query,M=b.open,k=b.options,C=b.disabled,l.useEffect(function(){if(!M&&!C){for(var e,t=Object.keys(w).map(function(e){return w[e]}),n=[],r=0;r<t.length;r++){var o=t[r];(null==(e=o.shortcut)?void 0:e.length)&&n.push(o)}n=n.sort(function(e,t){return t.shortcut.join(" ").length-e.shortcut.join(" ").length});for(var i={},a=function(e){var t;i[e.shortcut.join(" ")]=(t=function(t){var n,r,o,i,a,l;(0,d.shouldRejectKeystrokes)()||(t.preventDefault(),(null==(n=e.children)?void 0:n.length)?(x.setCurrentRootAction(e.id),x.toggle(),null==(o=null==(r=k.callbacks)?void 0:r.onOpen)||o.call(r)):(null==(i=e.command)||i.perform(),null==(l=null==(a=k.callbacks)?void 0:a.onSelectAction)||l.call(a,e)))},function(e){f.has(e)||(t(e),f.add(e))})},l=0,s=n;l<s.length;l++){var o=s[l];a(o)}var c=(0,u.default)(window,i,{timeout:400});return function(){c()}}},[w,M,k.callbacks,x,C]),E=l.useRef(!0),O=(S=(0,c.useKBar)(function(e){return{isShowing:e.visualState===s.VisualState.showing||e.visualState===s.VisualState.animatingIn}})).isShowing,A=S.query,j=l.useRef(null),l.useEffect(function(){if(E.current){E.current=!1;return}if(O){j.current=document.activeElement;return}var e=document.activeElement;(null==e?void 0:e.tagName.toLowerCase())==="input"&&e.blur();var t=j.current;t&&t!==e&&t.focus()},[O]),l.useEffect(function(){function e(e){var t=A.getInput();e.target!==t&&t.focus()}if(O)return window.addEventListener("keydown",e),function(){window.removeEventListener("keydown",e)}},[O,A]),null};var f=new WeakSet},60642:(e,t,n)=>{n.d(t,{H_:()=>e0,UC:()=>eY,YJ:()=>eZ,q7:()=>eQ,VF:()=>e1,JU:()=>eJ,ZL:()=>eX,bL:()=>eU,wv:()=>e2,l9:()=>eq});var r=n(60222),o=n(12772),i=n(24368),a=n(4684),l=n(36612),u=n(24582),s=n(27926),c=n(9719),d=n(12795),f=n(88860),p=n(71663),h=n(31354),v=n(22207),m=n(84629),g=n(49258),y=n(76653),b=n(16586),w=n(88818),x=n(2064),M=n(30992),k=n(24443),C=["Enter"," "],E=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...E],O={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},j="Menu",[R,_,P]=(0,s.N)(j),[I,D]=(0,a.A)(j,[P,v.Bk,y.RG]),L=(0,v.Bk)(),N=(0,y.RG)(),[T,F]=I(j),[B,K]=I(j),z=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=L(t),[s,d]=r.useState(null),f=r.useRef(!1),p=(0,w.c)(a),h=(0,c.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,k.jsx)(v.bL,{...u,children:(0,k.jsx)(T,{scope:t,open:n,onOpenChange:p,content:s,onContentChange:d,children:(0,k.jsx)(B,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:l,children:o})})})};z.displayName=j;var W=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=L(n);return(0,k.jsx)(v.Mz,{...o,...r,ref:t})});W.displayName="MenuAnchor";var V="MenuPortal",[$,H]=I(V,{forceMount:void 0}),G=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=F(V,t);return(0,k.jsx)($,{scope:t,forceMount:n,children:(0,k.jsx)(g.C,{present:n||i.open,children:(0,k.jsx)(m.Z,{asChild:!0,container:o,children:r})})})};G.displayName=V;var U="MenuContent",[q,X]=I(U),Y=r.forwardRef((e,t)=>{let n=H(U,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=F(U,e.__scopeMenu),a=K(U,e.__scopeMenu);return(0,k.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:r||i.open,children:(0,k.jsx)(R.Slot,{scope:e.__scopeMenu,children:a.modal?(0,k.jsx)(Z,{...o,ref:t}):(0,k.jsx)(J,{...o,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=F(U,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,k.jsx)(Q,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=F(U,e.__scopeMenu);return(0,k.jsx)(Q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:h,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:C,disableOutsideScroll:O,...A}=e,j=F(U,n),R=K(U,n),P=L(n),I=N(n),D=_(n),[T,B]=r.useState(null),z=r.useRef(null),W=(0,i.s)(t,z,j.onContentChange),V=r.useRef(0),$=r.useRef(""),H=r.useRef(0),G=r.useRef(null),X=r.useRef("right"),Y=r.useRef(0),Z=O?M.A:r.Fragment,J=O?{as:b.DX,allowPinchZoom:!0}:void 0,Q=e=>{let t=$.current+e,n=D().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){$.current=t,window.clearTimeout(V.current),""!==t&&(V.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.Oh)();let ee=r.useCallback(e=>X.current===G.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,u=t[i].x,s=t[i].y;l>r!=s>r&&n<(u-a)*(r-l)/(s-l)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,G.current?.area),[]);return(0,k.jsx)(q,{scope:n,searchRef:$,onItemEnter:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:r.useCallback(e=>{ee(e)||(z.current?.focus(),B(null))},[ee]),onTriggerLeave:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:H,onPointerGraceIntentChange:r.useCallback(e=>{G.current=e},[]),children:(0,k.jsx)(Z,{...J,children:(0,k.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{e.preventDefault(),z.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,k.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:x,onDismiss:C,children:(0,k.jsx)(y.bL,{asChild:!0,...I,dir:R.dir,orientation:"vertical",loop:a,currentTabStopId:T,onCurrentTabStopIdChange:B,onEntryFocus:(0,o.m)(h,e=>{R.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eE(j.open),"data-radix-menu-content":"",dir:R.dir,...P,...A,ref:W,style:{outline:"none",...A.style},onKeyDown:(0,o.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Q(e.key));let o=z.current;if(e.target!==o||!S.includes(e.key))return;e.preventDefault();let i=D().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),$.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eA(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(X.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Y.displayName=U;var ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,k.jsx)(u.sG.div,{role:"group",...r,ref:t})});ee.displayName="MenuGroup";var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,k.jsx)(u.sG.div,{...r,ref:t})});et.displayName="MenuLabel";var en="MenuItem",er="menu.itemSelect",eo=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,s=r.useRef(null),c=K(en,e.__scopeMenu),d=X(en,e.__scopeMenu),f=(0,i.s)(t,s),p=r.useRef(!1);return(0,k.jsx)(ei,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let t=new CustomEvent(er,{bubbles:!0,cancelable:!0});e.addEventListener(er,e=>a?.(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...s}=e,c=X(en,n),d=N(n),f=r.useRef(null),p=(0,i.s)(t,f),[h,v]=r.useState(!1),[m,g]=r.useState("");return r.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[s.children]),(0,k.jsx)(R.ItemSlot,{scope:n,disabled:a,textValue:l??m,children:(0,k.jsx)(y.q7,{asChild:!0,...d,focusable:!a,children:(0,k.jsx)(u.sG.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eA(e=>{a?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eA(e=>c.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,k.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,k.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eS(n)?"mixed":n,...i,ref:t,"data-state":eO(n),onSelect:(0,o.m)(i.onSelect,()=>r?.(!!eS(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[eu,es]=I(el,{value:void 0,onValueChange:()=>{}}),ec=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,w.c)(r);return(0,k.jsx)(eu,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,k.jsx)(ee,{...o,ref:t})})});ec.displayName=el;var ed="MenuRadioItem",ef=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=es(ed,e.__scopeMenu),a=n===i.value;return(0,k.jsx)(eh,{scope:e.__scopeMenu,checked:a,children:(0,k.jsx)(eo,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eO(a),onSelect:(0,o.m)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ef.displayName=ed;var ep="MenuItemIndicator",[eh,ev]=I(ep,{checked:!1}),em=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=ev(ep,n);return(0,k.jsx)(g.C,{present:r||eS(i.checked)||!0===i.checked,children:(0,k.jsx)(u.sG.span,{...o,ref:t,"data-state":eO(i.checked)})})});em.displayName=ep;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,k.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=L(n);return(0,k.jsx)(v.i3,{...o,...r,ref:t})});ey.displayName="MenuArrow";var[eb,ew]=I("MenuSub"),ex="MenuSubTrigger",eM=r.forwardRef((e,t)=>{let n=F(ex,e.__scopeMenu),a=K(ex,e.__scopeMenu),l=ew(ex,e.__scopeMenu),u=X(ex,e.__scopeMenu),s=r.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),d(null)}},[c,d]),(0,k.jsx)(W,{asChild:!0,...f,children:(0,k.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eE(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eA(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eA(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;e.disabled||r&&" "===t.key||O[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eM.displayName=ex;var ek="MenuSubContent",eC=r.forwardRef((e,t)=>{let n=H(U,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=F(U,e.__scopeMenu),s=K(U,e.__scopeMenu),c=ew(ek,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,k.jsx)(R.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.C,{present:a||u.open,children:(0,k.jsx)(R.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(Q,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{s.isUsingKeyboardRef.current&&d.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=A[s.dir].includes(e.key);t&&n&&(u.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eE(e){return e?"open":"closed"}function eS(e){return"indeterminate"===e}function eO(e){return eS(e)?"indeterminate":e?"checked":"unchecked"}function eA(e){return t=>"mouse"===t.pointerType?e(t):void 0}eC.displayName=ek;var ej="DropdownMenu",[eR,e_]=(0,a.A)(ej,[D]),eP=D(),[eI,eD]=eR(ej),eL=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:s=!0}=e,c=eP(t),d=r.useRef(null),[f=!1,p]=(0,l.i)({prop:i,defaultProp:a,onChange:u});return(0,k.jsx)(eI,{scope:t,triggerId:(0,h.B)(),triggerRef:d,contentId:(0,h.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,k.jsx)(z,{...c,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};eL.displayName=ej;var eN="DropdownMenuTrigger",eT=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eD(eN,n),s=eP(n);return(0,k.jsx)(W,{asChild:!0,...s,children:(0,k.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eT.displayName=eN;var eF=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eP(t);return(0,k.jsx)(G,{...r,...n})};eF.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eD(eB,n),l=eP(n),u=r.useRef(!1);return(0,k.jsx)(Y,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{u.current||a.triggerRef.current?.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eK.displayName=eB;var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(ee,{...o,...r,ref:t})});ez.displayName="DropdownMenuGroup";var eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(et,{...o,...r,ref:t})});eW.displayName="DropdownMenuLabel";var eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(eo,{...o,...r,ref:t})});eV.displayName="DropdownMenuItem";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(ea,{...o,...r,ref:t})});e$.displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(ec,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(ef,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(em,{...o,...r,ref:t})});eH.displayName="DropdownMenuItemIndicator";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(eg,{...o,...r,ref:t})});eG.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(eM,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eP(n);return(0,k.jsx)(eC,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eU=eL,eq=eT,eX=eF,eY=eK,eZ=ez,eJ=eW,eQ=eV,e0=e$,e1=eH,e2=eG},61770:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(60222);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:l="",children:u,iconNode:s,...c},d)=>(0,r.createElement)("svg",{ref:d,...a,width:t,height:t,stroke:e,strokeWidth:o?24*Number(n)/Number(t):n,className:i("lucide",l),...c},[...s.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(u)?u:[u]])),u=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...a},u)=>(0,r.createElement)(l,{ref:u,iconNode:t,className:i(`lucide-${o(e)}`,n),...a}));return n.displayName=`${e}`,n}},63299:(e,t,n)=>{n.d(t,{In:()=>eP,JU:()=>eT,LM:()=>eL,PP:()=>ez,UC:()=>eD,VF:()=>eK,WT:()=>e_,YJ:()=>eN,ZL:()=>eI,bL:()=>ej,l9:()=>eR,p4:()=>eB,q7:()=>eF,wn:()=>eW,wv:()=>eV});var r=n(60222),o=n(89859),i=n(37375),a=n(12772),l=n(27926),u=n(24368),s=n(4684),c=n(9719),d=n(12795),f=n(88860),p=n(71663),h=n(31354),v=n(22207),m=n(84629),g=n(24582),y=n(16586),b=n(88818),w=n(36612),x=n(21382),M=n(87483),k=n(30413),C=n(2064),E=n(30992),S=n(24443),O=[" ","Enter","ArrowUp","ArrowDown"],A=[" ","Enter"],j="Select",[R,_,P]=(0,l.N)(j),[I,D]=(0,s.A)(j,[P,v.Bk]),L=(0,v.Bk)(),[N,T]=I(j),[F,B]=I(j),K=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:a,value:l,defaultValue:u,onValueChange:s,dir:d,name:f,autoComplete:p,disabled:m,required:g,form:y}=e,b=L(t),[x,M]=r.useState(null),[k,C]=r.useState(null),[E,O]=r.useState(!1),A=(0,c.jH)(d),[j=!1,_]=(0,w.i)({prop:o,defaultProp:i,onChange:a}),[P,I]=(0,w.i)({prop:l,defaultProp:u,onChange:s}),D=r.useRef(null),T=!x||y||!!x.closest("form"),[B,K]=r.useState(new Set),z=Array.from(B).map(e=>e.props.value).join(";");return(0,S.jsx)(v.bL,{...b,children:(0,S.jsxs)(N,{required:g,scope:t,trigger:x,onTriggerChange:M,valueNode:k,onValueNodeChange:C,valueNodeHasChildren:E,onValueNodeHasChildrenChange:O,contentId:(0,h.B)(),value:P,onValueChange:I,open:j,onOpenChange:_,dir:A,triggerPointerDownPosRef:D,disabled:m,children:[(0,S.jsx)(R.Provider,{scope:t,children:(0,S.jsx)(F,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{K(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{K(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),T?(0,S.jsxs)(eS,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:P,onChange:e=>I(e.target.value),disabled:m,form:y,children:[void 0===P?(0,S.jsx)("option",{value:""}):null,Array.from(B)]},z):null]})})};K.displayName=j;var z="SelectTrigger",W=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,l=L(n),s=T(z,n),c=s.disabled||o,d=(0,u.s)(t,s.onTriggerChange),f=_(n),p=r.useRef("touch"),[h,m,y]=eO(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=eA(t,e,n);void 0!==r&&s.onValueChange(r.value)}),b=e=>{c||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(v.Mz,{asChild:!0,...l,children:(0,S.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eE(s.value)?"":void 0,...i,ref:d,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&b(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(b(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&O.includes(e.key)&&(b(),e.preventDefault())})})})});W.displayName=z;var V="SelectValue",$=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...l}=e,s=T(V,n),{onValueNodeHasChildrenChange:c}=s,d=void 0!==i,f=(0,u.s)(t,s.onValueNodeChange);return(0,x.N)(()=>{c(d)},[c,d]),(0,S.jsx)(g.sG.span,{...l,ref:f,style:{pointerEvents:"none"},children:eE(s.value)?(0,S.jsx)(S.Fragment,{children:a}):i})});$.displayName=V;var H=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});H.displayName="SelectIcon";var G=e=>(0,S.jsx)(m.Z,{asChild:!0,...e});G.displayName="SelectPortal";var U="SelectContent",q=r.forwardRef((e,t)=>{let n=T(U,e.__scopeSelect),[i,a]=r.useState();return((0,x.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,S.jsx)(Z,{...e,ref:t}):i?o.createPortal((0,S.jsx)(X,{scope:e.__scopeSelect,children:(0,S.jsx)(R.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),i):null});q.displayName=U;var[X,Y]=I(U),Z=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:s,side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:b,collisionPadding:w,sticky:x,hideWhenDetached:M,avoidCollisions:k,...O}=e,A=T(U,n),[j,R]=r.useState(null),[P,I]=r.useState(null),D=(0,u.s)(t,e=>R(e)),[L,N]=r.useState(null),[F,B]=r.useState(null),K=_(n),[z,W]=r.useState(!1),V=r.useRef(!1);r.useEffect(()=>{if(j)return(0,C.Eq)(j)},[j]),(0,f.Oh)();let $=r.useCallback(e=>{let[t,...n]=K().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),n?.focus(),document.activeElement!==o))return},[K,P]),H=r.useCallback(()=>$([L,j]),[$,L,j]);r.useEffect(()=>{z&&H()},[z,H]);let{onOpenChange:G,triggerPointerDownPosRef:q}=A;r.useEffect(()=>{if(j){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():j.contains(n.target)||G(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[j,G,q]),r.useEffect(()=>{let e=()=>G(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[G]);let[Y,Z]=eO(e=>{let t=K().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eA(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{let r=!V.current&&!n;(void 0!==A.value&&A.value===t||r)&&(N(e),r&&(V.current=!0))},[A.value]),et=r.useCallback(()=>j?.focus(),[j]),en=r.useCallback((e,t,n)=>{let r=!V.current&&!n;(void 0!==A.value&&A.value===t||r)&&B(e)},[A.value]),er="popper"===o?Q:J,eo=er===Q?{side:c,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:b,collisionPadding:w,sticky:x,hideWhenDetached:M,avoidCollisions:k}:{};return(0,S.jsx)(X,{scope:n,content:j,viewport:P,onViewportChange:I,itemRefCallback:ee,selectedItem:L,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:H,selectedItemText:F,position:o,isPositioned:z,searchRef:Y,children:(0,S.jsx)(E.A,{as:y.DX,allowPinchZoom:!0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{A.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...O,...eo,onPlaced:()=>W(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...O.style},onKeyDown:(0,a.m)(O.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=K().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>$(t)),e.preventDefault()}})})})})})})});Z.displayName="SelectContentImpl";var J=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...a}=e,l=T(U,n),s=Y(U,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,u.s)(t,e=>p(e)),v=_(n),m=r.useRef(!1),y=r.useRef(!0),{viewport:b,selectedItem:w,selectedItemText:M,focusSelectedItem:k}=s,C=r.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&b&&w&&M){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=M.getBoundingClientRect();if("rtl"!==l.dir){let o=r.left-t.left,a=n.left-o,l=e.left-a,u=e.width+l,s=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(a,[10,Math.max(10,d-s)]);c.style.minWidth=u+"px",c.style.left=f+"px"}else{let o=t.right-r.right,a=window.innerWidth-n.right-o,l=window.innerWidth-e.right-a,u=e.width+l,s=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(a,[10,Math.max(10,d-s)]);c.style.minWidth=u+"px",c.style.right=f+"px"}let a=v(),u=window.innerHeight-20,s=b.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),y=p+h+s+parseInt(d.paddingBottom,10)+g,x=Math.min(5*w.offsetHeight,y),k=window.getComputedStyle(b),C=parseInt(k.paddingTop,10),E=parseInt(k.paddingBottom,10),S=e.top+e.height/2-10,O=w.offsetHeight/2,A=p+h+(w.offsetTop+O);if(A<=S){let e=a.length>0&&w===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(u-S,O+(e?E:0)+(f.clientHeight-b.offsetTop-b.offsetHeight)+g);c.style.height=A+t+"px"}else{let e=a.length>0&&w===a[0].ref.current;c.style.top="0px";let t=Math.max(S,p+b.offsetTop+(e?C:0)+O);c.style.height=t+(y-A)+"px",b.scrollTop=A-S+b.offsetTop}c.style.margin="10px 0",c.style.minHeight=x+"px",c.style.maxHeight=u+"px",o?.(),requestAnimationFrame(()=>m.current=!0)}},[v,l.trigger,l.valueNode,c,f,b,w,M,l.dir,o]);(0,x.N)(()=>C(),[C]);let[E,O]=r.useState();(0,x.N)(()=>{f&&O(window.getComputedStyle(f).zIndex)},[f]);let A=r.useCallback(e=>{e&&!0===y.current&&(C(),k?.(),y.current=!1)},[C,k]);return(0,S.jsx)(ee,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:m,onScrollButtonChange:A,children:(0,S.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,S.jsx)(g.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});J.displayName="SelectItemAlignedPosition";var Q=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,a=L(n);return(0,S.jsx)(v.UC,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Q.displayName="SelectPopperPosition";var[ee,et]=I(U,{}),en="SelectViewport",er=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,l=Y(en,n),s=et(en,n),c=(0,u.s)(t,l.onViewportChange),d=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(R.Slot,{scope:n,children:(0,S.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});er.displayName=en;var eo="SelectGroup",[ei,ea]=I(eo),el=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,S.jsx)(ei,{scope:n,id:o,children:(0,S.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});el.displayName=eo;var eu="SelectLabel",es=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=ea(eu,n);return(0,S.jsx)(g.sG.div,{id:o.id,...r,ref:t})});es.displayName=eu;var ec="SelectItem",[ed,ef]=I(ec),ep=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:l,...s}=e,c=T(ec,n),d=Y(ec,n),f=c.value===o,[p,v]=r.useState(l??""),[m,y]=r.useState(!1),b=(0,u.s)(t,e=>d.itemRefCallback?.(e,o,i)),w=(0,h.B)(),x=r.useRef("touch"),M=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ed,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,S.jsx)(R.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,S.jsx)(g.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:b,onFocus:(0,a.m)(s.onFocus,()=>y(!0)),onBlur:(0,a.m)(s.onBlur,()=>y(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==x.current&&M()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===x.current&&M()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{x.current=e.pointerType,i?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(A.includes(e.key)&&M()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ec;var eh="SelectItemText",ev=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...l}=e,s=T(eh,n),c=Y(eh,n),d=ef(eh,n),f=B(eh,n),[p,h]=r.useState(null),v=(0,u.s)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),m=p?.textContent,y=r.useMemo(()=>(0,S.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:b,onNativeOptionRemove:w}=f;return(0,x.N)(()=>(b(y),()=>w(y)),[b,w,y]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(g.sG.span,{id:d.textId,...l,ref:v}),d.isSelected&&s.valueNode&&!s.valueNodeHasChildren?o.createPortal(l.children,s.valueNode):null]})});ev.displayName=eh;var em="SelectItemIndicator",eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ef(em,n).isSelected?(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eg.displayName=em;var ey="SelectScrollUpButton",eb=r.forwardRef((e,t)=>{let n=Y(ey,e.__scopeSelect),o=et(ey,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,u.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eM,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eb.displayName=ey;var ew="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{let n=Y(ew,e.__scopeSelect),o=et(ew,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,u.s)(t,o.onScrollButtonChange);return(0,x.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eM,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ew;var eM=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,l=Y("SelectScrollButton",n),u=r.useRef(null),s=_(n),c=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,x.N)(()=>{let e=s().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[s]),(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{l.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{c()})})}),ek=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})});ek.displayName="SelectSeparator";var eC="SelectArrow";function eE(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=L(n),i=T(eC,n),a=Y(eC,n);return i.open&&"popper"===a.position?(0,S.jsx)(v.i3,{...o,...r,ref:t}):null}).displayName=eC;var eS=r.forwardRef((e,t)=>{let{value:n,...o}=e,i=r.useRef(null),a=(0,u.s)(t,i),l=(0,M.Z)(n);return r.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[l,n]),(0,S.jsx)(k.s,{asChild:!0,children:(0,S.jsx)("select",{...o,ref:a,defaultValue:n})})});function eO(e){let t=(0,b.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,a]}function eA(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=e,o=Math.max(a,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}eS.displayName="BubbleSelect";var ej=K,eR=W,e_=$,eP=H,eI=G,eD=q,eL=er,eN=el,eT=es,eF=ep,eB=ev,eK=eg,ez=eb,eW=ex,eV=ek},64071:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ActionInterface=void 0;var i=o(n(94560)),a=n(22440);t.ActionInterface=function(){function e(e,t){void 0===e&&(e=[]),void 0===t&&(t={}),this.actions={},this.options=t,this.add(e)}return e.prototype.add=function(e){for(var t=0;t<e.length;t++){var n=e[t];n.parent&&(0,i.default)(this.actions[n.parent],'Attempted to create action "'+n.name+'" without registering its parent "'+n.parent+'" first.'),this.actions[n.id]=a.ActionImpl.create(n,{history:this.options.historyManager,store:this.actions})}return r({},this.actions)},e.prototype.remove=function(e){var t=this;return e.forEach(function(e){var n=t.actions[e.id];if(n){for(var r=n.children;r.length;){var o=r.pop();if(!o)return;delete t.actions[o.id],o.parentActionImpl&&o.parentActionImpl.removeChild(o),o.children&&r.push.apply(r,o.children)}n.parentActionImpl&&n.parentActionImpl.removeChild(n),delete t.actions[e.id]}}),r({},this.actions)},e}()},64779:(e,t,n)=>{n.r(t),n.d(t,{defaultRangeExtractor:()=>h,useVirtual:()=>v});var r,o=n(60222);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var a=["bottom","height","left","right","top","width"],l=new Map,u=function e(){var t=[];l.forEach(function(e,n){var r,o,i=n.getBoundingClientRect();r=i,o=e.rect,void 0===r&&(r={}),void 0===o&&(o={}),a.some(function(e){return r[e]!==o[e]})&&(e.rect=i,t.push(e))}),t.forEach(function(e){e.callbacks.forEach(function(t){return t(e.rect)})}),r=window.requestAnimationFrame(e)},s="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;function c(e,t){var n=t.rect;return e.height!==n.height||e.width!==n.width?n:e}var d=function(){return 50},f=function(e){return e},p=function(e,t){return e[t?"offsetWidth":"offsetHeight"]},h=function(e){for(var t=Math.max(e.start-e.overscan,0),n=Math.min(e.end+e.overscan,e.size-1),r=[],o=t;o<=n;o++)r.push(o);return r};function v(e){var t,n=e.size,a=void 0===n?0:n,v=e.estimateSize,g=void 0===v?d:v,y=e.overscan,b=void 0===y?1:y,w=e.paddingStart,x=void 0===w?0:w,M=e.paddingEnd,k=e.parentRef,C=e.horizontal,E=e.scrollToFn,S=e.useObserver,O=e.initialRect,A=e.onScrollElement,j=e.scrollOffsetFn,R=e.keyExtractor,_=void 0===R?f:R,P=e.measureSize,I=void 0===P?p:P,D=e.rangeExtractor,L=void 0===D?h:D,N=C?"width":"height",T=C?"scrollLeft":"scrollTop",F=o.useRef({scrollOffset:0,measurements:[]}),B=o.useState(0),K=B[0],z=B[1];F.current.scrollOffset=K;var W=(S||function(e,t){void 0===t&&(t={width:0,height:0});var n=o.useState(e.current),i=n[0],a=n[1],d=o.useReducer(c,t),f=d[0],p=d[1],h=o.useRef(!1);return s(function(){e.current!==i&&a(e.current)}),s(function(){i&&!h.current&&(h.current=!0,p({rect:i.getBoundingClientRect()}))},[i]),o.useEffect(function(){if(i){var e,t=(e=function(e){p({rect:e})},{observe:function(){var t=0===l.size;l.has(i)?l.get(i).callbacks.push(e):l.set(i,{rect:void 0,hasRectChanged:!1,callbacks:[e]}),t&&u()},unobserve:function(){var t=l.get(i);if(t){var n=t.callbacks.indexOf(e);n>=0&&t.callbacks.splice(n,1),t.callbacks.length||l.delete(i),l.size||cancelAnimationFrame(r)}}});return t.observe(),function(){t.unobserve()}}},[i]),f})(k,O)[N];F.current.outerSize=W;var V=o.useCallback(function(e){k.current&&(k.current[T]=e)},[k,T]),$=E||V;E=o.useCallback(function(e){$(e,V)},[V,$]);var H=o.useState({}),G=H[0],U=H[1],q=o.useCallback(function(){return U({})},[]),X=o.useRef([]),Y=o.useMemo(function(){var e=X.current.length>0?Math.min.apply(Math,X.current):0;X.current=[];for(var t=F.current.measurements.slice(0,e),n=e;n<a;n++){var r=_(n),o=G[r],i=t[n-1]?t[n-1].end:x,l="number"==typeof o?o:g(n),u=i+l;t[n]={index:n,start:i,size:l,end:u,key:r}}return t},[g,G,x,a,_]),Z=((null==(t=Y[a-1])?void 0:t.end)||x)+(void 0===M?0:M);F.current.measurements=Y,F.current.totalSize=Z;var J=A?A.current:k.current,Q=o.useRef(j);Q.current=j,s(function(){if(!J)return void z(0);var e=function(e){z(Q.current?Q.current(e):J[T])};return e(),J.addEventListener("scroll",e,{capture:!1,passive:!0}),function(){J.removeEventListener("scroll",e)}},[J,T]);var ee=function(e){for(var t=e.measurements,n=e.outerSize,r=e.scrollOffset,o=t.length-1,i=m(0,o,function(e){return t[e].start},r),a=i;a<o&&t[a].end<r+n;)a++;return{start:i,end:a}}(F.current),et=ee.start,en=ee.end,er=o.useMemo(function(){return L({start:et,end:en,overscan:b,size:Y.length})},[et,en,b,Y.length,L]),eo=o.useRef(I);eo.current=I;var ei=o.useMemo(function(){for(var e=[],t=0,n=er.length;t<n;t++)!function(t,n){var r=er[t],o=Y[r],a=i(i({},o),{},{measureRef:function(e){if(e){var t=eo.current(e,C);if(t!==a.size){var n=F.current.scrollOffset;a.start<n&&V(n+(t-a.size)),X.current.push(r),U(function(e){var n;return i(i({},e),{},((n={})[a.key]=t,n))})}}}});e.push(a)}(t);return e},[er,V,C,Y]),ea=o.useRef(!1);s(function(){ea.current&&U({}),ea.current=!0},[g]);var el=o.useCallback(function(e,t){var n=(void 0===t?{}:t).align,r=void 0===n?"start":n,o=F.current,i=o.scrollOffset,a=o.outerSize;"auto"===r&&(r=e<=i?"start":e>=i+a?"end":"start"),"start"===r?E(e):"end"===r?E(e-a):"center"===r&&E(e-a/2)},[E]),eu=o.useCallback(function(e,t){var n=void 0===t?{}:t,r=n.align,o=void 0===r?"auto":r,l=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)t.indexOf(n=i[r])>=0||(o[n]=e[n]);return o}(n,["align"]),u=F.current,s=u.measurements,c=u.scrollOffset,d=u.outerSize,f=s[Math.max(0,Math.min(e,a-1))];if(f){if("auto"===o)if(f.end>=c+d)o="end";else{if(!(f.start<=c))return;o="start"}el("center"===o?f.start+f.size/2:"end"===o?f.end:f.start,i({align:o},l))}},[el,a]);return{virtualItems:ei,totalSize:Z,scrollToOffset:el,scrollToIndex:o.useCallback(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];eu.apply(void 0,t),requestAnimationFrame(function(){eu.apply(void 0,t)})},[eu]),measure:q}}var m=function(e,t,n,r){for(;e<=t;){var o=(e+t)/2|0,i=n(o);if(i<r)e=o+1;else{if(!(i>r))return o;t=o-1}}return e>0?e-1:0}},66125:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","slash","IconSlash",[["path",{d:"M17 5l-10 14",key:"svg-0"}]])},67214:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},l=this&&this.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.Priority=t.isModKey=t.shouldRejectKeystrokes=t.useThrottledValue=t.getScrollbarWidth=t.useIsomorphicLayout=t.noop=t.createAction=t.randomId=t.usePointerMovedSinceMount=t.useOuterClick=t.swallowEvent=void 0;var u=a(n(60222));function s(){return Math.random().toString(36).substring(2,9)}function c(){}t.swallowEvent=function(e){e.stopPropagation(),e.preventDefault()},t.useOuterClick=function(e,t){var n=u.useRef(t);n.current=t,u.useEffect(function(){function t(t){var r,o;null!=(r=e.current)&&r.contains(t.target)||t.target===(null==(o=e.current)?void 0:o.getRootNode().host)||(t.preventDefault(),t.stopPropagation(),n.current())}return window.addEventListener("pointerdown",t,!0),function(){return window.removeEventListener("pointerdown",t,!0)}},[e])},t.usePointerMovedSinceMount=function(){var e=u.useState(!1),t=e[0],n=e[1];return u.useEffect(function(){function e(){n(!0)}if(!t)return window.addEventListener("pointermove",e),function(){return window.removeEventListener("pointermove",e)}},[t]),t},t.randomId=s,t.createAction=function(e){return r({id:s()},e)},t.noop=c,t.useIsomorphicLayout="undefined"==typeof window?c:u.useLayoutEffect,t.getScrollbarWidth=function(){var e=document.createElement("div");e.style.visibility="hidden",e.style.overflow="scroll",document.body.appendChild(e);var t=document.createElement("div");e.appendChild(t);var n=e.offsetWidth-t.offsetWidth;return e.parentNode.removeChild(e),n},t.useThrottledValue=function(e,t){void 0===t&&(t=100);var n=u.useState(e),r=n[0],o=n[1],i=u.useRef(Date.now());return u.useEffect(function(){if(0!==t){var n=setTimeout(function(){o(e),i.current=Date.now()},i.current-(Date.now()-t));return function(){clearTimeout(n)}}},[t,e]),0===t?e:r},t.shouldRejectKeystrokes=function(e){var t,n,r,o=l(["input","textarea"],(void 0===e?{ignoreWhenFocused:[]}:e).ignoreWhenFocused,!0).map(function(e){return e.toLowerCase()}),i=document.activeElement;return i&&(-1!==o.indexOf(i.tagName.toLowerCase())||(null==(t=i.attributes.getNamedItem("role"))?void 0:t.value)==="textbox"||(null==(n=i.attributes.getNamedItem("contenteditable"))?void 0:n.value)==="true"||(null==(r=i.attributes.getNamedItem("contenteditable"))?void 0:r.value)==="plaintext-only")};var d="undefined"!=typeof window&&"MacIntel"===window.navigator.platform;t.isModKey=function(e){return d?e.metaKey:e.ctrlKey},t.Priority={HIGH:1,NORMAL:0,LOW:-1}},68343:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","mail","IconMail",[["path",{d:"M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z",key:"svg-0"}],["path",{d:"M3 7l9 6l9 -6",key:"svg-1"}]])},69322:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","check","IconCheck",[["path",{d:"M5 12l5 5l10 -10",key:"svg-0"}]])},71575:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.KBarProvider=t.KBarContext=void 0;var a=n(2048),l=i(n(60222)),u=n(60079);t.KBarContext=l.createContext({}),t.KBarProvider=function(e){var n=(0,a.useStore)(e);return l.createElement(t.KBarContext.Provider,{value:n},l.createElement(u.InternalEvents,null),e.children)}},71663:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(60222),o=n(24368),i=n(24582),a=n(88818),l=n(24443),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[b,w]=r.useState(null),x=(0,a.c)(m),M=(0,a.c)(g),k=r.useRef(null),C=(0,o.s)(t,e=>w(e)),E=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(E.paused||!b)return;let t=e.target;b.contains(t)?k.current=t:h(k.current,{select:!0})},t=function(e){if(E.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||h(k.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,b,E.paused]),r.useEffect(()=>{if(b){v.add(E);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(u,c);b.addEventListener(u,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(b))}return()=>{b.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(s,c);b.addEventListener(s,M),b.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),b.removeEventListener(s,M),v.remove(E)},0)}}},[b,x,M,E]);let S=r.useCallback(e=>{if(!n&&!d||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,E.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:S})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=m(e,t)).unshift(t)},remove(t){e=m(e,t),e[0]?.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},71672:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","chevron-left","IconChevronLeft",[["path",{d:"M15 6l-6 6l6 6",key:"svg-0"}]])},72295:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.useDeepMatches=t.useMatches=t.NO_GROUP=void 0;var l=i(n(60222)),u=n(30526),s=n(67214),c=a(n(88138));t.NO_GROUP={name:"none",priority:s.Priority.NORMAL};var d={keys:[{name:"name",weight:.5},{name:"keywords",getFn:function(e){var t;return(null!=(t=e.keywords)?t:"").split(",")},weight:.5},"subtitle"],ignoreLocation:!0,includeScore:!0,includeMatches:!0,threshold:.2,minMatchCharLength:1};function f(e,t){return t.priority-e.priority}function p(){var e,n,r,o,i,a,p,h=(0,u.useKBar)(function(e){return{search:e.searchQuery,actions:e.actions,rootActionId:e.currentRootActionId}}),v=h.search,m=h.actions,g=h.rootActionId,y=l.useMemo(function(){return Object.keys(m).reduce(function(e,t){var n=m[t];if(n.parent||g||e.push(n),n.id===g)for(var r=0;r<n.children.length;r++)e.push(n.children[r]);return e},[]).sort(f)},[m,g]),b=l.useCallback(function(e){for(var t=[],n=0;n<e.length;n++)t.push(e[n]);return function e(n,r){void 0===r&&(r=t);for(var o=0;o<n.length;o++)if(n[o].children.length>0){for(var i=n[o].children,a=0;a<i.length;a++)r.push(i[a]);e(n[o].children,r)}return r}(e)},[]),w=!v,x=l.useMemo(function(){return w?y:b(y)},[b,y,w]),M=l.useMemo(function(){return new c.default(x,d)},[x]),k=(e=x,n=v,r=M,o=l.useMemo(function(){return{filtered:e,search:n}},[e,n]),a=(i=(0,s.useThrottledValue)(o)).filtered,p=i.search,l.useMemo(function(){if(""===p.trim())return a.map(function(e){return{score:0,action:e}});var e=[];return r.search(p).map(function(e){var t=e.item,n=e.score;return{score:1/((null!=n?n:0)+1),action:t}})},[a,p,r])),C=l.useMemo(function(){for(var e,n,r={},o=[],i=[],a=0;a<k.length;a++){var l=k[a],u=l.action,c=l.score||s.Priority.NORMAL,d={name:"string"==typeof u.section?u.section:(null==(e=u.section)?void 0:e.name)||t.NO_GROUP.name,priority:"string"==typeof u.section?c:(null==(n=u.section)?void 0:n.priority)||0+c};r[d.name]||(r[d.name]=[],o.push(d)),r[d.name].push({priority:u.priority+c,action:u})}i=o.sort(f).map(function(e){return{name:e.name,actions:r[e.name].sort(f).map(function(e){return e.action})}});for(var p=[],a=0;a<i.length;a++){var h=i[a];h.name!==t.NO_GROUP.name&&p.push(h.name);for(var v=0;v<h.actions.length;v++)p.push(h.actions[v])}return p},[k]),E=l.useMemo(function(){return g},[C]);return l.useMemo(function(){return{results:C,rootActionId:E}},[E,C])}t.useMatches=p,t.useDeepMatches=p},73656:(e,t,n)=>{n.d(t,{Kq:()=>V,UC:()=>U,ZL:()=>G,bL:()=>$,i3:()=>q,l9:()=>H});var r=n(60222),o=n(12772),i=n(24368),a=n(4684),l=n(12795),u=n(31354),s=n(22207),c=n(84629),d=n(49258),f=n(24582),p=n(16586),h=n(36612),v=n(30413),m=n(24443),[g,y]=(0,a.A)("Tooltip",[s.Bk]),b=(0,s.Bk)(),w="TooltipProvider",x="tooltip.open",[M,k]=g(w),C=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:a}=e,[l,u]=r.useState(!0),s=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(M,{scope:t,isOpenDelayed:l,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),u(!1)},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>u(!0),o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:r.useCallback(e=>{s.current=e},[]),disableHoverableContent:i,children:a})};C.displayName=w;var E="Tooltip",[S,O]=g(E),A=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i=!1,onOpenChange:a,disableHoverableContent:l,delayDuration:c}=e,d=k(E,e.__scopeTooltip),f=b(t),[p,v]=r.useState(null),g=(0,u.B)(),y=r.useRef(0),w=l??d.disableHoverableContent,M=c??d.delayDuration,C=r.useRef(!1),[O=!1,A]=(0,h.i)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(x))):d.onClose(),a?.(e)}}),j=r.useMemo(()=>O?C.current?"delayed-open":"instant-open":"closed",[O]),R=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,C.current=!1,A(!0)},[A]),_=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,A(!1)},[A]),P=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{C.current=!0,A(!0),y.current=0},M)},[M,A]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,m.jsx)(s.bL,{...f,children:(0,m.jsx)(S,{scope:t,contentId:g,open:O,stateAttribute:j,trigger:p,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayed?P():R()},[d.isOpenDelayed,P,R]),onTriggerLeave:r.useCallback(()=>{w?_():(window.clearTimeout(y.current),y.current=0)},[_,w]),onOpen:R,onClose:_,disableHoverableContent:w,children:n})})};A.displayName=E;var j="TooltipTrigger",R=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,l=O(j,n),u=k(j,n),c=b(n),d=r.useRef(null),p=(0,i.s)(t,d,l.onTriggerChange),h=r.useRef(!1),v=r.useRef(!1),g=r.useCallback(()=>h.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,m.jsx)(s.Mz,{asChild:!0,...c,children:(0,m.jsx)(f.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||u.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});R.displayName=j;var _="TooltipPortal",[P,I]=g(_,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=O(_,t);return(0,m.jsx)(P,{scope:t,forceMount:n,children:(0,m.jsx)(d.C,{present:n||i.open,children:(0,m.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};D.displayName=_;var L="TooltipContent",N=r.forwardRef((e,t)=>{let n=I(L,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,a=O(L,e.__scopeTooltip);return(0,m.jsx)(d.C,{present:r||a.open,children:a.disableHoverableContent?(0,m.jsx)(K,{side:o,...i,ref:t}):(0,m.jsx)(T,{side:o,...i,ref:t})})}),T=r.forwardRef((e,t)=>{let n=O(L,e.__scopeTooltip),o=k(L,e.__scopeTooltip),a=r.useRef(null),l=(0,i.s)(t,a),[u,s]=r.useState(null),{trigger:c,onClose:d}=n,f=a.current,{onPointerInTransitChange:p}=o,h=r.useCallback(()=>{s(null),p(!1)},[p]),v=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());s(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>h(),[h]),r.useEffect(()=>{if(c&&f){let e=e=>v(e,f),t=e=>v(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,v,h]),r.useEffect(()=>{if(u){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=c?.contains(t)||f?.contains(t),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,l=t[e].y,u=t[i].x,s=t[i].y;l>r!=s>r&&n<(u-a)*(r-l)/(s-l)+a&&(o=!o)}return o}(n,u);r?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,u,d,h]),(0,m.jsx)(K,{...e,ref:l})}),[F,B]=g(E,{isInside:!1}),K=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:a,onPointerDownOutside:u,...c}=e,d=O(L,n),f=b(n),{onClose:h}=d;return r.useEffect(()=>(document.addEventListener(x,h),()=>document.removeEventListener(x,h)),[h]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;t?.contains(d.trigger)&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,h]),(0,m.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,m.jsxs)(s.UC,{"data-state":d.stateAttribute,...f,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(p.xV,{children:o}),(0,m.jsx)(F,{scope:n,isInside:!0,children:(0,m.jsx)(v.b,{id:d.contentId,role:"tooltip",children:i||o})})]})})});N.displayName=L;var z="TooltipArrow",W=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=b(n);return B(z,n).isInside?null:(0,m.jsx)(s.i3,{...o,...r,ref:t})});W.displayName=z;var V=C,$=A,H=R,G=D,U=N,q=W},75838:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},76653:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>R,q7:()=>_});var r=n(60222),o=n(12772),i=n(27926),a=n(24368),l=n(4684),u=n(31354),s=n(24582),c=n(88818),d=n(36612),f=n(9719),p=n(24443),h="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[g,y,b]=(0,i.N)(m),[w,x]=(0,l.A)(m,[b]),[M,k]=w(m),C=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(E,{...e,ref:t})})}));C.displayName=m;var E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...k}=e,C=r.useRef(null),E=(0,a.s)(t,C),S=(0,f.jH)(u),[O=null,A]=(0,d.i)({prop:m,defaultProp:g,onChange:b}),[R,_]=r.useState(!1),P=(0,c.c)(w),I=y(n),D=r.useRef(!1),[L,N]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(h,P),()=>e.removeEventListener(h,P)},[P]),(0,p.jsx)(M,{scope:n,orientation:i,dir:S,loop:l,currentTabStopId:O,onItemFocus:r.useCallback(e=>A(e),[A]),onItemShiftTab:r.useCallback(()=>_(!0),[]),onFocusableItemAdd:r.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>N(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:R||0===L?-1:0,"data-orientation":i,...k,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!D.current;if(e.target===e.currentTarget&&t&&!R){let t=new CustomEvent(h,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=I().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===O),...e].filter(Boolean).map(e=>e.ref.current),x)}}D.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>_(!1))})})}),S="RovingFocusGroupItem",O=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,...c}=e,d=(0,u.B)(),f=l||d,h=k(S,n),v=h.currentTabStopId===f,m=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:w}=h;return r.useEffect(()=>{if(i)return b(),()=>w()},[i,b,w]),(0,p.jsx)(g.ItemSlot,{scope:n,id:f,focusable:i,active:a,children:(0,p.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":h.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?h.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>h.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return A[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=h.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>j(n))}})})})});O.displayName=S;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var R=C,_=O},79397:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","user-x","IconUserX",[["path",{d:"M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0",key:"svg-0"}],["path",{d:"M6 21v-2a4 4 0 0 1 4 -4h3.5",key:"svg-1"}],["path",{d:"M22 22l-5 -5",key:"svg-2"}],["path",{d:"M17 22l5 -5",key:"svg-3"}]])},80409:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","layout-kanban","IconLayoutKanban",[["path",{d:"M4 4l6 0",key:"svg-0"}],["path",{d:"M14 4l6 0",key:"svg-1"}],["path",{d:"M4 8m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-2"}],["path",{d:"M14 8m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v2a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z",key:"svg-3"}]])},80910:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},81587:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Command=void 0,t.Command=function(e,t){var n=this;void 0===t&&(t={}),this.perform=function(){var r=e.perform();if("function"==typeof r){var o=t.history;o&&(n.historyItem&&o.remove(n.historyItem),n.historyItem=o.add({perform:e.perform,negate:r}),n.history={undo:function(){return o.undo(n.historyItem)},redo:function(){return o.redo(n.historyItem)}})}}}},84265:(e,t,n)=>{n.r(t),n.d(t,{circularDeepEqual:()=>g,circularShallowEqual:()=>y,createCustomEqual:()=>h,deepEqual:()=>v,sameValueZeroEqual:()=>o,shallowEqual:()=>m});var r=Object.keys;function o(e,t){return e===t||e!=e&&t!=t}function i(e){return e.constructor===Object||null==e.constructor}function a(e){return!!e&&"function"==typeof e.then}function l(e){return!!(e&&e.$$typeof)}var u="function"==typeof WeakSet?function(){return new WeakSet}:function(){var e=[];return{add:function(t){e.push(t)},has:function(t){return -1!==e.indexOf(t)}}};function s(e){return function(t){var n=e||t;return function(e,t,r){void 0===r&&(r=u());var o=!!e&&"object"==typeof e,i=!!t&&"object"==typeof t;if(o||i){var a=o&&r.has(e),l=i&&r.has(t);if(a||l)return a&&l;o&&r.add(e),i&&r.add(t)}return n(e,t,r)}}}var c=Function.prototype.bind.call(Function.prototype.call,Object.prototype.hasOwnProperty);function d(e,t,n,o){var i=r(e),a=i.length;if(r(t).length!==a)return!1;if(a)for(var u=void 0;a-- >0;){if("_owner"===(u=i[a])){var s=l(e),d=l(t);if((s||d)&&s!==d)return!1}if(!c(t,u)||!n(e[u],t[u],o))return!1}return!0}var f="function"==typeof Map,p="function"==typeof Set;function h(e){var t="function"==typeof e?e(n):n;function n(e,n,r){if(e===n)return!0;if(e&&n&&"object"==typeof e&&"object"==typeof n){if(i(e)&&i(n))return d(e,n,t,r);var l=Array.isArray(e),u=Array.isArray(n);return l||u?l===u&&function(e,t,n,r){var o=e.length;if(t.length!==o)return!1;for(;o-- >0;)if(!n(e[o],t[o],r))return!1;return!0}(e,n,t,r):(l=e instanceof Date,u=n instanceof Date,l||u)?l===u&&o(e.getTime(),n.getTime()):(l=e instanceof RegExp,u=n instanceof RegExp,l||u)?l===u&&e.source===n.source&&e.global===n.global&&e.ignoreCase===n.ignoreCase&&e.multiline===n.multiline&&e.unicode===n.unicode&&e.sticky===n.sticky&&e.lastIndex===n.lastIndex:a(e)||a(n)?e===n:f&&(l=e instanceof Map,u=n instanceof Map,l||u)?l===u&&function(e,t,n,r){var o=e.size===t.size;if(o&&e.size){var i={};e.forEach(function(e,a){if(o){var l=!1,u=0;t.forEach(function(t,o){!l&&!i[u]&&(l=n(a,o,r)&&n(e,t,r))&&(i[u]=!0),u++}),o=l}})}return o}(e,n,t,r):p&&(l=e instanceof Set,u=n instanceof Set,l||u)?l===u&&function(e,t,n,r){var o=e.size===t.size;if(o&&e.size){var i={};e.forEach(function(e){if(o){var a=!1,l=0;t.forEach(function(t){!a&&!i[l]&&(a=n(e,t,r))&&(i[l]=!0),l++}),o=a}})}return o}(e,n,t,r):d(e,n,t,r)}return e!=e&&n!=n}return n}var v=h(),m=h(function(){return o}),g=h(s()),y=h(s(o))},84338:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},84629:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(60222),o=n(89859),i=n(24582),a=n(21382),l=n(24443),u=r.forwardRef((e,t)=>{let{container:n,...u}=e,[s,c]=r.useState(!1);(0,a.N)(()=>c(!0),[]);let d=n||s&&globalThis?.document?.body;return d?o.createPortal((0,l.jsx)(i.sG.div,{...u,ref:t}),d):null});u.displayName="Portal"},86127:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},86292:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.history=t.HistoryItemImpl=void 0;var r=n(67214),o=function(){function e(e){this.perform=e.perform,this.negate=e.negate}return e.create=function(t){return new e(t)},e}();t.HistoryItemImpl=o;var i=new(function(){function e(){return this.undoStack=[],this.redoStack=[],e.instance||(e.instance=this,this.init()),e.instance}return e.prototype.init=function(){var e=this;"undefined"!=typeof window&&window.addEventListener("keydown",function(t){if(!(!e.redoStack.length&&!e.undoStack.length||(0,r.shouldRejectKeystrokes)())){var n,o=null==(n=t.key)?void 0:n.toLowerCase();t.metaKey&&"z"===o&&t.shiftKey?e.redo():t.metaKey&&"z"===o&&e.undo()}})},e.prototype.add=function(e){var t=o.create(e);return this.undoStack.push(t),t},e.prototype.remove=function(e){var t=this.undoStack.findIndex(function(t){return t===e});if(-1!==t)return void this.undoStack.splice(t,1);var n=this.redoStack.findIndex(function(t){return t===e});-1!==n&&this.redoStack.splice(n,1)},e.prototype.undo=function(e){if(!e){var t=this.undoStack.pop();if(!t)return;return null==t||t.negate(),this.redoStack.push(t),t}var n=this.undoStack.findIndex(function(t){return t===e});if(-1!==n)return this.undoStack.splice(n,1),e.negate(),this.redoStack.push(e),e},e.prototype.redo=function(e){if(!e){var t=this.redoStack.pop();if(!t)return;return null==t||t.perform(),this.undoStack.push(t),t}var n=this.redoStack.findIndex(function(t){return t===e});if(-1!==n)return this.redoStack.splice(n,1),e.perform(),this.undoStack.push(e),e},e.prototype.reset=function(){this.undoStack.splice(0),this.redoStack.splice(0)},e}());t.history=i,Object.freeze(i)},87140:(e,t,n)=>{var r=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,s=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))u.call(e,l)||l===n||o(e,l,{get:()=>t[l],enumerable:!(r=i(t,l))||r.enumerable});return e},c=(e,t,n)=>(n=null!=e?r(l(e)):{},s(!t&&e&&e.__esModule?n:o(n,"default",{value:e,enumerable:!0}),e)),d={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(d,{Portal:()=>g,Root:()=>y}),e.exports=s(o({},"__esModule",{value:!0}),d);var f=c(n(60222)),p=c(n(89859)),h=n(93805),v=n(59757),m=n(24443),g=f.forwardRef((e,t)=>{let{container:n,...r}=e,[o,i]=f.useState(!1);(0,v.useLayoutEffect)(()=>i(!0),[]);let a=n||o&&globalThis?.document?.body;return a?p.default.createPortal((0,m.jsx)(h.Primitive.div,{...r,ref:t}),a):null});g.displayName="Portal";var y=g},87483:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(60222);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},87615:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","message-circle","IconMessageCircle",[["path",{d:"M3 20l1.3 -3.9c-2.324 -3.437 -1.426 -7.872 2.1 -10.374c3.526 -2.501 8.59 -2.296 11.845 .48c3.255 2.777 3.695 7.266 1.029 10.501c-2.666 3.235 -7.615 4.215 -11.574 2.293l-4.7 1",key:"svg-0"}]])},88138:(e,t,n)=>{function r(e){return Array.isArray?Array.isArray(e):"[object Array]"===c(e)}n.r(t),n.d(t,{default:()=>J});let o=1/0;function i(e){return"string"==typeof e}function a(e){return"number"==typeof e}function l(e){return"object"==typeof e}function u(e){return null!=e}function s(e){return!e.trim().length}function c(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}let d=e=>`Invalid value for key ${e}`,f=e=>`Pattern length exceeds max of ${e}.`,p=e=>`Missing ${e} property in key`,h=e=>`Property 'weight' in key '${e}' must be a positive integer`,v=Object.prototype.hasOwnProperty;class m{constructor(e){this._keys=[],this._keyMap={};let t=0;e.forEach(e=>{let n=g(e);t+=n.weight,this._keys.push(n),this._keyMap[n.id]=n,t+=n.weight}),this._keys.forEach(e=>{e.weight/=t})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function g(e){let t=null,n=null,o=null,a=1,l=null;if(i(e)||r(e))o=e,t=y(e),n=b(e);else{if(!v.call(e,"name"))throw Error(p("name"));let r=e.name;if(o=r,v.call(e,"weight")&&(a=e.weight)<=0)throw Error(h(r));t=y(r),n=b(r),l=e.getFn}return{path:t,id:n,weight:a,src:o,getFn:l}}function y(e){return r(e)?e:e.split(".")}function b(e){return r(e)?e.join("."):e}var w={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(e,t)=>e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(e,t){let n=[],s=!1,d=(e,t,f)=>{if(u(e))if(t[f]){var p,h;let v=e[t[f]];if(!u(v))return;if(f===t.length-1&&(i(v)||a(v)||!0===(p=v)||!1===p||l(h=p)&&null!==h&&"[object Boolean]"==c(p)))n.push(null==v?"":function(e){if("string"==typeof e)return e;let t=e+"";return"0"==t&&1/e==-o?"-0":t}(v));else if(r(v)){s=!0;for(let e=0,n=v.length;e<n;e+=1)d(v[e],t,f+1)}else t.length&&d(v,t,f+1)}else n.push(e)};return d(e,i(t)?t.split("."):t,0),s?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};let x=/[^ ]+/g;class M{constructor({getFn:e=w.getFn,fieldNormWeight:t=w.fieldNormWeight}={}){this.norm=function(e=1,t=3){let n=new Map,r=Math.pow(10,t);return{get(t){let o=t.match(x).length;if(n.has(o))return n.get(o);let i=parseFloat(Math.round(1/Math.pow(o,.5*e)*r)/r);return n.set(o,i),i},clear(){n.clear()}}}(t,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((e,t)=>{this._keysMap[e.id]=t})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,i(this.docs[0])?this.docs.forEach((e,t)=>{this._addString(e,t)}):this.docs.forEach((e,t)=>{this._addObject(e,t)}),this.norm.clear())}add(e){let t=this.size();i(e)?this._addString(e,t):this._addObject(e,t)}removeAt(e){this.records.splice(e,1);for(let t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}getValueForItemAtKeyId(e,t){return e[this._keysMap[t]]}size(){return this.records.length}_addString(e,t){if(!u(e)||s(e))return;let n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}_addObject(e,t){let n={i:t,$:{}};this.keys.forEach((t,o)=>{let a=t.getFn?t.getFn(e):this.getFn(e,t.path);if(u(a)){if(r(a)){let e=[],t=[{nestedArrIndex:-1,value:a}];for(;t.length;){let{nestedArrIndex:n,value:o}=t.pop();if(u(o))if(i(o)&&!s(o)){let t={v:o,i:n,n:this.norm.get(o)};e.push(t)}else r(o)&&o.forEach((e,n)=>{t.push({nestedArrIndex:n,value:e})})}n.$[o]=e}else if(i(a)&&!s(a)){let e={v:a,n:this.norm.get(a)};n.$[o]=e}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function k(e,t,{getFn:n=w.getFn,fieldNormWeight:r=w.fieldNormWeight}={}){let o=new M({getFn:n,fieldNormWeight:r});return o.setKeys(e.map(g)),o.setSources(t),o.create(),o}function C(e,{errors:t=0,currentLocation:n=0,expectedLocation:r=0,distance:o=w.distance,ignoreLocation:i=w.ignoreLocation}={}){let a=t/e.length;if(i)return a;let l=Math.abs(r-n);return o?a+l/o:l?1:a}class E{constructor(e,{location:t=w.location,threshold:n=w.threshold,distance:r=w.distance,includeMatches:o=w.includeMatches,findAllMatches:i=w.findAllMatches,minMatchCharLength:a=w.minMatchCharLength,isCaseSensitive:l=w.isCaseSensitive,ignoreLocation:u=w.ignoreLocation}={}){if(this.options={location:t,threshold:n,distance:r,includeMatches:o,findAllMatches:i,minMatchCharLength:a,isCaseSensitive:l,ignoreLocation:u},this.pattern=l?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;let s=(e,t)=>{this.chunks.push({pattern:e,alphabet:function(e){let t={};for(let n=0,r=e.length;n<r;n+=1){let o=e.charAt(n);t[o]=(t[o]||0)|1<<r-n-1}return t}(e),startIndex:t})},c=this.pattern.length;if(c>32){let e=0,t=c%32,n=c-t;for(;e<n;)s(this.pattern.substr(e,32),e),e+=32;if(t){let e=c-32;s(this.pattern.substr(e),e)}}else s(this.pattern,0)}searchIn(e){let{isCaseSensitive:t,includeMatches:n}=this.options;if(t||(e=e.toLowerCase()),this.pattern===e){let t={isMatch:!0,score:0};return n&&(t.indices=[[0,e.length-1]]),t}let{location:r,distance:o,threshold:i,findAllMatches:a,minMatchCharLength:l,ignoreLocation:u}=this.options,s=[],c=0,d=!1;this.chunks.forEach(({pattern:t,alphabet:p,startIndex:h})=>{let{isMatch:v,score:m,indices:g}=function(e,t,n,{location:r=w.location,distance:o=w.distance,threshold:i=w.threshold,findAllMatches:a=w.findAllMatches,minMatchCharLength:l=w.minMatchCharLength,includeMatches:u=w.includeMatches,ignoreLocation:s=w.ignoreLocation}={}){let c;if(t.length>32)throw Error(f(32));let d=t.length,p=e.length,h=Math.max(0,Math.min(r,p)),v=i,m=h,g=l>1||u,y=g?Array(p):[];for(;(c=e.indexOf(t,m))>-1;)if(v=Math.min(C(t,{currentLocation:c,expectedLocation:h,distance:o,ignoreLocation:s}),v),m=c+d,g){let e=0;for(;e<d;)y[c+e]=1,e+=1}m=-1;let b=[],x=1,M=d+p,k=1<<d-1;for(let r=0;r<d;r+=1){let i=0,l=M;for(;i<l;)C(t,{errors:r,currentLocation:h+l,expectedLocation:h,distance:o,ignoreLocation:s})<=v?i=l:M=l,l=Math.floor((M-i)/2+i);M=l;let u=Math.max(1,h-l+1),c=a?p:Math.min(h+l,p)+d,f=Array(c+2);f[c+1]=(1<<r)-1;for(let i=c;i>=u;i-=1){let a=i-1,l=n[e.charAt(a)];if(g&&(y[a]=+!!l),f[i]=(f[i+1]<<1|1)&l,r&&(f[i]|=(b[i+1]|b[i])<<1|1|b[i+1]),f[i]&k&&(x=C(t,{errors:r,currentLocation:a,expectedLocation:h,distance:o,ignoreLocation:s}))<=v){if(v=x,(m=a)<=h)break;u=Math.max(1,2*h-m)}}if(C(t,{errors:r+1,currentLocation:h,expectedLocation:h,distance:o,ignoreLocation:s})>v)break;b=f}let E={isMatch:m>=0,score:Math.max(.001,x)};if(g){let e=function(e=[],t=w.minMatchCharLength){let n=[],r=-1,o=-1,i=0;for(let a=e.length;i<a;i+=1){let a=e[i];a&&-1===r?r=i:a||-1===r||((o=i-1)-r+1>=t&&n.push([r,o]),r=-1)}return e[i-1]&&i-r>=t&&n.push([r,i-1]),n}(y,l);e.length?u&&(E.indices=e):E.isMatch=!1}return E}(e,t,p,{location:r+h,distance:o,threshold:i,findAllMatches:a,minMatchCharLength:l,includeMatches:n,ignoreLocation:u});v&&(d=!0),c+=m,v&&g&&(s=[...s,...g])});let p={isMatch:d,score:d?c/this.chunks.length:1};return d&&n&&(p.indices=s),p}}class S{constructor(e){this.pattern=e}static isMultiMatch(e){return O(e,this.multiRegex)}static isSingleMatch(e){return O(e,this.singleRegex)}search(){}}function O(e,t){let n=e.match(t);return n?n[1]:null}class A extends S{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){let t=e===this.pattern;return{isMatch:t,score:+!t,indices:[0,this.pattern.length-1]}}}class j extends S{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){let t=-1===e.indexOf(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class R extends S{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){let t=e.startsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,this.pattern.length-1]}}}class _ extends S{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){let t=!e.startsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class P extends S{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){let t=e.endsWith(this.pattern);return{isMatch:t,score:+!t,indices:[e.length-this.pattern.length,e.length-1]}}}class I extends S{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){let t=!e.endsWith(this.pattern);return{isMatch:t,score:+!t,indices:[0,e.length-1]}}}class D extends S{constructor(e,{location:t=w.location,threshold:n=w.threshold,distance:r=w.distance,includeMatches:o=w.includeMatches,findAllMatches:i=w.findAllMatches,minMatchCharLength:a=w.minMatchCharLength,isCaseSensitive:l=w.isCaseSensitive,ignoreLocation:u=w.ignoreLocation}={}){super(e),this._bitapSearch=new E(e,{location:t,threshold:n,distance:r,includeMatches:o,findAllMatches:i,minMatchCharLength:a,isCaseSensitive:l,ignoreLocation:u})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}}class L extends S{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let t,n=0,r=[],o=this.pattern.length;for(;(t=e.indexOf(this.pattern,n))>-1;)n=t+o,r.push([t,n-1]);let i=!!r.length;return{isMatch:i,score:+!i,indices:r}}}let N=[A,L,R,_,I,P,j,D],T=N.length,F=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,B=new Set([D.type,L.type]);class K{constructor(e,{isCaseSensitive:t=w.isCaseSensitive,includeMatches:n=w.includeMatches,minMatchCharLength:r=w.minMatchCharLength,ignoreLocation:o=w.ignoreLocation,findAllMatches:i=w.findAllMatches,location:a=w.location,threshold:l=w.threshold,distance:u=w.distance}={}){this.query=null,this.options={isCaseSensitive:t,includeMatches:n,minMatchCharLength:r,findAllMatches:i,ignoreLocation:o,location:a,threshold:l,distance:u},this.pattern=t?e:e.toLowerCase(),this.query=function(e,t={}){return e.split("|").map(e=>{let n=e.trim().split(F).filter(e=>e&&!!e.trim()),r=[];for(let e=0,o=n.length;e<o;e+=1){let o=n[e],i=!1,a=-1;for(;!i&&++a<T;){let e=N[a],n=e.isMultiMatch(o);n&&(r.push(new e(n,t)),i=!0)}if(!i)for(a=-1;++a<T;){let e=N[a],n=e.isSingleMatch(o);if(n){r.push(new e(n,t));break}}}return r})}(this.pattern,this.options)}static condition(e,t){return t.useExtendedSearch}searchIn(e){let t=this.query;if(!t)return{isMatch:!1,score:1};let{includeMatches:n,isCaseSensitive:r}=this.options;e=r?e:e.toLowerCase();let o=0,i=[],a=0;for(let r=0,l=t.length;r<l;r+=1){let l=t[r];i.length=0,o=0;for(let t=0,r=l.length;t<r;t+=1){let r=l[t],{isMatch:u,indices:s,score:c}=r.search(e);if(u){if(o+=1,a+=c,n){let e=r.constructor.type;B.has(e)?i=[...i,...s]:i.push(s)}}else{a=0,o=0,i.length=0;break}}if(o){let e={isMatch:!0,score:a/o};return n&&(e.indices=i),e}}return{isMatch:!1,score:1}}}let z=[];function W(e,t){for(let n=0,r=z.length;n<r;n+=1){let r=z[n];if(r.condition(e,t))return new r(e,t)}return new E(e,t)}let V={AND:"$and",OR:"$or"},$={PATH:"$path",PATTERN:"$val"},H=e=>!!(e[V.AND]||e[V.OR]),G=e=>!!e[$.PATH],U=e=>!r(e)&&l(e)&&!H(e),q=e=>({[V.AND]:Object.keys(e).map(t=>({[t]:e[t]}))});function X(e,t,{auto:n=!0}={}){let o=e=>{let a=Object.keys(e),l=G(e);if(!l&&a.length>1&&!H(e))return o(q(e));if(U(e)){let r=l?e[$.PATH]:a[0],o=l?e[$.PATTERN]:e[r];if(!i(o))throw Error(d(r));let u={keyId:b(r),pattern:o};return n&&(u.searcher=W(o,t)),u}let u={children:[],operator:a[0]};return a.forEach(t=>{let n=e[t];r(n)&&n.forEach(e=>{u.children.push(o(e))})}),u};return H(e)||(e=q(e)),o(e)}function Y(e,t){let n=e.matches;t.matches=[],u(n)&&n.forEach(e=>{if(!u(e.indices)||!e.indices.length)return;let{indices:n,value:r}=e,o={indices:n,value:r};e.key&&(o.key=e.key.src),e.idx>-1&&(o.refIndex=e.idx),t.matches.push(o)})}function Z(e,t){t.score=e.score}class J{constructor(e,t={},n){this.options={...w,...t},this.options.useExtendedSearch,this._keyStore=new m(this.options.keys),this.setCollection(e,n)}setCollection(e,t){if(this._docs=e,t&&!(t instanceof M))throw Error("Incorrect 'index' type");this._myIndex=t||k(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){u(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){let t=[];for(let n=0,r=this._docs.length;n<r;n+=1){let o=this._docs[n];e(o,n)&&(this.removeAt(n),n-=1,r-=1,t.push(o))}return t}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:t=-1}={}){let{includeMatches:n,includeScore:r,shouldSort:o,sortFn:l,ignoreFieldNorm:u}=this.options,s=i(e)?i(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return!function(e,{ignoreFieldNorm:t=w.ignoreFieldNorm}){e.forEach(e=>{let n=1;e.matches.forEach(({key:e,norm:r,score:o})=>{let i=e?e.weight:null;n*=Math.pow(0===o&&i?Number.EPSILON:o,(i||1)*(t?1:r))}),e.score=n})}(s,{ignoreFieldNorm:u}),o&&s.sort(l),a(t)&&t>-1&&(s=s.slice(0,t)),function(e,t,{includeMatches:n=w.includeMatches,includeScore:r=w.includeScore}={}){let o=[];return n&&o.push(Y),r&&o.push(Z),e.map(e=>{let{idx:n}=e,r={item:t[n],refIndex:n};return o.length&&o.forEach(t=>{t(e,r)}),r})}(s,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(e){let t=W(e,this.options),{records:n}=this._myIndex,r=[];return n.forEach(({v:e,i:n,n:o})=>{if(!u(e))return;let{isMatch:i,score:a,indices:l}=t.searchIn(e);i&&r.push({item:e,idx:n,matches:[{score:a,value:e,norm:o,indices:l}]})}),r}_searchLogical(e){let t=X(e,this.options),n=(e,t,r)=>{if(!e.children){let{keyId:n,searcher:o}=e,i=this._findMatches({key:this._keyStore.get(n),value:this._myIndex.getValueForItemAtKeyId(t,n),searcher:o});return i&&i.length?[{idx:r,item:t,matches:i}]:[]}let o=[];for(let i=0,a=e.children.length;i<a;i+=1){let a=n(e.children[i],t,r);if(a.length)o.push(...a);else if(e.operator===V.AND)return[]}return o},r=this._myIndex.records,o={},i=[];return r.forEach(({$:e,i:r})=>{if(u(e)){let a=n(t,e,r);a.length&&(o[r]||(o[r]={idx:r,item:e,matches:[]},i.push(o[r])),a.forEach(({matches:e})=>{o[r].matches.push(...e)}))}}),i}_searchObjectList(e){let t=W(e,this.options),{keys:n,records:r}=this._myIndex,o=[];return r.forEach(({$:e,i:r})=>{if(!u(e))return;let i=[];n.forEach((n,r)=>{i.push(...this._findMatches({key:n,value:e[r],searcher:t}))}),i.length&&o.push({idx:r,item:e,matches:i})}),o}_findMatches({key:e,value:t,searcher:n}){if(!u(t))return[];let o=[];if(r(t))t.forEach(({v:t,i:r,n:i})=>{if(!u(t))return;let{isMatch:a,score:l,indices:s}=n.searchIn(t);a&&o.push({score:l,key:e,value:t,idx:r,norm:i,indices:s})});else{let{v:r,n:i}=t,{isMatch:a,score:l,indices:u}=n.searchIn(r);a&&o.push({score:l,key:e,value:r,norm:i,indices:u})}return o}}J.version="6.6.2",J.createIndex=k,J.parseIndex=function(e,{getFn:t=w.getFn,fieldNormWeight:n=w.fieldNormWeight}={}){let{keys:r,records:o}=e,i=new M({getFn:t,fieldNormWeight:n});return i.setKeys(r),i.setIndexRecords(o),i},J.config=w,J.parseQuery=X,function(...e){z.push(...e)}(K)},88629:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},88818:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(60222);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},88860:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(60222),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},89915:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(61770).A)("GalleryVerticalEnd",[["path",{d:"M7 2h10",key:"nczekb"}],["path",{d:"M5 6h14",key:"u2x4p"}],["rect",{width:"18",height:"12",x:"3",y:"10",rx:"2",key:"l0tzu3"}]])},91537:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","login","IconLogin",[["path",{d:"M15 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2",key:"svg-0"}],["path",{d:"M21 12h-13l3 -3",key:"svg-1"}],["path",{d:"M11 15l-3 -3",key:"svg-2"}]])},91953:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=["Shift","Meta","Alt","Control"],r="object"==typeof navigator&&/Mac|iPod|iPhone|iPad/.test(navigator.platform)?"Meta":"Control";function o(e,t){return"function"==typeof e.getModifierState&&e.getModifierState(t)}t.default=function(e,t,i){void 0===i&&(i={});var a,l,u=null!=(a=i.timeout)?a:1e3,s=null!=(l=i.event)?l:"keydown",c=Object.keys(t).map(function(e){return[e.trim().split(" ").map(function(e){var t=e.split(/\b\+/),n=t.pop();return[t=t.map(function(e){return"$mod"===e?r:e}),n]}),t[e]]}),d=new Map,f=null,p=function(e){e instanceof KeyboardEvent&&(c.forEach(function(t){var r=t[0],i=t[1],a=d.get(r)||r,l=a[0];/^[^A-Za-z0-9]$/.test(e.key)&&l[1]===e.key||!(l[1].toUpperCase()!==e.key.toUpperCase()&&l[1]!==e.code||l[0].find(function(t){return!o(e,t)})||n.find(function(t){return!l[0].includes(t)&&l[1]!==t&&o(e,t)}))?a.length>1?d.set(r,a.slice(1)):(d.delete(r),i(e)):o(e,e.key)||d.delete(r)}),f&&clearTimeout(f),f=setTimeout(d.clear.bind(d),u))};return e.addEventListener(s,p),function(){e.removeEventListener(s,p)}}},92113:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","stethoscope","IconStethoscope",[["path",{d:"M6 4h-1a2 2 0 0 0 -2 2v3.5h0a5.5 5.5 0 0 0 11 0v-3.5a2 2 0 0 0 -2 -2h-1",key:"svg-0"}],["path",{d:"M8 15a6 6 0 1 0 12 0v-3",key:"svg-1"}],["path",{d:"M11 3v2",key:"svg-2"}],["path",{d:"M6 3v2",key:"svg-3"}],["path",{d:"M20 10m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0",key:"svg-4"}]])},92700:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","alert-triangle","IconAlertTriangle",[["path",{d:"M12 9v4",key:"svg-0"}],["path",{d:"M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z",key:"svg-1"}],["path",{d:"M12 16h.01",key:"svg-2"}]])},93596:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","pizza","IconPizza",[["path",{d:"M12 21.5c-3.04 0 -5.952 -.714 -8.5 -1.983l8.5 -16.517l8.5 16.517a19.09 19.09 0 0 1 -8.5 1.983z",key:"svg-0"}],["path",{d:"M5.38 15.866a14.94 14.94 0 0 0 6.815 1.634a14.944 14.944 0 0 0 6.502 -1.479",key:"svg-1"}],["path",{d:"M13 11.01v-.01",key:"svg-2"}],["path",{d:"M11 14v-.01",key:"svg-3"}]])},93805:(e,t,n)=>{var r=Object.create,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,l=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,s=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))u.call(e,l)||l===n||o(e,l,{get:()=>t[l],enumerable:!(r=i(t,l))||r.enumerable});return e},c=(e,t,n)=>(n=null!=e?r(l(e)):{},s(!t&&e&&e.__esModule?n:o(n,"default",{value:e,enumerable:!0}),e)),d={};((e,t)=>{for(var n in t)o(e,n,{get:t[n],enumerable:!0})})(d,{Primitive:()=>m,Root:()=>y,dispatchDiscreteCustomEvent:()=>g}),e.exports=s(o({},"__esModule",{value:!0}),d);var f=c(n(60222)),p=c(n(89859)),h=n(6517),v=n(24443),m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=f.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?h.Slot:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,v.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function g(e,t){e&&p.flushSync(()=>e.dispatchEvent(t))}var y=m},94477:function(e,t,n){var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},l=this&&this.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};Object.defineProperty(t,"__esModule",{value:!0}),t.KBarSearch=t.getListboxItemId=t.KBAR_LISTBOX=void 0;var u=a(n(60222)),s=n(45410),c=n(30526);t.KBAR_LISTBOX="kbar-listbox",t.getListboxItemId=function(e){return"kbar-listbox-item-"+e},t.KBarSearch=function(e){var n=(0,c.useKBar)(function(e){return{search:e.searchQuery,currentRootActionId:e.currentRootActionId,actions:e.actions,activeIndex:e.activeIndex,showing:e.visualState===s.VisualState.showing}}),o=n.query,i=n.search,a=n.actions,d=n.currentRootActionId,f=n.activeIndex,p=n.showing,h=n.options,v=u.useState(i),m=v[0],g=v[1];u.useEffect(function(){o.setSearch(m)},[m,o]);var y=e.defaultPlaceholder,b=l(e,["defaultPlaceholder"]);u.useEffect(function(){return o.setSearch(""),o.getInput().focus(),function(){return o.setSearch("")}},[d,o]);var w=u.useMemo(function(){var e=null!=y?y:"Type a command or search…";return d&&a[d]?a[d].name:e},[a,d,y]);return u.createElement("input",r({},b,{ref:o.inputRefSetter,autoFocus:!0,autoComplete:"off",role:"combobox",spellCheck:"false","aria-expanded":p,"aria-controls":t.KBAR_LISTBOX,"aria-activedescendant":(0,t.getListboxItemId)(f),value:m,placeholder:w,onChange:function(t){var n,r,o;null==(n=e.onChange)||n.call(e,t),g(t.target.value),null==(o=null==(r=null==h?void 0:h.callbacks)?void 0:r.onQueryChange)||o.call(r,t.target.value)},onKeyDown:function(t){var n;if(null==(n=e.onKeyDown)||n.call(e,t),d&&!i&&"Backspace"===t.key){var r=a[d].parent;o.setCurrentRootAction(r)}}}))}},94560:e=>{e.exports=function(e,t){if(!e)throw Error("Invariant failed")}},95748:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","calendar","IconCalendar",[["path",{d:"M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z",key:"svg-0"}],["path",{d:"M16 3v4",key:"svg-1"}],["path",{d:"M8 3v4",key:"svg-2"}],["path",{d:"M4 11h16",key:"svg-3"}],["path",{d:"M11 15h1",key:"svg-4"}],["path",{d:"M12 15v3",key:"svg-5"}]])},97185:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","activity","IconActivity",[["path",{d:"M3 12h4l3 8l4 -16l3 8h4",key:"svg-0"}]])},97342:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","chevrons-down","IconChevronsDown",[["path",{d:"M7 7l5 5l5 -5",key:"svg-0"}],["path",{d:"M7 13l5 5l5 -5",key:"svg-1"}]])},98862:(e,t,n)=>{n.d(t,{A:()=>r});var r=(0,n(46244).A)("outline","brand-twitter","IconBrandTwitter",[["path",{d:"M22 4.01c-1 .49 -1.98 .689 -3 .99c-1.121 -1.265 -2.783 -1.335 -4.38 -.737s-2.643 2.06 -2.62 3.737v1c-3.245 .083 -6.135 -1.395 -8 -4c0 0 -4.182 7.433 4 11c-1.872 1.247 -3.739 2.088 -6 2c3.308 1.803 6.913 2.423 10.034 1.517c3.58 -1.04 6.522 -3.723 7.651 -7.742a13.84 13.84 0 0 0 .497 -3.753c0 -.249 1.51 -2.772 1.818 -4.013z",key:"svg-0"}]])},99873:(e,t,n)=>{n.d(t,{G$:()=>U,Hs:()=>x,UC:()=>et,VY:()=>er,ZL:()=>Q,bL:()=>Z,bm:()=>eo,hE:()=>en,hJ:()=>ee,l9:()=>J});var r=n(60222),o=n(12772),i=n(24368),a=n(4684),l=n(31354),u=n(36612),s=n(12795),c=n(71663),d=n(84629),f=n(49258),p=n(24582),h=n(88860),v=n(30992),m=n(2064),g=n(16586),y=n(24443),b="Dialog",[w,x]=(0,a.A)(b),[M,k]=w(b),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f=!1,p]=(0,u.i)({prop:o,defaultProp:i,onChange:a});return(0,y.jsx)(M,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};C.displayName=b;var E="DialogTrigger",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=k(E,n),l=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":H(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});S.displayName=E;var O="DialogPortal",[A,j]=w(O,{forceMount:void 0}),R=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=k(O,t);return(0,y.jsx)(A,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};R.displayName=O;var _="DialogOverlay",P=r.forwardRef((e,t)=>{let n=j(_,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=k(_,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(I,{...o,ref:t})}):null});P.displayName=_;var I=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(_,n);return(0,y.jsx)(v.A,{as:g.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":H(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),D="DialogContent",L=r.forwardRef((e,t)=>{let n=j(D,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=k(D,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(N,{...o,ref:t}):(0,y.jsx)(T,{...o,ref:t})})});L.displayName=D;var N=r.forwardRef((e,t)=>{let n=k(D,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,m.Eq)(e)},[]),(0,y.jsx)(F,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=r.forwardRef((e,t)=>{let n=k(D,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...u}=e,d=k(D,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(s.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":H(d.open),...u,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:d.titleId}),(0,y.jsx)(Y,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(B,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});K.displayName=B;var z="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(z,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});W.displayName=z;var V="DialogClose",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=k(V,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function H(e){return e?"open":"closed"}$.displayName=V;var G="DialogTitleWarning",[U,q]=(0,a.q)(G,{contentName:D,titleName:B,docsSlug:"dialog"}),X=({titleId:e})=>{let t=q(G),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Y=({contentRef:e,descriptionId:t})=>{let n=q("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Z=C,J=S,Q=R,ee=P,et=L,en=K,er=W,eo=$}};
//# sourceMappingURL=4144.js.map