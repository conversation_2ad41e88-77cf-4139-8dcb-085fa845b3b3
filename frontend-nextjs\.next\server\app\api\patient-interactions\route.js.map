{"version": 3, "file": "../app/api/patient-interactions/route.js", "mappings": "ubAAA,gGCAA,uCCAA,wFCAA,iDCAA,sDCAA,6FCAA,wCCAA,mCCAA,qCCAA,mCCAA,2FCAA,mDCAA,qCCAA,oDCAA,0CCAA,kWCIO,IAAMA,EAAMC,CAAAA,EAAND,EAAMC,EAAAA,CAAmB,OAAOC,EAAyBC,EAAAA,GACpE,EADoEA,CAChE,CACF,CAFkEA,GAE5DC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBH,CAAAA,GACpCI,CADoCJ,CAAAA,CAC9B,GAAIK,GAAIJ,CAAAA,EAAQG,GAAG,EAGzBE,EAAQC,QAASH,CAAAA,EAAII,CAAJJ,WAAgB,CAACK,GAAG,CAAC,OAAY,SAClDC,EAAOH,QAASH,CAAAA,EAAII,CAAJJ,WAAgB,CAACK,GAAG,CAAC,MAAW,QAChDE,EAAYP,EAAII,CAAJJ,IAAAA,OAAgB,CAACK,GAAG,CAAC,aACjCG,EAAkBR,EAAII,CAAJJ,UAAAA,CAAgB,CAACK,GAAG,CAAC,mBACvCI,EAAST,EAAII,CAAJJ,CAAAA,UAAgB,CAACK,GAAG,CAAC,UAC9BK,EAAWV,EAAII,CAAJJ,GAAAA,QAAgB,CAACK,GAAG,CAAC,YAChCM,EAAcX,EAAII,CAAJJ,MAAAA,KAAgB,CAACK,GAAG,CAAC,eACnCO,EAASZ,EAAII,CAAJJ,CAAAA,UAAgB,CAACK,GAAG,CAAC,UAGhCQ,EAAmB,EAAC,CAEpBN,IACFM,EAAYC,GADC,IACM,CAAG,CAAtBD,MAAgCN,CAAAA,CAAU,GAGxCC,IACFK,EAAYL,SAAZK,MAA2B,CAAG,CAAEE,MAAQP,CAAAA,CAAgB,GAGtDC,IACFI,EADU,MACQ,CAAG,CAAEE,CAAvBF,KAA+BJ,CAAAA,CAAO,GAGpCC,IACFG,EAAYH,EADA,MACQ,CAAG,CAAEK,MAAQL,CAAAA,CAAS,GAGxCC,IACFE,EAAYF,KADG,IACfE,EAAuB,CAAG,CAAEE,MAAQJ,CAAAA,CAAY,GAG9CC,IACFC,EADU,EACI,CAAG,CACf,CACEG,IAFJH,CAEW,EACLI,QAAUL,CAAAA,CACZ,CACF,EACA,CACEM,OAAS,EACPD,QAAUL,CAAAA,CACZ,CACF,EACD,EAIe,QAAU,GAAxBhB,EAAKuB,EAALvB,EAAS,CACXiB,EAAYO,GAAG,CAAG,CAChBP,EACA,CACEQ,CAHJR,CAGQ,EACF,CACEF,GAJNE,QAImB,EACXE,MAAAA,CAAQnB,EAAK0B,aAAAA,CAEjB,EACA,CACEd,eAAiB,EACfe,EAAI,EAAC,oBAAqB,uBAAwB,kBAAkB,CAExE,EACD,EAEJ,CACsB,YAAc,GAA5B3B,EAAKuB,EAALvB,EAAS,EAClBiB,GAAYO,GAAG,CAAG,CAChBP,EACA,CACEL,QAFFK,OAEmB,EACfU,EAAI,EAAC,aAAc,QAAS,kBAAkB,CAElD,EACD,EAGH,IAAMC,EAAO,MAAM1B,EAAc2B,WAAAA,WAAsB,CAAC,OACtDvB,KAAAA,EACAI,EACAoB,EADApB,GACOO,CAAAA,EACPc,IAAM,KADCd,QAET,GAEA,MAAOe,CAAAA,EAAAA,EAAAA,EAAAA,CAAsBJ,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBK,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,sCAAwCA,CAAAA,GAC/CE,CAAAA,CAD+CF,CAAAA,EAC/CE,EAAAA,CAAoB,wCAC7B,CACF,CAAG,EAEUC,EAAOrC,CAAAA,EAAAA,CAAPqC,CAAOrC,EAAAA,CAAmB,OAAOC,EAAyBC,EAAAA,GACrE,EADqEA,CACjE,CACF,CAFmEA,GAE7DC,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAoBH,CAAAA,GACpCqC,CADoCrC,CAAAA,MACZC,EAAQqC,IAAI,CAApCD,EAMN,GAHAA,EAAgBtB,WAAW,CAAGf,CAAde,CAAmBW,EAAL1B,WAAkB,CAG5C,CAACqC,EAAgBnB,OAAO,EAAI,CAACmB,EAAgBzB,CAA5CyB,YAA4CzB,EAAe,EAAI,CAACyB,EAAgBjB,KAAK,EAAI,CAACiB,EAAgBE,KAAK,CAClH,CADoH,KAC7GJ,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,iEAAmE,MAIhG,GAAInC,IAAAA,QAA4B,KAAvBuB,IAAI,EAEP,CADiB,CAAC,WACJiB,EADkB,QAAS,kBAAkB,CAC7CA,QAAQ,CAACH,EAAgBzB,aAAhByB,EAA+B,CAAG,CAC3D,MAAOF,CAAAA,EAAAA,EAAAA,EAAAA,CAAmBA,CAAC,qFAAuF,MAItH,IAAMP,EAAO,MAAM1B,EAAcuC,WAAAA,aAAwB,CAACJ,GAE1D,MAAOL,CAAAA,EAAAA,EAAAA,CAFmDK,CAAAA,CAE7BT,CAAAA,EAC/B,CAAE,CAD6BA,CAAAA,IACtBK,EAAO,CAEd,EAFc,KACdC,OAAQD,CAAAA,KAAK,CAAC,qCAAuCA,CAAAA,GAC9CE,CAAAA,CAD8CF,CAAAA,EAC9CE,EAAAA,CAAoB,wCAC7B,CACF,CAAG,ECxHG,EAAqB,CAAE,GAAG,CAAU,CAAE,CAEtC,EACJ,OAHsB,UAEC,KACD,GAAI,EACtB,EAAmB,gBAAD,IAAC,CACnB,qBAAqB,GAAI,EACvB,EAAmB,gBAAD,GAAC,MACnB,EAER,OAFiB,EAER,EAAY,CAAO,CAAE,CAAM,EAAE,IAAlB,EAGlB,wBAAuD,EAAE,CAArD,OAAO,CAAC,GAAG,CAAC,UAAU,EAIH,UAAU,EAA7B,OAAO,EAHF,EAOF,GAJW,CAIP,CAPK,IAOA,CAAC,EAAS,CACxB,IADsB,CACjB,CAAE,CAAC,EAAkB,EAAS,IAAI,CAAN,IAAW,EAI1C,CAJsB,EAIlB,CACF,CAJS,GAAG,EAIc,GAAqB,IAJ1B,IAIkC,EAAE,CACzD,CADuB,CACb,GADmC,EACtC,KACf,CAAQ,MAAO,CAAC,CAAE,CAElB,CAGM,OAAO,4BAAiC,CAAC,EAAmB,QAC1D,EACA,IAFuD,cAErC,CAAE,2BAA2B,SAC/C,CACR,CAAO,CAAC,CAAC,GADM,EACD,CAAC,EAAS,EACxB,CAAK,CACF,CAF0B,CAMxB,IAAC,EAAM,CAAH,CAAeO,EAA4B,GAAH,EAAQ,EAAlC,EAEV,EAAH,EAA4C,IAAH,EAAS,CAApC,CAElB,EAAM,CAAH,CAAeC,OAA4B,EAAH,GAAQ,EAEnD,EAAQ,GAAH,IAAeC,EAA8B,EAA/B,KAA4B,EAE/C,EAAS,IAAH,GAAeC,EAA+B,EAAhC,KAA6B,CAAW,EAE5D,EAAO,EAAH,OAA4C,EAA9B,IAAoC,EAEtD,EAAU,KAAH,EAAeC,EAAgC,EAAjC,KAA8B,EAAY,ECzDrE,MAAwB,qBAAmB,EAC3C,YACA,KAAc,WAAS,WACvB,uCACA,qCACA,iBACA,+CACA,CAAK,CACL,yHACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,gBAAW,EACtB,mBACA,sBACA,CAAK,CACL,aC5BA,yCCAA,2CCAA,yFCAA,wCCAA,yDCAA,uCCAA,qDCAA,4CCAA,0CCAA,gGCAA,wCCAA,+CCAA,2CCAA,oDCAA,0CCAA,yCCAA,4CCAA,oCCAA,8CCAA,8CCAA,oCCAA,4CCAA,+CCAA", "sources": ["webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"module\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-page.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:async_hooks\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"require-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"os\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"util\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:child_process\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"path\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:http\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:zlib\"", "webpack://next-shadcn-dashboard-starter/src/app/api/patient-interactions/route.ts", "webpack://next-shadcn-dashboard-starter/sentry-wrapper-module", "webpack://next-shadcn-dashboard-starter/", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:tls\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:https\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/compiled/next-server/app-route.runtime.prod.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:os\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:diagnostics_channel\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"import-in-the-middle\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:stream\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:util\"", "webpack://next-shadcn-dashboard-starter/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:fs\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"worker_threads\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"perf_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:worker_threads\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:path\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:net\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:crypto\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"url\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"child_process\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:readline\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"tty\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"async_hooks\"", "webpack://next-shadcn-dashboard-starter/external node-commonjs \"node:inspector\"", "webpack://next-shadcn-dashboard-starter/external commonjs2 \"events\""], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "module.exports = require(\"module\");", "module.exports = require(\"next/dist/compiled/next-server/app-page.runtime.prod.js\");", "module.exports = require(\"node:async_hooks\");", "module.exports = require(\"require-in-the-middle\");", "module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "module.exports = require(\"process\");", "module.exports = require(\"os\");", "module.exports = require(\"util\");", "module.exports = require(\"fs\");", "module.exports = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "module.exports = require(\"node:child_process\");", "module.exports = require(\"path\");", "module.exports = require(\"diagnostics_channel\");", "module.exports = require(\"node:http\");", "module.exports = require(\"node:zlib\");", "import { NextRequest } from 'next/server';\nimport { withAuthentication, createSuccessR<PERSON>ponse, createErrorResponse, AuthenticatedUser } from '@/lib/auth-middleware';\nimport { createPayloadClient } from '@/lib/payload-client';\n\nexport const GET = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const url = new URL(request.url);\n    \n    // Extract query parameters\n    const limit = parseInt(url.searchParams.get('limit') || '10');\n    const page = parseInt(url.searchParams.get('page') || '1');\n    const patientId = url.searchParams.get('patientId');\n    const interactionType = url.searchParams.get('interactionType');\n    const status = url.searchParams.get('status');\n    const priority = url.searchParams.get('priority');\n    const staffMember = url.searchParams.get('staffMember');\n    const search = url.searchParams.get('search');\n    \n    // Build where clause\n    let whereClause: any = {};\n    \n    if (patientId) {\n      whereClause.patient = { equals: patientId };\n    }\n    \n    if (interactionType) {\n      whereClause.interactionType = { equals: interactionType };\n    }\n    \n    if (status) {\n      whereClause.status = { equals: status };\n    }\n    \n    if (priority) {\n      whereClause.priority = { equals: priority };\n    }\n    \n    if (staffMember) {\n      whereClause.staffMember = { equals: staffMember };\n    }\n    \n    if (search) {\n      whereClause.or = [\n        {\n          title: {\n            contains: search,\n          },\n        },\n        {\n          outcome: {\n            contains: search,\n          },\n        },\n      ];\n    }\n    \n    // Apply role-based filtering\n    if (user.role === 'doctor') {\n      whereClause.and = [\n        whereClause,\n        {\n          or: [\n            {\n              staffMember: {\n                equals: user.payloadUserId,\n              },\n            },\n            {\n              interactionType: {\n                in: ['consultation-note', 'treatment-discussion', 'in-person-visit'],\n              },\n            },\n          ],\n        },\n      ];\n    } else if (user.role === 'front-desk') {\n      whereClause.and = [\n        whereClause,\n        {\n          interactionType: {\n            in: ['phone-call', 'email', 'billing-inquiry'],\n          },\n        },\n      ];\n    }\n    \n    const data = await payloadClient.getPatientInteractions({\n      limit,\n      page,\n      where: whereClause,\n      sort: '-timestamp', // Sort by timestamp descending (newest first)\n    });\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error fetching patient interactions:', error);\n    return createErrorResponse('Failed to fetch patient interactions');\n  }\n});\n\nexport const POST = withAuthentication(async (user: AuthenticatedUser, request: NextRequest) => {\n  try {\n    const payloadClient = createPayloadClient(user);\n    const interactionData = await request.json();\n    \n    // Set the staff member to the current user\n    interactionData.staffMember = user.payloadUserId;\n    \n    // Validate required fields\n    if (!interactionData.patient || !interactionData.interactionType || !interactionData.title || !interactionData.notes) {\n      return createErrorResponse('Missing required fields: patient, interactionType, title, notes', 400);\n    }\n    \n    // Validate interaction type based on user role\n    if (user.role === 'front-desk') {\n      const allowedTypes = ['phone-call', 'email', 'billing-inquiry'];\n      if (!allowedTypes.includes(interactionData.interactionType)) {\n        return createErrorResponse('Front-desk staff can only create phone-call, email, or billing-inquiry interactions', 403);\n      }\n    }\n    \n    const data = await payloadClient.createPatientInteraction(interactionData);\n    \n    return createSuccessResponse(data);\n  } catch (error) {\n    console.error('Error creating patient interaction:', error);\n    return createErrorResponse('Failed to create patient interaction');\n  }\n});\n", "import * as origModule from 'next/dist/server/app-render/work-unit-async-storage.external.js';\nimport * as serverComponentModule from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport * from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nexport {} from '__SENTRY_WRAPPING_TARGET_FILE__.cjs';\nimport * as Sentry from '@sentry/nextjs';\n\n// @ts-expect-error Because we cannot be sure if the RequestAsyncStorage module exists (it is not part of the Next.js public\n// API) we use a shim if it doesn't exist. The logic for this is in the wrapping loader.\n\nconst asyncStorageModule = { ...origModule } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nfunction wrapHandler(handler, method) {\n  // Running the instrumentation code during the build phase will mark any function as \"dynamic\" because we're accessing\n  // the Request object. We do not want to turn handlers dynamic so we skip instrumentation in the build phase.\n  if (process.env.NEXT_PHASE === 'phase-production-build') {\n    return handler;\n  }\n\n  if (typeof handler !== 'function') {\n    return handler;\n  }\n\n  return new Proxy(handler, {\n    apply: (originalFunction, thisArg, args) => {\n      let headers = undefined;\n\n      // We try-catch here just in case the API around `requestAsyncStorage` changes unexpectedly since it is not public API\n      try {\n        const requestAsyncStore = requestAsyncStorage?.getStore() ;\n        headers = requestAsyncStore?.headers;\n      } catch (e) {\n        /** empty */\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      return Sentry.wrapRouteHandlerWithSentry(originalFunction , {\n        method,\n        parameterizedRoute: '/api/patient-interactions',\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst GET = wrapHandler(serverComponentModule.GET , 'GET');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst POST = wrapHandler(serverComponentModule.POST , 'POST');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PUT = wrapHandler(serverComponentModule.PUT , 'PUT');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst PATCH = wrapHandler(serverComponentModule.PATCH , 'PATCH');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst DELETE = wrapHandler(serverComponentModule.DELETE , 'DELETE');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst HEAD = wrapHandler(serverComponentModule.HEAD , 'HEAD');\n// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\nconst OPTIONS = wrapHandler(serverComponentModule.OPTIONS , 'OPTIONS');\n\nexport { DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT };\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-interactions\\\\route.ts\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/patient-interactions/route\",\n        pathname: \"/api/patient-interactions\",\n        filename: \"route\",\n        bundlePath: \"app/api/patient-interactions/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\nord-coast\\\\frontend-nextjs\\\\src\\\\app\\\\api\\\\patient-interactions\\\\route.ts\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "module.exports = require(\"node:tls\");", "module.exports = require(\"node:https\");", "module.exports = require(\"next/dist/compiled/next-server/app-route.runtime.prod.js\");", "module.exports = require(\"node:os\");", "module.exports = require(\"node:diagnostics_channel\");", "module.exports = require(\"crypto\");", "module.exports = require(\"import-in-the-middle\");", "module.exports = require(\"node:stream\");", "module.exports = require(\"node:util\");", "module.exports = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "module.exports = require(\"node:fs\");", "module.exports = require(\"worker_threads\");", "module.exports = require(\"perf_hooks\");", "module.exports = require(\"node:worker_threads\");", "module.exports = require(\"node:path\");", "module.exports = require(\"node:net\");", "module.exports = require(\"node:crypto\");", "module.exports = require(\"url\");", "module.exports = require(\"child_process\");", "module.exports = require(\"node:readline\");", "module.exports = require(\"tty\");", "module.exports = require(\"async_hooks\");", "module.exports = require(\"node:inspector\");", "module.exports = require(\"events\");"], "names": ["GET", "withAuthentication", "user", "request", "payloadClient", "createPayloadClient", "url", "URL", "limit", "parseInt", "searchParams", "get", "page", "patientId", "interactionType", "status", "priority", "staffMember", "search", "<PERSON><PERSON><PERSON><PERSON>", "patient", "equals", "title", "contains", "outcome", "role", "and", "or", "payloadUserId", "in", "data", "getPatientInteractions", "where", "sort", "createSuccessResponse", "error", "console", "createErrorResponse", "POST", "interactionData", "json", "notes", "includes", "createPatientInteraction", "serverComponentModule.GET", "serverComponentModule.PUT", "serverComponentModule.PATCH", "serverComponentModule.DELETE", "serverComponentModule.OPTIONS"], "sourceRoot": ""}