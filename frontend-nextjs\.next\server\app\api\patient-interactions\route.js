try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},r=(new e.Error).stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="3da5b768-51ed-4cd3-9e0c-7eed341bef21",e._sentryDebugIdIdentifier="sentry-dbid-3da5b768-51ed-4cd3-9e0c-7eed341bef21")}catch(e){}"use strict";(()=>{var e={};e.id=3481,e.ids=[3481],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:e=>{e.exports=require("module")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},19063:e=>{e.exports=require("require-in-the-middle")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{e.exports=require("process")},21820:e=>{e.exports=require("os")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},36686:e=>{e.exports=require("diagnostics_channel")},37067:e=>{e.exports=require("node:http")},38522:e=>{e.exports=require("node:zlib")},41108:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>A,routeModule:()=>v,serverHooks:()=>E,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>k});var i={};t.r(i),t.d(i,{DELETE:()=>P,GET:()=>h,HEAD:()=>b,OPTIONS:()=>T,PATCH:()=>m,POST:()=>f,PUT:()=>g});var n=t(86047),o=t(85544),s=t(36135),a=t(63033),p=t(53547),d=t(54360),u=t(19761);let c=(0,p.ZA)(async(e,r)=>{try{let t=(0,d.o)(e),i=new URL(r.url),n=parseInt(i.searchParams.get("limit")||"10"),o=parseInt(i.searchParams.get("page")||"1"),s=i.searchParams.get("patientId"),a=i.searchParams.get("interactionType"),u=i.searchParams.get("status"),c=i.searchParams.get("priority"),l=i.searchParams.get("staffMember"),x=i.searchParams.get("search"),q={};s&&(q.patient={equals:s}),a&&(q.interactionType={equals:a}),u&&(q.status={equals:u}),c&&(q.priority={equals:c}),l&&(q.staffMember={equals:l}),x&&(q.or=[{title:{contains:x}},{outcome:{contains:x}}]),"doctor"===e.role?q.and=[q,{or:[{staffMember:{equals:e.payloadUserId}},{interactionType:{in:["consultation-note","treatment-discussion","in-person-visit"]}}]}]:"front-desk"===e.role&&(q.and=[q,{interactionType:{in:["phone-call","email","billing-inquiry"]}}]);let y=await t.getPatientInteractions({limit:n,page:o,where:q,sort:"-timestamp"});return(0,p.$y)(y)}catch(e){return console.error("Error fetching patient interactions:",e),(0,p.WX)("Failed to fetch patient interactions")}}),l=(0,p.ZA)(async(e,r)=>{try{let t=(0,d.o)(e),i=await r.json();if(i.staffMember=e.payloadUserId,!i.patient||!i.interactionType||!i.title||!i.notes)return(0,p.WX)("Missing required fields: patient, interactionType, title, notes",400);if("front-desk"===e.role&&!["phone-call","email","billing-inquiry"].includes(i.interactionType))return(0,p.WX)("Front-desk staff can only create phone-call, email, or billing-inquiry interactions",403);let n=await t.createPatientInteraction(i);return(0,p.$y)(n)}catch(e){return console.error("Error creating patient interaction:",e),(0,p.WX)("Failed to create patient interaction")}}),x={...a},q="workUnitAsyncStorage"in x?x.workUnitAsyncStorage:"requestAsyncStorage"in x?x.requestAsyncStorage:void 0;function y(e,r){return"phase-production-build"===process.env.NEXT_PHASE||"function"!=typeof e?e:new Proxy(e,{apply:(e,t,i)=>{let n;try{let e=q?.getStore();n=e?.headers}catch(e){}return u.wrapRouteHandlerWithSentry(e,{method:r,parameterizedRoute:"/api/patient-interactions",headers:n}).apply(t,i)}})}let h=y(c,"GET"),f=y(l,"POST"),g=y(void 0,"PUT"),m=y(void 0,"PATCH"),P=y(void 0,"DELETE"),b=y(void 0,"HEAD"),T=y(void 0,"OPTIONS"),v=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/patient-interactions/route",pathname:"/api/patient-interactions",filename:"route",bundlePath:"app/api/patient-interactions/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\api\\patient-interactions\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:w,workUnitAsyncStorage:k,serverHooks:E}=v;function A(){return(0,s.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:k})}},41692:e=>{e.exports=require("node:tls")},44708:e=>{e.exports=require("node:https")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},53053:e=>{e.exports=require("node:diagnostics_channel")},55511:e=>{e.exports=require("crypto")},56801:e=>{e.exports=require("import-in-the-middle")},57075:e=>{e.exports=require("node:stream")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74998:e=>{e.exports=require("perf_hooks")},75919:e=>{e.exports=require("node:worker_threads")},76760:e=>{e.exports=require("node:path")},77030:e=>{e.exports=require("node:net")},77598:e=>{e.exports=require("node:crypto")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},80481:e=>{e.exports=require("node:readline")},83997:e=>{e.exports=require("tty")},84297:e=>{e.exports=require("async_hooks")},86592:e=>{e.exports=require("node:inspector")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[55,3738,1950,5886,9615,125],()=>t(41108));module.exports=i})();
//# sourceMappingURL=route.js.map