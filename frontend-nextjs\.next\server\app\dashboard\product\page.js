try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="ad488ad3-43a1-44f6-a57a-8b5ccf5ae0a5",e._sentryDebugIdIdentifier="sentry-dbid-ad488ad3-43a1-44f6-a57a-8b5ccf5ae0a5")}catch(e){}(()=>{var e={};e.id=1505,e.ids=[1505],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5149:(e,t,r)=>{"use strict";r.d(t,{AM:()=>o,Wv:()=>l,hl:()=>i});var s=r(24443);r(60222);var a=r(98487),n=r(72595);function o({...e}){return(0,s.jsx)(a.bL,{"data-slot":"popover",...e,"data-sentry-element":"PopoverPrimitive.Root","data-sentry-component":"Popover","data-sentry-source-file":"popover.tsx"})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"popover-trigger",...e,"data-sentry-element":"PopoverPrimitive.Trigger","data-sentry-component":"PopoverTrigger","data-sentry-source-file":"popover.tsx"})}function i({className:e,align:t="center",sideOffset:r=4,...o}){return(0,s.jsx)(a.ZL,{"data-sentry-element":"PopoverPrimitive.Portal","data-sentry-component":"PopoverContent","data-sentry-source-file":"popover.tsx",children:(0,s.jsx)(a.UC,{"data-slot":"popover-content",align:t,sideOffset:r,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...o,"data-sentry-element":"PopoverPrimitive.Content","data-sentry-source-file":"popover.tsx"})})}},7944:(e,t,r)=>{let{createProxy:s}=r(55604);e.exports=s("C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\node_modules\\.pnpm\\next@15.3.2_@babel+core@7.2_779ed0850b03e649bfcf2a817b1ecc4b\\node_modules\\next\\dist\\client\\app-dir\\link.js")},8086:e=>{"use strict";e.exports=require("module")},10531:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(24443);r(60222);var a=r(16586),n=r(29693),o=r(72595);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,asChild:r=!1,...n}){let i=r?a.DX:"span";return(0,s.jsx)(i,{"data-slot":"badge",className:(0,o.cn)(l({variant:t}),e),...n,"data-sentry-element":"Comp","data-sentry-component":"Badge","data-sentry-source-file":"badge.tsx"})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10883:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(78869),a=r(19557);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,a.cn)("bg-accent animate-pulse rounded-md",e),...t,"data-sentry-component":"Skeleton","data-sentry-source-file":"skeleton.tsx"})}},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{"use strict";e.exports=require("node:child_process")},33873:e=>{"use strict";e.exports=require("path")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},37862:(e,t,r)=>{"use strict";r.d(t,{D:()=>a});var s=r(78869);let a=({title:e,description:t})=>(0,s.jsxs)("div",{"data-sentry-component":"Heading","data-sentry-source-file":"heading.tsx",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold tracking-tight",children:e}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:t})]})},38522:e=>{"use strict";e.exports=require("node:zlib")},39530:(e,t,r)=>{"use strict";r.d(t,{ProductTable:()=>s});let s=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call ProductTable() from the server but ProductTable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\index.tsx","ProductTable")},40256:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,38250,23)),Promise.resolve().then(r.bind(r,67529)),Promise.resolve().then(r.bind(r,75895)),Promise.resolve().then(r.bind(r,61074)),Promise.resolve().then(r.bind(r,59962)),Promise.resolve().then(r.bind(r,96992))},41692:e=>{"use strict";e.exports=require("node:tls")},44708:e=>{"use strict";e.exports=require("node:https")},47923:(e,t,r)=>{"use strict";r.d(t,{c:()=>n,g:()=>o});var s=r(36637),a=r(3944);let n=e=>new Promise(t=>setTimeout(t,e)),o={records:[],initialize(){let e=[];for(let r=1;r<=20;r++){var t;e.push({id:t=r,name:s.a.commerce.productName(),description:s.a.commerce.productDescription(),created_at:s.a.date.between({from:"2022-01-01",to:"2023-12-31"}).toISOString(),price:parseFloat(s.a.commerce.price({min:5,max:500,dec:2})),photo_url:`https://api.slingacademy.com/public/sample-products/${t}.png`,category:s.a.helpers.arrayElement(["Electronics","Furniture","Clothing","Toys","Groceries","Books","Jewelry","Beauty Products"]),updated_at:s.a.date.recent().toISOString()})}this.records=e},async getAll({categories:e=[],search:t}){let r=[...this.records];return e.length>0&&(r=r.filter(t=>e.includes(t.category))),t&&(r=(0,a.Ht)(r,t,{keys:["name","description","category"]})),r},async getProducts({page:e=1,limit:t=10,categories:r,search:s}){await n(1e3);let a=r?r.split("."):[],o=await this.getAll({categories:a,search:s}),l=o.length,i=(e-1)*t,d=o.slice(i,i+t);return{success:!0,time:new Date().toISOString(),message:"Sample data for testing and learning purposes",total_products:l,offset:i,limit:t,products:d}},async getProductById(e){await n(1e3);let t=this.records.find(t=>t.id===e);return t?{success:!0,time:new Date().toISOString(),message:`Product with ID ${e} found`,product:t}:{success:!1,message:`Product with ID ${e} not found`}}};o.initialize()},48161:e=>{"use strict";e.exports=require("node:os")},50051:(e,t,r)=>{"use strict";r.d(t,{columns:()=>s});let s=(0,r(91611).registerClientReference)(function(){throw Error("Attempted to call columns() from the server but columns is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\features\\products\\components\\product-tables\\columns.tsx","columns")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},53408:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,7944,23)),Promise.resolve().then(r.bind(r,89371)),Promise.resolve().then(r.bind(r,9597)),Promise.resolve().then(r.bind(r,68260)),Promise.resolve().then(r.bind(r,50051)),Promise.resolve().then(r.bind(r,39530))},55511:e=>{"use strict";e.exports=require("crypto")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},59962:(e,t,r)=>{"use strict";r.d(t,{columns:()=>k});var s=r(24443),a=r(10531),n=r(61770);let o=(0,n.A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var l=r(11035),i=r(72595),d=r(39255);function c({column:e,title:t,className:r,...a}){return e.getCanSort()||e.getCanHide()?(0,s.jsxs)(l.rI,{"data-sentry-element":"DropdownMenu","data-sentry-component":"DataTableColumnHeader","data-sentry-source-file":"data-table-column-header.tsx",children:[(0,s.jsxs)(l.ty,{className:(0,i.cn)("hover:bg-accent focus:ring-ring data-[state=open]:bg-accent [&_svg]:text-muted-foreground -ml-1.5 flex h-8 items-center gap-1.5 rounded-md px-2 py-1.5 focus:ring-1 focus:outline-none [&_svg]:size-4 [&_svg]:shrink-0",r),...a,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"data-table-column-header.tsx",children:[t,e.getCanSort()&&("desc"===e.getIsSorted()?(0,s.jsx)(d.D3D,{}):"asc"===e.getIsSorted()?(0,s.jsx)(d.Mtm,{}):(0,s.jsx)(d.TBE,{}))]}),(0,s.jsxs)(l.SQ,{align:"start",className:"w-28","data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"data-table-column-header.tsx",children:[e.getCanSort()&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.hO,{className:"[&_svg]:text-muted-foreground relative pr-8 pl-2 [&>span:first-child]:right-2 [&>span:first-child]:left-auto",checked:"asc"===e.getIsSorted(),onClick:()=>e.toggleSorting(!1),children:[(0,s.jsx)(d.Mtm,{}),"Asc"]}),(0,s.jsxs)(l.hO,{className:"[&_svg]:text-muted-foreground relative pr-8 pl-2 [&>span:first-child]:right-2 [&>span:first-child]:left-auto",checked:"desc"===e.getIsSorted(),onClick:()=>e.toggleSorting(!0),children:[(0,s.jsx)(d.D3D,{}),"Desc"]}),e.getIsSorted()&&(0,s.jsxs)(l._2,{className:"[&_svg]:text-muted-foreground pl-2",onClick:()=>e.clearSorting(),children:[(0,s.jsx)(d.MKb,{}),"Reset"]})]}),e.getCanHide()&&(0,s.jsxs)(l.hO,{className:"[&_svg]:text-muted-foreground relative pr-8 pl-2 [&>span:first-child]:right-2 [&>span:first-child]:left-auto",checked:!e.getIsVisible(),onClick:()=>e.toggleVisibility(!1),children:[(0,s.jsx)(o,{}),"Hide"]})]})]}):(0,s.jsx)("div",{className:(0,i.cn)(r),children:t})}let u=(0,n.A)("Text",[["path",{d:"M17 6.1H3",key:"wptmhv"}],["path",{d:"M21 12.1H3",key:"1j38uz"}],["path",{d:"M15.1 18H3",key:"1nb16a"}]]),p=(0,n.A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var m=r(24417),f=r(31822),h=r(60222),x=r(33284),g=r(61780);let y=({title:e,description:t,isOpen:r,onClose:a,children:n})=>(0,s.jsx)(g.lG,{open:r,onOpenChange:e=>{e||a()},"data-sentry-element":"Dialog","data-sentry-component":"Modal","data-sentry-source-file":"modal.tsx",children:(0,s.jsxs)(g.Cf,{"data-sentry-element":"DialogContent","data-sentry-source-file":"modal.tsx",children:[(0,s.jsxs)(g.c7,{"data-sentry-element":"DialogHeader","data-sentry-source-file":"modal.tsx",children:[(0,s.jsx)(g.L3,{"data-sentry-element":"DialogTitle","data-sentry-source-file":"modal.tsx",children:e}),(0,s.jsx)(g.rr,{"data-sentry-element":"DialogDescription","data-sentry-source-file":"modal.tsx",children:t})]}),(0,s.jsx)("div",{children:n})]})}),b=({isOpen:e,onClose:t,onConfirm:r,loading:a})=>{let[n,o]=(0,h.useState)(!1);return((0,h.useEffect)(()=>{o(!0)},[]),n)?(0,s.jsx)(y,{title:"Are you sure?",description:"This action cannot be undone.",isOpen:e,onClose:t,"data-sentry-element":"Modal","data-sentry-component":"AlertModal","data-sentry-source-file":"alert-modal.tsx",children:(0,s.jsxs)("div",{className:"flex w-full items-center justify-end space-x-2 pt-6",children:[(0,s.jsx)(x.$,{disabled:a,variant:"outline",onClick:t,"data-sentry-element":"Button","data-sentry-source-file":"alert-modal.tsx",children:"Cancel"}),(0,s.jsx)(x.$,{disabled:a,variant:"destructive",onClick:r,"data-sentry-element":"Button","data-sentry-source-file":"alert-modal.tsx",children:"Continue"})]})}):null};var v=r(9556),j=r(49958),w=r(9752),C=r(34769);let P=({data:e})=>{let[t]=(0,h.useState)(!1),[r,a]=(0,h.useState)(!1),n=(0,C.useRouter)(),o=async()=>{};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b,{isOpen:r,onClose:()=>a(!1),onConfirm:o,loading:t,"data-sentry-element":"AlertModal","data-sentry-source-file":"cell-action.tsx"}),(0,s.jsxs)(l.rI,{modal:!1,"data-sentry-element":"DropdownMenu","data-sentry-source-file":"cell-action.tsx",children:[(0,s.jsx)(l.ty,{asChild:!0,"data-sentry-element":"DropdownMenuTrigger","data-sentry-source-file":"cell-action.tsx",children:(0,s.jsxs)(x.$,{variant:"ghost",className:"h-8 w-8 p-0","data-sentry-element":"Button","data-sentry-source-file":"cell-action.tsx",children:[(0,s.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,s.jsx)(v.A,{className:"h-4 w-4","data-sentry-element":"IconDotsVertical","data-sentry-source-file":"cell-action.tsx"})]})}),(0,s.jsxs)(l.SQ,{align:"end","data-sentry-element":"DropdownMenuContent","data-sentry-source-file":"cell-action.tsx",children:[(0,s.jsx)(l.lp,{"data-sentry-element":"DropdownMenuLabel","data-sentry-source-file":"cell-action.tsx",children:"Actions"}),(0,s.jsxs)(l._2,{onClick:()=>n.push(`/dashboard/product/${e.id}`),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"cell-action.tsx",children:[(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconEdit","data-sentry-source-file":"cell-action.tsx"})," Update"]}),(0,s.jsxs)(l._2,{onClick:()=>a(!0),"data-sentry-element":"DropdownMenuItem","data-sentry-source-file":"cell-action.tsx",children:[(0,s.jsx)(w.A,{className:"mr-2 h-4 w-4","data-sentry-element":"IconTrash","data-sentry-source-file":"cell-action.tsx"})," Delete"]})]})]})]})},k=[{accessorKey:"photo_url",header:"IMAGE",cell:({row:e})=>(0,s.jsx)("div",{className:"relative aspect-square",children:(0,s.jsx)(f.default,{src:e.getValue("photo_url"),alt:e.getValue("name"),fill:!0,className:"rounded-lg"})})},{id:"name",accessorKey:"name",header:({column:e})=>(0,s.jsx)(c,{column:e,title:"Name"}),cell:({cell:e})=>(0,s.jsx)("div",{children:e.getValue()}),meta:{label:"Name",placeholder:"Search products...",variant:"text",icon:u},enableColumnFilter:!0},{id:"category",accessorKey:"category",header:({column:e})=>(0,s.jsx)(c,{column:e,title:"Category"}),cell:({cell:e})=>{let t=e.getValue(),r="active"===t?p:m.A;return(0,s.jsxs)(a.E,{variant:"outline",className:"capitalize",children:[(0,s.jsx)(r,{}),t]})},enableColumnFilter:!0,meta:{label:"categories",variant:"multiSelect",options:[{value:"Electronics",label:"Electronics"},{value:"Furniture",label:"Furniture"},{value:"Clothing",label:"Clothing"},{value:"Toys",label:"Toys"},{value:"Groceries",label:"Groceries"},{value:"Books",label:"Books"},{value:"Jewelry",label:"Jewelry"},{value:"Beauty Products",label:"Beauty Products"}]}},{accessorKey:"price",header:"PRICE"},{accessorKey:"description",header:"DESCRIPTION"},{id:"actions",cell:({row:e})=>(0,s.jsx)(P,{data:e.original})}]},61780:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>m,L3:()=>f,c7:()=>p,lG:()=>l,rr:()=>h,zM:()=>i});var s=r(24443);r(60222);var a=r(99873),n=r(20422),o=r(72595);function l({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e,"data-sentry-element":"DialogPrimitive.Root","data-sentry-component":"Dialog","data-sentry-source-file":"dialog.tsx"})}function i({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e,"data-sentry-element":"DialogPrimitive.Trigger","data-sentry-component":"DialogTrigger","data-sentry-source-file":"dialog.tsx"})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e,"data-sentry-element":"DialogPrimitive.Portal","data-sentry-component":"DialogPortal","data-sentry-source-file":"dialog.tsx"})}function c({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t,"data-sentry-element":"DialogPrimitive.Overlay","data-sentry-component":"DialogOverlay","data-sentry-source-file":"dialog.tsx"})}function u({className:e,children:t,...r}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal","data-sentry-element":"DialogPortal","data-sentry-component":"DialogContent","data-sentry-source-file":"dialog.tsx",children:[(0,s.jsx)(c,{"data-sentry-element":"DialogOverlay","data-sentry-source-file":"dialog.tsx"}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,"data-sentry-element":"DialogPrimitive.Content","data-sentry-source-file":"dialog.tsx",children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4","data-sentry-element":"DialogPrimitive.Close","data-sentry-source-file":"dialog.tsx",children:[(0,s.jsx)(n.A,{"data-sentry-element":"XIcon","data-sentry-source-file":"dialog.tsx"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t,"data-sentry-component":"DialogHeader","data-sentry-source-file":"dialog.tsx"})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t,"data-sentry-component":"DialogFooter","data-sentry-source-file":"dialog.tsx"})}function f({className:e,...t}){return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t,"data-sentry-element":"DialogPrimitive.Title","data-sentry-component":"DialogTitle","data-sentry-source-file":"dialog.tsx"})}function h({className:e,...t}){return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t,"data-sentry-element":"DialogPrimitive.Description","data-sentry-component":"DialogDescription","data-sentry-source-file":"dialog.tsx"})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67529:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,ScrollArea:()=>o});var s=r(24443);r(60222);var a=r(54889),n=r(72595);function o({className:e,children:t,...r}){return(0,s.jsxs)(a.bL,{"data-slot":"scroll-area",className:(0,n.cn)("relative",e),...r,"data-sentry-element":"ScrollAreaPrimitive.Root","data-sentry-component":"ScrollArea","data-sentry-source-file":"scroll-area.tsx",children:[(0,s.jsx)(a.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1","data-sentry-element":"ScrollAreaPrimitive.Viewport","data-sentry-source-file":"scroll-area.tsx",children:t}),(0,s.jsx)(l,{"data-sentry-element":"ScrollBar","data-sentry-source-file":"scroll-area.tsx"}),(0,s.jsx)(a.OK,{"data-sentry-element":"ScrollAreaPrimitive.Corner","data-sentry-source-file":"scroll-area.tsx"})]})}function l({className:e,orientation:t="vertical",...r}){return(0,s.jsx)(a.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,n.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,"data-sentry-element":"ScrollAreaPrimitive.ScrollAreaScrollbar","data-sentry-component":"ScrollBar","data-sentry-source-file":"scroll-area.tsx",children:(0,s.jsx)(a.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full","data-sentry-element":"ScrollAreaPrimitive.ScrollAreaThumb","data-sentry-source-file":"scroll-area.tsx"})})}},68260:(e,t,r)=>{"use strict";r.d(t,{Table:()=>a,TableBody:()=>o,TableCell:()=>d,TableHead:()=>l,TableHeader:()=>n,TableRow:()=>i});var s=r(91611);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","Table"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableHeader"),o=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableBody");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableFooter");let l=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableHead"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableRow"),d=(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableCell");(0,s.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\table.tsx","TableCaption")},68829:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f22efd92a3b59d43d3d12fe480e87910640e1db9e":()=>a.y,"7f7b45347fd50452ee6e2850ded1018991a7b086f0":()=>s.at,"7f909588461cb83e855875f4939d6f26e4ae81b49e":()=>s.ot,"7fe4197e906c8b4cd0ec15b4e0f6272dfe50de9261":()=>s.ai});var s=r(27235),a=r(41372)},73024:e=>{"use strict";e.exports=require("node:fs")},73393:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>L,generateImageMetadata:()=>U,generateMetadata:()=>z,generateViewport:()=>O,metadata:()=>_});var a=r(63033),n=r(78869),o=r(83829),l=r(22762),i=r(37862),d=r(9597),c=r(10883),u=r(68260),p=r(19557);function m({columnCount:e,rowCount:t=10,filterCount:r=0,cellWidths:s=["auto"],withViewOptions:a=!0,withPagination:o=!0,shrinkZero:l=!1,className:i,...d}){let m=Array.from({length:e},(e,t)=>s[t%s.length]??"auto");return(0,n.jsxs)("div",{className:(0,p.cn)("flex flex-1 flex-col space-y-4",i),...d,"data-sentry-component":"DataTableSkeleton","data-sentry-source-file":"data-table-skeleton.tsx",children:[(0,n.jsxs)("div",{className:"flex w-full items-center justify-between gap-2 overflow-auto p-1",children:[(0,n.jsx)("div",{className:"flex flex-1 items-center gap-2",children:r>0?Array.from({length:r}).map((e,t)=>(0,n.jsx)(c.E,{className:"h-7 w-[4.5rem] border-dashed"},t)):null}),a?(0,n.jsx)(c.E,{className:"ml-auto hidden h-7 w-[4.5rem] lg:flex"}):null]}),(0,n.jsx)("div",{className:"flex-1 rounded-md border",children:(0,n.jsxs)(u.Table,{"data-sentry-element":"Table","data-sentry-source-file":"data-table-skeleton.tsx",children:[(0,n.jsx)(u.TableHeader,{"data-sentry-element":"TableHeader","data-sentry-source-file":"data-table-skeleton.tsx",children:Array.from({length:1}).map((t,r)=>(0,n.jsx)(u.TableRow,{className:"hover:bg-transparent",children:Array.from({length:e}).map((e,t)=>(0,n.jsx)(u.TableHead,{style:{width:m[t],minWidth:l?m[t]:"auto"},children:(0,n.jsx)(c.E,{className:"h-6 w-full"})},t))},r))}),(0,n.jsx)(u.TableBody,{"data-sentry-element":"TableBody","data-sentry-source-file":"data-table-skeleton.tsx",children:Array.from({length:t}).map((t,r)=>(0,n.jsx)(u.TableRow,{className:"hover:bg-transparent",children:Array.from({length:e}).map((e,t)=>(0,n.jsx)(u.TableCell,{style:{width:m[t],minWidth:l?m[t]:"auto"},children:(0,n.jsx)(c.E,{className:"h-6 w-full"})},t))},r))})]})}),o?(0,n.jsxs)("div",{className:"flex w-full items-center justify-between gap-4 overflow-auto p-1 sm:gap-8",children:[(0,n.jsx)(c.E,{className:"h-7 w-40 shrink-0"}),(0,n.jsxs)("div",{className:"flex items-center gap-4 sm:gap-6 lg:gap-8",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(c.E,{className:"h-7 w-24"}),(0,n.jsx)(c.E,{className:"h-7 w-[4.5rem]"})]}),(0,n.jsx)("div",{className:"flex items-center justify-center text-sm font-medium",children:(0,n.jsx)(c.E,{className:"h-7 w-20"})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(c.E,{className:"hidden size-7 lg:block"}),(0,n.jsx)(c.E,{className:"size-7"}),(0,n.jsx)(c.E,{className:"size-7"}),(0,n.jsx)(c.E,{className:"hidden size-7 lg:block"})]})]})]}):null]})}var f=r(47923),h=r(22576),x={303:"Multiple adapter contexts detected. This might happen in monorepos.",404:"nuqs requires an adapter to work with your framework.",409:"Multiple versions of the library are loaded. This may lead to unexpected behavior. Currently using `%s`, but `%s` (via the %s adapter) was about to load on top.",414:"Max safe URL length exceeded. Some browsers may not be able to accept this URL. Consider limiting the amount of state stored in the URL.",429:"URL update rate-limited by the browser. Consider increasing `throttleMs` for key(s) `%s`. %O",500:"Empty search params cache. Search params can't be accessed in Layouts.",501:"Search params cache already populated. Have you called `parse` twice?"};function g(e){return`[nuqs] ${x[e]}
  See https://err.47ng.com/NUQS-${e}`}var y=Symbol("Input"),b=function(){try{if("undefined"==typeof localStorage)return!1;let e="nuqs-localStorage-test";localStorage.setItem(e,e);let t=localStorage.getItem(e)===e;if(localStorage.removeItem(e),!t)return!1}catch(e){return console.error("[nuqs]: debug mode is disabled (localStorage unavailable).",e),!1}return(localStorage.getItem("debug")??"").includes("nuqs")}();function v(e){function t(t){if(void 0===t)return null;let r="";if(Array.isArray(t)){if(void 0===t[0])return null;r=t[0]}return"string"==typeof t&&(r=t),function(e,t,r){try{return e(t)}catch(e){return!function(e,...t){b&&console.warn(e,...t)}("[nuqs] Error while parsing value `%s`: %O",t,e,r),null}}(e.parse,r)}return{eq:(e,t)=>e===t,...e,parseServerSide:t,withDefault(e){return{...this,defaultValue:e,parseServerSide:r=>t(r)??e}},withOptions(e){return{...this,...e}}}}var j=v({parse:e=>e,serialize:e=>`${e}`}),w=v({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:t},serialize:e=>Math.round(e).toFixed()});function C(e,t){return e.valueOf()===t.valueOf()}v({parse:e=>{let t=w.parse(e);return null===t?null:t-1},serialize:e=>w.serialize(e+1)}),v({parse:e=>{let t=parseInt(e,16);return Number.isNaN(t)?null:t},serialize:e=>{let t=Math.round(e).toString(16);return t.padStart(t.length+t.length%2,"0")}}),v({parse:e=>{let t=parseFloat(e);return Number.isNaN(t)?null:t},serialize:e=>e.toString()}),v({parse:e=>"true"===e,serialize:e=>e?"true":"false"}),v({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:new Date(t)},serialize:e=>e.valueOf().toString(),eq:C}),v({parse:e=>{let t=new Date(e);return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString(),eq:C}),v({parse:e=>{let t=new Date(e.slice(0,10));return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString().slice(0,10),eq:C});let P={page:w.withDefault(1),perPage:w.withDefault(10),name:j,gender:j,category:j},k=function(e,{urlKeys:t={}}={}){let r=function(e,{urlKeys:t={}}={}){return function r(s){if(s instanceof Promise)return s.then(e=>r(e));let a=function(e){try{if(e instanceof Request)if(e.url)return new URL(e.url).searchParams;else return new URLSearchParams;if(e instanceof URL)return e.searchParams;if(e instanceof URLSearchParams)return e;if("object"==typeof e){let t=Object.entries(e),r=new URLSearchParams;for(let[e,s]of t)if(Array.isArray(s))for(let t of s)r.append(e,t);else void 0!==s&&r.set(e,s);return r}if("string"==typeof e){if("canParse"in URL&&URL.canParse(e))return new URL(e).searchParams;return new URLSearchParams(e)}}catch(e){}return new URLSearchParams}(s),n={};for(let[r,s]of Object.entries(e)){let e=t[r]??r,o=a.get(e);n[r]=s.parseServerSide(o??void 0)}return n}}(e,{urlKeys:t}),s=h.cache(()=>({searchParams:{}}));function a(e){let t=s();if(Object.isFrozen(t.searchParams)){if(t[y]&&function(e,t){if(e===t)return!0;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}(e,t[y]))return n();throw Error(g(501))}return t.searchParams=r(e),t[y]=e,Object.freeze(t.searchParams)}function n(){let{searchParams:e}=s();if(0===Object.keys(e).length)throw Error(g(500));return e}return{parse:function(e){return e instanceof Promise?e.then(a):a(e)},get:function(e){let{searchParams:t}=s(),r=t[e];if(void 0===r)throw Error(g(500)+`
  in get(${String(e)})`);return r},all:n}}(P);!function(e,{clearOnDefault:t=!0,urlKeys:r={}}={}){}(0);var S=r(39530),D=r(50051);async function N({}){let e=k.get("page"),t=k.get("name"),r=k.get("perPage"),s=k.get("category"),a={page:e,limit:r,...t&&{search:t},...s&&{categories:s}},o=await f.g.getProducts(a),l=o.total_products,i=o.products;return(0,n.jsx)(S.ProductTable,{data:i,totalItems:l,columns:D.columns,"data-sentry-element":"ProductTable","data-sentry-component":"ProductListingPage","data-sentry-source-file":"product-listing.tsx"})}var T=(0,r(19e3).A)("outline","plus","IconPlus",[["path",{d:"M12 5l0 14",key:"svg-0"}],["path",{d:"M5 12l14 0",key:"svg-1"}]]),A=r(7944),q=r.n(A),I=r(19761);let _={title:"Dashboard: Products"};async function E(e){let t=await e.searchParams;return k.parse(t),(0,n.jsx)(o.A,{scrollable:!1,"data-sentry-element":"PageContainer","data-sentry-component":"Page","data-sentry-source-file":"page.tsx",children:(0,n.jsxs)("div",{className:"flex flex-1 flex-col space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsx)(i.D,{title:"Products",description:"Manage products (Server side table functionalities.)","data-sentry-element":"Heading","data-sentry-source-file":"page.tsx"}),(0,n.jsxs)(q(),{href:"/dashboard/product/new",className:(0,p.cn)((0,l.r)(),"text-xs md:text-sm"),"data-sentry-element":"Link","data-sentry-source-file":"page.tsx",children:[(0,n.jsx)(T,{className:"mr-2 h-4 w-4","data-sentry-element":"IconPlus","data-sentry-source-file":"page.tsx"})," Add New"]})]}),(0,n.jsx)(d.Separator,{"data-sentry-element":"Separator","data-sentry-source-file":"page.tsx"}),(0,n.jsx)(h.Suspense,{fallback:(0,n.jsx)(m,{columnCount:5,rowCount:8,filterCount:2}),"data-sentry-element":"Suspense","data-sentry-source-file":"page.tsx",children:(0,n.jsx)(N,{"data-sentry-element":"ProductListingPage","data-sentry-source-file":"page.tsx"})})]})})}let M={...a},R="workUnitAsyncStorage"in M?M.workUnitAsyncStorage:"requestAsyncStorage"in M?M.requestAsyncStorage:void 0;s=new Proxy(E,{apply:(e,t,r)=>{let s,a,n;try{let e=R?.getStore();s=e?.headers.get("sentry-trace")??void 0,a=e?.headers.get("baggage")??void 0,n=e?.headers}catch(e){}return I.wrapServerComponentWithSentry(e,{componentRoute:"/dashboard/product",componentType:"Page",sentryTraceHeader:s,baggageHeader:a,headers:n}).apply(t,r)}});let z=void 0,U=void 0,O=void 0,L=s},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},76760:e=>{"use strict";e.exports=require("node:path")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80481:e=>{"use strict";e.exports=require("node:readline")},80494:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var s=r(29703),a=r(85544),n=r(62458),o=r(77821),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let i={children:["",{children:["dashboard",{children:["product",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73393)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,56164)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,69549)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,62458)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,8036)),"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,11103,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,13780,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3259))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\app\\dashboard\\product\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/product/page",pathname:"/dashboard/product",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},83829:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(78869);r(22576);var a=r(89371);function n({children:e,scrollable:t=!0}){return(0,s.jsx)(s.Fragment,{children:t?(0,s.jsx)(a.ScrollArea,{className:"h-[calc(100dvh-52px)]",children:(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})}):(0,s.jsx)("div",{className:"flex flex-1 p-4 md:px-6",children:e})})}},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},86592:e=>{"use strict";e.exports=require("node:inspector")},89371:(e,t,r)=>{"use strict";r.d(t,{ScrollArea:()=>a});var s=r(91611);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ScrollArea() from the server but ScrollArea is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollArea");(0,s.registerClientReference)(function(){throw Error("Attempted to call ScrollBar() from the server but ScrollBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\nord-coast\\frontend-nextjs\\src\\components\\ui\\scroll-area.tsx","ScrollBar")},94735:e=>{"use strict";e.exports=require("events")},96992:(e,t,r)=>{"use strict";r.d(t,{ProductTable:()=>i});var s=r(24443),a=r(74916),n=r(68882),o=r(92585),l=r(98663);function i({data:e,totalItems:t,columns:r}){let[i]=(0,l.ZA)("perPage",l.GJ.withDefault(10)),d=Math.ceil(t/i),{table:c}=(0,o.u)({data:e,columns:r,pageCount:d,shallow:!1,debounceMs:500});return(0,s.jsx)(a.b,{table:c,"data-sentry-element":"DataTable","data-sentry-component":"ProductTable","data-sentry-source-file":"index.tsx",children:(0,s.jsx)(n.w,{table:c,"data-sentry-element":"DataTableToolbar","data-sentry-source-file":"index.tsx"})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[55,3738,7927,6451,5618,2584,9616,4144,4889,3875,3408,9255,5903,4298,1822,8774,7494,7331],()=>r(80494));module.exports=s})();
//# sourceMappingURL=page.js.map